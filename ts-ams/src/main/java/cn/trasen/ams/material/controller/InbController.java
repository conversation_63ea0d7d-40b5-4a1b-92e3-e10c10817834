package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.service.InbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName InbController
 * @Description TODO
 * @date 2025年7月31日 上午9:49:53
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "InbController")
public class InbController {

	private transient static final Logger logger = LoggerFactory.getLogger(InbController.class);

	@Autowired
	private InbService inbService;

	/**
	 * @Title saveInb
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/material/inb/save")
	public PlatformResult<String> saveInb(@RequestBody Inb record) {
		try {
			inbService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateInb
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/material/inb/update")
	public PlatformResult<String> updateInb(@RequestBody Inb record) {
		try {
			inbService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectInbById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<Inb>
	 * @date 2025年7月31日 上午9:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/material/inb/{id}")
	public PlatformResult<Inb> selectInbById(@PathVariable String id) {
		try {
			Inb record = inbService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteInbById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/material/inb/delete/{id}")
	public PlatformResult<String> deleteInbById(@PathVariable String id) {
		try {
			inbService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectInbList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<Inb>
	 * @date 2025年7月31日 上午9:49:53
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/inb/list")
	public DataSet<Inb> selectInbList(Page page, Inb record) {
		return inbService.getDataSetList(page, record);
	}
}
