package cn.trasen.ams.material.model;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.validator.dict.DictExistValid;
import cn.trasen.ams.common.validator.pk.PkExistValid;
import cn.trasen.ams.material.constant.MethodCodeConst;
import io.swagger.annotations.*;

import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.*;

@Table(name = "m_mtd_code")
@Setter
@Getter
public class MethodCode {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 方式
     */
    @DictExistValid(code = MethodCodeConst.RECEIPT_METHOD, message = "所属单据不正确")
    @NotNull(message = "方式不能为空")
    @ApiModelProperty(value = "Q:所属单据")
    private String method;

    @Transient
    @ApiModelProperty(value = "所属单据翻译")
    private String methodShow;

    /**
     * 方式名称
     */
    @NotNull(message = "方式名称不能为空")
    @ApiModelProperty(value = "Q:方式名称")
    private String name;

    /**
     * 业务类型
     */
    @DictExistValid(code = MethodCodeConst.METHOD_CODE_TYPE, message = "业务类型不正确")
    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型")
    private String type;

    @Transient
    @ApiModelProperty(value = "业务类型翻译")
    private String typeShow;

    /**
     * 排序
     */
    @Column(name = "seq_no")
    @ApiModelProperty(value = "排序")
    private Integer seqNo;

    /**
     * 使用仓库，多个逗号隔开
     */
    @PkExistValid(table = "c_warehouse", message = "使用仓库不正确")
    @NotNull(message = "使用仓库不能为空")
    @Column(name = "warehouse_id_set")
    @ApiModelProperty(value = "使用仓库，多个逗号隔开")
    private String warehouseIdSet;

    @Transient
    @ApiModelProperty(value = "使用仓库名称")
    private String warehouseNameSet;

    /**
     * 流水号前缀
     */
    @NotNull(message = "流水号前缀必填")
    @Column(name = "flow_no_prefix")
    @ApiModelProperty(value = "流水号前缀")
    private String flowNoPrefix;

    /**
     * 是否需要年份
     */
    @DictExistValid(code = CommonConst.YES_OR_NO, message = "是否需要年份不正确")
    @Column(name = "flow_no_need_year")
    @ApiModelProperty(value = "是否需要年份")
    private String flowNoNeedYear;

    @Transient
    @ApiModelProperty(value = "是否需要年份显示")
    private String flowNoNeedYearShow;

    /**
     * 是否需要月份
     */
    @DictExistValid(code = CommonConst.YES_OR_NO, message = "是否需要月份不正确")
    @Column(name = "flow_no_need_month")
    @ApiModelProperty(value = "是否需要月份")
    private String flowNoNeedMonth;

    @Transient
    @ApiModelProperty(value = "是否需要月份显示")
    private String flowNoNeedMonthShow;


    // flow_no_need_day
    @DictExistValid(code = CommonConst.YES_OR_NO, message = "是否需要日期不正确")
    @Column(name = "flow_no_need_day")
    @ApiModelProperty(value = "是否需要日期")
    private String flowNoNeedDay;

    @Transient
    @ApiModelProperty(value = "是否需要日期显示")
    private String flowNoNeedDayShow;

    /**
     * 流水号长度
     */
    // 必须是整数
    @Pattern(regexp = "^[1-9]\\d*$", message = "流水号长度必须是正整数")
    @NotNull(message = "流水号长度必填")
    @Column(name = "flow_no_len")
    @ApiModelProperty(value = "流水号长度")
    private String flowNoLen;

    /**
     * 状态
     */
    @ApiModelProperty(value = "Q:状态")
    private String status;

    @Transient
    @ApiModelProperty(value = "状态显示")
    private String statusShow;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}