package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Batch;
import cn.trasen.ams.material.service.BatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName BatchController
 * @Description TODO
 * @date 2025年7月31日 上午11:55:09
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "BatchController")
public class BatchController {

	private transient static final Logger logger = LoggerFactory.getLogger(BatchController.class);

	@Autowired
	private BatchService batchService;

	/**
	 * @Title saveBatch
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午11:55:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/material/batch/save")
	public PlatformResult<String> saveBatch(@RequestBody Batch record) {
		try {
			batchService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateBatch
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午11:55:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/material/batch/update")
	public PlatformResult<String> updateBatch(@RequestBody Batch record) {
		try {
			batchService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectBatchById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<Batch>
	 * @date 2025年7月31日 上午11:55:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/material/batch/{id}")
	public PlatformResult<Batch> selectBatchById(@PathVariable String id) {
		try {
			Batch record = batchService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteBatchById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午11:55:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/material/batch/delete/{id}")
	public PlatformResult<String> deleteBatchById(@PathVariable String id) {
		try {
			batchService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectBatchList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<Batch>
	 * @date 2025年7月31日 上午11:55:09
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/batch/list")
	public DataSet<Batch> selectBatchList(Page page, Batch record) {
		return batchService.getDataSetList(page, record);
	}
}
