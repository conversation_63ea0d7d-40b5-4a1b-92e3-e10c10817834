package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.StockCur;

/**
 * @ClassName StockCurService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:36
 * <AUTHOR>
 * @version 1.0
 */
public interface StockCurService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	Integer save(StockCur record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	Integer update(StockCur record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return StockCur
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	StockCur selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<StockCur>
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	DataSet<StockCur> getDataSetList(Page page, StockCur record);
}
