package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.StockSeq;

/**
 * @ClassName StockSeqService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:48
 * <AUTHOR>
 * @version 1.0
 */
public interface StockSeqService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	Integer save(StockSeq record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	Integer update(StockSeq record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return StockSeq
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	StockSeq selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<StockSeq>
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	DataSet<StockSeq> getDataSetList(Page page, StockSeq record);
}
