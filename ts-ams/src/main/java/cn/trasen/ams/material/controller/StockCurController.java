package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.StockCur;
import cn.trasen.ams.material.service.StockCurService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName StockCurController
 * @Description TODO
 * @date 2025年7月31日 上午9:50:36
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "StockCurController")
public class StockCurController {

	private transient static final Logger logger = LoggerFactory.getLogger(StockCurController.class);

	@Autowired
	private StockCurService stockCurService;

	/**
	 * @Title saveStockCur
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/material/stockCur/save")
	public PlatformResult<String> saveStockCur(@RequestBody StockCur record) {
		try {
			stockCurService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateStockCur
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/material/stockCur/update")
	public PlatformResult<String> updateStockCur(@RequestBody StockCur record) {
		try {
			stockCurService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectStockCurById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<StockCur>
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/material/stockCur/{id}")
	public PlatformResult<StockCur> selectStockCurById(@PathVariable String id) {
		try {
			StockCur record = stockCurService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteStockCurById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/material/stockCur/delete/{id}")
	public PlatformResult<String> deleteStockCurById(@PathVariable String id) {
		try {
			stockCurService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectStockCurList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<StockCur>
	 * @date 2025年7月31日 上午9:50:36
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/stockCur/list")
	public DataSet<StockCur> selectStockCurList(Page page, StockCur record) {
		return stockCurService.getDataSetList(page, record);
	}
}
