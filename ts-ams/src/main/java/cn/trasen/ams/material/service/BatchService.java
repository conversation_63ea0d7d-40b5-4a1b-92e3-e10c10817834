package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.Batch;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName BatchService
 * @Description TODO
 * @date 2025年7月31日 上午11:55:09
 */
public interface BatchService {

    /**
     * @param record
     * @return Integer
     * @Title save
     * @Description 新增
     * @date 2025年7月31日 上午11:55:09
     * <AUTHOR>
     */
    Integer save(Batch record);

    /**
     * @param record
     * @return Integer
     * @Title update
     * @Description 修改
     * @date 2025年7月31日 上午11:55:09
     * <AUTHOR>
     */
    Integer update(Batch record);

    /**
     * @param id
     * @return Integer
     * @Title deleteById
     * @Description 根据ID删除
     * @date 2025年7月31日 上午11:55:09
     * <AUTHOR>
     */
    Integer deleteById(String id);

    /**
     * @return Batch
     * @Title selectById
     * @Description 根据ID查询
     * @date 2025年7月31日 上午11:55:09
     * <AUTHOR>
     */
    Batch selectById(String id);

    /**
     * @param page
     * @param record
     * @return DataSet<Batch>
     * @Title getDataSetList
     * @Description 分页查询
     * @date 2025年7月31日 上午11:55:09
     * <AUTHOR>
     */
    DataSet<Batch> getDataSetList(Page page, Batch record);
    
    void batchInsert(List<Batch> batchList);
}
