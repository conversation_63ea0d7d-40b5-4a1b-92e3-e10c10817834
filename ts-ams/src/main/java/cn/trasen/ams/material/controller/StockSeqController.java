package cn.trasen.ams.material.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.StockSeq;
import cn.trasen.ams.material.service.StockSeqService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName StockSeqController
 * @Description TODO
 * @date 2025年7月31日 上午9:50:48
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "StockSeqController")
public class StockSeqController {

	private transient static final Logger logger = LoggerFactory.getLogger(StockSeqController.class);

	@Autowired
	private StockSeqService stockSeqService;

	/**
	 * @Title saveStockSeq
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/material/stockCur/save")
	public PlatformResult<String> saveStockSeq(@RequestBody StockSeq record) {
		try {
			stockSeqService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateStockSeq
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/material/stockCur/update")
	public PlatformResult<String> updateStockSeq(@RequestBody StockSeq record) {
		try {
			stockSeqService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectStockSeqById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<StockSeq>
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/material/stockCur/{id}")
	public PlatformResult<StockSeq> selectStockSeqById(@PathVariable String id) {
		try {
			StockSeq record = stockSeqService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteStockSeqById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/material/stockCur/delete/{id}")
	public PlatformResult<String> deleteStockSeqById(@PathVariable String id) {
		try {
			stockSeqService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectStockSeqList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<StockSeq>
	 * @date 2025年7月31日 上午9:50:48
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/material/stockCur/list")
	public DataSet<StockSeq> selectStockSeqList(Page page, StockSeq record) {
		return stockSeqService.getDataSetList(page, record);
	}
}
