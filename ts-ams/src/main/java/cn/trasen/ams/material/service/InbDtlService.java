package cn.trasen.ams.material.service;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.ams.material.model.InbDtl;

/**
 * @ClassName InbDtlService
 * @Description TODO
 * @date 2025年7月31日 上午9:50:10
 * <AUTHOR>
 * @version 1.0
 */
public interface InbDtlService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:10
	 * <AUTHOR>
	 */
	Integer save(InbDtl record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:10
	 * <AUTHOR>
	 */
	Integer update(InbDtl record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2025年7月31日 上午9:50:10
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return InbDtl
	 * @date 2025年7月31日 上午9:50:10
	 * <AUTHOR>
	 */
	InbDtl selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<InbDtl>
	 * @date 2025年7月31日 上午9:50:10
	 * <AUTHOR>
	 */
	DataSet<InbDtl> getDataSetList(Page page, InbDtl record);
}
