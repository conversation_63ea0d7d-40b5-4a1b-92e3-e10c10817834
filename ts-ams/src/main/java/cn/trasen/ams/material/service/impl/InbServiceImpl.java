package cn.trasen.ams.material.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import cn.trasen.ams.common.constant.CommonConst;
import cn.trasen.ams.common.constant.WarehouseConst;
import cn.trasen.ams.common.model.Warehouse;
import cn.trasen.ams.common.service.WarehouseService;
import cn.trasen.ams.material.bean.inb.InbInsertReq;
import cn.trasen.ams.material.constant.OrdConst;
import cn.trasen.ams.material.model.InbDtl;
import cn.trasen.ams.material.service.BatchService;
import cn.trasen.ams.material.service.MethodCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.ams.material.dao.InbMapper;
import cn.trasen.ams.material.model.Inb;
import cn.trasen.ams.material.service.InbService;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName InbServiceImpl
 * @Description TODO
 * @date 2025年7月31日 上午9:49:53
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class InbServiceImpl implements InbService {

    @Autowired
    private InbMapper mapper;

    @Autowired
    private MethodCodeService methodCodeService;

    @Autowired
    private BatchService batchService;

    @Autowired
    private WarehouseService warehouseService;


    @Transactional(readOnly = false)
    @Override
    public Integer save(Inb record) {

        if (record.getId() == null) {
            record.setId(IdGeneraterUtils.nextId());
        }

        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
            record.setSsoOrgCode(user.getCorpcode());
            record.setSsoOrgName(user.getOrgName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptname());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(Inb record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }


    private void prepare(InbInsertReq record) {
        Inb inb = record.getInb();
        List<InbDtl> inbDtlList = record.getInbDtlList();
        Warehouse warehouse = warehouseService.selectById(inb.getWhId());

        // 新增
        if (inb.getId() == null) {
            String flowNo = methodCodeService.genFlowNo(inb.getMtdCodeId());
            // 写入主表数据
            inb.setFlowNo(flowNo);
            // 默认状态
            inb.setStat(OrdConst.ORD_STAT_REGED);
            inb.setPrintStat(CommonConst.NO);
            inb.setReturnStat(CommonConst.NO);
        }
        // 计算总价格
        BigDecimal totalAmt = new BigDecimal(0);
        inbDtlList.forEach(inbDtl -> {
            inbDtl.setInbId(inb.getId());
            inbDtl.setTotalAmt(inbDtl.getPrice().multiply(new BigDecimal(inbDtl.getNum())));
            totalAmt.add(inbDtl.getTotalAmt());
            // 如果是批次管理，则需要为每一条明细生成批次
            if (WarehouseConst.STK_MTD_BATCH.equals(warehouse.getStkMtd())) {

            }
        });

    }

    @Transactional(readOnly = false)
    @Override
    public void insert(InbInsertReq record) {

        Inb inb = record.getInb();
        List<InbDtl> inbDtlList = record.getInbDtlList();
        // 根据方式码生成单据号


        save(inb);


        // 为详细数据写入主表ID
        // 如果本单对应的仓库启用批次，为每一条明细生成批次
        // 写入详情数据

    }

    @Transactional(readOnly = false)
    @Override
    public void edit(InbInsertReq record) {
        //
    }

    @Transactional(readOnly = false)
    @Override
    public void confirm(String inbId) {
        // 处理主数据状态
        // 更新实时库存
        // 添加库存时序记录
    }

    @Transactional(readOnly = false)
    @Override
    public void batchConfirm(List<String> inbIdList) {

    }

    @Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        Inb record = new Inb();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Inb selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<Inb> getDataSetList(Page page, Inb record) {
        Example example = new Example(Inb.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<Inb> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }
}
