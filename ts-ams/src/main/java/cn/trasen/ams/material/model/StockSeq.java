package cn.trasen.ams.material.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "m_stock_seq")
@Setter
@Getter
public class StockSeq {
    /**
     * 主键
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 相关单据主键ID
     */
    @Column(name = "ord_id")
    @ApiModelProperty(value = "相关单据主键ID")
    private String ordId;

    /**
     * 相关单据明细表ID
     */
    @Column(name = "ord_dtl_id")
    @ApiModelProperty(value = "相关单据明细表ID")
    private String ordDtlId;

    /**
     * 单据类型 0 入库单 1 出库单 2 退货单 3 退库单
     */
    @Column(name = "ord_type")
    @ApiModelProperty(value = "单据类型 0 入库单 1 出库单 2 退货单 3 退库单")
    private String ordType;

    /**
     * 物资字典ID
     */
    @Column(name = "sku_id")
    @ApiModelProperty(value = "物资字典ID")
    private String skuId;

    /**
     * 仓库ID
     */
    @Column(name = "wh_id")
    @ApiModelProperty(value = "仓库ID")
    private String whId;

    /**
     * 批次号
     */
    @Column(name = "batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /**
     * 变动数量 （有符号 方便计算）
     */
    @ApiModelProperty(value = "变动数量 （有符号 方便计算）")
    private Integer num;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 部门ID
     */
    @Column(name = "dept_id")
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门名称
     */
    @Column(name = "dept_name")
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;

    /**
     * 是否删除 Y N
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "是否删除 Y N")
    private String isDeleted;
}