<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.ams.material.dao.StockSeqMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.ams.material.model.StockSeq">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ord_id" jdbcType="VARCHAR" property="ordId" />
    <result column="ord_dtl_id" jdbcType="VARCHAR" property="ordDtlId" />
    <result column="ord_type" jdbcType="CHAR" property="ordType" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="ware_id" jdbcType="VARCHAR" property="wareId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
    <result column="sso_org_name" jdbcType="VARCHAR" property="ssoOrgName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
  </resultMap>
</mapper>