package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.service.RedisService;
import cn.trasen.ams.common.service.SerialNoGenService;
import cn.trasen.ams.common.service.StepperService;
import cn.trasen.ams.common.util.CommonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: SerialNoGenServiceImpl
 * @author: chenbin
 * @description: TODO
 * @date: 2024/12/14 16:41
 * @version: 1.0
 */
@Service
public class SerialNoGenServiceImpl implements SerialNoGenService {

    @Autowired
    private StepperService stepperService;

    @Autowired
    private RedisService redisService;

    @Override
    public String genByDate(String key) {
        key = key + "-" + CommonUtil.getCurDate("yyyyMMdd");
        long step = stepperService.step(key, 86400);
        String code = String.format("%s-%04d", key, step);
        return code;
    }

    @Override
    public String genByDate(String key, String tpl, int length) {
        // tpl 可以是任意可能的日期格式 如
        return "";
    }


    @Override
    public String genCommonNo(String key, int length) {
        long step = stepperService.step(key, 0);
        String code = String.format("%s%0" + length + "d", key, step);
        return code;
    }

    @Override
    public String genAssetNo() {
        // 获取当前年份 只保留后两位
        String year = CommonUtil.getCurDate("yy");
        String key = "ASSET_NO" + year;
        // 防止闰月？好像阳历根本不需要关心，但是这个数据很小，多存一段时间也无所谓
        long step = stepperService.step(key, 86400 * 400);
        // 5位数字
        String code = String.format("%s%06d", year, step);
        return code;
    }
}
