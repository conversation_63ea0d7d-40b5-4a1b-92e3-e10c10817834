package cn.trasen.ams.common.service.impl;

import cn.trasen.ams.common.service.StepperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @projectName: apps
 * @package: cn.trasen.ams.common.service.impl
 * @className: DaySteperServiceImpl
 * @author: chenbin
 * @description: 时间步进器
 * @date: 2024/9/18 17:51
 * @version: 1.0
 */

@Primary
@Service("daySteperService")
public class DaySteperServiceImpl implements StepperService {

    @Autowired
    private RedisTemplate redisTemplate;

    private String prefix = "ams:step:";


    private String cacheKey(String key) {
        return prefix + key;
    }

    @Override
    public long step(String key, int expire) {

        long step = redisTemplate.opsForValue().increment(cacheKey(key), 1);
        redisTemplate.expire(key, expire, TimeUnit.SECONDS);
        return step;
    }

    @Override
    public void fix(String key, int value) {
        // key 的数据进行更新修正
        String cacheKey = cacheKey(key);
        // 先获取剩余过期时间
        Long expire = redisTemplate.getExpire(cacheKey);
        // 设置新值
        redisTemplate.opsForValue().set(cacheKey, value);
        // 如果原来有过期时间，重新设置
        if (expire != null && expire > 0) {
            redisTemplate.expire(cacheKey, expire, TimeUnit.SECONDS);
        }
    }


}
