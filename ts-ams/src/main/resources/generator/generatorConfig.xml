<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="application.properties"/>

    <context id="Mysql" targetRuntime="MyBatis3Simple">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>

        <plugin type="cn.trasen.mapper.generate.plugins.SwaggerMapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
        </plugin>

        <plugin type="cn.trasen.mapper.generate.plugins.GenerateBizCodePlugin">
            <property name="author" value="chenbin"/>
            <property name="restfulUrl" value="material/batch"/>
        </plugin>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${spring.datasource.driver-class-name}"
                        connectionURL="${spring.datasource.url}" userId="${spring.datasource.username}"
                        password="${spring.datasource.password}">

            <property name="remarksReporting" value="true"></property>

        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <javaModelGenerator targetPackage="cn.trasen.ams.material.model"
                            targetProject="src/main/java"/>
        <sqlMapGenerator targetPackage="cn.trasen.ams.material.mapper" targetProject="src/main/java"/>
        <javaClientGenerator targetPackage="cn.trasen.ams.material.dao"
                             targetProject="src/main/java" type="XMLMAPPER"/>
        <table tableName="m_batch">
            <domainObjectRenamingRule searchString="^M" replaceString=""/>
        </table>
    </context>
</generatorConfiguration>