INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('11EF85A218D7499BAD4F3C7C5BC53715', '', NULL, 'ZCSB_YBZCZY_GLY', '资产设备一般资产转移管理员', '1', '0', 'AMS-用于审核一般资产转科的单子', 'admin', '2025-04-18 01:38:31', 'admin', '2025-05-22 06:01:47', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('1603ECD24826464C8368B5C8F1E39D70', '', NULL, 'ZCSB_ADMIN', '资产设备管理员', '1', '0', 'AMS_资产设备最高管理员', 'admin', '2025-03-08 07:18:33', 'admin', '2025-06-03 08:54:04', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('26BFE61FB626458D8B12CD2FC64FCE7C', '', NULL, 'ZCSB_YLSBCGDD_GLY', '资产设备医疗设备采购订单管理员', '1', '0', 'AMS-资产设备医疗设备采购订单管理员', 'admin', '2025-06-23 03:56:38', 'admin', '2025-06-23 03:56:38', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('6CCFFA477D364AEA8E42F57557DFADAF', '', NULL, 'ZCSB_CKSDY', '资产设备仓库审单员', '1', '0', 'AMS_资产设备仓库审单员', 'admin', '2025-03-08 07:49:53', 'admin', '2025-06-03 08:54:14', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('8217F2FC6B03405FBCF0DC48091BD952', '', NULL, 'ZCSB_YBZCCZ_GLY', '资产设备一般资产处置管理员', '1', '0', 'AMS-用于对临床科室申请的一般资产处置申请进行审核', 'admin', '2025-04-18 01:51:58', 'admin', '2025-05-22 06:01:28', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('93F66CC2D6D54C3E800F5DB2DE445172', '', NULL, 'ZCSB_YLSBCZ_GLY', '资产设备医疗设备处置管理员', '1', '0', 'AMS-用于对临床科室申请的医疗设备处置申请进行审核', 'admin', '2025-04-18 02:34:04', 'admin', '2025-05-22 06:01:55', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('94DA56733B014C50A203628A2A903E73', '', NULL, 'ZCSB_YBZCCGDD_GLY', '资产设备一般资产采购订单管理员', '1', '0', 'AMS-资产设备一般资产采购订单管理员', 'admin', '2025-06-23 03:52:47', 'admin', '2025-06-23 05:50:32', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('A0741886A25F4FB083049B9CC57C6125', '', NULL, 'ZCSB_YBZCPD_GLY', '资产设备一般资产盘点管理员', '1', '0', 'AMS_资产设备一般资产盘点管理员', 'admin', '2025-03-08 07:52:03', 'admin', '2025-06-03 08:54:34', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('B3E545F2BEC24F0E819B6E38D26CCE5A', '', NULL, 'ZCSB_YLSBZY_GLY', '资产设备医疗设备转移管理员', '1', '0', 'AMS-用于对临床科室申请的医疗设备转移申请进行审核', 'admin', '2025-04-18 01:40:22', 'admin', '2025-05-22 06:02:11', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('B3E798066CD34825B2E840BC8F7F7490', '', NULL, 'ZCSB_YLSBPD_GLY', '资产设备医疗设备盘点管理员', '1', '0', 'AMS_资产设备医疗设备盘点管理员', 'admin', '2025-03-08 07:52:43', 'admin', '2025-06-03 08:54:51', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('06412A26DDA34220B6B6F541F6A63E71', 'InitialImportBtn', '期初导入', 'ADB8130C97574528A0AF1EB4723C247A', 'AMS-期初导入', 'admin', '2025-05-22 06:18:04', 'admin', '2025-05-22 06:23:38', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('715C4BC44565448CBE4A89BD53FBAA1A', 'putInConfirmBtn', '确认入库', 'A049E1920B414ECC8A9386CFCA747D32', 'AMS-确认入库', 'admin', '2025-05-15 09:04:19', 'admin', '2025-05-22 06:22:44', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('76FCCB7701CB4EEE91CF002FCD8EEB27', 'disposalApprovedBtn', '处置审批', 'B44064C74F3F4B10AC11053F7D7A6BAF', 'AMS-处置审批', 'admin', '2025-04-29 03:52:43', 'admin', '2025-05-22 05:50:06', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('A54615FE771D46088D1DD62AD1B232C6', 'initialAddBtn', '期初新增', 'ADB8130C97574528A0AF1EB4723C247A', 'AMS-期初新增', 'admin', '2025-05-22 06:06:37', 'admin', '2025-05-22 06:17:21', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('A6072521F92643CBAA7C1759B035F715', 'transferApprovedBtn', '转移审批', '10EB1EE1047C43FFB6052C54190B5A0A', 'AMS-转移审批', 'admin', '2025-04-28 03:35:01', 'admin', '2025-05-22 05:49:55', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('C1787E68D00444A09A7ADCF22E8B7FDF', 'outboundConfirmBtn', '确认出库', '94913AC9C1B34568B1C053B6E8343EF5', 'AMS-确认出库', 'admin', '2025-05-22 06:23:07', 'admin', '2025-05-22 06:23:07', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_operation (`id`, `resource_id`, `resource_name`, `menu_id`, `resource_title`, `createuser`, `createtime`, `updateuser`, `updatetime`, `url`, `resource_icon`, `PUBLISH_FROM_DESIGN`, `CONTROL_TYPE`) VALUES ('E5B861C648584543B83EAABE8CFB0F60', 'hasOperateBtn', '资产盘点 新增，编辑，删除按钮权限', '7D9B263DEAF34BEFA599F996C264CF2F', 'AMS-资产盘点 新增，编辑，删除按钮权限', 'admin', '2025-05-14 09:00:15', 'admin', '2025-05-22 08:35:15', '', '', NULL, '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `resource_id` = VALUES(`resource_id`), `resource_name` = VALUES(`resource_name`), `menu_id` = VALUES(`menu_id`), `resource_title` = VALUES(`resource_title`), `createuser` = VALUES(`createuser`), `createtime` = VALUES(`createtime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `url` = VALUES(`url`), `resource_icon` = VALUES(`resource_icon`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `CONTROL_TYPE` = VALUES(`CONTROL_TYPE`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('0261308878D0440D86F76FF7256C103A', '3', 'BFDF80657B614235900381776F2A14B6', '设备台账', '/ts-web-equipment/management/equipment-ledger', '1', '1', '1', '', 'admin', '2025-01-15 10:09:01', 'admin', '2025-05-22 05:57:29', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-设备台账', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('06C3D774C35E4FBB9AF2F278810542AD', '1', 'C2F079CE73B344D8A721E0BB60D513FE', '设备全生命周期', '#', '10', '1', '1', '', 'admin', '2025-01-15 10:06:30', 'admin', '2025-05-22 03:48:37', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-设备全生命周期', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('0C423AEB7175484DAE00209E576678AA', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '品牌管理', '/ts-web-equipment/setting/brand-management', '2', '1', '1', '', 'admin', '2025-01-15 10:13:28', 'admin', '2025-05-22 03:53:02', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-品牌管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('0E4D1932F3EB45318367423A43F84185', '3', '9C5887961D334F2988303FF490D6C816', '办理查阅', '/ts-web-equipment/purchase-management/view', '3', '1', '1', '', 'admin', '2025-06-03 03:19:55', 'admin', '2025-06-03 03:19:55', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-办理查阅', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('10EB1EE1047C43FFB6052C54190B5A0A', '3', 'E818706DC83B47A088AC9E28448FFBE9', '资产转移', '/ts-web-equipment/equipment-asset-management/asset-transfer', '5', '1', '1', '', 'admin', '2025-04-18 07:36:33', 'admin', '2025-05-22 05:48:33', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-资产转移', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('10F7C90237A846C9841FBCB2A39C3462', '2', '06C3D774C35E4FBB9AF2F278810542AD', '出入库管理', '#', '3', '1', '1', '', 'admin', '2025-02-07 01:12:48', 'admin', '2025-06-03 09:06:39', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-出入库管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('2C5204185D7C4150B343286DC24675AD', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '资产字典', '/ts-web-equipment/setting/equipment-dictionary', '5', '1', '1', '', 'admin', '2025-01-15 10:15:30', 'admin', '2025-06-03 09:08:34', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-资产字典', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('3AD7C22616234AC984DF9A5CC5005A42', '3', 'BFDF80657B614235900381776F2A14B6', '安装验收', '/ts-web-equipment/management/install-acceptance', '2', '1', '1', '', 'admin', '2025-06-30 06:48:33', 'admin', '2025-07-02 09:10:42', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-安装验收', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('4C3E260C9ED14DBAB2D2BDE6A54F1649', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '厂家管理', '/ts-web-equipment/setting/manufacturer-management', '1', '1', '1', '', 'admin', '2025-01-15 10:13:03', 'admin', '2025-05-22 03:53:10', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-厂家管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('56E0134B87C342B7A3A9230656D4F571', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '系统设置', '/ts-web-equipment/setting/system', '6', '1', '1', '', 'admin', '2025-01-15 10:16:01', 'admin', '2025-05-22 03:51:33', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-系统设置', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('59081815F53B44DC9D0ABEF42AA3A671', '3', '9C5887961D334F2988303FF490D6C816', '采购申请', '/ts-web-equipment/purchase-management/apply', '1', '1', '1', '', 'admin', '2025-05-23 02:52:46', 'admin', '2025-06-03 03:18:59', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-采购申请', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('5CB40818C2394A3CBC7B25AB62331426', '3', '9C5887961D334F2988303FF490D6C816', '采购审批', '/ts-web-equipment/purchase-management/approve', '2', '1', '1', '', 'admin', '2025-05-23 02:53:08', 'admin', '2025-06-03 03:18:36', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-采购审批', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('7D9B263DEAF34BEFA599F996C264CF2F', '3', 'E818706DC83B47A088AC9E28448FFBE9', '设备盘点', '/ts-web-equipment/equipment-asset-management/equipment-inventory', '2', '1', '1', '', 'admin', '2025-02-21 06:26:48', 'admin', '2025-06-03 09:07:25', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-设备盘点', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('8990B1453B974861BC5E4C9A93CAB397', '3', 'E818706DC83B47A088AC9E28448FFBE9', '异常记录', '/ts-web-equipment/equipment-asset-management/lost-records', '2', '1', '1', '', 'admin', '2025-02-21 06:27:32', 'admin', '2025-05-22 05:48:24', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-异常记录', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('94913AC9C1B34568B1C053B6E8343EF5', '3', '10F7C90237A846C9841FBCB2A39C3462', '出库管理', '/ts-web-equipment/inventory-management/outbound-record', '3', '1', '1', '', 'admin', '2025-02-07 01:13:36', 'admin', '2025-05-22 05:51:18', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-出库管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('9C5887961D334F2988303FF490D6C816', '2', '06C3D774C35E4FBB9AF2F278810542AD', '采购管理', '#', '6', '1', '1', '', 'admin', '2025-05-16 03:18:00', 'admin', '2025-05-22 03:49:32', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-采购管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('A049E1920B414ECC8A9386CFCA747D32', '3', '10F7C90237A846C9841FBCB2A39C3462', '入库管理', '/ts-web-equipment/inventory-management/storage-record', '2', '1', '1', '', 'admin', '2025-02-07 01:13:24', 'admin', '2025-05-22 05:50:59', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-出入管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('ADB8130C97574528A0AF1EB4723C247A', '3', 'E818706DC83B47A088AC9E28448FFBE9', '资产台账', '/ts-web-equipment/inventory-management/inventory-device', '1', '1', '1', '', 'admin', '2025-02-07 01:13:07', 'admin', '2025-06-03 09:07:37', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-资产台账', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('B44064C74F3F4B10AC11053F7D7A6BAF', '3', 'E818706DC83B47A088AC9E28448FFBE9', '资产处置', '/ts-web-equipment/equipment-asset-management/asset-disposal', '5', '1', '1', '', 'admin', '2025-04-18 02:48:30', 'admin', '2025-05-22 05:48:59', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-资产处置', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('B763B750B33A49E48ADD840CCFE0EF35', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '供应商管理', '/ts-web-equipment/setting/supplier-management', '3', '1', '1', '', 'admin', '2025-01-15 10:13:49', 'admin', '2025-05-22 03:51:45', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-供应商管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('BFDF80657B614235900381776F2A14B6', '2', '06C3D774C35E4FBB9AF2F278810542AD', '设备管理', '#', '1', '1', '1', '', 'admin', '2025-01-15 10:08:17', 'admin', '2025-05-22 05:57:37', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-设备管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('C2DC10E5687F4F3CBC32FE1E32B92543', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '设备分类字典', '/ts-web-equipment/setting/classification-dictionary', '4', '1', '1', '', 'admin', '2025-01-15 10:14:16', 'admin', '2025-06-03 09:08:42', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-设备分类字典', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('D2143C554B7B419CA6EF96EA3CB5C31E', '3', 'EC511C46B2B74FB190CBA9DDF410E1AA', '固定资产分类', '/ts-web-equipment/equipment-asset-management/asset-classification', '5', '1', '1', '', 'admin', '2025-03-28 02:32:48', 'admin', '2025-05-22 03:50:53', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-固定资产分类', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('D38D83EE3C2A4C0B8BD611606EACDE83', '3', '9C5887961D334F2988303FF490D6C816', '合同登记', '/ts-web-oa/equipment/contract-management', '4', '1', '1', '', 'admin', '2025-05-16 03:18:54', 'admin', '2025-05-23 02:54:24', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-合同登记', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('E818706DC83B47A088AC9E28448FFBE9', '2', '06C3D774C35E4FBB9AF2F278810542AD', '资产管理', '#', '4', '1', '1', '', 'admin', '2025-02-21 06:25:21', 'admin', '2025-06-03 09:06:30', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-资产管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('E8E1BCB4BAD44A61B96BEDCEF219CD36', '3', '9C5887961D334F2988303FF490D6C816', '采购订单管理', '/ts-web-equipment/purchase-management/order-management', '3', '1', '1', '', 'admin', '2025-05-23 02:53:57', 'admin', '2025-05-23 02:54:48', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-采购订单管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('EC511C46B2B74FB190CBA9DDF410E1AA', '2', '06C3D774C35E4FBB9AF2F278810542AD', '基础设置', '#', '5', '1', '1', '', 'admin', '2025-01-15 10:12:43', 'admin', '2025-05-22 03:49:01', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-基础设置', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848756468902707200', 'AMS_YES_OR_NO', '是否', '是否', 'AMS', 'admin', 'admin', '2024-12-12 10:55:11', 'admin', 'admin', '2024-12-12 10:56:22', 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848758731989762048', 'AMS_AHT_OWNER', '设备附件所属', '', 'AMS', 'admin', 'admin', '2024-12-12 11:04:11', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848759289375014912', 'AMS_DEVICE_STATUS', '设备状态', '设备状态', 'AMS', 'admin', 'admin', '2024-12-12 11:06:24', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848761511785709568', 'AMS_DEPRECIATION_METHOD', '折旧方法', '折旧方法', 'AMS', 'admin', 'admin', '2024-12-12 11:15:14', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848762551889215488', 'AMS_MAINT_CONTRACT_STATUS', '保养合同状态', '保养合同状态', 'AMS', 'admin', 'admin', '2024-12-12 11:19:22', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848763315034439680', 'AMS_LIFE_EVENT_TYPE', '生命周期事件类型', '生命周期事件类型', 'AMS', 'admin', 'admin', '2024-12-12 11:22:24', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848766213172674560', 'AMS_YMD', '年月日', '年月日', 'AMS', 'admin', 'admin', '2024-12-12 11:33:55', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848767198406295552', 'AMS_CALIBRATION_TYPE', '强检类型', '强检类型', 'AMS', 'admin', 'admin', '2024-12-12 11:37:49', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('848768611958046720', 'AMS_SUPPLIER_LEVEL', '供应商级别', '供应商级别', 'AMS', 'admin', 'admin', '2024-12-12 11:43:26', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('849493275344814080', 'AMS_MAINT_PLAN_ARCHIVE_STATUS', '保养计划归档状态', '', 'AMS', 'admin', 'admin', '2024-12-14 11:43:00', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('849493793735622656', 'AMS_MAINT_PLAN_STATUS', '保养计划状态', '保养计划状态', 'AMS', 'admin', 'admin', '2024-12-14 11:45:03', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('850627135617024000', 'AMS_MAINT_TASK_MAINT_STATUS', '保养状态', '保养状态', 'AMS', 'admin', 'admin', '2024-12-17 14:48:33', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('850628743289552896', 'AMS_MAINT_TAKS_STATUS', '任务状态', '', 'AMS', 'admin', 'admin', '2024-12-17 14:54:56', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('850635473964679168', 'AMS_MAINT_TASK_COMPLETE_STATUS', '任务完成状态', '任务完成状态', 'AMS', 'admin', 'admin', '2024-12-17 15:21:41', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('850656529177821184', 'AMS_MAINT_COST_TYPE', '保养费用类型', '保养费用类型', 'AMS', 'admin', 'admin', '2024-12-17 16:45:21', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('851378286788526080', 'AMS_MAINT_TASK_SOLUTION', '任务异常处理方案', '任务异常处理方案', 'AMS', 'admin', 'admin', '2024-12-19 16:33:21', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('851397775177465856', 'AMS_DIY_TPL_INSPECTION_CATE', '自定义模板巡检分类', '自定义模板巡检分类', 'AMS', 'admin', 'admin', '2024-12-19 17:50:48', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('851720913702535168', 'AMS_INSPECTION_PLAN_STATUS', '巡检计划状态', '巡检计划状态', 'AMS', 'admin', 'admin', '2024-12-20 15:14:50', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('851721721269633024', 'AMS_INSPECTION_PLAN_ARCHIVE_STATUS', '巡检计划归档状态', '巡检计划归档状态', 'AMS', 'admin', 'admin', '2024-12-20 15:18:03', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('852829139026231296', 'AMS_INSPECTION_TASK_INSPECTION_STATUS', '设备巡检完上报的状态', '设备巡检完上报的状态', 'AMS', 'admin', 'admin', '2024-12-23 16:38:32', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('852830920460386304', 'AMS_INSPECTION_TASK_STATUS', '巡检任务状态', '巡检任务状态', 'AMS', 'admin', 'admin', '2024-12-23 16:45:36', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('852831791122399232', 'AMS_INSPECTION_TASK_COMPLETE_STATUS', '巡检任务完成状态', '巡检任务完成状态', 'AMS', 'admin', 'admin', '2024-12-23 16:49:04', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('852832521472360448', 'AMS_INSPECTION_TASK_SOLUTION', '巡检任务异常处理方案', '', 'AMS', 'admin', 'admin', '2024-12-23 16:51:58', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('853517340871147520', 'AMS_MAINT_PLAN_LEVEL', '保养级别', '保养级别', 'AMS', 'admin', 'admin', '2024-12-25 14:13:12', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('853577984811122688', 'AMS_ENGINEER_ITEM', '作业人员类型', '作业人员类型', 'AMS', 'admin', 'admin', '2024-12-25 18:14:10', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('861160785873760256', 'AMS_YES_OR_NO', '是否', '是否', 'AMS', 'admin', 'admin', '2025-01-15 16:25:31', NULL, NULL, NULL, 'Y', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('871202836100526080', 'AMS_INBOUND_ORDER_TYPE', '入库单类型', '入库单类型', 'AMS', 'admin', 'admin', '2025-02-12 09:29:02', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('871202910847217664', 'AMS_OUTBOUND_ORDER_TYPE', '出库单类型', '出库单类型', 'AMS', 'admin', 'admin', '2025-02-12 09:29:20', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('871945269721542656', 'AMS_INBOUND_ORDER_STATUS', '入库单状态', '入库单状态', 'AMS', 'admin', 'admin', '2025-02-14 10:39:12', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('873118507579322368', 'AMS_OUTBOUND_ORDER_STATUS', '出入库订单状态', '出入库订单状态', 'AMS', 'admin', 'admin', '2025-02-17 16:21:14', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('875255468972580864', 'AMS_INVENTORY_TASK_STATUS', '盘点任务状态', '盘点任务状态', 'AMS', 'admin', 'admin', '2025-02-23 00:00:00', 'admin', 'admin', '2025-02-23 13:54:34', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('875256047547457536', 'AMS_INVENTORY_TASK_RET', '盘点结果', '盘点结果', 'AMS', 'admin', 'admin', '2025-02-23 13:55:03', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('875630203963187200', 'AMS_INVENTORY_PLAN_STATUS', '盘点计划状态', '盘点计划状态', 'AMS', 'admin', 'admin', '2025-02-24 14:41:49', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('878834828006662144', 'AMS_SKU_TYPE', '资产类别', '0 医疗设备 1 普通资产', 'AMS', 'admin', 'admin', '2025-03-05 10:55:51', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('878836527203098624', 'AMS_DEVICE_WAREHOUSE_STATUS', '资产在库状态', '资产在库状态', 'AMS', 'admin', 'admin', '2025-03-05 11:02:36', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('879290552306376704', 'AMS_ASSET_TYPE', '资产类型', '资产类型', 'AMS', 'admin', 'admin', '2025-03-06 17:06:44', NULL, NULL, NULL, 'Y', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('886903239551066112', 'AMS_SKU_NEED_INSTALL', '资产是否需要安装', '资产是否需要安装', 'AMS', 'admin', 'admin', '2025-03-27 17:16:50', NULL, NULL, NULL, 'Y', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('888669091338231808', 'AMS_DEVICE_TAG', '资产标签', '资产标签', 'AMS', 'admin', 'admin', '2025-04-01 14:13:42', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('894788563857842176', 'AMS_CHECK', '通用审核状态', '通用审核状态', 'AMS', 'admin', 'admin', '2025-04-18 00:00:00', 'admin', 'admin', '2025-04-22 11:20:48', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('896956816147390464', 'AMS_ROLE_SKU_TYPE', '描述角色与SKU_TYPE的关系', '描述角色与SKU_TYPE的关系', 'AMS', 'admin', 'admin', '2025-04-24 11:06:10', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('896957297817067520', 'AMS_BUSINESS_ROLE', '描述业务与角色的关系', '本字典不可以禁用与修改配置项的业务KEY', 'AMS', 'admin', 'admin', '2025-04-24 11:08:04', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('898467406305075200', 'AMS_DISPOSAL_TYPE', '处置类型', '处置类型', 'AMS', 'admin', 'admin', '2025-04-28 15:08:42', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('908901915500552192', 'AMS_PURCHASE_INS_DEFINITION_LIST', '采购相关的流程列表', '采购相关的流程列表', 'AMS', 'admin', 'admin', '2025-05-27 00:00:00', 'admin', 'admin', '2025-05-27 14:53:33', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('908960578722406400', 'AMS_PURCHASE_INS_STATUS', '采购流程状态列表', '采购流程状态列表', 'AMS', 'admin', 'admin', '2025-05-27 14:04:50', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('913994477005512704', 'AMS_PURCHASE_ORDER_PAGE_STATUS', '采购订单页面页头tab状态', '采购订单页面页头tab状态，不参与具体的业务，用于文档性建设', 'AMS', 'admin', 'admin', '2025-06-10 11:27:45', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('914043199441674240', 'AMS_PURCHASE_ORDER_STATUS', '采购订单状态', '采购订单状态', 'AMS', 'admin', 'admin', '2025-06-10 14:41:21', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('916513213289668608', 'AMS_PURCHASE_ORDER_INSTANCE_CONFIG', '采购订单相关流程配置', '采购订单相关流程配置', 'AMS', 'admin', 'admin', '2025-06-17 10:16:18', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('916972802170757120', 'AMS_PURCHASE_TYPE', '采购类型', '采购类型', 'AMS', 'admin', 'admin', '2025-06-18 16:42:33', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('917261360878133248', 'AMS_PURCHASE_FUND_SOURCE', '资金来源', '资金来源', 'AMS', 'admin', 'admin', '2025-06-19 11:49:10', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('918018008596946944', 'AMS_PURCHASE_ORDER_MATCH_TYPE', '采购订单匹配字典方式', '采购订单匹配字典方式', 'AMS', 'admin', 'admin', '2025-06-21 13:55:49', NULL, NULL, NULL, 'Y', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('919050156808413184', 'AMS_PURCHASE_ORDER_ARRIVED_STATUS', '采购订单到货状态', '采购订单到货状态', 'AMS', 'admin', 'admin', '2025-06-24 10:17:13', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('924904004974366720', 'AMS_SIGNOFF_STATUS', '安装验收状态', '安装验收状态', 'AMS', 'admin', 'admin', '2025-07-10 13:58:19', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848757610881662976', '848756468902707200', 'YES', '是', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-12 10:59:44', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848757718088073216', '848756468902707200', 'NO', '否', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:00:09', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848758921370976256', '848758731989762048', 'DEVICE', '设备', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:04:56', 'admin', 'admin', '2024-12-12 11:05:35', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848759049217556480', '848758731989762048', 'SKU', '设备字典', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 11:05:27', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848759624562819072', '848759289375014912', '未安装', '未安装', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-12 11:07:44', 'admin', 'admin', '2025-02-19 14:39:20', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848759724760547328', '848759289375014912', '使用中', '使用中', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:08:08', 'admin', 'admin', '2025-02-19 14:39:25', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848759862937698304', '848759289375014912', '故障中', '故障中', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 11:08:41', 'admin', 'admin', '2025-02-19 14:39:30', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848759961998770176', '848759289375014912', '闲置', '闲置', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-12 11:09:04', 'admin', 'admin', '2025-02-19 14:39:35', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848760825907953664', '848759289375014912', '已报废', '已报废', '', '', '4', 'AMS', 'admin', 'admin', '2024-12-12 11:12:30', 'admin', 'admin', '2025-02-19 14:39:40', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848760901149573120', '848759289375014912', '遗失', '遗失', '', '', '5', 'AMS', 'admin', 'admin', '2024-12-12 11:12:48', 'admin', 'admin', '2025-02-19 14:39:44', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '5', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('887270062079451136', '848759289375014912', '盘点异常', '盘点异常', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-03-28 17:34:27', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898498474248806400', '848759289375014912', '捐赠（出）', '捐赠（出）', NULL, NULL, '7', 'AMS', 'admin', 'admin', '2025-04-28 17:12:10', 'admin', 'admin', '2025-04-29 10:09:33', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '7', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898498718390853632', '848759289375014912', '其他', '其他', NULL, NULL, '8', 'AMS', 'admin', 'admin', '2025-04-28 17:13:08', 'admin', 'admin', '2025-04-29 10:09:38', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '8', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848761583315369984', '848761511785709568', 'STRAIGHT_LINE', '直线法', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-12 11:15:31', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848761657453887488', '848761511785709568', 'DECLINING_BALANCE', '递减余额法', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:15:48', 'admin', 'admin', '2024-12-12 11:16:26', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848761788303589376', '848761511785709568', 'SUM_OF_YEARS_DIGITS', '年数总和法', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 11:16:20', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848762894026981376', '848762551889215488', 'YES', '在保', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:20:43', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763008116244480', '848762551889215488', 'NO', '脱保', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-12 11:21:10', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763394088681472', '848763315034439680', 'TRANSFER', '转科', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:22:42', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763487365808128', '848763315034439680', 'ACCEPTANCE', '验收', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 11:23:05', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763586456240128', '848763315034439680', 'SCRAP', '报废', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-12 11:23:28', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763685072715776', '848763315034439680', 'REPAIR', '维修', '', '4', '4', 'AMS', 'admin', 'admin', '2024-12-12 11:23:52', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763836965240832', '848763315034439680', 'MAINTENANCE', '保养', '', '', '5', 'AMS', 'admin', 'admin', '2024-12-12 11:24:28', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '5', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848763945648046080', '848763315034439680', 'CALIBRATION', '计量', '', '', '6', 'AMS', 'admin', 'admin', '2024-12-12 11:24:54', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '6', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848764056700633088', '848763315034439680', 'INSPECTION', '巡检', '', '', '7', 'AMS', 'admin', 'admin', '2024-12-12 11:25:20', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '7', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848764153589055488', '848763315034439680', 'BORROW', '转借', '', '', '8', 'AMS', 'admin', 'admin', '2024-12-12 11:25:43', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '8', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848765874885279744', '848763315034439680', 'PURCHASE', '购入', '', '', '9', 'AMS', 'admin', 'admin', '2024-12-12 11:32:34', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '9', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848766301173366784', '848766213172674560', 'YEAR', '年', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:34:16', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848766361781059584', '848766213172674560', 'MONTH', '月', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 11:34:30', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848766426025213952', '848766213172674560', 'DAY', '日', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-12 11:34:45', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848767369416458240', '848767198406295552', 'FORCE', '强检', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 11:38:30', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848767453730357248', '848767198406295552', 'NORMAL', '非强检', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 11:38:50', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848800120505024512', '848768611958046720', 'LEVEL_1', '一级', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-12 13:48:39', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '一级', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848800320493633536', '848768611958046720', 'LEVEL_2', '二级', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-12 13:49:26', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '二级', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848800397203259392', '848768611958046720', 'LEVEL_3', '三级', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-12 13:49:45', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '三级', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848800452706484224', '848768611958046720', 'LEVEL_4', '四级', '', '', '4', 'AMS', 'admin', 'admin', '2024-12-12 13:49:58', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '四级', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('848800557106905088', '848768611958046720', 'LEVEL_5', '五级', '', '', '5', 'AMS', 'admin', 'admin', '2024-12-12 13:50:23', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '五级', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('849493402503528448', '849493275344814080', 'NO', '未归档', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-14 11:43:30', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('849493477522849792', '849493275344814080', 'YES', '已归档', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-14 11:43:48', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('849495033915817984', '849493793735622656', 'WAIT', '未开始', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-14 11:49:59', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('849495092673822720', '849493793735622656', 'DOING', '进行中', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-14 11:50:13', 'admin', 'admin', '2024-12-19 11:35:01', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('849495169890959360', '849493793735622656', 'DONE', '已完成', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-14 11:50:31', 'admin', 'admin', '2024-12-19 11:34:52', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('849522282798956544', '849493793735622656', 'OVERDUE', '超期未完成', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-14 13:38:16', 'admin', 'admin', '2024-12-19 11:34:42', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850638585160974336', '850627135617024000', 'NORMAL', '正常使用', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-17 15:34:03', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850638740710932480', '850627135617024000', 'LACK', '可以使用，部分功能失效', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-17 15:34:40', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850638867580239872', '850627135617024000', 'FAULT', '无法使用，故障维修', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-17 15:35:10', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850639066352500736', '850627135617024000', 'DEATH', '无法维修，待报废', '', '', '4', 'AMS', 'admin', 'admin', '2024-12-17 15:35:58', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850631208042938368', '850628743289552896', 'NORMAL', '正常', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-17 15:04:44', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850631339878301696', '850628743289552896', 'CANCEL', '已作废', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-17 15:05:15', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850635770875265024', '850635473964679168', 'WAIT', '未完成', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-17 15:22:52', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850635870104109056', '850635473964679168', 'COMPLETE', '已完成', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-17 15:23:15', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850656620315852800', '850656529177821184', 'WORK', '工时费', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-17 16:45:43', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850656822460334080', '850656529177821184', 'ACCESSORY', '配件费用', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-17 16:46:31', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('850664798944411648', '850656529177821184', 'OTHER', '其他费用', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-17 17:18:13', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851378450525765632', '851378286788526080', 'IGNORE', '暂不处理', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-19 16:34:01', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851378648597577728', '851378286788526080', 'REPAIR', '转保修', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-19 16:34:48', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851378820731813888', '851378286788526080', 'SCRAP', '转报废', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-19 16:35:29', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851397979591065600', '851397775177465856', 'IMPORTANT', '重点设备', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-19 17:51:37', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851398550289039360', '851397775177465856', 'LIFE_SUPPORT', '急救、生命支持类设备', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-19 17:53:53', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851398645160001536', '851397775177465856', 'NORMAL', '普通设备', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-19 17:54:15', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851721143927881728', '851720913702535168', 'WAIT', '未开始', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-20 15:15:45', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851721244314353664', '851720913702535168', 'DOING', '进行中', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-20 15:16:09', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851721330209505280', '851720913702535168', 'DONE', '已完成', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-20 15:16:29', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851721398325002240', '851720913702535168', 'OVERDUE', '超期未完成', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-20 15:16:46', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851721848474484736', '851721721269633024', 'NO', '未归档', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-20 15:18:33', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('851721915935670272', '851721721269633024', 'YES', '已归档', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-20 15:18:49', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852829659384168448', '852829139026231296', 'NORMAL', '正常使用', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-23 16:40:36', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852829751415586816', '852829139026231296', 'LACK', '可以使用，部分功能失效', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-23 16:40:58', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852829827470901248', '852829139026231296', 'FAULT', '无法使用，故障维修', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-23 16:41:16', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852829937885954048', '852829139026231296', 'DEATH', '无法维修，待报废', '', '', '4', 'AMS', 'admin', 'admin', '2024-12-23 16:41:42', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852831320756371456', '852830920460386304', 'CANCEL', '已作废', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-23 16:47:12', 'admin', 'admin', '2024-12-23 16:47:38', 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852831383616405504', '852830920460386304', 'NORMAL', '正常', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-23 16:47:27', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852832327146061824', '852831791122399232', 'WAIT', '未完成', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-23 16:51:12', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852832377909723136', '852831791122399232', 'COMPLETE', '已完成', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-23 16:51:24', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852835925775007744', '852832521472360448', 'IGNORE', '暂不处理', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-23 17:05:30', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852835999183716352', '852832521472360448', 'REPAIR', '转保修', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-23 17:05:47', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('852836079219425280', '852832521472360448', 'SCRAP', '转报废', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-23 17:06:06', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853519980602507264', '853517340871147520', 'NORMAL', '日常保养', '', '', '0', 'AMS', 'admin', 'admin', '2024-12-25 14:23:41', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853520090069647360', '853517340871147520', 'FIRST', '一级保养', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-25 14:24:07', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853520150572482560', '853517340871147520', 'SECOND', '二级保养', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-25 14:24:22', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853524555921276928', '853517340871147520', 'THIRD', '三级保养', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-25 14:41:52', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853578363061846016', '853577984811122688', 'INSPECTER', '巡检人员', '', '', '2', 'AMS', 'admin', 'admin', '2024-12-25 18:15:40', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853578432234307584', '853577984811122688', 'MAINTER', '保养人员', '', '', '1', 'AMS', 'admin', 'admin', '2024-12-25 18:15:57', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('853578677349433344', '853577984811122688', 'INVENTORIER', '盘点人员', '', '', '3', 'AMS', 'admin', 'admin', '2024-12-25 18:16:55', NULL, NULL, NULL, 'N', NULL, NULL, '20888576', '浏阳市卫健局', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871309032488808448', '871202836100526080', '0', '采购入库', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-02-12 16:31:01', 'admin', 'admin', '2025-02-13 17:02:42', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871334455767576576', '871202836100526080', '1', '退回入库', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-02-12 18:12:03', 'admin', 'admin', '2025-02-13 17:02:51', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871334592216674304', '871202836100526080', '2', '其他入库', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-02-12 18:12:35', 'admin', 'admin', '2025-02-13 17:02:57', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871334836606185472', '871202910847217664', 'LYCK', '领用出库', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-02-12 18:13:34', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871334977002123264', '871202910847217664', 'ZDCK', '自动出库', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-02-12 18:14:07', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871335064403030016', '871202910847217664', 'QTCK', '其他出库', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-02-12 18:14:28', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871945457219514368', '871945269721542656', 'DRK', '待入库', NULL, NULL, '0', 'AMS', 'admin', 'admin', '2025-02-14 10:39:57', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('871945525653778432', '871945269721542656', 'YRK', '已入库', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-02-14 10:40:13', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('873118809095254016', '873118507579322368', 'DCK', '待出库', NULL, NULL, '0', 'AMS', 'admin', 'admin', '2025-02-17 16:22:26', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('873118868864086016', '873118507579322368', 'YCK', '已出库', NULL, '已出库', '1', 'AMS', 'admin', 'admin', '2025-02-17 16:22:40', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875255623838867456', '875255468972580864', 'WPD', '未盘点', NULL, '未盘点', '1', 'AMS', 'admin', 'admin', '2025-02-23 13:53:22', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875255746622922752', '875255468972580864', 'PD', '盘到', NULL, '盘到', '2', 'AMS', 'admin', 'admin', '2025-02-23 13:53:51', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875255842903171072', '875255468972580864', 'PK', '盘亏', NULL, '盘亏', '3', 'AMS', 'admin', 'admin', '2025-02-23 13:54:14', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875256472526921728', '875256047547457536', 'WPD', ' ', NULL, '0 1 2 3 4  未盘点  正常使用  闲置  故障  损坏 ', '1', 'AMS', 'admin', 'admin', '2025-02-23 13:56:45', NULL, NULL, NULL, 'Y', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875256547151978496', '875256047547457536', 'ZCSY', '正常使用', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-02-23 13:57:02', 'admin', 'admin', '2025-02-25 11:31:03', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875256641750310912', '875256047547457536', 'XZ', '闲置', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-02-23 13:57:25', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875256822566756352', '875256047547457536', 'GZ', '故障', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-02-23 13:58:08', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875256890464149504', '875256047547457536', 'SH', '损坏', NULL, '损坏', '4', 'AMS', 'admin', 'admin', '2025-02-23 13:58:24', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875281492510777344', '875256047547457536', 'BX', '遗失', NULL, NULL, '5', 'AMS', 'admin', 'admin', '2025-02-23 15:36:10', 'admin', 'admin', '2025-02-26 15:04:24', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '5', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875631784402100224', '875630203963187200', 'WKS', '未开始', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-02-24 14:48:06', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875631933253754880', '875630203963187200', 'JXZ', '进行中', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-02-24 14:48:41', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('875632009279709184', '875630203963187200', 'YJS', '已结束', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-02-24 14:48:59', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('878834937725460480', '878834828006662144', 'YLSB', '医疗设备', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-03-05 10:56:17', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('878835066595450880', '878834828006662144', 'YBZC', '一般资产', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-03-05 10:56:48', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897424565428592640', '878834828006662144', 'SD', '水电', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-04-25 18:04:50', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('878837896018403328', '878836527203098624', 'ZK', '在库', NULL, ' 0 在库 1 在役  2 减少 （报废）', '1', 'AMS', 'admin', 'admin', '2025-03-05 11:08:02', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('878838143943712768', '878836527203098624', 'ZY', '在役', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-03-05 11:09:02', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('878838299019714560', '878836527203098624', 'JS', '减少', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-03-05 11:09:39', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('879208617253019648', '878836527203098624', 'RKZ', '入库中', NULL, '退回入库', '3', 'AMS', 'admin', 'admin', '2025-03-06 11:41:09', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('879208697322283008', '878836527203098624', 'CKZ', '出库中', NULL, NULL, '4', 'AMS', 'admin', 'admin', '2025-03-06 11:41:28', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('895937415654678528', '878836527203098624', 'ZYZ', '转移中', NULL, NULL, '5', 'AMS', 'admin', 'admin', '2025-04-21 15:35:26', 'admin', 'admin', '2025-04-21 15:36:08', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '5', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('895937566611873792', '878836527203098624', 'CZZ', '处置中', NULL, NULL, '6', 'AMS', 'admin', 'admin', '2025-04-21 15:36:02', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '6', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('888669364936876032', '888669091338231808', 'JY', '金阳', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-04-01 14:14:47', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '金阳', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('888669459577151488', '888669091338231808', 'YA', '永安', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-04-01 14:15:10', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '永安', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('894788658124824576', '894788563857842176', '0', '待审核', NULL, NULL, '0', 'AMS', 'admin', 'admin', '2025-04-18 11:30:40', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('894788707907018752', '894788563857842176', '1', '已通过', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-04-18 11:30:52', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('894788749644537856', '894788563857842176', '2', '未通过', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-04-18 11:31:02', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('896958212619935744', '896956816147390464', 'ZCSB_ADMIN', '资产设备管理员', NULL, '所有数据', '1', 'AMS', 'admin', 'admin', '2025-04-24 11:11:43', 'admin', 'admin', '2025-04-24 11:12:06', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ALL', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897017386439581696', '896956816147390464', 'ZCSB_CKSDY', '资产设备仓库审单员', NULL, '用于对出库单入库单进行确认的角色', '2', 'AMS', 'admin', 'admin', '2025-04-24 15:06:51', 'admin', 'admin', '2025-04-24 15:15:40', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0,1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897017962795671552', '896956816147390464', 'ZCSB_YBZCCZ_GLY', '资产设备一般资产处置管理员', NULL, '用于对临床科室申请的一般资产处置申请进行审核', '3', 'AMS', '001688', '陈彬', '2025-04-24 15:09:08', 'admin', 'admin', '2025-04-30 09:29:07', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1,2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897018050880249856', '896956816147390464', 'ZCSB_YBZCPD_GLY', '资产设备一般资产盘点管理员', NULL, '用户管理和计划一般资产的盘点', '4', 'AMS', 'admin', 'admin', '2025-04-24 15:09:29', 'admin', 'admin', '2025-04-24 15:11:39', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897018249715425280', '896956816147390464', 'ZCSB_YBZCZY_GLY', '资产设备一般资产转移管理员', NULL, '用于审核一般资产转科的单子', '5', 'AMS', 'admin', 'admin', '2025-04-24 15:10:17', 'admin', 'admin', '2025-04-30 09:37:57', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1,2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897018812293226496', '896956816147390464', 'ZCSB_YLSBCZ_GLY', '资产设备医疗设备处置管理员', NULL, '用于对临床科室申请的医疗设备处置申请进行审核', '6', 'AMS', 'admin', 'admin', '2025-04-24 15:12:31', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897018931428237312', '896956816147390464', 'ZCSB_YLSBPD_GLY', '资产设备医疗设备盘点管理员', NULL, '用户管理和计划医疗资产的盘点', '7', 'AMS', 'admin', 'admin', '2025-04-24 15:12:59', 'admin', 'admin', '2025-04-24 15:13:52', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897019044208877568', '896956816147390464', 'ZCSB_YLSBZY_GLY', '资产设备医疗设备转移管理员', NULL, '用于对临床科室申请的医疗设备转移申请进行审核', '8', 'AMS', 'admin', 'admin', '2025-04-24 15:13:26', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('918742996710924288', '896956816147390464', 'ZCSB_YLSBCGDD_GLY', '资产设备医疗设备采购订单管理员', NULL, '用于对医疗设备采购申请进行订单处理与实际采购', '9', 'AMS', 'admin', 'admin', '2025-06-23 13:56:40', 'admin', 'admin', '2025-06-23 13:57:53', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('918743115258732544', '896956816147390464', 'ZCSB_YBZCCGDD_GLY', '资产设备一般资产采购订单管理员', NULL, '用于对一般采购申请进行订单处理与实际采购', '10', 'AMS', 'admin', 'admin', '2025-06-23 13:57:08', 'admin', 'admin', '2025-06-23 13:59:55', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1,2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897020978634465280', '896957297817067520', '审核出库单', '审核出库单', NULL, '用于出库单的审核与出库单列表数据范围限制', '1', 'AMS', 'admin', 'admin', '2025-04-24 15:21:07', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_CKSDY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897021134314446848', '896957297817067520', '审核入库单', '审核入库单', NULL, '用于入库单的审核与入库单列表数据范围限制', '2', 'AMS', 'admin', 'admin', '2025-04-24 15:21:44', 'admin', 'admin', '2025-04-24 15:22:07', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_CKSDY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('897023733344280576', '896957297817067520', '审核资产转移单', '审核资产转移单', NULL, '用于资产转移审核权限和资产转移列表数据权限', '3', 'AMS', 'admin', 'admin', '2025-04-24 15:32:04', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_YBZCZY_GLY,ZCSB_YLSBZY_GLY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898736664310226944', '896957297817067520', '审核资产处置单', '审核资产处置单', NULL, '用于资产处置审核权限和资产处置列表数据权限', '4', 'AMS', 'admin', 'admin', '2025-04-29 08:58:38', 'admin', 'admin', '2025-04-29 08:59:08', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_YLSBCZ_GLY,ZCSB_YBZCCZ_GLY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('902364174361075712', '896957297817067520', '台账管理', '台账管理', NULL, '目前资产审单员可以给台账权限', '5', 'AMS', 'admin', 'admin', '2025-05-09 09:13:04', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_CKSDY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('902378432046080000', '896957297817067520', '资产盘点', '资产盘点', NULL, '用于资产列表的数据权限', '6', 'AMS', 'admin', 'admin', '2025-05-09 10:09:44', 'admin', 'admin', '2025-05-09 10:10:28', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_YBZCPD_GLY,ZCSB_YLSBPD_GLY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('910068382766014464', '896957297817067520', '采购申请', '采购申请', NULL, NULL, '7', 'AMS', 'admin', 'admin', '2025-05-30 15:26:51', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_ADMIN', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('918747433659604992', '896957297817067520', '采购订单', '采购订单', NULL, '采购订单创建，到货登记', '8', 'AMS', 'admin', 'admin', '2025-06-23 14:14:18', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_YLSBCGDD_GLY,ZCSB_YBZCCGDD_GLY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('925215494398889984', '896957297817067520', '安装验收', '安装验收', NULL, '用于资产安装验收', '9', 'AMS', 'admin', 'admin', '2025-07-11 10:36:04', 'admin', 'admin', '2025-07-11 10:38:47', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'ZCSB_AZYS_GLY', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898467477226561536', '898467406305075200', '1', '报废', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-04-28 15:08:59', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898467530326450176', '898467406305075200', '2', '损坏', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-04-28 15:09:12', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898467572219158528', '898467406305075200', '3', '盘亏', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-04-28 15:09:22', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898467873609261056', '898467406305075200', '4', '捐赠（出）', NULL, NULL, '4', 'AMS', 'admin', 'admin', '2025-04-28 15:10:34', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('898467948997681152', '898467406305075200', '5', '其他', NULL, NULL, '5', 'AMS', 'admin', 'admin', '2025-04-28 15:10:52', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '5', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908990224914522112', '908901915500552192', '7BD713E02894494A8068AC8A01E6DA4E', '一般设备采购申请', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-05-27 16:02:38', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '7BD713E02894494A8068AC8A01E6DA4E', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908990317021437952', '908901915500552192', 'A351A21E775A4A27B77FEB212DE09A63', '医疗专用设备采购申请', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-05-27 16:03:00', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', 'A351A21E775A4A27B77FEB212DE09A63', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908962084590788608', '908960578722406400', '10000', '已完成', NULL, '状态：草稿 0 在办 1 办结 2 强制结束 3  撤销 4 终止 5 销毁 6
已完结：2 3 4 5 6  ', '1', 'AMS', 'admin', 'admin', '2025-05-27 14:10:49', 'admin', 'admin', '2025-05-27 14:12:03', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10000', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908962333963132928', '908960578722406400', '10001', '退回', NULL, '流程状态是在办，当前节点是我', '2', 'AMS', 'admin', 'admin', '2025-05-27 14:11:48', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10001', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908962680098070528', '908960578722406400', '10002', '审批中', NULL, '在办的，当前节点不是我', '3', 'AMS', 'admin', 'admin', '2025-05-27 14:13:11', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10002', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908962848180609024', '908960578722406400', '10003', '草稿箱', NULL, '状态等于0', '4', 'AMS', 'admin', 'admin', '2025-05-27 14:13:51', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10003', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908963135679176704', '908960578722406400', '10004', '待我审批', NULL, '所有task 当前指向我的', '4', 'AMS', 'admin', 'admin', '2025-05-27 14:14:59', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10004', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908963324720652288', '908960578722406400', '10005', '我已审批', NULL, '所有task_his 我相关的', '6', 'AMS', 'admin', 'admin', '2025-05-27 14:15:44', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10005', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908963473912045568', '908960578722406400', '10006', '抄送给我', NULL, '所有copy 与我的', '7', 'AMS', 'admin', 'admin', '2025-05-27 14:16:20', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10006', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908964717581901824', '908960578722406400', '10007', '办理查阅在办', NULL, NULL, '8', 'AMS', 'admin', 'admin', '2025-05-27 14:21:16', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10007', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('908964942878941184', '908960578722406400', '10008', '办理查阅-已完结', NULL, NULL, '9', 'AMS', 'admin', 'admin', '2025-05-27 14:22:10', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10008', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('913994572954411008', '913994477005512704', '10008', '采购申请', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-06-10 11:28:07', 'admin', 'admin', '2025-06-10 14:27:09', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10008', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('913994709684527104', '913994477005512704', '10001', '进行中订单', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-06-10 11:28:40', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10001', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('913994805251743744', '913994477005512704', '10002', '已完成订单', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-06-10 11:29:03', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '10002', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('914043274754596864', '914043199441674240', '1', '进行中', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-06-10 14:41:39', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('914043331893600256', '914043199441674240', '2', '已完成', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-06-10 14:41:52', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('916525915743526912', '916513213289668608', 'A351A21E775A4A27B77FEB212DE09A63', '医疗专用设备采购申请设备明细', NULL, '设置字典来约定每个流程子表单创建订单时候的约定
code : 流程定义ID
value: skuType,是否绑定字典,数量字段名,名称的字段名｜N个自定义字段名', '1', 'AMS', 'admin', 'admin', '2025-06-17 11:06:47', 'admin', 'admin', '2025-06-21 11:27:54', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0,1,sl,sbmc|sbmc,ggxh,sl,dj,zje', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('916532140224880640', '916513213289668608', '7BD713E02894494A8068AC8A01E6DA4E', '一般设备采购申请', NULL, '设置字典来约定每个流程子表单创建订单时候的约定
code : 流程定义ID
value: skuType,是否绑定字典,数量字段名,名称的字段名｜N个自定义字段名', '2', 'AMS', 'admin', 'admin', '2025-06-17 11:31:31', 'admin', 'admin', '2025-07-07 09:35:14', 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0,1,sl,sbmc|sbmc,ggxh,sl,zje', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('916972866708512768', '916972802170757120', '1', '公开招标', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-06-18 16:42:48', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('916972906546012160', '916972802170757120', '2', '邀请招标', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-06-18 16:42:57', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('916972947792797696', '916972802170757120', '3', '竞争性谈判', NULL, NULL, '3', 'AMS', 'admin', 'admin', '2025-06-18 16:43:07', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('916973002943700992', '916972802170757120', '4', '单一来源采购', NULL, NULL, '4', 'AMS', 'admin', 'admin', '2025-06-18 16:43:20', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('917261429257871360', '917261360878133248', '1', '自有资金', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-06-19 11:49:27', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('917261471377072128', '917261360878133248', '2', '政府拨款', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-06-19 11:49:37', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('919050258721611776', '919050156808413184', '0', '未到货', NULL, NULL, '0', 'AMS', 'admin', 'admin', '2025-06-24 10:17:37', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('919050364850085888', '919050156808413184', '1', '部分到货', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-06-24 10:18:02', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('919050429811466240', '919050156808413184', '2', '全部到货', NULL, NULL, '2', 'AMS', 'admin', 'admin', '2025-06-24 10:18:18', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('924904080681553920', '924904004974366720', '0', '待确认', NULL, NULL, '0', 'AMS', 'admin', 'admin', '2025-07-10 13:58:37', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('924904121395662848', '924904004974366720', '1', '已确认', NULL, NULL, '1', 'AMS', 'admin', 'admin', '2025-07-10 13:58:46', NULL, NULL, NULL, 'N', NULL, NULL, '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, NULL) ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
