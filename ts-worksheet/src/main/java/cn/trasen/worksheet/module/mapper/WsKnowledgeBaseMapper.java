package cn.trasen.worksheet.module.mapper;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseMobileInputVo;
import cn.trasen.worksheet.module.dto.inputVo.KnowledgeBaseQueryInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBaseMobileInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeBasePageOutVo;
import cn.trasen.worksheet.module.dto.outVo.KnowledgeTypeTreeOutVo;
import cn.trasen.worksheet.module.entity.WsKnowledgeBase;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface WsKnowledgeBaseMapper extends Mapper<WsKnowledgeBase> {

    int insertknowledgeBase(WsKnowledgeBase wsKnowledgeBase);

    int updateknowledgeBase(WsKnowledgeBase wsKnowledgeBase);

    WsKnowledgeBase selectOneById(@Param("pkKnowledgeBaseId") String pkKnowledgeBaseId,
                                  @Param("userId") String userId);

    WsKnowledgeBase selectOneKnowledgeBase(WsKnowledgeBase wsKnowledgeBase);

    List<WsKnowledgeBase> selectListByUserId(String fkUserId);

    List<KnowledgeBasePageOutVo> selectPageList(Page page, KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo);

    /**
     * 申请人首页-我的知识点
     *
     * @param fkUserId
     * @return
     */
    Map<String, Object> getMyKnowledgeBaseCount(String fkUserId);

    /**
     * 知识点提交趋势
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getKnowledgeBaseCountByDate(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 一级知识点占比
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getLevelOneKnowledgeBaseTypeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    /**
     * 知识点提交top榜单
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    List<Map<String, Object>> getKnowledgeBaseSubmitTopDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo);

    List<KnowledgeTypeTreeOutVo> selectKnowledgeAllList(
            @Param("knowledgeStatus") int knowledgeStatus,
            @Param("categoryName") String categoryName,
            @Param("beginTime") String beginTime,
            @Param("endTime") String endTime);


    /**
     * 查询知识库菜单，各页签数据
     *
     * @param knowledgeBaseQueryInputVo
     * @return
     */
    Map<String, Object> selectCountsGroupStatus(KnowledgeBaseQueryInputVo knowledgeBaseQueryInputVo);

    /**
     * 知识点点赞排行（点赞数排序，相同时取贡献时间晚的）
     *
     * @param knowledgeBaseMobileInputVo
     * @return
     */
    List<KnowledgeBaseMobileInfoOutVo> knowledgeLikeRank(KnowledgeBaseMobileInputVo knowledgeBaseMobileInputVo);


}