package cn.trasen.worksheet.module.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.utils.DateUtil;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.DictCodeEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.PropertyNameEnum;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.enums.WorkSheetTaskEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.DictUtils;
import cn.trasen.worksheet.common.util.FeignInfoUitls;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.LyszyyyMessageUtil;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.OtherUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsFileInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetAssistInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsTaskInputVo;
import cn.trasen.worksheet.module.dto.outVo.WsTaskWorkHoursListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsOpenTaskInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsTaskInfoOutVo;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.entity.WsHospitalDistrict;
import cn.trasen.worksheet.module.entity.WsOmMeau;
import cn.trasen.worksheet.module.entity.WsWsBack;
import cn.trasen.worksheet.module.entity.WsWsMessage;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.entity.WsWsTask;
import cn.trasen.worksheet.module.mapper.WsWsTaskMapper;
import cn.trasen.worksheet.module.service.WsExternalPersonnelService;
import cn.trasen.worksheet.module.service.WsFaultTypeService;
import cn.trasen.worksheet.module.service.WsFileService;
import cn.trasen.worksheet.module.service.WsHospitalDistrictService;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsSheetTaskService;
import cn.trasen.worksheet.module.service.WsWsBackService;
import cn.trasen.worksheet.module.service.WsWsMessageService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/6/17 14:43
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Slf4j
@Service
public class WsSheetTaskServiceImpl implements WsSheetTaskService {

    @Autowired
    private WsWsTaskMapper wsTaskMapper;

    @Autowired
    private WsSheetService wsSheetService;

    @Autowired
    private WsFaultTypeService wsFaultTypeService;

    @Autowired
    private WsWsBackService wsWsBackService;

    @Autowired
    private WsWsMessageService wsWsMessageService;

    @Autowired
    private WsExternalPersonnelService wsExternalPersonnelService;

    @Autowired
    private InformationFeignService informationFeignService;

    @Autowired
    private WsOmMeauService wsOmMeauService;

    @Autowired
    private WsFileService wsFileService;

    @Autowired
    private WsHospitalDistrictService wsHospitalDistrictService;
    
    @Autowired
    private GlobalSettingsFeignService globalSettingsFeignService;

    @Value("${externalPersonnel.ORG_ID}")
    private String defaultOrgId;

    @Value("${oaUrl.workInfoUrl}")
    private String workInfoUrl;


    @Override
    public int insert(WsWsTask wsTask) {
        wsTask.setPkWsTaskId(IdUtils.getId());
        return wsTaskMapper.insert(wsTask);
    }


    @Override
    public void insertBatch(List<WsWsTask> list) {
        wsTaskMapper.insertBatch(list);
    }

    @Transactional
    @Override
    public int insertWsTask(WsWsTask wsTask) {
        return wsTaskMapper.insertWsTask(wsTask);
    }


    @Override
    public WsWsTask selectOneById(WsWsTask wsTask) {
        return wsTaskMapper.selectOne(wsTask);
    }

    @Override
    public WsWsTask selectOneWsTaskById(String id) {
        return wsTaskMapper.selectOneWsTaskById(id);
    }

    @Override
    public void updateWsTaskById(WsWsTask wsTask) {
        wsTaskMapper.updateWsTaskById(wsTask);
    }

    @Override
    public void updateBatch(List<WsWsTask> wsTask) {
        wsTaskMapper.updateBatch(wsTask);
    }

    @Override
    public WsWsTask selectOneWsTaskByuser(WsWsTask wsTask) {
        return wsTaskMapper.selectOneWsTaskByuser(wsTask);
    }


    @Override
    public void update(WsWsTask wsTask) {
        wsTaskMapper.updateByPrimaryKey(wsTask);
    }

    /**
     * 添加协助节点操作
     *
     * @return
     */
    @Override
    public PlatformResult addAssistTask(WsWsSheetAssistInputVo wsSheetAssistInputVo) {
        WsWsSheet wsWsSheet = Optional.ofNullable(wsSheetService.selectOneWsSheet(wsSheetAssistInputVo.getWorkNumber()))
                .orElseThrow(() -> new BusinessException("未查询到工单数据"));
        // 协助添加限制
        if (wsSheetAssistInputVo.getFkUserIds().contains(wsWsSheet.getFkUserId())) {
            throw new BusinessException(wsWsSheet.getFkUserName() + "无法即做处理人又做协助人");
        }
        List<WsWsTask> wsTaskList = wsTaskMapper.selectAssisListByWorkNumber(wsSheetAssistInputVo.getWorkNumber());
        // 协助添加限制
        if (!CollectionUtil.isEmpty(wsTaskList)) {
            List<WsWsTask> collect = wsTaskList.stream().filter(temp -> wsSheetAssistInputVo.getFkUserIds().contains(temp.getFkUserId())).collect(Collectors.toList());
            if (!CollectionUtil.isEmpty(collect)) {
                throw new BusinessException(collect.stream().map(WsWsTask::getFkUserName).collect(Collectors.joining(",")) + "已为在办协助人，请勿重复添加");
            }
        }
        String[] fkUserId = wsSheetAssistInputVo.getFkUserIds().split(",");
        String[] remark = wsSheetAssistInputVo.getRemarks().split(",");
        if (fkUserId.length != remark.length) {
            PlatformResult.failure("协助人ID、协助备注数据需等长");
        }
        return assistTaskInsertBatch(wsSheetAssistInputVo.getWorkNumber(), fkUserId, remark);
    }

    /**
     * 批量添加协助记录
     *
     * @param workNumber 工单编号
     * @param remarks    协助备注
     */
    public PlatformResult assistTaskInsertBatch(String workNumber, String[] fkUserIds, String[] remarks) {
        List<WsWsTask> wsTaskLists = Lists.newArrayList();
        // 所有OA人员信息
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(Arrays.asList(fkUserIds));
        for (int i = 0; i < fkUserIds.length; i++) {
            WsWsTask wsTask = new WsWsTask();
            wsTask.setPkWsTaskId(IdUtils.getId());
            wsTask.setWorkNumber(workNumber);
            wsTask.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
            wsTask.setTaskName(WorkSheetTaskEnum.ASSIST.getValue());
            wsTask.setTakeRemark(remarks[i]);
            wsTask.setComplete(CommonlyConstants.YesOrNo.NO);
            wsTask.setAssist(CommonlyConstants.YesOrNo.YES);
            wsTask.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
            wsTask.setFkUserId(fkUserIds[i]);
            wsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());

            int j = i;
            String fkUserName = userListByIds
                    .stream()
                    .filter(employeeResp -> employeeResp.getEmployeeId().equals(fkUserIds[j]))
                    .map(EmployeeResp::getEmployeeName).collect(Collectors.joining());
            wsTask.setFkUserName(fkUserName);
            wsTask.setTakeRemark("申请" + fkUserName + "协助。" + remarks[i]);
            wsTaskLists.add(wsTask);
        }
        // SSO外部人员信息
        List<EmployeeResp> externalPersonnelList = userListByIds
                .stream()
                .filter(temp -> defaultOrgId.equals(temp.getOrgId()))
                .collect(Collectors.toList());
        // 仅剩OA人员信息
        userListByIds.removeAll(externalPersonnelList);
        // 工单外部人员信息
        List<WsExternalPersonnel> wsExternalPersonnels = wsExternalPersonnelService.selectOneByIds(
                externalPersonnelList
                        .stream()
                        .map(EmployeeResp::getEmployeeId)
                        .collect(Collectors.toList())
        );
        // 补充人员部门信息
        userListByIds.forEach(user -> {
            wsTaskLists.forEach(task -> {
                if (user.getEmployeeId().equals(task.getFkUserId())) {
                    task.setFkUserDeptId(user.getOrgId());
                    task.setFkUserDeptName(user.getOrgName());
                }
            });
        });
        // 补充人员部门信息
        wsExternalPersonnels.forEach(externalPersonnels -> {
            wsTaskLists.forEach(task -> {
                if (externalPersonnels.getPkExternalPersonnelId().equals(task.getFkUserId())) {
                    task.setFkUserDeptId(externalPersonnels.getInstitutionalAffiliations());
                    task.setFkUserDeptName(externalPersonnels.getInstitutionalAffiliations());
                }
            });
        });
        try {
            wsTaskMapper.insertBatch(wsTaskLists);
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("错误信息：" + e.getMessage());
        }
        // 消息推送
        assembledMessages(workNumber, WorkSheetTaskEnum.ASSIST.getValue(), null);
        return PlatformResult.success();
    }

    /**
     * 检查工单二次保存，是否发生数据改变
     *
     * @param wsSheetInputVo 传输DTO
     */
    @Override
    public WsWsSheet checkworkSheetChange(WsWsSheetInputVo wsSheetInputVo, WsWsTask wsTask) {
        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(wsSheetInputVo.getWorkNumber());
        StringBuilder message = new StringBuilder("");
        if (!(wsSheetInputVo.getFkHospitalDistrictId() + "").equals(wsSheet.getFkHospitalDistrictId())) {
            WsHospitalDistrict input = wsHospitalDistrictService.getOne(wsSheetInputVo.getFkHospitalDistrictId());
            WsHospitalDistrict hospitalDistrict = wsHospitalDistrictService.getOne(wsSheet.getFkHospitalDistrictId());
            if(null != input && !StringUtil.isEmpty(input.getHospitalDistrictName())){
                message.append(
                        (StringUtil.isEmpty(wsSheet.getRepairDeptAddress()) ? "报修院区" : "报修院区由\"" + hospitalDistrict.getHospitalDistrictName() + "\"") +
                                "修改为\"" + input.getHospitalDistrictName() + "\"" + CommonlyConstants.CuttOperator.CUT);
            }
        }
        if (!("" + wsSheetInputVo.getRepairDeptAddress()).equals(wsSheet.getRepairDeptAddress() + "")) {
            message.append(
                    (StringUtil.isEmpty(wsSheet.getRepairDeptAddress()) ? "报修地址" : "报修地址由\"" + wsSheet.getRepairDeptAddress() + "\"") +
                            "修改为\"" + wsSheetInputVo.getRepairDeptAddress() + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (!(wsSheetInputVo.getFkFaultTypeId()+"").equals(wsSheet.getFkFaultTypeId())) {
            String inputVOFkFaultTypeName = "";
            String wsSheetFkFaultTypeName = "";
            if (!StringUtils.isEmpty(wsSheetInputVo.getFkFaultTypeId())) {
                inputVOFkFaultTypeName = Optional.ofNullable(wsFaultTypeService.selectOne(wsSheetInputVo.getFkFaultTypeId()))
                        .map(temp -> temp.getFullPath())
                        .orElseThrow(() -> new BusinessException("故障类型id未查询到数据"));
            }
            if (!StringUtils.isEmpty(wsSheet.getFkFaultTypeId())) {
                wsSheetFkFaultTypeName = Optional.ofNullable(wsFaultTypeService.selectOne(wsSheet.getFkFaultTypeId()))
                        .map(temp -> temp.getFullPath())
                        .orElseThrow(() -> new BusinessException("故障类型id未查询到数据"));
            }
            message.append("故障类型由\"" + wsSheetFkFaultTypeName + "\"修改为\"" + inputVOFkFaultTypeName + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (!(wsSheetInputVo.getFaultEquipmentName() + "").equals(wsSheet.getFaultEquipmentName() + "")) {
            message.append(
                    (StringUtil.isEmpty(wsSheet.getFaultEquipmentName()) ? "故障设备由" : "故障设备由\"" + wsSheet.getFaultEquipmentName() + "\"") +
                            "修改为\"" + wsSheetInputVo.getFaultEquipmentName() + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (!wsSheetInputVo.getFaultDeion().equals(wsSheet.getFaultDeion())) {
            message.append("故障描述由\"" + wsSheet.getFaultDeion() + "\"修改为\"" + wsSheetInputVo.getFaultDeion() + "\"" + CommonlyConstants.CuttOperator.CUT);
        }

        if (wsSheetInputVo.getRepairType() != wsSheet.getRepairType()) {
            message.append("报修方式由\"" + DictUtils.getDictItemRespItemName(DictCodeEnum.REPAIR_TYPE.getValue(), wsSheet.getRepairType() + "")
                    + "\"修改为\"" + DictUtils.getDictItemRespItemName(DictCodeEnum.REPAIR_TYPE.getValue(), wsSheetInputVo.getRepairType() + "")
                    + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (wsSheetInputVo.getFaultEmergency() != wsSheet.getFaultEmergency()) {
            message.append("故障紧急程度由\"" + DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_EMERGENCY.getValue(), wsSheet.getFaultEmergency() + "")
                    + "\"修改为\"" + DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_EMERGENCY.getValue(), wsSheetInputVo.getFaultEmergency() + "")
                    + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (wsSheetInputVo.getFaultAffectScope() != wsSheet.getFaultAffectScope()) {
            message.append("故障影响范围由\"" + DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_AFFECT_SCOPE.getValue(), wsSheet.getFaultAffectScope() + "")
                    + "\"修改为\"" + DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_AFFECT_SCOPE.getValue(), wsSheetInputVo.getFaultAffectScope() + "")
                    + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (!(wsSheetInputVo.getRequiredCompletionTime() + "").equals(wsSheet.getRequiredCompletionTime() + "")) {
            message.append(
                    Optional.ofNullable(wsSheet.getRequiredCompletionTime())
                            .map(temp -> "故障完成时间由\"" + DateUtil.format(wsSheet.getRequiredCompletionTime(), "yyyy-MM-dd") + "\"")
                            .orElse("故障完成时间")
                            + "修改为\"" + DateUtil.format(wsSheetInputVo.getRequiredCompletionTime(), "yyyy-MM-dd") + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (!(wsSheetInputVo.getRemark() + "").equals(wsSheet.getRemark())) {
            message.append(
                    (StringUtil.isEmpty(wsSheet.getRemark()) ? "备注" : "备注由\"" + wsSheet.getRemark() + "\"") +
                            "修改为\"" + wsSheetInputVo.getRemark() + "\"" + CommonlyConstants.CuttOperator.CUT);
        }
        if (!StringUtil.isEmpty(wsSheetInputVo.getFkUserId()) && !(wsSheetInputVo.getFkUserId() + "").equals(wsSheet.getFkUserId())) {
            if (StringUtils.isEmpty(wsSheet.getFkUserId())) {
                message.append("处理人为\"" + FeignInfoUitls.getUserNameById(wsSheetInputVo.getFkUserId()).getEmployeeName() + "\"" + CommonlyConstants.CuttOperator.CUT);
            } else {
                message.append("处理人由\"" + wsSheet.getFkUserName() + "\"修改为\"" + FeignInfoUitls.getUserNameById(wsSheetInputVo.getFkUserId()).getEmployeeName() + "\"" + CommonlyConstants.CuttOperator.CUT);
            }
        }
//        if(!StringUtils.isEmpty(message)){
//            completeTaskAndAddTask(wsSheet.getPkWsSheetId(),wrokStatus
//                    ,WorkSheetStatusEnum.EDITOR.getValue(),message + "",
//                    StringUtils.isEmpty(wsSheet.getFkUserId()) ? null : wsSheet.getFkUserId(),
//                    StringUtils.isEmpty(wsSheet.getFkUserName()) ? null : wsSheet.getFkUserName(),
//                    wsSheetInputVo.getProcessingDeptId(), wsSheetInputVo.getProcessingDeptName(),
//                    0,null, null );
//
//        }
        BeanUtils.copyProperties(wsSheetInputVo, wsSheet);
        if (StringUtils.isEmpty(wsSheetInputVo.getFkUserId())) {
            wsSheet.setFkUserName(null);
            wsSheet.setFkUserDeptName(null);
        }
        wsTask.setTakeRemark(StringUtil.isEmpty(message + "") ? "无操作 " : (message + ""));
        return wsSheet;
    }

    /**
     * 工单业务当前节点已完成，开启下个节点
     *
     * @param wsWsTaskInputVo
     */
    @Transactional
    @Override
    public WsWsSheet completeTaskAndAddTask(WsWsTaskInputVo wsWsTaskInputVo) {
        WsWsTask wsTaskTemp = Optional.ofNullable(wsTaskMapper.selectOneWsTaskById(wsWsTaskInputVo.getPkWsTaskId()))
                .map(temp -> temp.get())
                .orElseThrow(new BusinessException("未查询到数据"));
        if (IndexEnum.ZERO.getValue() == wsTaskTemp.getAssist() && IndexEnum.ONE.getValue() == wsTaskTemp.getComplete()) {
            throw new BusinessException("当前步骤已有其他人操作过，请刷新当前页面");
        }
        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(wsTaskTemp.getWorkNumber());
        // 初始化冗余字段，id转名称
        if(null != wsWsTaskInputVo && !StringUtil.isEmpty(wsWsTaskInputVo.getFkUserId())){
        Optional.ofNullable(wsWsTaskInputVo.getFkUserId())
                .map(temp -> {
                    FeignInfoUitls.fillNameById(
                            wsSheet,
                            Arrays.asList(PropertyNameEnum.FK_USER_ID.getName()),
                            Arrays.asList(wsWsTaskInputVo.getFkUserId())
                    );
                    // 修复外部机构人员所属机构问题
                    if (wsSheet.getFkUserDeptId().equals(defaultOrgId)) {
                        WsExternalPersonnel wsExternalPersonnel = wsExternalPersonnelService.selectOneById(wsSheet.getFkUserId());
                        wsSheet.setFkUserDeptId(wsExternalPersonnel.getInstitutionalAffiliations());
                        wsSheet.setFkUserDeptName(wsExternalPersonnel.getInstitutionalAffiliations());
                    }
                    return null;
                });
        	}
        // 转发、派单，业务所属科室、故障类型调整
        if (WorkSheetTaskEnum.DISPATCH.getValue().equals(wsWsTaskInputVo.getTaskName()) || WorkSheetTaskEnum.RESEND.getValue().equals(wsWsTaskInputVo.getTaskName()) || WorkSheetTaskEnum.TO_CLAIM.getValue().equals(wsWsTaskInputVo.getTaskName()) ) {
            wsSheet.setBusinessDeptId(wsWsTaskInputVo.getBusinessDeptId());
            wsSheet.setFkFaultTypeId(wsWsTaskInputVo.getFkFaultTypeId());
        }
        // 更新工单业务主表,协助人操作不予更新
        if (!WorkSheetTaskEnum.UPDATE_PROCESS.getValue().equals(wsWsTaskInputVo.getTaskName())) {
            wsSheetService.workSheetComplete(
                    wsSheet,
                    wsWsTaskInputVo.getWorkStatus(),
                    WorkSheetStatusEnum.EVALUATE.getValue().equals(wsWsTaskInputVo.getWorkStatus()) ? new Date() : null,
                    wsWsTaskInputVo.getWorkHours()
            );
        } else if (!WorkSheetTaskEnum.UPDATE_PROCESS.getValue().equals(wsWsTaskInputVo.getTaskName())
                && null != wsWsTaskInputVo.getWorkHours()) {
            // 协助更新操作，更新工时
            WsWsSheet wsSheetTemp = wsSheetService.selectOneWsSheet(wsTaskTemp.getWorkNumber());
            wsSheetTemp.setWorkHours(wsWsTaskInputVo.getWorkHours() + wsSheetTemp.getWorkHours());
            wsSheetService.updateWsSheetByWorkNumber(wsSheetTemp);
        }
        // 工单业务主处理人人节点数据，工时特殊处理
        if (WorkSheetStatusEnum.PROCESSING.getValue().equals(wsTaskTemp.getWorkStatus()) && CommonlyConstants.YesOrNo.NO == wsTaskTemp.getAssist()) {
            wsTaskTemp.setWorkHours(wsWsTaskInputVo.getWorkHours());
            wsWsTaskInputVo.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
        }
        // 当前节点步骤更新为已完成
        workSheetTaskComplete(wsTaskTemp, null);

        // 创建新步骤
        // 初始化数据
        WsWsTask wsTask = new WsWsTask();
        if(null != wsWsTaskInputVo && !StringUtil.isEmpty(wsWsTaskInputVo.getFkUserId())){
        Optional.ofNullable(wsWsTaskInputVo.getFkUserId())
                .map(temp -> {
                    FeignInfoUitls.fillNameById(
                            wsTask,
                            Arrays.asList(PropertyNameEnum.FK_USER_ID.getName(), PropertyNameEnum.FK_FORMER_USER_ID.getName()),
                            Arrays.asList(wsWsTaskInputVo.getFkUserId(), wsWsTaskInputVo.getFkFormerUserId())
                    );
                    // 修复外部机构人员所属机构问题
                    if (wsTask.getFkUserDeptId().equals(defaultOrgId)) {
                        WsExternalPersonnel wsExternalPersonnel = wsExternalPersonnelService.selectOneById(wsTask.getFkUserId());
                        wsTask.setFkUserDeptId(wsExternalPersonnel.getInstitutionalAffiliations());
                        wsTask.setFkUserDeptName(wsExternalPersonnel.getInstitutionalAffiliations());
                    }
                    return null;
                });
        }
        // 拼接备注
        String taskRemark = "";
        if (WorkSheetTaskEnum.DISPATCH.getValue().equals(wsWsTaskInputVo.getTaskName()) || WorkSheetTaskEnum.RESEND.getValue().equals(wsWsTaskInputVo.getTaskName())) {
            WsOmMeau wsOmMeau = new WsOmMeau();
            wsOmMeau.setDeptId(wsWsTaskInputVo.getBusinessDeptId());
            // 分派、重派拼接
            taskRemark =
                    wsWsTaskInputVo.getIndex() ?
                            ("工单流转至" + wsOmMeauService.seleteOneOmMeau(wsOmMeau).getDeptName())
                            : ("处理人：" + wsTask.getFkUserName() + "-" + wsTask.getFkUserDeptName())
                            + CommonlyConstants.CuttOperator.CUT +
                            (null == wsSheet.getRequiredCompletionTime() ? "" : "要求时间：" + DateUtil.format(wsSheet.getRequiredCompletionTime(), "yyyy-MM-dd") + CommonlyConstants.CuttOperator.CUT) +
                            wsWsTaskInputVo.getRemark();
        } else {
            taskRemark = wsWsTaskInputVo.getRemark();
        }
        // 延续节点性质
        wsTask.setAssist(wsTaskTemp.getAssist());

        wsTask.setPkWsTaskId(IdUtils.getId());
        wsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsTask.setWorkNumber(wsTaskTemp.getWorkNumber());
        wsTask.setTaskName(wsWsTaskInputVo.getTaskName());
        wsTask.setWorkStatus(wsWsTaskInputVo.getWorkStatus());
        wsTask.setTakeRemark(taskRemark);
        wsTask.setWorkHours(null == wsWsTaskInputVo.getWorkHours() ? 0f : wsWsTaskInputVo.getWorkHours());
        // 协助业务特殊处理
        if (CommonlyConstants.YesOrNo.YES == wsTaskTemp.getAssist()) {
            wsTask.setComplete(Integer.parseInt(wsWsTaskInputVo.getYesOrNo()));
        } else {
            wsTask.setComplete(CommonlyConstants.YesOrNo.NO);
        }
        wsTaskMapper.insertWsTask(wsTask);
        // 保存节点附件信息
        supplementFiles(wsWsTaskInputVo.getFiles(), wsTask.getPkWsTaskId());
        return wsSheet;
    }


    /**
     * 附件补录
     *
     * @param wsFileInputVo 附件信息
     * @return
     */
    public void supplementFiles(List<WsFileInputVo> wsFileInputVo, String taskId) {
        if (CollectionUtil.isEmpty(wsFileInputVo)) {
            return;
        }
        List<WsFileFile> files = Lists.newArrayList();
        for (int i = 0; i < wsFileInputVo.size(); i++) {
            if (!OtherUtils.notEmpty(
                    wsFileInputVo.get(i).getFkFileId(),
                    wsFileInputVo.get(i).getFileUrl(),
                    wsFileInputVo.get(i).getFkFileName()
            )
            ) {
                throw new BusinessException("附件信息不全");
            }

            WsFileFile wsFileFile = new WsFileFile();
            MyBeanUtils.copyBeanNotNull2Bean(wsFileInputVo.get(i), wsFileFile);
            wsFileFile.setPkWsFileId(IdUtils.getId());
            wsFileFile.setFkWsTaskId(taskId);
            files.add(wsFileFile);
        }
        wsFileService.insertBatchFile(files);
    }

    /**
     * 查询工单详情中操作日志信息
     *
     * @param workNumber
     * @return
     */
    @Override
    public List<WsWsTaskInfoOutVo> selectOneWsTaskInfo(String workNumber,String taskName) {
        return wsTaskMapper.selectOneWsTaskInfo(workNumber,taskName);
    }

    /**
     * 派单
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetDispatch(WsWsTaskInputVo wsTaskInputVo) {
        if (!OtherUtils.notEmpty(wsTaskInputVo.getPkWsTaskId(), wsTaskInputVo.getBusinessDeptId())) {
            throw new BusinessException("节点id、处理科室皆为必填！！！");
        }
        WsWsTask wsWsTask = selectOneWsTaskById(wsTaskInputVo.getPkWsTaskId());
        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(wsWsTask.getWorkNumber());
        Boolean index = false;
        
        PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	if("lyszyyy".equals(globalSetting.getOrgCode())) {
            if (!OtherUtils.notEmpty(wsTaskInputVo.getFkUserId(), wsTaskInputVo.getFkFaultTypeId())) {
                throw new BusinessException("处理人、故障类型皆为必填");
            }
            // 直接转发无须接单
            if (!wsWsTask.getWorkStatus().equals(WorkSheetStatusEnum.WAITING.getValue()) && !wsWsTask.getWorkStatus().equals(WorkSheetStatusEnum.SNET.getValue())) {
                wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
                wsTaskInputVo.setTaskName(WorkSheetTaskEnum.DISPATCH.getValue());
            } else {
                wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.WAITING.getValue());
                wsTaskInputVo.setTaskName(WorkSheetTaskEnum.DISPATCH.getValue());
            }
    	}else {
    		if (wsSheet.getBusinessDeptId().equals(wsTaskInputVo.getBusinessDeptId())) {
	            if (!OtherUtils.notEmpty(wsTaskInputVo.getFkUserId(), wsTaskInputVo.getFkFaultTypeId())) {
	                throw new BusinessException("处理人、故障类型皆为必填");
	            }
	            // 直接转发无须接单
	            if (!wsWsTask.getWorkStatus().equals(WorkSheetStatusEnum.WAITING.getValue()) && !wsWsTask.getWorkStatus().equals(WorkSheetStatusEnum.SNET.getValue())) {
	                wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
	                wsTaskInputVo.setTaskName(WorkSheetTaskEnum.DISPATCH.getValue());
	            } else {
	                wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.WAITING.getValue());
	                wsTaskInputVo.setTaskName(WorkSheetTaskEnum.DISPATCH.getValue());
	            }
	        } else {
	            wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.SNET.getValue());
	            wsTaskInputVo.setTaskName(WorkSheetTaskEnum.DISPATCH.getValue());
	            index = true;
	        }
    	}
        
      
        wsTaskInputVo.setIndex(index);
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);

        // 重派时非处理科室，故障类型、处理人为空时特殊处理
        if (index) {
            if (StringUtils.isEmpty(wsTaskInputVo.getFkUserId())) {
                wsWsSheet.setFkUserId(null);
                wsWsSheet.setFkUserName(null);
                wsSheet.setFkUserDeptId(null);
                wsSheet.setFkUserDeptName(null);
            }
            if (StringUtils.isEmpty(wsTaskInputVo.getFkFaultTypeId())) {
                wsWsSheet.setFkFaultTypeId(null);
            }
            wsSheetService.updateWsSheetByWorkNumber(wsSheet);
        }

        // 消息推送
        assembledMessages(wsWsSheet.getWorkNumber(), wsTaskInputVo.getTaskName(), wsTaskInputVo.getRemark());
        return PlatformResult.success("派单成功");
    }

    /**
     * 退回
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetBack(WsWsTaskInputVo wsTaskInputVo) {
        wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.SNET.getValue());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.BACK.getValue());
        wsTaskInputVo.setFkUserId(null);
        completeTaskAndAddTask(wsTaskInputVo);
        // 节点操作
        WsWsSheet wsWsSheet = wsSheetService.selectOneWsSheetByTaskId(wsTaskInputVo.getPkWsTaskId());
        wsWsSheet.setWorkStatus(WorkSheetStatusEnum.SNET.getValue());
        wsWsSheet.setFkUserId(null);
        wsWsSheet.setFkUserName(null);
        wsWsSheet.setFkUserDeptId(null);
        wsWsSheet.setFkUserDeptName(null);
        wsSheetService.updateWsSheetByWorkNumber(wsWsSheet);
        // 退回信息
        wsWsBackService.insertBack(
                new WsWsBack(
                        IdUtils.getId(),
                        wsWsSheet.getWorkNumber(),
                        wsTaskInputVo.getRemark(),
                        wsTaskInputVo.getRemark(),
                        UserInfoHolder.getCurrentUserName(),
                        UserInfoHolder.getCurrentUserInfo().getDeptname(),
                        IndexEnum.ONE.getValue()
                )
        );
        return PlatformResult.success("退回成功");
    }

    /**
     * 追回
     *
     * @param wsTaskInputVo
     * @return
     */
    @Override
    public PlatformResult workSheetToRecover(WsWsTaskInputVo wsTaskInputVo) {
        wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.TO_RECOVER.getValue());
        completeTaskAndAddTask(wsTaskInputVo);
        return PlatformResult.success("确认完成");
    }

    @Override
    public PlatformResult workSheetToRecoverEnd(WsWsTaskInputVo wsTaskInputVo) {
        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(wsTaskInputVo.getWorkNumber());
        wsTaskInputVo.setFkUserId(wsSheet.getFkUserId());
        wsTaskInputVo.setWorkStatus(wsSheet.getEndWorkStatus());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.TO_END.getValue());
        completeTaskAndAddTask(wsTaskInputVo);
        return PlatformResult.success("撤回完成");
    }

    /**
     * 接单
     *
     * @param wsTaskInputVo
     * @return
     */
    @Override
    public PlatformResult workSheetAccept(WsWsTaskInputVo wsTaskInputVo) {
        wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.ACCEPT_WORK_SHEET.getValue());
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
        // 消息推送
        assembledMessages(wsWsSheet.getWorkNumber(), WorkSheetTaskEnum.ACCEPT_WORK_SHEET.getValue(), null);
        return PlatformResult.success("确认完成");
    }

    @Override
    public PlatformResult toClaim(WsWsTaskInputVo wsTaskInputVo) {
        wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.TO_CLAIM.getValue());
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
        // 消息推送
        assembledMessages(wsWsSheet.getWorkNumber(), WorkSheetTaskEnum.ACCEPT_WORK_SHEET.getValue(), null);
        return PlatformResult.success("确认完成");
    }

    /**
     * 转发
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetResend(WsWsTaskInputVo wsTaskInputVo) {
        if (!OtherUtils.notEmpty(wsTaskInputVo.getPkWsTaskId(), wsTaskInputVo.getBusinessDeptId())) {
            throw new BusinessException("节点id、处理科室皆为必填！！！");
        }
        WsWsTask wsWsTask =
                Optional
                        .ofNullable(selectOneWsTaskById(wsTaskInputVo.getPkWsTaskId()))
                        .orElseThrow(() -> new BusinessException("未查询到节点信息"));
        if(wsTaskMapper.selectAssistIdByWorkNumber(wsWsTask.getWorkNumber())
                .contains(wsTaskInputVo.getFkUserId())){
            throw new BusinessException("转发的处理人不能为协助人");
        }
        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(wsWsTask.getWorkNumber());
        Boolean index = false;
        if (wsSheet.getBusinessDeptId().equals(wsTaskInputVo.getBusinessDeptId())) {
            if (!OtherUtils.notEmpty(wsTaskInputVo.getFkUserId(), wsTaskInputVo.getFkFaultTypeId())) {
                throw new BusinessException("处理人、故障类型皆为必填");
            }
            // 直接转发无须接单
            if (!wsWsTask.getWorkStatus().equals(WorkSheetStatusEnum.WAITING.getValue()) && !wsWsTask.getWorkStatus().equals(WorkSheetStatusEnum.SNET.getValue())) {
                wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
                wsTaskInputVo.setTaskName(WorkSheetTaskEnum.RESEND.getValue());
            } else {
                wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.WAITING.getValue());
                wsTaskInputVo.setTaskName(WorkSheetTaskEnum.RESEND.getValue());
            }
        } else {
            index = true;
            wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.SNET.getValue());
            wsTaskInputVo.setTaskName(WorkSheetTaskEnum.RESEND.getValue());
        }
        wsTaskInputVo.setIndex(index);
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
        // 转发非处理科室，故障类型、处理人为空时特殊处理
        if (index) {
            if (StringUtils.isEmpty(wsTaskInputVo.getFkUserId())) {
                wsWsSheet.setFkUserId(null);
                wsWsSheet.setFkUserName(null);
                wsSheet.setFkUserDeptId(null);
                wsSheet.setFkUserDeptName(null);
            }
            if (StringUtils.isEmpty(wsTaskInputVo.getFkFaultTypeId())) {
                wsWsSheet.setFkFaultTypeId(null);
            }
            wsSheetService.updateWsSheetByWorkNumber(wsSheet);
        }
        // 消息推送
        assembledMessages(wsWsSheet.getWorkNumber(), WorkSheetTaskEnum.DISPATCH.getValue(), null);
        return PlatformResult.success("重新派单完成");
    }

    /**
     * 更新进度
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetUpdateProgress(WsWsTaskInputVo wsTaskInputVo) {

        WsWsTask wsTask = selectOneWsTaskById(wsTaskInputVo.getPkWsTaskId());
        MyBeanUtils.copyBeanNotNull2Bean(wsTaskInputVo, wsTask);
        wsTask.setPkWsTaskId(IdUtils.getId());
        wsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsTask.setTaskName(WorkSheetTaskEnum.UPDATE_PROCESS.getValue());
        wsTask.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
        wsTask.setTakeRemark(wsTaskInputVo.getRemark());
        wsTask.setWorkHours(wsTaskInputVo.getWorkHours());
        wsTask.setComplete(IndexEnum.ZERO.getValue());
        wsTask.setAssist(IndexEnum.ONE.getValue());
        wsTask.setFkUserId(UserInfoHolder.getCurrentUserId());
        wsTask.setFkUserName(UserInfoHolder.getCurrentUserName());
        wsTask.setFkUserDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsTask.setFkUserDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        wsTask.setCreateBy(UserInfoHolder.getCurrentUserId());
        wsTask.setCreateByName(UserInfoHolder.getCurrentUserName());
        wsTask.setCreateTime(new Date());
        wsTask.setUpdateBy(UserInfoHolder.getCurrentUserId());
        wsTask.setUpdateByName(UserInfoHolder.getCurrentUserName());
        wsTask.setUpdateTime(new Date());
        // 协助更新操作，更新工时
        if (null != wsTask.getWorkHours()) {
            WsWsSheet wsSheetTemp = wsSheetService.selectOneWsSheet(wsTask.getWorkNumber());
            wsSheetTemp.setWorkHours(wsTask.getWorkHours() + wsSheetTemp.getWorkHours());
            wsSheetService.updateWsSheetByWorkNumber(wsSheetTemp);
        }
        // 保存节点附件信息
        supplementFiles(wsTaskInputVo.getFiles(), wsTask.getPkWsTaskId());
        wsTaskMapper.insertWsTask(wsTask);
        return PlatformResult.success("更新进度完成");
    }

    /**
     * 处理完成
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetProcessingComplete(WsWsTaskInputVo wsTaskInputVo) {
        String remark = "";
        if (null != wsTaskInputVo.getWorkHours()) {
            remark = "用时" + wsTaskInputVo.getWorkHours() + "H，" + wsTaskInputVo.getRemark();
        } else {
            remark = wsTaskInputVo.getRemark();
        }
        if ((CommonlyConstants.YesOrNo.NO + "").equals(wsTaskInputVo.getYesOrNo())) {
            wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
            wsTaskInputVo.setTaskName(WorkSheetTaskEnum.PROCESSING_FAIL.getValue());
            wsTaskInputVo.setRemark(remark);
            completeTaskAndAddTask(wsTaskInputVo);
        } else {
//            // 查询是否存在协助未完成
//            if(CommonlyConstants.YesOrNo.NO == assistanceToCompleted(wsTaskInputVo.getPkWsTaskId())){
            wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.ACCEPTANCE.getValue());
            wsTaskInputVo.setTaskName(WorkSheetTaskEnum.PROCESSING_COMPLETE.getValue());
            wsTaskInputVo.setRemark(remark);
            WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
            // 消息推送
            assembledMessages(wsWsSheet.getWorkNumber(), WorkSheetTaskEnum.PROCESSING_COMPLETE.getValue(), wsTaskInputVo.getRemark());
//            }else{
//                return PlatformResult.failure("存在协助未处理完，请协助完成后再操作！");
//            }
        }
        return PlatformResult.failure(CommonlyConstants.OperationReturnValue.SUCCESS);
    }

    /**
     * 已终止
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetTerminated(WsWsTaskInputVo wsTaskInputVo) {
        WsWsTask wsWsTaskTemp = Optional.ofNullable(selectOneWsTaskById(wsTaskInputVo.getPkWsTaskId()))
                .orElseThrow(() -> new BusinessException("未查询到当前节点数据"));
        if (IndexEnum.ONE.getValue() == wsWsTaskTemp.getComplete()) {
            throw new BusinessException("当前步骤已有其他人操作过，请刷新当前页面");
        }
        // 更新工单业务主表
        wsSheetService.workSheetComplete(
                wsSheetService.selectOneWsSheet(
                        wsWsTaskTemp.getWorkNumber()),
                WorkSheetStatusEnum.TERMINATED.getValue(),
                new Date(),
                Float.parseFloat(IndexEnum.ZERO.getValue() + "")
        );

        // 当前节点更新已完成
        workSheetTaskComplete(wsWsTaskTemp, null);
        // 终止协助节点
        wsTaskMapper.updateTaskTerminated(wsWsTaskTemp.getWorkNumber());
        WsWsTask wsWsTask = new WsWsTask();
        // 初始化数据
        MyBeanUtils.copyBeanNotNull2Bean(wsWsTaskTemp, wsWsTask);
        wsWsTask.setPkWsTaskId(IdUtils.getId());
        wsWsTask.setWorkStatus(WorkSheetStatusEnum.TERMINATED.getValue());
        wsWsTask.setTaskName(WorkSheetTaskEnum.TASK_TERMINATED.getValue());
        wsWsTask.setComplete(CommonlyConstants.YesOrNo.NO);
        wsWsTask.setTakeRemark(wsTaskInputVo.getRemark());
        wsWsTask.setFkFormerUserId(null);
        wsWsTask.setFkFormerUserName(null);
        wsWsTask.setCreateBy(UserInfoHolder.getCurrentUserId());
        wsWsTask.setCreateByName(UserInfoHolder.getCurrentUserName());
        wsWsTask.setCreateTime(new Date());
        wsWsTask.setUpdateBy(UserInfoHolder.getCurrentUserId());
        wsWsTask.setUpdateByName(UserInfoHolder.getCurrentUserName());
        wsWsTask.setUpdateTime(new Date());
        wsWsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        if (CommonlyConstants.YesOrNo.NO == insertWsTask(wsWsTask)) {
            throw new BusinessException(CommonlyConstants.OperationReturnValue.FAIL);
        }
        // 消息推送
        
        try {
        	 assembledMessages(wsWsTaskTemp.getWorkNumber(), WorkSheetTaskEnum.TASK_TERMINATED.getValue(), wsTaskInputVo.getRemark());
        }catch(Exception e) {
        	e.printStackTrace();
        }
       
        return PlatformResult.success(CommonlyConstants.OperationReturnValue.SUCCESS);
    }

    /**
     * 验收
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetAcceptance(WsWsTaskInputVo wsTaskInputVo) {
        String pkWsTaskId = "";
        // 验收不通过
        if ((CommonlyConstants.YesOrNo.NO + "").equals(wsTaskInputVo.getYesOrNo())) {
            wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
            wsTaskInputVo.setTaskName(WorkSheetTaskEnum.BACK_ACCEPTANCE.getValue());
            wsTaskInputVo.setRemark(wsTaskInputVo.getRemark());
        } else {
            wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.EVALUATE.getValue());
            wsTaskInputVo.setTaskName(WorkSheetTaskEnum.COMPLETION_ACCEPTANCE.getValue());
            wsTaskInputVo.setRemark(WorkSheetTaskEnum.COMPLETION_ACCEPTANCE.getName());
        }
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
        // 验收不通过，存入打回记录
        if ((CommonlyConstants.YesOrNo.NO + "").equals(wsTaskInputVo.getYesOrNo())) {
            wsWsBackService.insertBack(
                    new WsWsBack(
                            IdUtils.getId(),
                            wsWsSheet.getWorkNumber(),
                            null,
                            wsTaskInputVo.getRemark(),
                            wsWsSheet.getRepairManName(),
                            wsWsSheet.getRepairManDeptName(),
                            IndexEnum.TWO.getValue()
                    )
            );
            // 消息推送
            assembledMessages(wsWsSheet.getWorkNumber(), WorkSheetTaskEnum.BACK_ACCEPTANCE.getValue(), wsTaskInputVo.getRemark());
        } else {
            // 查询验收节点id
            pkWsTaskId = wsTaskMapper.selectOneMaxTimeTaskByWorkNumber(wsWsSheet.getWorkNumber()).getPkWsTaskId();
        }
        return PlatformResult.success(pkWsTaskId, CommonlyConstants.OperationReturnValue.SUCCESS);
    }

    /**
     * 暂停
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetHasStopped(WsWsTaskInputVo wsTaskInputVo) {
        wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.SUSPENDED.getValue());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.HAS_STOPPED.getValue());
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
        // 暂停协助节点
        wsTaskMapper.updateTaskStop(selectOneWsTaskById(wsTaskInputVo.getPkWsTaskId()).getWorkNumber());
        // 消息推送
        assembledMessages(wsWsSheet.getWorkNumber(), WorkSheetTaskEnum.HAS_STOPPED.getValue(), wsTaskInputVo.getRemark());
        return PlatformResult.success(CommonlyConstants.OperationReturnValue.SUCCESS);
    }


    /**
     * 获取恢复节点信息
     *
     * @param taskId
     * @return
     */
    @Override
    public PlatformResult openNodeInfo(String taskId) {
        if (lastTaskInfo(taskId)) {
            throw new BusinessException("当前步骤已有其他人操作过，请刷新当前页面");
        }
        WsWsTask wsWsTask = wsTaskMapper.selectOneWsTaskById(taskId);
        WsWsOpenTaskInfoOutVo wsOpenTaskInfoOutVo = new WsWsOpenTaskInfoOutVo();
        BeanUtils.copyProperties(wsWsTask, wsOpenTaskInfoOutVo);
        return PlatformResult.success(wsOpenTaskInfoOutVo);
    }

    /**
     * 恢复所需信息
     *
     * @param wsTaskInputVo
     * @return
     */
    @Transactional
    @Override
    public PlatformResult workSheetHadRecovered(WsWsTaskInputVo wsTaskInputVo) {
        wsTaskInputVo.setWorkStatus(WorkSheetStatusEnum.PROCESSING.getValue());
        wsTaskInputVo.setTaskName(WorkSheetTaskEnum.HAD_RECOVERED.getValue());
        WsWsSheet wsWsSheet = completeTaskAndAddTask(wsTaskInputVo);
        // 开启协助节点
        wsTaskMapper.updateTaskOpen(selectOneWsTaskById(wsTaskInputVo.getPkWsTaskId()).getWorkNumber());
        // 消息推送
        assembledMessages(wsWsSheet.getWorkNumber(), "", null);
        return PlatformResult.success(CommonlyConstants.OperationReturnValue.SUCCESS);
    }

    /**
     * 查看是否存在协助未完成
     *
     * @param taskId
     * @return
     */
    @Override
    public int assistanceToCompleted(String taskId) {
        return wsTaskMapper.assistanceToCompleted(taskId);
    }


    /**
     * 节点当前步骤已完成
     *
     * @param wsTask
     * @return
     */
    @Transactional
    @Override
    public WsWsTask workSheetTaskComplete(WsWsTask wsTask, String workStatus) {
        if (!StringUtil.isEmpty(workStatus)) {
            wsTask.setWorkStatus(workStatus);
        }
        wsTask.setComplete(CommonlyConstants.YesOrNo.YES);
        wsTask.setUpdateTime(new DateTime());
        wsTask.setUpdateBy(UserInfoHolder.getCurrentUserId());
        wsTask.setUpdateByName(UserInfoHolder.getCurrentUserName());
        wsTaskMapper.updateWsTaskById(wsTask);
        return wsTask;
    }

    /**
     * 查看最新的一条节点信息
     *
     * @param workNumber 工单编号
     * @return
     */
    @Override
    public WsWsTask selectOneMaxTimeTaskByWorkNumber(String workNumber) {
        return wsTaskMapper.selectOneMaxTimeTaskByWorkNumber(workNumber);
    }

    /**
     * 组装消息推送数据
     *
     * @param workNumber
     * @param type
     */
    @Override
    public void assembledMessages(String workNumber, String type, String remark) {
        WsWsSheet wsWsSheet = wsSheetService.selectOneWsSheet(workNumber);
        String title = "";
        String content = "";
        String url = "";
        String receiver = "";
        // 分派工单
        if (WorkSheetTaskEnum.DISPATCH.getValue().equals(type)) {
            title = "您有新的工单请及时处理";
            content = "报修人：" + wsWsSheet.getRepairManDeptName() + "-" + wsWsSheet.getRepairManName() + "   " +
                    "分配人：" + UserInfoHolder.getCurrentUserName() + "<br/>" +
                    (null == wsWsSheet.getRequiredCompletionTime() ? "" : "要求日期：" + DateUtil.format(wsWsSheet.getRequiredCompletionTime(), "yyyy-MM-dd")) +
                    DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_EMERGENCY.getValue(), wsWsSheet.getFaultEmergency() + "") + "<br/>" +
                    "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion();
            receiver = wsWsSheet.getFkUserId();

//            if (StringUtils.isEmpty(wsWsSheet.getFkUserId())) {
//                // 工单上报，推送提醒
//                wsSheetService.pushWeChatMessage(
//                        wsWsSheet.getBusinessDeptId(),
//                        DateUtils.dateToStringFormat("yyyy-MM-dd", new Date()),
//                        wsWsSheet.getWorkNumber(),
//                        content
//                );
//                return;
//            }
            
            //浏阳中短信提醒
            PlatformResult<GlobalSetting> res = globalSettingsFeignService.getSafeGlobalSetting("Y");
        	GlobalSetting globalSetting = res.getObject();
        	if("lyszyyy".equals(globalSetting.getOrgCode())) {
        		log.info("发送短信了==========接收人为：" + wsWsSheet.getFkUserPhone());
        		LyszyyyMessageUtil.sendMessage(content, wsWsSheet.getFkUserPhone());
        	}
        	
        	
            // 响应工单
        } else if (WorkSheetTaskEnum.ACCEPT_WORK_SHEET.getValue().equals(type)) {
            title = "您的工单已由" + wsWsSheet.getFkUserDeptName() + "-" + wsWsSheet.getFkUserName() + "接收";
            content = "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion();
            receiver = wsWsSheet.getRepairManId();

            // 完成工单
        } else if (WorkSheetTaskEnum.PROCESSING_COMPLETE.getValue().equals(type)) {
            title = "工单已处理完成请及时确认并评价";
            content = "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion();
            receiver = wsWsSheet.getRepairManId();

            // 申请协助
        } else if (WorkSheetTaskEnum.ASSIST.getValue().equals(type)) {
            title = wsWsSheet.getFkUserName() + "请您协助处理工单，请及时关注";
            List<WsWsTask> wsTaskList = wsTaskMapper.selectAssisListByWorkNumber(workNumber);
            for (WsWsTask wsWsTask : wsTaskList) {
                content =
                        (null == wsWsSheet.getRequiredCompletionTime() ? "" : "要求时间：" + DateUtil.format(wsWsSheet.getRequiredCompletionTime(), "yyyy-MM-dd") + "<br/>") +
//                        "要求日期：" + DateUtil.format(wsWsSheet.getRequiredCompletionTime(), "yyyy-MM-dd HH:mm:ss") + "<br/>" +
                                "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion() + "<br/>" +
                                "协助内容：" + wsWsTask.getTakeRemark().replace("申请" + wsWsTask.getFkUserName() + "协助。", "");
                receiver = wsWsTask.getFkUserId();

                List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(Arrays.asList(receiver));

                pushMessage(workNumber, title, content, url, userListByIds.stream().map(EmployeeResp::getEmployeeNo).collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue())), receiver);
                // 微信端消息
                PlatformResult<String> stringPlatformResult = informationFeignService.sendNotice(
                        NoticeReq.builder()
                                .content(content)
                                .noticeType("3")
                                .receiver(userListByIds.stream().map(EmployeeResp::getEmployeeNo).collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue())))
                                .sender(UserInfoHolder.getCurrentUserCode())
                                .senderName(UserInfoHolder.getCurrentUserName())
                                .subject(title)
                                .wxSendType("1")
                                .url(workInfoUrl + "?id=" + workNumber + "&showDtail=false")
                                .build()
                );
            }
            return;

            // 暂停
        } else if (WorkSheetTaskEnum.HAS_STOPPED.getValue().equals(type)) {
            // 操作人
            String fkUserId = UserInfoHolder.getCurrentUserId();
            title = "工单已被" + UserInfoHolder.getCurrentUserName() + "暂停，请及时关注";
            content = "操作人：" + UserInfoHolder.getCurrentUserName() + "<br/>" +
                    "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion() + "<br/>" +
                    "原因：" + remark;
            if (fkUserId.equals(wsWsSheet.getRepairManId())) {
                receiver = wsWsSheet.getFkUserId();
            } else if (fkUserId.equals(wsWsSheet.getFkUserId())) {
                receiver = wsWsSheet.getRepairManId();
            } else {
                receiver = wsWsSheet.getRepairManId() + CuttingOperatorEnum.COMMA.getValue() + wsWsSheet.getFkUserId();
            }

            // 终止
        } else if (WorkSheetTaskEnum.TASK_TERMINATED.getValue().equals(type)) {
            // 操作人
            String fkUserId = UserInfoHolder.getCurrentUserId();
            title = "工单已被" + UserInfoHolder.getCurrentUserName() + "终止，请及时关注";
            content = "操作人：" + UserInfoHolder.getCurrentUserName() + "<br/>" +
                    "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion() + "<br/>" +
                    "原因：" + remark;
            if (fkUserId.equals(wsWsSheet.getRepairManId())) {
                // 无终止人，且操作人为报修人时，无消息推送
                if (StringUtils.isEmpty(wsWsSheet.getFkUserId())) {
                    return;
                }
                receiver = wsWsSheet.getFkUserId();
            } else if (fkUserId.equals(wsWsSheet.getFkUserId())) {
                receiver = wsWsSheet.getRepairManId();
            } else {
                receiver = wsWsSheet.getRepairManId() + CuttingOperatorEnum.COMMA.getValue() + wsWsSheet.getFkUserId();
            }
            // 开启
        } else if ("".equals(type)) {
            // 操作人
            String fkUserId = UserInfoHolder.getCurrentUserId();
            title = "工单已被" + UserInfoHolder.getCurrentUserName() + "开启，请及时关注";
            content = "操作人：" + UserInfoHolder.getCurrentUserName() +
                    "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion() + "<br/>";
            if (fkUserId.equals(wsWsSheet.getRepairManId())) {
                receiver = wsWsSheet.getFkUserId();
            } else if (fkUserId.equals(wsWsSheet.getFkUserId())) {
                receiver = wsWsSheet.getRepairManId();
            } else {
                receiver = wsWsSheet.getRepairManId() + CuttingOperatorEnum.COMMA.getValue() + wsWsSheet.getFkUserId();
            }

            // 催办
        } else if (null == type) {
            title = UserInfoHolder.getCurrentUserName() + "催办工单，请尽快处理";
            content = "目前工单催办" + wsWsSheet.getHatenCount() + "次" +
                    (null == wsWsSheet.getRequiredCompletionTime() ? "" : "要求日期：" + DateUtil.format(wsWsSheet.getRequiredCompletionTime(), "yyyy-MM-dd") + "<br/>") +
//                    "要求日期：" + DateUtil.format(wsWsSheet.getRequiredCompletionTime(), "yyyy-MM-dd HH:mm:ss") + "<br/>" +
                    "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion();
            receiver = wsWsSheet.getFkUserId();

            // 验收不通过
        } else if (WorkSheetTaskEnum.BACK_ACCEPTANCE.getValue().equals(type)) {
            title = "工单验收不成功，请尽快处理";
            content = "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion() + "<br/>" +
                    "原因：" + remark;
            receiver = wsWsSheet.getFkUserId();
        }else if("WF".equals(type)){
            title = "流程已转工单，可在一站式后勤菜单-我的报修查看进度。";
            content = "[" + wsWsSheet.getWorkNumber() + "]" + wsWsSheet.getFaultDeion();
            receiver = wsWsSheet.getRepairManId();
        }
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(Arrays.asList(receiver));

       
        // web端消息
        pushMessage(workNumber, title, content, url, userListByIds.stream().map(EmployeeResp::getEmployeeNo).collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue())), receiver);

        // 微信端消息
        pushOaMessage(
                content,
                "3",
                userListByIds.stream().map(EmployeeResp::getEmployeeNo).collect(Collectors.joining(CuttingOperatorEnum.COMMA.getValue())),
                title,
                "1",
                workInfoUrl + "?id=" + workNumber + "&showDtail=false",
                null
        );
    }

    /**
     * Oa消息推送
     *
     * @param content      内容
     * @param noticeType   通知类型   1 短信   2邮件   3微信推送  4web
     * @param receiverCode 接收人code
     * @param title        标题
     * @param wxSendType   微信推送类型 暂时只支持 1卡片消息 2文本消息
     * @param url          微信卡片跳转url
     * @param toUrl        web跳转url
     * @return
     */
    @Override
    public boolean pushOaMessage(String content,
                                 String noticeType,
                                 String receiverCode,
                                 String title,
                                 String wxSendType,
                                 String url,
                                 String toUrl
    ) {
    	
    	if(org.apache.commons.lang3.StringUtils.isNotBlank(receiverCode)) {
    		 log.info("》》》》》》》》》》》》》》》》》》OA消息推送开始");
    	        NoticeReq noticeReq = NoticeReq.builder()
    	                .content(content)
    	                .noticeType(noticeType)
    	                .receiver(receiverCode)
    	                .sender(UserInfoHolder.getCurrentUserCode())
    	                .senderName(UserInfoHolder.getCurrentUserName())
    	                .subject(title)
    	                .wxSendType(wxSendType)
    	                .build();
    	        if(!StringUtils.isEmpty(url)){
    	            noticeReq.setUrl(url);
    	        }
    	        if(!StringUtils.isEmpty(toUrl)){
    	            noticeReq.setToUrl(toUrl);
    	        }
    	        noticeReq.setSource("工单管理");
    	        PlatformResult<String> stringPlatformResult = informationFeignService.sendNotice(noticeReq);
    	        if (stringPlatformResult.isSuccess()) {
    	            log.info("》》》》》》》》》》》》》》》》》》OA消息推送结束：" + stringPlatformResult.getMessage());
    	            return true;
    	        }
    	        log.error("》》》》》》》》》》》》》》》》》》OA消息推送失败：" + stringPlatformResult.getMessage());
    	}
       
        return false;

    }

    /**
     * 基础服务推送消息
     *\
     * @param workNumber   工单编号
     * @param title        消息标题
     * @param content      消息内容
     * @param url          内容跳转页面
     * @param receiverCode 接收人Code
     * @param receiverId   接收人Id
     */
    @Override
    public boolean pushMessage(String workNumber, String title, String content, String url, String receiverCode, String receiverId) {
        // 存入消息推送记录
        WsWsMessage message = new WsWsMessage(IdUtils.getId(), workNumber, title, content, receiverId, url);
        WsWsSheet wsSheet = wsSheetService.selectOneWsSheet(workNumber);
        wsWsMessageService.insertMessage(message);
        return pushOaMessage(
                content,
                "4",
                receiverCode,
                title,
                null,
                null,
                "&&NoJump#"+workNumber+"&MessageType=WorkOrder&status="+wsSheet.getWorkStatus()
        );

//        MessageWebPushReq messageWebPushReq = new MessageWebPushReq();
//        MessageWebPushReq.WebMessageTemplate webMessageTemplate = new MessageWebPushReq.WebMessageTemplate();
//        webMessageTemplate.setId(workNumber);
//        webMessageTemplate.setTitle(title);
//        webMessageTemplate.setContent(content);
//        webMessageTemplate.setUrl(url);
//        messageWebPushReq.setReceiver(receiverCode);
//        messageWebPushReq.setWebMessageTemplate(webMessageTemplate);
//        log.info("》》》》》》》》》》》》》》》》》》基础服务消息推送开始");
//        PlatformResult platformResult = messageFeignService.webPush(messageWebPushReq);
//        if (!platformResult.isSuccess()) {
//            log.error("》》》》》》》》》》》》》》》》》》基础服务消息推送失败：" + platformResult.getMessage());
//            return platformResult.isSuccess();
//        }
//        log.info("》》》》》》》》》》》》》》》》》》基础服务消息推送结束：" + platformResult.getMessage());
//         platformResult.isSuccess();
    }


    @Override
    public List<WsTaskWorkHoursListOutVo> selectAllTaskWorkHoursList(String workNumber) {
        return wsTaskMapper.selectAllTaskWorkHoursList(workNumber);
    }

    @Override
    public WsWsTaskInfoOutVo selectSubmitKnowledgeBaseInfo(String workNumber) {
        return wsTaskMapper.selectSubmitKnowledgeBaseInfo(workNumber);
    }

    /**
     * 查询当前节点是否完成（非协助人节点）
     *
     * @param pkWsTaskId 节点id
     * @return
     */
    public boolean lastTaskInfo(String pkWsTaskId) {
        int complete = wsTaskMapper.lastTaskInfoIsComplete(pkWsTaskId);
        if (IndexEnum.ONE.getValue() == complete) {
            return true;
        }
        return false;
    }
}
