import $dayjs from 'dayjs';
import convertCapital from '@/common/js/changeNumToChinese.js';

//自定义方法
var customFun = (function() {
  var getDateRangeLength = function(startTime, endTime, diffType) {
    //将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式
    startTime = startTime.replace(/\-/g, '/');
    endTime = endTime.replace(/\-/g, '/');
    //将计算间隔类性字符转换为小写
    diffType = diffType.toLowerCase();
    var sTime = new Date(startTime); //开始时间
    var eTime = new Date(endTime); //结束时间
    //作为除数的数字
    var timeType = 1;
    switch (diffType) {
      case 'second':
        timeType = 1000;
        break;
      case 'minute':
        timeType = 1000 * 60;
        break;
      case 'hour':
        timeType = 1000 * 3600;
        break;
      case 'day':
        timeType = 1000 * 3600 * 24;
        break;
      default:
        break;
    }
    return parseInt((eTime.getTime() - sTime.getTime()) / parseInt(timeType));
  };
  //字符串拼接
  function CONCAT() {
    var arr = Array.prototype.slice.call(arguments);
    return arr.join('');
  }
  //小写转大写
  function CHANGCASE(val) {
    return convertCapital(val);
  }
  //数字求和
  function SUM() {
    var d = 0;
    var arr = Array.prototype.slice.call(arguments);
    for (var i = 0; i < arr.length; i++) {
      arr[i] = typeof arr[i] == 'string' ? arr[i].replace(/,/g, '') : arr[i];
      if (isNaN(Number(arr[i]))) {
        throw '' + arr[i] + ' 不是数字';
      } else {
        d = addSumPrecision(d, Number(arr[i]));
      }
    }
    return d.toString();
  }

  //加法精度处理
  function addSumPrecision(num1, num2) {
    var p1 = 0;
    var p2 = 0;
    if (num1.toString().split('.').length > 1) {
      p1 = num1.toString().split('.')[1].length;
    }
    if (num2.toString().split('.').length > 1) {
      p2 = num2.toString().split('.')[1].length;
    }
    var p = p1 > p2 ? p1 : p2;
    var n1 = num1 * Math.pow(10, p);
    var n2 = num2 * Math.pow(10, p);
    return (n1 + n2) / Math.pow(10, p);
  }

  function MULTIPLY() {
    var d = 1;
    var arr = Array.prototype.slice.call(arguments);
    for (var i = 0; i < arr.length; i++) {
      if (isNaN(Number(arr[i]))) {
        throw '' + arr[i] + ' 不是数字';
      } else {
        d = addMultiplyPrecision(d, Number(arr[i]));
      }
    }
    return d.toString();
  }

  //乘法精度处理
  function addMultiplyPrecision(num1, num2) {
    var p1 = 0;
    var p2 = 0;
    if (num1.toString().split('.').length > 1) {
      p1 = num1.toString().split('.')[1].length;
    }
    if (num2.toString().split('.').length > 1) {
      p2 = num2.toString().split('.')[1].length;
    }
    var p = p1 + p2;
    var n1 = num1 * Math.pow(10, p1);
    var n2 = num2 * Math.pow(10, p2);
    return (n1 * n2) / Math.pow(10, p);
  }

  //日期间隔
  function DATELONG(start, end) {
    if (!start && !end) {
      throw '参数错误，日期不能为空';
    }
    if (!start || !end) {
      return '';
    }
    var startVal = $dayjs(start).format('YYYY-MM-DD');
    var endVal = $dayjs(end).format('YYYY-MM-DD');
    var day = getDateRangeLength(startVal, endVal, 'day');
    return day;
  }
  //从身份证号获取出身年月
  function BIRTHDAYFROMCARD(idcard, format) {
    var idcard = idcard;
    var format = format || 'YYYY-MM-DD';
    var s;
    if (!idcard) {
      throw '输入的身份证号码不合法';
    }
    if (idcard.length == 15) {
      s = '19' + idcard.substr(6, 6);
      return s.replace(/(.{4})(.{2})/, '$1-$2-');
    } else if (idcard.length == 18) {
      s = idcard.substr(6, 8);
      return s.replace(/(.{4})(.{2})/, '$1-$2-');
    } else {
      throw '输入的身份证号码不合法';
    }
  }
  return {
    CONCAT: CONCAT,
    CHANGCASE: CHANGCASE,
    SUM: SUM,
    MULTIPLY: MULTIPLY,
    DATELONG: DATELONG,
    BIRTHDAYFROMCARD: BIRTHDAYFROMCARD
  };
})();

const obj = {
  customFun,
  //parsehtml
  /**
    *@param start: function (tag, attrs, unary) {
                    results += '<' + tag;
                    for (var i = 0; i < attrs.length; i++) results += ' ' + attrs[i].name + '="' + attrs[i].escaped + '"';
                    results += '>';
                },
    *@param end: function (tag) {
                    results += '</' + tag + '>';
                },
    *@param chars: function (text) {
                    results += text;
                },
    *@param comment: function (text) {
                    results += '<!--' + text + '-->';
                },
     * **/
  parsehtml: (function() {
    var _this = {};
    // Regular Expressions for parsing tags and attributes
    var startTag = /^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,
      endTag = /^<\/([-A-Za-z0-9_]+)[^>]*>/,
      attr = /([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g;
    // Empty Elements - HTML 5
    var empty = makeMap(
      'area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr'
    );
    // Block Elements - HTML 5
    var block = makeMap(
      'a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,ins,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video'
    );
    // Inline Elements - HTML 5
    var inline = makeMap(
      'abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var'
    );
    // Elements that you can, intentionally, leave open
    // (and which close themselves)
    var closeSelf = makeMap('colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr');
    // Attributes that have their values filled in disabled="disabled"
    var fillAttrs = makeMap(
      'checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected'
    );
    // Special Elements (can contain anything)
    var special = makeMap('script,style');
    var HTMLParser = (_this.HTMLParser = function(html, handler) {
      var index,
        chars,
        match,
        stack = [],
        last = html;
      stack.last = function() {
        return _this[_this.length - 1];
      };
      while (html) {
        chars = true;
        // Make sure we're not in a script or style element
        if (!stack.last() || !special[stack.last()]) {
          // Comment
          if (html.indexOf('<!--') == 0) {
            index = html.indexOf('-->');
            if (index >= 0) {
              if (handler.comment) handler.comment(html.substring(4, index));
              html = html.substring(index + 3);
              chars = false;
            }
            // end tag
          } else if (html.indexOf('</') == 0) {
            match = html.match(endTag);
            if (match) {
              html = html.substring(match[0].length);
              match[0].replace(endTag, parseEndTag);
              chars = false;
            }
            // start tag
          } else if (html.indexOf('<') == 0) {
            match = html.match(startTag);
            if (match) {
              html = html.substring(match[0].length);
              match[0].replace(startTag, parseStartTag);
              chars = false;
            }
          }
          if (chars) {
            index = html.indexOf('<');
            var text = index < 0 ? html : html.substring(0, index);
            html = index < 0 ? '' : html.substring(index);
            if (handler.chars) handler.chars(text);
          }
        } else {
          html = html.replace(
            new RegExp('([\\s\\S]*?)</' + stack.last() + '[^>]*>'),
            function(all, text) {
              text = text.replace(
                /<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,
                '$1$2'
              );
              if (handler.chars) handler.chars(text);
              return '';
            }
          );
          parseEndTag('', stack.last());
        }
        if (html == last) throw 'Parse Error: ' + html;
        last = html;
      }
      // Clean up any remaining tags
      parseEndTag();
      function parseStartTag(tag, tagName, rest, unary) {
        tagName = tagName.toLowerCase();
        if (block[tagName]) {
          while (stack.last() && inline[stack.last()]) {
            parseEndTag('', stack.last());
          }
        }
        if (closeSelf[tagName] && stack.last() == tagName) {
          parseEndTag('', tagName);
        }
        unary = empty[tagName] || !!unary;
        if (!unary) stack.push(tagName);
        if (handler.start) {
          var attrs = [];

          rest.replace(attr, function(match, name) {
            var value = arguments[2]
              ? arguments[2]
              : arguments[3]
              ? arguments[3]
              : arguments[4]
              ? arguments[4]
              : fillAttrs[name]
              ? name
              : '';
            attrs.push({
              name: name,
              value: value,
              escaped: value.replace(/(^|[^\\])"/g, '$1\\"') //"
            });
          });
          if (handler.start) handler.start(tagName, attrs, unary);
        }
      }
      function parseEndTag(tag, tagName) {
        // If no tag name is provided, clean shop
        if (!tagName) var pos = 0;
        // Find the closest opened tag of the same type
        else
          for (var pos = stack.length - 1; pos >= 0; pos--)
            if (stack[pos] == tagName) break;
        if (pos >= 0) {
          // Close all the open elements, up the stack
          for (var i = stack.length - 1; i >= pos; i--)
            if (handler.end) handler.end(stack[i]);
          // Remove the open elements from the stack
          stack.length = pos;
        }
      }
    });
    _this.HTMLtoXML = function(html) {
      var results = '';
      HTMLParser(html, {
        start: function(tag, attrs, unary) {
          results += '<' + tag;
          for (var i = 0; i < attrs.length; i++)
            results += ' ' + attrs[i].name + '="' + attrs[i].escaped + '"';
          results += '>';
        },
        end: function(tag) {
          results += '</' + tag + '>';
        },
        chars: function(text) {
          results += text;
        },
        comment: function(text) {
          results += '<!--' + text + '-->';
        }
      });
      return results;
    };
    _this.HTMLtoDOM = function(html, doc) {
      // There can be only one of these elements
      var one = makeMap('html,head,body,title');
      // Enforce a structure for the document
      var structure = {
        link: 'head',
        base: 'head'
      };
      if (!doc) {
        if (typeof DOMDocument != 'undefined') doc = new DOMDocument();
        else if (
          typeof document != 'undefined' &&
          document.implementation &&
          document.implementation.createDocument
        )
          doc = document.implementation.createDocument('', '', null);
        else if (typeof ActiveX != 'undefined')
          doc = new ActiveXObject('Msxml.DOMDocument');
      } else
        doc =
          doc.ownerDocument ||
          (doc.getOwnerDocument && doc.getOwnerDocument()) ||
          doc;
      var elems = [],
        documentElement =
          doc.documentElement ||
          (doc.getDocumentElement && doc.getDocumentElement());
      // If we're dealing with an empty document then we
      // need to pre-populate it with the HTML document structure
      if (!documentElement && doc.createElement)
        (function() {
          var html = doc.createElement('html');
          var head = doc.createElement('head');
          head.appendChild(doc.createElement('title'));
          html.appendChild(head);
          html.appendChild(doc.createElement('body'));
          doc.appendChild(html);
        })();
      // Find all the unique elements
      if (doc.getElementsByTagName)
        for (var i in one) one[i] = doc.getElementsByTagName(i)[0];
      // If we're working with a document, inject contents into
      // the body element
      var curParentNode = one.body;
      HTMLParser(html, {
        start: function(tagName, attrs, unary) {
          // If it's a pre-built element, then we can ignore
          // its construction
          if (one[tagName]) {
            curParentNode = one[tagName];
            if (!unary) {
              elems.push(curParentNode);
            }
            return;
          }
          var elem = doc.createElement(tagName);
          for (var attr in attrs)
            elem.setAttribute(attrs[attr].name, attrs[attr].value);
          if (structure[tagName] && typeof one[structure[tagName]] != 'boolean')
            one[structure[tagName]].appendChild(elem);
          else if (curParentNode && curParentNode.appendChild)
            curParentNode.appendChild(elem);
          if (!unary) {
            elems.push(elem);
            curParentNode = elem;
          }
        },
        end: function(tag) {
          elems.length -= 1;
          // Init the new parentNode
          curParentNode = elems[elems.length - 1];
        },
        chars: function(text) {
          curParentNode.appendChild(doc.createTextNode(text));
        },
        comment: function(text) {
          // create comment node
        }
      });
      return doc;
    };
    function makeMap(str) {
      var obj = {},
        items = str.split(',');
      for (var i = 0; i < items.length; i++) obj[items[i]] = true;
      return obj;
    }
    return HTMLParser;
  })(),
  createCustomFun: function(str) {
    let func = Function('"use strict";  return (' + str + ')');
    return func;
  },
  //解析计算规则
  parseCalRules: function(str, template, itemData) {
    var isFiled = false;
    var funcReg = /^func_name_/;
    var filedReg = /^form_item_keyid_/;
    var funStr = '';
    var triggerField = [];
    var triggerFieldKeyId = [];
    //非法字符过滤
    // var illegalStrReg = /(&ldquo;|&rdquo;|“|‘|（|）|，)/g;
    this.parsehtml(str, {
      start: function(tag, attrs, unary) {
        for (var i = 0; i < attrs.length; i++) {
          if (attrs[i].name == 'class' && funcReg.test(attrs[i].value)) {
            funStr +=
              'this.customFun.' + attrs[i].value.replace(/func_name_/, '');
            isFiled = true;
            break;
          }
          if (attrs[i].name == 'class' && filedReg.test(attrs[i].value)) {
            var field =
              template[attrs[i].value.replace(/form_item_keyid_/, '')];
            if (!field.fieldName) {
              console.error('字段缺失');
              break;
            }
            triggerFieldKeyId.push(field.keyId);
            triggerField.push(field.fieldName);
            funStr += `'${itemData[field.fieldName]}'`;
            isFiled = true;
            break;
          }
        }
      },
      chars: function(text) {
        if (!isFiled) {
          funStr += text;
        }
      },
      end: function(tag) {
        isFiled = false;
      },
      comment: function(text) {}
    });
    return {
      fun: this.createCustomFun(funStr),
      triggerField: triggerField,
      triggerFieldKeyId: triggerFieldKeyId
    };
  },
  /**
   * @desc 解析计算公式内容
   * @param {string} calculationRole 公式 HTML 内容
   * @param {function} computedValueKey 计算公式涉及传参的参数名称
   * @returns [{string}, {string[]}] {funStr, relatedAttrs} 公式内容
   */
  computedFormula(calculationRole, computedValueKey) {
    /**@desc 函数内容 */
    let funStr = '',
      funcReg = /^func_name_/,
      filedReg = /^form_item_keyid_/,
      /**@desc 相关参数唯一标识 */
      relatedAttrs = [],
      isFiled = false;
    this.parsehtml(calculationRole, {
      start: function(tag, attrs, unary) {
        for (var i = 0; i < attrs.length; i++) {
          // 匹配算法名字
          if (attrs[i].name == 'class' && funcReg.test(attrs[i].value)) {
            funStr +=
              'this.customFun.' + attrs[i].value.replace(/func_name_/, '');
            isFiled = true;
            break;
          }
          // 匹配字段名称
          if (attrs[i].name == 'class' && filedReg.test(attrs[i].value)) {
            /**@type {string} 所需操作数据在表单的唯一标识 */
            let formKey = attrs[i].value.replace(/form_item_keyid_/, ''),
              valueName = '';
            if (computedValueKey) {
              valueName = computedValueKey(formKey);
            } else {
              valueName = formKey;
            }
            if (!valueName) {
              console.error('字段缺失');
              break;
            }
            relatedAttrs.push(formKey);
            funStr += 'data.' + valueName;
            isFiled = true;
            break;
          }
        }
      },
      chars: function(text) {
        if (!isFiled) {
          funStr += text;
        }
      },
      end: function(tag) {
        isFiled = false;
      },
      comment: function(text) {}
    });
    return {
      funStr: Function('data', '"use strict";  return (' + funStr + ')').bind(
        this
      ),
      relatedAttrs
    };
  }
};
export default obj;
