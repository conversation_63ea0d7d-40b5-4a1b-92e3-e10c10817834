<template>
  <view class="form_content">
    <view class="row_tab" v-for="item in formTemplate" :key="item.keyId">
      <view
        class="form_row dis_flex"
        v-if="item.fieldType === 'file' || item.fieldType === 'fileTemplate'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view
            class="fiel_list"
            v-if="
              Array.isArray(form[item.fieldName]) &&
                form[item.fieldName].length > 0
            "
          >
            <view
              class="file_item"
              v-for="(i, index) in form[item.fieldName]"
              :key="index"
            >
              <text
                class="oa-icon"
                :class="`oa-icon-${$oaModule.formatFileType(i.fileExtension)}`"
              ></text>
              <text @tap="previewFile(i.id, i.fileName)">
                {{ i.originalName }}
              </text>
              <text
                class="preview-text"
                @tap.stop="previewFile(i.id, i.fileName)"
              >
                预览
              </text>
              <text
                class="download-text"
                @tap.stop="downLoadFile(i.id, i.fileName)"
              >
                下载
              </text>
              <text
                class="preview-text"
                @tap.stop="connectionFile(i.id, i.fileName)"
              >
                收藏
              </text>
            </view>
          </view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="
          item.fieldType === 'personChose' || item.fieldType === 'deptChose'
        "
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          {{
            form[item.fieldName] != null
              ? form[item.fieldName].split('--')[0]
              : ''
          }}
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'processChoose'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view
            class="link"
            v-for="(item, index) in form[item.fieldName]"
            :key="index"
            @click="checkProcessDetail(item)"
          >
            {{ `${item.workflowName} - ${item.createDate}` }}
          </view>
        </view>
      </view>
      <view
        class="form_row"
        v-else-if="
          item.fieldType === 'table' || item.fieldType === 'operationItem'
        "
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value row_value_table">
          <uni-table
            ref="ntable"
            titleTextAlign="center"
            textAlign="center"
            :tableData="form[item.fieldName]"
            :columns="item.columns"
            :stickSide="false"
          ></uni-table>
        </view>
      </view>
      <view class="form_row" v-else-if="item.fieldType === 'childForm'">
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value row_value_table">
          <uni-table
            ref="ntable"
            titleTextAlign="center"
            textAlign="center"
            :tableData="form[item.fieldName]"
            :columns="item.childFormColumns"
            :stickSide="false"
          ></uni-table>
        </view>
      </view>
      <view class="form_row" v-else-if="item.fieldType === 'essentialDrugDic'">
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value row_value_table">
          <EssentialDrugDicSelect v-model="form[item.fieldName]" disabled />
        </view>
      </view>
      <view class="form_row" v-else-if="item.fieldType === 'medicalSupplieDic'">
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value row_value_table">
          <MedicalSupplieDicSelect v-model="form[item.fieldName]" disabled />
        </view>
      </view>
      <view class="form_row dis_flex" v-else-if="item.fieldType === 'date'">
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          {{ form[item.fieldName] | formatTime }}
        </view>
      </view>
      <view class="form_row" v-else-if="item.fieldType === 'remark'">
        <!-- <view class="row_lable">{{ item.showName }}</view> -->
        <view
          class="row_value remark"
          :class="{
            'red-text': item.isRed,
            'bold-text': item.isBold
          }"
        >
          <rich-text
            :nodes="item.remark.replace(/(\r\n)|(\n)/g, '<br>')"
          ></rich-text>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'interworkSick'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view
          class="row_value"
          v-if="JSON.stringify(form[`${item.fieldName}_valName`]) != '{}'"
        >
          <view class="" v-for="(val, key) in form[item.fieldName]" :key="key">
            {{
              `【${key | sickKeyFilter}】修改前：${
                key == 'sex'
                  ? form[`${item.fieldName}_valName`][key] == '1'
                    ? '男'
                    : form[`${item.fieldName}_valName`][key] == '2'
                    ? '女'
                    : '未知'
                  : form[`${item.fieldName}_valName`][key]
              } 修改后：${
                key == 'sex'
                  ? val == '1'
                    ? '男'
                    : val == '2'
                    ? '女'
                    : '未知'
                  : val
              }`
            }}
          </view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'interworkSettle'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view>{{ form[item.fieldName]['index'] }}</view>
          <view>{{ form[item.fieldName]['value'] }}</view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'interworkPay'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view
            class=""
            v-for="(item, index) in form[item.fieldName]"
            :key="index"
          >
            <view>{{ `${index + 1}、${item.index}` }}</view>
            <view>{{ item.value }}</view>
          </view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'interworkHosPro'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view
            class=""
            v-for="(item, index) in form[item.fieldName]"
            :key="index"
          >
            <view>{{ `${index + 1}、${item.SHOW_ORDER_NAME}` }}</view>
            <view>{{ `医嘱日期：${item.BOOK_DATE}` }}</view>
            <view class="dis_flex" style="justify-content: space-between;">
              <text>{{ `数量：${item.SHOW_QTY}` }}</text>
              <text>{{ `单价：${item.RETAIL_PRICE}` }}</text>
            </view>
            <view>{{ `总金额：${item.RETAIL_VALUE}` }}</view>
          </view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'interworkTest'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view v-for="(item, index) in form[item.fieldName]" :key="index">
            <view>{{ `${index + 1}、医嘱项目：${item.name}` }}</view>
            <view>{{ `标本：${item.sampleName}` }}</view>
            <view>{{ `单价：${item.price}` }}</view>
          </view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'inPatientOrder'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">
          <view v-for="(item, index) in form[item.fieldName]" :key="index">
            <view>{{ `${index + 1}、医嘱内容：${item.orderName}` }}</view>
            <view style="padding-left:26px">{{ `规格：${item.spec}` }}</view>
            <view style="padding-left:26px">{{
              `单位：${item.dosageUnitName}`
            }}</view>
          </view>
        </view>
      </view>
      <view
        class="form_row dis_flex"
        v-else-if="item.fieldType == 'workorderSetting'"
      >
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">{{ form[`${item.fieldName}_valName`] }}</view>
      </view>
      <!-- 请假统计 -->
      <view class="form_row" v-else-if="item.fieldType == 'leaveStatistics'">
        <view class="row_lable_table">
          <uni-table
            v-if="leaveStatisticsDataList.length"
            ref="leaveStatisticsTable"
            titleTextAlign="center"
            textAlign="center"
            :tableData="leaveStatisticsDataList"
            :columns="columns"
            :stickSide="false"
            style="width: auto;"
            :border="true"
          ></uni-table>
          <view v-else>暂无请假数据</view>
        </view>
      </view>
      <view class="form_row dis_flex" v-else-if="item.fieldType == 'signature'">
        <view class="row_lable">{{ item.showName }}</view>
        <view
          class="row_value"
          :class="{
            row_value_image: form[item.fieldName].includes(
              '/ts-basics-bottom/fileAttachment/downloadFile'
            )
          }"
        >
          <image
            v-if="
              form[item.fieldName].includes(
                '/ts-basics-bottom/fileAttachment/downloadFile'
              )
            "
            :src="
              form[item.fieldName]
                ? $config.BASE_HOST + form[item.fieldName]
                : ''
            "
            mode="aspectFit"
          ></image>
          <template v-else>{{ form[item.fieldName] }}</template>
        </view>
      </view>
      <view class="form_row dis_flex" v-else-if="item.fieldType != 'comment'">
        <view class="row_lable">{{ item.showName }}</view>
        <view class="row_value">{{ form[item.fieldName] }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import uniTable from '../uni-table/uni-table.vue';
import EssentialDrugDicSelect from './components/essential-drug-dic-select.vue';
import MedicalSupplieDicSelect from './components/medical-supplie-dic-select.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  name: 'FormCheck',
  components: {
    uniTable,
    EssentialDrugDicSelect,
    MedicalSupplieDicSelect
  },
  data() {
    return {
      leaveStatisticsDataList: [],
      columns: [],
      noData: false
    };
  },
  props: {
    formTemplate: {
      type: Array,
      default() {
        return [];
      }
    },
    formDatas: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  computed: {
    form() {
      return this.formDatas;
    }
  },
  filters: {
    sickKeyFilter(value) {
      let key = '';
      switch (value) {
        case 'name':
          key = '姓名';
          break;
        case 'sex':
          key = '性别';
          break;
        case 'phone':
          key = '电话';
          break;
        case 'idcard':
          key = '身份证';
          break;
      }
      return key;
    },
    formatDate(dateVal) {
      let date = new Date(dateVal),
        YY = date.getFullYear(),
        MM = String(date.getMonth() + 1).replace(/(^\d{1}$)/, '0$1'),
        DD = String(date.getDate()).replace(/(^\d{1}$)/, '0$1'),
        time = `${YY}年${MM}月${DD}日`;
      return time;
    }
  },
  watch: {
    formTemplate: {
      handler: function() {
        this.init();
      },
      immediate: true
    }
  },
  methods: {
    //初始化数据
    async init() {
      let _self = this;
      await Promise.all(
        _self.formTemplate.map(async (el, indexNum, arr) => {
          if (el.showName && el.fieldType === 'processChoose') {
            _self.$set(
              _self.form,
              el.fieldName,
              _self.form[el.fieldName]
                ? JSON.parse(_self.form[el.fieldName])
                : []
            );
          } else if (el.showName && el.fieldType === 'table') {
            let columns = el.optionValue.split(';'),
              columnsArr = columns.map(item => {
                return {
                  title: item,
                  key: item,
                  width: columns.length > 4 ? '300rpx' : ''
                };
              });
            el.columns = columnsArr;
            _self.$set(
              _self.form,
              el.fieldName,
              _self.form[el.fieldName]
                ? JSON.parse(_self.form[el.fieldName])
                : []
            );
          } else if (el.showName && el.fieldType === 'fileTemplate') {
            let list = await _self.getFiles(el.fileTemplate);
            list.forEach(one => {
              one.fileRealName = one.fileName;
              one.fileName = one.originalName;
              one.fileType = one.fileExtension;
            });
            _self.$set(_self.form, el.fieldName, list);
          } else if (el.showName && el.fieldType === 'interworkCom') {
            let comVal = _self.form[el.fieldName]
              ? JSON.parse(_self.form[el.fieldName])
              : [];
            _self.$set(
              _self.form,
              el.fieldName,
              comVal
                .map((item, index) => {
                  return `${index + 1}、${item.name}`;
                })
                .join('；')
            );
          } else if (el.showName && el.fieldType === 'interworkSick') {
            let sickVal = _self.form[el.fieldName]
                ? JSON.parse(_self.form[el.fieldName])
                : {
                    before: {},
                    after: {}
                  },
              afterSick = {},
              beforeSick = sickVal['before'];
            for (let i in sickVal['after']) {
              if (i != 'id' && sickVal['after'][i]) {
                afterSick[i] = sickVal['after'][i];
              }
            }
            _self.$set(_self.form, el.fieldName, afterSick);
            _self.$set(_self.form, `${el.fieldName}_valName`, beforeSick);
          } else if (el.showName && el.fieldType === 'interworkSettle') {
            let settleVal = _self.form[el.fieldName]
              ? JSON.parse(_self.form[el.fieldName])
              : {};
            _self.$set(_self.form, el.fieldName, {
              index: settleVal.zy || '',
              value: settleVal.finishDate
                ? `${_self.$options.filters.formatDate(
                    settleVal.finishDate
                  )}结算,应退${settleVal.recedeFee}元`
                : ''
            });
          } else if (el.showName && el.fieldType === 'interworkPay') {
            let payVal = _self.form[el.fieldName]
              ? JSON.parse(_self.form[el.fieldName])
              : [];
            _self.$set(
              _self.form,
              el.fieldName,
              payVal.map(i => {
                return {
                  index: i.name,
                  value: `${_self.$options.filters.formatDate(i.arriveDate)} ${
                    i.payModeName
                  } ${i.payValues}元`
                };
              })
            );
          } else if (
            el.showName &&
            (el.fieldType === 'interworkHosPro' ||
              el.fieldType === 'interworkTest' ||
              el.fieldType === 'inPatientOrder')
          ) {
            _self.$set(
              _self.form,
              el.fieldName,
              _self.form[el.fieldName]
                ? JSON.parse(_self.form[el.fieldName])
                : []
            );
          } else if (
            el.showName &&
            el.fieldType === 'select' &&
            el.dataSource == 6
          ) {
            let relationWorkflowVal = '';
            if (_self.form[el.fieldName]) {
              //获取关联流程数据
              let dataList = await _self.getDataList(el.relationWorkflowId),
                //获取关联字段
                fieldList = await _self.getFindByFieldId(el.keyId),
                dataVal = null;
              dataList.map(item => {
                if (_self.form[el.fieldName] == item.ID) {
                  dataVal = item;
                }
              });
              relationWorkflowVal = fieldList
                .map(fieldItem => {
                  return dataVal[fieldItem.valueFieldname];
                })
                .join('——');
            }
            _self.$set(_self.form, el.fieldName, relationWorkflowVal);
          } else if (el.showName && el.dataSource == 7) {
            let selectValList = [];
            if (_self.form[el.fieldName]) {
              let optionList = await _self.getSelectOptionList(
                  el.interfaceServices
                ),
                list = _self.form[el.fieldName].split(',');
              list.map(one => {
                let index = optionList.findIndex(i => {
                  return i.value == one;
                });
                if (index != -1) {
                  selectValList.push(optionList[index].text);
                }
              });
            }
            _self.$set(_self.form, el.fieldName, selectValList.join(','));
          } else if (el.showName && el.dataSource == 8) {
            let meauPermissionsList = await _self.getWorkOrderDatas(
              '/omMeau/meauPermissionsList'
            );
            meauPermissionsList.map(i => {
              if (i.deptId == _self.form[el.fieldName]) {
                _self.$set(_self.form, `${el.fieldName}_valName`, i.deptName);
              }
            });
          } else if (el.showName && el.dataSource == 9) {
            let param = arr.filter(i => {
              return i.dataSource == 8;
            });
            let faultTypeAllList = await _self.getWorkOrderDatas(
              `/faultType/getFaultTypeAllList/1/${
                _self.form[param[0]['fieldName']]
              }`
            );
            faultTypeAllList.map(i => {
              if (i.id == _self.form[el.fieldName]) {
                _self.$set(_self.form, `${el.fieldName}_valName`, i.name);
              }
            });
          } else if (el.fieldType === 'leaveStatistics') {
            this.getLeaveStatistics();
          }
        })
      );
    },
    async getLeaveStatistics() {
      let res = await this.ajax.getleaveStatisticsTableHeadCols({
        isWorkflow: 'Y'
      });
      let tableHeadList = [];
      let tableData = [];
      let year = new Date().getFullYear();
      if (res.success && res.object) {
        res.object.push({
          label: '合计',
          name: 'total',
          hidden: false
        });
        tableHeadList = res.object;
      } else {
        uni.showToast({
          title: `未查询到请假表头信息`,
          icon: 'none'
        });
        return;
      }
      let resData = await this.ajax.getleaveStatisticsDataList({
        isWorkflow: 'Y',
        startLeaveMonth: year + '-01',
        endLeaveMonth: year + '-12',
        employeeCode: this.formDatas.CREATE_USER
      });
      tableData = resData.rows || [];
      tableData.forEach(e => {
        let total = 0;
        for (let key in e) {
          if (typeof e[key] == 'number') {
            total += e[key];
          }
        }
        e.total = total;
      });
      let noData = false;
      tableHeadList.forEach(item => {
        if (!item.hidden) {
          let canShow = false;
          tableData.forEach(e => {
            if (typeof e[item.name] == 'number' && e[item.name] > 0) {
              canShow = true;
              noData = true;
            }
          });
          if (!canShow) {
            item.hidden = true;
          }
        }
      });
      this.columns = tableHeadList
        .filter(e => !e.hidden)
        .map(e => {
          return {
            title: e.label,
            key: e.name,
            width: '80px'
          };
        });
      this.leaveStatisticsDataList = tableData;
      this.noData = noData;
    },
    //获取附件
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax
        .getFiles({
          idsStr: fileIds
        })
        .then(res => {
          list = res.object;
        });
      return list;
    },
    //获取流程表单数据
    async getFindByFieldId(keyId) {
      let _self = this,
        relation = null;
      await _self.ajax.getDatasByFieldId(keyId).then(res => {
        relation = res.object.relation ? JSON.parse(res.object.relation) : [];
      });
      return relation;
    },
    //根据流程id获取自己发起已完结的流程流程数据
    async getDataList(workflowId) {
      let _self = this,
        datas = [];
      await _self.ajax.getMyselfDataListByWorkflowId(workflowId).then(res => {
        datas = res.object;
      });
      return datas;
    },
    //通过接口获取下拉选项
    async getSelectOptionList(api) {
      let _self = this,
        options = null;
      await _self.ajax.getSelectOptionList(api).then(res => {
        options = res.object || [];
        options = options.map(item => {
          return {
            text: item.itemName,
            value: item.itemValue
          };
        });
      });
      return options;
    },
    //获取工单相关数据
    async getWorkOrderDatas(api) {
      let _self = this,
        datas = [];
      await _self.ajax.getWorkOrderDatas(api).then(res => {
        datas = res.object;
      });
      return datas;
    },
    //查看附件详情
    previewFile(id, fileName) {
      let _self = this,
        filePath = `${_self.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        // _self.$downloadFile.downloadFile(filePath);
      }
    },
    downLoadFile(id, fileName) {
      let _self = this,
        filePath = `${_self.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      _self.$downloadFile.downloadFile(filePath);
    },
    connectionFile(id, fileName) {
      this.ajax.saveCollect({ collectId: id }).then(res => {
        uni.showToast({
          icon: 'none',
          title: res.message || '收藏成功,已收藏到个人文档'
        });
      });
    },
    checkProcessDetail(data) {
      let pagePramas = {
        isMobile: true,
        wfInstId: data.wfInstanceId,
        name: 'checkDetail'
      };
      uni.navigateTo({
        url: `/pages/workflow/my-workflow-detail?${this.$common.convertObj(
          pagePramas
        )}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dis_flex {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.row_tab {
  &:last-child {
    .form_row::after {
      height: 0;
    }
  }
  .form_row {
    position: relative;
    background-color: #ffffff;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 30rpx;
      right: 0;
      transform: scaleY(-0.5);
      height: 1px;
      background-color: #eee;
    }
    .row_title {
      color: #333;
      margin: 30rpx 30rpx 20rpx;
    }
    .row_lable_table {
      padding: 20rpx 30rpx;
      box-sizing: border-box;
      position: relative;
      color: #333;
      font-size: 28rpx;
      overflow-x: auto;
    }
    .row_lable {
      width: 200rpx;
      padding: 20rpx 30rpx;
      box-sizing: border-box;
      position: relative;
      color: #666;
      font-size: 28rpx;
      text-align: right;
    }
    .row_value {
      font-size: 28rpx;
      color: #333;
      box-sizing: border-box;
      text-align: left;
      flex: 1;
      padding: 20rpx 30rpx;
    }
    .row_lable ~ .row_value {
      padding-top: 0;
    }
    .row_value_image image {
      width: 100%;
      height: 100rpx;
    }
    &.dis_flex {
      .row_lable ~ .row_value {
        padding-top: 20rpx;
        padding-left: 30rpx;
      }
    }
    .remark.red-text {
      color: $uni-color-error !important;
    }
    .remark.bold-text {
      font-weight: bold !important;
    }
    .fiel_list {
      .file_item {
        text-decoration: none;
        color: $theme-color;
        font-size: 28rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        .oa-icon {
          font-size: 40rpx;
          margin-right: 10rpx;
        }
        text:nth-child(2) {
          flex: 1;
          overflow: hidden;
        }
        .preview-text,
        .download-text {
          flex-shrink: 0;
          margin-left: 8px;
        }
      }
    }
    .link {
      color: $theme-color;
      font-size: 28rpx;
    }
  }
}
</style>
