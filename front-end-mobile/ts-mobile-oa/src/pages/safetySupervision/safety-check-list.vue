<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="安全检查"></page-head>
    <view class="content-top">
      <view class="top-tab">
        <scroll-view
          class="swiper-head"
          :scroll-x="true"
          :show-scrollbar="false"
        >
          <view
            v-for="(tab, index) in tabBars"
            :key="index"
            class="uni-tab-item"
            :data-current="index"
            @click="ontabtap"
          >
            <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
              <text class="uni-tab-item-title">{{ tab.name }}</text>
              <text
                class="uni-tab-item-num"
                v-if="tab.total != null && tab.total != 0 && index != 2"
                >{{ tab.total >= 100 ? '99+' : tab.total }}</text
              >
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="search-sift" @click="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <swiper
      :current="tabIndex"
      class="swiper-box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="uni-list">
            <view
              class="uni-list-item"
              v-for="row in item['list']"
              :key="row.id"
              @click="jumpDetails(row)"
            >
              <view class="item-head">
                <text class="item-safety-title ellipsis">
                  {{ row.projectName }} - {{ row.routeName }}
                </text>
                <text class="item-check-date">{{ row.checkDate }}</text>
              </view>
              <view class="item-safety-address ellipsis">
                <text class="oa-icon oa-icon-didian"></text>
                {{ row.checkAddress }}
              </view>
              <view class="item-safety-text">
                <text class="item-safety-checkperson" v-if="item.handleStatus"
                  >检查人：{{ row.checkUserName }}</text
                >
                <text>核查人：{{ row.examineUserName }}</text>
              </view>
              <view class="item-safety-result" v-if="item.handleStatus">
                检查结果：<text
                  :class="
                    row.unqualifiedCount ? 'notUpToStandard' : 'upToStandard'
                  "
                  >{{
                    row.unqualifiedCount
                      ? `${row.unqualifiedCount}项未达标`
                      : '全部达标'
                  }}</text
                >
              </view>
              <view
                class="item-safety-text notUpToStandard"
                v-if="item.handleStatus == 2"
                >{{ row.remark }}</view
              >
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <uni-drawer :visible="showRight" mode="right" @close="closeDrawer('right')">
      <view class="drawer-option-tap">
        <view class="drawer-option-title">检查路线</view>
        <view class="drawer-option-list">
          <view class="drawer-option-item" @click="showPopup">
            <view class="ellipsis main-text">{{
              selectedRouterName.join('、')
            }}</view>
            <view
              style="margin-left: 2px;"
              v-if="selectedRouterName.length > 1"
              >{{ `等${selectedRouterName.length}个` }}</view
            >
          </view>
        </view>
      </view>
      <view class="drawer-option-tap">
        <view class="drawer-option-title">检查时间</view>
        <view class="drawer-option-list">
          <text
            class="drawer-option-item time-item"
            @tap="showPicker('range')"
            >{{ agentTime.beginDate }}</text
          >
          <text class="drawer-option-item divider">-</text>
          <text
            class="drawer-option-item time-item"
            @tap="showPicker('range')"
            >{{ agentTime.endDate }}</text
          >
        </view>
      </view>
      <view class="drawer-option-tap">
        <view class="drawer-option-title">所属项目</view>
        <view class="drawer-option-list">
          <view class="drawer-option-item" @tap="showPopup('project')">
            <view class="ellipsis main-text">{{ selectedProjectName }}</view>
          </view>
        </view>
      </view>
      <view class="drawer-btn-tap">
        <view class="drawer-btn-item cancle-btn" @tap="reset">重置</view>
        <view class="drawer-btn-item them-btn" @tap="submit">确定</view>
      </view>
    </uni-drawer>
    <uni-popup ref="routerPopup" type="bottom" style="z-index: 999;">
      <view class="scroll-list pop-list">
        <template v-if="popupSelectType == 'project'">
          <view
            class="pop-list-item"
            v-for="item in projectNameList"
            :key="item.itemName"
            @click="chooseProject(item)"
          >
            <text class="pop-list-item-text">{{ item.itemName }}</text>
            <view class="pop-selected-icon">
              <uni-icons
                v-if="selectedProjectName == item.itemName"
                type="checkmarkempty"
                color="#005BAC"
                size="44"
              />
            </view>
          </view>
        </template>
        <template v-else>
          <view
            class="pop-list-item"
            v-for="item in routerList"
            :key="item.id"
            @click="chooseRouter(item)"
          >
            <text class="pop-list-item-text">{{ item.routeName }}</text>
            <view class="pop-selected-icon">
              <uni-icons
                v-if="selectedRouterList.some(one => one.id == item.id)"
                type="checkmarkempty"
                color="#005BAC"
                size="44"
              />
            </view>
          </view>
        </template>
      </view>
      <view class="pop-button-list">
        <button
          class="pop-button-item"
          type="primary"
          data-ref="popup"
          @click="confirm"
        >
          确定
        </button>
      </view>
    </uni-popup>
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      pickerZindex="999"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import datePicker from '@/components/picker/date-picker.vue';
import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
export default {
  components: {
    mescroll,
    datePicker,
    uniDrawer
  },
  data() {
    return {
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '草稿',
          handleStatus: 0,
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        },
        {
          name: '待确认',
          handleStatus: 1, //办理状态
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          list: [],
          total: 0
        },
        {
          name: '不通过',
          handleStatus: 2,
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        },
        {
          name: '整改中',
          handleStatus: 3,
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        },
        {
          name: '已完成',
          handleStatus: 4,
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        }
      ],
      showRight: false,
      agentTime: {
        beginDate: '',
        endDate: ''
      },
      routerList: [],
      selectedRouterList: [],
      selectedRouterId: [],
      selectedRouterName: [],
      projectNameList: [], // 所属项目 选项列表
      selectedProjectName: '', // 已选 所属项目 值
      popupSelectType: null
    };
  },
  computed: {
    agentTimeArr: function() {
      return [this.agentTime.beginDate, this.agentTime.endDate];
    }
  },
  onLoad(opt) {
    if (opt.index) this.tabIndex = Number(opt.index);
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.getRouterList();
    this.getProjectNameData();
  },
  methods: {
    getRouterList() {
      this.ajax
        .getSafetyRouteList({
          pageNo: 1,
          pageSize: 1000
        })
        .then(res => {
          this.routerList = this.routerList.concat(res.rows);
        });
    },
    getProjectNameData() {
      this.ajax.getDataByDataLibrary('PATROL_PROJECT').then(res => {
        this.projectNameList = res.object;
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.showRight = !this.showRight;
    },
    showDrawer() {
      this.showRight = true;
    },
    closeDrawer() {
      this.showRight = false;
    },
    showPopup(type) {
      this.popupSelectType = type;
      this.$refs['routerPopup'].open();
    },
    chooseRouter(item) {
      let index = this.selectedRouterList.findIndex(i => i.id === item.id);
      if (index != -1) {
        this.selectedRouterList.splice(index, 1);
      } else {
        this.selectedRouterList.push({
          id: item.id,
          name: item.routeName
        });
      }
    },
    chooseProject(item = {}) {
      if (this.selectedProjectName == item.itemName) {
        this.selectedProjectName = '';
        return;
      }
      this.selectedProjectName = item.itemName;
    },
    //多选确定
    confirm() {
      let idArr = [],
        nameArr = [];
      this.selectedRouterList.forEach(item => {
        idArr.push(item.id);
        nameArr.push(item.name);
      });
      this.selectedRouterId = JSON.parse(JSON.stringify(idArr));
      this.selectedRouterName = JSON.parse(JSON.stringify(nameArr));
      this.$nextTick(() => {
        this.$refs['routerPopup'].close();
      });
    },
    //显示时间弹出层
    showPicker(dateRef) {
      this.$refs[dateRef].show();
    },
    //时间选择确认
    onConfirm(res) {
      this.agentTime.beginDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      this.agentTime.endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
    },
    //时间取消
    onCancel() {},
    //重置
    reset() {
      this.agentTime.beginDate = '';
      this.agentTime.endDate = '';
      this.selectedRouterList = [];
      this.selectedRouterId = [];
      this.selectedRouterName = [];
      this.selectedProjectName = '';
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.tabBars[this.tabIndex]['isInit'] = true;
      this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
    },
    //确定
    submit() {
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.tabBars[this.tabIndex]['isInit'] = true;
      this.$nextTick(() => {
        this.closeDrawer();
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    async ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    //根据类型获取数据
    async getListData(page, successCallback, errorCallback, keywords, index) {
      await this.ajax
        .getSafetyList({
          status: this.tabBars[index]['handleStatus'],
          pageSize: page.size,
          pageNo: page.num,
          startDate: this.agentTime.beginDate,
          endDate: this.agentTime.endDate,
          checkRoutes: this.selectedRouterId.join(','),
          projectName: this.selectedProjectName,
          sidx: 'create_date',
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      rows.forEach(item => {
        this.tabBars[index]['list'].push(item);
      });
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    jumpDetails(row) {
      uni.navigateTo({
        url: `/pages/safetySupervision/safety-check-details?index=${this.tabIndex}&id=${row.id}`
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.content-top {
  display: flex;
  flex-direction: row;
  position: relative;
  &::before,
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &::before {
    top: 0;
  }
}
.top-tab {
  flex: 1;
}
.search-sift {
  font-size: 28rpx;
  color: #666666;
  padding: 0 16rpx;
  line-height: 76rpx;
  background-color: #ffffff;
}
.search-sift-icon {
  font-size: 36rpx;
  color: #666666;
}
.swiper-head {
  position: relative;
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  /* flex-wrap: nowrap; */
  /* border-color: #cccccc;
		border-bottom-style: solid;
		border-bottom-width: 1px; */
}
.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  width: 20%;
  box-sizing: border-box;
  text-align: center;
}
.uni-tab-item-title,
.uni-tab-item-num {
  color: #555;
  font-size: 30rpx;
  height: 80rpx;
  line-height: 76rpx;
  flex-wrap: nowrap;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}
.uni-tab-item-num {
  font-size: 28rpx;
  color: #fff;
  background-color: #f59a23;
  border-radius: 40rpx;
  padding: 0 10rpx;
  margin: 0 10rpx;
}
.uni-tab-item-title-active {
  color: $theme-color;
  border-bottom: 2px solid $theme-color;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  flex: 1;
  flex-direction: row;
}
.uni-list-item {
  position: relative;
  padding: 20rpx 30rpx;
}
.uni-list-item:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: $uni-border-color;
}
.item-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-safety-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}
.item-check-date {
  font-size: 24rpx;
  color: #999;
}
.item-safety-address {
  width: 100%;
  color: #666666;
  font-size: 28rpx;
}
.item-safety-text {
  font-size: 28rpx;
  color: #666666;
}
.item-safety-checkperson {
  margin-right: 30rpx;
}
.item-safety-result {
  color: #333333;
  font-size: 28rpx;
}
.upToStandard {
  color: $uni-color-success;
}
.notUpToStandard {
  color: $uni-color-error;
}
.drawer-option-tap {
  padding: 0 20rpx;
}
.drawer-option-title {
  height: 56rpx;
  line-height: 56rpx;
  font-size: 28rpx;
  margin: 10rpx 0;
  color: #666;
}
.drawer-option-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.drawer-option-item {
  line-height: 70rpx;
  height: 70rpx;
  width: 100%;
  text-align: center;
  background: #ffffff;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #333333;
  padding: 0 20rpx;
  border: 0;
  border-radius: 40rpx;
  background-color: #f2f2f2;
}
.main-text {
  flex: 1;
  text-align: left;
}
.time-item {
  width: 40%;
}
.divider {
  width: auto;
  border: 0;
  display: inline-block;
  box-sizing: border-box;
  background-color: transparent;
}
.drawer-btn-tap {
  margin: 80rpx 0;
  padding: 0 20rpx;
  text-align: right;
}
.drawer-btn-item {
  border-radius: 8rpx;
  line-height: 70rpx;
  height: 70rpx;
  width: 30%;
  text-align: center;
  background: #ffffff;
  display: inline-block;
  font-size: 28rpx;
  border: 1px solid #ddd;
  border-right: 0;
  box-sizing: border-box;
  color: #333333;
}
.cancle-btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.them-btn {
  border-left: 0;
  border: 1px solid #005bac;
  background-color: #005bac;
  color: #ffffff;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  margin-right: 4%;
}
.scroll-list {
  max-height: 800rpx;
  overflow: auto;
}
.pop-list-item {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333333;
  position: relative;
}
.pop-list-item::after {
  position: absolute;
  z-index: 10;
  right: 0;
  left: 30rpx;
  bottom: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #eeeeee;
}
.pop-list-item:last-child::after {
  height: 0;
}
.pop-list-item-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.pop-selected-icon {
  line-height: 1;
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
}
.pop-button-list {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #cccccc;
  z-index: 10;
}
.pop-button-item {
  font-size: 28rpx;
  margin: 0 20rpx;
  width: 160rpx;
}
</style>
