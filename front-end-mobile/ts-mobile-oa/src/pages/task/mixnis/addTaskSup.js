import common from '@/common/js/common.js';
export default {
  data() {
    return {
      formList: [
        {
          title: '督办事项',
          prop: 'registerMatter',
          type: 'text',
          placeholder: '请填写督办事项',
          maxlength: 50,
          required: true
        },
        {
          title: '类型',
          prop: 'registerTypeName',
          propVal: 'registerType',
          type: 'select',
          mode: 'select',
          optionList: [],
          placeholder: '请选择任务类型',
          required: true
        },
        {
          title: '紧急程度',
          prop: 'registerUrgency',
          type: 'radio',
          radioList: [
            {
              label: '普通',
              value: 1
            },
            {
              label: '紧急',
              value: 2
            },
            {
              label: '非常紧急',
              value: 3
            }
          ],
          required: true
        },
        {
          title: '完成时间',
          prop: 'completeDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择计划完成时间',
          required: true
        },
        {
          title: '预警时间(天)',
          prop: 'overdueDay',
          type: 'number',
          placeholder: '请输入预警时间'
        },
        {
          title: '描述',
          prop: 'registerRemark',
          type: 'textarea',
          placeholder: '请填写描述',
          maxlength: 500,
          required: true
        },
        {
          title: '附件',
          prop: 'registerFiles',
          propVal: 'registerFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件',
          actionStatus: '9'
        }
      ],
      formListTaskSup: [
        {
          title: '承办人',
          prop: 'taskName',
          propVal: 'taskCode',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-person-callback',
          placeholder: '请选择承办人',
          required: true
        },
        {
          title: '承办事项',
          prop: 'taskRemark',
          type: 'textarea',
          placeholder: '请填写承办事项',
          maxlength: 200
        },
        {
          title: '抄送给分管领导',
          prop: 'copyLeader',
          type: 'radio',
          radioList: [
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ],
          required: true
        }
      ],
      formListBottom: [
        {
          title: '协办人',
          prop: 'assistName',
          propVal: 'assistCode',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-person-callback',
          placeholder: '请选择协办人'
        },
        {
          title: '验收人',
          prop: 'checkName',
          propVal: 'checkCode',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-person-callback',
          placeholder: '请选择验收人',
          required: true
        },
        {
          title: '批示人',
          prop: 'approveName',
          propVal: 'approveCode',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-person-callback',
          placeholder: '请选择协办人'
        },
        {
          title: '指派日期',
          prop: 'taskDate',
          type: 'select',
          mode: 'time',
          format: 'YYYY-MM-DD',
          placeholder: '请选择指派日期',
          required: true
        },
        {
          title: '指派说明',
          prop: 'taskDesc',
          type: 'textarea',
          placeholder: '请填写指派说明',
          maxlength: 500
        },
        {
          title: '指派附件',
          prop: 'taskFile',
          propVal: 'taskFileList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件'
        }
      ],
      form: {
        registerMatter: '',
        registerRemark: '',
        registerType: '',
        registerTypeName: '',
        registerUrgency: '1',
        completeDate: '',
        overdueDay: 1,
        registerFiles: common.guid(),
        registerFilesList: [],
        superviseTaskList: [
          {
            taskCode: '',
            taskName: '',
            taskRemark: '',
            copyLeader: 1
          }
        ],
        assistName: '',
        assistCode: '',
        checkName: '',
        checkCode: '',
        approveName: '',
        approveCode: '',
        taskDesc: '',
        taskDate: '',
        taskFile: common.guid(),
        taskFileList: []
      },
      rules: {
        registerMatter: [
          {
            required: true,
            message: '请填写督办事项',
            trigger: ''
          }
        ],
        registerRemark: [
          {
            required: true,
            message: '请填写描述',
            trigger: ''
          }
        ],
        registerTypeName: [
          {
            required: true,
            message: '请选择任务类型',
            trigger: ''
          }
        ],
        completeDate: [
          {
            required: true,
            message: '请选择计划完成时间',
            trigger: ''
          }
        ],
        checkCode: [
          {
            required: true,
            message: '请选择验收人',
            trigger: ''
          }
        ],
        taskDate: [
          {
            required: true,
            message: '请选择指派日期',
            trigger: ''
          }
        ],
        taskName: [
          {
            required: true,
            message: '请选择承办人',
            trigger: ''
          }
        ]
      }
    };
  }
};
