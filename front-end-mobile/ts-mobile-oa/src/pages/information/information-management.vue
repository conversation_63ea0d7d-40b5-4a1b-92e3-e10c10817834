<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="信息管理"></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          cancelButton="none"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          @confirm="search"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact_list">
          <view
            class="contact_item"
            v-for="item in dataList"
            :key="item.id"
            @tap.stop="chooseItem(item.id)"
          >
            <view
              class="contact_item_img"
              :data-c="item.initColor"
              :style="{ 'background-color': item.initColor }"
            >
              {{ item.channelName.substring(item.channelName.length - 2) }}
            </view>
            <view class="contact_item_info">
              <view class="contact_item_row">
                <text class="contact_item_top" v-if="item.showSign === '1'"
                  >[顶]</text
                >
                <text
                  class="contact_item_quintessence"
                  v-if="item.isMarrow === '1'"
                  >[精]</text
                >
                <text
                  class="contact_item_title"
                  :class="[
                    item.titleColor === '1' ? 'titleRed' : '',
                    item.informationStatus === '1' ? '' : 'unpublish'
                  ]"
                >
                  {{
                    item.informationTitle
                      ? item.informationTitle.replace(
                          / &gt; \/?[^>]* &gt; /g,
                          ''
                        )
                      : '无主题'
                  }}
                </text>
                <text class="contact_item_read">
                  浏览 {{ item.informationKits }}
                </text>
              </view>
              <!-- <view class="contact_item_row">
                <rich-text
                  class="contact_item_content"
                  :nodes="
                    item.informationContent
                      ? item.informationContent.replace(/<\/?[^>]*>/g, '')
                      : '此信息没有文字内容'
                  "
                ></rich-text>
              </view> -->
              <view class="contact_item_row">
                <text class="contact_item_user">{{
                  item.createDeptName + ' ' + item.createUserName
                }}</text>
                <text class="contact_item_time">{{
                  item.releaseDate.substring(0, 16)
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
    <uni-drawer :visible="showRight" mode="right" @close="closeDrawer('right')">
      <view class="option-tap">
        <view class="option-title">栏目</view>
        <view class="option-list">
          <text
            class="option-item"
            v-for="item in infoTypeList"
            :key="item.channelId"
            :class="channelArr.includes(item.channelId) ? 'selected' : ''"
            :data-channel-id="item.channelId"
            @tap="chooseChannel"
          >
            {{ item.channelName }}
          </text>
        </view>
      </view>
      <!-- <view class="option-tap">
				<view class="option-title">范围</view>
				<view class="option-list">
					<text class="option-item" :class="messageReadType == item.rangId ? 'selected' : ''" :data-range-id="item.rangId" v-for="item in infoRangeList" @tap="chooseRange">{{item.rangName}}</text>
				</view>
			</view> -->
      <!-- <view class="option-tap">
				<view class="option-title">状态</view>
				<view class="option-list">
					<text class="option-item" :class="isRead == item.statuId ? 'selected' : ''" :data-statu-id="item.statuId" v-for="item in infoStatusList" @tap="chooseStatus">{{item.statuName}}</text>
				</view>
			</view> -->
      <view class="option-tap">
        <view class="option-title">发布科室</view>
        <view class="option-list">
          <view class="dept-item" v-for="item in deptList" :key="item.id">
            <view class="dept-item-wrap">
              <text class="dept-item-text">{{ item.name }}</text>
              <uni-icons
                :size="30"
                class="uni-icon-wrapper"
                color="#999"
                type="closeempty"
                @tap="deletDept(item.id)"
              />
            </view>
          </view>
          <text class="dept-item" @tap="chooseDept">+</text>
        </view>
      </view>
      <view class="option-tap">
        <view class="option-title">发布时间</view>
        <view class="option-list">
          <text class="option-item time-item" @tap="showPicker('range')">{{
            beginDate
          }}</text>
          <text class="option-item divider">-</text>
          <text class="option-item time-item" @tap="showPicker('range')">{{
            endDate
          }}</text>
        </view>
      </view>
      <view class="btn-tap">
        <view class="btn-item cancle-btn" @tap="reset">重置</view>
        <view class="btn-item them-btn" @tap="submit">确定</view>
      </view>
    </uni-drawer>
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      pickerZindex="999"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import datePicker from '@/components/picker/date-picker.vue';
import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
export default {
  components: {
    mescroll,
    datePicker,
    uniDrawer
  },
  data() {
    return {
      infoRangeList: [
        {
          rangName: '全部',
          rangId: ''
        },
        {
          rangName: '我的收藏',
          rangId: 'collect'
        }
      ],
      infoStatusList: [
        {
          statuName: '全部',
          statuId: ''
        },
        {
          statuName: '未读',
          statuId: 'no'
        },
        {
          statuName: '已读',
          statuId: 'yes'
        }
      ],
      deptList: [],
      infoTypeList: [
        {
          channelName: '全部',
          channelId: ''
        }
      ],
      dataList: [],
      itemColor: [
        '#005BAC',
        '#8080ff',
        '#1ab785',
        '#ff7f65',
        '#da70d6',
        '#639ef6',
        '#2cb8c4'
      ],
      showRight: false,
      agentTimeArr: [],
      channelArr: [''],
      keywords: '',
      messageReadType: '',
      isRead: '',
      deptCode: '',
      channelId: '',
      beginDate: '',
      endDate: ''
    };
  },
  onLoad() {
    this.getInfoCountChannel();
  },
  methods: {
    //获取信息类型
    getInfoCountChannel() {
      this.ajax
        .getInfoCountByChannel({
          index: 3,
          informationStatus: 1
        })
        .then(res => {
          this.infoTypeList = this.infoTypeList.concat(res.object);
        });
    },
    //获取数据
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getInformationList({
          index: 3,
          sidx: 'releaseDate',
          sord: 'desc',
          pageSize: page.size,
          pageNo: page.num,
          informationTitle: this.keywords,
          messageReadType: this.messageReadType,
          channelId: this.channelId,
          isRead: this.isRead,
          deptCode: this.deptCode,
          beginDate: this.beginDate,
          endDate: this.endDate
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      rows.forEach(item => {
        this.infoTypeList.forEach((one, index) => {
          if (one.channelName == item.channelName) {
            item.initColor = this.itemColor[index - 1];
          }
        });
      });
      this.dataList = this.dataList.concat(rows);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(id) {
      uni.navigateTo({
        url: `/pages/information/information-details?informationId=${id}&fromPage=informationManagement`
      });
    },
    //搜索
    search(res) {
      this.keywords = res.value;
      this.datasInit();
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.showRight = !this.showRight;
    },
    showDrawer() {
      this.showRight = true;
    },
    closeDrawer() {
      this.showRight = false;
    },
    chooseChannel(e) {
      let data = e.currentTarget.dataset,
        isInclude = this.channelArr.includes(data.channelId);
      if (
        data.channelId == '' ||
        (data.channelId && this.channelArr.indexOf('') > -1)
      ) {
        this.channelArr = [];
        this.channelArr.push(data.channelId);
      } else {
        if (isInclude) {
          this.channelArr.splice(
            this.channelArr.findIndex(item => item === data.channelId),
            1
          );
        } else {
          this.channelArr.push(data.channelId);
        }
      }
      if (this.channelArr.indexOf('') > -1) {
        this.channelId = '';
      } else {
        this.channelId = this.channelArr.join(',');
      }
    },
    chooseRange(e) {
      let data = e.currentTarget.dataset;
      this.messageReadType = data.rangeId;
    },
    chooseStatus(e) {
      let data = e.currentTarget.dataset;
      this.isRead = data.statuId;
    },
    chooseDept() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('deptlist', res => {
        this.deptCode = res
          .map(item => {
            return item.id;
          })
          .join(',');
        this.deptList = res;
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('deptlist');
      });
      uni.setStorageSync('dept_list', JSON.stringify(this.deptList));
      uni.navigateTo({
        url: '/pages/selectDept/select-dept?checkType=checkBox'
      });
    },
    deletDept(id) {
      this.deptList = this.deptList.filter(item => item.id != id);
      this.deptCode = this.deptList
        .map(item => {
          return item.id;
        })
        .join(',');
    },
    //显示时间弹出层
    showPicker(dateRef) {
      this.$refs[dateRef].show();
    },
    //时间选择确认
    onConfirm(res) {
      this.agentTimeArr = [
        `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`,
        `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`
      ];
      this.beginDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      this.endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
    },
    //时间取消
    onCancel() {},
    reset() {
      this.channelArr = [''];
      this.channelId = '';
      this.messageReadType = '';
      this.isRead = '';
      this.deptCode = '';
      this.deptList = [];
      this.agentTimeArr = [];
      this.beginDate = '';
      this.endDate = '';
    },
    submit() {
      this.datasInit();
      this.$nextTick(() => {
        this.closeDrawer();
        this.$refs['mescroll'].downCallback();
      });
    },
    //返回
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 36rpx !important;
        color: #666666;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .scroll_list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      border-radius: 100%;
      text-align: center;
      line-height: 80rpx;
      font-size: 28rpx;
      background-color: #005bac;
      color: #fff;
    }
    .contact_item_info {
      flex: 1;
      .contact_item_row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
    }
    .contact_item_top {
      color: #f59a23;
      font-size: 28rpx;
    }
    .contact_item_quintessence {
      color: #3aad73;
      font-size: 28rpx;
    }
    .titleRed {
      color: #dd1f36 !important;
    }
    .unpublish {
      color: #999 !important;
    }
    .contact_item_title {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      padding-right: 20rpx;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .contact_item_read {
      font-size: 24rpx;
      color: #999;
    }
    .contact_item_content {
      font-size: 28rpx;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .contact_item_user {
      flex: 1;
    }
    .contact_item_user,
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
    }
    .contact_item_text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
    }
    .contact_item_icon {
      line-height: 1;
    }
  }
  .option-tap {
    padding: 0 20rpx;
    .option-title {
      height: 56rpx;
      line-height: 56rpx;
      font-size: 28rpx;
      margin: 10rpx 0;
      color: #666;
    }
    .option-item {
      border-radius: 8rpx;
      line-height: 70rpx;
      height: 70rpx;
      width: 30%;
      margin: 0 3% 20rpx 0;
      text-align: center;
      background: #ffffff;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      border: 1px solid #ddd;
      box-sizing: border-box;
      color: #333333;
    }
    .selected {
      background-color: #005bac;
      border-color: #005bac;
      color: #ffffff;
    }
    .dept-item {
      background-color: #f2f2f2;
      border-radius: 8rpx;
      line-height: 70rpx;
      height: 70rpx;
      width: 30%;
      margin: 0 3% 20rpx 0;
      text-align: center;
      display: inline-block;
      font-size: 28rpx;
      border: 1px solid #ddd;
      box-sizing: border-box;
      color: #333333;
      .dept-item-wrap {
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 0 2px;
        align-items: center;
        height: 100%;
        font-size: 28rpx;
        .dept-item-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .time-item {
      border: 0;
      width: 40%;
      border-radius: 40rpx;
      background-color: #f2f2f2;
    }
    .divider {
      width: auto;
      border: 0;
      box-sizing: border-box;
    }
  }
  .btn-tap {
    margin-top: 80rpx;
    padding: 0 20rpx;
    text-align: right;
    .btn-item {
      border-radius: 8rpx;
      line-height: 70rpx;
      height: 70rpx;
      width: 30%;
      text-align: center;
      background: #ffffff;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      border: 1px solid #ddd;
      border-right: 0;
      box-sizing: border-box;
      color: #333333;
    }
    .cancle-btn {
      border-bottom-right-radius: 0;
      border-top-right-radius: 0;
    }
    .them-btn {
      border-left: 0;
      border: 1px solid #005bac;
      background-color: #005bac;
      color: #ffffff;
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
      margin-right: 4%;
    }
  }
}
</style>
