<template>
  <view class="ts-content">
    <page-head
      @clickLeft="returnBack"
      title="新建日程"
      rightText="提交"
      @clickRight="submit"
    />
    <view class="schedule_content row_group" v-if="showContent">
      <view class="row dis_flex">
        <view class="row_lable">
          <text class="row_lable_text">类型</text>
        </view>
        <view
          class="row_value row_value_input"
          data-tap="scheduleType"
          data-ref="popup"
          data-popup-type="bottom"
          data-popup-choice="radio"
          :data-datas-list="JSON.stringify(scheduleTypeList)"
          @tap="showPopup"
        >
          <input
            class="row_value_input_text"
            disabled="true"
            :value="schedule.scheduleType"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="row dis_flex">
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="row_lable_text">主题</text>
        </view>
        <view class="row_value row_value_input">
          <input
            class="row_value_input_text"
            v-model="schedule.scheduleSubject"
            placeholder="请输入日程主题"
          />
        </view>
      </view>
      <!-- <view class='row dis_flex'>
				<view class='row_lable'>
					<text class='row_lable_text'>全天</text>
				</view>
				<view class='row_value row_value_switch'>
					<switch class='row_value_switch_text' :checked='switchVal' @change='onSwitchChange' />
				</view>
			</view> -->
      <view class="row dis_flex">
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="row_lable_text">日期</text>
        </view>
        <view
          class="row_value row_value_input"
          data-ref="date"
          data-tap="scheduleDate"
          data-mode="date"
          data-fields="day"
          :data-value="schedule.scheduleDate"
          :data-start-date="currentDate"
          @tap="showPicker"
        >
          <input
            class="row_value_input_text"
            disabled="true"
            placeholder="请选择日期"
            :value="schedule.scheduleDate"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="row dis_flex" v-if="!switchVal">
        <view class="row_item dis_flex">
          <view class="row_lable row_lable_time">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text class="row_lable_text">开始</text>
          </view>
          <view
            class="row_value row_value_input"
            data-ref="date"
            data-tap="scheduleStartTime"
            data-mode="time"
            :data-value="schedule.scheduleStartTime"
            @tap="showPicker"
          >
            <input
              class="row_value_input_text"
              disabled="true"
              placeholder="请选择开始时间"
              :value="schedule.scheduleStartTime"
            />
          </view>
        </view>
        <uni-icons
          :size="30"
          class="uni-icon-wrapper"
          color="#bbb"
          type="arrowright"
        />
        <view class="row_item dis_flex">
          <view class="row_lable row_lable_time">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text class="row_lable_text">结束</text>
          </view>
          <view
            class="row_value row_value_input"
            data-ref="date"
            data-tap="scheduleEndTime"
            data-mode="time"
            :data-value="schedule.scheduleEndTime"
            @tap="showPicker"
          >
            <input
              class="row_value_input_text"
              disabled="true"
              placeholder="请选择结束时间"
              :value="schedule.scheduleEndTime"
            />
          </view>
        </view>
      </view>
      <view class="row dis_flex">
        <view class="row_lable">
          <text class="row_lable_text">提醒</text>
        </view>
        <view
          class="row_value row_value_input"
          data-tap="remindType"
          data-ref="popup"
          data-popup-type="bottom"
          data-popup-choice="radio"
          :data-datas-list="JSON.stringify(remindTypeList)"
          @tap="showPopup"
        >
          <input
            class="row_value_input_text"
            disabled="true"
            :value="schedule.remindType"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="row dis_flex" v-if="canRepat">
        <view class="row_lable">
          <text class="row_lable_text">是否重复</text>
        </view>
        <view
          class="row_value row_value_input"
          data-tap="repeatStatus"
          data-ref="popup"
          data-popup-type="bottom"
          data-popup-choice="radio"
          :data-datas-list="JSON.stringify(repeatStatusList)"
          @tap="showPopup"
        >
          <input
            class="row_value_input_text"
            disabled="true"
            :value="schedule.repeatStatusShow"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view
        class="row dis_flex"
        v-if="schedule.repeatStatus != '0' && canRepat"
      >
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="row_lable_text">截止日期</text>
        </view>
        <view
          class="row_value row_value_input"
          data-ref="date"
          data-tap="repeatEndTime"
          data-mode="date"
          data-fields="day"
          :data-value="schedule.repeatEndTime"
          :data-start-date="currentDate"
          @tap="showPicker"
        >
          <input
            class="row_value_input_text"
            disabled="true"
            placeholder="请选择日期"
            :value="schedule.repeatEndTime"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="row">
        <view class="row_lable">
          <text class="row_lable_text">内容</text>
        </view>
        <view class="row_value row_value_textarea">
          <textarea
            class="row_value_textarea_text"
            placeholder="请输入日程内容"
            auto-height
            v-model="schedule.scheduleContent"
          />
        </view>
      </view>
      <view class="row dis_flex">
        <view class="row_lable">
          <text class="row_lable_text">地点</text>
        </view>
        <view class="row_value row_value_input">
          <input
            class="row_value_input_text"
            v-model="schedule.scheduleAddress"
            placeholder="请输入日程地点"
          />
        </view>
      </view>
    </view>
    <date-picker
      ref="date"
      :mode="picker.mode"
      :second="false"
      :value="picker.value"
      :startDate="picker.startDate"
      endDate="2100-12-31 23:59"
      @confirm="onConfirm"
      :fields="picker.fields"
    />
    <uni-popup :type="picker.popupType" top-height="44px" ref="showpopup">
      <view class="contact_list">
        <view
          class="contact_item"
          v-for="item in picker.list"
          :key="item.value"
          @tap="singleColumn(item)"
        >
          <text class="contact_item_text">{{ item.text }}</text>
          <view class="contact_item_icon">
            <uni-icons
              v-if="
                picker.valueObj != null && picker.valueObj.value === item.value
              "
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import datePicker from '@/components/picker/date-picker.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import graceChecker from '@/common/js/graceChecker.js';
export default {
  components: {
    datePicker,
    uniPopup
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      currentDate: '',
      type: 0, //类型 0：新建；1：修改
      id: '',
      switchVal: false,
      schedule: {
        scheduleType: '工作日程',
        scheduleSubject: '',
        scheduleDate: '',
        scheduleStartTime: '',
        scheduleEndTime: '',
        remindType: '不提醒',
        isRemind: 0,
        remindTime: 0,
        scheduleContent: '',
        scheduleAddress: '',
        repeatStatus: '0',
        repeatStatusShow: '不重复',
        repeatEndTime: ''
      },
      picker: {},
      scheduleTypeList: [
        {
          text: '工作日程',
          vlaue: 0
        },
        {
          text: '会议日程',
          value: 1
        }
      ],
      remindTypeList: [
        {
          text: '不提醒',
          type: 0,
          value: 0
        },
        {
          text: '提前15分钟',
          type: 1,
          value: 15
        },
        {
          text: '提前30分钟',
          type: 1,
          value: 30
        },
        {
          text: '提前1小时',
          type: 1,
          value: 60
        },
        {
          text: '提前2小时',
          type: 1,
          value: 120
        },
        {
          text: '提前1天',
          type: 1,
          value: 1440
        }
      ],
      repeatStatusList: [
        {
          text: '不重复',
          type: 0,
          value: 0
        },
        {
          text: '每天重复',
          type: 1,
          value: 1
        },
        {
          text: '每周重复',
          type: 2,
          value: 2
        },
        {
          text: '每月重复',
          type: 3,
          value: 3
        }
      ],
      canRepat: false,
      selectValue: {
        scheduleType: {
          text: '工作日程',
          vlaue: 0
        },
        remindType: {
          text: '不提醒',
          type: 0,
          value: 0
        }
      }
    };
  },
  onLoad(opt) {
    let _self = this;
    _self.fromPage = opt.fromPage;
    if (opt.id != null) {
      _self.type = 1;
      _self.id = opt.id;
      _self.getDatas(opt.id);
      _self.canRepat = false;
    } else {
      let current = this.$common.getDate('date');
      this.currentDate = current.timeStr;
      this.$set(this.schedule, 'scheduleDate', opt.dateStr);
      _self.showContent = true;
      _self.canRepat = true;
    }
  },
  methods: {
    //获取详情
    getDatas(id) {
      let _self = this;
      _self.ajax
        .getScheduleDetails({
          id: id
        })
        .then(res => {
          let data = res.object;
          _self.selectValue.scheduleType =
            data.scheduleType === '工作日程'
              ? { text: '工作日程', vlaue: 0 }
              : { text: '会议日程', vlaue: 1 };
          _self.schedule.scheduleType = data.scheduleType;
          _self.schedule.scheduleSubject = data.scheduleSubject;
          _self.schedule.scheduleDate = data.scheduleDate;
          _self.schedule.scheduleStartTime = data.scheduleStartTime;
          _self.schedule.scheduleEndTime = data.scheduleEndTime;
          let remind = _self.getremind(Number(data.remindTime));
          _self.schedule.remindType =
            data.isRemind && data.isRemind === 1
              ? `提前${remind.str}`
              : '不提醒';
          _self.selectValue.remindType = remind.obj;
          _self.schedule.isRemind = data.isRemind;
          _self.schedule.remindTime = Number(data.remindTime);
          _self.schedule.scheduleContent = data.scheduleContent;
          _self.schedule.scheduleAddress = data.scheduleAddress;
          _self.schedule.id = data.id;
          _self.showContent = true;
        });
    },
    getremind(value) {
      let val = {};
      switch (value) {
        case 0:
          val.str = '';
          val.obj = {
            text: '不提醒',
            type: 0,
            value: 0
          };
        case 15:
          val.str = '15分钟';
          val.obj = {
            text: '提前15分钟',
            type: 1,
            value: 15
          };
          break;
        case 30:
          val.str = '30分钟';
          val.obj = {
            text: '提前30分钟',
            type: 1,
            value: 30
          };
          break;
        case 60:
          val.str = '1小时';
          val.obj = {
            text: '提前1小时',
            type: 1,
            value: 60
          };
          break;
        case 120:
          val.str = '2分钟';
          val.obj = {
            text: '提前2小时',
            type: 1,
            value: 120
          };
          break;
        case 1440:
          val.str = '一天';
          val.obj = {
            text: '提前1天',
            type: 1,
            value: 1440
          };
          break;
      }
      return val;
    },
    //启动开关
    onSwitchChange(event) {
      let _self = this;
      _self.switchVal = event.detail.value;
      if (_self.switchVal) {
        _self.schedule.scheduleStartTime = '00:00';
        _self.schedule.scheduleEndTime = '23:59';
      } else {
        _self.schedule.scheduleStartTime = '';
        _self.schedule.scheduleEndTime = '';
      }
    },
    //显示时间选择弹出层
    showPicker(e) {
      let _self = this;
      let data = e.currentTarget.dataset;
      _self.picker = {
        startDate: data.startDate ? data.startDate : '',
        mode: data.mode,
        fields: data.fields ? data.fields : '',
        tap: data.tap,
        value: data.value
      };
      _self.$nextTick(function() {
        _self.$refs[data.ref].show();
      });
    },
    //时间选择确认
    onConfirm(res) {
      let _self = this;
      if (
        _self.picker.tap === 'scheduleStartTime' &&
        _self.schedule.scheduleEndTime &&
        _self.schedule.scheduleEndTime < res.result
      ) {
        _self.schedule.scheduleStartTime = '';
        _self.schedule.scheduleEndTime = '';
        return false;
      } else if (
        _self.picker.tap === 'scheduleEndTime' &&
        _self.schedule.scheduleStartTime &&
        _self.schedule.scheduleStartTime > res.result
      ) {
        _self.schedule.scheduleStartTime = '';
        _self.schedule.scheduleEndTime = '';
        return false;
      }
      _self.schedule[_self.picker.tap] = res.result;
    },
    //显示弹出层
    showPopup(e) {
      let _self = this;
      let data = e.currentTarget.dataset;
      _self.picker = {
        popupType: data.popupType,
        popupChoice: data.popupChoice,
        tap: data.tap,
        list: JSON.parse(data.datasList),
        valueObj: _self.selectValue[data.tap] ? _self.selectValue[data.tap] : {}
      };
      _self.$nextTick(function() {
        _self.$refs['showpopup'].open();
      });
    },
    //单选
    singleColumn(item) {
      let _self = this;
      if (_self.picker.tap === 'scheduleType') {
        _self.schedule.scheduleType = item.text;
      } else if (_self.picker.tap === 'remindType') {
        _self.schedule.remindType = item.text;
        _self.schedule.isRemind = item.type;
        _self.schedule.remindTime = item.value;
      } else if (_self.picker.tap === 'repeatStatus') {
        _self.schedule.repeatStatusShow = item.text;
        _self.schedule.repeatStatus = item.value;
        _self.schedule.repeatEndTime = '';
      }
      _self.selectValue[_self.picker.tap] = item;
      _self.$nextTick(() => {
        _self.$refs['showpopup'].close();
      });
    },
    submit() {
      let _self = this;
      //定义表单规则
      let rule = [
        {
          filedKey: 'scheduleSubject',
          required: true,
          checkType: 'notnull',
          errorMsg: '请输入日程主题'
        },
        {
          filedKey: 'scheduleDate',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择日期'
        },
        {
          filedKey: 'scheduleStartTime',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择开始时间'
        },
        {
          filedKey: 'scheduleEndTime',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择结束时间'
        }
      ];
      //进行表单检查
      let checkRes = graceChecker.check(_self.schedule, rule);
      if (checkRes) {
        _self.submitDatas();
      } else {
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    },
    //提交数据到服务器
    submitDatas() {
      let _self = this;
      _self.ajax.insertOrUpdateSchedule(_self.schedule).then(res => {
        if (_self.type === 0) {
          uni.showToast({ title: '提交成功!', icon: 'none' });
        } else {
          uni.showToast({ title: '操作成功!', icon: 'none' });
        }
        _self.jumpPage();
      });
    },
    jumpPage() {
      let _self = this;
      if (_self.type === 0) {
        uni.redirectTo({
          url: `/pages/schedule/schedule-calendar?fromPage=${_self.fromPage}`
        });
      } else {
        uni.redirectTo({
          url: `/pages/schedule/schedule-details?id=${_self.id}&fromPage=${_self.fromPage}`
        });
      }
    },
    returnBack() {
      this.jumpPage();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  .dis_flex {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .row_group {
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row_lable {
        width: 240rpx;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        .row_lable_text {
          box-sizing: border-box;
        }
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add_icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .row_lable_time {
        width: 124rpx;
      }
      .row_lable ~ .row_value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row_lable ~ .row_value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row_value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
      }
      .row_lable ~ .row_value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row_value_textarea_text {
          width: 100%;
          min-height: 280rpx;
          font-size: 32rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
      .row_lable ~ .row_value_personal {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        padding: 0 30rpx 22rpx;
        .personNameStr {
          flex: 1;
        }
      }
      .row_value_switch_text {
        font-size: 32rpx;
        transform: scale(0.68);
      }
    }
  }
  .contact_list {
    background-color: #ffffff;
    .contact_item {
      padding: 22rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 30rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .contact_item_text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      .contact_item_icon {
        line-height: 1;
      }
    }
  }
}
</style>
