<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="详情"></page-head>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-view-id="tab.viewId"
        :data-current="index"
        @click="ontabtap"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tab.name }}</text
        >
      </view>
    </scroll-view>
    <view class="content_wrap" v-if="showContent">
      <scroll-view
        class="content"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view
          class="form_wrap scroll-view-item"
          id="form"
          style="background-color: #FFFFFF;"
        >
          <view class="node_info">
            <view class="node_info_row">
              <text class="node_info_title">{{
                workflowInfo.workflowTitle
              }}</text>
            </view>
            <view class="node_info_row">
              <text v-if="workflowInfo.isPress === 1" class="node_info_speed"
                >[催办]</text
              >
              <text
                v-if="
                  workflowInfo.urgencyLevel && workflowInfo.urgencyLevel != 1
                "
                class="node_info_urge"
                >[{{
                  $oaModule.getUrgencyLevel(workflowInfo.urgencyLevel)
                }}]</text
              >
              <text class="node_info_time">{{
                workflowInfo.updateDate | formatTime
              }}</text>
            </view>
            <view class="node_info_row">
              <text class="node_info_node">{{
                workflowInfo.status === 2
                  ? ''
                  : '当前节点：' + workflowInfo.currentStepName
              }}</text>
              <text
                class="node_info_status"
                :class="{
                  node_info_status_blue: workflowInfo.statusName === '待我办理',
                  node_info_status_org:
                    workflowInfo.statusName === '退回上一步' ||
                    workflowInfo.statusName === '退回重办'
                }"
              >
                {{ workflowInfo.statusName }}
              </text>
            </view>
          </view>
          <view
            style="padding: 10px 15px 0; font-size: 14px; font-weight: bold;"
            >正文内容</view
          >
          <view class="info_head">
            <view class="info_title">{{
              applicationInfo.informationTitle
            }}</view>
            <view class="info_head_info">
              {{ applicationInfo.createUserName }}
              {{ applicationInfo.createDeptName }}
              {{ applicationInfo.createDate }}
            </view>
          </view>
          <view class="info_content">
            <rich-text :nodes="applicationInfo.informationContent"></rich-text>
          </view>
          <view
            v-if="
              applicationInfo.uploadedFile &&
                applicationInfo.uploadedFile.length > 0
            "
          >
            <view
              style="padding: 10px 15px 0; font-size: 14px; font-weight: bold;"
              >附件({{ applicationInfo.uploadedFile.length }})</view
            >
            <view class="fiel_list">
              <view
                class="file_item"
                v-for="(item, index) in applicationInfo.uploadedFile"
                :key="index"
                @tap="previewFile(item.id, item.accessorySaveName)"
              >
                <text
                  class="oa-icon"
                  :class="
                    'oa-icon-' + $oaModule.formatFileType(item.accessoryType)
                  "
                ></text>
                <text>{{ item.accessoryName }}</text>
                <text
                  class="oa-icon oa-icon-xiazai down_load"
                  @tap.stop="downloadFile(item.id, item.fileName)"
                >
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="form_wrap scroll-view-item" id="opinion">
          <view class="meeting_title">详细信息</view>
          <view class="row-group" style="margin: 0;">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">所属栏目</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.channelName
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">可查看人</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.informationReaderName
                    ? applicationInfo.informationReaderName
                    : '默认所有人'
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">有效期限</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.validbeginTime
                    ? applicationInfo.validbeginTime +
                      '至' +
                      applicationInfo.validendTime
                    : '默认永久'
                }}</text>
              </view>
            </view>
            <view
              class="row dis_flex"
              v-if="applicationInfo.messageRemind == '1'"
            >
              <view class="row_lable">
                <text class="row_lable_text">是否推送消息</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">是</text>
              </view>
            </view>
            <view class="row dis_flex" v-if="applicationInfo.watermark == '1'">
              <view class="row_lable">
                <text class="row_lable_text">水印文字</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">
                  {{ applicationInfo.watermarkText }}
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="form_wrap scroll-view-item" id="record">
          <view class="meeting_title">流程信息</view>
          <view class="task_history_wrap">
            <data-process-history
              :processHistoryList="taskHistoryList"
            ></data-process-history>
          </view>
        </view>
        <view class="form-wrap scroll-view-item" id="copyToUser">
          <view class="meeting_title">抄送信息</view>
          <view class="task_history_wrap">
            <data-copyToUser-list
              :processHistoryList="taskCopytoUserList"
            ></data-copyToUser-list>
          </view>
        </view>
      </scroll-view>
      <view class="btn_wrap" v-if="nameType === 'approvalToDo'">
        <button class="btn_item" @tap="handleTransfer">
          <text class="oa-icon oa-icon-dakai theme-color"></text>
          转办
        </button>
        <button class="btn_item" @tap="formReturn">
          <text class="oa-icon oa-icon-guanbi grey-color"></text>
          不通过
        </button>
        <button class="btn_item" @tap="formSubmit">
          <text class="oa-icon oa-icon-tijiaobanli theme-color"></text>
          通过
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
import loginModule from '@/common/js/loginModule.js';
export default {
  data() {
    return {
      showContent: false,
      nameType: '',
      parentFrom: '',
      tabIndex: 0,
      tabBars: [
        {
          name: '正文内容',
          viewId: 'form'
        },
        {
          name: '详细信息',
          viewId: 'opinion'
        },
        {
          name: '流程信息',
          viewId: 'record'
        },
        {
          name: '抄送信息',
          viewId: 'copyToUser'
        }
      ],
      scrollStatus: true, //点击状态，是否能点击
      scrollViewId: '',
      applicationInfo: {},
      taskHistoryList: [],
      taskCopytoUserList: []
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    if (opt.isMobile) {
      this.getWorkflowData(opt.wfInstId, opt.status);
    } else {
      this.nameType = opt.name;
      this.parentFrom = opt.parentFrom ? opt.parentFrom : '';
      this.workflowInfo = uni.getStorageSync('workflow_info');
      this.getData();
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    //获取流程信息数据
    async getWorkflowData(wfInstanceId, status) {
      let _self = this;
      await _self.ajax
        .getWorkflowData({
          wfInstId: wfInstanceId,
          status: status //待办-1，办结-2
        })
        .then(res => {
          _self.workflowInfo = res.object;
          let storageWorkflowInfo = uni.getStorageSync('workflow_info');
          if (!storageWorkflowInfo)
            uni.setStorageSync('workflow_info', res.object);
          let assigneeNo = res.object.assigneeNo
              ? res.object.assigneeNo.split(',')
              : '',
            permission = assigneeNo.includes(_self.empcode);
          if (res.object.status === 1 && permission) {
            _self.nameType = 'approvalToDo';
          }
          _self.getData();
        });
    },
    //获取数据
    getData() {
      let _self = this;
      _self.ajax
        .getInformationDatas({
          id: _self.workflowInfo.businessId
        })
        .then(res => {
          _self.applicationInfo = res.object;
          _self.$set(
            _self.applicationInfo,
            'informationContent',
            _self.formatRichText(_self.applicationInfo.informationContent)
          );
          _self.getfiles(res.object.id);
        });
    },
    formatRichText(html) {
      //控制正文中图片大小
      let _self = this,
        newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
          match = match
            .replace(/style="[^"]+"/gi, '')
            .replace(/style='[^']+'/gi, '');
          match = match
            .replace(/width="[^"]+"/gi, '')
            .replace(/width='[^']+'/gi, '');
          match = match
            .replace(/height="[^"]+"/gi, '')
            .replace(/height='[^']+'/gi, '');
          return match;
        });
      newContent = newContent.replace(/style="[^"]+"/gi, function(
        match,
        capture
      ) {
        match = match
          .replace(/width:[^;]+;/gi, 'max-width:100%;')
          .replace(/width:[^;]+;/gi, 'max-width:100%;');
        return match;
      });
      newContent = newContent.replace(
        /\<img/gi,
        '<img style="width:100%;height:auto;display:inline-block;"'
      );
      newContent = newContent.replace(
        new RegExp('src="/ts-document/attachment/', 'gm'),
        'src="' + _self.$config.BASE_HOST + '/ts-document/attachment/'
      );
      newContent = newContent.replace(/<o:p>(.*?)<\/o:p>/g, '$1');
      return newContent;
    },
    //获取附件
    getfiles(id) {
      let _self = this;
      _self.ajax
        .getInformationFiles({
          informationId: id
        })
        .then(res => {
          _self.applicationInfo.uploadedFile = res.object;
          _self.getTaskHisList();
          _self.getCopyUserList();
        });
    },
    //获取流程信息
    async getTaskHisList() {
      let _self = this;
      await _self.ajax
        .getTaskHisList({
          wfInstId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 100,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(async res => {
          _self.taskHistoryList = _self.$oaModule.taskHisList(res.rows);
          await _self.getcurrentNodeTips();
        });
    },
    //获取抄送信息
    async getCopyUserList() {
      let _self = this;
      await _self.ajax
        .getCopyUserList({
          wfInstId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 100,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(async res => {
          _self.taskCopytoUserList = res.object || [];
        });
    },
    //获取流程节点提示信息
    async getcurrentNodeTips() {
      let _self = this;
      await _self.ajax
        .getCurrentNodeTips(_self.workflowInfo.wfInstanceId)
        .then(res => {
          _self.showContent = true;
          if (res.object.backToStepStr) {
            uni.showModal({
              content: `${res.object.backToStepStr}\n退回说明：${res.object.handleMarkedWords}`,
              confirmText: '知道了',
              showCancel: false,
              confirmColor: '#005BAC'
            });
          } else if (res.object.handleMarkedWords) {
            uni.showModal({
              content: `办理提示：${res.object.handleMarkedWords}`,
              confirmText: '知道了',
              showCancel: false,
              confirmColor: '#005BAC'
            });
          }
        });
    },
    //tab点击事件
    ontabtap(e) {
      let _self = this;
      // 200毫秒才能执行下次点击
      if (_self.scrollStatus) {
        _self.scrollStatus = false;
        let data = e.currentTarget.dataset;
        _self.scrollViewId = data.viewId;
        _self.tabIndex = data.current;
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          _self.scrollStatus = true;
        }, 200);
      }
    },
    scroll(e) {},
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    //转办
    handleTransfer() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-transfer?formListPage=${this.formListPage}`
      });
    },
    //不通过
    formReturn() {
      let _self = this;
      uni.setStorageSync('comment_field', [
        {
          fieldType: 'comment',
          fileName: 'informationComment',
          showName: '审批意见',
          isMust: '',
          defaultVal: '',
          placeholderContent: ''
        }
      ]);
      uni.setStorageSync('form_info', {
        taskId: _self.workflowInfo.taskId,
        completeType: 2,
        infoId: _self.workflowInfo.businessId
      });
      _self.$nextTick(() => {
        let pagePramas = {
          name: _self.nameType,
          fromPage: 'approvalInformationReturn'
        };
        if (_self.parentFrom) pagePramas.parentFrom = _self.parentFrom;
        uni.navigateTo({
          url: `/pages/workflow/operation/approval-pass?${_self.$common.convertObj(
            pagePramas
          )}`
        });
      });
    },
    //通过
    formSubmit() {
      let _self = this;
      uni.setStorageSync('comment_field', [
        {
          fieldType: 'comment',
          fieldName: 'informationComment',
          keyId: 'informationComment',
          showName: '审核意见',
          isMust: '',
          defaultVal: '',
          placeholderContent: ''
        }
      ]);
      uni.setStorageSync('form_info', {
        taskId: _self.workflowInfo.taskId,
        completeType: 1, //同意-1，不同意-2
        infoId: _self.workflowInfo.businessId
      });
      _self.$nextTick(() => {
        let pagePramas = {
          name: _self.nameType,
          fromPage: 'approvalInformationPass'
        };
        if (_self.parentFrom) pagePramas.parentFrom = _self.parentFrom;
        uni.navigateTo({
          url: `/pages/workflow/operation/approval-pass?${_self.$common.convertObj(
            pagePramas
          )}`
        });
      });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .dis_flex {
    display: flex;
    justify-content: center;
  }
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 33.3333333%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title {
        color: #555;
        font-size: 30rpx;
        height: 80rpx;
        line-height: 76rpx;
        flex-wrap: nowrap;
        display: block;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .meeting_title {
    margin: 20rpx 30rpx 10rpx;
    font-size: 28rpx;
    color: #999;
  }
  .content_wrap {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: hidden;
      .node_info {
        background-color: #fff;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        border-bottom: 1px solid #eee;
        .node_info_row {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_title {
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_time {
          font-size: 24rpx;
          color: #999;
          overflow: hidden;
          .node_info_icon {
            color: #f59a23;
            padding-right: 10rpx;
            font-size: 28rpx;
          }
        }
        .node_info_node {
          font-size: 28rpx;
          color: #666;
        }
        .node_info_speed {
          color: #dd1f36;
        }
        .node_info_urge {
          color: #f59a23;
        }
        .node_info_speed,
        .node_info_urge {
          font-size: 28rpx;
          font-weight: bold;
        }
        .node_info_status {
          font-size: 24rpx;
          color: #999;
          font-weight: bold;
        }
        .node_info_status_blue {
          color: #005bac;
        }
        .node_info_status_org {
          color: #f59a23;
        }
      }
      .info_head {
        color: #999999;
        background-color: #ffffff;
        font-size: 24rpx;
        padding: 20rpx 30rpx 0;
        position: relative;
        &::after {
          position: absolute;
          content: '';
          left: 30rpx;
          right: 30rpx;
          bottom: 0;
          height: 1px;
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #eeeeee;
        }
        .info_title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          margin-bottom: 10rpx;
        }
        .info_head_info {
          font-size: 28rpx;
        }
      }
      .info_content {
        padding: 20rpx 30rpx;
        font-size: 30rpx;
        color: #333;
        background-color: #ffffff;
        image {
          width: 100%;
        }
      }
      .row-group {
        margin-top: 20rpx;
        .row {
          width: 100%;
          background-color: #ffffff;
          position: relative;
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            left: 30rpx;
            right: 0;
            transform: scaleY(-0.5);
            height: 1px;
            background-color: #eee;
          }
          &:last-child::after {
            height: 0;
          }
          .row_lable {
            width: 200rpx;
            font-size: 30rpx;
            color: #999;
            padding: 22rpx 30rpx;
            box-sizing: border-box;
            position: relative;
            text-align: right;
            .row_lable_text {
              padding-right: 20rpx;
              box-sizing: border-box;
            }
          }
          .row_lable ~ .row_value {
            flex: 1;
            font-size: 30rpx;
            color: #333;
            padding: 22rpx 30rpx;
            padding-left: 0;
            box-sizing: border-box;
            text-align: left;
          }
          // .row_lable~.row_value_textarea{
          // 	width: 100%;
          // 	padding-left: 30rpx;
          // 	padding-top: 0;
          // 	text-align: left;
          // }
        }
      }
      .fiel_list {
        padding: 0 30rpx 20rpx;
        .file_item {
          text-decoration: none;
          color: #005bac;
          font-size: 30rpx;
          background-color: #fff;
          display: flex;
          align-items: center;
          .oa-icon {
            flex-shrink: 0;
            font-size: 40rpx;
            margin-right: 10rpx;
          }
          text:not(.oa-icon) {
            flex: 1;
          }
        }
      }
      .task_history_wrap {
        padding: 20rpx 30rpx 20rpx 40rpx;
        background-color: #fff;
        .task_history_list {
          list-style-type: none;
          border-left: 4rpx dashed #ddd;
          padding: 0px;
          .task_history_item_current {
            &::after {
              background: #005bac !important;
            }
            .task_history_item_name,
            .task_history_item_content {
              color: #005bac !important;
            }
          }
          .task_history_item {
            position: relative;
            &::after {
              content: '';
              position: absolute;
              top: 10rpx;
              left: 0;
              transform: translateX(-18rpx);
              width: 30rpx;
              height: 30rpx;
              border-radius: 100%;
              background: #ddd;
            }
            .task_history_item_wrap {
              margin: 20rpx 0 20rpx 30rpx;
              .task_history_item_assent {
                color: #3aad73;
                font-size: 28rpx;
              }
              .task_history_item_dissent {
                color: #f59a23;
                font-size: 28rpx;
              }
              .task_history_item_name {
                font-size: 28rpx;
                color: #666;
                padding-right: 20rpx;
              }
              .task_history_item_time {
                font-size: 24rpx;
                color: #999;
                float: right;
              }
              .task_history_item_content {
                font-size: 28rpx;
                color: #666;
              }
            }
          }
        }
      }
    }
    .theme-color {
      color: $theme-color !important;
    }
    .grey-color {
      color: $uni-text-color-subtitle !important;
    }
    .btn_wrap {
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      z-index: 10;
      .btn_item {
        height: 90rpx;
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        background-color: transparent;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
          border: 0;
          top: 20rpx;
          bottom: 20rpx;
          right: 0;
          left: unset;
          transform: scaleX(-0.5);
          width: 1px;
          height: unset;
          background-color: #ccc;
        }
        &:last-child::after {
          width: 0;
        }
        .oa-icon {
          margin: 0 6rpx;
          font-size: 36rpx;
        }
      }
    }
  }
}
</style>
