<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle" />
    <view class="content" v-if="showContent">
      <view class="form_wrap">
        <form-input
          ref="form"
          :formId="formId"
          :formTemplate="formTemplate"
          :formDatas="formDatas"
          :fileOpt="fileOpt"
          :btnType="btnType"
          @afterCheck="afterCheck"
        />
      </view>
      <view class="bottom_btn">
        <button class="btn_item" @tap="formSubmitDraft">
          存草稿
        </button>
        <button class="btn_item uni-bg-blue" @tap="formSubmit">提交</button>
      </view>
    </view>
  </view>
</template>

<script>
import formInput from '@/components/form-components/form-input.vue';
import formAutoCalculat from '@/common/js/formAutoCalculat.js';
import { getUuid } from './mixins/common.js';

export default {
  components: {
    formInput
  },
  data() {
    return {
      workflowLabel: '',
      showContent: false,
      btnType: '', //startAgain-再次发起 draft-草稿箱
      formTitle: '',
      formTemplate: [], //表单模板
      formId: '', //表单id
      wfDefinitionId: '', //流程id
      workflowNo: '',
      tableName: '',
      approvalPersonCode: '',
      approvalPersonName: '',
      isSubmit: false,
      formDatas: {},
      fileOpt: {}
    };
  },
  async onLoad(opt) {
    this.wfDefinitionId = opt.wfDefinitionId;
    this.workflowNo = opt.workflowNo;
    this.formId = opt.formId;
    this.btnType = opt.btnType || '';
    if (opt.btnType) {
      await this.getFormDatas(opt.businessId);
    } else {
      await this.getFormTemplate();
    }
    if (opt.workflowLabel) this.workflowLabel = opt.workflowLabel;
  },
  methods: {
    //获取表单数据
    async getFormDatas(businessId) {
      await this.ajax
        .getFormDatas({
          id: businessId,
          wfDefinitionId: this.wfDefinitionId
        })
        .then(async res => {
          this.formDatas = JSON.parse(JSON.stringify(res['object'][0]));
          if (this.btnType == 'startAgain') {
            //再次发起需删掉业务数据
            delete this.formDatas.ID;
            delete this.formDatas.WORKFLOW_ID;
            delete this.formDatas.CREATE_DATE;
            delete this.formDatas.CREATE_USER;
            delete this.formDatas.CREATE_USER_NAME;
            delete this.formDatas.IS_DELETED;
            delete this.formDatas.UPDATE_DATE;
            delete this.formDatas.UPDATE_USER;
            delete this.formDatas.UPDATE_USER_NAME;
          }
          await this.getFormTemplate();
        });
    },
    //获取表单设计模板
    async getFormTemplate() {
      await this.ajax.getFormTemplateById(this.formId).then(async res => {
        this.formTitle = res.object.workflowName;
        let dataList = res.object.toaFieldSetList,
          dataSort = JSON.parse(res.object.formTemplate);
        for (let t = 0; t < dataSort.length; t++) {
          for (let i = 0; i < dataList.length; i++) {
            if (dataSort[t] == dataList[i].fieldName) {
              if (dataList[i].fieldType == 'childForm') {
                let childFormDetail = await this.ajax
                  .getChildFormDetailById(dataList[i].tableId)
                  .catch(res => res);
                if (childFormDetail.success) {
                  let object = childFormDetail.object,
                    fields = object.fields
                      .filter(childItem => childItem.pcShow == 1)
                      .map(childItem => {
                        let newField = {
                          ...childItem,
                          optionList: (childItem.optionValue || '')
                            .replaceAll(/[,，;；]/g, ',')
                            .split(',')
                            .filter(item => item)
                            .map(item => ({ text: item, value: item }))
                        };
                        newField.fieldType == 'EXPRESSION' &&
                          (newField.formula = formAutoCalculat.computedFormula(
                            childItem.defaultValue
                          ));
                        return newField;
                      });
                  dataList[i].childFormDetail = childFormDetail.object;
                  dataList[i].childFormColumns = fields;
                }
              }
              this.formTemplate.push(dataList[i]);
              continue;
            }
          }
        }
        if (this.btnType) {
          let fieldList = await this.fieldFilter(dataList);
          for (let t = 0; t < fieldList.length; t++) {
            await this.getFiles(fieldList[t].fieldName);
          }
        }
        this.tableName = res.object.tableName;
        this.$nextTick(() => {
          this.showContent = true;

          setTimeout(() => {
            const scriptText = res.object.javascriptText;
            if (scriptText) {
              let fn = new Function(scriptText);
              fn.call(this);
              fn = null;
              this.$forceUpdate();
            }
          }, 1000);
        });
      });
    },
    async fieldFilter(dataList) {
      let fieldList = [];
      for (let i = 0; i < dataList.length; i++) {
        if (dataList[i].fieldType === 'file') {
          fieldList.push(dataList[i]);
        }
      }
      return fieldList;
    },
    //获取附件
    async getFiles(fieldName) {
      await this.ajax
        .getFiles({
          idsStr: this.formDatas[fieldName]
        })
        .then(async res => {
          await this.$set(this.formDatas, fieldName, res.object);
          await this.$set(this.fileOpt, fieldName, res.object);
        });
    },
    formSubmitDraft() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      let childForm = this.$refs.form;
      let api = this.formDatas.ID ? 'updateWorkflowDraft' : 'saveWorkflowDraft';
      let fromInfo = {
        remark: childForm.itemData[childForm.commentValTap],
        approvalFiled: childForm.commentValTap,
        taskFileList: childForm.choiceData[childForm.commentValTap],
        wfDefinitionId: this.wfDefinitionId,
        dataMap: {
          ...this.formDatas,
          ...childForm.itemData
        },
        tableName: this.tableName,
        workflowNo: this.workflowNo
      };
      this.ajax[api](fromInfo)
        .then(res => {
          if (!this.formDatas.ID) {
            this.formDatas.ID = res.object;
          }
          this.isSubmit = false;
          uni.showToast({
            icon: 'none',
            duration: 2500,
            title: '流程已成功保存到草稿箱'
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    formSubmit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      this.$refs['form'].checkFrom();
    },
    afterCheck(valid, itemData, commentData) {
      if (!valid) {
        this.isSubmit = false;
        return false;
      }
      let nodeCondition = {
        ...{
          wfDefId: this.wfDefinitionId,
          autoCommentId: getUuid(),
          workflowStart: 'Y'
        },
        ...itemData
      };
      let dataMap =
        this.btnType == 'draft' ? { ...this.formDatas, ...itemData } : itemData;
      this.getNodeInfoByWfDefId(dataMap, nodeCondition, commentData);
    },

    //根据流程定义id获取节点信息
    getNodeInfoByWfDefId(itemData, nodeCondition, commentData) {
      this.ajax.getNextWfStepListByWfDefId(nodeCondition).then(res => {
        if (typeof res === 'string') {
          uni.showToast({
            icon: 'none',
            duration: 2500,
            title: res
          });
          return false;
        }
        let list = res.object,
          fromInfo = {
            ...commentData,
            ...{
              wfDefinitionId: this.wfDefinitionId,
              dataMap: itemData,
              tableName: this.tableName,
              workflowNo: this.workflowNo,
              autoCommentId: nodeCondition.autoCommentId
            }
          };
        let isPromoterChoice = list.some(item => {
          return item.promoterChoice == 1;
        });
        let openPageUrl = '';
        if (isPromoterChoice) {
          list = list.filter(item => {
            return item.wfStepName != '结束';
          });
          openPageUrl = `/pages/workflow/operation/workflow-define?fromPage=initWorkflow`;
        } else {
          openPageUrl = `/pages/workflow/operation/select-workflow-node`;
          if (document.querySelector('#serialNumber')) {
            let dom = document
              .querySelector('#serialNumber')
              .getAttribute('field-name');
            fromInfo.dataMap['fieldSerialNumberProp'] = dom;
          }
        }
        uni.setStorageSync('node_list', list);
        uni.setStorageSync('form_info', fromInfo);
        if (this.workflowLabel)
          openPageUrl = `${openPageUrl}?workflowLabel=${this.workflowLabel}`;
        this.$nextTick(() => {
          this.isSubmit = false;
          uni.navigateTo({
            url: openPageUrl
          });
        });
      });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  .content {
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    .form_wrap {
      flex: 1;
      overflow: hidden;
    }
    .bottom_btn {
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      z-index: 10;
      .btn_item {
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        border-radius: 10rpx;
        margin-right: 30rpx;
        &:last-child {
          margin: 0;
        }
      }
    }
  }
}
</style>
