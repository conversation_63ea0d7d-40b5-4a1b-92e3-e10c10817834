<template>
  <view class="ts-container">
    <u-navbar title="活动记录" title-bold :custom-back="goBack"></u-navbar>
    <view class="political-management-box">
      <view class="political-management-top">
        <base-tabs-swiper
          ref="tabSwiper"
          class="tab-swiper-box"
          :list="tabList"
          badgeType="text"
          :current="currentTab"
          :is-scroll="false"
          @change="changeTab"
        ></base-tabs-swiper>
        <!-- <view class="search-icon-box">
          <u-icon
            name="shaixuan"
            custom-prefix="wxoa-icon"
            size="36"
            @click="toggleScreenInfo"
            :class="{
              'has-search-inform':
                screenForm.politicalName || screenForm.startDate
            }"
          ></u-icon>
        </view> -->
      </view>
      <swiper
        class="swiper-box"
        :current="currentTab"
        :duration="300"
        @change="onTabChange"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in tabList"
          :key="index"
        >
          <mescroll
            :ref="`mescroll${index}`"
            :mescrollIndex="index"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <political-item
              v-for="i in item.list"
              :key="i.id"
              :item="i"
              @click="jumpToDetail(i.id, item.path)"
            >
            </political-item>
          </mescroll>
        </swiper-item>
      </swiper>
    </view>
    <!-- <base-filter-popup
      ref="rightScreen"
      v-model="isShowScreen"
      :form-list="screenFormList"
      :form-data.sync="screenForm"
      @reset="screenReset"
      @confirm="screenConfirm"
    ></base-filter-popup> -->
  </view>
</template>

<script>
import index from './index.js';
import baseTabsSwiper from '@/components/base-tabs-swiper/base-tabs-swiper.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import politicalItem from './components/political-item.vue';
// import baseFilterPopup from '@/components/base-filter-popup/base-filter-popup.vue';
export default {
  name: 'political-management-list',
  mixins: [index],
  components: {
    baseTabsSwiper,
    mescroll,
    politicalItem
    // baseFilterPopup
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
@import '@/assets/css/ellipsis.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.political-management-box {
  @include vue-flex(column);
  flex: 1;
}
.tab-swiper-box {
  flex: 1;
  width: auto;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  height: 100%;
}
.ellipsis-row-1 {
  min-width: 0;
  @include ellipsis;
}
.has-search-inform {
  color: $uni-color-primary;
}
.political-management-top {
  @include vue-flex;
  margin-bottom: $uni-spacing-col-base;
}
.search-icon-box {
  padding-right: $uni-spacing-row-lg;
  background-color: $uni-text-color-inverse;
  line-height: 80rpx;
}
</style>
