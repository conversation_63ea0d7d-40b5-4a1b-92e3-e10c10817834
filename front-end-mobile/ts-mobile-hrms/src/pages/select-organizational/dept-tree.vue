<template>
  <view class="dept-tree">
    <dept-tree-item
      v-for="(item, index) of listData"
      :key="index"
      :data="item"
      :level="1"
      :propotySet="propotySet"
    >
    </dept-tree-item>
  </view>
</template>

<script>
import deptTreeItem from './dept-tree-item.vue';
export default {
  name: 'dept-tree',
  components: {
    deptTreeItem
  },
  props: {
    /**@desc 树数据
     * @param {String}  label     显示的名称      可通过 propotySet 属性配置
     * @param {any}     value     点击选中的值    可通过 propotySet 属性配置
     * @param {Array}   children  子数据列表      可通过 propotySet 属性配置
     */
    data: {
      type: Array,
      default: () => []
    },
    /**@desc 树显示数据配置
     * @param {String}    label     显示的名称的属性名
     * @param {String}    value     选中值的属性名
     * @param {String}    children  子数据列表的属性名
     * @param {Function}  formatter 名称显示内容
     */
    propotySet: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id',
          children: 'children'
        };
      }
    },
    /**@desc 唯一标志 */
    nodeKey: {
      type: String,
      default: () => 'id'
    },
    /**@desc 选中标签背景色 */
    selectedBackgroundColor: {
      type: String,
      default: () => '$u-type-primary-light'
    },
    /**@desc 是否只有在点击左侧icon标签才展开 */
    onlyExpandInClickIcon: {
      type: Boolean,
      default: () => true
    },
    /**@desc 手风琴模式 */
    accordion: {
      type: Boolean,
      default: () => false
    },
    lazyLoading: {
      type: Boolean,
      default: () => false
    },
    /**@desc 最长异步层级数据加载时长 单位ms */
    maxAsyncTime: {
      type: Number,
      default: () => 15000
    },
    filterMethod: {
      type: Function
    }
  },
  watch: {
    data: function(val) {
      this.setListData();
    }
  },
  data() {
    return {
      isTree: true,
      listData: [],
      tree: [],
      currentKey: null,
      currentNode: null,
      selectedKey: [],
      selectedNode: []
    };
  },
  methods: {
    createdNode(level, data, node) {
      node.lazyLoading = this.lazyLoading;
      let parent = this.tree.find(
        item => item.key == node.parent.data[this.nodeKey]
      );
      let newNode = {
        level,
        key: data[this.nodeKey],
        children: [],
        data,
        dom: node
      };
      parent && parent.children.push(newNode);
      this.tree.push(newNode);
    },
    //获取指定key值的node
    getNode(key) {
      let treeItem =
        this.tree.find(item => item.data[this.nodeKey] === key) || {};

      return treeItem.dom;
    },
    getSelectedNode() {
      return this.selectedNode;
    },
    getSelectedValue() {
      let valueList = this.selectedNode.map(item => {
        return item.data[this.propotySet.value];
      });
      return valueList;
    },

    setCurrentNode(node, sendEvent) {
      const nodeType = Object.prototype.toString.call(node);
      let nodeKey;
      if (nodeType == '[object Array]') {
        return;
      } else if (nodeType == 'String') {
        nodeKey = node;
      } else if (nodeType == '[object Object]') {
        nodeKey = node.data[this.nodeKey];
      }

      if (nodeKey === undefined) {
        return;
      }

      if (this.currentKey) {
        let currentNode = this.currentNode;
        while (!currentNode.isTree) {
          if (currentNode.isCurrent) {
            currentNode.isCurrent = false;
            this.currentKey = null;
            this.currentNode = null;
            break;
          } else {
            currentNode = currentNode.parent;
          }
        }
        if (this.currentKey) {
          return;
        }
      }

      let optionNode = this.getNode(nodeKey);

      this.currentKey = optionNode.data[this.nodeKey];
      this.currentNode = optionNode;
      optionNode.isCurrent = true;

      if (sendEvent) {
        this.$emit('current-node-change', node, node.data);
      }
    },
    setExpandedNode(node) {
      const nodeType = Object.prototype.toString.call(node);
      let nodeKey;
      if (nodeType == '[object Array]') {
        nodeType.forEach(item => {
          this.setExpandedNode(item);
        });
      } else if (nodeType == 'String') {
        nodeKey = node;
      } else if (nodeType == '[object Object]') {
        nodeKey = node.data[this.nodeKey];
      }

      if (nodeKey === undefined) {
        return;
      }

      let optionNode = this.getNode(nodeKey);

      if (!optionNode.isExpanded && this.accordion) {
        let siblingList = optionNode.$parent.$children;
        for (let i = 0; i < siblingList.length; i++) {
          let sibling = siblingList[i];
          if (sibling.isExpanded) {
            sibling.isExpanded = false;
          }
        }
      }
      optionNode.isExpanded = !optionNode.isExpanded;
    },
    setSelectedNode(node) {
      const nodeType = Object.prototype.toString.call(node);
      let nodeKey;
      if (nodeType == '[object Array]') {
        nodeType.forEach(item => {
          this.setExpandedNode(item);
        });
      } else if (nodeType == 'String') {
        nodeKey = node;
      } else if (nodeType == '[object Object]') {
        nodeKey = node.data[this.nodeKey];
      }

      if (nodeKey === undefined) {
        return;
      }

      let optionNode = this.getNode(nodeKey),
        selectedIndex = this.selectedKey.findIndex(
          item => item === optionNode.data[this.nodeKey]
        );
      if (selectedIndex >= 0) {
        optionNode.isChecked = false;
        this.selectedKey.splice(selectedIndex, 1);
        this.selectedNode.splice(selectedIndex, 1);
      } else {
        optionNode.isChecked = true;
        this.selectedKey.push(optionNode.data[this.nodeKey]);
        this.selectedNode.push(optionNode);
      }
    },
    setListData() {
      this.listData = JSON.parse(JSON.stringify(this.data));
    },

    /**@desc 异步层级树加载*/
    loadAsyncSourceData(parentNode, sourceLevel) {
      return new Promise((resolve, reject) => {
        let loadTimer = setTimeout(() => {
          reject();
        }, 3000);
        this.$emit('async-source-load', parentNode.data, sourceData => {
          loadTimer && clearTimeout(loadTimer);
          this.appendData(parentNode.data[this.nodeKey], sourceData);
          resolve();
        });
      });
    },

    appendData(key, data, node = { children: this.listData }) {
      if (node[this.nodeKey] == key) {
        node.children ? null : (node.children = []);
        node.children.push(...data);
        return;
      }
      if (node[this.propotySet.children]) {
        node[this.propotySet.children].forEach(item => {
          this.appendData(key, data, item);
        });
      }
    },

    filter(val) {
      if (!this.filterMethod) {
        throw Error('filterMethod is required');
      }
      const filterFunc = node => {
        const childNodes = node.children || [],
          dom = node.dom;

        childNodes.forEach(child => {
          child.dom.isHidden = !this.filterMethod.call(child, val, child.data);
          filterFunc(child);
        });

        if (node.dom.isHidden && childNodes.length) {
          let allHidden = true;
          allHidden = !childNodes.some(child => !child.dom.isHidden);

          node.dom.isHidden = allHidden === true;
        }

        if (!val) {
          return;
        }

        if (
          !node.dom.isHidden &&
          node.data.children &&
          node.data.children.length &&
          !this.lazyLoading
        ) {
          this.setExpandedNode(node);
        }
      };
      filterFunc(this.tree[0]);
    }
  }
};
</script>

<style lang="scss" scoped>
.dept-tree {
  background: #fff;
  padding: 8px;
}
</style>
