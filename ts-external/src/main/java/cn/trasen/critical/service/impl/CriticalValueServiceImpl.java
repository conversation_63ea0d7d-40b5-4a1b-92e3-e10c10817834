package cn.trasen.critical.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import cn.trasen.critical.dao.CriticalValueMapper;
import cn.trasen.critical.dao.ExternalLogsMapper;
import cn.trasen.critical.model.CriticalValue;
import cn.trasen.critical.model.ExternalLogs;
import cn.trasen.critical.service.CriticalValueService;
import cn.trasen.critical.service.HisApiService;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.po.Message;
import cn.trasen.po.ResponseJson;
import cn.trasen.utils.HttpClientUtils;
import cn.trasen.utils.ThpsUserDBHelper;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
@Slf4j
public class CriticalValueServiceImpl implements CriticalValueService {

	@Resource
	private CriticalValueMapper criticalValueMapper;
	
	@Resource
	private ExternalLogsMapper externalLogsMapper;
	
	@Autowired
	private ThpsUserDBHelper thpsUserDBHelper;
	 
	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;
	
	@Autowired
	private HisApiService hisApiService;
	
	@Value("${wx.loginUrl}")
	private String wxLoginUrl;
	
	@Value("${remindLeaderTime}")
	private String second;
	
	@Value("${his.request.url}")
	private String hisRequestUrl;
	
	@Value("${his.request.queryOrderItemUrl}")
	private String queryOrderItemUrl;
	
	@Value("${his.request.querySampleUrl}")
	private String querySampleUrl;
	
	@Value("${his.request.orgCode}")
	private String hisRequestOrgCode;
	
	@Value("${his.request.hospCode}")
	private String hisRequestHospCode;
	
	@Value("${wxOrDingtalk}")
	private String wxOrDingtalk;
	
	@Value("${his.request.handleCriticalValue}")
	private String handleCriticalValue;
	
	@Value("${his.request.appid}")
	private String appid;
	
	@Value("${his.request.version}")
	private String version;
	
	
	
	@Override
	@Transactional(readOnly = false)
	public void insert(ResponseJson responseJson) {
		long startTime = System.currentTimeMillis();    //获取开始时间
		
		
		//HSB消息体
		Message message = responseJson.getMessage();
		//TopicConfig topicConfig = responseJson.getTopicConfig();
		
		//his消息体
		String content = message.getContent();
		
		//转JSON对象
		JSONObject jsonObject =	JSONObject.parseObject(content);
		
		JSONObject Body = (JSONObject) jsonObject.get("Body");//主体
		JSONObject criticaInfo = (JSONObject) Body.get("criticaInfo");//消息体内容
		
		String title = jsonObject.getString("Title");//标题
		String reportNo = criticaInfo.getString("reportNo");//检验单号
		String patientName = criticaInfo.getString("patientName");//病人姓名
		//JSONArray criticaiValues = (JSONArray)criticaInfo.get("criticaIValues");//危急值内容
		String dangerNote = criticaInfo.getString("dangerNote");//危急值内容文本
		String deptName = criticaInfo.getString("deptName");//病人科室
		String bedNo = criticaInfo.getString("bedNo");//床号
		String regNo = criticaInfo.getString("regNo");//病历号
		String doctorId = criticaInfo.getString("doctorId");//医生工号
		String bedDoctorName = criticaInfo.getString("BED_DOCTOR_NAME");//主管医生
		String doctorName = criticaInfo.getString("doctorName");//医生姓名
		if(StringUtils.isEmpty(bedDoctorName)){
			bedDoctorName = doctorName;
		}
		String panicvalue = criticaInfo.getString("PANICVALUE");// 检验危急值
		String signDate = criticaInfo.getString("SIGN_DATE");// 危急值报告时间
		
		
		String sendUserId = Body.getString("sendUserId");//发送人ID
		String sendUser = Body.getString("sendUser");//危急值报告人
		String sendDept = Body.getString("sendDept");//发送人科室
		String receiveUser = Body.getString("receiveUser");//开单医生
		
		
		
		String leaderDoctorId = criticaInfo.getString("leaderDoctorId"); //上级主任ID
		String leaderDoctorName = criticaInfo.getString("leaderDoctorName");//上级主任名称
		
		String target_type = criticaInfo.getString("target_type");//0科主任，1医生
		String leaderDoctorCode = criticaInfo.getString("leaderDoctorCode");//医生工号
		
		if("0".equals(target_type)) {
			Map<String, String> map =  thpsUserDBHelper.getLeaderUser(leaderDoctorCode);
			String  directleadershipid = map.get("directleadershipid");
			String  directleadershipname = map.get("directleadershipname");
			if(StringUtils.isNotBlank(directleadershipid)){
				leaderDoctorCode = directleadershipid;
				leaderDoctorName = directleadershipname;
			}
		}
		
		String criticalValueId = String.valueOf(IdWork.id.nextId());
		CriticalValue criticalValue = new CriticalValue();
		criticalValue.setId(criticalValueId);
		criticalValue.setTitle(title);
		criticalValue.setReportNo(reportNo);
		criticalValue.setPatientName(patientName);
		criticalValue.setCriticaiValues(criticaInfo.getString("criticaIValues"));
		criticalValue.setDangerNote(dangerNote);
		criticalValue.setDeptName(deptName);
		criticalValue.setBedNo(bedNo);
		criticalValue.setDoctorId(doctorId);
		criticalValue.setDoctorName(doctorName);
		criticalValue.setSendUserId(sendUserId);
		criticalValue.setSendUser(sendUser);
		criticalValue.setLeaderDoctorId(leaderDoctorId);
		criticalValue.setLeaderDoctorName(leaderDoctorName);
		criticalValue.setCreateTime(new Date());
		criticalValue.setIsRead(0);
		criticalValue.setStatus(0);
		criticalValue.setRegNo(regNo);
		criticalValue.setSendDept(sendDept);
		criticalValue.setBedDoctorName(doctorName);;
		criticalValue.setReceiveUser(receiveUser);
		criticalValue.setPanicvalue(panicvalue);
		criticalValue.setSignDate(signDate);
		criticalValue.setMsgId(Body.getString("MsgId"));
		
		criticalValue.setOrgCode(Body.getString("sendOrgCode"));
		criticalValue.setHospCode(hisRequestHospCode);
		criticalValueMapper.insertSelective(criticalValue);
		
		StringBuffer contentSb = new StringBuffer();
		
		if("1".equals(wxOrDingtalk)){
			contentSb.append("您收到一条危急值消息，请及时处理！病人姓名：").append(patientName).append("，床位号：").append(bedNo);
		}else{
			//contentSb.append("<div class=\"gray\">").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("</div><br>");
			contentSb.append("<div class=\"normal\">病人姓名：").append(patientName).append("</div><br>");
			contentSb.append("<div class=\"highlight\">危急值项目：").append(dangerNote).append("</div>");
			//contentSb.append("<div class=\"highlight\">病人科室：").append(deptName).append("</div><br>");
			//contentSb.append("<div class=\"highlight\">床位号：").append(bedNo).append("</div><br>");
			//contentSb.append("<div class=\"highlight\">点击后表示已读</div>");
		}
		
		//推送微信消息
		NoticeReq notice = NoticeReq.builder()
					.content(contentSb.toString())
					.noticeType("3")
					.receiver(doctorId)
					.sender(sendUserId)
					.senderName(sendUser)
					.subject("危急值提醒")
					.wxSendType("1")
					.url(wxLoginUrl + "pages/criticalValues/critical-values-detail?id="+criticalValueId)//要跳转的url + criticalValueId
					.build();
		
		NoticeService informationFeignClient = new NoticeService();
		informationFeignClient.sendAsynNotice(notice);
		
		long endTime = System.currentTimeMillis();    //获取结束时间
		
		ExternalLogs externalLogs = new ExternalLogs();
		externalLogs.setId(String.valueOf(IdWork.id.nextId()));
		//externalLogs.setRequestUrl(hisRequestUrl);
		externalLogs.setRequestService("危急值提醒接口");
		externalLogs.setRequestMethos("receiveCriticalValue");
		//externalLogs.setRequestParams(null);
		externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
		externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
		externalLogs.setCreateTime(new Date());
		externalLogs.setResponseParams(JSONObject.toJSONString(responseJson));
		externalLogs.setTakeTime((endTime - startTime) + "ms");
		externalLogsMapper.insertSelective(externalLogs);
	}

	@Override
	@Transactional(readOnly = false)
	public CriticalValue selectById(String id) {
		
		if(id.contains("?")){
			String[] split = id.split("?");
			id = split[0];
		}
		
		CriticalValue criticalValue = criticalValueMapper.selectByPrimaryKey(id);
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		
		if("bhsdermyy".equals(orgCode)){
			
	       	Map<String,Object> paramsMap = new HashMap<>();
	       	paramsMap.put("orgCode", hisRequestOrgCode);//机构编码
	    	paramsMap.put("operatorId", criticalValue.getLeaderDoctorId());//操作人ID
	    	paramsMap.put("operatorName", criticalValue.getDoctorName());//操作人姓名
	    	//paramsMap.put("deptId", );//科室ID
	    	//paramsMap.put("deptName", );//科室名称
	    	
	    	List<Map<String,Object>> list = new ArrayList<>(); 
	    	Map<String,Object> map = new HashMap<>();
	    	map.put("orgCode", hisRequestOrgCode);
	    	map.put("tradeNo", criticalValue.getMsgId());
	    	map.put("systemCode","YS012");//系统代码
	    	map.put("systemName", "OA");//系统名称
	    	map.put("receiveStatus", "1");
	    	map.put("receiveUserId", criticalValue.getLeaderDoctorId());
	    	map.put("receiveUserName",criticalValue.getDoctorName());
	    	map.put("receiveDate", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
	    	map.put("receiveDesc", "已接收");
	    	
	    	list.add(map);
	    	
	    	paramsMap.put("list", list);
	       	
	       	String requestParams = JSON.toJSONString(paramsMap);
	       	
	    	log.info("调用   UpdateCriticalValueStatus请求的参数 :" + requestParams);
	    	
	       	String resultJson = HttpClientUtils.doPostJson(handleCriticalValue, requestParams);
	       	
	        log.info("返回的数据 :" + resultJson);
	       	
	        criticalValue.setIsRead(1);
	       	criticalValueMapper.updateByPrimaryKeySelective(criticalValue);
	       	
	       	ExternalLogs externalLogs = new ExternalLogs();
			externalLogs.setId(String.valueOf(IdWork.id.nextId()));
			externalLogs.setRequestUrl(handleCriticalValue);
			externalLogs.setRequestService("new接收危急值并回传给HIS");
			externalLogs.setRequestMethos("UpdateCriticalValueStatus");
			externalLogs.setRequestParams(requestParams);
			externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
			externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
			externalLogs.setCreateTime(new Date());
			externalLogs.setResponseParams(resultJson);
			externalLogsMapper.insertSelective(externalLogs);
			
		}
		
		return criticalValue;
	}

	@Override
	public List<CriticalValue> getDataList(Page page, CriticalValue record) {
		Example example = new Example(CriticalValue.class);
		example.and().andEqualTo("doctorId", UserInfoHolder.getCurrentUserCode());
        example.setOrderByClause("create_time desc");
        if (StringUtils.isNotBlank(record.getTitle())) {//标题
            example.and().andLike("title", "%" + record.getTitle() + "%");
        }
        return criticalValueMapper.selectByExampleAndRowBounds(example, page);
	}

	@Override
	@Transactional(readOnly = false)
	public void remindLeaderCriticalValue() {
		List<CriticalValue> list = criticalValueMapper.selectRemindLeaderCriticalValue(second);
		for (CriticalValue criticalValue : list) {
			if(StringUtils.isNotBlank(criticalValue.getLeaderDoctorId())) {
				StringBuffer contentSb = new StringBuffer();
				//contentSb.append("<div class=\"gray\">").append(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")).append("</div><br>");
				contentSb.append("<div class=\"normal\">病人姓名：").append(criticalValue.getPatientName()).append("</div><br>");
				contentSb.append("<div class=\"highlight\">危急值项目：").append(criticalValue.getDangerNote()).append("</div>");
				//contentSb.append("<div class=\"highlight\">病人科室：").append(deptName).append("</div><br>");
				//contentSb.append("<div class=\"highlight\">床位号：").append(bedNo).append("</div><br>");
				//contentSb.append("<div class=\"highlight\">点击后表示已读（超过一定时间未点击会通知上级领导）</div>");
				
				//推送微信消息
				NoticeReq notice = NoticeReq.builder()
							.content(contentSb.toString())
							.noticeType("3")
							.receiver(criticalValue.getLeaderDoctorId())
							.sender(criticalValue.getSendUserId())
							.senderName(criticalValue.getSendUser())
							.subject("危急值提醒")
							.wxSendType("1")
							.url(wxLoginUrl)//要跳转的url + criticalValueId
							.build();
				
				NoticeService informationFeignClient = new NoticeService();
				informationFeignClient.sendAsynNotice(notice);
				
				criticalValue.setIsRead(1);
				criticalValueMapper.updateByPrimaryKeySelective(criticalValue);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void updateCriticalValueStstus(String id) {
		CriticalValue criticalValue = new CriticalValue();
		criticalValue.setId(id);
		criticalValue.setIsRead(1);
		criticalValueMapper.updateByPrimaryKeySelective(criticalValue);
	}

	@Override
	@Transactional(readOnly = false)
	public void handleCriticalValue(CriticalValue record) {
		
		PlatformResult<GlobalSetting> globalSetting = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = globalSetting.getObject().getOrgCode();
		
		if("bhsdermyy".equals(orgCode)){
			
	       	CriticalValue criticalValue = criticalValueMapper.selectByPrimaryKey(record.getId());
	       	
	       	Map<String,Object> paramsMap = new HashMap<>();
	       	paramsMap.put("orgCode", hisRequestOrgCode);//机构编码
//	    	paramsMap.put("operatorId", criticalValue.getLeaderDoctorId());//操作人ID
//	    	paramsMap.put("operatorName", criticalValue.getDoctorName());//操作人姓名
	       	paramsMap.put("operatorId", UserInfoHolder.getCurrentUserCode());//操作人ID
	    	paramsMap.put("operatorName", UserInfoHolder.getCurrentUserName());//操作人姓名
	    	//paramsMap.put("deptId", );//科室ID
	    	//paramsMap.put("deptName", );//科室名称
	    	
	    	List<Map<String,Object>> list = new ArrayList<>(); 
	    	Map<String,Object> map = new HashMap<>();
	    	map.put("orgCode", hisRequestOrgCode);
	    	map.put("tradeNo", criticalValue.getMsgId());
	    	map.put("systemCode","YS012");//系统代码
	    	map.put("systemName", "OA");//系统名称
	    	map.put("status", "1");//系统名称
//	    	map.put("processUserId", criticalValue.getLeaderDoctorId());
//	    	map.put("processUserName", criticalValue.getDoctorName());
	    	map.put("processUserId", UserInfoHolder.getCurrentUserCode());
	    	map.put("processUserName", UserInfoHolder.getCurrentUserName());
	    	map.put("processDate", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
	    	if(StringUtils.isEmpty(record.getRemark())){
	    		map.put("processDesc", "已处理");
	    	}else{
	    		map.put("processDesc", record.getRemark());
	    	}
	    	map.put("remark", record.getRemark());
	    	list.add(map);
	    	
	    	paramsMap.put("list", list);
	       	
	       	String requestParams = JSON.toJSONString(paramsMap);
	       	
	    	log.info("调用   UpdateCriticalValueStatus请求的参数 :" + requestParams);
	    	
	       	String resultJson = HttpClientUtils.doPostJson(handleCriticalValue, requestParams);
	       	
	        log.info("返回的数据 :" + resultJson);
	       	
	       	record.setStatus(1);
	       	criticalValueMapper.updateByPrimaryKeySelective(record);
	       	
	       	ExternalLogs externalLogs = new ExternalLogs();
			externalLogs.setId(String.valueOf(IdWork.id.nextId()));
			externalLogs.setRequestUrl(handleCriticalValue);
			externalLogs.setRequestService("new处理危急值并回传给HIS");
			externalLogs.setRequestMethos("UpdateCriticalValueStatus");
			externalLogs.setRequestParams(requestParams);
			externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
			externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
			externalLogs.setCreateTime(new Date());
			externalLogs.setResponseParams(resultJson);
			externalLogsMapper.insertSelective(externalLogs);
			
			
			
		}else{
			CriticalValue criticalValue = criticalValueMapper.selectByPrimaryKey(record.getId());
	       	
	       	Map<String,Object> paramsMap = new HashMap<>();
	       	paramsMap.put("orgCode", hisRequestOrgCode);//机构编码
	       	
	       	
	       	Map<String, String> queryMember = hisApiService.queryMember(UserInfoHolder.getCurrentUserCode(),null);
	       	
	       	
	       	Map<String,Object> map = new HashMap<>();
	       	
	       	if("lyszyyy".equals(orgCode)) {
	       		paramsMap.put("operatorId", criticalValue.getBedDoctorCode());//操作人ID
		    	paramsMap.put("operatorName", criticalValue.getBedDoctorName());//操作人姓名
		    	
		    	map.put("processUserId", criticalValue.getBedDoctorCode());
		    	map.put("processUserName", criticalValue.getBedDoctorName());
	       	}else {
	       		paramsMap.put("operatorId", queryMember.get("uid"));//操作人ID
		    	paramsMap.put("operatorName", queryMember.get("username"));//操作人姓名
		    	
		    	map.put("processUserId", queryMember.get("uid"));
		    	map.put("processUserName", queryMember.get("username"));
	       	}
	       	
	    	
	    	
	    	List<Map<String,Object>> list = new ArrayList<>(); 
	    	
	    	map.put("orgCode", hisRequestOrgCode);
	    	map.put("tradeNo", criticalValue.getMsgId());
	    	map.put("systemCode","YS012");//系统代码
	    	map.put("systemName", "OA");//系统名称
	    	map.put("status", "1");//系统名称
	    	map.put("processDate", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
	    	if(StringUtils.isEmpty(record.getRemark())){
	    		map.put("processDesc", "已处理");
	    	}else{
	    		map.put("processDesc", record.getRemark());
	    	}
	    	map.put("remark", record.getRemark());
	    	list.add(map);
	    	
	    	paramsMap.put("list", list);
	       	
	       	String requestParams = JSON.toJSONString(paramsMap);
	       	
	    	log.info("调用   UpdateCriticalValueStatus请求的参数 :" + requestParams);
	    	
	       	String resultJson = HttpClientUtils.doPostJson(handleCriticalValue + "?appId=" + appid, requestParams);
	       	
	        log.info("返回的数据 :" + resultJson);
	       	
	       	record.setStatus(1);
	       	criticalValueMapper.updateByPrimaryKeySelective(record);
	       	
	       	ExternalLogs externalLogs = new ExternalLogs();
			externalLogs.setId(String.valueOf(IdWork.id.nextId()));
			externalLogs.setRequestUrl(handleCriticalValue);
			externalLogs.setRequestService("new处理危急值并回传给HIS");
			externalLogs.setRequestMethos("UpdateCriticalValueStatus");
			externalLogs.setRequestParams(requestParams);
			externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
			externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
			externalLogs.setCreateTime(new Date());
			externalLogs.setResponseParams(resultJson);
			externalLogsMapper.insertSelective(externalLogs);
		}
//		}else{
//			Map<String,Object> params = new HashMap<>();
//	       	params.put("IsCompress", "false");
//	       	params.put("ServiceName", "Lis-InterFaceService");//服务名
//	       	params.put("InterfaceName", "UpdateCriticalValueStatus");//接口名
//	    	params.put("IfTokenExpiredThenReloginAndRetry", "false");
//	       	params.put("TimeOut", 15000);//超时时间
//	       	
//	       	CriticalValue criticalValue = criticalValueMapper.selectByPrimaryKey(record.getId());
//	       	
//	       	Map<String,Object> paramsMap = new HashMap<>();
//	       	paramsMap.put("ORG_CODE", hisRequestOrgCode);//机构编码
//	       	paramsMap.put("HOSP_CODE", hisRequestHospCode);//#院区编码
//	       	List<String> ID_LIST = new ArrayList<>();
//	       	ID_LIST.add(criticalValue.getMsgId());  //这个id现在是有问题的 要拿lis传过来的id  
//	    	paramsMap.put("ID_LIST", ID_LIST);   
//	    	paramsMap.put("STATUS", "1");
//	    	paramsMap.put("PROCESS_USER_ID", criticalValue.getDoctorId());
//	    	paramsMap.put("PROCESS_USER_NAME", criticalValue.getDoctorName());
//	    	paramsMap.put("PROCESS_EMP_NAME", criticalValue.getDoctorName());
//	    	paramsMap.put("REMARK", record.getRemark());
//	    	paramsMap.put("PROCESS_DATE", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
//	    	
//	       	params.put("Parameter",paramsMap);
//	       	
//	       	String requestParams = JSON.toJSONString(params);
//	       	
//	    	log.info("调用   UpdateCriticalValueStatus请求的参数 :" + requestParams);
//	    	
//	       	String resultJson = HttpClientUtils.doPostJson(hisRequestUrl, requestParams);
//	       	
//	        log.info("返回的数据 :" + resultJson);
//	       	
//	       	record.setStatus(1);
//	       	criticalValueMapper.updateByPrimaryKeySelective(record);
//	       	
//	       	ExternalLogs externalLogs = new ExternalLogs();
//			externalLogs.setId(String.valueOf(IdWork.id.nextId()));
//			externalLogs.setRequestUrl(hisRequestUrl);
//			externalLogs.setRequestService("处理危急值并回传给HIS");
//			externalLogs.setRequestMethos("UpdateCriticalValueStatus");
//			externalLogs.setRequestParams(requestParams);
//			externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
//			externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
//			externalLogs.setCreateTime(new Date());
//			externalLogs.setResponseParams(resultJson);
//			externalLogsMapper.insertSelective(externalLogs);
//		}
	}

	@Override
	@Transactional(readOnly = false)
	public void newReceiveCriticalValue(Map<String, Object> paramsJson) {
		long startTime = System.currentTimeMillis();    //获取开始时间
		
		Map<String,Object> body = (Map<String, Object>) paramsJson.get("body");
		
		PlatformResult<GlobalSetting> result = globalSettingsFeignService.getSafeGlobalSetting("Y");
		
		GlobalSetting globalSetting = result.getObject();
		
		String criticalValueId = String.valueOf(IdWork.id.nextId());
		CriticalValue criticalValue = new CriticalValue();
		criticalValue.setId(criticalValueId);
		criticalValue.setTitle("危急值报告详情单【" + (String) body.get("reportNo") + "】");
		criticalValue.setMsgId((String) body.get("tradeNo")); //唯一id
		criticalValue.setReportNo((String) body.get("reportNo"));  //检验单号
		criticalValue.setSampleName((String) body.get("sampleName")); //样本名称
		criticalValue.setPatientName((String) body.get("name")); //患者姓名
		criticalValue.setDeptName((String) body.get("requestDeptName")); //患者科室
		criticalValue.setDeptId((String) body.get("requestDeptId")); //患者科室id
		criticalValue.setBedNo((String) body.get("bedNo")); //床号
		criticalValue.setRegNo((String) body.get("visitNo")); //病历号
		
		if("3".equals(version)) {
			criticalValue.setBedDoctorCode((String) body.get("bedDoctorCode")); //管床医生工号
			criticalValue.setBedDoctorAccount((String) body.get("bedDoctorCode")); //管床医生账号
			criticalValue.setBedDoctorName((String) body.get("bedDoctorName")); //主管医生姓名
			criticalValue.setDoctorId((String) body.get("bedDoctorCode"));
			criticalValue.setDoctorName((String) body.get("bedDoctorName"));
		}else {
			if("lyszyyy".equals(globalSetting.getOrgCode())) {
				criticalValue.setBedDoctorCode((String) body.get("bedDoctorCode")); //管床医生工号
				criticalValue.setBedDoctorAccount((String) body.get("bedDoctorAccount")); //管床医生账号
				criticalValue.setBedDoctorName((String) body.get("bedDoctorName")); //主管医生姓名
				criticalValue.setDoctorId((String) body.get("bedDoctorAccount"));
				criticalValue.setDoctorName((String) body.get("bedDoctorName"));
			}else {
				
				Map<String, String> queryMember = hisApiService.queryMember(null,(String) body.get("bedDocId"));
				String ucode = queryMember.get("ucode");
				String username = queryMember.get("username");
				criticalValue.setBedDoctorCode(ucode); //管床医生工号
				criticalValue.setBedDoctorAccount(ucode); //管床医生账号
				criticalValue.setBedDoctorName(username); //主管医生姓名
				criticalValue.setDoctorId(ucode);
				criticalValue.setDoctorName(username);
			}
		}
		
		
		criticalValue.setReceiveUser((String) body.get("requestDocName")); //开单医生姓名
		criticalValue.setSignDate((String) body.get("reportDate")); //危急值报告时间
		criticalValue.setIsRead(0);
		criticalValue.setStatus(0);
		criticalValue.setCreateTime(new Date());
		
		//危急值内容详情
		List<Map<String,Object>> requestList = (List<Map<String, Object>>) body.get("requestList");
		
		for (Map<String, Object> map : requestList) {
			System.out.println(map.get("panicValue"));
		}
		
		criticalValue.setCriticaiValueJson((JSON.toJSONString(requestList))); 
		
		criticalValueMapper.insertSelective(criticalValue);
		
		StringBuffer contentSb = new StringBuffer();
		
		contentSb.append("<div class=\"normal\">病人姓名：").append((String) body.get("name")).append("</div><br>");
		contentSb.append("<div class=\"highlight\">病人科室：").append((String) body.get("requestDeptName")).append("</div><br>");
		contentSb.append("<div class=\"highlight\">床位号：").append((String) body.get("bedNo")).append("</div><br>");
		
		//推送微信消息
		NoticeReq notice = NoticeReq.builder()
					.content(contentSb.toString())
					.noticeType("3")
					.receiver(criticalValue.getBedDoctorAccount())
					.sender("admin")
					.senderName("admin")
					.subject("危急值提醒")
					.wxSendType("1")
					.url(wxLoginUrl + "pages/criticalValues/critical-values-detail?id="+criticalValueId)//要跳转的url + criticalValueId
					.build();
		
		NoticeService informationFeignClient = new NoticeService();
		informationFeignClient.sendAsynNotice(notice);
		
		long endTime = System.currentTimeMillis();    //获取结束时间
		
		ExternalLogs externalLogs = new ExternalLogs();
		externalLogs.setId(String.valueOf(IdWork.id.nextId()));
		externalLogs.setRequestService("危急值提醒接口");
		externalLogs.setRequestMethos("receiveCriticalValue");
		externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
		externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
		externalLogs.setCreateTime(new Date());
		externalLogs.setResponseParams(JSONObject.toJSONString(paramsJson));
		externalLogs.setTakeTime((endTime - startTime) + "ms");
		externalLogsMapper.insertSelective(externalLogs);
	}

	@Override
	@Transactional(readOnly = false)
	public void receiveHandleCriticalValue(Map<String, Object> paramsJson) {
		
		Map<String,Object> body = (Map<String, Object>) paramsJson.get("body");
		
		String tradeNo = (String) body.get("tradeNo");
		
		criticalValueMapper.updateByMsgId(tradeNo);
		
		ExternalLogs externalLogs = new ExternalLogs();
		externalLogs.setId(String.valueOf(IdWork.id.nextId()));
		externalLogs.setRequestService("危急值处理接口");
		externalLogs.setRequestMethos("receiveCriticalValue");
		externalLogs.setCreateUser(UserInfoHolder.getCurrentUserCode());
		externalLogs.setCreateUserName(UserInfoHolder.getCurrentUserName());
		externalLogs.setCreateTime(new Date());
		externalLogs.setResponseParams(JSONObject.toJSONString(paramsJson));
		externalLogsMapper.insertSelective(externalLogs);
	}
	
	

}
