package cn.trasen.homs.base.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.trasen.BootComm.utils.CommTree;
import cn.trasen.homs.base.contants.CommonContants;
import cn.trasen.homs.base.mapper.HrmsJobtitleBasicMapper;
import cn.trasen.homs.base.model.HrmsJobtitleBasic;
import cn.trasen.homs.base.service.HrmsJobtitleBasicService;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsJobtitleBasicServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 职称基础信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年4月23日 上午10:18:20 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsJobtitleBasicServiceImpl implements HrmsJobtitleBasicService {

    @Autowired
    HrmsJobtitleBasicMapper hrmsJobtitleBasicMapper;

    /**
     * @Title: insert
     * @Description: 新增职称基础信息
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int insert(HrmsJobtitleBasic entity) {
        entity.setJobtitleBasicId(String.valueOf(IdWork.id.nextId()));
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
        entity.setCreateDate(new Date());
        HrmsJobtitleBasic parent = null;
        if (StringUtils.isNotBlank(entity.getJobtitleBasicPid())) {
            parent = hrmsJobtitleBasicMapper.selectByPrimaryKey(entity.getJobtitleBasicPid());
            if (parent != null) {
                entity.setTreeIds(parent.getTreeIds() + "," + entity.getJobtitleBasicId());
                int grade = parent.getJobtitleBasicGrade().intValue();
                grade = grade + 1;
                entity.setJobtitleBasicGrade(grade);
                if (StringUtils.isBlank(entity.getClassificationName())) { // 如果分类名称没有填写，跟上级保持一致
                    entity.setClassificationName(parent.getClassificationName());
                }
            }
        } else {
            entity.setJobtitleBasicGrade(1);
            entity.setTreeIds(entity.getJobtitleBasicId());
        }
        return hrmsJobtitleBasicMapper.insert(entity);
    }

    /**
     * @Title: update
     * @Description: 更新职称基础信息
     * @Param: entity
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int update(HrmsJobtitleBasic entity) {
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        entity.setUpdateDate(new Date());
        HrmsJobtitleBasic parent = null;
        if (StringUtils.isNotBlank(entity.getJobtitleBasicPid())) {
            parent = hrmsJobtitleBasicMapper.selectByPrimaryKey(entity.getJobtitleBasicPid());
            if (parent != null) {
                entity.setTreeIds(parent.getTreeIds() + "," + entity.getJobtitleBasicId());
                int grade = parent.getJobtitleBasicGrade().intValue();
                grade = grade + 1;
                entity.setJobtitleBasicGrade(grade);
            }
        } else {
            entity.setJobtitleBasicGrade(1);
            entity.setTreeIds(entity.getJobtitleBasicId());
        }
        return hrmsJobtitleBasicMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * @Title: deleted
     * @Description: 删除职称基础信息
     * @Param: id
     * @Return: int
     * <AUTHOR>
     */
    @Override
    @Transactional(readOnly = false)
    public int deleted(String id) {
        HrmsJobtitleBasic jobtitleBasic = hrmsJobtitleBasicMapper.selectByPrimaryKey(id);
        if (jobtitleBasic != null) {
            jobtitleBasic.setIsDeleted(Contants.IS_DELETED_TURE);
        }
        return hrmsJobtitleBasicMapper.updateByPrimaryKeySelective(jobtitleBasic);
    }

    /**
     * @Title: getDataList
     * @Description: 查询职称信息列表(分页)
     * @Param: page
     * @param entity
     * @Return: List<HrmsJobtitleBasic>
     * <AUTHOR>
     */
    @Override
    public List<HrmsJobtitleBasic> getDataList(Page page, HrmsJobtitleBasic entity) {
        Example example = new Example(HrmsJobtitleBasic.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
        if (StringUtils.isNotBlank(entity.getJobtitleBasicPid())) { // 父级ID
            example.and().andLike("treeIds", "%" + entity.getJobtitleBasicPid() + "%");
        }
        if (StringUtils.isNotBlank(entity.getJobtitleBasicName())) { // 父级ID
            example.and().andLike("jobtitleBasicName", "%" + entity.getJobtitleBasicName().trim() + "%");
        }

        List<HrmsJobtitleBasic> list = hrmsJobtitleBasicMapper.selectByExampleAndRowBounds(example, page);
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询所有职称信息列表
            Example example1 = new Example(HrmsJobtitleBasic.class);
            example1.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
            List<HrmsJobtitleBasic> allJobtitle = hrmsJobtitleBasicMapper.selectByExample(example1);
            Map<String, HrmsJobtitleBasic> jobtitleMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(allJobtitle)) {
                for (HrmsJobtitleBasic item : allJobtitle) {
                    jobtitleMap.put(item.getJobtitleBasicId(), item);
                }
            }

            for (HrmsJobtitleBasic o : list) {
                if (o.getJobtitleBasicGrade().intValue() == 2) {
                    HrmsJobtitleBasic parent = jobtitleMap.get(o.getJobtitleBasicPid());
                    if (parent != null) {
                        o.setJobtitleBasicPName(parent.getJobtitleBasicName());
                    }
                } else if (o.getJobtitleBasicGrade().intValue() == 3) {
                    HrmsJobtitleBasic parent = jobtitleMap.get(o.getJobtitleBasicPid());
                    if (parent != null) {
                        o.setJobtitleBasicPName(parent.getJobtitleBasicName());
                        HrmsJobtitleBasic topParent = jobtitleMap.get(parent.getJobtitleBasicPid());
                        if (topParent != null) {
                            o.setJobtitleCategoryId(topParent.getJobtitleBasicId());
                            o.setJobtitleCategoryName(topParent.getJobtitleBasicName());
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * @Title: getJobtitleCategory
     * @Description: 查询职称类别列表
     * @param entity
     * @Return List<HrmsJobtitleBasic>
     * <AUTHOR>
     * @date 2020年4月23日 上午11:28:18
     */
    @Override
    public List<HrmsJobtitleBasic> getJobtitleCategory(HrmsJobtitleBasic entity) {
        entity.setJobtitleBasicPid(CommonContants.JOBTITLE_BASE_PID);
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        //根据当前登录账号机构编码过滤查询数据
//        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsJobtitleBasicMapper.getListByPid(entity);
    }

    /**
     * @Title: getListByPid
     * @Description: 根据Pid查询职称基础数据列表
     * @param entity
     * @Return List<HrmsJobtitleBasic>
     * <AUTHOR>
     * @date 2020年4月23日 上午11:46:24
     */
    @Override
    public List<HrmsJobtitleBasic> getListByPid(HrmsJobtitleBasic entity) {
        Assert.hasText(entity.getJobtitleBasicPid(), "pid must be not null.");
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
//        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        return hrmsJobtitleBasicMapper.getListByPid(entity);
    }

    /**
     * @Title: getJobtitleTree
     * @Description: 获取职称信息树
     * @Return List<TreeModel>
     * <AUTHOR>
     * @date 2020年6月12日 上午9:38:51
     */
    @Override
    public List<TreeModel> getJobtitleTree() {
        Example example = new Example(HrmsJobtitleBasic.class);
        example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);

        List<HrmsJobtitleBasic> jobtitleBasicList = hrmsJobtitleBasicMapper.selectByExample(example);
        List<TreeModel> trees = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(jobtitleBasicList)) {
            List<TreeModel> nodes = Lists.newLinkedList();
            jobtitleBasicList.stream().forEach(item -> {
                TreeModel node = new TreeModel();
                node.setId(item.getJobtitleBasicId());
                node.setName(item.getJobtitleBasicName());
                if (StringUtils.isBlank(item.getJobtitleBasicPid()) || "0".equals(item.getJobtitleBasicPid())) {
                    node.setPid("");
                } else {
                    node.setPid(item.getJobtitleBasicPid());
                }
                nodes.add(node);
            });
            CommTree commTree = new CommTree();
            trees = commTree.CommTreeList(nodes);
        }
        return trees;
    }

    @Override
    public List<HrmsJobtitleBasic> getJobtitleBasicList(HrmsJobtitleBasic entity)  {
        Example example = new Example(HrmsJobtitleBasic.class);
        example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
        if(null != entity.getJobtitleBasicGrade()) {
            example.and().andEqualTo("jobtitleBasicGrade",entity.getJobtitleBasicGrade());
        }
        return hrmsJobtitleBasicMapper.selectByExample(example);
    }

}
