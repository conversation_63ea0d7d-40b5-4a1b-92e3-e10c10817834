<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.homs.base.mapper.CommOrganizationContactsMapper">
	<resultMap id="BaseResultMap"
		type="cn.trasen.homs.base.model.CommOrganizationContacts">
		<!--
          WARNING - @mbg.generated
        -->
		<result column="id" jdbcType="VARCHAR" property="id" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="tel" jdbcType="VARCHAR" property="tel" />
		<result column="sort" jdbcType="INTEGER" property="sort" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR"
			property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR"
			property="updateUserName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>
	
	<select id="getDataSetList" resultType="cn.trasen.homs.base.model.CommOrganizationContacts"> 
		SELECT t2.name AS org_name, t1.*  FROM comm_organization_contacts t1 
		LEFT JOIN comm_organization t2 ON t1.org_id = t2.organization_id 
		WHERE t1.is_deleted = 'N' 
		<if test="id != null and id.size()>0 "> 
			AND t2.organization_id in 
			<foreach item="orgId" index="index" collection="id" open="(" separator="," close=")">
				#{orgId, jdbcType=VARCHAR}
			</foreach>
		</if>
        <if test="keyword != null and keyword !=''">
			and (
				t1.tel like concat('%',#{keyword},'%')
				or t2.name like concat('%',#{keyword},'%')
				or t1.name like concat('%',#{keyword},'%')
			)
		</if> 
		order by t2.org_level asc, t2.seq_no asc, t2.organization_id, t1.create_date desc 
	</select>
	
	<select id="getExternalDataSetList" resultType="cn.trasen.homs.base.model.CommOrganizationContacts"> 
		SELECT t1.*,t1.external_org_name as orgName  FROM comm_organization_contacts t1 
		WHERE t1.is_deleted = 'N' 
        <if test="keyword != null and keyword !=''">
			and (
				t1.tel like concat('%',#{keyword},'%')
				or t1.name like concat('%',#{keyword},'%')
				or t1.external_org_name like concat('%',#{keyword},'%')
			)
		</if> 
		order by t1.sort
	</select>
	
	<insert id="batchInsert">
        <![CDATA[
			INSERT INTO comm_organization_contacts
			(
				id,
				org_id,
				external_org_name,
				name,
				tel,
				sort,
				create_date,
				create_user,
				create_user_name,
				update_date,
				update_user,
				update_user_name,
				is_deleted
			)
			VALUES
		]]>
		<foreach collection="list" item="item" index="index" separator=",">
            <![CDATA[
			(
				#{item.id},
				#{item.orgId},
				#{item.orgName},
				#{item.name},
				#{item.tel},
				#{item.sort},
				#{item.createDate},
				#{item.createUser},
				#{item.createUserName},
				#{item.updateDate},
				#{item.createUser},
				#{item.updateUser},
				#{item.isDeleted}
			)
			]]>
        </foreach>
	</insert>
	<update id="deleteAll">
		update comm_organization_contacts
		set is_deleted='Y'
	</update>
</mapper>
