package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.DTO.*;
import cn.trasen.hrms.salary.dao.*;
import cn.trasen.hrms.salary.enums.CarryRuleEnum;
import cn.trasen.hrms.salary.model.*;
import cn.trasen.hrms.salary.service.*;
import cn.trasen.hrms.salary.utils.FormulaParse;
import cn.trasen.hrms.salary.utils.VueTableEntity;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.utils.ListUtils;
import com.alibaba.druid.sql.dialect.oracle.ast.expr.OracleDateTimeUnit;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.udojava.evalex.Expression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName: HrmsNewsalaryOptionPayrollServiceImpl
 * @Description: 薪酬方案核算实现V2.0版
 * @date 2024年6月27日 下午5:31:26
 */
@Slf4j
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryOptionPayrollServiceImpl implements HrmsNewsalaryOptionPayrollService {

    @Resource
    private HrmsNewsalaryOptionPayrollMapper mapper;
    @Autowired
    HrmsNewsalaryOptionService hrmsNewsalaryOptionService;
    @Autowired
    HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;
    @Resource
    HrmsNewsalaryOptionEmpMapper hrmsNewsalaryOptionEmpMapper;
    @Resource
    HrmsNewsalaryBasicitemEmpMapper hrmsNewsalaryBasicitemEmpMapper;
    @Resource
    DictItemFeignService dictItemFeignService;
    @Autowired
    HrmsNewsalaryBasicColumnService hrmsNewsalaryBasicColumnService;
    @Autowired
    HrmsNewsalaryItemService hrmsNewsalaryItemService;
    @Autowired
    HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;
    @Autowired
    HrmsNewsalaryPayrollService hrmsNewsalaryPayrollService;
    @Resource
    HrmsNewsalaryPayrollMapper hrmsNewsalaryPayrollMapper;
    @Autowired
    HrmsNewsalaryPayrollDetailService hrmsNewsalaryPayrollDetailService;
    @Resource
    HrmsNewsalaryOptionPayrollService hrmsNewsalaryOptionPayrollService;
    @Autowired
    HrmsNewsalaryFirststepHistoryService hrmsNewsalaryFirststepHistoryService;
    @Autowired
    HrmsNewsalarySecondstepTitleHistoryService hrmsNewsalarySecondstepTitleHistoryService;
    @Autowired
    HrmsNewsalarySecondstepHistoryService hrmsNewsalarySecondstepHistoryService;
    @Resource
    HrmsNewsalaryOptionMapper hrmsNewsalaryOptionMapper;
    @Autowired
    HrmsNewsalaryPayrollDetailImportService hrmsNewsalaryPayrollDetailImportService;
    @Autowired
    HrmsNewsalarySeniorityWageService hrmsNewsalarySeniorityWageService;
    @Autowired
    HrmsNewsalarySeniorityWageEfService hrmsNewsalarySeniorityWageEfService;
    @Resource
    private HrmsEmployeeMapper hrmsEmployeeMapper;
    @Resource
    private HrmsOrganizationFeignService hrmsOrganizationFeignService;
    @Autowired
    private HrmsNewsalaryItemGroupService hrmsNewsalaryItemGroupService;
    @Resource
    private HrmsNewsalaryItemMapper itemMapper;
    @Resource
    private HrmsNewsalaryReportTotalMapper totalMapper;
    @Autowired
    private HrmsNewsalaryTemporaryAdjustService newsalaryTemporaryAdjustService;
    @Resource
    private HrmsNewsalaryPayrollMapper newsalaryPayrollMapper;
    @Autowired
    private HrmsNewsalaryItemRemindRecordService newsalaryItemRemindRecordService;

    @Resource
    private HrmsNewsalaryReportMapMapper newsalaryReportMapMapper;

    @Autowired
    private HrmsNewsalaryReportsStatisticsService newsalaryReportsStatisticsService;

    @Resource
    private HrmsNewsalaryChangesDetailedMapper hrmsNewsalaryChangesDetailedMapper;

    @Resource
    private HrmsNewsalaryItemRemindRecordMapper hrmsNewsalaryItemRemindRecordMapper;

    @Autowired
    private HrmsNewsalaryRemindSettingService hrmsNewsalaryRemindSettingService;

    @Resource
    private InformationFeignService informationFeignService;

    //	@Transactional(readOnly = false)
    @Override
    public Integer save(HrmsNewsalaryOptionPayroll record) {
        if (StringUtil.isEmpty(record.getId())) {
            record.setId(IdUtil.getId());
        }
        record.setCreateDate(new Date());
        record.setUpdateDate(new Date());
        record.setIsDeleted("N");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setCreateUser(user.getUsercode());
            record.setCreateUserName(user.getUsername());
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.insertSelective(record);
    }

    @Transactional(readOnly = false)
    @Override
    public Integer update(HrmsNewsalaryOptionPayroll record) {
        record.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        int count  = mapper.updateByPrimaryKeySelective(record);
        //锁定薪酬调整明细
        if("3".equals(record.getComputeStatus())){
            record = mapper.selectByPrimaryKey(record.getId());
            Example example = new Example(HrmsNewsalaryPayroll.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("optionId",record.getOptionId());
            criteria.andEqualTo("payrollDate",record.getComputeDate());
            criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            List<HrmsNewsalaryPayroll> payrolls = newsalaryPayrollMapper.selectByExample(example);
            List<String> employeeIds = payrolls.stream().map(HrmsNewsalaryPayroll::getEmployeeId).collect(Collectors.toList());
            newsalaryTemporaryAdjustService.lockAdjuct(employeeIds,record.getComputeDate());
        }
        return count;
    }

    //	@Transactional(readOnly = false)
    @Override
    public Integer deleteById(String id) {
        Assert.hasText(id, "ID不能为空.");
        HrmsNewsalaryOptionPayroll record = new HrmsNewsalaryOptionPayroll();
        record.setId(id);
        record.setUpdateDate(new Date());
        record.setIsDeleted("Y");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            record.setUpdateUser(user.getUsercode());
            record.setUpdateUserName(user.getUsername());
        }
        return mapper.updateByPrimaryKeySelective(record);
    }

    //	@Transactional(readOnly = false)
    @Override
    public Integer deleteByExample(String optionId, String date) {
        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("optionId", optionId);
        criteria.andEqualTo("computeDate", date);
        return mapper.deleteByExample(example);
    }

    @Override
    public HrmsNewsalaryOptionPayroll selectById(String id) {
        Assert.hasText(id, "ID不能为空.");
        return mapper.selectByPrimaryKey(id);
    }

    @Override
    public DataSet<HrmsNewsalaryOptionPayroll> getDataSetList(Page page, HrmsNewsalaryOptionPayroll record) {
        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        List<HrmsNewsalaryOptionPayroll> records = mapper.selectByExampleAndRowBounds(example, page);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
    }

    // 薪酬核算列表
    @Override
    public DataSet<HrmsNewsalaryOptionPayroll> calculateList(Page page, HrmsNewsalaryOptionPayroll record) {

        String computeDate = record.getComputeDate(); // 薪酬核算月份
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
        }
//		1.根据薪酬组和月份查询是否有历史数据
        List<HrmsNewsalaryOptionPayroll> data = getDataByComputeDate(page, record);
        if (!CollUtil.isEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                if (!"3".equals(data.get(i).getComputeStatus()) && !"4".equals(data.get(i).getComputeStatus())) {
                    List<HrmsNewsalaryOptionEmp> empList = hrmsNewsalaryOptionEmpService
                            .getAllByOptionId(data.get(i).getOptionId(), null);
                    // 定薪调薪人数
                    List<String> sbids = new ArrayList<>();
                    if (!empList.isEmpty()) {
                        empList.forEach(item -> {
                            sbids.add(item.getEmployeeId());
                        });
                    }
                    Integer setCount = getSetCount(computeDate, data.get(i).getOptionId(), sbids);
                    Integer updateCount = getUpdateCount(computeDate, data.get(i).getOptionId(), sbids);
                    data.get(i).setHeadCount(getHeadCount(data.get(i).getOptionId())); // 算薪人数
                    if (null == data.get(i).getSetCount()) {
                        data.get(i).setSetCount(setCount); // 定薪人数
                    }
                    if (null == data.get(i).getUpdateCount()) {
                        data.get(i).setUpdateCount(updateCount); // 调薪人数
                    }
                }
                if (StringUtil.isEmpty(data.get(i).getComputeStatus())) {
                    data.get(i).setComputeStatus("0");
                }
                data.get(i).setComputeDate(computeDate);
            }
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), data);
    }

    // 查询定薪人数
    private Integer getHeadCount(String optionId) {
        List<HrmsNewsalaryOptionEmp> allByOptionId = hrmsNewsalaryOptionEmpService.getAllByOptionId(optionId, null);
        return allByOptionId.size();
    }

    // 查询定薪人数
    private Integer getSetCount(String computeDate, String optionId, List<String> empIds) {
        Integer setCount = mapper.getSetCount(computeDate, optionId, empIds);
        if (null != setCount) {
            return setCount;
        }
        return 0;
    }

    private Integer getUpdateCount(String computeDate, String optionId, List<String> empIds) {
        Integer updateCount = mapper.getUpdateCount(computeDate, optionId, empIds);
        if (null != updateCount) {
            return updateCount;
        }
        return 0;
    }

    private List<HrmsNewsalaryOptionPayroll> getDataByComputeDate(Page page, HrmsNewsalaryOptionPayroll record) {
        List<HrmsNewsalaryOptionPayroll> records = mapper.getDataByComputeDate(page, record);
        return records;
    }

    // 核对人员接口
    @Override
    public DataSet<CheckPersonnel> checkPersonnel(Page page, SearchListTable record) {

        Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
        Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
        Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类别

        // 时间
        String payrollDate = record.getPayrollDate(); // 核算时间/checkPersonnel
        if (StringUtil.isEmpty(payrollDate)) {
            payrollDate = DateUtils.getStringDateShortYM(new Date());
            record.setPayrollDate(payrollDate);
        }
        if (StrUtil.isNotBlank(record.getOrgId())) {
            PlatformResult<List<String>> orgResult = hrmsOrganizationFeignService
                    .getHrmsOrganizationAndNextList(record.getOrgId());
            List<String> orgList = orgResult.getObject();
            record.setOrgList(orgList);
        }
        List<CheckPersonnel> list = null;
        list = mapper.checkPersonnel(page, record);
        // 处理数据字典
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setPersonalIdentityText(personalIdentityDictMap.get(list.get(i).getPersonalIdentity()));
                list.get(i).setEstablishmentTypeText(establishmentTypeDictMap.get(list.get(i).getEstablishmentType()));
                list.get(i).setEmployeeStatusText(employeeStatusDictMap.get(list.get(i).getEmployeeStatus()));
            }
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

    // 薪酬核算定薪调薪列表表头
    @Override
    public List<VueTableEntity> makePayTableTitle(HrmsNewsalaryOptionPayroll record) {
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();

        // 判断是否有历史数据
        HrmsNewsalaryOptionPayroll hnop = new HrmsNewsalaryOptionPayroll();
        hnop.setOptionId(record.getOptionId());
        hnop.setComputeDate(record.getComputeDate());
        HrmsNewsalaryOptionPayroll completeInfo = hrmsNewsalaryOptionPayrollService.getCalculationStatus(hnop);

        if (!"2".equals(completeInfo.getComputeStatus()) && !"3".equals(completeInfo.getComputeStatus())) {
            // 根据分组查询所有数据
            List<BasicItemVO> retmap = mapper.makePayTableTitle(record);
            int width = 20;
            if (!retmap.isEmpty() && retmap.size() > 0) {
                retmap.forEach(item -> {
                    // 处理岗位工资和薪级工资
                    if ("1".equals(item.getCompare())) {
                        retVueTableEntity.add(new VueTableEntity("(调薪前)" + item.getBasicItemName(),
                                "before_" + item.getBasicItemId(), null, 160, null));
                        retVueTableEntity.add(new VueTableEntity("(调薪后)" + item.getBasicItemName(),
                                item.getBasicItemId(), null, 160, null));
                    } else {
                        if ("姓名".equals(item.getBasicItemName())) {
                            retVueTableEntity.add(
                                    new VueTableEntity(item.getBasicItemName(), "employee_name", null, 80, "left"));
                        } else if ("工号".equals(item.getBasicItemName())) {
                            retVueTableEntity
                                    .add(new VueTableEntity(item.getBasicItemName(), "employee_no", null, 80, "left"));
                        } else if ("部门".equals(item.getBasicItemName())) {
                            retVueTableEntity
                                    .add(new VueTableEntity(item.getBasicItemName(), "org_name", null, 80, "left"));
                        } else if ("员工状态".equals(item.getBasicItemName())) {
                            retVueTableEntity.add(
                                    new VueTableEntity(item.getBasicItemName(), "employee_status", null, 80, "left"));
                        } else if ("岗位名称".equals(item.getBasicItemName())) {
                            retVueTableEntity.add(
                                    new VueTableEntity(item.getBasicItemName(), "personal_identity", null, 80, null));
                        } else if ("编制类型".equals(item.getBasicItemName())) {
                            retVueTableEntity.add(
                                    new VueTableEntity(item.getBasicItemName(), "establishment_type", null, 80, null));
                        } else if ("入职日期".equals(item.getBasicItemName())) {
                            retVueTableEntity
                                    .add(new VueTableEntity(item.getBasicItemName(), "entry_date", null, 80, null));
                        } else if ("转正日期".equals(item.getBasicItemName())) {
                            retVueTableEntity
                                    .add(new VueTableEntity(item.getBasicItemName(), "positive_time", null, 80, null));
                        } else if ("离退休日期".equals(item.getBasicItemName())) {
                            retVueTableEntity.add(
                                    new VueTableEntity(item.getBasicItemName(), "retirement_time", null, 80, null));
                        } else if ("薪酬组".equals(item.getBasicItemName())) {
                            // retVueTableEntity.add(new
                            // VueTableEntity(item.getBasicItemName(),"option_name",null,80,null));
                        } else {
                            retVueTableEntity.add(new VueTableEntity(item.getBasicItemName(), item.getBasicItemId(),
                                    null, item.getBasicItemName().length() == 2 ? 80 : item.getBasicItemName().length() * width, null));
                        }
                    }
                });
            }

            retVueTableEntity.add(new VueTableEntity("最近修改人", "update_user_name", null, 120, null));
            retVueTableEntity.add(new VueTableEntity("最近修改日期", "update_date", null, 120, null));
            return retVueTableEntity;
        } else { // 查历史数据
            HrmsNewsalarySecondstepTitleHistory historyTitle = hrmsNewsalarySecondstepTitleHistoryService
                    .getData(record.getOptionId(), record.getComputeDate());
            if (null != historyTitle) {
                String val = historyTitle.getVal();
                ObjectMapper mapper = new ObjectMapper();
                try {
                    List<VueTableEntity> cellList = mapper.readValue(val, new TypeReference<List<VueTableEntity>>() {
                    });
                    retVueTableEntity.addAll(cellList);
                } catch (Exception e) {
                    log.error("历史表头获取出错" + e.getMessage(), e);
                }
            }
            return retVueTableEntity;
        }

    }

    @Override
    public List<VueTableEntity> makePayTableTitleCount(HrmsNewsalaryOptionPayroll record) {
        int width = 20;
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        // 根据分组查询所有数据
        List<BasicItemVO> retmap = mapper.makePayTableTitleCount(record);
        if (!retmap.isEmpty() && retmap.size() > 0) {
            retmap.forEach(item -> {
                // 处理岗位工资和薪级工资
                if ("1".equals(item.getCompare())) {
                    retVueTableEntity.add(new VueTableEntity("(调薪前)" + item.getBasicItemName(),
                            "before_" + item.getBasicItemId(), null, null, null));
                    retVueTableEntity.add(new VueTableEntity("(调薪后)" + item.getBasicItemName(), item.getBasicItemId(),
                            null, null, null));
                } else {
                    if ("姓名".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "employee_name", null, null, null));
                    } else if ("工号".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "employee_no", null, null, null));
                    } else if ("部门".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "org_name", null, null, null));
                    } else if ("员工状态".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "employee_status", null, null, null));
                    } else if ("岗位名称".equals(item.getBasicItemName())) {
                        retVueTableEntity.add(
                                new VueTableEntity(item.getBasicItemName(), "personal_identity", null, null, null));
                    } else if ("编制类别".equals(item.getBasicItemName())) {
                        retVueTableEntity.add(
                                new VueTableEntity(item.getBasicItemName(), "employee_category", null, null, null));
                    } else if ("入职日期".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "entry_date", null, null, null));
                    } else if ("转正日期".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "positive_time", null, null, null));
                    } else if ("离退休日期".equals(item.getBasicItemName())) {
                        retVueTableEntity
                                .add(new VueTableEntity(item.getBasicItemName(), "retirement_time", null, null, null));
                    } else {
                        retVueTableEntity.add(
                                new VueTableEntity(item.getBasicItemName(), item.getBasicItemId(), null, item.getBasicItemName().length() == 2 ? 80 : item.getBasicItemName().length() * width, null));
                    }
                }
            });
        }

        retVueTableEntity.add(new VueTableEntity("最近修改人", "update_user_name", null, null, null));
        retVueTableEntity.add(new VueTableEntity("最近修改日期", "update_date", null, null, null));
        return retVueTableEntity;
    }

    // 定薪调薪接口数据
    @Override
    public DataSet<Map<String, String>> makePayTableData(Page page, HrmsNewsalaryOptionPayroll record) {

        Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
        Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
        Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类别
        List<Map<String, String>> retRows = null;

        String computeDate = record.getComputeDate(); // 薪酬核算月份
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
        }

        HrmsNewsalaryOptionPayroll hnop = new HrmsNewsalaryOptionPayroll();
        hnop.setOptionId(record.getOptionId());
        hnop.setComputeDate(record.getComputeDate());
        HrmsNewsalaryOptionPayroll completeInfo = hrmsNewsalaryOptionPayrollService.getCalculationStatus(hnop);

        if (!"2".equals(completeInfo.getComputeStatus()) && !"3".equals(completeInfo.getComputeStatus())) { // 没有历史数据
            record.setComputeDate(computeDate);
            retRows = hrmsNewsalaryBasicColumnService.makePayTableData(page, record);
            if (!retRows.isEmpty()) {
                for (int i = 0; i < retRows.size(); i++) {
                    // 插入对比数据
                    Map<String, String> beanMap = retRows.get(i);
                    String employeeId = beanMap.get("employee_id");
                    List<HrmsNewsalaryBasicitemEmpHistory> _listBasic = hrmsNewsalaryBasicColumnService
                            .getHistoryByEmp(employeeId);
                    if (!_listBasic.isEmpty()) {
                        _listBasic.forEach(item -> {
                            if ("1".equals(item.getCompare())) {
                                if ("1".equals(item.getBasicItemType())) {
                                    beanMap.put("before_" + item.getBasicItemId(), item.getEmpFieldValueText() + "");
                                } else if ("2".equals(item.getBasicItemType())) {
                                    beanMap.put("before_" + item.getEmpField(), item.getSalaryAmount() + "");
                                } else {
                                    beanMap.put("before_" + item.getEmpField(), item.getEmpFieldValue() + "");
                                }
                            }
                        });
                    }
                    if (!StringUtil.isEmpty(retRows.get(i).get("personal_identity"))) {
                        retRows.get(i).put("personal_identity",
                                personalIdentityDictMap.get(retRows.get(i).get("personal_identity")));
                    }
                    if (!StringUtil.isEmpty(retRows.get(i).get("establishment_type"))) {
                        retRows.get(i).put("establishment_type",
                                establishmentTypeDictMap.get(retRows.get(i).get("establishment_type")));
                    }
                }

            }
        } else { // 历史数据
            List<HrmsNewsalarySecondstepHistory> historyList = hrmsNewsalarySecondstepHistoryService.getData(page,
                    record);
            if (!historyList.isEmpty()) {
                retRows = new ArrayList<>();
                for (int i = 0; i < historyList.size(); i++) {
                    String val = historyList.get(i).getVal();
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        Map<String, String> valMap = objectMapper.readValue(val, Map.class);
                        retRows.add(valMap);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }

        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), retRows);
    }

    // 定薪调薪接口数据
    @Override
    public DataSet<Map<String, Object>> makePayTableDataCount(Page page, HrmsNewsalaryOptionPayroll record) {

        Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
        Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
        Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类别
        List<Map<String, Object>> retRows = null;

        // 1.查询出本月生效的历史数据
        retRows = hrmsNewsalaryBasicColumnService.makePayTableDataCount(page, record);
        if (!retRows.isEmpty()) {
            for (int i = 0; i < retRows.size(); i++) {
                // 插入对比数据
                Map<String, Object> beanMap = retRows.get(i);
                String employeeId = beanMap.get("employee_id").toString();
                List<HrmsNewsalaryBasicitemEmpHistory> _listBasic = hrmsNewsalaryBasicColumnService
                        .getHistoryByEmp(employeeId);
                if (!_listBasic.isEmpty()) {
                    _listBasic.forEach(item -> {
                        if ("1".equals(item.getCompare())) {
                            if ("1".equals(item.getBasicItemType())) {
                                beanMap.put("before_" + item.getBasicItemId(), item.getEmpFieldValueText() + "");
                            } else if ("2".equals(item.getBasicItemType())) {
                                beanMap.put("before_" + item.getEmpField(), item.getSalaryAmount() + "");
                            } else {
                                beanMap.put("before_" + item.getEmpField(), item.getEmpFieldValue() + "");
                            }
                        }
                    });
                }
            }
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), retRows);
    }

    /**
     * 薪酬核算-核算列表表头
     *
     * @param record
     * @return
     */
    @Override
    public List<VueTableEntity> calculateWagesTitle(HrmsNewsalaryOptionPayroll record) {
        Assert.hasText(record.getOptionId(), "薪酬方案ID不能为空.");
        String computeDate = record.getComputeDate(); // 薪酬核算月份
        List<VueTableEntity> calculateWagesTitle = null;
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
        }
        // 根据月份判断是否有数据
		HrmsNewsalaryOptionPayroll calculationStatus = getCalculationStatus(record);
		if(null == calculationStatus || "0".equals(calculationStatus.getComputeStatus())){  //没有计算记录数据
			calculateWagesTitle = getCalculateWagesTitle(record);
		}else{  //查历史表头
			calculateWagesTitle = getCalculateWagesHistoryTitle(record);
		}
//		if ("3".equals(calculationStatus.getComputeStatus())) { //3代表已经锁定，那查询薪酬表头时，则不能直接从方案中来取，有可能方案中的薪酬项目变动，而历史薪酬项目无法查询出来
//			calculateWagesTitle = getCalculateWagesHistoryTitle(record);
//		}else { //表示取当前薪酬方案的薪酬项
//			calculateWagesTitle = getCalculateWagesTitle(record);
//		}
		//过滤需要隐藏的列名
        Map<String, String> hiddenColMap = convertDictMap("salary_table_column_hidden");
		if(CollUtil.isNotEmpty(hiddenColMap)) {
            Set<String> colList = hiddenColMap.keySet();
            List<VueTableEntity> result =  calculateWagesTitle.stream().filter(vo-> !colList.contains(vo.getProp())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(result)) {
                return result;
            }
        }
        return calculateWagesTitle;
    }

    private List<VueTableEntity> getCalculateWagesTitle(HrmsNewsalaryOptionPayroll record) {
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("员工状态", "employee_status_text", null, null, "left"));
        if (StringUtils.isNoneBlank(record.getSalaryIndex())) {
            retVueTableEntity.add(new VueTableEntity("薪酬组", "option_name", null, 180, null));
            retVueTableEntity.add(new VueTableEntity("发送状态", "send_status_text", null, null, null));
            retVueTableEntity.add(new VueTableEntity("查看状态", "is_view_text", null, null, null));
        }
        retVueTableEntity.add(new VueTableEntity("岗位", "personal_identity_text", null, null, null));
        retVueTableEntity.add(new VueTableEntity("编制类型", "establishment_type_text", null, null, null));
        retVueTableEntity.add(new VueTableEntity("银行卡号", "bankcardNo", null, null, null));
        retVueTableEntity.add(new VueTableEntity("政策标准", "policy_standard_name", null, null, null));
        // retVueTableEntity.add(new
        // VueTableEntity("定薪调薪日期","effective_date",null,null,"left"));

        List<HrmsNewsalaryItem> itemList = hrmsNewsalaryItemService.getItemByOptionId(record.getOptionId());
        if (!itemList.isEmpty() && itemList.size() > 0) {
            int width = 20;
            itemList.forEach(item -> {
                String isAdjust = "A05".equals(item.getCustomRule())?"1":"A06".equals(item.getCustomRule())?"2":"";
                retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getId(), null, item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width, true, null,isAdjust));
            });
        }
        if (StringUtils.isBlank(record.getSalaryIndex())) {
            retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, null, null));
            retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 160, null));
        }
        return retVueTableEntity;
    }

    // 查询核算历史表头
    private List<VueTableEntity> getCalculateWagesHistoryTitle(HrmsNewsalaryOptionPayroll record) {
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("员工状态", "employee_status_text", null, null, "left"));
        if (StringUtils.isNoneBlank(record.getSalaryIndex())) {
            retVueTableEntity.add(new VueTableEntity("薪酬组", "option_name", null, 180, null));
            retVueTableEntity.add(new VueTableEntity("发送状态", "send_status_text", null, null, null));
            retVueTableEntity.add(new VueTableEntity("查看状态", "is_view_text", null, null, null));
        }
        retVueTableEntity.add(new VueTableEntity("岗位", "personal_identity_text", null, null, null));
        retVueTableEntity.add(new VueTableEntity("编制类型", "establishment_type_text", null, null, null));
        retVueTableEntity.add(new VueTableEntity("政策标准", "policy_standard_name", null, null, null));
        // retVueTableEntity.add(new
        // VueTableEntity("定薪调薪日期","effective_date",null,null,"left"));

        List<HrmsNewsalaryItem> itemList = hrmsNewsalaryPayrollDetailService.getCalculateWagesHistoryTitle(record);
        // 此时拿的表头比方案中配置中要多，因此需要去掉不在方案中的表头字段
        if (!itemList.isEmpty() && itemList.size() > 0) {
            itemList.forEach(item -> {
                String isAdjust = "A05".equals(item.getCustomRule())?"1":"A06".equals(item.getCustomRule())?"2":"";
                retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getItemId(), null, 120,null, null,isAdjust));
            });
        }
        if (StringUtils.isBlank(record.getSalaryIndex())) {
            retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, null, null));
            retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 120, null));
        }

        return retVueTableEntity;
    }

    /**
     * 薪酬核算-核算列表数
     *
     * @param page
     * @param record
     * @return
     */
    @Override
    public DataSet<Map<String, String>> calculateWagesData(Page page, HrmsNewsalaryOptionPayroll record) {
        Assert.hasText(record.getOptionId(), "薪酬方案ID不能为空.");
        String computeDate = record.getComputeDate(); // 薪酬核算月份
        List<Map<String, String>> calculateWagesData = null;
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
            record.setComputeDate(computeDate);
        }
        // 根据月份判断是否有数据
        HrmsNewsalaryOptionPayroll calculationStatus = getCalculationStatus(record);
        if (StrUtil.isNotBlank(record.getOrgId())) {
            PlatformResult<List<String>> orgResult = hrmsOrganizationFeignService
                    .getHrmsOrganizationAndNextList(record.getOrgId());
            List<String> orgList = orgResult.getObject();
            record.setOrgList(orgList);
        }
        if (null == calculationStatus || "0".equals(calculationStatus.getComputeStatus())) { // 没有计算记录数据
            calculateWagesData = getCalculateWagesData(page, record);
        } else { // 查询核算数据
            calculateWagesData = getCalculateWagesDataCurrent(page, record);
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
                calculateWagesData);
    }

    // 关联查询导入的数据
    private List<Map<String, String>> getCalculateWagesData(Page page, HrmsNewsalaryOptionPayroll record) {
        // 根据方案查询手工录入工资项
        List<HrmsNewsalaryItem> salaryList = hrmsNewsalaryItemService.getItemByOptionId(record.getOptionId());
        record.setSalaryList(salaryList);
        List<Map<String, String>> data = mapper.getCalculateWagesData(page, record);
        return data;
    }

    // 关联查询导入的数据
    private List<Map<String, String>> getSalaryConfirmData(Page page, HrmsNewsalaryOptionPayroll record) {
        // 根据方案查询手工录入工资项
        List<HrmsNewsalaryItem> salaryList = hrmsNewsalaryItemService.getItemByOptionAndrule(record.getOptionId(), "1");
        record.setSalaryList(salaryList);
        List<Map<String, String>> data = mapper.getCalculateWagesData(page, record);
        return data;
    }

    // 关联查询当前月的数据
    private List<Map<String, String>> getCalculateWagesDataCurrent(Page page, HrmsNewsalaryOptionPayroll record) {
		List<HrmsNewsalaryItem> salaryList = itemMapper.getItemByTitlesbyId(record.getOptionId());
//        List<HrmsNewsalaryItem> salaryList = hrmsNewsalaryPayrollDetailService.getCalculateWagesHistoryTitle(record);
		

        record.setSalaryList(salaryList);
        List<Map<String, String>> data = mapper.getCalculateWagesDataCurrent(page, record);
        return data;
    }

    // 关联查询当前月的数据
//    private List<Map<String, String>> getSalaryConfirmDataCurrent(Page page, HrmsNewsalaryOptionPayroll record) {
//        // 根据方案查询手工录入工资项
//        List<HrmsNewsalaryItem> salaryList = hrmsNewsalaryItemService.getManualByOptionId(record.getOptionId());
//        record.setSalaryList(salaryList);
//        List<Map<String, String>> data = mapper.getCalculateWagesDataCurrent(page, record);
////		calculateWagesData = getCalculateWagesDataCurrent(page,record);
//        return data;
//    }

    // 排序函数 保证顺序
    public static List<HrmsNewsalaryItem> sortListByCountFormulaAppearance(List<HrmsNewsalaryItem> list) {

        list.sort(Comparator.comparing(HrmsNewsalaryItem::getItemRule));
        list.sort(new Comparator<HrmsNewsalaryItem>() {
            @Override
            public int compare(HrmsNewsalaryItem t1, HrmsNewsalaryItem t2) {
                // 如果count_formula为null或者空字符串，排在最上面
                if (t1.getCountFormula() == null || t1.getCountFormula().isEmpty()) {
                    return -1;
                } else if (t2.getCountFormula() == null || t2.getCountFormula().isEmpty()) {
                    return 1;
                }

                // 检查t1中的count_formula是否在t2的id中出现过
                boolean t1AppearsInT2 = t1.getCountFormula().chars().anyMatch(ch -> Character.isDigit(ch)
                        && list.stream().noneMatch(test -> test.getId().equals(String.valueOf(ch))));

                // 检查t2中的count_formula是否在t1的id中出现过
                boolean t2AppearsInT1 = t2.getCountFormula().chars().anyMatch(ch -> Character.isDigit(ch)
                        && list.stream().noneMatch(test -> test.getId().equals(String.valueOf(ch))));

                // 如果t1的count_formula在t2的id中出现过，而t2的没有，那么t1排在前面
                if (t1AppearsInT2 && !t2AppearsInT1) {
                    return -1;
                }
                // 如果t2的count_formula在t1的id中出现过，而t1的没有，那么t2排在前面
                else if (t2AppearsInT1 && !t1AppearsInT2) {
                    return 1;
                }
                // 否则保持原有顺序
                return 0;
            }
        });
        return list;
    }

    /**
     * @param @param  record
     * @param @return 参数
     * @return String 返回类型
     * @throws
     * @功能描述: 薪酬计算
     * @Title: startCalculationNew
     * <AUTHOR>
     * @date 2024年6月16日 下午6:52:04
     */
    @Override
    public SseEmitter  startCalculation(HrmsNewsalaryOptionPayroll record) {
        Assert.hasText(record.getOptionId(), "薪酬方案ID不能为空.");
        Assert.hasText(record.getComputeDate(), "核算月份不能为空");
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        SseEmitter emitter = new SseEmitter(-1L);
        // 在新线程中模拟进度更新
        new Thread(() -> {
            try {
                String optionId = record.getOptionId(); // 方案Id
                String computeDate = record.getComputeDate(); // 薪酬核算月份
                String hnopId = IdUtil.getId(); // 发放记录Id
                String hnspId = "";
                boolean SingleEmpFlag = false;
                String salaryDate = computeDate + "-01";
                //需处理定薪、调薪生效日期的数据，然后开始计算；当前算薪周期，应该从调薪历史记发表中取数据判断newsalary_basicitem_emp_history，
                //然后将符合当前算薪周期的定薪调薪数据更新到Hrms_newsalary_basicitem_emp表中

                // 创建工资记录主表集合
                List<HrmsNewsalaryPayroll> payrollRecords = new ArrayList<>(); // 工资记录集合
                List<HrmsNewsalaryPayrollDetail> payrollDetails = new ArrayList<>(); // 工资明细集合
                // 创建子表集合
                // 拿到方案 要用小数位置
                HrmsNewsalaryOption hrmsNewsalaryOption = hrmsNewsalaryOptionMapper.selectByPrimaryKey(optionId);
                // 取核算方案中所引用的薪酬项目，如果只引用了计算公式，未将其它薪酬项目引入到方案中，也应该可以通过解析公式将所有的薪酬项目引入进来；
                // 由于在薪资方案中也可以创建薪酬项目，此项目也会同步给薪酬项目公共库，但修改时不再同步，如果是公式的话，就不能以项目公共库中的公式来引用计算，而应该以私有方案中的公式为准；
                List<HrmsNewsalaryItem> itemList = hrmsNewsalaryItemService.getItemByOptionIdAll(optionId);
                List<HrmsNewsalaryOptionEmp> empList = new ArrayList<>();
                // 拿到方案对应的人员,此方案中已经包含了自定义公式以及自定义规则，以及定薪项目；
                if (StringUtils.isAllBlank(record.getEmployeeId())) { // 如果传入参数为空，则计算薪资方案中的所有人员，如果为非空，则计算指定人员薪酬
                    empList = hrmsNewsalaryOptionEmpService.getAllByOptionId(optionId, null);
                    SingleEmpFlag = false;
                } else {
                    String empId = record.getEmployeeId();
                    empList = hrmsNewsalaryOptionEmpService.getAllByOptionId(optionId, empId);
                    SingleEmpFlag = true;
                    HrmsNewsalaryPayroll hnp = hrmsNewsalaryPayrollMapper.getPayrollByEmpId(empId, computeDate, optionId);
                    hnspId = hnp.getId();
                    hnopId = hnp.getOptionPayrollId();
                }
                if (empList.isEmpty()) {
                    throw new BusinessException("方案未添加核算人员");
                }
                List<String> empIds = empList.stream().map(HrmsNewsalaryOptionEmp::getEmployeeId).collect(Collectors.toList());

                // 以上代码用下面方法替换
                List<String> ls = hrmsNewsalaryOptionEmpMapper.getIsSalary(optionId);
                if ((ls != null) && (ls.size() > 0)) {
                    StringBuilder msgBuilder = new StringBuilder();
                    for (int i = 0; i < ls.size(); i++) {
                        msgBuilder.append("员工工号：").append(ls.get(i)).append("\n");
                    }
                    String msg = msgBuilder.toString();
                    throw new BusinessException("未定薪人员,请先定薪!" + msg);
                }
                // 手工录入的工资项和延用上个月数据取数；
                Map<String, Map<String, Object>> handWorkSalary = getLastMonth(empIds, optionId, record.getComputeDate(),
                        itemList);

                HrmsNewsalaryOptionPayroll hnop = new HrmsNewsalaryOptionPayroll();
                // 计算完后，在插入前先对旧数据删除
                if (!SingleEmpFlag) {
                    // 删除明细
                    hrmsNewsalaryPayrollService.deleteByExample(optionId, computeDate);
                    // 删除记录信息
                    hrmsNewsalaryPayrollDetailService.deleteByExample(optionId, computeDate);
                } else {
                    hrmsNewsalaryPayrollService.deleteByEmployeeIdAndDate(record.getEmployeeId(), computeDate);
                }

                HrmsNewsalaryTemporaryAdjust adjust = null;
                List<HrmsNewsalaryTemporaryAdjust> adjusts = null;
                HrmsEmployee employee = null;
                String empId = null;
                // 根据取得的员工列表，开始插入薪酬主表
                for (int i = 0; i < empList.size(); i++) {
                    empId = empList.get(i).getEmployeeId();
                    //删除员工本月已生成未处理的薪酬项数据
                    newsalaryItemRemindRecordService.deleteUnprocessedDataByEmployeeId(empId, computeDate);
                    /**
                     * @TODO add ni.jiang
                     * 获取员工的编制类型和员工状态数据,用户核算时进行快照保存
                     */
                    employee = hrmsEmployeeMapper.findDetailById(empId);
                    if(employee == null){
                        throw new BusinessException("员工["+empList.get(i).getEmployeeName()+"]未找到相关的员工档案数据");
                    }
                    HrmsNewsalaryPayroll salaryPayroll = new HrmsNewsalaryPayroll(); // 薪酬主表
                    String salaryPayrollId = IdUtil.getId();
                    salaryPayroll.setId(salaryPayrollId);
                    salaryPayroll.setPayrollDate(computeDate);
                    salaryPayroll.setOptionId(optionId);
                    salaryPayroll.setEmployeeId(empId);
                    salaryPayroll.setSendStatus("0");
                    salaryPayroll.setIsView("0"); // 未查看
                    salaryPayroll.setOptionPayrollId(hnopId); // 发放记录id
                    salaryPayroll.setIsDeleted("N");
                    salaryPayroll.setCreateDate(new Date());
                    salaryPayroll.setUpdateDate(new Date());
                    salaryPayroll.setEmployeeStatus(employee.getEmployeeStatus()); //快照员工状态
                    salaryPayroll.setEstablishmentType(employee.getEstablishmentType()); //快照员工编制类型
                    salaryPayroll.setSortNum(empList.get(i).getSortNum()); //设置人员排序号
                    if (user != null) {
                        salaryPayroll.setCreateUser(user.getUsercode());
                        salaryPayroll.setCreateUserName(user.getUsername());
                        salaryPayroll.setUpdateUser(user.getUsercode());
                        salaryPayroll.setUpdateUserName(user.getUsername());
                    }

                    // 每个方案，每个人员的算薪项目数据,因增加了个人项目启动配置，因此emp_field可能会重复，
                    // basici_item_type=3为启动项而非薪资值项， emp_field_value值，1为开启，2为关闭个人相关项目启动项；
                    List<HrmsNewsalaryBasicitemEmp> empLists = hrmsNewsalaryBasicitemEmpMapper.getSalaryOptionEmp(optionId,
                            empId);
                    //获取当前员工的生效日期
                    String effDate = "";
                    if (empLists.size() == 0) {
                        effDate = salaryDate;
                    } else {
                        effDate = empLists.get(0).getEffectiveDate();
                    }
                    Date date2 = null;
                    try{
                        date2 = DateUtil.parse(effDate);
                    } catch (Exception e){
                        throw new BusinessException("员工["+empList.get(i).getEmployeeName()+"]薪酬生效日期为空或格式错误");
                    }
                    // 当前定薪表中的日期
                    String newDate = hrmsNewsalaryBasicitemEmpMapper.getEffectiveDate(empId, salaryDate);
                    Date date1 = DateUtil.parse(newDate); //按算薪日期查最近的生效日期
                    int compare = DateUtil.compare(date1, date2, "yyyy-MM-dd");
                    //如果生效日期
                    if (compare < 0) {
                        empLists = hrmsNewsalaryBasicitemEmpMapper.getSalaryOptionEmpEffDate(optionId, empId, newDate);
                    }
                    //获取员工当前的政策标准id
                   List<HrmsNewsalaryBasicitemEmp> policyList = empLists.stream().filter(p -> "policy_standard_id".equals(p.getEmpField()) ).collect(Collectors.toList());
                    if(CollUtil.isNotEmpty(policyList)){
                        String policyStandardId = policyList.get(0).getEmpFieldValue();
                        salaryPayroll.setPolicyStandardId(policyStandardId);
                    }
                    // 薪酬发放保留项 等确认
                    payrollRecords.add(salaryPayroll);


                    // 将规则为item_rule=5的先进行处理；-自定义规则
                    Map<String, BigDecimal> rule5 = empLists.stream().filter(emp -> "5".equals(emp.getItemRule()))
                            .filter(emp -> emp.getEmpField() != null && emp.getSalaryAmount() != null && emp.getSalaryAmount().compareTo(BigDecimal.ZERO)>0).collect(Collectors
                                    .toMap(HrmsNewsalaryBasicitemEmp::getEmpField, HrmsNewsalaryBasicitemEmp::getSalaryAmount));
                    // 拿到人员对应的定薪数据
                    Map<String, BigDecimal> empResultMap = new HashMap<>(); // 当前项
                    Map<String, String> itemNames = new HashMap<>();
                    for (int j = 0; j < itemList.size(); j++) {
                        boolean flag = true; // 默认不开启
                        HrmsNewsalaryItem salaryItem = itemList.get(j);
                        HrmsNewsalaryBasicitemEmp basicitemMap = new HrmsNewsalaryBasicitemEmp();
                        // 以下代码判断薪酬项目是否有开启标志
                        if (("3".equals(salaryItem.getItemRule())) || ("4".equals(salaryItem.getItemRule()))) {
                            // 如果薪酬项目有开启标志，则存在2条记录，一条为判断标识，其字段值均相同，一条记录为存储此薪酬项目的金额；
                            List<HrmsNewsalaryBasicitemEmp> resultList = empLists.stream()
                                    .filter(emp -> emp.getEmpField().equals(salaryItem.getId())).collect(Collectors.toList());
                            if (null != resultList) {
                                Optional<HrmsNewsalaryBasicitemEmp> itemTypes = resultList.stream()
                                        .filter(emp -> emp.getEmpField().equals(salaryItem.getId()))
                                        .filter(emp -> emp.getBasicItemType().equals("3")).findFirst();
                                // ItemRule为3对应薪酬档案字段的启停开关
                                if (itemTypes.isPresent()) {
                                    basicitemMap = itemTypes.get();
                                    if ("1".equals(basicitemMap.getEmpFieldValue())) {// 1表示开启，其它全部为关闭
                                        flag = true;
                                    } else {
                                        flag = false;
                                    }
                                }
                            }
                            Optional<HrmsNewsalaryBasicitemEmp> result = empLists.stream()
                                    .filter(emp -> emp.getEmpField().equals(salaryItem.getId()))
                                    .filter(emp -> !emp.getBasicItemType().equals("3")).findFirst();
                            if (result.isPresent()) { // 如果找到则直接使用此中数据，如果未找到，则需要继续保留方案中的项目，可能项目值为0
                                basicitemMap = result.get();
                            } else {
                                Optional<HrmsNewsalaryBasicitemEmp> resultList1 = empLists.stream()
                                        .filter(emp -> emp.getEmpField().equals(salaryItem.getId())).findFirst();
                                if (resultList1.isPresent()) {
                                    basicitemMap = resultList1.get();
                                }
                            }
                        } else {
                            Optional<HrmsNewsalaryBasicitemEmp> resultList = empLists.stream()
                                    .filter(emp -> emp.getEmpField().equals(salaryItem.getId())).findFirst();
                            if (resultList.isPresent()) {
                                basicitemMap = resultList.get();
                            }
                        }

                        BigDecimal salaryAmt = new BigDecimal(0);
                        Object obj = basicitemMap.getSalaryAmount();
                        String itemId = salaryItem.getId();
                        //只保存薪酬方案的薪酬项数据
                        if(!"0".equals(salaryItem.getGroupId())) {
                            itemNames.put(itemId, salaryItem.getItemName());
                        }
                        if (null != obj) {
                            if (flag) {
                                salaryAmt = basicitemMap.getSalaryAmount();
                            }
                        }
                        if ("1".equals(salaryItem.getItemRule())) { // 手工录入
                            // 分二种情况，1、先从导入数据中取数，如果取到则不从标准设置中取，如果没取到导入数据，则在标准中取数；
                            Map<String, Object> empHandWorkSalaryMap = handWorkSalary.get(empId);
                            if (null != empHandWorkSalaryMap) {
                                Object _empItemSalary = null;
                                if (null != basicitemMap.getId()) {
                                    _empItemSalary = empHandWorkSalaryMap.get(basicitemMap.getEmpField());
                                } else {
                                    _empItemSalary = empHandWorkSalaryMap.get(salaryItem.getId());
                                }
                                if (null != _empItemSalary) {
                                    empResultMap.put(itemId, new BigDecimal(String.valueOf(_empItemSalary)));
                                } else {
                                    empResultMap.put(itemId, salaryAmt);
                                }
                            } else {
                                empResultMap.put(itemId, salaryAmt);
                            }
                        } else if ("2".equals(salaryItem.getItemRule())) { // 固定值
                            empResultMap.put(itemId, salaryItem.getSalaryItemAmount());
                        } else if ("3".equals(salaryItem.getItemRule())) { // 从薪资档案取
                            int scale = Integer.parseInt(salaryItem.getItemDigit());
                            int roundingMode = CarryRuleEnum.getValByKey(salaryItem.getCarryRule());
                            BigDecimal aa = new BigDecimal(0);
                            if (null != basicitemMap.getEmpFieldValue()) {
                                aa = new BigDecimal(basicitemMap.getEmpFieldValue());
                            } else {
                                aa = new BigDecimal(100);
                            }
                            BigDecimal amt = salaryAmt.multiply(aa).divide(BigDecimal.valueOf(100)).setScale(scale,
                                    roundingMode);
                            empResultMap.put(itemId, amt);
                            if ("A05".equals(salaryItem.getCustomRule())) { //设置薪酬调整加项
                                //根据员工id+算薪周期+计算类型查询薪酬调整数据
                                adjust = new HrmsNewsalaryTemporaryAdjust();
                                adjust.setEmployeeId(empId);
                                adjust.setOptionCycle(computeDate);
                                adjust.setCountType("1");
                                adjusts = newsalaryTemporaryAdjustService.getList(adjust);
                                if (adjusts != null && adjusts.size() > 0) {
                                    amt = adjusts.stream().map(HrmsNewsalaryTemporaryAdjust::getSalaryItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    empResultMap.put(itemId, amt);
                                }
                            }
                            if ("A06".equals(salaryItem.getCustomRule())) { //设置薪酬调整减项
                                //根据员工id+算薪周期+计算类型查询薪酬调整数据
                                adjust = new HrmsNewsalaryTemporaryAdjust();
                                adjust.setEmployeeId(empId);
                                adjust.setOptionCycle(computeDate);
                                adjust.setCountType("2");
                                adjusts = newsalaryTemporaryAdjustService.getList(adjust);
                                if (adjusts != null && adjusts.size() > 0) {
                                    amt = adjusts.stream().map(HrmsNewsalaryTemporaryAdjust::getSalaryItemAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                                    empResultMap.put(itemId, amt);
                                }
                            }

                        } else if ("4".equals(salaryItem.getItemRule())) { // 4计算
                            String countFormula = salaryItem.getCountFormula(); // 公式
                            //					if ((("788247737063776256".equals(salaryItem.getId())) && ("4515EB959BCD4F34BACEDEC0EB9609DE").equals(empId))) {
                            //						System.out.println(countFormula);
                            //					}
                            //					String countFormula = "{783720767861899264[2]}+{783721190781960192[2]}+{783721519212740608[2]}+{783721999422799872[2]}+{783722409038528512[2]}+{783722895124807680[2]}+{783722954063167488[2]}+{783723069771431936[2]}+{783723147525439488[2]}+{783723200616939520[2]}+{783723243663081472[2]}";
                            if (!flag) { // 如果公式配置了开启与关闭项，则进行特列处理
                                empResultMap.put(itemId, new BigDecimal(0));
                            } else {
                                // 在对公式全部替换前，先对公式中未启用的进行0的替换，需要针对开关项做特殊处理，在empList中存在开关的数据
                                List<String> lsa = FormulaParse.getCountFormulaList(countFormula);
                                for (int n = 0; n < lsa.size(); n++) {
                                    // 针对公式中的项次进行判断，哪些针对此人是禁用的,作特殊处理
                                    Map<String, String> code = FormulaParse.getCountFormulaCode(lsa.get(n)); // code=783721190781960192[2]
                                    if ("2".equals(code.get("type"))) {
                                        // 判断code.get("code")是否为禁用项？
                                        Optional<HrmsNewsalaryBasicitemEmp> tmp = empLists.stream()
                                                .filter(emp -> emp.getEmpField().equals(code.get("code")))
                                                .filter(emp -> emp.getBasicItemType().equals("3")).findFirst();
                                        if (tmp.isPresent()) {
                                            if (tmp.get().getBasicItemType().equals("3")) {
                                                if (!"1".equals(tmp.get().getEmpFieldValue())) { // 如果开关禁用则直接对公式进行替换为0，表示不参再参与计算
                                                    countFormula = countFormula.replace(lsa.get(n), "0");
                                                }
                                            }
                                        }
                                        if (null != empResultMap.get(code.get("code"))) { //优先取已经计算完成的项次，并针对项目进行值替换，避免二次计算出现四舍五入所产生的差异。
                                            if (empResultMap.get(code.get("code")).compareTo(new BigDecimal("0")) > 0) {
                                                countFormula = countFormula.replace(lsa.get(n), empResultMap.get(code.get("code")).toString());
                                            }
                                        }
                                    }
                                }
                                //
                                // 对公式进行处理，直接对公式变量进行全面替换
                                countFormula = FormulaParse.getCountFormulaListLoop(countFormula, itemList);
                                List<String> _item = FormulaParse.getCountFormulaList(countFormula); // 单独是项目;
                                // 此处应该将转换后的计算公式带出来
                                boolean resume = true; // 继续标识
                                for (int k = 0; k < _item.size(); k++) {
                                    Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(_item.get(k));
                                    if (null != countFormulaCode) {
                                        if ("2".equals(countFormulaCode.get("type"))
                                                || "1".equals(countFormulaCode.get("type"))) { // 薪酬方案里面的项目
                                            if (null == empResultMap.get(countFormulaCode.get("code"))) {
                                                if ((null == rule5) || (null == rule5.get(countFormulaCode.get("code")))) {
                                                    countFormula = countFormula.replace(_item.get(k), "0");
                                                } else {
                                                    countFormula = countFormula.replace(_item.get(k),
                                                            rule5.get(countFormulaCode.get("code")).toString());
                                                }
                                            } else {
                                                countFormula = countFormula.replace(_item.get(k),
                                                        empResultMap.get(countFormulaCode.get("code")).toString());
                                            }
                                        } else if ("4".equals(countFormulaCode.get("type"))) { // 常数
                                            String formula = _item.get(k);
                                            int index = formula.indexOf("[");
                                            countFormula = countFormula.replace(_item.get(k), formula.substring(0, index));
                                        } else { // 0 为定薪类型的 直接替换成值
                                            log.info("替换后：" + countFormula);
                                        }
                                        //							} else {
                                        //								log.error("计算错误人员id:" + empId);
                                        //								throw new RuntimeException(empName + "的薪酬项" + basicitemMap.getItemName() + "计算公式错误:"
                                        //										+ basicitemMap.getCountFormulaText());
                                    }
                                }
                                if (resume) {
                                    // 用函数库拿到结果
                                    countFormula = countFormula.replace("\\[\\d+\\]", "");
                                    // 匹配薪酬项目
                                    log.info("匹配替换前===========" + countFormula);
                                    for (Map.Entry<String, BigDecimal> entry : empResultMap.entrySet()) {
                                        if (null != entry.getValue()) {
                                            countFormula = countFormula.replace("{" + entry.getKey() + "}",
                                                    entry.getValue().toString());
                                        } else {
                                            countFormula = countFormula.replace("{" + entry.getKey() + "}", "0");
                                        }
                                    }
                                    // 去掉大括号
                                    countFormula = countFormula.replaceAll("[{}]", "");
                                    log.info("匹配替换后 计算算式===========" + countFormula);
                                    if (!StringUtil.isEmpty(countFormula)) {
                                        Expression expression = new Expression(countFormula);
                                        // 设置小数位以及小数位取整规则
                                        BigDecimal itemVal = expression.eval().setScale(
                                                Integer.parseInt(salaryItem.getItemDigit()),
                                                CarryRuleEnum.getValByKey(salaryItem.getCarryRule()));
                                        if (flag) {
                                            empResultMap.put(itemId, itemVal);
                                        } else {
                                            empResultMap.put(itemId, new BigDecimal(0));
                                        }
                                    } else {
                                        empResultMap.put(itemId, new BigDecimal(0));
                                    }
                                }
                            }

                        } else if ("5".equals(salaryItem.getItemRule())) { // 自定义配置
                            empResultMap.put(itemId, salaryAmt);
                        }
                    }
                    for (Map.Entry<String, BigDecimal> entry : empResultMap.entrySet()) {
                        if(itemNames.containsKey(entry.getKey())) {
                            HrmsNewsalaryPayrollDetail payrollDetail = new HrmsNewsalaryPayrollDetail(IdUtil.getId(),
                                    salaryPayrollId);
                            payrollDetail.setItemId(entry.getKey());
                            payrollDetail.setItemName(itemNames.get(entry.getKey()));
                            payrollDetail.setSalary(entry.getValue());
                            payrollDetail.setOptionId(optionId);
                            payrollDetail.setPayrollDate(computeDate);
                            payrollDetails.add(payrollDetail);
                        }
                    }
                    /**
                     * 保存员工薪酬项提醒记录
                     * @TODO add ni.jiang
                     */
                    try {
                        saveNewSalaryItemChanges(employee, payrollDetails);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("员工[" + employee.getEmployeeName() + "]保存薪酬项异动提醒数据失败:" + e.getMessage());
                    }
                    // 插入发放记录表
                    hrmsNewsalaryPayrollMapper.insertSelective(salaryPayroll);
                    payrollRecords.clear();
                    // 插入薪酬发放明细表
                    hrmsNewsalaryPayrollDetailService.batchInsert(payrollDetails);
                    payrollDetails.clear();

                    // 发送进度数据
                    BigDecimal proccess = Convert.toBigDecimal(i+1).divide(Convert.toBigDecimal(empList.size()),3,RoundingMode.HALF_UP ).multiply(Convert.toBigDecimal(100)).setScale(0,RoundingMode.DOWN);
                    if(proccess.compareTo(new BigDecimal(100))<0) {
                        emitter.send(proccess);
                    }
                }
                log.error("==========开始算薪批量处理完成");
                //发送薪酬
                try {
                    HrmsNewsalaryRemindSetting setting = hrmsNewsalaryRemindSettingService.selectByType(2);
                    Example example = new Example(HrmsNewsalaryItemRemindRecord.class);
                    example.createCriteria().andEqualTo("computeDate",record.getComputeDate())
                            .andEqualTo("isDeleted","N")
                            .andIn("employeeId",empIds)
                            .andEqualTo("handleStatus","0");
                    int count = hrmsNewsalaryItemRemindRecordMapper.selectCountByExample(example);
                    String content = "当前核算周期有" + count + "条薪酬提醒异动记录需要进行处理，请前往薪酬提醒-薪资异动办理进行处理";
                    //发送通知
                    NoticeReq notice = NoticeReq.builder()
                            .content(content)
                            .noticeType("4")
                            .receiver(setting.getNoticeUser())
                            .sender("admin")
                            .senderName("系统管理员")
                            .subject("薪酬薪资异动提醒通知")
                            .toUrl("/ts-web-hrm/pay-manager/newsalary-remind")
                            .wxSendType("2")
                            .source("异动提醒")
                            .build();
                    informationFeignService.sendNotice(notice);
                }catch (Exception e){
                    e.printStackTrace();
                    System.out.println("发送通知异常:"+e.getMessage());
                }
                if (!SingleEmpFlag) {
                    try {
                        // 删除记录
                        hrmsNewsalaryOptionPayrollService.deleteByExample(optionId, computeDate);
                        // 插入一条薪酬核算记录
                        hnop.setOptionName(hrmsNewsalaryOption.getOptionName());
                        hnop.setId(hnopId);
                        hnop.setOptionId(optionId);
                        hnop.setComputeDate(computeDate);
                        hnop.setHeadCount(empIds.size());
                        hnop.setComputeTime(DateUtils.getPresentTimeStr());
                        hnop.setComputeStatus("1"); // 已核算
                        hnop.setPaySlip("0"); // 未发放工资条
                        hnop.setUpdateUser(user.getUsercode());
                        hnop.setUpdateUserName(user.getUsername());
                        hnop.setCreateDate(new Date());
                        hnop.setUpdateDate(new Date());
                        hnop.setCreateUser(user.getUsercode());
                        hnop.setCreateUserName(user.getUsername());
                        save(hnop);
                    } catch (Exception e) {
                        throw new RuntimeException("薪酬计算异常" + e.getMessage(), e);
                    }
                }
                // 发送进度数据
                emitter.send(new BigDecimal(100));
                emitter.complete();
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    emitter.send("message:" + e.getMessage());
                }catch (Exception e1){
                    emitter.completeWithError(e);
                }
                if(e instanceof IOException) {
                    emitter.completeWithError(e);
                }
                log.error("薪酬计算失败：" + e.getMessage());
            }
        }).start();
        return emitter;
    }

    /**
     * 根据员工保存薪酬薪资异动数据
     * @param employee 员工对象
     * @param payrollDetails 核算薪酬项明细
     */
//    @Async("threadPoolTaskExecutor")
    private void saveNewSalaryItemChanges(HrmsEmployee employee,List<HrmsNewsalaryPayrollDetail> payrollDetails) throws Exception{
        if(CollectionUtils.isEmpty(payrollDetails) || employee == null || employee.getEntryDate() == null){
            return ;
        }
        Date entryDate = employee.getEntryDate();
        //根据员工id查询上个月核算记录及薪酬项提示设置
        List<HrmsNewsalaryItemRemindSetting> itemRemindSettingList = mapper.selectEmployeeSalaryChangeItemSettingDatas(employee.getEmployeeId(),getLastDate(payrollDetails.get(0).getPayrollDate()),payrollDetails.get(0).getPayrollDate());
        if(CollectionUtils.isEmpty(itemRemindSettingList)){
            return ;
        }
        HrmsNewsalaryItemRemindRecord remindRecord = null;
        Date backPaymentDate = null;
        BigDecimal salary = BigDecimal.ZERO;
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        List<HrmsNewsalaryItemRemindRecord> list = new ArrayList<>();
        for(HrmsNewsalaryItemRemindSetting setting:itemRemindSettingList){
            remindRecord = new HrmsNewsalaryItemRemindRecord();
            remindRecord.setId(IdGeneraterUtils.nextId());
            //获取补缴开始月份
            if("1".equals(setting.getIsBackPayment())) {
//                String month = Integer.valueOf(setting.getBackPaymentDate()) <10 ? "0"+setting.getBackPaymentDate() : setting.getBackPaymentDate();
                //将补缴月份设置为对应的日期
                backPaymentDate = DateUtils.getStringToDate(setting.getBackPaymentDate()+"-01");
            }
            for(HrmsNewsalaryPayrollDetail detail:payrollDetails){
                //判断薪酬核算本月数据与上个月是否金额是否一致，不一致则进行提醒
                if(setting.getItemId().equals(detail.getItemId()) && !detail.getSalary().equals(new BigDecimal(setting.getSalary()))){
                    remindRecord.setBeforeSalary(new BigDecimal(setting.getSalary()));
                    remindRecord.setAfterSalary(detail.getSalary());
                    salary = detail.getSalary().subtract(new BigDecimal(setting.getSalary()));
                    if((salary.compareTo(BigDecimal.ZERO)>0 && "1".equals(setting.getCountType()))
                            || (salary.compareTo(BigDecimal.ZERO)< 0 && "2".equals(setting.getCountType()))){
                        remindRecord.setIsBackPayment("1");
                    }else if((salary.compareTo(BigDecimal.ZERO)>0 && "2".equals(setting.getCountType()))
                            || (salary.compareTo(BigDecimal.ZERO) < 0 && "1".equals(setting.getCountType()))){
                        remindRecord.setIsBackPayment("2");
                    }else{
                        continue;
                    }
                    remindRecord.setDifferenceAmount(salary);
                    remindRecord.setCountType(setting.getCountType());
                    remindRecord.setComputeDate(detail.getPayrollDate());
                    remindRecord.setRemindId(setting.getRemindId());
                    remindRecord.setItemId(setting.getItemId());
                    remindRecord.setEmployeeId(employee.getEmployeeId());
                    remindRecord.setEmployeeStatus(employee.getEmployeeStatus());
                    remindRecord.setEstablishmentType(employee.getEstablishmentType());
                    remindRecord.setHandleStatus("0");
                    if("1".equals(setting.getIsBackPayment())) {
                        remindRecord.setBackPaymentDate(setting.getBackPaymentDate());
                        if (entryDate.compareTo(backPaymentDate) > 0) { //入职时间大于补缴开始时间，则只补缴当前时间到入职时间
                            Date endDate = cn.hutool.core.date.DateUtil.parse(detail.getPayrollDate() + "-01");
                            Long num = DateUtil.betweenMonth(entryDate,endDate,false);
                            remindRecord.setBackPaymentDate(detail.getPayrollDate());
//                            int startMonth = DateUtil.month(entryDate);
//                            int endMonth = DateUtil.month(DateUtils.getStringToDate(detail.getPayrollDate() + "-01"));
                            if (num>0) {
                                remindRecord.setBackPaymentMonth(num + "");
                            } else {
                                continue;
                            }
                        } else { //入职时间小于补缴时间，则全额补缴
                            Date endDate = cn.hutool.core.date.DateUtil.parse(detail.getPayrollDate() + "-01");
                            if(endDate.compareTo(backPaymentDate)>0) {
                                remindRecord.setBackPaymentDate(setting.getBackPaymentDate());
                                Long num = DateUtil.betweenMonth(backPaymentDate, endDate, false);
//                            int startMonth = DateUtil.month(backPaymentDate);
//                            int endMonth = DateUtil.month(DateUtils.getStringToDate(detail.getPayrollDate() + "-01"));

                                remindRecord.setBackPaymentMonth(num + "");
                            }else{
                                remindRecord.setBackPaymentDate(detail.getPayrollDate());
                                remindRecord.setBackPaymentMonth("0");
                            }
                        }
                    }
                    if (user != null) {
                        remindRecord.setCreateUser(user.getUsercode());
                        remindRecord.setCreateUserName(user.getUsername());
                        remindRecord.setUpdateUser(user.getUsercode());
                        remindRecord.setUpdateUserName(user.getUsername());
                    }
                    remindRecord.setCreateDate(new Date());
                    remindRecord.setUpdateDate(new Date());
                    remindRecord.setIsDeleted("N");
                    if("1".equals(setting.getIsBackPayment())) {
                        //查询补缴时间之后的薪酬异动记录，保存到备注字段
                        List<Map<String, String>> changesDetailedEoList = hrmsNewsalaryChangesDetailedMapper.getEmployeeNewsalaryChangesGroupByDate(remindRecord.getEmployeeId(), setting.getBackPaymentDate() + "-01", detail.getPayrollDate() + "-01");
                        if (CollUtil.isNotEmpty(changesDetailedEoList)) {
                            StringBuffer sb = new StringBuffer();
                            changesDetailedEoList.forEach(vo -> {
                                sb.append("调整时间：").append(vo.get("effective_date")).append("，操作：").append(vo.get("reason")).append("\n");
                            });
                            remindRecord.setRemark(sb.toString());
                        }
                    }
                    list.add(remindRecord);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(list)){
            newsalaryItemRemindRecordService.batchInsert(list);
        }
    }

    /**
     * 查询手工录入或者引用上月方法
     *
     * @param empIds
     * @param optionId
     * @param computeDate
     * @param itemList
     * @return
     */
    private Map<String, Map<String, Object>> getLastMonth(List<String> empIds, String optionId, String computeDate,
                                                          List<HrmsNewsalaryItem> itemList) {

        Map<String, Map<String, Object>> retMap = new HashMap<>();

        List<LastSalaryOut> lastSalary = mapper.getLastSalary(optionId, getLastDate(computeDate));// 查询上月数据
        Map<String, Map<String, String>> lastSalaryMap = null;
        if (!lastSalary.isEmpty()) {
            lastSalaryMap = lastSalary.stream().collect(Collectors.groupingBy(LastSalaryOut::getEmployeeId, Collectors
                    .toMap(LastSalaryOut::getItemId, LastSalaryOut::getSalary, (oldValue, value) -> oldValue)));
        }

        List<LastSalaryOut> nowSalary = mapper.getNowSalary(optionId, computeDate);// 查询本月导入数据
        Map<String, Map<String, String>> nowSalaryMap = null;
        if (!nowSalary.isEmpty()) {
            nowSalaryMap = nowSalary.stream().collect(Collectors.groupingBy(LastSalaryOut::getEmployeeId, Collectors
                    .toMap(LastSalaryOut::getItemId, LastSalaryOut::getSalary, (oldValue, value) -> oldValue)));
        }

        // 遍历人员创建数据
        for (int i = 0; i < empIds.size(); i++) {
            Map<String, Object> _map = new HashMap<>();
            for (int j = 0; j < itemList.size(); j++) {
                HrmsNewsalaryItem salaryItem = itemList.get(j);
                if ("1".equals(salaryItem.getItemRule())) { // 手工录入的
                    // 手工没有导入 判断
                    if (null != nowSalaryMap) {
                        Map<String, String> empSalaryMap = nowSalaryMap.get(empIds.get(i));
                        if (null != empSalaryMap) { // 本月不为空
                            String _salary = empSalaryMap.get(salaryItem.getId());
                            if (!StringUtil.isEmpty(_salary)) { // 本月有导入取导入数据
                                _map.put(salaryItem.getId(), _salary);
                            } else {
                                if ("1".equals(salaryItem.getNextMonth())) { // 设置了取上月的才取上月的数据
                                    if (null != lastSalaryMap) {
                                        Map<String, String> empLastSalaryMap = lastSalaryMap.get(empIds.get(i)); // 上月数据
                                        if (null != empLastSalaryMap) {
                                            String _lastSalary = empLastSalaryMap.get(salaryItem.getId());
                                            if (!StringUtil.isEmpty(_lastSalary)) { // 本月有导入取导入数据
                                                _map.put(salaryItem.getId(), _lastSalary);
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            if ("1".equals(salaryItem.getNextMonth())) { // 设置了取上月的才取上月的数据
                                if (null != lastSalaryMap) {
                                    Map<String, String> empLastSalaryMap = lastSalaryMap.get(empIds.get(i)); // 上月数据
                                    if (null != empLastSalaryMap) {
                                        String _lastSalary = empLastSalaryMap.get(salaryItem.getId());
                                        if (!StringUtil.isEmpty(_lastSalary)) { // 取上月
                                            _map.put(salaryItem.getId(), _lastSalary);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        if ("1".equals(salaryItem.getNextMonth())) { // 设置了取上月的才取上月的数据
                            if (null != lastSalaryMap) {
                                Map<String, String> empLastSalaryMap = lastSalaryMap.get(empIds.get(i)); // 上月数据
                                if (null != empLastSalaryMap) {
                                    String _lastSalary = empLastSalaryMap.get(salaryItem.getId());
                                    if (!StringUtil.isEmpty(_lastSalary)) { // 用上月的数据
                                        _map.put(salaryItem.getId(), _lastSalary);
                                    }
                                }
                            }
                        }

                    }
                }
            }
            retMap.put(empIds.get(i), _map);
        }
        return retMap;
    }

    private String getLastDate(String inputDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar cal = Calendar.getInstance();
        try {
            Date date = sdf.parse(inputDate);
            cal.setTime(date);
            cal.add(Calendar.MONTH, -1); // 减去一个月
            return sdf.format(cal.getTime());
        } catch (Exception e) {
            log.error("获取上月时间异常" + inputDate);
        }
        return null;
    }

    /**
     * 根据方案id和算薪周期查询是否存在未处理薪酬项提醒数据
     * @param optionId
     * @param computeDate
     * @return
     */
   public Integer checkIsExistsUnprocessedRemindData(String optionId,String computeDate){
       //获取方案下的所有人
       List<HrmsNewsalaryOptionEmp> empList = hrmsNewsalaryOptionEmpService.getAllByOptionId(optionId, null);
       List<String> empIds = empList.stream().map(HrmsNewsalaryOptionEmp::getEmployeeId).collect(Collectors.toList());
       return  newsalaryItemRemindRecordService.checkIsExistsUnprocessedRemindDataByEmpIds(empIds,computeDate);
    }

    // 完成核算的方法
//	@Transactional(readOnly = false)
    @Override
    public void complete(HrmsNewsalaryOptionPayroll record) {

        // 根据id先查询出数据
        HrmsNewsalaryOptionPayroll hrmsNewsalaryOptionPayroll = mapper.selectByPrimaryKey(record.getId());

        // 删除本月核算人员历史数据
        hrmsNewsalaryFirststepHistoryService.deleteByOptionIdAndDate(hrmsNewsalaryOptionPayroll.getOptionId(),
                hrmsNewsalaryOptionPayroll.getComputeDate());
        // 删除本月定薪调薪历史表头
        hrmsNewsalarySecondstepTitleHistoryService.deleteByOptionIdAndDate(hrmsNewsalaryOptionPayroll.getOptionId(),
                hrmsNewsalaryOptionPayroll.getComputeDate());
        // 删除本月定薪调薪数据
        hrmsNewsalarySecondstepHistoryService.deleteByOptionIdAndDate(hrmsNewsalaryOptionPayroll.getOptionId(),
                hrmsNewsalaryOptionPayroll.getComputeDate());

        // 插入核对人员历史记录
        Page page = new Page();
        page.setPageSize(Integer.MAX_VALUE);
        SearchListTable searchListTable = new SearchListTable();
        searchListTable.setOptionId(hrmsNewsalaryOptionPayroll.getOptionId());
        DataSet<CheckPersonnel> checkPersonnelDataSet = hrmsNewsalaryOptionPayrollService.checkPersonnel(page,
                searchListTable);
        List<CheckPersonnel> rows = checkPersonnelDataSet.getRows();

        if (!rows.isEmpty()) {
            for (int i = 0; i < rows.size(); i++) {
                String json = "";
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    json = objectMapper.writeValueAsString(rows.get(i));
                } catch (Exception e) {
                    log.error("JSON转换异常" + e.getMessage(), e);
                }
                HrmsNewsalaryFirststepHistory _hnfs = new HrmsNewsalaryFirststepHistory();
                _hnfs.setOptionId(hrmsNewsalaryOptionPayroll.getOptionId());
                _hnfs.setPayrollDate(hrmsNewsalaryOptionPayroll.getComputeDate());
                _hnfs.setEmployeeNo(rows.get(i).getEmployeeNo());
                _hnfs.setEmployeeName(rows.get(i).getEmployeeName());
                _hnfs.setEmployeeStatus(rows.get(i).getEmployeeStatus());
                _hnfs.setEmployeeStatus(rows.get(i).getEmployeeStatus());
                _hnfs.setOrgName(rows.get(i).getOrgName());
                _hnfs.setVal(json);

                hrmsNewsalaryOptionPayroll.getComputeDate();

                if (!StringUtil.isEmpty(rows.get(i).getEntryDate())
                        && rows.get(i).getEntryDate().contains(hrmsNewsalaryOptionPayroll.getComputeDate())) {// 入职 1 离职
                    // 退休 2
                    _hnfs.setYdqk("1"); // 入职的
                } else if ("4".equals(rows.get(i).getEmployeeStatus()) || "8".equals(rows.get(i).getEmployeeStatus())) {
                    _hnfs.setYdqk("2"); // 离职退休的
                }
                hrmsNewsalaryFirststepHistoryService.save(_hnfs);
            }
            record.setSetCount(rows.size());
        }

        // 插入定薪调薪历史记录表表头
        HrmsNewsalaryOptionPayroll _title = new HrmsNewsalaryOptionPayroll();
        _title.setOptionId(hrmsNewsalaryOptionPayroll.getOptionId());
        List<VueTableEntity> vueTableEntities = hrmsNewsalaryOptionPayrollService.makePayTableTitle(_title);

        String jsonTitle = "";
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            jsonTitle = objectMapper.writeValueAsString(vueTableEntities);
        } catch (Exception e) {
            log.error("JSON转换异常" + e.getMessage(), e);
        }
        HrmsNewsalarySecondstepTitleHistory hnsth = new HrmsNewsalarySecondstepTitleHistory();
        hnsth.setOptionId(hrmsNewsalaryOptionPayroll.getOptionId());
        hnsth.setPayrollDate(hrmsNewsalaryOptionPayroll.getComputeDate());
        hnsth.setVal(jsonTitle);
        hrmsNewsalarySecondstepTitleHistoryService.save(hnsth);

        // 查询历史定薪数据
        Page page2 = new Page();
        page2.setPageSize(Integer.MAX_VALUE);
        HrmsNewsalaryOptionPayroll hnop = new HrmsNewsalaryOptionPayroll();
        hnop.setComputeDate(hrmsNewsalaryOptionPayroll.getComputeDate());
        hnop.setOptionId(hrmsNewsalaryOptionPayroll.getOptionId());
        DataSet<Map<String, String>> mapDataSet = hrmsNewsalaryOptionPayrollService.makePayTableData(page2, hnop);
        List<Map<String, String>> rows2 = mapDataSet.getRows();
        if (CollUtil.isNotEmpty(rows2)) {
            for (int i = 0; i < rows2.size(); i++) {
                String json = "";
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.disable(SerializationFeature.INDENT_OUTPUT);
                    json = objectMapper.writeValueAsString(rows2.get(i));
                } catch (Exception e) {
                    log.error("JSON转换异常" + e.getMessage(), e);
                }
                HrmsNewsalarySecondstepHistory _hnsh = new HrmsNewsalarySecondstepHistory();
                _hnsh.setOptionId(hrmsNewsalaryOptionPayroll.getOptionId());
                _hnsh.setPayrollDate(hrmsNewsalaryOptionPayroll.getComputeDate());
                _hnsh.setEmployeeNo(rows2.get(i).get("employee_no"));
                _hnsh.setEmployeeName(rows2.get(i).get("employee_name"));
                _hnsh.setEmployeeStatus(rows2.get(i).get("employee_status"));
                _hnsh.setOrgName(rows2.get(i).get("org_name"));
                _hnsh.setVal(json);
                hrmsNewsalarySecondstepHistoryService.save(_hnsh);
            }
            record.setUpdateCount(rows2.size());
        }
        update(record); // 插入完成核算的状态
    }

    /**
     * 获取核算信息
     *
     * @param record
     * @return
     */
    @Override
    public HrmsNewsalaryOptionPayroll getCompleteInfo(HrmsNewsalaryOptionPayroll record) {
        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("computeDate", record.getComputeDate());
        criteria.andEqualTo("optionId", record.getOptionId());
        List<HrmsNewsalaryOptionPayroll> records = mapper.selectByExample(example);
        if (records != null && records.size() == 1) {
            return records.get(0);
        } else if (records != null && records.size() > 1) {
            throw new BusinessException("核算记录错误，请联系管理员");
        }
        return null;
    }

    @Override
    public HrmsNewsalaryOptionPayroll getCalculationStatus(HrmsNewsalaryOptionPayroll record) {
        String optionId = record.getOptionId();
        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("computeDate", record.getComputeDate());
        criteria.andEqualTo("optionId", record.getOptionId());
        HrmsNewsalaryOption hnso = hrmsNewsalaryOptionMapper.selectByPrimaryKey(optionId);
        List<HrmsNewsalaryOptionPayroll> records = mapper.selectByExample(example);
        if (records.size() > 0) {
            return records.get(0);
        } else {
            HrmsNewsalaryOptionPayroll bean = new HrmsNewsalaryOptionPayroll();
            bean.setComputeStatus("0");
            bean.setComputeStatus(bean.getComputeStatus());
            bean.setOptionId(record.getOptionId());
            bean.setComputeDate(record.getComputeDate());
            bean.setPaySlip("0");
            bean.setOptionName(hnso.getOptionName());
            return bean;
        }

    }

    /**
     * @param source 要分割的集合
     * @param n      最多分割成几个
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> averageAssign(List<T> source, int n) {
        List<List<T>> result = new ArrayList<List<T>>();
        int remaider = source.size() % n; // (先计算出余数)
        int number = source.size() / n; // 然后是商
        int offset = 0;// 偏移量
        for (int i = 0; i < n; i++) {
            List<T> value = null;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    // 薪酬统计汇总获取数据的方法
    @Override
    public DataSet<Map<String, Object>> salaryCountData(Page page, SalaryCountSearchReq record) {

        if (StringUtil.isEmpty(record.getPayrollDate())) {
            record.setPayrollDate(DateUtils.getStringDateShortYM(new Date()));
        }

        List<HrmsNewsalaryItem> salaryList = hrmsNewsalaryItemService.getListItemCode(record.getOptionId());
        List<Map<String, Object>> retRows = mapper.salaryCountData(page, record, salaryList);
        if (retRows != null && retRows.size() > 0) {
            for (int i = 0; i < retRows.size(); i++) {
                retRows.get(i).put("no", i + 1);
            }
        }
        //汇总数据
        if(CollectionUtil.isNotEmpty(retRows)) {
            Map<String, Object> totalData = new HashMap<>();
            for (String key : retRows.get(0).keySet()) {
                if (retRows.get(0).get(key) instanceof Number) {
                    totalData.put(key, retRows.stream().map(vo -> new BigDecimal(String.valueOf(vo.get(key) == null ? "0" : vo.get(key)))).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
            totalData.put("payroll_date","合计");
            retRows.add(totalData);
        }


        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), retRows);
    }

    @Override
    public List<HrmsNewsalaryOptionPayroll> getDataByOptionId(String id) {
        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("optionId", id);
        List<HrmsNewsalaryOptionPayroll> records = mapper.selectByExample(example);
        return records;
    }

    // 获取需要修改的内容,按正确的顺序
    @Override
    public Map<String, Object> getSalaryChangesData(HrmsNewsalaryOptionPayroll record) {
        Map<String, Object> retMap = new LinkedHashMap<>();
        // 查询人员基本信息
        Map<String, Object> baseData = hrmsNewsalaryBasicitemEmpService.getBaseData(record.getEmployeeId());
        retMap.put("baseInfo", baseData);
        // 获取计算出来的数据
        List<UpdateSalaryVo> list = hrmsNewsalaryPayrollDetailService.getSalaryChangesData(record);
        if (!list.isEmpty()) {
            list.forEach(item -> item.setEmployeeNo(baseData.get("工号").toString()));
        }
        Map<String, List<UpdateSalaryVo>> flattenedMap = new HashMap<>();
        // 按SeqNo、itemGroup分组排序
        Map<String, Map<String, List<UpdateSalaryVo>>> groupedByType = list.stream().collect(
                Collectors.groupingBy(UpdateSalaryVo::getSeqNo, Collectors.groupingBy(UpdateSalaryVo::getItemGroup)));
        // 去掉SeqNo层，保持正确顺序
        for (Iterator iterator = groupedByType.values().iterator(); iterator.hasNext(); ) {
            flattenedMap.putAll((Map<? extends String, ? extends List<UpdateSalaryVo>>) iterator.next());

        }
        retMap.put("item", flattenedMap);
        return retMap;
    }

    /**
     * 修改计算后的工资
     *
     * @param record
     * @return
     */
    @Override
//	@Transactional(readOnly = false)
    public Integer reloadStartCalculation(HrmsNewsalaryOptionPayroll record) {
        // 修改人员月份导入的工资数据
        List<UpdateSalaryVo> updateSalaryList = record.getUpdateSalaryList();
        hrmsNewsalaryPayrollDetailImportService.updateSalaryList(updateSalaryList);
        startCalculation(record); // 计算单个人工资
        return 1;
    }

    @Override
    public void hsryExport(Page page, SearchListTable record, HttpServletRequest request,
                           HttpServletResponse response) {
        // 模板位置
        String templateUrl = "template/hsry.xlsx";
        // 表头标题
        String fileName = "核算人员.xlsx";
        Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
        Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
        Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类别
        // 时间
        String payrollDate = record.getPayrollDate(); // 核算时间
        if (StringUtil.isEmpty(payrollDate)) {
            payrollDate = DateUtils.getStringDateShortYM(new Date());
            record.setPayrollDate(payrollDate);
        }
        page.setPageNo(1);
        page.setPageSize(Integer.MAX_VALUE);
        HrmsNewsalaryOptionPayroll hnop = new HrmsNewsalaryOptionPayroll();
        hnop.setOptionId(record.getOptionId());
        hnop.setComputeDate(payrollDate);
        HrmsNewsalaryOptionPayroll completeInfo = hrmsNewsalaryOptionPayrollService.getCalculationStatus(hnop);

        List<CheckPersonnel> list = null;
        // 未计算进来
        if (!"2".equals(completeInfo.getComputeStatus()) && !"3".equals(completeInfo.getComputeStatus())) {
            list = mapper.checkPersonnel(page, record);
        } else {// 已计算进来 查历史数据
            list = hrmsNewsalaryFirststepHistoryService.getHistoryData(page, record);

        }
        // 处理数据字典
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setPersonalIdentityText(personalIdentityDictMap.get(list.get(i).getPersonalIdentity()));
                list.get(i).setEstablishmentTypeText(establishmentTypeDictMap.get(list.get(i).getEstablishmentType()));
                list.get(i).setEmployeeStatusText(employeeStatusDictMap.get(list.get(i).getEmployeeStatus()));
            }
        }
        // 添加序号
        int pm = 1;
        for (CheckPersonnel item : list) {
            item.setPm(pm);
            pm++;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("list", list);
        map.put("exportDate", DateUtil.format(new Date(), "yyyy-MM-dd"));
        map.put("exportUserName", UserInfoHolder.getCurrentUserName());
        try {
            cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, fileName, templateUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, Integer> checkPersonnelCount(SearchListTable record) {
        // 时间
        String payrollDate = record.getPayrollDate(); // 核算时间
        if (StringUtil.isEmpty(payrollDate)) {
            payrollDate = DateUtils.getStringDateShortYM(new Date());
            record.setPayrollDate(payrollDate);
        }
        Map<String, Integer> map = new HashMap<>();
        map = mapper.checkPersonnelCount(record);
        List<HrmsNewsalaryOptionEmp> list = hrmsNewsalaryOptionEmpService.getAllByOptionId(record.getOptionId(), null);
        List<String> empList = list.stream().map(HrmsNewsalaryOptionEmp::getEmployeeId).collect(Collectors.toList());
        record.setEmpList(empList);
        map.put("tdx", CollectionUtils.isNotEmpty(empList) ? mapper.getDtx(record) : 0);
        return map;
    }

    @Override
    public Map<String, Map<String, Object>> makePayTableDataStatis(HrmsNewsalaryOptionPayroll record) {
        String computeDate = record.getComputeDate(); // 薪酬核算月份
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
            record.setComputeDate(computeDate);
        }
        Map<String, Map<String, Object>> map = new HashMap();
        // 本月入职定薪
        List<String> fixList = mapper.getMonthEntryFix(record);
        // 调薪
        List<String> changeList = mapper.getChangeFix(record);
        // 在职未定薪
        List<String> unFixList = mapper.getMonthEntryUnFix(record);
        Map<String, Object> fixMap = new HashMap<>();
        fixMap.put("count", fixList.size());
        fixMap.put("data", fixList);
        map.put("fix", fixMap);
        Map<String, Object> changeMap = new HashMap<>();
        changeMap.put("count", changeList.size());
        changeMap.put("data", changeList);
        map.put("change", changeMap);
        Map<String, Object> unFixMap = new HashMap<>();
        unFixMap.put("count", unFixList.size());
        unFixMap.put("data", unFixList);
        map.put("unfix", unFixMap);
        return map;
    }

    @Override
	@Transactional(readOnly = false)
    public void batchLockSalary(List<String> ids) {
        for (String id : ids) {
            HrmsNewsalaryOptionPayroll payroll = this.selectById(id);
            if (!StrUtil.equals("2", payroll.getComputeStatus()) && !StrUtil.equals("3", payroll.getComputeStatus())) {
                throw new BusinessException("方案未完成核算,请确认!");
            }
            if (StrUtil.equals("3", payroll.getComputeStatus())) {
                throw new BusinessException("方案已锁定,请确认!");
            }
            payroll.setComputeStatus("3");
            update(payroll);
        }
//        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("id", ids);
//        HrmsNewsalaryOptionPayroll optionPayroll = new HrmsNewsalaryOptionPayroll();
//        optionPayroll.setComputeStatus("3");
//        mapper.updateByExampleSelective(optionPayroll, example);
    }

    @Override
    public void batchPayslip(List<String> ids) {
        for (String id : ids) {
            HrmsNewsalaryOptionPayroll payroll = this.selectById(id);
            if (!StrUtil.equals("3", payroll.getComputeStatus())) {
                throw new BusinessException("方案未锁定,请确认!");
            }
        }
        Example example = new Example(HrmsNewsalaryOptionPayroll.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        HrmsNewsalaryOptionPayroll optionPayroll = new HrmsNewsalaryOptionPayroll();
        optionPayroll.setPaySlip("1");
        mapper.updateByExampleSelective(optionPayroll, example);
    }

    @Override
    public DataSet<CheckPersonnel> getCheperList(Page page, SearchListTable record) {
        Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
        Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
        Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类别
        // 时间
        String payrollDate = record.getPayrollDate(); // 核算时间/checkPersonnel
        if (StringUtil.isEmpty(payrollDate)) {
            payrollDate = DateUtils.getStringDateShortYM(new Date());
            record.setPayrollDate(payrollDate);
        }
        if (StrUtil.isNotBlank(record.getOrgId())) {
            PlatformResult<List<String>> orgResult = hrmsOrganizationFeignService
                    .getHrmsOrganizationAndNextList(record.getOrgId());
            List<String> orgList = orgResult.getObject();
            record.setOrgList(orgList);
        }
        List<CheckPersonnel> list = null;
        list = mapper.getCheperList(page, record);
        // 处理数据字典
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setPersonalIdentityText(personalIdentityDictMap.get(list.get(i).getPersonalIdentity()));
                list.get(i).setEstablishmentTypeText(establishmentTypeDictMap.get(list.get(i).getEstablishmentType()));
                list.get(i).setEmployeeStatusText(employeeStatusDictMap.get(list.get(i).getEmployeeStatus()));
            }
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    @Override
    public List<VueTableEntity> salaryConfirmTitle(HrmsNewsalaryOptionPayroll record) {
        Assert.hasText(record.getOptionId(), "薪酬方案ID不能为空.");
        String computeDate = record.getComputeDate(); // 薪酬核算月份
        List<VueTableEntity> calculateWagesTitle = null;
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
        }
        // 根据月份判断是否有数据
//        HrmsNewsalaryOptionPayroll calculationStatus = getCalculationStatus(record);
//		if(null == calculationStatus || "0".equals(calculationStatus.getComputeStatus())){  //没有计算记录数据
        calculateWagesTitle = getSalaryConfirmTitle(record.getOptionId());
//		}else{  //查历史表头
//			calculateWagesTitle = getSalaryConfirmHistoryTitle(record);
//		}
        return calculateWagesTitle;
    }

    /**
     * 薪酬核算-薪酬数据确认数据
     *
     * @param page
     * @param record
     * @return
     */
    @Override
    public DataSet<Map<String, String>> salaryConfirmData(Page page, HrmsNewsalaryOptionPayroll record) {
        Assert.hasText(record.getOptionId(), "薪酬方案ID不能为空.");
        String computeDate = record.getComputeDate(); // 薪酬核算月份
        List<Map<String, String>> calculateWagesData = null;
        if (StringUtil.isEmpty(computeDate)) {
            computeDate = DateUtils.getStringDateShortYM(new Date());
            record.setComputeDate(computeDate);
        }
        calculateWagesData = getSalaryConfirmData(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(),
                calculateWagesData);
    }

    @Override
    public Map<String, Object> preview(String optionId, String employeeId, String computeDate) {
        String year = computeDate.substring(0, 4);
        String month = computeDate.substring(5, 7);
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("year", year);
        map.put("month", month);
        Map<String, String> empMap = hrmsEmployeeMapper.getPreviewInfo(employeeId);
        map.put("基本信息", empMap);
        List<HrmsNewsalaryItemGroup> groupList = hrmsNewsalaryItemGroupService.getSalaryGroupByOptionId(optionId);
        List<Map<String, String>> salaryItemList = hrmsNewsalaryPayrollService.getPayrollByEmployeeId(employeeId,
                computeDate);
        for (HrmsNewsalaryItemGroup itemGroup : groupList) {
            Map<String, Object> salaryItemMap = new LinkedHashMap<>();
            for (Map<String, String> itemMap : salaryItemList) {
                if (StrUtil.equals(itemGroup.getId(), itemMap.get("groupId"))) {
                    salaryItemMap.put(itemMap.get("itemName"), itemMap.get("salary"));
                }
            }
            map.put(itemGroup.getItemGroup(), salaryItemMap);
        }
        return map;
    }

    @Override
    public DataSet<Map<String, Object>> salaryTotalCountData(Page page, SalaryCountSearchReq record) {
        if (StringUtil.isEmpty(record.getPayrollDate())) {
            record.setPayrollDate(record.getStartDate());
        }
        page.setSidx("t.sortNo");
        page.setSord("asc");
        page.setPageNo(1);
        page.setPageSize(Integer.MAX_VALUE);

        //获取报表配置的所有薪酬项目id
        List<String> itemIdList = new ArrayList<>();
        itemIdList.addAll(newsalaryReportMapMapper.queryItem(record.getReportId()));
        List<Map<String,String>> countFormulaItemList = newsalaryReportMapMapper.queryCountFormulaItem(record.getReportId());
        if (CollectionUtils.isNotEmpty(countFormulaItemList)){
            countFormulaItemList.forEach(map->{
                String countFormula = map.get("count_formula");
                List<String> _item = FormulaParse.getCountFormulaList(countFormula); // 单独是项目;
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(_item)) {
                    _item.forEach(item-> {
                        Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(item);
                        if (null != countFormulaCode && StringUtils.isNotEmpty(countFormulaCode.get("code"))){
                            itemIdList.add(countFormulaCode.get("code"));
                        }
                    });
                }
            });
        }

        List<HrmsNewsalaryReportTotalEo> totalList = itemMapper.salaryTotalCountTitle(record.getReportId());
        List<Map<String, Object>> allList = new ArrayList<>();
        List<Map<String, Object>> setList = null;
        if (CollectionUtils.isNotEmpty(totalList)) {
            List<String> reportMapList = totalMapper.getReportMapOptionList(record.getReportId());
            if(CollectionUtil.isNotEmpty(record.getOptionIds())){
                reportMapList = reportMapList.stream().filter(record.getOptionIds() :: contains).collect(Collectors.toList());
            }
            if (reportMapList.size() > 1) {
                List<List<String>> partition = Lists.partition(reportMapList, 2);
                partition.forEach(list -> {
                    List<Map<String, Object>> partList = mapper.salaryTotalCountData(page, record, totalList, list, CollectionUtil.distinct(itemIdList));
                    formulaCalculation(partList,countFormulaItemList);
                    allList.addAll(partList);
                });
            } else {
                List<Map<String, Object>> partList = mapper.salaryTotalCountData(page, record, totalList, reportMapList, CollectionUtil.distinct(itemIdList));
                formulaCalculation(partList,countFormulaItemList);
                allList.addAll(partList);
            }
            if (allList != null && allList.size() > 0) {
                //重新排序
                allList.sort(Comparator.comparing((Map<String,Object> h) -> (String)h.get("employee_id")).thenComparing(h->(String)h.get("payroll_date")).reversed());
//                setList = allList.stream().collect(
//                        Collectors.collectingAndThen(
//                                Collectors.toCollection(
//                                        () ->new TreeSet<>(Comparator.comparing(m->m.get("employee_id").toString()))
//                                ),ArrayList::new
//                        )
//                );

                for (int i = 0; i < allList.size(); i++) {
                    allList.get(i).put("no", i + 1);
                }

                //汇总数据
                Map<String, Object> totalData = new HashMap<>();
                for(HrmsNewsalaryReportTotalEo en : totalList){
                    totalData.put(en.getColCode(),allList.stream().map(vo -> new BigDecimal(String.valueOf(vo.get(en.getColCode())==null ? "0": vo.get(en.getColCode())))).reduce(BigDecimal.ZERO,BigDecimal::add));
                }
                totalData.put("payroll_date","合计");
                allList.add(totalData);
            }
        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), allList);
    }

    /**
     * 报表配置公式计算
     * @param partList
     * @param countFormulaItemList
     */
    private void formulaCalculation( List<Map<String, Object>> partList,List<Map<String,String>> countFormulaItemList){
        partList.stream().forEach(vo -> {
            //根据方案id过滤所有计算公式
            List<Map<String, String>> optionCountFormulaList = countFormulaItemList.stream()
                    .filter(cfi -> cfi.get("option_id").equalsIgnoreCase(vo.get("option_id").toString()))
                    .collect(Collectors.toList());
            //公式类数据计算
            newsalaryReportsStatisticsService.reportFormulaCalculation(vo,optionCountFormulaList);
            vo.keySet().removeIf(key -> key.indexOf("temp_")>-1);
        });
    }


    private List<VueTableEntity> getSalaryConfirmTitle(String optionId) {
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        // 此处后续需要优化，不能写死字段
        retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("岗位", "personal_identity_text", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("编制类型", "establishment_type_text", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("员工状态", "employee_status_text", null, null, "left"));
        // retVueTableEntity.add(new
        // VueTableEntity("定薪调薪日期","effective_date",null,null,"left"));
        // 取手工录入字段生成列头
        List<HrmsNewsalaryItem> itemList = hrmsNewsalaryItemService.getManualByOptionId(optionId);
        if (!itemList.isEmpty() && itemList.size() > 0) {
            int width = 20;
            itemList.forEach(item -> {
                retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getId(), null, item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width, null));
            });
        }
        retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, null, null));
        retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 120, null));
        return retVueTableEntity;
    }

    // 查询薪酬数据确认
    private List<VueTableEntity> getSalaryConfirmHistoryTitle(HrmsNewsalaryOptionPayroll record) {
        List<VueTableEntity> retVueTableEntity = new ArrayList<>();
        retVueTableEntity.add(new VueTableEntity("工号", "employee_no", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("姓名", "employee_name", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("部门", "orgName", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("岗位", "personal_identity_text", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("编制类型", "establishment_type_text", null, null, "left"));
        retVueTableEntity.add(new VueTableEntity("员工状态", "employee_status_text", null, null, "left"));
        // retVueTableEntity.add(new
        // VueTableEntity("定薪调薪日期","effective_date",null,null,"left"));
        List<HrmsNewsalaryItem> itemList = hrmsNewsalaryPayrollDetailService.getsalaryConfirmHistoryTitle(record);
        int width = 20;
        if (!itemList.isEmpty() && itemList.size() > 0) {
            itemList.forEach(item -> {
                retVueTableEntity.add(new VueTableEntity(item.getItemName(), item.getItemId(), null, item.getItemName().length() == 2 ? 80 : item.getItemName().length() * width, null));
            });
        }
        retVueTableEntity.add(new VueTableEntity("处理人", "update_user_name", null, null, null));
        retVueTableEntity.add(new VueTableEntity("最后处理日期", "update_date", null, 120, null));
        return retVueTableEntity;
    }

    /**
     * 判断员工所在方案在薪酬方案是否已锁定 1-已锁定 0-未锁定 -1 -未绑定方案
     * @param computeDate
     * @param employeeId
     * @return
     */
    @Override
    public Integer getIsLockByEmpComputeDate(String computeDate, String employeeId){
        Assert.hasText(computeDate,"算薪周期不能为空");
        Assert.hasText(employeeId,"员工id不能为空");
        return mapper.getIsLockByEmpComputeDate(computeDate,employeeId);
    }
}
