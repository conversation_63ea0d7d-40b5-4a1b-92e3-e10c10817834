<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.salary.dao.HrmsNewsalaryChangesDetailedMapper">

    <resultMap id="BaseResultMap" type="cn.trasen.hrms.salary.model.HrmsNewsalaryChangesDetailedEo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="employeeId" column="employee_id" jdbcType="VARCHAR"/>
            <result property="adjustValue" column="adjust_value" jdbcType="VARCHAR"/>
            <result property="nowValue" column="now_value" jdbcType="VARCHAR"/>
            <result property="abnormalItems" column="abnormal_items" jdbcType="VARCHAR"/>
            <result property="salaryCategory" column="salary_category" jdbcType="VARCHAR"/>
            <result property="establishmentType" column="establishment_type" jdbcType="VARCHAR"/>
            <result property="sourceType" column="source_type" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="is_deleted" jdbcType="CHAR"/>
            <result property="ssoOrgCode" column="sso_org_code" jdbcType="VARCHAR"/>
            <result property="ssoOrgName" column="sso_org_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,employee_no,employee_id,
        option_name,adjust_value,now_value,
        abnormal_items,salary_category,establishment_type,
        personal_identity,org_name,employee_name,
        update_cause,remark,
        create_date,create_user,create_user_name,
        update_date,update_user,update_user_name,
        is_deleted,sso_org_code,sso_org_name
    </sql>

    <select id="queryOrgName" resultType="java.lang.String">
        SELECT t3.name FROM cust_emp_base t1
        LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id where t1.employee_id = #{employeeId}
        and t1.is_deleted='N'
    </select>
    <select id="getDataList" resultType="cn.trasen.hrms.salary.model.HrmsNewsalaryChangesDetailedEo">
        SELECT * from (SELECT
        inc.id as id,inc.employee_id employeeId,inc.employee_no employeeNo,inc.reason reason,
        inc.employee_name employeeName,
        inc.old_gwdj adjustValue, inc.new_gwdj nowValue,
        inc.effective_date effectiveDate, inc.source_type as sourceType,
        inc.remark remark, org.name AS OrgName,
        org.organization_id AS employeeOrgId,
        emp.identity_number as identityNumber,
        org.name AS employeeOrgName,
        "岗位等级"as salaryName,emp.establishment_type as establishmentType,
        inc.create_date,
        IFNULL(inc.update_user_name,inc.create_user_name) as updateUserName,
        IFNULL(inc.update_date,inc.create_date) as updateDate
        FROM
        hrms_advancement_incident inc
        LEFT JOIN (
            SELECT identity_number,establishment_type,employee_id,org_id,is_deleted  FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT  identity_number,tmp_establishment as establishment_type,id as employee_id,org_id,is_deleted FROM hrms_employee_temporary where is_sync_salary = 'Y'
        ) emp ON emp.employee_id = inc.employee_id AND emp.is_deleted='N'
        LEFT JOIN comm_organization org ON org.organization_id = emp.org_id
        where inc.is_deleted ="N" and inc.approval_status = 4
        and (inc.old_gwdj is not null or inc.new_gwdj  is not null or inc.new_gwdj != '' or inc.old_gwdj !='')
        <if test="entity.effectiveDate != null and entity.effectiveDate != ''">
            and inc.effective_date like concat(#{entity.effectiveDate},'%')
        </if>
        <if test="entity.employeeId != null and entity.employeeId != ''">
            and inc.employee_id = #{entity.employeeId}
        </if>
        <if test="entity.employeeName != null and entity.employeeName != ''">
            and inc.employee_name like concat(#{entity.employeeName},'%')
        </if>
        <if test="entity.sourceType != null and entity.sourceType != ''">
            and inc.source_type = #{entity.sourceType}
        </if>

        UNION ALL
        SELECT
        inc.id as id,inc.employee_id employeeId,inc.employee_no employeeNo,inc.reason reason, inc.employee_name employeeName,
        inc.old_plgw adjustValue, inc.new_plgw nowValue, inc.effective_date effectiveDate,
         inc.source_type as sourceType, inc.remark remark, org.name AS OrgName,
         org.organization_id AS employeeOrgId, emp.identity_number as identityNumber,
          org.name AS employeeOrgName, "岗位类别"as salaryName,emp.establishment_type as establishmentType,
          inc.create_date, IFNULL(inc.update_user_name,inc.create_user_name) as updateUserName,
          IFNULL(inc.update_date,inc.create_date) as updateDate
        FROM
        hrms_advancement_incident inc
        LEFT JOIN (
            SELECT identity_number,establishment_type,employee_id,org_id,is_deleted  FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT  identity_number,tmp_establishment as establishment_type,id as employee_id,org_id,is_deleted FROM hrms_employee_temporary where is_sync_salary = 'Y'
        ) emp ON emp.employee_id = inc.employee_id AND emp.is_deleted='N'
        LEFT JOIN comm_organization org ON org.organization_id = emp.org_id
        where inc.is_deleted ="N" and inc.approval_status = 4
        and (inc.old_plgw is not null or inc.new_plgw  is not null or inc.new_plgw != '' or inc.old_plgw !='')
        <if test="entity.effectiveDate != null and entity.effectiveDate != ''">
            and inc.effective_date like concat(#{entity.effectiveDate},'%')
        </if>
        <if test="entity.employeeId != null and entity.employeeId != ''">
            and inc.employee_id = #{entity.employeeId}
        </if>
        <if test="entity.employeeName != null and entity.employeeName != ''">
            and inc.employee_name like concat(#{entity.employeeName},'%')
        </if>
        <if test="entity.sourceType != null and entity.sourceType != ''">
            and inc.source_type = #{entity.sourceType}
        </if>
        UNION ALL
        SELECT
        inc.id as id,inc.employee_id employeeId, inc.employee_no employeeNo,inc.reason reason, inc.employee_name employeeName,
        inc.old_salary_level_id adjustValue, inc.new_salary_level_id    nowValue, inc.effective_date effectiveDate,
        inc.source_type as sourceType, inc.remark remark, org.name AS OrgName,
        org.organization_id AS employeeOrgId, emp.identity_number as identityNumber,
        org.name AS employeeOrgName, "薪级等级"as salaryName,emp.establishment_type as establishmentType,
        inc.create_date, IFNULL(inc.update_user_name,inc.create_user_name) as updateUserName,
        IFNULL(inc.update_date,inc.create_date) as updateDate
        FROM
        hrms_advancement_incident inc
        LEFT JOIN (
            SELECT identity_number,establishment_type,employee_id,org_id,is_deleted  FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT  identity_number,tmp_establishment as establishment_type,id as employee_id,org_id,is_deleted FROM hrms_employee_temporary where is_sync_salary = 'Y'
        ) emp ON emp.employee_id = inc.employee_id AND emp.is_deleted='N'
        LEFT JOIN comm_organization org ON org.organization_id = emp.org_id
        where inc.is_deleted ="N" and inc.approval_status = 4
        and (inc.old_salary_level_id is not null or inc.new_salary_level_id  is not null or inc.new_salary_level_id != '' or inc.old_salary_level_id !='')
        <if test="entity.effectiveDate != null and entity.effectiveDate != ''">
            and inc.effective_date like concat(#{entity.effectiveDate},'%')
        </if>
        <if test="entity.employeeId != null and entity.employeeId != ''">
            and inc.employee_id = #{entity.employeeId}
        </if>
        <if test="entity.employeeName != null and entity.employeeName != ''">
            and inc.employee_name like concat(#{entity.employeeName},'%')
        </if>
        <if test="entity.sourceType != null and entity.sourceType != ''">
            and inc.source_type = #{entity.sourceType}
        </if>
        UNION ALL
        SELECT
        inc.id as id,inc.employee_id employeeId,inc.employee_no employeeNo,inc.reason reason, inc.employee_name employeeName,
        inc.old_salary_level_type adjustValue, inc.new_salary_level_type    nowValue, inc.effective_date effectiveDate,
        inc.source_type as sourceType, inc.remark remark, org.name AS OrgName,
        org.organization_id AS employeeOrgId, emp.identity_number as identityNumber,
        org.name AS employeeOrgName, "薪级类别"as salaryName,emp.establishment_type as establishmentType,
        inc.create_date, IFNULL(inc.update_user_name,inc.create_user_name) as updateUserName,
        IFNULL(inc.update_date,inc.create_date) as updateDate
        FROM
        hrms_advancement_incident inc
        LEFT JOIN (
            SELECT identity_number,establishment_type,employee_id,org_id,is_deleted  FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT  identity_number,tmp_establishment as establishment_type,id as employee_id,org_id,is_deleted FROM hrms_employee_temporary where is_sync_salary = 'Y'
        ) emp ON emp.employee_id = inc.employee_id AND emp.is_deleted='N'
        LEFT JOIN comm_organization org ON org.organization_id = emp.org_id
        where inc.is_deleted ="N" and inc.approval_status = 4
        and (inc.old_salary_level_type is not null or inc.new_salary_level_type  is not null or inc.new_salary_level_type != '' or inc.old_salary_level_type !='')
        <if test="entity.effectiveDate != null and entity.effectiveDate != ''">
            and inc.effective_date like concat(#{entity.effectiveDate},'%')
        </if>
        <if test="entity.employeeId != null and entity.employeeId != ''">
            and inc.employee_id = #{entity.employeeId}
        </if>
        <if test="entity.employeeName != null and entity.employeeName != ''">
            and inc.employee_name like concat(#{entity.employeeName},'%')
        </if>
        <if test="entity.sourceType != null and entity.sourceType != ''">
            and inc.source_type = #{entity.sourceType}
        </if>
        union all

        SELECT
        cd.id,cd.employee_id as employeeId,cd.employee_no employeeNo,cd.reason reason, cd.employee_name as employeeName,
        cd.adjust_value adjustValue,cd.now_value nowValue,
        cd.effective_date effectiveDate, cd.source_type as sourceType,
        cd.remark remark,
        org.name AS OrgName,org.organization_id AS employeeOrgId,  emp.identity_number as identityNumber,
        org.name AS employeeOrgName,
        ib.basic_item_name as salaryName,emp.establishment_type as establishmentType,cd.create_date,
        IFNULL(cd.update_user_name,cd.create_user_name) as updateUserName,
        IFNULL(cd.update_date,cd.create_date) as updateDate
        from hrms_newsalary_changes_detailed cd
        LEFT JOIN (
            SELECT identity_number,establishment_type,employee_id,org_id,is_deleted  FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
            UNION ALL
            SELECT  identity_number,tmp_establishment as establishment_type,id as employee_id,org_id,is_deleted FROM hrms_employee_temporary where is_sync_salary = 'Y'
        ) emp ON emp.employee_id = cd.employee_id AND emp.is_deleted='N'
        LEFT JOIN comm_organization org ON org.organization_id = emp.org_id
        LEFT JOIN hrms_newsalary_basic_column ib on ib.id = cd.abnormal_items
        where 1=1
        <if test="entity.effectiveDate != null and entity.effectiveDate != ''">
            and cd.effective_date like concat(#{entity.effectiveDate},'%')
        </if>
        <if test="entity.employeeName != null and entity.employeeName != ''">
            and cd.employee_name like concat(#{entity.employeeName},'%')
        </if>
        <if test="entity.sourceType != null and entity.sourceType != ''">
            and cd.source_type = #{entity.sourceType}
        </if>
        <if test="entity.employeeId != null and entity.employeeId != ''">
            and cd.employee_id = #{entity.employeeId}
        </if>
        and cd.is_deleted ="N"
        ) cd

    </select>

    <!-- 根据员工id 获取时间段内的薪酬调整 -->
    <select id="getEmployeeNewsalaryChangesGroupByDate" resultType="java.util.Map">
        select effective_date,reason from hrms_newsalary_changes_detailed where employee_id = #{employeeId}
        and DATE_FORMAT(effective_date, '%Y-%m-%d') >= DATE_FORMAT(#{startDate}, '%Y-%m-%d')
         and DATE_FORMAT(effective_date, '%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{endDate}, '%Y-%m-%d')
         group by effective_date,reason
    </select>
</mapper>
