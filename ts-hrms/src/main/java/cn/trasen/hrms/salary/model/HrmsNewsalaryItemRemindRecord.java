package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬提醒--薪资异动办理记录表
 *
 */
@Table(name = "hrms_newsalary_item_remind_record")
@Setter
@Getter
public class HrmsNewsalaryItemRemindRecord {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 算薪周期
     */
    @Column(name = "compute_date")
    @ApiModelProperty(value = "算薪周期")
    private String computeDate;

    /**
     * 薪酬提醒ID
     */
    @Column(name = "remind_id")
    @ApiModelProperty(value = "薪酬提醒ID")
    private String remindId;

    /**
     * 薪酬项目ID
     */
    @Column(name = "item_id")
    @ApiModelProperty(value = "薪酬项目ID")
    private String itemId;

    /**
     * 是否补扣 0-不补补扣 1-补 2-扣
     */
    @Column(name = "is_back_payment")
    @ApiModelProperty(value = "是否补扣 0-不补补扣 1-补 2-扣")
    private String isBackPayment;

    /**
     * 补缴开始月份
     */
    @Column(name = "back_payment_date")
    @ApiModelProperty(value = "补缴开始月份")
    private String backPaymentDate;

    /**
     * 员工id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 编制类型
     */
    @Column(name = "establishment_type")
    @ApiModelProperty(value = "编制类型")
    private String establishmentType;

    /**
     * 调整前金额
     */
    @Column(name = "before_salary")
    @ApiModelProperty(value = "调整前金额")
    private BigDecimal beforeSalary;

    /**
     * 调整后金额
     */
    @Column(name = "after_salary")
    @ApiModelProperty(value = "调整后金额")
    private BigDecimal afterSalary;

    /**
     * 差额
     */
    @Column(name = "difference_amount")
    @ApiModelProperty(value = "差额")
    private BigDecimal differenceAmount;

    /**
     * 补缴月数
     */
    @Column(name = "back_payment_month")
    @ApiModelProperty(value = "补缴月数")
    private String backPaymentMonth;

    /**
     * 处理状态 0-未处理 1-已处理
     */
    @Column(name = "handle_status")
    @ApiModelProperty(value = "处理状态 0-未处理 1-已处理 2-不处理")
    private String handleStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "累计应补金额")
    private BigDecimal totalSupplementedAmount;

    @Transient
    @ApiModelProperty(value = "累计应扣金额")
    private BigDecimal totalDeductedAmount;

    @Transient
    @ApiModelProperty(value = "累计总差金额")
    private BigDecimal totalDifferenceAmount;

    @Transient
    @ApiModelProperty(value = "序号")
    private Integer no;

    @Transient
    @ApiModelProperty(value = "异动时间")
    private String createDateStr;

    @Transient
    @ApiModelProperty(value = "处理时间")
    private String updateDateStr;

    @Transient
    @ApiModelProperty(value = "总差额")
    private BigDecimal totalAmount;

    @Transient
    @ApiModelProperty(value = "所在组织id")
    private String orgId;

    @Transient
    @ApiModelProperty(value = "所在组织名称")
    private String orgName;

    @Transient
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    @Transient
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    @Transient
    @ApiModelProperty(value = "岗位名称id")
    private String personalIdentity;

    @Transient
    @ApiModelProperty(value = "岗位名称")
    private String personalIdentityName;

    @Transient
    @ApiModelProperty(value = "编制类型名称")
    private String establishmentTypeName;

    @Transient
    @ApiModelProperty(value = "员工状态名称")
    private String employeeStatusName;

    @Transient
    @ApiModelProperty(value = "性别")
    private String gender;

    @Transient
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;

    @Transient
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @Transient
    @ApiModelProperty(value = "加减项 1加项 2减项3不参与计算")
    private String countType;

    @Transient
    @ApiModelProperty(value = "薪酬调整项目类型")
    private String tmpItem;

    @Transient
    @ApiModelProperty(value = "员工状态列表")
    private List<String> employeeStatuses;

    @Transient
    @ApiModelProperty(value = "员工编制类型列表")
    private List<String> establishmentTypes;

    @Transient
    @ApiModelProperty(value = "薪资异动数据id集合")
    private List<String> ids;
}