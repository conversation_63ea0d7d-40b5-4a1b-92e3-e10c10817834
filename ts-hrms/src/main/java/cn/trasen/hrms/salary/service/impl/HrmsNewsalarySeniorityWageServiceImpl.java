package cn.trasen.hrms.salary.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.salary.dao.HrmsNewsalarySeniorityWageMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalarySeniorityWage;
import cn.trasen.hrms.salary.service.HrmsNewsalarySeniorityWageService;
import cn.trasen.hrms.utils.IdUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsNewsalarySeniorityWageServiceImpl
 * @Description TODO
 * @date 2024��4��12�� ����11:08:24
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalarySeniorityWageServiceImpl implements HrmsNewsalarySeniorityWageService {

	@Autowired
	private HrmsNewsalarySeniorityWageMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsNewsalarySeniorityWage record) {
		if(StringUtil.isEmpty(record.getId())){
			record.setId(IdUtil.getId());
		}
		record.setCreateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsNewsalarySeniorityWage record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalarySeniorityWage record = new HrmsNewsalarySeniorityWage();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalarySeniorityWage selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsNewsalarySeniorityWage> getDataSetList(Page page, HrmsNewsalarySeniorityWage record) {
		Example example = new Example(HrmsNewsalarySeniorityWage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalarySeniorityWage> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsNewsalarySeniorityWage> getAllList() {
		Example example = new Example(HrmsNewsalarySeniorityWage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		return mapper.selectByExample(example);
	}

	//定时任务修改
	@Transactional(readOnly = false)
	@Override
	public void tsakSave(HrmsNewsalarySeniorityWage saveData) {
		//先根据人员删除数据
		Example example = new Example(HrmsNewsalarySeniorityWage.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", saveData.getEmployeeId());
		mapper.deleteByExample(example);
		save(saveData);

	}
}
