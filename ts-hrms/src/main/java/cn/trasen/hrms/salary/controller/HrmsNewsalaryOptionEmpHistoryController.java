package cn.trasen.hrms.salary.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryOptionEmpHistory;
import cn.trasen.hrms.salary.service.HrmsNewsalaryOptionEmpHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryOptionEmpHistoryController
 * @Description 薪酬人员-方案关联表历史表
 * @date 2023��11��11�� ����4:36:51
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬人员-方案关联表历史表Controller")
public class HrmsNewsalaryOptionEmpHistoryController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryOptionEmpHistoryController.class);

	@Autowired
	private HrmsNewsalaryOptionEmpHistoryService hrmsNewsalaryOptionEmpHistoryService;

	/**
	 * @Title saveHrmsNewsalaryOptionEmpHistory
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryOptionEmpHistory/save")
	public PlatformResult<String> saveHrmsNewsalaryOptionEmpHistory(@RequestBody HrmsNewsalaryOptionEmpHistory record) {
		try {
			hrmsNewsalaryOptionEmpHistoryService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryOptionEmpHistory
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryOptionEmpHistory/update")
	public PlatformResult<String> updateHrmsNewsalaryOptionEmpHistory(@RequestBody HrmsNewsalaryOptionEmpHistory record) {
		try {
			hrmsNewsalaryOptionEmpHistoryService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsNewsalaryOptionEmpHistoryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryOptionEmpHistory>
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryOptionEmpHistory/{id}")
	public PlatformResult<HrmsNewsalaryOptionEmpHistory> selectHrmsNewsalaryOptionEmpHistoryById(@PathVariable String id) {
		try {
			HrmsNewsalaryOptionEmpHistory record = hrmsNewsalaryOptionEmpHistoryService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsNewsalaryOptionEmpHistoryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryOptionEmpHistory/delete/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryOptionEmpHistoryById(@PathVariable String id) {
		try {
			hrmsNewsalaryOptionEmpHistoryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryOptionEmpHistoryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryOptionEmpHistory>
	 * @date 2023��11��11�� ����4:36:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryOptionEmpHistory/list")
	public DataSet<HrmsNewsalaryOptionEmpHistory> selectHrmsNewsalaryOptionEmpHistoryList(Page page, HrmsNewsalaryOptionEmpHistory record) {
		return hrmsNewsalaryOptionEmpHistoryService.getDataSetList(page, record);
	}
}
