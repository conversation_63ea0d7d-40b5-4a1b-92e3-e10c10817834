package cn.trasen.hrms.salary.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItem;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemBasic;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemBasicService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsNewsalaryItemController
 * @Description 薪酬项目表
 * @date 2023��11��11�� ����4:34:26
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "薪酬项目Controller")
public class HrmsNewsalaryItemController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsNewsalaryItemController.class);

	@Autowired
	private HrmsNewsalaryItemService hrmsNewsalaryItemService;

	@Autowired
	private HrmsNewsalaryItemBasicService hrmsNewsalaryItemBasicService;

	/**
	 * @Title saveHrmsNewsalaryItem
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryItem/save")
	public PlatformResult<String> saveHrmsNewsalaryItem(@RequestBody HrmsNewsalaryItem record) {
		try {
			hrmsNewsalaryItemService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsNewsalaryItem
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryItem/update")
	public PlatformResult<String> updateHrmsNewsalaryItem(@RequestBody HrmsNewsalaryItem record) {
		try {
			hrmsNewsalaryItemService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 *
	 * @Title selectHrmsNewsalaryItemById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsNewsalaryItem>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryItem/{id}")
	public PlatformResult<HrmsNewsalaryItem> selectHrmsNewsalaryItemById(@PathVariable String id) {
		try {
			HrmsNewsalaryItem record = hrmsNewsalaryItemService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	/**
	 *
	 * @Title deleteHrmsNewsalaryItemById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryItem/delete/{uid}/{id}")
	public PlatformResult<String> deleteHrmsNewsalaryItemById(@PathVariable String uid,@PathVariable String id) {
		try {
			hrmsNewsalaryItemService.deleteById(uid,id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsNewsalaryItemList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsNewsalaryItem>
	 * @date 2023��11��11�� ����4:34:26
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/salaryItem/list")
	public DataSet<HrmsNewsalaryItem> selectHrmsNewsalaryItemList(Page page, HrmsNewsalaryItem record) {
		return hrmsNewsalaryItemService.getDataSetList(page, record);
	}

	@ApiOperation(value = "获取所有基础项", notes = "获取所有基础项")
	@GetMapping("/api/salaryItem/allList")
	public PlatformResult<List<HrmsNewsalaryItemBasic>> getAllList(Page page, HrmsNewsalaryItemBasic record) {
		try {
			page.setPageSize(Integer.MAX_VALUE);
			List<HrmsNewsalaryItemBasic> rows = hrmsNewsalaryItemService.getDataItemBasicList(page, record);
			return PlatformResult.success(rows);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "获取导入工资项页面数据", notes = "获取导入工资项页面数据")
	@GetMapping("/api/salaryItem/getItemBasicData")
	public PlatformResult<Map<String,Object>> getItemBasicData() {
		try {
			Map<String,Object> retMap = hrmsNewsalaryItemBasicService.getItemBasicData();
			return PlatformResult.success(retMap);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "根据薪酬方案获取所有工资项", notes = "根据薪酬方案获取所有工资项")
	@GetMapping("/api/salaryItem/getByOption/{optionId}")
	public PlatformResult<List<HrmsNewsalaryItem>> getByOption(@PathVariable String optionId) {
		try {
			List<HrmsNewsalaryItem> rows = hrmsNewsalaryItemService.getByOption(optionId);
			return PlatformResult.success(rows);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	//工资条配置接口
	@ApiOperation(value = "设置工资条", notes = "设置工资条")
	@PostMapping("/api/salaryItem/paySheet")
	public PlatformResult<String> paySheet(@RequestBody List<HrmsNewsalaryItem> record) {
		try {
			hrmsNewsalaryItemService.paySheet(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "设置排序", notes = "设置排序")
	@PostMapping("/api/salaryItem/sortNo")
	public PlatformResult<String> seqNo(@RequestBody List<HrmsNewsalaryItem> record) {
		try {
			hrmsNewsalaryItemService.sortNo(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "获取导入工资项项目库数据", notes = "获取导入工资项项目库数据")
	@GetMapping("/api/salaryItem/getItemLibraryData")
	public PlatformResult<Map<String,Object>> getItemLibraryData() {
		try {
			Map<String,Object> retMap = hrmsNewsalaryItemBasicService.getItemLibraryData();
			return PlatformResult.success(retMap);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	@ApiOperation(value = "启用/禁用", notes = "启用/禁用")
	@PostMapping("/api/salaryItem/enable/{uid}/{isEable}")
	public PlatformResult enable(@PathVariable String uid,@PathVariable String isEable) {
		try {
			hrmsNewsalaryItemService.enable(uid,isEable);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
