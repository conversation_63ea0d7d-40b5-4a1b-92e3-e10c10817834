package cn.trasen.hrms.voluntaries.controller;

import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.hrms.voluntaries.model.HrmsVoluntariesActivityRecord;
import cn.trasen.hrms.voluntaries.service.HrmsVoluntariesActivityRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsVoluntariesActivityRecordController
 * @Description TODO
 * @date 2023��9��7�� ����7:21:30
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "志愿者活动记录表")
public class HrmsVoluntariesActivityRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsVoluntariesActivityRecordController.class);

	@Autowired
	private HrmsVoluntariesActivityRecordService hrmsVoluntariesActivityRecordService;

	/**
	 * @Title saveHrmsVoluntariesActivityRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��9��7�� ����7:21:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/voluntariesActivityRecord/save")
	public PlatformResult<String> saveHrmsVoluntariesActivityRecord(@RequestBody HrmsVoluntariesActivityRecord record) {
		try {
			hrmsVoluntariesActivityRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "活动结束之后新增", notes = "活动结束之后新增")
	@PostMapping("/api/voluntariesActivityRecord/laterSave")
	public PlatformResult<String> laterSave(@RequestBody List<HrmsVoluntariesActivityRecord> record) {
		try {
			if(record != null && record.size() >0){
					hrmsVoluntariesActivityRecordService.laterSave(record);
			}else{
				throw new BusinessException("请选择人员");
			}

			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}


	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/voluntariesActivityRecord/batchUpdateDuration")
	public PlatformResult<String> batchUpdateDuration(@RequestBody HrmsVoluntariesActivityRecord record) {
		try {
			if(!StringUtil.isEmpty(record.getIds()) && !StringUtil.isEmpty(record.getActivityDurations())){
				String[] ids = record.getIds().split(",");
				String[] ads =  record.getActivityDurations().split(",", -1);
				if(ids != null && ids.length >0){
					for (int i=0;i<ids.length;i++) {
						HrmsVoluntariesActivityRecord _bean = new HrmsVoluntariesActivityRecord();
						_bean.setId(ids[i]);
						_bean.setActivityDuration(ads[i]);
						hrmsVoluntariesActivityRecordService.updatePrimaryKey(_bean);
					}
				}
			}else{
				throw new BusinessException("修改记录条数为空");
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsVoluntariesActivityRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��9��7�� ����7:21:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/voluntariesActivityRecord/update")
	public PlatformResult<String> updateHrmsVoluntariesActivityRecord(@RequestBody HrmsVoluntariesActivityRecord record) {
		try {
			hrmsVoluntariesActivityRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsVoluntariesActivityRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsVoluntariesActivityRecord>
	 * @date 2023��9��7�� ����7:21:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/voluntariesActivityRecord/{id}")
	public PlatformResult<HrmsVoluntariesActivityRecord> selectHrmsVoluntariesActivityRecordById(@PathVariable String id) {
		try {
			HrmsVoluntariesActivityRecord record = hrmsVoluntariesActivityRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsVoluntariesActivityRecordById
	 * @Description 根据ID删除
	 * @param
	 * @return PlatformResult<String>
	 * @date 2023��9��7�� ����7:21:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/voluntariesActivityRecord/delete")
	public PlatformResult<String> deleteHrmsVoluntariesActivityRecordById(@RequestBody HrmsVoluntariesActivityRecord record) {
		try {

			if(record != null && !StringUtil.isEmpty(record.getIds())){
				List<String> ids = Arrays.asList(record.getIds().split(","));
				for(int i=0;i<ids.size();i++){
					hrmsVoluntariesActivityRecordService.deleteById(ids.get(i));
				}
			}else{
				throw new BusinessException("请选择要删除的人员");
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsVoluntariesActivityRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsVoluntariesActivityRecord>
	 * @date 2023��9��7�� ����7:21:30
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/voluntariesActivityRecord/list")
	public DataSet<HrmsVoluntariesActivityRecord> selectHrmsVoluntariesActivityRecordList(Page page, HrmsVoluntariesActivityRecord record) {
		return hrmsVoluntariesActivityRecordService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @MethodName: getActivityRecordListByIdcard
	 * @Description: TODO
	 * <AUTHOR>
	 * @param idcard
	 * @return PlatformResult<List<HrmsVoluntariesActivityRecord>>
	 * @date 2023-09-13 04:33:45
	 */
	@ApiOperation(value = "身份证号码查询个人活动明细", notes = "身份证号码查询个人活动明细")
	@GetMapping("/api/voluntariesActivityRecord/getActivityRecordListByIdcard")
	public PlatformResult<List<HrmsVoluntariesActivityRecord>> getActivityRecordListByIdcard(String idcard) {
		try {
			List<HrmsVoluntariesActivityRecord> record = hrmsVoluntariesActivityRecordService.getActivityRecordListByIdcard(idcard);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
