package cn.trasen.hrms.train.controller;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.train.model.HrmsTrainScore;
import cn.trasen.hrms.train.service.HrmsTrainScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsTrainScoreController
 * @Description TODO
 * @date 2023��5��18�� ����5:37:24
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsTrainScoreController")
public class HrmsTrainScoreController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsTrainScoreController.class);

	@Autowired
	private HrmsTrainScoreService hrmsTrainScoreService;

	/**
	 * @Title saveHrmsTrainScore
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/trainscore/save")
	public PlatformResult<String> saveHrmsTrainScore(@RequestBody HrmsTrainScore record) {
		try {
			hrmsTrainScoreService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsTrainScore
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/trainscore/update")
	public PlatformResult<String> updateHrmsTrainScore(@RequestBody HrmsTrainScore record) {
		try {
			hrmsTrainScoreService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsTrainScoreById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsTrainScore>
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据培训id查询评分", notes = "根据培训id查询评分")
	@GetMapping("/api/trainscore/{trainPlanId}")
	public PlatformResult<HrmsTrainScore> selectHrmsTrainScoreById(@PathVariable String trainPlanId) {
		try {
			HrmsTrainScore record = hrmsTrainScoreService.selectById(trainPlanId);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsTrainScoreById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/trainscore/delete/{id}")
	public PlatformResult<String> deleteHrmsTrainScoreById(@PathVariable String id) {
		try {
			hrmsTrainScoreService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsTrainScoreList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsTrainScore>
	 * @date 2023��5��18�� ����5:37:24
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/trainscore/list")
	public DataSet<HrmsTrainScore> selectHrmsTrainScoreList(Page page, HrmsTrainScore record) {
		return hrmsTrainScoreService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "评分榜列表", notes = "评分榜列表")
	@GetMapping("/api/trainscore/scoreboard")
	public DataSet<HrmsTrainScore> scoreboard(Page page, HrmsTrainScore record) {
		return hrmsTrainScoreService.scoreboard(page, record);
	}
	
	@ApiOperation(value = "评分榜导出", notes = "评分榜导出")
	@GetMapping("/api/trainscore/scoreboardexport")
	public void scoreboardexport(HttpServletResponse response, HttpServletRequest request,Page page, HrmsTrainScore record) {
		page.setPageSize(Integer.MAX_VALUE);
		 try {
			 DataSet<HrmsTrainScore> scoreboard = hrmsTrainScoreService.scoreboard(page, record);
			 List<HrmsTrainScore> list = scoreboard.getRows();
			 if(list != null && list.size() >0) {
				 for (int j = 0; j < list.size(); j++) {
					 list.get(j).setNo(String.valueOf(j+1));
				}
			 }
			 
			//表头标题
			String name = "培训管理评分榜" + DateUtil.format(new Date(),"yyyyMMdd") + ".xlsx";
			// 模板位置
			String templateUrl = "template/hrmsTrainScoreBoard.xlsx";
			
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("list", list);
			cn.trasen.BootComm.excel.utils.ExportUtil.export(request, response, map, name, templateUrl);
		} catch (UnsupportedEncodingException e) {
			logger.error(e.getMessage(), e);
		}
	}
}
