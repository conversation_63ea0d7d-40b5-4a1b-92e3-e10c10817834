package cn.trasen.hrms.ywgl.server.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.HrmsSchedulingFrequencyMapper;
import cn.trasen.hrms.dao.OrgMappingMapper;
import cn.trasen.hrms.hlgl.bean.HlglBean;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsSchedulingFrequency;
import cn.trasen.hrms.model.HrmsSchedulingManage;
import cn.trasen.hrms.model.OrgMapping;
import cn.trasen.hrms.service.HrmsSchedulingManageService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.utils.RgbUtils;
import cn.trasen.hrms.ywgl.mapper.CallYwglMapper;
import cn.trasen.hrms.ywgl.server.CallYwglService;

/**   
 * @ClassName:  CallHlglServiceImpl   
 * @Description:调用实现类   
 * @author: WZH
 * @date:   2021年11月15日 下午3:34:08      
 * @Copyright:  
 */
@Service
public class CallYwglServiceImpl implements CallYwglService {
	
	@Autowired
	private CallYwglMapper callYwglMapper;  //取护理管理系统的数据 
	
	@Autowired
	private HrmsSchedulingManageService hrmsSchedulingManageService;  
	
	@Autowired
	private OrgMappingMapper orgMappingMapper;  //科室映射接口
	
	@Autowired
	private HrmsSchedulingFrequencyMapper hrmsSchedulingFrequencyMapper; //班次
	
	@Override
	@Transactional
	public void findAllDate(Map<String, String> parMap) {
		List<HrmsSchedulingManage> insertSMBean = new ArrayList<>();  //要插入的排班信息
		//人员身份证映射map
		List<OrgMapping> orgList = orgMappingMapper.getOrgIdNameMapping();
		List<HrmsEmployee> empMapping = orgMappingMapper.getEmpAllDate();
		Map<String, HrmsEmployee> empMap = empMapping.stream().collect(Collectors.toMap(HrmsEmployee::getIdentityNumber, Function.identity(), (entity1,entity2) -> entity1));
		Map<String, String> orgMap = orgList.stream().collect(Collectors.toMap(OrgMapping::getOrgId, OrgMapping::getOrgName, (entity1,entity2) -> entity1));
		List<HlglBean> findAllDate = callYwglMapper.findAllDate(parMap);
		//根据名称匹配
		if(findAllDate != null && findAllDate.size() > 0) {
			for (int i = 0; i < findAllDate.size(); i++) {
				HlglBean hlglBean = findAllDate.get(i);
				HrmsEmployee hrmsEmployee = empMap.get(hlglBean.getIdCard());
				if(hrmsEmployee != null && null != hlglBean .getFrequencyId()) {
					//判断科室是否有这个排班 ，没有就新增
					Map<String,String> freMap = new HashMap<>();
					freMap.put("hlbc", hlglBean.getFrequencyId()); //班次id
					freMap.put("orgId", hrmsEmployee.getOrgId()); //班次名称
					HrmsSchedulingFrequency HScFBean = hrmsSchedulingFrequencyMapper.getYwglFre(freMap);  //取医务的班次
					String  id = IdUtil.getId();
					
					if(null == HScFBean) {  //添加班次信息
						HrmsSchedulingFrequency hsBean = new HrmsSchedulingFrequency();
						hsBean.setFrequencyId(id);
						hsBean.setYwbc(hlglBean.getFrequencyId());
						hsBean.setFrequencyName(hlglBean.getFrequencyName());
						hsBean.setOrgId(hrmsEmployee.getOrgId());
						hsBean.setOrgName(orgMap.get(hrmsEmployee.getOrgId()));
						hsBean.setIsDeleted(Contants.IS_DELETED_FALSE);
						hsBean.setFrequencyTime(hlglBean.getStartTime() + " - " + hlglBean.getEndTime());
						hsBean.setDayLong(hlglBean.getAttendance());   
						hsBean.setRemark("医务系统同步数据");
						hsBean.setCreateDate(new Date());
						//处理颜色 
						if(!StringUtil.isEmpty(hlglBean.getFrequencyColor())) {
							String rgb = hlglBean.getFrequencyColor();
							String _str = rgb.substring(rgb.indexOf("(")+1, rgb.indexOf(")"));
							String[] split = _str.split(",");
							String rgb2Hex = RgbUtils.rgb2Hex(Integer.valueOf(split[0].trim()), Integer.valueOf(split[1].trim()), Integer.valueOf(split[2].trim()));
							hsBean.setFrequencyColour(rgb2Hex);
						}else {
							hsBean.setFrequencyColour("#000000");
						}
						//处理班次类型  护理班次（ 1 白班2行政班3夜班4晚班5两头班 6休班）
						if("2".equals(hlglBean.getFrequencyType()) || "3".equals(hlglBean.getFrequencyType())) {
							hsBean.setFrequencyType("4");  //全部设置为晚夜班
						}else {
							hsBean.setFrequencyType("1");  //全部设置为出勤
						}
						if("1".equals(hsBean.getFrequencyType()) || "4".equals(hsBean.getFrequencyType())) { 
							String frequencyTime = hsBean.getFrequencyTime();
							String[] split = frequencyTime.split(",");
							double countDate = 0;
							String min = "2021-05-01 ";
							String max = "2021-05-02 ";
							for (int s = 0; s < split.length; s++) {
								String[] split2 = split[s].split(" - ");  //00:00-04:00
								if(DateUtils.judgeSize(min +split2[0], min +split2[1])) {
									countDate += DateUtils.dateDiff(min +split2[0], min +split2[1]);
								}else {
									countDate += DateUtils.dateDiff(min +split2[0], max +split2[1]);
								}
							}
							hsBean.setDayHours(String.format("%.1f", (countDate / 60)));
						}
						hsBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						hrmsSchedulingFrequencyMapper.insertSelective(hsBean);
					}else {
						//判断时间段是不是一样 ，不一样 修改时间段 符合人事系统数据格式 合并为一个班次
						id = HScFBean.getFrequencyId();
						if(!HScFBean.getFrequencyTime().contains(hlglBean.getStartTime() + " - " + hlglBean.getEndTime())) {
							HScFBean.setFrequencyTime(HScFBean.getFrequencyTime()+ "," + hlglBean.getStartTime() + " - " + hlglBean.getEndTime());
							if("1".equals(HScFBean.getFrequencyType()) || "4".equals(HScFBean.getFrequencyType())) { 
								String frequencyTime = HScFBean.getFrequencyTime();
								String[] split = frequencyTime.split(",");
								double countDate = 0;
								String min = "2021-05-01 ";
								String max = "2021-05-02 ";
								for (int s = 0; s < split.length; s++) {
									String[] split2 = split[s].split(" - ");  //00:00-04:00
									if(DateUtils.judgeSize(min +split2[0], min +split2[1])) {
										countDate += DateUtils.dateDiff(min +split2[0], min +split2[1]);
									}else {
										countDate += DateUtils.dateDiff(min +split2[0], max +split2[1]);
									}
								}
								HScFBean.setDayHours(String.format("%.1f", (countDate / 60)));
							}
							hrmsSchedulingFrequencyMapper.updateByPrimaryKey(HScFBean);
						}
					}
					//排班信息
					HrmsSchedulingManage hsmBean = new HrmsSchedulingManage();
					hsmBean.setSchedulingId(IdUtil.getId());
					hsmBean.setSchedulingDate(hlglBean.getFrequencyTime());
					hsmBean.setEmployeeId(hrmsEmployee.getEmployeeId());
					hsmBean.setFrequencyId(id);
					hsmBean.setEmpOrgId(hrmsEmployee.getOrgId());
					hsmBean.setRemark("医务系统同步数据");
					hsmBean.setCreateDate(new Date());
					hsmBean.setIsDeleted(Contants.IS_DELETED_FALSE);
					hsmBean.setSync("2");
					insertSMBean.add(hsmBean);
				}
			}
			hrmsSchedulingManageService.deleteBySyncYw(parMap);  //先删除
			//去重添加
	        List<HrmsSchedulingManage> distinctList = insertSMBean.stream().filter(distinctByKey(distinctByKeyFunction())).collect(Collectors.toList());
	        hrmsSchedulingManageService.syncBatchInsert(distinctList);  //添加数据
	        syncMaternityLeaveDate();
		}
	}
	
	/**   
	 * @Title: syncMaternityLeaveDate   
	 * @Description: 同步产假数据  
	 * @param: @param parMap      
	 * @return: void      
	 * @throws   
	 */
	public void syncMaternityLeaveDate() {
		
		List<HrmsEmployee> empMapping = orgMappingMapper.getEmpAllDate();
		Map<String, HrmsEmployee> empMap = empMapping.stream().collect(Collectors.toMap(HrmsEmployee::getIdentityNumber, Function.identity(), (entity1,entity2) -> entity1));
		
		//查询所有产假数据
		List<HlglBean> maternityLeaveDate =  callYwglMapper.getMaternityLeaveDate();
		List<HrmsSchedulingManage> batchInsert = new ArrayList<>();
		if (maternityLeaveDate != null && maternityLeaveDate.size() > 0) {
			for (int i = 0; i < maternityLeaveDate.size(); i++) {
				HlglBean hlglBean = maternityLeaveDate.get(i);
				HrmsEmployee hrmsEmployee = empMap.get(hlglBean.getIdCard());
				//查询人员的 
				List<String> days = DateUtils.getDays(hlglBean.getStartTime(), hlglBean.getEndTime());
				for(int k=0;k<days.size();k++) {
					Map<String,String> parEmpMap = new HashMap<>();
					parEmpMap.put("employeeId", hrmsEmployee.getEmployeeId());
					parEmpMap.put("date", days.get(k));
					//查询是否有排班数据，没有就添加
					List<HrmsSchedulingManage>  listHsm = hrmsSchedulingManageService.getDataByEmpIdAndDate(parEmpMap);
					if(listHsm != null) {
						HrmsSchedulingManage hsm = new HrmsSchedulingManage();
						hsm.setSchedulingId(IdUtil.getId());
						hsm.setSchedulingDate(days.get(k));
						hsm.setEmployeeId(hrmsEmployee.getEmployeeId());
						hsm.setFrequencyId("******************");  //通用 产假id
						hsm.setEmpOrgId(hrmsEmployee.getOrgId());
						hsm.setRemark("医务系统同步数据");
						hsm.setCreateDate(new Date());
						hsm.setIsDeleted(Contants.IS_DELETED_FALSE);
						hsm.setSync("2");
						batchInsert.add(hsm);
					}
				}
			}
			hrmsSchedulingManageService.batchInsert(batchInsert,false);
		}
	}
	
	public static <T> Predicate<T> distinctByKey(Function<? super T, ?> function) {
		Map<Object, Boolean> seen = new ConcurrentHashMap<>();
		return t -> seen.putIfAbsent(function.apply(t), Boolean.TRUE) == null;
	}
	//同一天 同一个班次id 作为一条排班数据
	public static Function<HrmsSchedulingManage, String> distinctByKeyFunction() {
		return (HrmsSchedulingManage dish) -> dish.getSchedulingDate() + "-" + dish.getEmployeeId() + "-" +dish.getFrequencyId();
	}

	@Override
	public List<Map<String, Object>> selectAllPersonJobDescription() {
		return callYwglMapper.selectAllPersonJobDescription();
	}


}
