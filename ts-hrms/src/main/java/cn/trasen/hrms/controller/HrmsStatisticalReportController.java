package cn.trasen.hrms.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.JobtitleBasicReq;
import cn.trasen.homs.bean.base.JobtitleBasicResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.HrmsJobtitleBasicFeignService;
import cn.trasen.hrms.common.EnumInterfaceData;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.common.KeyValue;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.model.HrmsDictInfo;
import cn.trasen.hrms.model.HrmsStatisticalReportEntity;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsStatisticalReportService;
import cn.trasen.hrms.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Title: HrmsStatisticalReportController.java
 * @Package cn.trasen.hrms.controller
 * @Description: 统计报表 Controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月20日 下午9:51:55
 * @version V1.0
 */
@Slf4j
@Api(tags = "统计报表 Controller")
@RestController
public class HrmsStatisticalReportController {

	@Autowired
	HrmsDictInfoService hrmsDictInfoService;
	@Autowired
	HrmsStatisticalReportService hrmsStatisticalReportService;

	@Autowired
	HrmsJobtitleBasicFeignService hrmsJobtitleBasicService;

	/**
	 * @Title: getGenderReportTableHead
	 * @Description: 获取性别统计报表列表表头
	 * @Return PlatformResult<List < JdGridTableEntity>>
	 * <AUTHOR>
	 * @date 2020年5月20日 下午10:22:17
	 */
	@ApiOperation(value = "获取性别统计报表列表表头", notes = "获取性别统计报表列表表头")
	@PostMapping(value = "/statisticalReport/getGenderReportTableHead")
	public PlatformResult<List<JdGridTableEntity>> getGenderReportTableHead() {
		return PlatformResult.success(hrmsStatisticalReportService.getGenderReportTableHead());
	}

	/**
	 * @Title: getGenderReportTableData
	 * @Description: 获取性别统计报表列表数据(分机构)
	 * @Return PlatformResult<List < Map < String, Object>>>
	 * <AUTHOR>
	 * @date 2020年5月20日 下午10:25:42
	 */
	@ApiOperation(value = "获取性别统计报表列表数据(分机构)", notes = "获取性别统计报表列表数据(分机构)")
	@PostMapping(value = "/statisticalReport/getGenderReportTableData")
	public DataSet<Map<String, Object>> getGenderReportTableData(Page page, HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getGenderReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @Title: getGenderReportTableData
	 * @Description: 获取性别统计报表列表数据
	 * @Return PlatformResult<List < Map < String, Object>>>
	 * <AUTHOR>
	 * @date 2020年5月20日 下午10:25:42
	 */
	@ApiOperation(value = "获取性别统计报表列表数据", notes = "获取性别统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getGenderReportTableDataAll")
	public List<Map<String, Object>> getGenderReportTableDataAll() {
		return hrmsStatisticalReportService.getGenderReportTableDataAll();
	}

	/**
	 * @param page 组织机构ID集合
	 * @Title: getAgeReportTableData
	 * @Description: 获取年龄段统计报表列表数据(分机构)
	 * @Return PlatformResult<List < Map < String, Object>>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:13:24
	 */
	@ApiOperation(value = "获取年龄段统计报表列表数据", notes = "获取年龄段统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getAgeReportTableData")
	public DataSet<Map<String, Object>> getAgeReportTableData(Page page, HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getAgeReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @Title: getAgeReportTableData
	 * @Description: 获取年龄段统计报表列表数据
	 * @Return PlatformResult<List < Map < String, Object>>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:13:24
	 */
	@ApiOperation(value = "获取年龄段统计报表列表数据", notes = "获取年龄段统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getAgeReportTableDataAll")
	public List<Map<String, Object>> getAgeReportTableDataAll() {
		return hrmsStatisticalReportService.getAgeReportTableDataAll();
	}

	/**
	 * @Title: getEducationReportTableHead
	 * @Description: 获取学历统计报表列表表头
	 * @Return PlatformResult<List < JdGridTableEntity>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:46:10
	 */
	@ApiOperation(value = "获取学历统计报表列表表头", notes = "获取学历统计报表列表表头")
	@PostMapping(value = "/statisticalReport/getEducationReportTableHead")
	public PlatformResult<List<JdGridTableEntity>> getEducationReportTableHead() {
		return PlatformResult.success(hrmsStatisticalReportService.getEducationReportTableHead());
	}

	/**
	 * @param entity
	 * @Title: getEducationReportTableData
	 * @Description: 获取学历统计报表列表数据
	 * @Return PlatformResult<List < Map < String, Object>>>
	 * <AUTHOR>
	 * @date 2020年5月21日 下午2:03:39
	 */
	@ApiOperation(value = "获取学历统计报表列表数据", notes = "获取学历统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getEducationReportTableData")
	public DataSet<Map<String, Object>> getEducationReportTableData(Page page, HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getEducationReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @Title: getJobTitleReportTableHead
	 * @Description: 获取职称统计报表列表表头
	 * @Return PlatformResult<List < JdGridTableEntity>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:46:10
	 */
	@ApiOperation(value = "获取职称统计报表列表表头", notes = "获取职称统计报表列表表头")
	@PostMapping(value = "/statisticalReport/getJobTitleReportTableHead")
	public PlatformResult<List<JdGridTableEntity>> getJobTitleReportTableHead() {
		return PlatformResult.success(hrmsStatisticalReportService.getJobTitleReportTableHead());
	}

	/**
	 * @param page
	 * @param entity
	 * @return
	 */
	@ApiOperation(value = "获取职称统计报表列表数据", notes = "获取职称统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getJobTitleReportTableData")
	public DataSet<Map<String, Object>> getJobTitleReportTableData(Page page, HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getJobTitleReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @Title: getJobTitleReportTableHead
	 * @Description: 获取职称级别统计报表列表表头
	 * @Return PlatformResult<List < JdGridTableEntity>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:46:10
	 */
	@ApiOperation(value = "获取职称级别统计报表列表表头", notes = "获取职称级别统计报表列表表头")
	@PostMapping(value = "/statisticalReport/getJobTitleLevelReportTableHead")
	public PlatformResult<List<JdGridTableEntity>> getJobTitleLevelReportTableHead() {
		return PlatformResult.success(hrmsStatisticalReportService.getJobTitleLevelReportTableHead());
	}

	/**
	 * @param page
	 * @param entity
	 * @return
	 */
	@ApiOperation(value = "获取职称统计报表列表数据", notes = "获取职称统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getJobTitleLevelReportTableData")
	public DataSet<Map<String, Object>> getJobTitleLevelReportTableData(Page page, HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getJobTitleLevelReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @Title: getEmployeeCategoryReportTableHead
	 * @Description: 获取员工类别统计报表列表表头
	 * @Return PlatformResult<List < JdGridTableEntity>>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午5:12:45
	 */
	@ApiOperation(value = "获取员工类别统计报表列表表头", notes = "获取员工类别统计报表列表表头")
	@PostMapping(value = "/statisticalReport/getEmployeeCategoryReportTableHead")
	public PlatformResult<List<JdGridTableEntity>> getEmployeeCategoryReportTableHead() {
		return PlatformResult.success(hrmsStatisticalReportService.getEmployeeCategoryReportTableHead());
	}

	/**
	 * @param page
	 * @param entity
	 * @Title: getEmployeeCategoryReportTableData
	 * @Description: 获取员工类别统计报表列表数据
	 * @Return DataSet<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午5:14:42
	 */
	@ApiOperation(value = "获取员工类别统计报表列表数据", notes = "获取员工类别统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getEmployeeCategoryReportTableData")
	public DataSet<Map<String, Object>> getEmployeeCategoryReportTableData(Page page,
																		   HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getEmployeeCategoryReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @Title: getEmployeeIdentityReportTableHead
	 * @Description: 获取个人身份统计报表列表表头
	 * @Return PlatformResult<List < JdGridTableEntity>>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午5:12:45
	 */
	@ApiOperation(value = "获取个人身份统计报表列表表头", notes = "获取个人身份统计报表列表表头")
	@PostMapping(value = "/statisticalReport/getEmployeeIdentityReportTableHead")
	public PlatformResult<List<JdGridTableEntity>> getEmployeeIdentityReportTableHead() {
		return PlatformResult.success(hrmsStatisticalReportService.getEmployeeIdentityReportTableHead());
	}

	/**
	 * @param page
	 * @param entity
	 * @Title: getEmployeeIdentityReportTableData
	 * @Description: 获取个人身份统计报表列表数据
	 * @Return DataSet<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午5:14:42
	 */
	@ApiOperation(value = "获取个人身份统计报表列表数据", notes = "获取个人身份统计报表列表数据")
	@PostMapping(value = "/statisticalReport/getEmployeeIdentityReportTableData")
	public DataSet<Map<String, Object>> getEmployeeIdentityReportTableData(Page page,
																		   HrmsStatisticalReportEntity entity) {
		List<Map<String, Object>> list = hrmsStatisticalReportService.getEmployeeIdentityReportTableData(entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), list);
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportGenderReport
	 * @Description: 导出机构员工性别统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 下午2:21:51
	 */
	@ApiOperation(value = "导出机构员工性别统计报表", notes = "导出机构员工性别统计报表")
	@RequestMapping(value = "/statisticalReport/exportGenderReport", method = {RequestMethod.GET, RequestMethod.POST})
	public void exportGenderReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			List<Map<String, Object>> list = hrmsStatisticalReportService.getGenderReportTableData(entity);

			StringBuffer headerBuffer = new StringBuffer("科室名称");
			StringBuffer fieldsBuffer = new StringBuffer("orgName");
			for (KeyValue kv : EnumInterfaceData.getGenderTypeList()) {
				headerBuffer.append(",").append(kv.getText());
				fieldsBuffer.append(",").append(kv.getCode());
			}
			String fileName = "机构员工性别统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			// 统计
			Map<String, Object> countMap = new HashMap<String, Object>();
			if (list != null && list.size() > 0) {
				list.forEach(item -> { // 遍历大list
					item.forEach((key, value) -> { // 遍历map
						if (!"orgName".equals(key) && !"orgId".equals(key)) {
							if (value != null) {
								if (countMap.get(key) == null) {
									countMap.put(key, Integer.valueOf(value.toString()));
								} else {
									countMap.put(key, Integer.valueOf(value.toString())
											+ Integer.valueOf(countMap.get(key).toString()));
								}

							}
						}
					});

					countMap.put("orgName", "合计");
				});

			}
			list.add(countMap);
			exportExcelUtil.exportMapExcel(list, headerBuffer.toString(), fieldsBuffer.toString(), "机构性别统计报表",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportAgeReport
	 * @Description: 导出机构年龄段统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 下午2:41:43
	 */
	@ApiOperation(value = "导出机构年龄段统计报表", notes = "导出机构年龄段统计报表")
	@RequestMapping(value = "/statisticalReport/exportAgeReport", method = {RequestMethod.GET, RequestMethod.POST})
	public void exportAgeReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			List<Map<String, Object>> list = hrmsStatisticalReportService.getAgeReportTableData(entity);

			String headerStr = "科室名称,20以下,21~30,31~40,41~50,51~60,60以上";
			String fieldStr = "orgName,between0and20,between21and30,between31and40,between41and50,between51and60,gt60";

			String fileName = "机构年龄段统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			// 统计
			Map<String, Object> countMap = new HashMap<String, Object>();
			if (list != null && list.size() > 0) {
				list.forEach(item -> { // 遍历大list
					item.forEach((key, value) -> { // 遍历map
						if (!"orgName".equals(key) && !"orgId".equals(key)) {
							if (value != null) {
								if (countMap.get(key) == null) {
									countMap.put(key, Integer.valueOf(value.toString()));
								} else {
									countMap.put(key, Integer.valueOf(value.toString())
											+ Integer.valueOf(countMap.get(key).toString()));
								}

							}
						}
					});

					countMap.put("orgName", "合计");
				});

			}
			list.add(countMap);

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(list, headerStr, fieldStr, "机构年龄段统计报表", response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportEducationReport
	 * @Description: 导出机构学历统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 下午3:02:52
	 */
	@ApiOperation(value = "导出机构学历统计报表", notes = "导出机构学历统计报表")
	@RequestMapping(value = "/statisticalReport/exportEducationReport", method = {RequestMethod.GET,
			RequestMethod.POST})
	public void exportEducationReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			List<HrmsDictInfo> educationTypeMap = hrmsDictInfoService
					.getDictInfoListByDictType(DictContants.EDUCATION_TYPE);
			List<Map<String, Object>> list = hrmsStatisticalReportService.getEducationReportTableData(entity);

			StringBuffer headerBuffer = new StringBuffer("科室名称");
			StringBuffer fieldsBuffer = new StringBuffer("orgName");
			for (HrmsDictInfo d : educationTypeMap) {
				headerBuffer.append(",").append(d.getDictName());
				fieldsBuffer.append(",").append(d.getDictValue());
			}
			String fileName = "机构学历统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();

			// 统计
			Map<String, Object> countMap = new HashMap<String, Object>();
			if (list != null && list.size() > 0) {
				list.forEach(item -> { // 遍历大list
					item.forEach((key, value) -> { // 遍历map
						if (!"orgName".equals(key) && !"orgId".equals(key)) {
							if (value != null) {
								if (countMap.get(key) == null) {
									countMap.put(key, Integer.valueOf(value.toString()));
								} else {
									countMap.put(key, Integer.valueOf(value.toString())
											+ Integer.valueOf(countMap.get(key).toString()));
								}

							}
						}
					});

					countMap.put("orgName", "合计");
				});

			}
			list.add(countMap);

			exportExcelUtil.exportMapExcel(list, headerBuffer.toString(), fieldsBuffer.toString(), "机构学历统计报表",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportJobTitleReport
	 * @Description: 导出机构职称统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 下午3:02:52
	 */
	@ApiOperation(value = "导出机构职称统计报表", notes = "导出机构职称统计报表")
	@RequestMapping(value = "/statisticalReport/exportJobTitleReport", method = {RequestMethod.GET,
			RequestMethod.POST})
	public void exportJobTitleReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			JobtitleBasicReq hrmsJobtitleBasic = new JobtitleBasicReq();
			hrmsJobtitleBasic.setJobtitleBasicGrade(3);
			List<JobtitleBasicResp> jobtitleBasic = hrmsJobtitleBasicService.getJobtitleBasicList(hrmsJobtitleBasic).getObject();
			List<Map<String, Object>> list = hrmsStatisticalReportService.getJobTitleReportTableData(entity);

			StringBuffer headerBuffer = new StringBuffer("科室名称");
			StringBuffer fieldsBuffer = new StringBuffer("orgName");
			for (JobtitleBasicResp d : jobtitleBasic) {
				headerBuffer.append(",").append(d.getJobtitleBasicName());
				fieldsBuffer.append(",").append(d.getJobtitleBasicId());
			}
			String fileName = "机构职务统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(list, headerBuffer.toString(), fieldsBuffer.toString(), "机构职务统计报表",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportJobTitleReport
	 * @Description: 导出机构职称等级统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 下午3:02:52
	 */
	@ApiOperation(value = "导出机构职称等级统计报表", notes = "导出机构职称等级统计报表")
	@RequestMapping(value = "/statisticalReport/exportJobTitleLevelReport", method = {RequestMethod.GET,
			RequestMethod.POST})
	public void exportJobTitleLevelReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			JobtitleBasicReq hrmsJobtitleBasic = new JobtitleBasicReq();
			hrmsJobtitleBasic.setJobtitleBasicGrade(2);
			List<JobtitleBasicResp> jobtitleBasic = hrmsJobtitleBasicService.getJobtitleBasicList(hrmsJobtitleBasic).getObject();
			List<Map<String, Object>> list = hrmsStatisticalReportService.getJobTitleLevelReportTableData(entity);

			StringBuffer headerBuffer = new StringBuffer("科室名称");
			StringBuffer fieldsBuffer = new StringBuffer("orgName");
			for (JobtitleBasicResp d : jobtitleBasic) {
				headerBuffer.append(",").append("(" + d.getClassificationName() + ")" + d.getJobtitleBasicName());
				fieldsBuffer.append(",").append(d.getJobtitleBasicId());
			}
			String fileName = "机构职务等级统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();

			// 统计
			Map<String, Object> countMap = new HashMap<String, Object>();
			if (list != null && list.size() > 0) {
				list.forEach(item -> { // 遍历大list
					item.forEach((key, value) -> { // 遍历map
						if (!"orgName".equals(key) && !"orgId".equals(key)) {
							if (value != null) {
								if (countMap.get(key) == null) {
									countMap.put(key, Integer.valueOf(value.toString()));
								} else {
									countMap.put(key, Integer.valueOf(value.toString())
											+ Integer.valueOf(countMap.get(key).toString()));
								}

							}
						}
					});

					countMap.put("orgName", "合计");
				});

			}
			list.add(countMap);
			exportExcelUtil.exportMapExcel(list, headerBuffer.toString(), fieldsBuffer.toString(), "机构职务等级统计报表",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportEmployeeCategoryReport
	 * @Description: 导出员工类别统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 上午10:50:07
	 */
	@ApiOperation(value = "导出员工类别统计报表", notes = "导出员工类别统计报表")
	@RequestMapping(value = "/statisticalReport/exportEmployeeCategoryReport", method = {RequestMethod.GET,
			RequestMethod.POST})
	public void exportEmployeeCategoryReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			List<HrmsDictInfo> employeeCategoryMap = hrmsDictInfoService
					.getDictInfoListByDictType(DictContants.EMPLOYEE_CATEGORY);
			List<Map<String, Object>> list = hrmsStatisticalReportService.getEmployeeCategoryReportTableData(entity);

			StringBuffer headerBuffer = new StringBuffer("科室名称");
			StringBuffer fieldsBuffer = new StringBuffer("orgName");
			for (HrmsDictInfo d : employeeCategoryMap) {
				headerBuffer.append(",").append(d.getDictName());
				fieldsBuffer.append(",").append(d.getDictValue());
			}
			String fileName = "员工类别统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();

			// 统计
			Map<String, Object> countMap = new HashMap<String, Object>();
			if (list != null && list.size() > 0) {
				list.forEach(item -> { // 遍历大list
					item.forEach((key, value) -> { // 遍历map
						if (!"orgName".equals(key) && !"orgId".equals(key)) {
							if (value != null) {
								if (countMap.get(key) == null) {
									countMap.put(key, Integer.valueOf(value.toString()));
								} else {
									countMap.put(key, Integer.valueOf(value.toString())
											+ Integer.valueOf(countMap.get(key).toString()));
								}

							}
						}
					});

					countMap.put("orgName", "合计");
				});

			}
			list.add(countMap);

			exportExcelUtil.exportMapExcel(list, headerBuffer.toString(), fieldsBuffer.toString(), "员工类别统计报表",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param entity
	 * @param response
	 * @Title: exportEmployeeCategoryReport
	 * @Description: 导出员工个人身份统计报表
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月1日 上午10:50:07
	 */
	@ApiOperation(value = "导出员工个人身份统计报表", notes = "导出员工个人身份统计报表")
	@RequestMapping(value = "/statisticalReport/exportEmployeeIdentityReport", method = {RequestMethod.GET,
			RequestMethod.POST})
	public void exportEmployeeIdentityReport(HrmsStatisticalReportEntity entity, HttpServletResponse response) {
		try {
			List<HrmsDictInfo> employeeIdentityMap = hrmsDictInfoService
					.getDictInfoListByDictType(DictContants.PERSONAL_IDENTITY);

			List<HrmsDictInfo> establishmentTypeMap = hrmsDictInfoService
					.getDictInfoListByDictType(DictContants.ESTABLISHMENT_TYPE);


			List<Map<String, Object>> list = hrmsStatisticalReportService.getEmployeeIdentityReportTableData(entity);

			StringBuffer headerBuffer = new StringBuffer("科室名称");
			StringBuffer fieldsBuffer = new StringBuffer("orgName");

			for (HrmsDictInfo d : establishmentTypeMap) {
				headerBuffer.append(",").append(d.getDictName());
				fieldsBuffer.append(",").append("BZ" + d.getDictValue());
			}

			for (HrmsDictInfo d : employeeIdentityMap) {
				headerBuffer.append(",").append(d.getDictName());
				fieldsBuffer.append(",").append(d.getDictValue());
			}
			headerBuffer.append(",").append("总人数");
			fieldsBuffer.append(",").append("count");
			String fileName = "员工个人身份统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();

			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			// 统计
			Map<String, Object> countMap = new HashMap<String, Object>();
			if (list != null && list.size() > 0) {
				list.forEach(item -> { // 遍历大list
					item.forEach((key, value) -> { // 遍历map
						if (!"orgName".equals(key) && !"orgId".equals(key)) {
							if (value != null) {
								if (countMap.get(key) == null) {
									countMap.put(key, Integer.valueOf(value.toString()));
								} else {
									countMap.put(key, Integer.valueOf(value.toString())
											+ Integer.valueOf(countMap.get(key).toString()));
								}

							}
						}
					});

					countMap.put("orgName", "合计");
				});

			}
			list.add(countMap);
			exportExcelUtil.exportMapExcel(list, headerBuffer.toString(), fieldsBuffer.toString(), "员工个人身份统计报表",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @param request
	 * @param response
	 * @Title: getEmployeeDistributionReportTable
	 * @Description: 人员分布情况
	 * @return: DataSet<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2021年3月23日 上午9:50:26
	 */
	@ApiOperation(value = "人员分布情况", notes = "人员分布情况")
	@PostMapping(value = "/statisticalReport/getEmployeeDistributionReportTable")
	public Map<String, Object> getEmployeeDistributionReportTable(HttpServletRequest request,
																  HttpServletResponse response) {

		String orgIdList = request.getParameter("orgIdList");

		Map<String, Object> list = new HashMap<String, Object>();
		list.put("pageNo", 1);
		list.put("pageSize", 10000);
		list.put("pageCount", 100);
		list.put("totalCount", 100);

		List<Map<String, Object>> employeeDistributionReportTableDataResultList = hrmsStatisticalReportService
				.getEmployeeDistributionReportTableDataResult(orgIdList);

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		for (Map<String, Object> m : employeeDistributionReportTableDataResultList) {
			Map<String, Object> datamap = new HashMap<String, Object>();
			datamap.put("ksmc", m.get("ksmc"));
			datamap.put("bars", m.get("bars"));
			datamap.put("bazrs", m.get("bazrs"));
			datamap.put("zbrs", m.get("zbrs"));
			datamap.put("zrs", m.get("zrs"));

			Set<String> keys = m.keySet();
			for (String key : keys) {
				if (key.indexOf("gwxx-") >= 0) {
					// System.out.println(key);
					datamap.put(key + "-zb-zgj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb")).get("gwxx-jb-zgj"));
					datamap.put(key + "-zb-zj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb")).get("gwxx-jb-zj"));
					datamap.put(key + "-zb-wz",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb")).get("gwxx-jb-wz"));
					datamap.put(key + "-zb-fgj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb")).get("gwxx-jb-fgj"));
					datamap.put(key + "-zb-cj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb")).get("gwxx-jb-cj"));

					datamap.put(key + "-bw-zgj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-zgj"));
					datamap.put(key + "-bw-zj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-zj"));
					datamap.put(key + "-bw-wz",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-wz"));
					datamap.put(key + "-bw-fgj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-fgj"));
					datamap.put(key + "-bw-cj",
							((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-cj"));

					// (Map<String, Object>)m.get(key);
				}
				// Integer value = map.get(key);
			}
			dataList.add(datamap);

		}

		list.put("rows", dataList);
		return list;
	}

	/**
	 * @param request
	 * @param response
	 * @throws IOException
	 * @Title: getEmployeeDistributionReportTable
	 * @Description: 人员分布情况
	 * @return: DataSet<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2021年3月23日 上午9:50:26
	 */
	@ApiOperation(value = "导出人员分布情况", notes = "导出分布情况")
	@RequestMapping(value = "/statisticalReport/exportEmployeeDistributionReportTable", method = {RequestMethod.GET,
			RequestMethod.POST})

	public void exportEmployeeDistributionReportTable(HttpServletRequest request, HttpServletResponse response)
			throws IOException {

		String orgIdList = request.getParameter("orgIdList");

		List<Map<String, Object>> employeeDistributionReportTableDataResultList = hrmsStatisticalReportService
				.getEmployeeDistributionReportTableDataResult(orgIdList);

		HSSFWorkbook workbook = new HSSFWorkbook();// 这里也可以设置sheet的Name

		try {
			HSSFSheet sheet = workbook.createSheet();

			HSSFRow headrow = sheet.createRow(0);//
			HSSFRow headrow2 = sheet.createRow(1);//
			HSSFRow headrow3 = sheet.createRow(2);//

			headrow.createCell(0).setCellValue("科室名称");
			headrow.createCell(1).setCellValue("总人数");
			headrow.createCell(2).setCellValue("在编");
			headrow.createCell(3).setCellValue("编外");
			headrow.createCell(4).setCellValue("备案制");

			int datarowindex = 3;
			int i = 0;

			Map<String, Integer> coutrowmap = Maps.newLinkedHashMap();

			for (Map<String, Object> m : employeeDistributionReportTableDataResultList) {
				i++;
				HSSFRow row = sheet.createRow(datarowindex);//
				row.createCell(0).setCellValue((String) m.get("ksmc"));
				row.createCell(1).setCellValue((Integer) m.get("zrs"));
				row.createCell(2).setCellValue((Integer) m.get("zbrs"));
				row.createCell(3).setCellValue((Integer) m.get("bars"));
				row.createCell(4).setCellValue((Integer) m.get("bazrs"));

				coutrowmap.put("zrs",
						(coutrowmap.containsKey("zrs") ? coutrowmap.get("zrs") : 0) + (Integer) m.get("zrs"));
				coutrowmap.put("zbrs",
						(coutrowmap.containsKey("zbrs") ? coutrowmap.get("zbrs") : 0) + (Integer) m.get("zbrs"));
				coutrowmap.put("bars",
						(coutrowmap.containsKey("bars") ? coutrowmap.get("bars") : 0) + (Integer) m.get("bars"));
				coutrowmap.put("bazrs",
						(coutrowmap.containsKey("bazrs") ? coutrowmap.get("bazrs") : 0) + (Integer) m.get("bazrs"));
				Set<String> keys = m.keySet();
				int b = 4;
				for (String key : keys) {
					if (key.indexOf("gwxx-") >= 0) {

						// headrow.createCell(b+1).setCellValue((String)(Map) m.get(key)).get("gwmc"));
						headrow.createCell(b + 1).setCellValue((String) ((Map) m.get(key)).get("gwmc"));

						headrow2.createCell(b + 1).setCellValue("在编");
						headrow2.createCell(b + 6).setCellValue("编外");

						// 无证

						headrow3.createCell(b + 1).setCellValue("无证");
						headrow3.createCell(b + 2).setCellValue("初级");
						headrow3.createCell(b + 3).setCellValue("中级");
						headrow3.createCell(b + 4).setCellValue("副高级");
						headrow3.createCell(b + 5).setCellValue("正高级");

						headrow3.createCell(b + 6).setCellValue("无证");
						headrow3.createCell(b + 7).setCellValue("初级");
						headrow3.createCell(b + 8).setCellValue("中级");
						headrow3.createCell(b + 9).setCellValue("副高级");
						headrow3.createCell(b + 10).setCellValue("正高级");

						Integer gwxxJbZgj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb"))
								.get("gwxx-jb-zgj");

						Integer gwxxJbZj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb"))
								.get("gwxx-jb-zj");

						Integer gwxxJbWz = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb"))
								.get("gwxx-jb-wz");

						Integer gwxxJbFgj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb"))
								.get("gwxx-jb-fgj");

						Integer gwxxJbCj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-zb"))
								.get("gwxx-jb-cj");

						Integer gwxxBwZgj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
								.get("gwxx-jb-zgj");

						Integer gwxxBwZj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
								.get("gwxx-jb-zj");

						Integer gwxxBwWz = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
								.get("gwxx-jb-wz");

						Integer gwxxBwFgj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
								.get("gwxx-jb-fgj");

						Integer gwxxBwCj = (Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
								.get("gwxx-jb-cj");

						row.createCell(b + 1).setCellValue(gwxxJbWz);
						row.createCell(b + 2).setCellValue(gwxxJbCj);
						row.createCell(b + 3).setCellValue(gwxxJbZj);
						row.createCell(b + 4).setCellValue(gwxxJbFgj);
						row.createCell(b + 5).setCellValue(gwxxJbZgj);

						row.createCell(b + 6).setCellValue(gwxxBwWz);
						row.createCell(b + 7).setCellValue(gwxxBwCj);
						row.createCell(b + 8).setCellValue(gwxxBwZj);
						row.createCell(b + 9).setCellValue(gwxxBwFgj);
						row.createCell(b + 10).setCellValue(gwxxBwZgj);
//						row.createCell(b + 6)
//								.setCellValue((Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
//										.get("gwxx-jb-zgj"));
//						row.createCell(b + 7).setCellValue(
//								(Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-zj"));
//						row.createCell(b + 8).setCellValue(
//								(Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-wz"));
//						row.createCell(b + 9)
//								.setCellValue((Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw"))
//										.get("gwxx-jb-fgj"));
//						row.createCell(b + 10).setCellValue(
//								(Integer) ((Map) ((Map) ((Map) m.get(key)).get("v")).get("gwxx-bw")).get("gwxx-jb-cj"));

						coutrowmap.put(key + "-zb-wz",
								(coutrowmap.containsKey(key + "-zb-wz") ? coutrowmap.get(key + "-zb-wz") : 0)
										+ gwxxJbWz);
						coutrowmap.put(key + "-zb-cj",
								(coutrowmap.containsKey(key + "-zb-cj") ? coutrowmap.get(key + "-zb-cj") : 0)
										+ gwxxJbCj);
						coutrowmap.put(key + "-zb-zj",
								(coutrowmap.containsKey(key + "-zb-zj") ? coutrowmap.get(key + "-zb-zj") : 0)
										+ gwxxJbZj);
						coutrowmap.put(key + "-zb-fgj",
								(coutrowmap.containsKey(key + "-zb-fgj") ? coutrowmap.get(key + "-zb-fgj") : 0)
										+ gwxxJbFgj);
						coutrowmap.put(key + "-zb-zgj",
								(coutrowmap.containsKey(key + "-zb-zgj") ? coutrowmap.get(key + "-zb-zgj") : 0)
										+ gwxxJbZgj);

						coutrowmap.put(key + "-bw-wz",
								(coutrowmap.containsKey(key + "-bw-wz") ? coutrowmap.get(key + "-bw-wz") : 0)
										+ gwxxBwWz);
						coutrowmap.put(key + "-bw-cj",
								(coutrowmap.containsKey(key + "-bw-cj") ? coutrowmap.get(key + "-bw-cj") : 0)
										+ gwxxBwCj);
						coutrowmap.put(key + "-bw-zj",
								(coutrowmap.containsKey(key + "-bw-zj") ? coutrowmap.get(key + "-bw-zj") : 0)
										+ gwxxBwZj);

						coutrowmap.put(key + "-bw-fgj",
								(coutrowmap.containsKey(key + "-bw-fgj") ? coutrowmap.get(key + "-bw-fgj") : 0)
										+ gwxxBwFgj);
						coutrowmap.put(key + "-bw-zgj",
								(coutrowmap.containsKey(key + "-bw-zgj") ? coutrowmap.get(key + "-bw-zgj") : 0)
										+ gwxxBwZgj);

						if (i == 1) {
							CellRangeAddress region = new CellRangeAddress(0, 0, b + 1, b + 10);
							sheet.addMergedRegion(region);

							region = new CellRangeAddress(1, 1, b + 1, b + 5);
							sheet.addMergedRegion(region);

							region = new CellRangeAddress(1, 1, b + 6, b + 10);
							sheet.addMergedRegion(region);
						}
						b = b + 10;
					}
				}
				datarowindex++;
			}

			HSSFRow coutrow = sheet.createRow(3 + employeeDistributionReportTableDataResultList.size());//

			coutrow.createCell(0).setCellValue("合计");
			i = 1;
			for (String key : coutrowmap.keySet()) {
				coutrow.createCell(i).setCellValue(coutrowmap.get(key));
				i++;
			}

			for (i = 0; i < 5; i++) {
				CellRangeAddress region = new CellRangeAddress(0, 2, i, i);
				sheet.addMergedRegion(region);
			}
//			// 创建单元格，并设置值表头 设置表头居中
			HSSFCellStyle cellStyle = workbook.createCellStyle();

			// setAlignment(HorizontalAlignment align)
			cellStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			HSSFFont font = workbook.createFont();
			font.setFontName("仿宋_GB2312");
			font.setBold(true);// 粗体显示
			font.setFontHeightInPoints((short) 12);
			cellStyle.setFont(font);
			// System.out.println(headrow.getLastCellNum()+"----------------------");
			for (int celli = 0; celli < headrow.getLastCellNum(); celli++) {
				if (headrow.getCell(celli) != null) {
					headrow.getCell(celli).setCellStyle(cellStyle);
				}

			}

			for (int celli = 0; celli < headrow2.getLastCellNum(); celli++) {
				if (headrow2.getCell(celli) != null) {
					headrow2.getCell(celli).setCellStyle(cellStyle);
				}

			}

			for (int celli = 0; celli < headrow3.getLastCellNum(); celli++) {
				if (headrow3.getCell(celli) != null) {
					headrow3.getCell(celli).setCellStyle(cellStyle);
				}

			}
			// headrow2.setRowStyle(cellStyle);
			// headrow3.setRowStyle(cellStyle);

			sheet.setColumnWidth(0, 256 * 35);
			workbook.setSheetName(0, "sheet1");
			String name = "人员分布情况.xls";
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.flush();
		} finally {
			workbook.close();
		}

	}

	/**
	 * 人员分析
	 *
	 * @param request
	 * @param response
	 * @return
	 * @Title: getEmployeeAnalysisReportTable
	 * @Description: TODO
	 * @return: Map<String, Object>
	 * <AUTHOR>
	 * @date 2021年4月1日 上午9:31:01
	 */
	@ApiOperation(value = "人员分析报表", notes = "人员分析报表")
	@RequestMapping(value = "/statisticalReport/getEmployeeAnalysisReportTable", method = {RequestMethod.POST,
			RequestMethod.GET})
	public PlatformResult<List<Map<String, Object>>> getEmployeeAnalysisReportTable(HttpServletRequest request,
																					HttpServletResponse response) {
		String orgIdList = request.getParameter("orgIdList");
		String establishmentType = request.getParameter("establishmentType");
		if (establishmentType == null) {
			establishmentType = "0";
		}
		return PlatformResult.success(hrmsStatisticalReportService.getEmployeeAnalysisReportTable(establishmentType, orgIdList));
	}

	/**
	 * 人员分析
	 *
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 * @Title: getEmployeeAnalysisReportTable
	 * @Description: TODO
	 * @return: Map<String, Object>
	 * <AUTHOR>
	 * @date 2021年4月1日 上午9:31:01
	 */
	@ApiOperation(value = "导出人员分析报表", notes = "导出人员分析报表")
	@RequestMapping(value = "/statisticalReport/exportEmployeeAnalysisReportTable", method = {RequestMethod.POST,
			RequestMethod.GET})
	public void exportEmployeeAnalysisReportTable(HttpServletRequest request,
												  HttpServletResponse response) throws IOException {
		String orgIdList = request.getParameter("orgIdList");
		String establishmentType = request.getParameter("establishmentType");
		if (establishmentType == null) {
			establishmentType = "0";
		}
		Resource resource = new ClassPathResource("template/EmployeeAnalysisTemplate.xlsx");
		XSSFWorkbook wb = new XSSFWorkbook(resource.getInputStream());
		try {
			XSSFSheet sheet = wb.getSheetAt(0);
			List<Map<String, Object>> list = hrmsStatisticalReportService.getEmployeeAnalysisReportTable(establishmentType, orgIdList);
			int rowIndex = 2;
			int cellIndex = 0;
			for (Map<String, Object> map : list) {
				cellIndex = 0;
				for (Map.Entry<String, Object> entry : map.entrySet()) {
					//	System.out.println(sheet.getRow(rowIndex).getLastCellNum()+"-------------");
					if (cellIndex > 4) {

						if (entry.getValue() != null && sheet.getRow(rowIndex).getCell(cellIndex + 1) != null) {
							sheet.getRow(rowIndex).getCell(cellIndex + 1).setCellValue(entry.getValue().toString());
						}
					}
					cellIndex++;
				}
				rowIndex++;
			}
			String name = "人员自动分析.xlsx";
			wb.setSheetName(0, "人员自动分析");
			if (establishmentType.equals("1")) {
				name = "人员自动分析 (在编).xlsx";
				wb.setSheetName(0, "sheet1");
			} else if (establishmentType.equals("3")) {
				name = "人员自动分析 (聘用).xlsx";
				wb.setSheetName(0, "sheet1");
			} else if (establishmentType.equals("7")) {
				name = "人员自动分析 (备案制).xlsx";
				wb.setSheetName(0, "sheet1");
			}
			name = new String(name.getBytes("UTF-8"), "ISO8859-1");
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition", "attachment; filename=" + name);
			ServletOutputStream fos = response.getOutputStream();
			wb.write(fos);
			fos.flush();
		} finally {
			wb.close();
			if (resource.getInputStream() != null) {
				resource.getInputStream().close();
			}
		}
		//	return null;
	}


	/**
	 * 专技人员职称报表
	 *
	 * <AUTHOR>
	 * @date 2021/11/17 17:12
	 */
	@ApiOperation(value = "专技人员职称报表", notes = "专技人员职称报表")
	@RequestMapping(value = "/statisticalReport/getZJRYZCBB", method = {RequestMethod.POST,
			RequestMethod.GET})
	public PlatformResult getZJRYZCBB(HttpServletRequest request,
									  HttpServletResponse response) {
		return PlatformResult.success(hrmsStatisticalReportService.getZJRYZCBB());
	}
}