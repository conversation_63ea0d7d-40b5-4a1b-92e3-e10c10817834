package cn.trasen.hrms.controller;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;

import cn.hutool.core.lang.Assert;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.dingtalk.model.DingAttendancePo;
import cn.trasen.hrms.model.HrmsSchedulingGrouping;
import cn.trasen.hrms.model.HrmsSchedulingGroupingEmp;
import cn.trasen.hrms.model.HrmsSchedulingHolidays;
import cn.trasen.hrms.model.HrmsSchedulingManage;
import cn.trasen.hrms.model.HrmsSchedulingSort;
import cn.trasen.hrms.service.HrmsSchedulingGroupingEmpService;
import cn.trasen.hrms.service.HrmsSchedulingHolidaysService;
import cn.trasen.hrms.service.HrmsSchedulingManageService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ExportDataNew;
import cn.trasen.hrms.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**   
 * @ClassName:  HrmsScheduleController   
 * @Description:人员排班管理
 * @author: WZH
 * @date:   2021年7月19日 下午4:03:23      
 * @Copyright:  
 */
@Slf4j
@Api(tags = "排班管理控制器")
@RestController
public class HrmsScheduleController {
	
	@Autowired
	private HrmsSchedulingManageService hrmsSchedulingManageService;
	
	@Autowired
	private HrmsSchedulingHolidaysService hrmsSchedulingHolidaysService;
	@Autowired
	private HrmsSchedulingGroupingEmpService hrmsSchedulingGroupingEmpService;
	
    @Autowired
    DictItemFeignService dictItemFeignService;

	@ApiOperation(value = "保存排班信息", notes = "保存排班信息")
	@PostMapping(value = "/scheduleinfo/save")
	public PlatformResult<String> insert(@RequestBody List<HrmsSchedulingManage> list) {
		try {
			if(list != null && list.size() > 0) {
				hrmsSchedulingManageService.batchInsert(list,false);
			}else {
				return PlatformResult.failure("请设置排班数据");
			}
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	
	//保存人员排序
	@ApiOperation(value = "保存排班信息", notes = "保存排班信息")
	@PostMapping(value = "/scheduleinfo/savesort")
	public PlatformResult<String> savesort(@RequestBody List<HrmsSchedulingSort> list) {
		try {
			if(list != null && list.size() > 0) {
				hrmsSchedulingManageService.savesort(list);
			}else {
				return PlatformResult.failure("请设置排班数据");
			}
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	//获取人员排序
	@ApiOperation(value = "保存排班信息", notes = "保存排班信息")
	@PostMapping(value = "/scheduleinfo/getsort")
	public  PlatformResult<List<HrmsSchedulingSort>> getsort(@RequestBody String orgId) {
		try {
			List<HrmsSchedulingSort> list = hrmsSchedulingManageService.getsort(orgId);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	


	@ApiOperation(value = "查询排班信息", notes = "查询排班信息")
	@PostMapping(value = "/scheduleinfo/getSchedule")
	public PlatformResult<List<HrmsSchedulingManage>> getDataList(@RequestBody HrmsSchedulingManage entity) {
		try {
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getDataAllList(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "查询上周排班", notes = "查询上周排班")
	@PostMapping(value = "/scheduleinfo/getLastWeekSchedule")
	public PlatformResult<List<HrmsSchedulingManage>> getLastWeekSchedule(@RequestBody HrmsSchedulingManage entity) {
		
		try {
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getLastWeekSchedule(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "查询排班信息", notes = "查询排班信息")
	@PostMapping(value = "/scheduleinfo/getScheduleShow")
	public PlatformResult<List<HrmsSchedulingManage>> getScheduleShow(@RequestBody HrmsSchedulingManage entity) {
		
		try {
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getScheduleShow(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "获取员工某月考勤", notes = "获取员工某月考勤")
	@PostMapping(value = "/schedulingmanage/getEmpSchduing")
	@ResponseBody
	public PlatformResult<List<HrmsSchedulingManage>> getEmpSchduing(@RequestBody HrmsSchedulingManage entity) {
		try {
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getEmpSchduing(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	@ApiOperation(value = "获取员工某月考勤-查看明细", notes = "获取员工某月考勤-查看明细")
	@PostMapping(value = "/schedulingmanage/getempschduingdetails")
	@ResponseBody
	public PlatformResult<List<HrmsSchedulingManage>> getempschduingdetails(@RequestBody HrmsSchedulingManage entity) {
		try {
			entity.setIsDetails("2");
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getEmpSchduing(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	@ApiOperation(value = "获取员工某月考勤", notes = "获取员工某月考勤")
	@PostMapping(value = "/schedulingmanage/getEmpSchduingCount")
	@ResponseBody
	public PlatformResult<Map<String, List<HrmsSchedulingManage>>> getEmpSchduingCount(@RequestBody HrmsSchedulingManage entity) {
		try {
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getEmpSchduing(entity);
			Map<String, List<HrmsSchedulingManage>> map = list.stream().collect(Collectors.groupingBy(t -> t.getFrequencyName()));
			return PlatformResult.success(map);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	
	
	@ApiOperation(value = "排班统计数据", notes = "排班统计数据")
	@PostMapping(value = "/schedulingmanage/getWorkingtablePageList")
	public DataSet<HrmsSchedulingManage> getWorkingtablePageList(Page page, HrmsSchedulingManage entity) {
		List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getWorkingtablePageList(page, entity);
		return new DataSet<HrmsSchedulingManage>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	@ApiOperation(value = "排班数据统计页面", notes = "排班数据统计页面")
	@GetMapping(value = "/schedulingmanage/getWorkingtableJkyyList")
	public  DataSet<DingAttendancePo> getWorkingtableJkyyList(Page page,HrmsSchedulingManage entity, HttpServletResponse response,HttpServletRequest request ) {
		try {
			List<DingAttendancePo> list = hrmsSchedulingManageService.getWorkingtableJkyyList(page, entity,response,request);
			return new DataSet<DingAttendancePo>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}
	
	@ApiOperation(value = "排班数据统计页面", notes = "排班数据统计页面")
	@GetMapping(value = "/schedulingmanage/getWorkingtableJkyyTitle")
	public PlatformResult<DingAttendancePo> getWorkingtableJkyyTitle(HrmsSchedulingManage entity) {
		try {
			return PlatformResult.success(hrmsSchedulingManageService.getWorkingtableJkyyTitle(entity));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}
	
	
	
	
	@ApiOperation(value = "排班数据统计报表导出", notes = "排班数据统计报表导出")
	@RequestMapping(value = "/schedulingmanage/getWorkingtableExport", method = { RequestMethod.GET,
			RequestMethod.POST })
	public void getWorkingtableExport(HrmsSchedulingManage entity, HttpServletResponse response,HttpServletRequest request ) {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			hrmsSchedulingManageService.getWorkingtableExport(page, entity,response,request);
		
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	@ApiOperation(value = "经开排班月导出", notes = "经开排班月导出")
	@GetMapping(value = "/schedulingmanage/getWorkingtableMonthExport")
	public void getWorkingtableMonthExport(HrmsSchedulingManage entity, HttpServletResponse response,HttpServletRequest request ) {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			hrmsSchedulingManageService.getWorkingtableMonthExport(page, entity,response,request);
		
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	
	@ApiOperation(value = "排班加班统计数据", notes = "排班加班统计数据")
	@PostMapping(value = "/schedulingmanage/getOvertimeWorkingtablePageList")
	public DataSet<Map<String, Object>> getOvertimeWorkingtablePageList(Page page, HrmsSchedulingManage entity) {
		List<Map<String, Object>> list = hrmsSchedulingManageService.getOvertimeWorkingtablePageList(page, entity);
		return new DataSet<Map<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	//加班
	
	@ApiOperation(value = "节假日加班动态日期", notes = "节假日加班动态日期")
	@PostMapping(value = "/scheduleinfo/getOvertimeWorkingtableTitleCols")
	public PlatformResult<List<JdGridTableEntity>> getOvertimeWorkingtableTitleCols(@RequestBody HrmsSchedulingManage entity) {
		try {
			Map<String,String> paramMap = new HashMap<String,String>(); 
			paramMap.put("startDate", entity.getStartDate());
			paramMap.put("endDate", entity.getEndDate());
			return PlatformResult.success(hrmsSchedulingHolidaysService.getTableHeadCols(paramMap));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	@ApiOperation(value = "排班数据统计报表导出", notes = "排班数据统计报表导出")
	@RequestMapping(value = "/schedulingmanage/getOvertimeWorkingtableExport", method = { RequestMethod.GET,
			RequestMethod.POST })
	public void getOvertimeWorkingtableExport(HrmsSchedulingManage entity, HttpServletResponse response) {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			hrmsSchedulingManageService.getOvertimeWorkingtableExport(page, entity,response);
		
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	
	@ApiOperation(value = "排班晚夜班统计", notes = "排班晚夜班统计")
	@PostMapping(value = "/schedulingmanage/getnightclasstablepagelist")
	public DataSet<HrmsSchedulingManage> getNightClassTablePageList(Page page, HrmsSchedulingManage entity) {
		List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getNightClassTablePageList(page, entity);
		return new DataSet<HrmsSchedulingManage>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	@ApiOperation(value = "排班晚夜班统计导出", notes = "排班晚夜班统计导出")
	@RequestMapping(value = "/schedulingmanage/getnightclasstableexport", method = { RequestMethod.GET,
			RequestMethod.POST })
	public void getNightClassTableExport(HrmsSchedulingManage entity, HttpServletResponse response) {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			hrmsSchedulingManageService.getnightclasstableexport(page, entity,response);
		
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	@ApiOperation(value = "根据日期和科室查询排班", notes = "根据日期和科室查询排班")
	@PostMapping(value = "/scheduleinfo/getschedulingbydateandorg")
	public PlatformResult<List<HrmsSchedulingManage>> getSchedulingByDateAndOrg(@RequestBody HrmsSchedulingManage entity) {
		try {
			List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getSchedulingByDateAndOrg(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "根据日期和科室查询排班", notes = "根据日期和科室查询排班")
	@PostMapping(value = "/schedulingmanage/getRestStatisticsPageList")
	public DataSet<HrmsSchedulingManage> getRestStatisticsPageList(Page page, HrmsSchedulingManage entity) {
		List<HrmsSchedulingManage> list = hrmsSchedulingManageService.getRestStatisticsPageList(page,entity);;
		return new DataSet<HrmsSchedulingManage>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	
	@ApiOperation(value = "排班数据统计报表导出", notes = "排班数据统计报表导出")
	@RequestMapping(value = "/schedulingmanage/getRestStatisticsExport", method = { RequestMethod.GET,
			RequestMethod.POST })
	public void getRestStatisticsExport(HrmsSchedulingManage entity, HttpServletResponse response,HttpServletRequest request) {
		try {
			Page page = new Page();
			page.setPageSize(Integer.MAX_VALUE);
			hrmsSchedulingManageService.getRestStatisticsExport(page, entity,response,request);
		
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	@ApiOperation(value = "班时数统计", notes = "班时数统计")
	@GetMapping(value = "/scheduleinfo/getScheduleShiftHour")
	public PlatformResult<Map<String,Object>> getScheduleShiftHour(HrmsSchedulingManage entity) {
		
		try {
			Map<String,Object> map = hrmsSchedulingManageService.getScheduleShiftHour(entity);
			return PlatformResult.success(map);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "班时数统计", notes = "班时数统计")
	@GetMapping(value = "/scheduleinfo/getScheduleShiftHourExoprt")
	public void getScheduleShiftHourExoprt(HrmsSchedulingManage entity,HttpServletResponse response) {
			
		try {
			Map<String,Object> map = hrmsSchedulingManageService.getScheduleShiftHour(entity);
			List<Map<String, Object>> data = (List<Map<String, Object>>) map.get("data");
			//添加序号
			if(data!= null && data.size() > 0) {
				for(int i=0;i<data.size();i++) {
					data.get(i).put("no", i+1);
				}
			}
			
			List<String> title = (List<String>) map.get("title");
			StringBuffer headerBuffer = new StringBuffer("序号,科室");
			StringBuffer fieldsBuffer = new StringBuffer("no,orgName");
			title.forEach(item ->{
				headerBuffer.append(",").append(item);
				fieldsBuffer.append(",").append(item);
			});
			String fileName = entity.getSchedulingDate().replace("-", "年")+"月班时数统计报表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();
			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(data, headerBuffer.toString(), fieldsBuffer.toString(), "班时数统计",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	
	
	@ApiOperation(value = "班次数统计", notes = "班次数统计")
	@GetMapping(value = "/scheduleinfo/getScheduleQquantity")
	public PlatformResult<Map<String,Object>> getScheduleQquantity(HrmsSchedulingManage entity) {
		
		try {
			Map<String,Object> map = hrmsSchedulingManageService.getScheduleQquantity(entity);
			return PlatformResult.success(map);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}
	
	@ApiOperation(value = "班次数统计", notes = "班次数统计")
	@GetMapping(value = "/scheduleinfo/getScheduleQquantityExport")
	public void getScheduleQquantityExport(HrmsSchedulingManage entity,HttpServletResponse response) {
		
		try {
			Map<String,Object> map = hrmsSchedulingManageService.getScheduleQquantity(entity);
			List<Map<String, Object>> data = (List<Map<String, Object>>) map.get("data");
			List<String> title = (List<String>) map.get("title");
			
   		 	//定义一个对象数组！
	        String[][] headers=new String[2+title.size()][];
	        headers[0]= new String[]{"序号", "no"};
	        headers[1]= new String[]{"姓名","employeeName"};
            for(int k=0;k<title.size();k++) {
	        	 String[] _tem =new String[3];
	        	 _tem[0]=title.get(k);
	        	 _tem[1]="班次";
	        	 _tem[2]="时长";
	        	headers[k+2] = _tem;
	        }
	        
   		 //数据集合
	        List<Map<String,String>> listMap =new ArrayList<Map<String,String>>();
	        for (int i=0;i<data.size();i++){
	            Map<String,String> _map= new HashMap<String,String>();
	            _map.put("no",String.valueOf((i+1)));
	            _map.put("employeeName",data.get(i).get("employeeName").toString());
	            //二级表头数据赋值
	            for(int y=0;y<title.size();y++) {
	            	String _val = data.get(i).get(title.get(y)).toString();
	            	_map.put(title.get(y), _val);
	            }
	            listMap.add(_map);
	        }
	    
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(entity.getSchedulingDate().replace("-", "年")+"月"+entity.getEmpOrgName()+"班次数据统计分析.xlsx", "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();
	        
	        //指定要合并的列，全部合并
//	        Integer[] mergeLine={0,1};
	        Integer[] mergeLine={};
	        OutputStream out =   response.getOutputStream();
	        ExportDataNew exportDataNew = new ExportDataNew(headers,entity.getSchedulingDate().replace("-", "年")+"月班次数据统计",mergeLine);
	        exportDataNew.exportData(listMap,out);
	        out.flush();
	        out.close();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	@ApiOperation(value = "门急诊派班表", notes = "门急诊派班表")
	@GetMapping(value = "/scheduleinfo/getScheduleSendShift")
	public DataSet<TreeMap<String, Object>> getScheduleSendShift(Page page, HrmsSchedulingManage entity) {
		page.setPageSize(Integer.MAX_VALUE);
		List<TreeMap<String, Object>> result = hrmsSchedulingManageService.getScheduleSendShift(page, entity);
		return new DataSet<TreeMap<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), result);
	}
	
	@ApiOperation(value = "门诊派班表导出", notes = "门诊派班表导出")
	@GetMapping(value = "/scheduleinfo/getScheduleSendShiftExoprt")
	public void getScheduleSendShiftExoprt(Page page,HrmsSchedulingManage entity,HttpServletResponse response) {
		page.setPageSize(Integer.MAX_VALUE);
		try {
			
			List<TreeMap<String, Object>> result = hrmsSchedulingManageService.getScheduleSendShift(page, entity);
			//查询节假日
			HrmsSchedulingHolidays sh = new HrmsSchedulingHolidays();
			sh.setSearchStartDate(entity.getStartDate());
			sh.setSearchEndDate(entity.getEndDate());;
			List<HrmsSchedulingHolidays> getholidaysByDate = hrmsSchedulingHolidaysService.getholidaysByDateJJ(sh);  
			Map<String, HrmsSchedulingHolidays> hshMap = getholidaysByDate.stream().collect(Collectors.toMap(HrmsSchedulingHolidays::getHolidaysDate, bean -> bean));

			
			List<Map<String, Object>>hashmap = new ArrayList<Map<String, Object>>();
			
			if(result != null && result.size() > 0) {
				for(int i=0;i<result.size();i++) {
					 Map<String, Object> _map = new HashMap<>();
					 _map.put("no", i+1);
					 for (String key:result.get(i).keySet()){
						 _map.put(key, result.get(i).get(key));
				        }
					 hashmap.add(_map);
				}
			}
			
			StringBuffer headerBuffer = new StringBuffer("序号,姓名,科室,时段,手机号");
			StringBuffer fieldsBuffer = new StringBuffer("no,employeeName,name,shiftAttributeTime,phone");
			entity.getDays().forEach(item ->{
				HrmsSchedulingHolidays _hshBean = hshMap.get(item);
				if (null != _hshBean && !StringUtil.isEmpty(_hshBean.getHolidaysName())) {
					headerBuffer.append(",")
							.append(item + " " + DateUtils.getWeek(item) + "(" + _hshBean.getHolidaysName() + ")");
				} else {
					headerBuffer.append(",").append(item + " " + DateUtils.getWeek(item) + "");
				}
				fieldsBuffer.append(",").append(item);
			});
			String fileName = "门急诊"+entity.getStartDate() + " - " + entity.getEndDate()+ " 排/派班表表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();
			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(hashmap, headerBuffer.toString(), fieldsBuffer.toString(), "门急诊排派班",
					response.getOutputStream());
			
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	@ApiOperation(value = "临床科室派班表", notes = "临床科室派班表")
	@GetMapping(value = "/scheduleinfo/getScheduleClinical")
	public DataSet<TreeMap<String, Object>> getScheduleClinical(Page page, HrmsSchedulingManage entity) {
		page.setPageSize(Integer.MAX_VALUE);
		List<TreeMap<String, Object>> result = hrmsSchedulingManageService.getScheduleClinical(page, entity);
		return new DataSet<TreeMap<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), result);
	}
	
	@ApiOperation(value = "临床科室派班表导出", notes = "临床科室派班表导出")
	@GetMapping(value = "/scheduleinfo/getScheduleClinicalExoprt")
	public void getScheduleClinicalExoprt(Page page,HrmsSchedulingManage entity,HttpServletResponse response) {
		page.setPageSize(Integer.MAX_VALUE);
		try {
			
			List<TreeMap<String, Object>> result = hrmsSchedulingManageService.getScheduleClinical(page, entity);
			
			//查询节假日
			HrmsSchedulingHolidays sh = new HrmsSchedulingHolidays();
			sh.setSearchStartDate(entity.getStartDate());
			sh.setSearchEndDate(entity.getEndDate());;
			List<HrmsSchedulingHolidays> getholidaysByDate = hrmsSchedulingHolidaysService.getholidaysByDateJJ(sh);  
			
			Map<String, HrmsSchedulingHolidays> hshMap = getholidaysByDate.stream().collect(Collectors.toMap(HrmsSchedulingHolidays::getHolidaysDate, bean -> bean));

			
			List<Map<String, Object>>hashmap = new ArrayList<Map<String, Object>>();
			
			if(result != null && result.size() > 0) {
				for(int i=0;i<result.size();i++) {
					 Map<String, Object> _map = new HashMap<>();
					 _map.put("no", i+1);
					 for (String key:result.get(i).keySet()){
						 _map.put(key, result.get(i).get(key));
				        }
					 hashmap.add(_map);
				}
			}
			
			StringBuffer headerBuffer = new StringBuffer("序号,姓名,科室,手机号");
			StringBuffer fieldsBuffer = new StringBuffer("no,employeeName,name,phone");
			entity.getDays().forEach(item ->{
				HrmsSchedulingHolidays _hshBean = hshMap.get(item);
				if (null != _hshBean && !StringUtil.isEmpty(_hshBean.getHolidaysName())) {
					headerBuffer.append(",").append(item + " " + DateUtils.getWeek(item) + "(" + _hshBean.getHolidaysName() + ")");
				} else {
					headerBuffer.append(",").append(item + " " + DateUtils.getWeek(item) + "");
				}
				fieldsBuffer.append(",").append(item);
			});
			String fileName = "临床科室"+entity.getStartDate() + " - " + entity.getEndDate()+ " 排/派班表表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();
			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(hashmap, headerBuffer.toString(), fieldsBuffer.toString(), "临床科室排派班",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	
	@ApiOperation(value = "周末节假日派班表", notes = "周末节假日派班表")
	@GetMapping(value = "/scheduleinfo/getScheduleLogistics")
	public DataSet<TreeMap<String, Object>> getScheduleLogistics(Page page, HrmsSchedulingManage entity) {
		List<TreeMap<String, Object>> result = hrmsSchedulingManageService.getScheduleLogistics(page, entity);
		return new DataSet<TreeMap<String, Object>>(page.getPageNo(), page.getPageSize(), page.getTotalPages(),
				page.getTotalCount(), result);
	}
	
	@ApiOperation(value = "行政职能部门派班表导出", notes = "行政职能部门派班表导出")
	@GetMapping(value = "/scheduleinfo/getScheduleLogisticsExoprt")
	public void getScheduleLogisticsExoprt(Page page,HrmsSchedulingManage entity,HttpServletResponse response) {
		try {
			List<TreeMap<String, Object>> result = hrmsSchedulingManageService.getScheduleLogistics(page, entity);
			//查询节假日
			HrmsSchedulingHolidays sh = new HrmsSchedulingHolidays();
			sh.setSearchStartDate(entity.getDays().get(0));
			sh.setSearchEndDate(entity.getDays().get(entity.getDays().size()-1));;
			List<HrmsSchedulingHolidays> getholidaysByDate = hrmsSchedulingHolidaysService.getholidaysByDateJJ(sh);  
			Map<String, HrmsSchedulingHolidays> hshMap = getholidaysByDate.stream().collect(Collectors.toMap(HrmsSchedulingHolidays::getHolidaysDate, bean -> bean));
			List<Map<String, Object>>hashmap = new ArrayList<Map<String, Object>>();
			if(result != null && result.size() > 0) {
				for(int i=0;i<result.size();i++) {
					 Map<String, Object> _map = new HashMap<>();
					 _map.put("no", i+1);
					 for (String key:result.get(i).keySet()){
						 _map.put(key, result.get(i).get(key));
				        }
					 hashmap.add(_map);
				}
			}
			StringBuffer headerBuffer = new StringBuffer("序号,姓名,科室,手机号");
			StringBuffer fieldsBuffer = new StringBuffer("no,employeeName,name,phone");
			entity.getDays().forEach(item ->{
				HrmsSchedulingHolidays _hshBean = hshMap.get(item);
				if(null != _hshBean && !StringUtil.isEmpty(_hshBean.getHolidaysName())) {
					headerBuffer.append(",").append( item +" "+ DateUtils.getWeek(item) + "("+_hshBean.getHolidaysName()+")");
				}else {
					headerBuffer.append(",").append( item +" "+ DateUtils.getWeek(item) + "");
				}
				fieldsBuffer.append(",").append(item);
			});
			String fileName = "行政职能部门"+entity.getSchedulingDate().replace("-", "年")+ "月 排/派班表表.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();
			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(hashmap, headerBuffer.toString(), fieldsBuffer.toString(), "行政职能部门排派班",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
	
	@ApiOperation(value = "新排班查看", notes = "新排班查看")
	@GetMapping(value = "/scheduleinfo/getNewScheduleShowExport")
	public void getNewScheduleShow(HrmsSchedulingManage entity,HttpServletResponse response) {
		
		
		try {
			
			//获取岗位字典
			Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity");
			
			List<Map<String, Object>> result = hrmsSchedulingManageService.getNewScheduleShow( entity);
			
			HrmsSchedulingGrouping hsg = new HrmsSchedulingGrouping();
			hsg.setSearchStartDate(entity.getStartDate());
			hsg.setSearchStartDate(entity.getEndDate());
			//hsg.setOrgId(entity.getEmpOrgId());
			List<HrmsSchedulingGroupingEmp> hsgList = hrmsSchedulingGroupingEmpService.getPageAllList(hsg);  //科室所有人员
			
			
			//查询节假日
			HrmsSchedulingHolidays sh = new HrmsSchedulingHolidays();
			sh.setSearchStartDate(entity.getStartDate());
			sh.setSearchEndDate(entity.getEndDate());;
			List<HrmsSchedulingHolidays> getholidaysByDate = hrmsSchedulingHolidaysService.getholidaysByDateJJ(sh);  
			
			Map<String, HrmsSchedulingHolidays> hshMap = getholidaysByDate.stream().collect(Collectors.toMap(HrmsSchedulingHolidays::getHolidaysDate, bean -> bean));
			
			
			if (hsgList != null && hsgList.size() > 0) {
				for (int i = 0; i < hsgList.size(); i++) {
					boolean _contain = false;
					if (result != null && result.size() > 0) {
						for (int k = 0; k < result.size(); k++) {
							String _EmpNo = result.get(k).get("employeeNo").toString();
							if(_EmpNo.equals(hsgList.get(i).getEmployeeNo())) {
								_contain = true;
							}
						}
					}
					
					if(!_contain) { 
						Map<String, Object> _map = new HashMap<>();
						_map.put("employeeNo", hsgList.get(i).getEmployeeNo());
						_map.put("employeeName", hsgList.get(i).getEmployeeName());
						_map.put("phone", hsgList.get(i).getPhone());
						_map.put("personalIdentity", hsgList.get(i).getPersonalIdentity());
						_map.put("name", hsgList.get(i));
						result.add(_map);
					}
				}
			}
			
			
			//添加序号
			if(result!= null && result.size() > 0) {
				for(int i=0;i<result.size();i++) {
					result.get(i).put("no", i+1);
					
					if(null != result.get(i).get("personalIdentity") && !StringUtil.isEmpty(result.get(i).get("personalIdentity").toString())) {
						String _pi = personalIdentityDictMap.get(result.get(i).get("personalIdentity"));
						if(!StringUtil.isEmpty(_pi)) {
							result.get(i).put("personalIdentity", _pi);
						}
					}
				}
			}
			
			StringBuffer headerBuffer = new StringBuffer("序号,姓名,岗位,手机号");
			StringBuffer fieldsBuffer = new StringBuffer("no,employeeName,personalIdentity,phone");
			entity.getDays().forEach(item ->{
				HrmsSchedulingHolidays _hshBean = hshMap.get(item);
				if(null != _hshBean && !StringUtil.isEmpty(_hshBean.getHolidaysName())) {
					headerBuffer.append(",").append( item +" "+ DateUtils.getWeek(item) + "("+_hshBean.getHolidaysName()+")");
				}else {
					headerBuffer.append(",").append( item +" "+ DateUtils.getWeek(item) + "");
				}
				fieldsBuffer.append(",").append(item);
			});
			String fileName =  entity.getEmpOrgName()+"科室排班明细.xls";
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.flushBuffer();
			ExportExcelUtil<Map<String, Object>> exportExcelUtil = new ExportExcelUtil<Map<String, Object>>();
			exportExcelUtil.exportMapExcel(result, headerBuffer.toString(), fieldsBuffer.toString(), entity.getEmpOrgName()+"科排班明细",
					response.getOutputStream());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

    @ApiOperation(value = "查询请休假数据（排班）", notes = "查询请休假数据（排班）")
	@PostMapping(value = "/scheduleinfo/getLeaveData")
	public PlatformResult<List<Map<String,Object>>> getLeaveData(@RequestBody HrmsSchedulingManage entity) {
		
		try {
			List<Map<String,Object>> list = hrmsSchedulingManageService.getleaveData(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
    
    @ApiOperation(value = "查询销假数据（排班）", notes = "查询销假数据（排班）")
	@PostMapping(value = "/scheduleinfo/getCancelLeaveData")
	public PlatformResult<List<Map<String,Object>>> getCancelLeaveData(@RequestBody HrmsSchedulingManage entity) {
		
		try {
			List<Map<String,Object>> list = hrmsSchedulingManageService.getCancelLeaveData(entity);
			return PlatformResult.success(list);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
