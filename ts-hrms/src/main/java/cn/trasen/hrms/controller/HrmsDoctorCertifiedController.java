package cn.trasen.hrms.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsDoctorCertified;
import cn.trasen.hrms.service.HrmsDoctorCertifiedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**   
 * @Title: HrmsDoctorCertifiedController.java 
 * @Package cn.trasen.hrms.controller 
 * @Description: 执业情况 Controller
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月23日 下午5:19:23 
 * @version V1.0   
 */
@Slf4j
@Api(tags = "执业情况 Controller")
@RestController
public class HrmsDoctorCertifiedController {

	@Autowired
	HrmsDoctorCertifiedService hrmsDoctorCertifiedService;


	@ApiOperation(value = "新增执业情况", notes = "新增执业情况")
	@PostMapping(value = "/doctorcertified/save")
	public PlatformResult<String> insert(@RequestBody HrmsDoctorCertified entity) {
		try {
			if (hrmsDoctorCertifiedService.insert(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}


	@ApiOperation(value = "修改执业情况", notes = "修改执业情况")
	@PostMapping(value = "/doctorcertified/update")
	public PlatformResult<String> update(@RequestBody HrmsDoctorCertified entity) {
		try {
			if (hrmsDoctorCertifiedService.update(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}


	@ApiOperation(value = "删除执业情况", notes = "删除执业情况")
	@PostMapping(value = "/doctorcertified/deletedById/{id}")
	public PlatformResult<String> deleteById(@PathVariable String id) {
		try {
			hrmsDoctorCertifiedService.deleted(id);
			return PlatformResult.success();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}


	@ApiOperation(value = "执业情况列表", notes = "执业情况列表")
	@PostMapping(value = "/doctorcertified/list")
	public DataSet<HrmsDoctorCertified> getDataList(Page page,@RequestBody HrmsDoctorCertified entity) {
		List<HrmsDoctorCertified> list = hrmsDoctorCertifiedService.getDataList(page, entity);
		return new DataSet<HrmsDoctorCertified>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}


}
