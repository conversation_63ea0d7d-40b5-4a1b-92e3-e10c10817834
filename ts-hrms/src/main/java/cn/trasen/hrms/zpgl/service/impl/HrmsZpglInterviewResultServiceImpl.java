package cn.trasen.hrms.zpgl.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.trasen.homs.bean.base.*;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.zpgl.dao.HrmsZpglInterviewResultMapper;
import cn.trasen.hrms.zpgl.enums.ZpglEmployeeStatusEnum;
import cn.trasen.hrms.zpgl.model.*;
import cn.trasen.hrms.zpgl.service.*;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * @ClassName HrmsZpglInterviewResultServiceImpl
 * @Description 设置面试评价
 * @date 2023��2��16�� ����6:09:54
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsZpglInterviewResultServiceImpl implements HrmsZpglInterviewResultService {

	@Autowired
	private HrmsZpglInterviewResultMapper mapper;
	
	@Autowired
	private HrmsZpglEmployeeService hrmsZpglEmployeeService;
	
	@Autowired
	private HrmsZpglInterviewMessageService hrmsZpglInterviewMessageService;
	
	@Autowired
	HrmsZpglOperationService hrmsZpglOperationService;
	
	@Autowired
	HrmsZpglAddTalentpoolService hrmsZpglAddTalentpoolService;
	
	@Autowired
	HrmsEmployeeFeignService hrmsEmployeeFeignService;
	
	@Autowired
	HrmsZpglEducationService hrmsZpglEducationService;
	
	@Autowired
	HrmsZpglWorkrecordService hrmsZpglWorkrecordService;
	
	@Autowired
	HrmsZpglFamilyService hrmsZpglFamilyService;
	
	@Autowired
	HrmsZpglProfessionalService hrmsZpglProfessionalService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsZpglInterviewResult record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		//如果是人才库 过来的数据
		//加入到待面试
		if("1".equals(record.getAddResultType())) {
			//根据人员id 如果有 就update 没有就insert
			HrmsZpglInterviewMessage oldBean = hrmsZpglInterviewMessageService.selectByEmpId(record.getZpglempid());
			if(oldBean != null) {
				oldBean.setConform("1");
				oldBean.setInterviewDept(record.getStudyoutDept());
				oldBean.setInterviewDepttext(record.getStudyoutDepttext());
				oldBean.setInterviewJob(record.getStudyoutJob());
				oldBean.setInterviewJobtext(record.getStudyoutJobtext());
				//修改面试状态
				if("2".equals(record.getInterviewResultStatus()) ){
					oldBean.setInterviewStatus("2");
				}else{
					oldBean.setInterviewStatus("1");
				}

					hrmsZpglInterviewMessageService.update(oldBean);
				
			}else {
				HrmsZpglInterviewMessage inMsg = new HrmsZpglInterviewMessage();
				inMsg.setZpglempid(record.getZpglempid());
				inMsg.setConform("1");
				inMsg.setInterviewDept(record.getStudyoutDept());
				inMsg.setInterviewDepttext(record.getStudyoutDepttext());
				inMsg.setInterviewJob(record.getStudyoutJob());
				inMsg.setInterviewJobtext(record.getStudyoutJobtext());
				if("2".equals(record.getInterviewResultStatus()) ){
					inMsg.setInterviewStatus("2");
				}else{
					inMsg.setInterviewStatus("1");
				}
				inMsg.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				//根据  人员id 查询人才库信息
				hrmsZpglInterviewMessageService.save(inMsg);
			}

		}
		/*	if("1".equals(record.getInterviewResultStatus()) || "3".equals(record.getInterviewResultStatus())) {
		
		
		
		}	*/
		
		HrmsZpglEmployee empBean = new HrmsZpglEmployee();
		empBean.setId(record.getZpglempid());
		//设置为待入职
		empBean.setAddTalentPool("2");
		empBean.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_2.getKey());
		hrmsZpglEmployeeService.updateBasic(empBean);
	
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("面试评价");
		operation.setZpglempid(record.getZpglempid());
		JSONObject msg = new JSONObject();
		msg.put("评价建议", record.getRemark());
		operation.setMsg(msg.toJSONString());
		operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsZpglOperationService.save(operation);
		
		//查询是不是有
		
		HrmsZpglInterviewResult oldResBean = selectByEmpId(record.getZpglempid());
		if(oldResBean != null) {
			record.setId(oldResBean.getId());
			record.setUpdateDate(new Date());
			if (user != null) {
				oldResBean.setUpdateUser(user.getUsercode());
				oldResBean.setUpdateUserName(user.getUsername());
				
			}
			return mapper.updateByPrimaryKeySelective(record);
		}else {
			record.setId(IdUtil.getId());
			record.setCreateDate(new Date());
			record.setIsDeleted("N");
			
			if (user != null) {
				record.setCreateUser(user.getUsercode());
				record.setCreateUserName(user.getUsername());
			}
			return mapper.insertSelective(record);
		}
		
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsZpglInterviewResult record) {
		
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("变更");
		operation.setZpglempid(record.getZpglempid());
		JSONObject msg = new JSONObject();
		msg.put("评价建议", record.getRemark());
		operation.setMsg(msg.toJSONString());
		hrmsZpglOperationService.save(operation);
		
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsZpglInterviewResult.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", record.getId());
		return mapper.updateByExample(record, example);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		try {
			HrmsZpglInterviewResult record = new HrmsZpglInterviewResult();
			record.setZpglempid(id);
			record.setUpdateDate(new Date());
			record.setIsDeleted("Y");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			Example example = new Example(HrmsZpglInterviewResult.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("zpglempid", id);
			mapper.updateByExample(record, example);
			hrmsZpglInterviewMessageService.deleteByEmpId(id);
		} catch (Exception e) {
			log.error("面试不通过删除"+e.getMessage());
			throw new BusinessException(e.getMessage());
		}
		
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("面试不通过--删除");
		operation.setZpglempid(id);
		operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsZpglOperationService.save(operation);
		return 1;
	}

	@Override
	public HrmsZpglInterviewResult selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsZpglInterviewResult> getDataSetList(Page page, HrmsZpglInterviewResult record) {
		Example example = new Example(HrmsZpglInterviewResult.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsZpglInterviewResult> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	//入职
	@Transactional(readOnly = false)
	@Override
	public void entryId(String id) {
		
		
		//先判断人员档案是否有这个人存在
		
		//改变人员状态  
		HrmsZpglEmployee record = new HrmsZpglEmployee();
		record.setId(id);
		record.setZpglEmployeeStatus(ZpglEmployeeStatusEnum.ZPGL_EMPLOYEE_STATUS_4.getKey());
		record.setAddTalentPool("2");  //从人才库移除
		hrmsZpglEmployeeService.updateBasic(record);
		
		//查询出人员的基本信息
		HrmsZpglEmployee zpglEmp = hrmsZpglEmployeeService.selectById(id);
		
		//添加判断 经开医院
		if("jkyy".equals("jkyy")) {//添加人员到员工档案列表
			
			//判断有没有身份证
			if(!StringUtil.isEmpty(zpglEmp.getIdentityNumber())) {
				/*try {
					birthday = IDCardUtil.getBirthdayFromPersonIDCode(zpglEmp.getIdentityNumber());
				} catch (Throwable e) {
					log.error("身份证不符合规则，根据身份证获取信息失败");
					throw new BusinessException("根据身份证获取信息失败"+e.getMessage());
				}*/
				HrmsEmployeeSaveReq hrmsEmployeeSaveReq = new HrmsEmployeeSaveReq();
				//插入要保存的数据
				hrmsEmployeeSaveReq.setEmployeeNo(zpglEmp.getIdentityNumber()); //身份证
				hrmsEmployeeSaveReq.setEmployeeStatus("99");  //试用期
				hrmsEmployeeSaveReq.setEmployeeName(zpglEmp.getEmployeeName());
				hrmsEmployeeSaveReq.setPhoneNumber(zpglEmp.getIphone());//手机号
				hrmsEmployeeSaveReq.setIdentityNumber(zpglEmp.getIdentityNumber());
				try {
					hrmsEmployeeSaveReq.setBirthday(IdcardUtil.getBirthDate(zpglEmp.getIdentityNumber()));  //出生日期
				} catch (Throwable e) {
					log.error("身份证不符合规则，根据身份证获取信息失败");
					throw new BusinessException("身份证号码不符合规则："+e.getMessage());
				}
				hrmsEmployeeSaveReq.setEmpAge(zpglEmp.getAge());//年龄
				if("1".equals(zpglEmp.getGender())){
					hrmsEmployeeSaveReq.setGender("0");//性别
				}
				if("2".equals(zpglEmp.getGender())){
					hrmsEmployeeSaveReq.setGender("1");//性别
				}
				
				hrmsEmployeeSaveReq.setBirthplace(zpglEmp.getBirthplace());//籍贯
				hrmsEmployeeSaveReq.setNationality(zpglEmp.getNationality());//名族 
				hrmsEmployeeSaveReq.setPoliticalStatus(zpglEmp.getPoliticalStatus());//政治面貌
				if(StringUtils.isNotBlank(zpglEmp.getAvatar())){
					hrmsEmployeeSaveReq.setAvatar("/ts-basics-bottom/fileAttachment/downloadFile/" + zpglEmp.getAvatar());
				}
				hrmsEmployeeSaveReq.setAddress(zpglEmp.getAddresstext());//住址
				hrmsEmployeeSaveReq.setResidenceAddress(zpglEmp.getHukousuozaiditext());//户口所在地
				hrmsEmployeeSaveReq.setEmail(zpglEmp.getEmail());//邮箱
				hrmsEmployeeSaveReq.setMarriageStatus(zpglEmp.getMarriageStatus());//婚姻状态
				if (StringUtils.isNotEmpty(zpglEmp.getEntryDate())){
					hrmsEmployeeSaveReq.setWorkStartDate(zpglEmp.getEntryDate());//参加工作时间
				}
				hrmsEmployeeSaveReq.setEmergencyContact(zpglEmp.getJinjilianxiren());//紧急联系人
				hrmsEmployeeSaveReq.setEmergencyTel(zpglEmp.getRenxirendianhua());//紧急联系人电话
				
				Date retirementDate = DateUtils.getRetirementDate(zpglEmp.getIdentityNumber());//退休日期
				hrmsEmployeeSaveReq.setRetireDate(DateUtil.format(retirementDate, "yyyy-MM-dd"));
				
				Map<String,Object> dataMap = new HashMap<>();
				String hrms_child = "";//生育
				if("0".equals(zpglEmp.getOffspring())){
					hrms_child = "未育";
				}else{
					hrms_child = zpglEmp.getOffspring() + "个";
				}
				dataMap.put("hrms_child", hrms_child);
				dataMap.put("account_nature2", zpglEmp.getHujileixing());//户籍地类别
				
				
				//学历信息
				List<HrmsZpglEducation> hrmsZpglEducationList = hrmsZpglEducationService.findByEmpId(id);
				if(CollectionUtils.isNotEmpty(hrmsZpglEducationList)){
					List<HrmsEducation> educationList = new ArrayList<>();
					int i = 0;
					for (HrmsZpglEducation hrmsZpglEducation : hrmsZpglEducationList) {
						HrmsEducation hrmsEducation = new HrmsEducation();
						
						hrmsEducation.setId(hrmsZpglEducation.getId());
						if(StringUtils.isNotBlank(hrmsZpglEducation.getQizhishijian())){
							String[] split = hrmsZpglEducation.getQizhishijian().split("~");
							hrmsEducation.setStartTime(DateUtil.parse(split[0], "yyyy-MM-dd"));
							hrmsEducation.setEndTime(DateUtil.parse(split[1], "yyyy-MM-dd"));
						}
						
						if(i == 0){
							hrmsEducation.setHighestLevel("1");
							dataMap.put("dy_xueli", hrmsZpglEducation.getXueli());//第一学历
						}else{
							hrmsEducation.setHighestLevel("2");
						}
						
						hrmsEducation.setSchoolName(hrmsZpglEducation.getBiyeyuanxiao());
						hrmsEducation.setProfessional(hrmsZpglEducation.getZhuanye());
						hrmsEducation.setEducationType(hrmsZpglEducation.getXueli());
						if("是".equals(hrmsZpglEducation.getFullTime())){
							hrmsEducation.setLearnWay("1");
						}else{
							hrmsEducation.setLearnWay("2");
						}
						if(StringUtils.isNotBlank(hrmsZpglEducation.getFujian())){
							/*String fujian = hrmsZpglEducation.getFujian();
							com.alibaba.fastjson.JSONObject parseObject = JSON.parseObject(fujian);*/
							hrmsEducation.setXlfj(hrmsZpglEducation.getFujian());
						}
						
						educationList.add(hrmsEducation);
						i++;
					}
					hrmsEmployeeSaveReq.setHrmsEducationList(educationList);
				}
				
				
				//院外工作经历
				List<HrmsZpglWorkrecord> hrmsZpglWorkrecordList = hrmsZpglWorkrecordService.findByEmpId(id);
				if(CollectionUtils.isNotEmpty(hrmsZpglWorkrecordList)){
					List<HrmsWorkRecord> hrmsWorkRecordList = new ArrayList<>();
					int i = 0;
					for (HrmsZpglWorkrecord hrmsZpglWorkrecord : hrmsZpglWorkrecordList) {
						HrmsWorkRecord hrmsWorkRecord = new HrmsWorkRecord();
						
						if(i == 0){
							dataMap.put("jkhrms_work1", hrmsZpglWorkrecord.getDanweimingcheng());
						}
						if(i == 1){
							dataMap.put("jkhrms_work2", hrmsZpglWorkrecord.getDanweimingcheng());
						}
						if(i == 2){
							dataMap.put("jkhrms_work3", hrmsZpglWorkrecord.getDanweimingcheng());
						}
						
						if(StringUtils.isNotBlank(hrmsZpglWorkrecord.getQizhishijian())){
							String[] split = hrmsZpglWorkrecord.getQizhishijian().split("~");
							hrmsWorkRecord.setStartTime(DateUtil.parse(split[0], "yyyy-MM-dd"));
							hrmsWorkRecord.setEndTime(DateUtil.parse(split[1], "yyyy-MM-dd"));
						}
						
						hrmsWorkRecord.setWorkUnit(hrmsZpglWorkrecord.getDanweimingcheng());
						hrmsWorkRecord.setDeptName(hrmsZpglWorkrecord.getKeshi());
						hrmsWorkRecord.setPost(hrmsZpglWorkrecord.getZhiwu());
						hrmsWorkRecord.setWitness(hrmsZpglWorkrecord.getZhengmingren());
						hrmsWorkRecordList.add(hrmsWorkRecord);
						
						i++;
					}
					hrmsEmployeeSaveReq.setHrmsWorkRecordList(hrmsWorkRecordList);
					
				}
				
				//家庭信息
				List<HrmsZpglFamily> hrmsZpglFamilyList = hrmsZpglFamilyService.findByEmpId(id);
				if(CollectionUtils.isNotEmpty(hrmsZpglFamilyList)){
					List<HrmsFamilyInfo> hrmsFamilyInfoList = new ArrayList<>();
					for (HrmsZpglFamily hrmsZpglFamily : hrmsZpglFamilyList) {
						HrmsFamilyInfo hrmsFamilyInfo = new HrmsFamilyInfo();
						
						hrmsFamilyInfo.setMemberName(hrmsZpglFamily.getXingming());
						hrmsFamilyInfo.setContactNumber(hrmsZpglFamily.getPhone());
						hrmsFamilyInfo.setRelationship(hrmsZpglFamily.getGuanxi());
						hrmsFamilyInfo.setWorkUnit(hrmsZpglFamily.getGongzuodanwei());
						
						hrmsFamilyInfoList.add(hrmsFamilyInfo);
					}
					
					hrmsEmployeeSaveReq.setFamilyInfoList(hrmsFamilyInfoList);
				}
				
				//职称信息
				List<HrmsZpglProfessional> hrmsZpglProfessionalList = hrmsZpglProfessionalService.findByEmpId(id);
				if(CollectionUtils.isNotEmpty(hrmsZpglProfessionalList)){
					HrmsZpglProfessional hrmsZpglProfessional = hrmsZpglProfessionalList.get(0);
					String zhicheng = hrmsZpglProfessionalService.selectZhicheng(hrmsZpglProfessional.getZhichengmingcheng());
					dataMap.put("zhicheng", zhicheng);
					dataMap.put("zhichengzhuangye", hrmsZpglProfessional.getZhuenye());
					dataMap.put("zcqudeshijian", hrmsZpglProfessional.getQudeshijian());
					dataMap.put("zcqudedidian", hrmsZpglProfessional.getQudedidian());
				}
				
				hrmsEmployeeSaveReq.setDataMap(dataMap);
				PlatformResult<EmployeeResp> addEmployee = hrmsEmployeeFeignService.addEmployee(hrmsEmployeeSaveReq, UserInfoHolder.getToken());
				log.error(addEmployee.toString());
			}
		}
		
		HrmsZpglOperation operation = new HrmsZpglOperation();
		operation.setTitle("入职存档");
		operation.setZpglempid(id);
		operation.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		hrmsZpglOperationService.save(operation);
	}
	
	//根据人员id 查询面试评价
	@Override
	public HrmsZpglInterviewResult selectByEmpId(String zpglempid) {
		Example example = new Example(HrmsZpglInterviewResult.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", zpglempid);
		example.setOrderByClause(" create_date desc");
		List<HrmsZpglInterviewResult> selectByExample = mapper.selectByExample(example);
		if(selectByExample != null && selectByExample.size() >0) {
			return selectByExample.get(0);
		}
		return null;
	}

	@Override
	public Integer deleteAllByEmpId(String empId) {

		HrmsZpglInterviewResult record = new HrmsZpglInterviewResult();
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		Example example = new Example(HrmsZpglInterviewResult.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("zpglempid", empId);
		return mapper.updateByExampleSelective(record, example);
	}
}
