package cn.trasen.hrms.attendance.annualLeave.service.impl;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.hrms.attendance.annualLeave.dao.HrmsAnnualLeaveWageRulesMapper;
import cn.trasen.hrms.attendance.annualLeave.model.*;
import cn.trasen.hrms.attendance.annualLeave.service.HrmsAnnualLeaveEmpHistoryService;
import cn.trasen.hrms.attendance.annualLeave.service.HrmsAnnualLeaveSettingService;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.salary.enums.NewsalaryTemporaryAdjustOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryTemporaryAdjust;
import cn.trasen.hrms.salary.service.HrmsNewsalaryTemporaryAdjustService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.attendance.annualLeave.dao.HrmsAnnualLeaveEmpMapper;
import cn.trasen.hrms.attendance.annualLeave.service.HrmsAnnualLeaveEmpService;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;

/**
 * @ClassName HrmsAnnualLeaveEmpServiceImpl
 * @Description TODO
 * @date 2025��1��22�� ����4:10:15
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsAnnualLeaveEmpServiceImpl implements HrmsAnnualLeaveEmpService {

	@Autowired
	private HrmsAnnualLeaveSettingService hrmsAnnualLeaveSettingService;

	@Resource
	private HrmsAnnualLeaveEmpMapper mapper;

	@Autowired
	private HrmsEmployeeService hrmsEmployeeService;

	@Autowired
	private HrmsAnnualLeaveEmpHistoryService annualLeaveEmpHistoryService;

	@Resource
	private HrmsAnnualLeaveWageRulesMapper annualLeaveWageRulesMapper;

	@Autowired
	private HrmsNewsalaryTemporaryAdjustService newsalaryTemporaryAdjustService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsAnnualLeaveEmp record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

		//保存历史数据
		HrmsAnnualLeaveEmpHistory history = new HrmsAnnualLeaveEmpHistory();
		BeanUtils.copyProperties(record,history);
		annualLeaveEmpHistoryService.save(history);

		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsAnnualLeaveEmp record,ThpsUser user) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getCorpcode());
		}
		//保存历史数据
		HrmsAnnualLeaveEmpHistory history = new HrmsAnnualLeaveEmpHistory();
		BeanUtils.copyProperties(record,history);
		annualLeaveEmpHistoryService.save(history);

		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsAnnualLeaveEmp record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//保存历史数据
		HrmsAnnualLeaveEmpHistory history = new HrmsAnnualLeaveEmpHistory();
		BeanUtils.copyProperties(record,history);
		annualLeaveEmpHistoryService.save(history);

		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsAnnualLeaveEmp record,ThpsUser user) {
		record.setUpdateDate(new Date());
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}

		//保存历史数据
		HrmsAnnualLeaveEmpHistory history = new HrmsAnnualLeaveEmpHistory();
		BeanUtils.copyProperties(record,history);
		annualLeaveEmpHistoryService.save(history);

		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer batchUpdate(List<HrmsAnnualLeaveEmp> list) {
		Integer count = 0;
		if(CollUtil.isNotEmpty(list)){
			for(HrmsAnnualLeaveEmp vo: list){
				count += update(vo);
			}
		}
		return count;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsAnnualLeaveEmp record = new HrmsAnnualLeaveEmp();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsAnnualLeaveEmp selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectById(id);
	}

	@Override
	public DataSet<HrmsAnnualLeaveEmp> getDataSetList(Page page, HrmsAnnualLeaveEmp record) {
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsAnnualLeaveEmp> records = mapper.getList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<HrmsAnnualLeaveEmp> getList(HrmsAnnualLeaveEmp record) {
		List<HrmsAnnualLeaveEmp> records = mapper.getList(record);
		return records;
	}

	/**
	 * 生成员工年度年假数据
	 */
	@Transactional(readOnly = false)
	@Override
	public void createEmployeeAnnualLeaveByYear(String year,ThpsUser user,String employeeId){
		HrmsAnnualLeaveSetting annualLeaveSetting = hrmsAnnualLeaveSettingService.getDataByYear(year);
		if(annualLeaveSetting == null){
			String lastYear = (Integer.valueOf(year)-1) + "";
			annualLeaveSetting = hrmsAnnualLeaveSettingService.getDataByYear(lastYear);
			if(annualLeaveSetting == null){
				throw  new BusinessException("请先配置年假设置规则");
			}
			if("1".equals(annualLeaveSetting.getIsPutoffNextYear())) {
				annualLeaveSetting.setYear(year);
				annualLeaveSetting.setId(null);
				hrmsAnnualLeaveSettingService.save(annualLeaveSetting);
			}else{
				throw  new BusinessException("请先配置年假设置规则");
			}
		}
		if(CollUtil.isEmpty(annualLeaveSetting.getAnnualLeaveDaysRuleList())){
			throw  new BusinessException("请先配置年假设置规则");
		}
		Date yearStartDate = DateUtil.beginOfYear(DateUtil.parse(year+"-01-01"));
		Date yearEndDate = DateUtil.endOfYear(DateUtil.parse(year+"-01-01"));
		String ssoOrgCode = user.getCorpcode();
		List<Map<String,Object>> employeeParamDatas =mapper.getEmployeeAnnualLeaveParamsData(DateUtil.formatDate(yearStartDate),DateUtil.formatDate(yearEndDate),ssoOrgCode,employeeId);
		if(CollUtil.isNotEmpty(employeeParamDatas)){
			List<Map<String,Object>> employeeData = new ArrayList<>();
			List<String> employeeIds = new ArrayList<>();
			for(HrmsAnnualLeaveDaysRule daysRule : annualLeaveSetting.getAnnualLeaveDaysRuleList()){
			    //开始时间为空则默认当前规则不生效
			    if(null == daysRule.getStartYearLimit() || 1 > daysRule.getStartYearLimit()){
			        continue;
                }
                //如果年假规则的结束时间为空则设置为Integer最大值
                if(null == daysRule.getEndYearLimit()){
			        daysRule.setEndYearLimit(Integer.MAX_VALUE);
                }
				if(null != daysRule.getAnnualLeaveDays() &&  daysRule.getAnnualLeaveDays()>0){
					//获取年假规则内容员工数据
                    // 年初工龄时间>年假规则开始时间，年初工龄时间 < 年假规则开始时间， 并且年末工龄时间 < 年假规则结束时间
					employeeData = employeeParamDatas.stream().filter(vo-> null != vo.get("glyearstartnum")
                            &&  null != vo.get("glyearendnum")
                            && Convert.toInt(vo.get("glyearstartnum")) >= daysRule.getStartYearLimit()
							&& (Convert.toInt(vo.get("glyearstartnum")) < daysRule.getEndYearLimit()|| daysRule.getEndYearLimit() == null)
							&& (Convert.toInt(vo.get("glyearendnum")) < daysRule.getEndYearLimit() || daysRule.getEndYearLimit() == null)).collect(Collectors.toList());
					if(CollUtil.isNotEmpty(employeeData)) {
						for (Map<String, Object> employeeParam : employeeData) {
                            //病假或事假超过规则定义的累计假期，则本年年假设置为0
						    Integer annualLeaveDays = daysRule.getAnnualLeaveDays();
						    if((null != employeeParam.get("sjdays") && null != daysRule.getTotalPersonalLeaveDays() &&  Convert.toInt(employeeParam.get("sjdays")) > daysRule.getTotalPersonalLeaveDays())
                                    || (null != employeeParam.get("bjdays") && null != daysRule.getTotalSickLeaveDays()  && Convert.toInt(employeeParam.get("bjdays")) > daysRule.getTotalSickLeaveDays())){
                                annualLeaveDays = 0;
                            }
							employeeIds.add( Convert.toStr(employeeParam.get("employee_id")));
							if (StringUtil.isNotEmpty(Convert.toStr(employeeParam.get("annual_leave_emp_id")))) {
								HrmsAnnualLeaveEmp entity = new HrmsAnnualLeaveEmp(year, Convert.toStr(employeeParam.get("employee_id")),annualLeaveDays );
								entity.setIsUpdate("0");
								entity.setId(Convert.toStr(employeeParam.get("annual_leave_emp_id")));
								update(entity,user);
							} else {
								HrmsAnnualLeaveEmp entity =new HrmsAnnualLeaveEmp(year, Convert.toStr(employeeParam.get("employee_id")), annualLeaveDays);
								save(entity,user);
							}
						}
					}
					//获取年假规则内容员工数据 年初工龄时间 < 年假规则开始时间，年末工龄时间 >= 年假规则开始时间  并且年末工龄时间 > 年假规则结束时间
					employeeData = employeeParamDatas.stream().filter(vo-> null != vo.get("glyearstartnum")
							&&  null != vo.get("glyearendnum")
							&& Convert.toInt(vo.get("glyearstartnum")) < daysRule.getStartYearLimit()
							&& Convert.toInt(vo.get("glyearendnum")) >= daysRule.getStartYearLimit()
							&& (Convert.toInt(vo.get("glyearendnum")) < daysRule.getEndYearLimit()|| daysRule.getEndYearLimit() == null)).collect(Collectors.toList());
					for (Map<String, Object> employeeParam : employeeData) {
						if(StringUtils.isNotEmpty(Convert.toStr(employeeParam.get("curyeardate")))) {
							employeeIds.add(Convert.toStr(employeeParam.get("employee_id")));
							//计算年假天数
							Date curyeardate = DateUtil.parse(Convert.toStr(employeeParam.get("curyeardate")));
							Long num = DateUtil.betweenDay(curyeardate, yearEndDate, false);
							BigDecimal days = (new BigDecimal(num)).divide(new BigDecimal(365), new MathContext(2, RoundingMode.HALF_UP)).multiply(new BigDecimal(5)).setScale(2, RoundingMode.HALF_UP);
							if (days.compareTo(new BigDecimal(1)) < 0) {
								days = BigDecimal.ZERO;
							}
							days = days.add(new BigDecimal(daysRule.getAnnualLeaveDays())).subtract(new BigDecimal(5));
							Integer annualLeaveDays = days.setScale(0, RoundingMode.DOWN).intValue();
							//病假或事假超过规则定义的累计假期，则本年年假设置为0
							if((null != employeeParam.get("sjdays") && null != daysRule.getTotalPersonalLeaveDays() &&  Convert.toInt(employeeParam.get("sjdays")) > daysRule.getTotalPersonalLeaveDays())
									|| (null != employeeParam.get("bjdays") && null != daysRule.getTotalSickLeaveDays()  && Convert.toInt(employeeParam.get("bjdays")) > daysRule.getTotalSickLeaveDays())){
								annualLeaveDays = 0;
							}
							if (StringUtil.isNotEmpty(Convert.toStr(employeeParam.get("annual_leave_emp_id")))) {
								HrmsAnnualLeaveEmp entity = new HrmsAnnualLeaveEmp(year, Convert.toStr(employeeParam.get("employee_id")),annualLeaveDays);
								entity.setId(Convert.toStr(employeeParam.get("annual_leave_emp_id")));
								entity.setIsUpdate("0");
								update(entity,user);
							} else {
								HrmsAnnualLeaveEmp entity = new HrmsAnnualLeaveEmp(year, Convert.toStr(employeeParam.get("employee_id")), annualLeaveDays);
								save(entity,user);
							}
						}
					}
				}
			}
			//不再规则内的员工将可用年假设置为0
			employeeData = employeeParamDatas.stream().filter(vo-> !employeeIds.contains(Convert.toStr(vo.get("employee_id")))).collect(Collectors.toList());
			for (Map<String, Object> employeeParam : employeeData) {
				if (StringUtil.isNotEmpty(Convert.toStr(employeeParam.get("annual_leave_emp_id")))) {
					HrmsAnnualLeaveEmp entity = new HrmsAnnualLeaveEmp(year, Convert.toStr(employeeParam.get("employee_id")), 0);
					entity.setId(Convert.toStr(employeeParam.get("annual_leave_emp_id")));
					entity.setIsUpdate("0");
					update(entity,user);
				} else {
					HrmsAnnualLeaveEmp entity = new HrmsAnnualLeaveEmp(year, Convert.toStr(employeeParam.get("employee_id")), 0);
					save(entity,user);
				}
			}
		}
	}

	/**
	 *
	 * @Title importData
	 * @Description 导入
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public Map<String,String> importData(List<HrmsAnnualLeaveEmp> list){
		log.info("导入的数据:" + list);
		if(CollectionUtil.isEmpty(list)){
			throw new BusinessException("请至少添加一条数据");
		}
		int sucessNum = 0;
		StringBuffer errorMess = new StringBuffer();
		for (HrmsAnnualLeaveEmp annualLeaveEmp : list) {
			if(annualLeaveEmp.getAvailableAnnualLeaveDays() == null){
				annualLeaveEmp.setAvailableAnnualLeaveDays(0);
			}
			if(annualLeaveEmp.getRemainingAnnualLeaveDays() == null){
				annualLeaveEmp.setRemainingAnnualLeaveDays(0);
			}
			if(annualLeaveEmp.getUseAnnualLeaveDays() == null){
				annualLeaveEmp.setUseAnnualLeaveDays(0);
			}
			try {
				sucessNum += importDataSave(annualLeaveEmp);
			}catch (Exception e){
				errorMess.append(e.getMessage()+",");
				e.printStackTrace();
			}
		}
		Map<String,String> map = new HashMap<>();
		map.put("successNum",sucessNum+"");
		if(list.size() - sucessNum>0 && StringUtils.isNotBlank(errorMess.toString())) {
			map.put("errorNum", list.size() - sucessNum + "条，错误信息：" + errorMess.substring(0,errorMess.length()-1));
		}else{
			map.put("errorNum",list.size() - sucessNum + " 条");
		}

		return map;
	}

	/**
	 * 保存导入数据
	 * @return
	 */
	private Integer importDataSave(HrmsAnnualLeaveEmp annualLeaveEmp){
		Assert.notNull(annualLeaveEmp);
		HrmsAnnualLeaveEmp parmas = new HrmsAnnualLeaveEmp();
		parmas.setEmployeeNo(annualLeaveEmp.getEmployeeNo());
		parmas.setYear(annualLeaveEmp.getYear());
		parmas.setIsDeleted("N");
		List<HrmsAnnualLeaveEmp> list = mapper.getList(parmas);
		if(CollUtil.isNotEmpty(list)){
			HrmsAnnualLeaveEmp leaveEmp = list.get(0);
			leaveEmp.setAvailableAnnualLeaveDays(annualLeaveEmp.getAvailableAnnualLeaveDays());
			leaveEmp.setRemainingAnnualLeaveDays(annualLeaveEmp.getRemainingAnnualLeaveDays());
			leaveEmp.setUseAnnualLeaveDays(annualLeaveEmp.getUseAnnualLeaveDays());
			leaveEmp.setIsUpdate("1");
			return update(leaveEmp);
		}else {
            //根据员工工号查询员工信息
			HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(annualLeaveEmp.getEmployeeNo().trim());
			if(hrmsEmployee == null){
				throw new BusinessException(annualLeaveEmp.getEmployeeNo()+":未找到员工信息");
			}
			HrmsAnnualLeaveEmp leaveEmp = new HrmsAnnualLeaveEmp();
			leaveEmp.setYear(annualLeaveEmp.getYear());
			leaveEmp.setEmployeeId(hrmsEmployee.getEmployeeId());
			leaveEmp.setAvailableAnnualLeaveDays(annualLeaveEmp.getAvailableAnnualLeaveDays());
			leaveEmp.setRemainingAnnualLeaveDays(annualLeaveEmp.getRemainingAnnualLeaveDays());
			leaveEmp.setUseAnnualLeaveDays(annualLeaveEmp.getUseAnnualLeaveDays());
			leaveEmp.setIsUpdate("1");
			return save(leaveEmp);
		}
	}

	/**
	 * 失效年假折算成薪资
	 * @param optionCycle
	 * @param leaveIds
	 */
	@Transactional(readOnly = false)
	@Override
	public String batchCreateEmpSalaryAdjust(String optionCycle,List<String> leaveIds){
		StringBuffer errorMessage = new StringBuffer();
		for(String id : leaveIds) {
			HrmsAnnualLeaveEmp emp = selectById(id);
			try {
				//判断当前员工年假是否存在失效年假且未折现，不存在则直接跳过
				if (emp != null && emp.getLapsedAnnualLeaveDays() != null && emp.getLapsedAnnualLeaveDays() > 0 && "0".equals(emp.getIsConvertWage())) {
					//根据员工年假所在年份获取年假设置参数
					HrmsAnnualLeaveSetting setting = hrmsAnnualLeaveSettingService.getDataByYear(emp.getYear());
					if (setting != null && CollUtil.isNotEmpty(setting.getAnnualLeaveWageRulesList())) {
						//生成薪酬调薪项数据
						HrmsNewsalaryTemporaryAdjust temporaryAdjust = new HrmsNewsalaryTemporaryAdjust();
						temporaryAdjust.setEmployeeNo(emp.getEmployeeNo());
						temporaryAdjust.setEmployeeId(emp.getEmployeeId());
						temporaryAdjust.setTmpItem("njzx");
						temporaryAdjust.setOptionCycle(optionCycle);
						//职称设置了失效后折现金额，则按配置的金额*失效天数
						BigDecimal totalAmount = BigDecimal.ZERO;
						//根据员工获取员工职称
						List<Map<String, Object>> empHeighesJobtitleList = annualLeaveWageRulesMapper.getEmpHeighesJobtitleWageAmount(UserInfoHolder.getCurrentUserCorpCode(), emp.getEmployeeId(), emp.getYear());
						if (CollUtil.isNotEmpty(empHeighesJobtitleList)) {
							Map<String, Object> jobtitleData = empHeighesJobtitleList.get(0);
							if (jobtitleData.get("wage_amount") != null) {
								totalAmount = Convert.toBigDecimal(jobtitleData.get("wage_amount")).multiply(Convert.toBigDecimal(emp.getLapsedAnnualLeaveDays())).setScale(2, RoundingMode.HALF_UP);
								temporaryAdjust.setSalaryItemAmount(totalAmount);
							} else if (jobtitleData.get("wage_amount") == null && StringUtils.isNotBlank(Convert.toStr(jobtitleData.get("jobtitle_basic_name")))) {
								//职称未设置失效后折现金额则按获取其他职称折现金额处理
								List<HrmsAnnualLeaveWageRules> wageRulesList = setting.getAnnualLeaveWageRulesList().stream().filter(vo -> "其他职称".equals(vo.getJobtitle())).collect(Collectors.toList());
								if (CollUtil.isNotEmpty(wageRulesList) && wageRulesList.get(0).getWageAmount() != null) {
									totalAmount = Convert.toBigDecimal(wageRulesList.get(0).getWageAmount()).multiply(Convert.toBigDecimal(emp.getLapsedAnnualLeaveDays())).setScale(2, RoundingMode.HALF_UP);
									temporaryAdjust.setSalaryItemAmount(totalAmount);
								} else {
									continue;
								}
							} else {
								//员工无职称
								List<HrmsAnnualLeaveWageRules> wageRulesList = setting.getAnnualLeaveWageRulesList().stream().filter(vo -> "无职称".equals(vo.getJobtitle())).collect(Collectors.toList());
								if (CollUtil.isNotEmpty(wageRulesList) && wageRulesList.get(0).getWageAmount() != null) {
									totalAmount = Convert.toBigDecimal(wageRulesList.get(0).getWageAmount()).multiply(Convert.toBigDecimal(emp.getLapsedAnnualLeaveDays())).setScale(2, RoundingMode.HALF_UP);
									temporaryAdjust.setSalaryItemAmount(totalAmount);
								} else {
									continue;
								}
							}
						} else {
							//员工无职称
							List<HrmsAnnualLeaveWageRules> wageRulesList = setting.getAnnualLeaveWageRulesList().stream().filter(vo -> "无职称".equals(vo.getJobtitle())).collect(Collectors.toList());
							if (CollUtil.isNotEmpty(wageRulesList) && wageRulesList.get(0).getWageAmount() != null) {
								totalAmount = Convert.toBigDecimal(wageRulesList.get(0).getWageAmount()).multiply(Convert.toBigDecimal(emp.getLapsedAnnualLeaveDays())).setScale(2, RoundingMode.HALF_UP);
								temporaryAdjust.setSalaryItemAmount(totalAmount);
							} else {
								continue;
							}
						}
						temporaryAdjust.setRemark(emp.getYear() + "年度[" + emp.getLapsedAnnualLeaveDays() + "天]失效年假折算工资");
						temporaryAdjust.setCountType("1");
						if (totalAmount.compareTo(BigDecimal.ZERO) < 0) {
							temporaryAdjust.setCountType("2");
						}
						newsalaryTemporaryAdjustService.save(temporaryAdjust, NewsalaryTemporaryAdjustOpTypeEnum.ADD);
					}
				}
				if (!"1".equals(emp.getIsConvertWage())) {
					emp.setIsConvertWage("1");
					update(emp);
				}
			}catch(Exception e){
				errorMessage.append("员工"+emp.getEmployeeName()+",工号【"+emp.getEmployeeNo()+"】失效年假折算工资失败："+e.getMessage()+"\n");
			}
		}
		return errorMessage.toString();
	}

	/**
	 * 获取员工年假参数数据
	 * @return
	 */
	@Override
	public List<Map<String,Object>> getEmployeeAnnualLeaveParamsData(String yearStartDate,String yearEndDate,String employeeId){
		return mapper.getEmployeeAnnualLeaveParamsData(yearStartDate,yearEndDate,UserInfoHolder.getCurrentUserCorpCode(),employeeId);
	}
	/**
	 * 根据请假统计数据批量更新员工年假数据
	 * @return
	 */
	@Transactional(readOnly = false)
	@Override
	public int batchUpdateEmpAnnualLeaveByLeaveReport(){
		return mapper.batchUpdateEmpAnnualLeaveByLeaveReport();
	}

	/**
	 * 将上年剩余年假转移到本年
	 * @return
	 */
	@Transactional(readOnly = false)
	@Override
	public int saveLastYearRemainingAnnualLeaveDays(){
		return mapper.saveLastYearRemainingAnnualLeaveDays();
	}

	/**
	 * 计算并保存上一年的失效年假
	 * @return
	 */
	@Transactional(readOnly = false)
	@Override
	public int saveLastYearLapsedAnnualLeaveDays(){
		return mapper.saveLastYearLapsedAnnualLeaveDays();
	}

	/**
	 * 根据病假、事假是否达到累计天数更新员工可休年假
	 * 判断员工事假、病假是否超标，如果已超限定天数，则将可休年假设置为0
	 * @param startDate
	 * @param endDate
	 * @param employeeNo
	 */
	@Transactional(readOnly = false)
	@Override
	public void updateEmpAnnualLeaveDaysByLeaveDay(Date startDate, Date endDate, String employeeNo){
		log.error("================更新员工年假开始");
		int yearNow = DateUtil.year(startDate);
		int yearLeaveStart = DateUtil.year(startDate);
		Map<String, String> employee = hrmsEmployeeService.getEmployeeByEmployeeNo(employeeNo);
		if(CollUtil.isNotEmpty(employee)) {
			Date yearStartDate = DateUtil.beginOfYear(startDate);
			Date yearEndDate = DateUtil.endOfYear(endDate);
			List<Map<String,Object>> emAnnualLeaveList =getEmployeeAnnualLeaveParamsData(DateUtil.formatDate(yearStartDate),DateUtil.formatDate(yearEndDate), employee.get("employee_id"));
			if(CollUtil.isNotEmpty(emAnnualLeaveList)){
				Map<String,Object> emAnnualLeave = emAnnualLeaveList.get(0);
				log.error("================年假请假数据"+ JSONUtil.toJsonStr(emAnnualLeave));
				//工龄开始时间不为空
				if(emAnnualLeave.get("glkssj")!=null && emAnnualLeave.get("glyearstartnum") != null && Convert.toInt(emAnnualLeave.get("glyearstartnum")).intValue()>=1) {
					Integer glyearstartnum = Convert.toInt(emAnnualLeave.get("glyearstartnum"));
					Integer glyearendnum = Convert.toInt(emAnnualLeave.get("glyearendnum"));
					Date curyeardate = Convert.toDate(emAnnualLeave.get("curyeardate"));
					//根据员工工龄年份或当前日期获取所在年假阶梯
					HrmsAnnualLeaveSetting hrmsAnnualLeaveSetting = hrmsAnnualLeaveSettingService.getDataByYear(yearNow + "");
					HrmsAnnualLeaveDaysRule daysRule = null;
					if (hrmsAnnualLeaveSetting != null && CollUtil.isNotEmpty(hrmsAnnualLeaveSetting.getAnnualLeaveDaysRuleList())) {
						//工龄开始时间当年年份 > 请假开始时间，则按未满年份的阶梯计算
						if (curyeardate.compareTo(startDate) > 0) {
							daysRule = hrmsAnnualLeaveSetting.getAnnualLeaveDaysRuleList().stream()
									.filter(vo -> ((glyearstartnum.compareTo(vo.getStartYearLimit()) >= 0 && (glyearstartnum.compareTo(vo.getEndYearLimit())<0 || vo.getEndYearLimit() == null)))).findFirst().get();
//							daysRule = hrmsAnnualLeaveSetting.getAnnualLeaveDaysRuleList().stream()
//									.filter(vo -> ((glyearstartnum >= vo.getStartYearLimit() && glyearstartnum < vo.getEndYearLimit() && glyearendnum < vo.getEndYearLimit())
//											|| (glyearstartnum <= vo.getStartYearLimit() && glyearendnum >= vo.getStartYearLimit() && glyearendnum <= vo.getEndYearLimit()))).findFirst().get();
						} else {
							daysRule = hrmsAnnualLeaveSetting.getAnnualLeaveDaysRuleList().stream().
									filter(vo -> ((glyearendnum.compareTo(vo.getStartYearLimit().intValue()) >= 0 && (glyearendnum.compareTo(vo.getEndYearLimit()) <0 || vo.getEndYearLimit() == null)))).findFirst().get();
						}
						log.error("================年假阶梯规则："+ JSONUtil.toJsonStr(daysRule));
						if(daysRule != null) {
							//查询员工第一年的年假数据并保存已使用年假
							HrmsAnnualLeaveEmp annualLeaveEmp = new HrmsAnnualLeaveEmp();
							annualLeaveEmp.setYear(yearLeaveStart + "");
							annualLeaveEmp.setEmployeeNo(employeeNo);
							List<HrmsAnnualLeaveEmp> empLeaveDayList = getList(annualLeaveEmp);
							if (CollUtil.isNotEmpty(empLeaveDayList)) {
								annualLeaveEmp = empLeaveDayList.get(0);
								log.error("================当前年假："+ JSONUtil.toJsonStr(annualLeaveEmp));
								//病假或事假天数大于年假阶梯的累计天数
								if ((emAnnualLeave.get("sjdays") != null && daysRule.getTotalPersonalLeaveDays() != null && daysRule.getTotalPersonalLeaveDays().intValue() <= Convert.toInt(emAnnualLeave.get("sjdays")).intValue())
										|| (emAnnualLeave.get("bjdays") != null && daysRule.getTotalSickLeaveDays() != null  && daysRule.getTotalSickLeaveDays().intValue() <= Convert.toInt(emAnnualLeave.get("bjdays")).intValue())) {
									int availableAnnualLeaveDays = annualLeaveEmp.getAvailableAnnualLeaveDays();
									annualLeaveEmp.setDeductAnnualLeaveDays(availableAnnualLeaveDays);
									annualLeaveEmp.setAvailableAnnualLeaveDays(0);
									annualLeaveEmp.setRemark("病假或事假已达到累计天数，取消本年年假天数");
									update(annualLeaveEmp);
									log.error("================修改年假11111："+ JSONUtil.toJsonStr(annualLeaveEmp));
								} else if(annualLeaveEmp.getDeductAnnualLeaveDays()!=null && annualLeaveEmp.getDeductAnnualLeaveDays().intValue()>0){
									int deductAnnualLeaveDays = annualLeaveEmp.getDeductAnnualLeaveDays();
									annualLeaveEmp.setAvailableAnnualLeaveDays(deductAnnualLeaveDays);
									annualLeaveEmp.setDeductAnnualLeaveDays(0);
									annualLeaveEmp.setRemark("");
									update(annualLeaveEmp);
									log.error("================修改年假22222："+ JSONUtil.toJsonStr(annualLeaveEmp));
								}
							}
						}
					}
				}
			}
		}
	}
}
