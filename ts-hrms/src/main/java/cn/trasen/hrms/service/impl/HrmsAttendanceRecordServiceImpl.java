package cn.trasen.hrms.service.impl;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.dao.HrmsAttendanceRecordMapper;
import cn.trasen.hrms.dao.HrmsAttendanceStatisticsMapper;
import cn.trasen.hrms.enums.SalaryItemTypeEnum;
import cn.trasen.hrms.model.HrmsAttendanceDetail;
import cn.trasen.hrms.model.HrmsAttendanceRecord;
import cn.trasen.hrms.model.HrmsAttendanceStatistics;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsSalaryItem;
import cn.trasen.hrms.service.HrmsAttendanceDetailService;
import cn.trasen.hrms.service.HrmsAttendanceRecordService;
import cn.trasen.hrms.service.HrmsAttendanceStatisticsService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsSalaryItemService;
import cn.trasen.hrms.utils.DateUtils;
//import org.jeecgframework.poi.excel.ExcelExportUtil;
import cn.trasen.hrms.utils.ExcelExportUtils;
import cn.trasen.hrms.utils.UserPermissionManager;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @Title: HrmsAttendanceRecordServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 考勤记录 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月18日 上午9:15:10
 * @version V1.0
 */
@Slf4j
@Service
public class HrmsAttendanceRecordServiceImpl implements HrmsAttendanceRecordService {

	@Autowired
	HrmsAttendanceRecordMapper hrmsAttendanceRecordMapper;
	@Autowired
	HrmsSalaryItemService hrmsSalaryItemService;
	@Autowired
	HrmsEmployeeService hrmsEmployeeService;
	@Autowired
	HrmsAttendanceStatisticsService hrmsAttendanceStatisticsService;
	@Autowired
	HrmsAttendanceDetailService hrmsAttendanceDetailService;

	@Autowired
	HrmsAttendanceStatisticsMapper hrmsAttendanceStatisticsMapper;

	@Value("${attendanceCode}")
	private String attendanceCode; // 正常出勤的编号

	@Value("${attendanceValue}")
	private String attendanceValue; // 正常出勤编号的值
	
	@Value("${administrationCode}")
	private String administrationCode;  //副班费40元的审核科室ID
	
	@Value("${administrationItemCode}")
	private String administrationItemCode;  //副班费40元的项目ID

	/**
	 * @Title: batchInsert
	 * @Description: 批量插入(无事务)
	 * @param list
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年5月20日 上午8:54:06
	 */
	@Override
	@Transactional(readOnly = false)
	public int batchInsert(List<HrmsAttendanceRecord> list) {
		return hrmsAttendanceRecordMapper.batchInsert(list);
	}

	/**
	 * @Title: insert
	 * @Description: 新增考勤记录
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsAttendanceRecord entity) {
		if (StringUtil.isEmpty(entity.getAttendanceRecordId())) {
			entity.setAttendanceRecordId(String.valueOf(IdWork.id.nextId()));
		}
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsAttendanceRecordMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新考勤记录
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsAttendanceRecord entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsAttendanceRecordMapper.updateByPrimaryKeySelective(entity);
	}

	public int updateByempIdAndDate(HrmsAttendanceRecord entity) {
		Example example = new Example(HrmsAttendanceRecord.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("attendanceDate", entity.getAttendanceDate());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsAttendanceRecordMapper.updateByExampleSelective(entity, example);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除考勤记录
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	public int deleted(String id) {
		HrmsAttendanceRecord record = hrmsAttendanceRecordMapper.selectByPrimaryKey(id);
		if (record != null) {
			record.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsAttendanceRecordMapper.updateByPrimaryKeySelective(record);
	}

	/**
	 * @Title: getTableHeadCols
	 * @Description: 查询考勤项目JdGrid表头列表
	 * @param
	 * @Return List<JdGridTableEntity>
	 * <AUTHOR>
	 * @date 2020年5月19日 上午01:10:16
	 */
	@Override
	public List<JdGridTableEntity> getTableHeadCols() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		// result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,
		// false, false, false, "")); // 考勤日期
		result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100, false, true, false, "", false)); // 员工ID
		result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100, false, false, false, "", false)); // 员工工号
		result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100, false, false, false, "", false)); // 员工姓名
		result.add(new JdGridTableEntity("科ID", "deptId", "", 100, false, true, false, "", false)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", false)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		salaryItemEntity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName());
				entity.setName(item.getSalaryItemCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(item.getSalaryItemType())) {
					entity.setEditable(true);
				} else {
					entity.setEditable(false);
				}
				entity.setIndex(item.getSalaryItemId());
				result.add(entity);
			}
		}

		return result;
	}

	/**
	 * @Title: getDataList
	 * @Description: 查询考勤记录列表(分页)
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsAttendanceRecord>
	 * <AUTHOR>
	 */
	@Override
	public List<Map<String, Object>> getDataList(Page page, HrmsAttendanceRecord entity) {
		List<Map<String, Object>> result = Lists.newArrayList();

		UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getUserDataPermission();
		if (CollectionUtils.isNotEmpty(userDataPermissionVo.getOrgCodeList())) {
			entity.setOrgIdList(userDataPermissionVo.getOrgCodeList());
		}
		if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {
			entity.setUserCode(userDataPermissionVo.getUserCode());
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getPageList(page, entity);

		if (CollectionUtils.isNotEmpty(list)) {
			// 查询员工考勤统计记录
			HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
			statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
			statisticsEntity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
					.getEmployeeStatisticsList(statisticsEntity);
			if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					// map.put("attendanceDate", record.getAttendanceDate());
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							map.put(o.getSalaryItemCode(), o.getAttendanceDays());
						}
					}
					result.add(map);
				});
			}
		}
		return result;
	}

	/**
	 * <p>
	 * Title: getPageList
	 * </p>
	 * <p>
	 * Description: 考勤上报列表
	 * </p>
	 * 
	 * @param page
	 * @param entity
	 * @return
	 */
	@Override
	public List<HrmsAttendanceRecord> getPageList(Page page, HrmsAttendanceRecord entity) {


		String currentUserCode = UserInfoHolder.getCurrentUserCode();
		if (!"admin".equals(currentUserCode)) {   //不是管理员就只查归自己考勤的人员
			HrmsEmployee bean = hrmsEmployeeService.findByEmployeeNo(currentUserCode);
			entity.setOrgRangs(bean.getEmployeeId()); 
		}
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getRecordPageList(page, entity);
		return list;
	}

	// 定时生成所有人的默认考勤数据
	@Override
	@Transactional(readOnly = false)
	public void setDefaultAttendance() {
		try {
			String date = DateUtils.getLastMonth();
			hrmsAttendanceRecordMapper.setDefaultAttendance(date); // 生成考勤主表数据
			// 生成考勤子表数据

			log.error("定时生成" + DateUtils.getLastMonth() + "月考勤记录正常常");
		} catch (Exception e) {
			log.error("定时生成" + DateUtils.getLastMonth() + "月考勤记录异常：" + e.getMessage(), e);
		}
	}

	/**
	 * <p>
	 * Title: batchDelete
	 * </p>
	 * <p>
	 * Description:批量删除考勤数据（包含明细）
	 * </p>
	 * 
	 * @param map
	 * @return
	 */
	@Override
	@Transactional
	public void batchDelete(Map<String, Object> map) {
		hrmsAttendanceRecordMapper.batchDelete(map);
		hrmsAttendanceDetailService.batchDelete(map);
	}

	/**
	 * <p>
	 * Title: batchInsertAttendanceRecord
	 * </p>
	 * <p>
	 * Description:批量添加考勤数据
	 * </p>
	 * 
	 * @param ids
	 * @param date
	 * @throws Exception
	 */
	@Override
	@Transactional
	public void batchInsertAttendanceRecord(String ids, String date, String orgIds) throws Exception {
		if (!StringUtil.isEmpty(ids) && !StringUtil.isEmpty(date)) {
			List<HrmsAttendanceRecord> list = new ArrayList<>();
			List<HrmsAttendanceDetail> listDetail = new ArrayList<>();

			List<String> mlist = Arrays.asList(ids.split(","));
			List<String> mOrgIdlist = Arrays.asList(orgIds.split(","));

			Map<String, Object> mapP = new HashMap<>();
			mapP.put("list", mlist);
			mapP.put("date", date);
			List<HrmsAttendanceRecord> reList = hrmsAttendanceRecordMapper.getRecordByEmpIdAndDate(mapP);

			Map<String, String> recordMap = reList.stream().collect(
					Collectors.toMap(HrmsAttendanceRecord::getEmployeeId, HrmsAttendanceRecord::getAttendanceRecordId));

			for (int i = 0; i < mlist.size(); i++) {
				HrmsAttendanceRecord bean = new HrmsAttendanceRecord();
				String rid = "";
				if (recordMap.get(mlist.get(i)) != null) { // 如果表原来有数据的就用原来的id,
					rid = recordMap.get(mlist.get(i));  //拿审核表id
				} else {
					rid = String.valueOf(IdWork.id.nextId());
				}
				bean.setAttendanceRecordId(rid);
				bean.setBelongOrg(mOrgIdlist.get(i));
				bean.setEmployeeId(mlist.get(i));
				bean.setAttendanceDate(date);
				bean.setApprovalStatus("0"); // 审核状态 0审核
				bean.setSetQq("1"); 	//已设置全勤标志
				bean.setCreateDate(new Date());
				bean.setCreateUser(UserInfoHolder.getCurrentUserCode());
				bean.setCreateUserName(UserInfoHolder.getCurrentUserName());
				bean.setIsDeleted(Contants.IS_DELETED_FALSE);
				bean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				if (UserInfoHolder.getCurrentUserInfo() != null) {
					bean.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
					bean.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
				}
				listDetail.addAll(setDetail(rid, date, mlist.get(i))); // 设置全勤明细
				list.add(bean);
			}

			Map<String, Object> map = new HashMap<>();
			map.put("list", list);
			map.put("date", date);
//			hrmsAttendanceRecordMapper.batchDelete(map);
			hrmsAttendanceRecordMapper.batchInsert(list);
			Integer count = 700;
			if (listDetail != null && listDetail.size() > 0) {
				if (listDetail.size() > 700) {
					int begin = 0;
					int end = begin + count;
					while (begin <= listDetail.size() - 1) {
						List<HrmsAttendanceDetail> fragmentList = listDetail.subList(begin,
								end <= listDetail.size() ? end : listDetail.size());
						hrmsAttendanceDetailService.batchInsert(fragmentList);
						begin = end;
						end = begin + count;
					}
				} else {
					hrmsAttendanceDetailService.batchInsert(listDetail);
				}
			}
		}
	}
	
	//生成子表全勤数据
	private List<HrmsAttendanceDetail> setDetail(String rid, String date, String empId) {
		String[] _split = date.split("-");
		int year = Integer.valueOf(_split[0]);
		int month = Integer.valueOf(_split[1]);
		Calendar c = Calendar.getInstance();
		c.set(year, month, 0); // 输入类型为int类型
		int days = c.get(Calendar.DAY_OF_MONTH); // 得到月份天数
		List<HrmsAttendanceDetail> list = new ArrayList<>();
	
		
		// 查询人员考勤月份详细数据
		HrmsAttendanceDetail entity = new HrmsAttendanceDetail();
		entity.setAttendanceDate(date); // 传入考勤月份
		entity.setEmployeeId(empId);
		entity.setAttendanceItemId(attendanceCode);
		List<HrmsAttendanceDetail> timecardDetail = hrmsAttendanceDetailService.getTimecardDetailByType(entity); // 查询没有2
																													// 3的请假类型的数据
		Map<String, List<HrmsAttendanceDetail>> detaiMap = timecardDetail.stream()
				.collect(Collectors.groupingBy(HrmsAttendanceDetail::getAttendanceDate));

		for (int i = 1; i <= days; i++) {
			String _data = "";
			if (i < 10) {
				_data = date + "-0" + i;
			} else {
				_data = date + "-" + i;
			}
			// 这一天没有考勤和这一天没有全勤的要插入考勤数据
			if (detaiMap.get(_data) == null) {
				HrmsAttendanceDetail rBean = new HrmsAttendanceDetail();
				rBean.setAttendanceDetailId(String.valueOf(IdWork.id.nextId()));
				rBean.setAttendanceItemId(attendanceCode);
				rBean.setRemark(attendanceValue);
				rBean.setAttendanceRecordId(rid);
				rBean.setCreateDate(new Date());
				rBean.setCreateUser(UserInfoHolder.getCurrentUserCode());
				rBean.setCreateUserName(UserInfoHolder.getCurrentUserName());
				rBean.setEmployeeId(empId);
				rBean.setAttendanceDate(_data);
				list.add(rBean);
			}
		}
		return list;
	}

	/**
	 * <p>
	 * Title: batchUpdate
	 * </p>
	 * <p>
	 * Description:批量修改状态
	 * </p>
	 * 
	 * @param map
	 * @return
	 */
	@Override
	public int batchUpdate(Map<String, Object> map) {
		return hrmsAttendanceRecordMapper.batchUpdate(map);
	}

	/**
	 * <p>
	 * @Title: reportedTimecard
	 * </p>
	 * <p>
	 * @Description: 上报考勤数据
	 * </p>
	 * <p>
	 * @Param:
	 * </p>
	 * <p>
	 * @Return: void
	 * </p>
	 * <P>
	 * @Date: 2020年7月25日 下午4:30:52
	 * </p>
	 * <p>
	 * <AUTHOR>
	 * </p>
	 */
	@Override
	@Transactional
	public void reportedTimecard(Map<String, Object> map) {
		hrmsAttendanceRecordMapper.batchUpdate(map); // 修改状态
		Map<String, Object> param = new HashMap<>();
		param.put("date", map.get("date"));
		param.put("createUser", UserInfoHolder.getCurrentUserCode());
		param.put("createUserName", UserInfoHolder.getCurrentUserName());
		param.put("createDate", new Date());
		HrmsAttendanceStatistics record = new HrmsAttendanceStatistics();
		record.setCountDate(map.get("date").toString());
		hrmsAttendanceStatisticsService.deleteByParam(map);// 根据条件删除统计
		hrmsAttendanceStatisticsService.insertByParam(map); // 根据条件统计
		
		//归属办公室审核的人员没有副班40元的人员全部设置为已审核状态
/*		Map<String, Object> forty = new HashMap<>();
		forty.put("attendance_item_id", administrationItemCode);
		forty.put("review_depart", administrationCode);
		forty.put("attendance_date", map.get("date"));
		hrmsAttendanceRecordMapper.updateForty(forty);*/
		

	}

	// 部门统计动态表头
	@Override
	public List<JdGridTableEntity> getDeptAuditTableHeadCols() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		// result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,
		// false, false, false, "")); // 考勤日期
//		result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100,false, true, false, "",true)); // 员工ID
//		result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100,false, false, false, "",true)); // 员工工号
//		result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100,false, false, false, "",true)); // 员工姓名
		result.add(new JdGridTableEntity("科ID", "deptId", "", 100, false, true, false, "", true)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", true)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName());
				entity.setName(item.getSalaryItemCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				entity.setIndex(item.getSalaryItemId());
				result.add(entity);
			}
		}
		result.add(new JdGridTableEntity("科室审核", "deptStatus", "", 100, false, false, false, "", false)); // 科室审核状态
		return result;
	}

	/**
	 * <p>
	 * Title: geDeptAuditList
	 * </p>
	 * <p>
	 * Description:科室考勤审核列表，按部门统计
	 * </p>
	 * 
	 * @param page
	 * @param entity
	 * @return
	 */
	@Override
	public List<Map<String, Object>> geDeptAuditList(Page page, HrmsAttendanceRecord entity) {
		// 按月份取出统计表数据
		List<HrmsAttendanceStatistics> attendanceStatisticsList = getDeptAuditStatisticsList(entity);
		List<Map<String, Object>> result = Lists.newArrayList();
		entity.setReviewDepart(UserInfoHolder.getCurrentUserInfo().getDeptcode());
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			if(CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream().filter(item->StringUtils.isNotBlank(item.getOrgId()))
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getOrgId));  //科室分组
				
				attendanceStatisticsMap.forEach((k, v) -> {	//遍历科室
					Map<String, Object> map = Maps.newHashMap();
					map.put("deptId", k);
					map.put("deptName",v.get(0).getOrgName());
					Map<String, List<HrmsAttendanceStatistics>> typeMap = v.stream().filter(item->StringUtils.isNotBlank(item.getSalaryItemCode()))
							.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode));  //科室数据按考勤项目分组
					typeMap.forEach((k1,v1)->{
						Map<String, Long> collect = v1.stream().filter(item->StringUtils.isNotBlank(item.getSalaryItemCode())).collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode, 
								Collectors.summingLong(HrmsAttendanceStatistics::getAttendanceDays)));
						map.putAll(collect);
					});
					String approvalStatus = v.get(0).getApprovalStatus();
					if(!StringUtil.isEmpty(approvalStatus)) {
						if("1".equals(approvalStatus)) {
							map.put("deptStatus","<span style='color:#FFD700'>已提交</span>");
						}else if("2".equals(approvalStatus)) {
							map.put("deptStatus","<span style='color:#00BFFF'>已审核</span>");
						}else if("3".equals(approvalStatus)) {
							map.put("deptStatus","<span style='color:#FF0000'>已生效</span>");
						}else {
							map.put("deptStatus","<span style='color:#808080'>未提交</span>");
						}
					}
					result.add(map);
				});
			}
		}
			/*if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					// map.put("attendanceDate", record.getAttendanceDate());
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							map.put(o.getSalaryItemCode(), o.getAttendanceDays());
						}
					}
					String deptStatus = record.getApprovalStatus();
					
					if ("1".equals(deptStatus)) {
						map.put("deptStatus", "<span style='color:#FFD700'>已提交</span>");
					} else if ("2".equals(deptStatus)) {
						map.put("deptStatus", "<span style='color:#00BFFF'>已审核</span>");
					} else if ("3".equals(deptStatus)) {
						map.put("deptStatus", "<span style='color:#FF0000'>已生效</span>");
					} else {
						map.put("deptStatus", "<span style='color:#808080'>未提交</span>");
					}
					
					result.add(map);
				});
			}*/
	
		return result;
	}
	

	
	// 自己上报的考勤明细
	@Override
	public List<Map<String, Object>> attendanceReportedDataDetailsTable(Page page, HrmsAttendanceRecord entity) {
		List<Map<String, Object>> result = Lists.newArrayList();
		//根据用户code获取empid
		HrmsEmployee hrmsEmployee = hrmsEmployeeService.findByEmployeeNo(UserInfoHolder.getCurrentUserCode());
		entity.setUserCode(hrmsEmployee.getEmployeeId());
		List<HrmsAttendanceStatistics> list = hrmsAttendanceRecordMapper.attendanceReportedDataDetailsTable(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = list.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				if(attendanceStatisticsMap != null) {
					for (String key : attendanceStatisticsMap.keySet()) {
						List<HrmsAttendanceStatistics> listStatis = attendanceStatisticsMap.get(key);
						if (CollectionUtils.isNotEmpty(listStatis)) {
							Map<String, Object> map = Maps.newHashMap();
							map.put("employeeId", listStatis.get(0).getEmployeeId());
							map.put("employeeNo", listStatis.get(0).getEmployeeNo());
							map.put("employeeName", listStatis.get(0).getEmployeeName());
							map.put("orgId", listStatis.get(0).getOrgId());
							map.put("deptName", listStatis.get(0).getOrgName());
							for (HrmsAttendanceStatistics o : listStatis) {
								map.put(o.getSalaryItemCode(), o.getAttendanceDays());
							}
							result.add(map);
						}
					}
				}
				
			/*	list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());
					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							map.put(o.getSalaryItemCode(), o.getAttendanceDays());
						}
						result.add(map);
					}
				});*/
		}
		return result;
	}
	

	// 部门详情
	@Override
	public List<Map<String, Object>> getAttendanceStatisticsListByDept(Page page, HrmsAttendanceRecord entity) {
		List<Map<String, Object>> result = Lists.newArrayList();
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			// 查询员工考勤统计记录
			HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
			statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
			statisticsEntity.setIsQQ(entity.getIsQQ());
			statisticsEntity.setAttendanceNumber(attendanceCode);
			statisticsEntity.setDeptCheck(entity.getDeptCheck());
			statisticsEntity.setOvertime(entity.getOvertime());
			List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
					.getEmployeeStatisticsList(statisticsEntity);
			if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					// map.put("attendanceDate", record.getAttendanceDate());
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							if(null != o.getSalaryItemCode()) {
								map.put(o.getSalaryItemCode(), o.getAttendanceDays());
							}
						}
						result.add(map);
					}
					
				});
			}
		}
		return result;
	}

	// 全勤审核表头
	@Override
	public List<JdGridTableEntity> getPersonnelAllDayAuditTableHeadCols() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		// result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,
		// false, false, false, "")); // 考勤日期
		// result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100,
		// false, true, false, "")); // 员工ID
		// result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100,
		// false, false, false, "")); // 员工工号
		// result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100,
		// false, false, false, "")); // 员工姓名
		result.add(new JdGridTableEntity("科ID", "deptId", "", 100, false, true, false, "", true)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", true)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName());
				entity.setName(item.getSalaryItemCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(item.getSalaryItemType())) {
					entity.setEditable(true);
				} else {
					entity.setEditable(false);
				}
				entity.setIndex(item.getSalaryItemId());
				result.add(entity);
			}
		}
		result.add(new JdGridTableEntity("科室审核", "deptStatus", "", 100, false, false, false, "", true)); // 科室审核状态
		result.add(new JdGridTableEntity("全勤审核", "rskqCheckStatus", "", 100, false, false, false, "", true)); // 考勤审核
		return result;
	}

	// 全勤审核数据
	@Override
	public List<Map<String, Object>> gePersonnelAllDayAuditList(Page page, HrmsAttendanceRecord entity) {

		List<Map<String, Object>> result = Lists.newArrayList();
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setOrgId(entity.getOrgId());
		statisticsEntity.setRskqCheckStatus(entity.getApprovalStatus());
		statisticsEntity.setAttendanceNumber(attendanceCode);
		List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
				.getAllDayStatisticsList(statisticsEntity);
		if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
			Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
					.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getOrgId)); // 科室分组

			attendanceStatisticsMap.forEach((k, v) -> { // 遍历科室
				Map<String, Object> map = Maps.newHashMap();
				map.put("deptId", k);
				map.put("deptName", v.get(0).getOrgName());
				Map<String, List<HrmsAttendanceStatistics>> typeMap = v.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode)); // 科室数据按考勤项目分组
				typeMap.forEach((k1, v1) -> {
					Map<String, Long> collect = v1.stream()
							.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode,
									Collectors.summingLong(HrmsAttendanceStatistics::getAttendanceDays)));
					map.putAll(collect);
				});
				String approvalStatus = v.get(0).getApprovalStatus();
				if (!StringUtil.isEmpty(approvalStatus)) { // 科室审核
					String _value = "";
					if ("1".equals(approvalStatus)) {
						_value = "<span style='color:#FFD700'>已提交</span>";
					} else if ("2".equals(approvalStatus)) {
						_value = "<span style='color:#00BFFF'>已审核</span>";
					} else if ("3".equals(approvalStatus)) {
						_value = "<span style='color:#FF0000'>已生效</span>";
					} else {
						_value = "<span style='color:#808080'>未提交</span>";
					}
					map.put("deptStatus", _value);
				}
				// 考勤审核
				String rskqCheckStatus = v.get(0).getRskqCheckStatus();
				if (!StringUtil.isEmpty(approvalStatus) && "1".equals(rskqCheckStatus)) {
					map.put("rskqCheckStatus", "<span style='color:#FF0000'>已生效</span>");
				} else {
					map.put("rskqCheckStatus", "<span style='color:#808080'>未生效</span>");
				}
				result.add(map);
			});
		}
		return result;
	}

	@Override
	public List<JdGridTableEntity> getPersonnelSpecialDayAuditTableHeadCols() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		// result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,
		// false, false, false, "")); // 考勤日期
		// result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100,
		// false, true, false, "")); // 员工ID
		// result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100,
		// false, false, false, "")); // 员工工号
		// result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100,
		// false, false, false, "")); // 员工姓名
		result.add(new JdGridTableEntity("科ID", "deptId", "", 100, false, true, false, "", true)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", true)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName());
				entity.setName(item.getSalaryItemCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(item.getSalaryItemType())) {
					entity.setEditable(true);
				} else {
					entity.setEditable(false);
				}
				entity.setIndex(item.getSalaryItemId());
				result.add(entity);
			}
		}
		result.add(new JdGridTableEntity("科室审核", "deptStatus", "", 100, false, false, false, "", true)); // 科室审核状态
		result.add(new JdGridTableEntity("晚夜班审核", "rswybCheckStatus", "", 100, false, false, false, "", true)); // 考勤审核
		return result;
	}

	// 晚夜班审核
	@Override
	public List<Map<String, Object>> getpersonnelSpecialDayAuditList(Page page, HrmsAttendanceRecord entity) {

		List<Map<String, Object>> result = Lists.newArrayList();

		// 按月份取出统计表数据
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setOrgId(entity.getOrgId());
		statisticsEntity.setAttendanceNumber(attendanceCode);
		statisticsEntity.setRswybCheckStatus(entity.getApprovalStatus());
		List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
				.getPersonnelSpecialDayStatisticsList(statisticsEntity);
		if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
			Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
					.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getOrgId)); // 科室分组

			attendanceStatisticsMap.forEach((k, v) -> { // 遍历科室
				Map<String, Object> map = Maps.newHashMap();
				map.put("deptId", k);
				map.put("deptName", v.get(0).getOrgName());
				Map<String, List<HrmsAttendanceStatistics>> typeMap = v.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode)); // 科室数据按考勤项目分组
				typeMap.forEach((k1, v1) -> {
					Map<String, Long> collect = v1.stream()
							.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode,
									Collectors.summingLong(HrmsAttendanceStatistics::getAttendanceDays)));
					map.putAll(collect);
				});
				String approvalStatus = v.get(0).getApprovalStatus();
				if (!StringUtil.isEmpty(approvalStatus)) { // 科室审核
					String _value = "";
					if ("1".equals(approvalStatus)) {
						_value = "<span style='color:#FFD700'>已提交</span>";
					} else if ("2".equals(approvalStatus)) {
						_value = "<span style='color:#00BFFF'>已审核</span>";
					} else if ("3".equals(approvalStatus)) {
						_value = "<span style='color:#FF0000'>已生效</span>";
					} else {
						_value = "<span style='color:#808080'>未提交</span>";
					}
					map.put("deptStatus", _value);
				}
				// 考勤审核
				String rswybCheckStatus = v.get(0).getRswybCheckStatus();
				if (!StringUtil.isEmpty(approvalStatus) && "1".equals(rswybCheckStatus)) {
					map.put("rswybCheckStatus", "<span style='color:#FF0000'>已生效</span>");
				} else {
					map.put("rswybCheckStatus", "<span style='color:#808080'>未生效</span>");
				}
				result.add(map);
			});
		}
		return result;
	}

	// 科室审核
	@Override
	public void updateStatusByOrg(Map<String, Object> map) {
		hrmsAttendanceRecordMapper.updateStatusByOrg(map);
	}

	// 人事考勤审核
	@Override
	public void updatePersonnelAllDayAudit(Map<String, Object> map) {
		hrmsAttendanceRecordMapper.updatePersonnelAllDayAudit(map);
	}

	@Override
	public void updatePersonnelSpecialDayAudit(Map<String, Object> map) {
		hrmsAttendanceRecordMapper.updatePersonnelSpecialDayAudit(map);

	}

	@Override
	public List<JdGridTableEntity> getTableHeadColsByDept() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		// result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,
		// false, false, false, "")); // 考勤日期
		result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100, false, true, false, "", true)); // 员工ID
		result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100, false, false, false, "", true)); // 员工工号
		result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100, false, false, false, "", true)); // 员工姓名
		result.add(new JdGridTableEntity("科室ID", "deptId", "", 100, false, true, false, "", true)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", true)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName());
				entity.setName(item.getSalaryItemCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(item.getSalaryItemType())) {
					entity.setEditable(true);
				} else {
					entity.setEditable(false);
				}
				entity.setIndex(item.getSalaryItemId());
				result.add(entity);
			}
		}
		return result;
	}

	/**
	 * 
	 * @Title: getDeptAuditStatisticsList
	 * @Description: 获取所有数据
	 * @Params: @param entity
	 * @Params: @return
	 * @Return: List<HrmsAttendanceStatistics>
	 * <AUTHOR>
	 * @date:2020年8月1日
	 * @Throws
	 */
	private List<HrmsAttendanceStatistics> getDeptAuditStatisticsList(HrmsAttendanceRecord entity) {
		// 按月份取出统计表数据
		String deptCode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setOrgId(entity.getOrgId());
		statisticsEntity.setApprovalStatus(entity.getApprovalStatus());
		statisticsEntity.setOrgId(entity.getOrgId());
		statisticsEntity.setReviewDepart(deptCode);
		//办公室特殊处理()
	/*	if(administrationCode.equals(deptCode)) {
			statisticsEntity.setAttendanceItemId(administrationItemCode);
		}*/
		List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
				.getDeptAuditStatisticsList(statisticsEntity);

		return attendanceStatisticsList;
	}

	/**
	 * 
	 * @Title: exportDeptAuditList
	 * @Description: 导出
	 * @Params: @param page
	 * @Params: @param entity
	 * @Return: void
	 * <AUTHOR>
	 * @date:2020年8月1日
	 * @Throws
	 */
	public void exportDeptAuditList(Page page, HrmsAttendanceRecord entity,HttpServletResponse response) {
		
		
		String filename = entity.getTitle();
		
		//表头
		List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
		
		//导出整理后的数据
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		// 查询所有数据
		List<HrmsAttendanceStatistics> attendanceStatisticsList = getDeptAuditStatisticsList(entity);
		
		//设置导出表头
		Map<String, String> titleMap = setCol(attendanceStatisticsList, colList);
		colList.forEach(item ->{item.setHeight(6);});  //设置表格高度
		//绑定数据
		List<Map<String, Object>> dataMap = groupAttendanceStatisticsList(attendanceStatisticsList);
		
		Integer index = 1;
		for(Map<String,Object> map : dataMap) {
			
			Map<String, Object> valMap = new HashMap<String, Object>();
			
			for(String key : titleMap.keySet()) {
				
				valMap.put(key, map.get(key));
			}
			valMap.put("no", index);
			list.add(valMap);
			index++;
		}
		
		try {
            Workbook workbook = ExcelExportUtils
                    .exportExcel(new ExportParams(filename, "数据", ExcelType.XSSF), colList, list);

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

            OutputStream fos = response.getOutputStream();
            
            workbook.write(fos);
            fos.close();

        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
	}
	
	/**
	 * 
	* @Title: groupAttendanceStatisticsList  
	* @Description: 对查询出的所有数据做分组
	* @Params: @param attendanceStatisticsList
	* @Params: @return      
	* @Return: List<Map<String,Object>>
	* <AUTHOR>
	* @date:2020年8月1日
	* @Throws
	 */
	private List<Map<String, Object>> groupAttendanceStatisticsList(List<HrmsAttendanceStatistics> attendanceStatisticsList) {
		
		List<Map<String, Object>> result = Lists.newArrayList();
		
		if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
			Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
					.filter(item->StringUtils.isNotBlank(item.getOrgId()))
					.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getOrgId)); // 科室分组

			attendanceStatisticsMap.forEach((k, v) -> { // 遍历科室
				Map<String, Object> map = Maps.newHashMap();
				map.put("deptId", k);
				map.put("deptName", v.get(0).getOrgName());
				Map<String, List<HrmsAttendanceStatistics>> typeMap = v.stream()
						.filter(item->StringUtils.isNotBlank(item.getSalaryItemCode()))
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode)); // 科室数据按考勤项目分组
				typeMap.forEach((k1, v1) -> {
					Map<String, Long> collect = v1.stream()
							.filter(item->StringUtils.isNotBlank(item.getSalaryItemCode()))
							.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getSalaryItemCode,
									Collectors.summingLong(HrmsAttendanceStatistics::getAttendanceDays)));
					map.putAll(collect);
				});
				String approvalStatus = v.get(0).getApprovalStatus();
				if (!StringUtil.isEmpty(approvalStatus)) {
					if ("1".equals(approvalStatus)) {
						map.put("deptStatus", "<span style='color:#FFD700'>已提交</span>");
					} else if ("2".equals(approvalStatus)) {
						map.put("deptStatus", "<span style='color:#00BFFF'>已审核</span>");
					} else if ("3".equals(approvalStatus)) {
						map.put("deptStatus", "<span style='color:#FF0000'>已生效</span>");
					} else {
						map.put("deptStatus", "<span style='color:#808080'>未提交</span>");
					}
				}
				result.add(map);
			});
		}
		
		return result;
	}
	
	/**
	 * 
	* @Title: setCol  
	* @Description: 设置导出表头
	* @Params: @param attendanceStatisticsList      
	* @Return: void
	* <AUTHOR>
	* @date:2020年8月1日
	* @Throws
	 */
	private Map<String, String> setCol(List<HrmsAttendanceStatistics> attendanceStatisticsList,List<ExcelExportEntity> colList) {

		// 获取所有表头
		Map<String, String> titleMap = new LinkedHashMap<>();

		for (HrmsAttendanceStatistics record : attendanceStatisticsList) {

			titleMap.put(record.getSalaryItemCode(), record.getAttendanceItemName());
		}
		
		colList.add(new ExcelExportEntity("序号", "no",6));
        colList.add(new ExcelExportEntity("科室名称", "deptName",9));
        ExcelExportEntity colEntity = new ExcelExportEntity();
		if (!titleMap.isEmpty()) {
			for (String titel : titleMap.keySet()) {
				if(!StringUtil.isEmpty(titel)) {
					colEntity = new ExcelExportEntity(titleMap.get(titel), titel);
					colEntity.setWidth(9);
					colList.add(colEntity);
				}
			}
		}
		titleMap.put("no", "序号");
		titleMap.put("deptName", "科室名称");
		return titleMap;
	}
	
	//导出科室统计详细
	@Override
	public void exportDeptAuditDetailsList(Page page, HrmsAttendanceRecord entity, HttpServletResponse response) {
		
		String filename = entity.getTitle();
		
		//表头
		List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
		
		//导出整理后的数据
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		List<HrmsAttendanceRecord> recordList = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		// 2020-12-25  注释原因（放出所有考勤内容）  
//		statisticsEntity.setIsQQ(entity.getIsQQ());
//		statisticsEntity.setAttendanceNumber(attendanceCode);
		
		statisticsEntity.setDeptCheck(entity.getDeptCheck());
		statisticsEntity.setReviewDepart(entity.getReviewDepart());
		List<HrmsAttendanceStatistics> attendanceStatisticsList =  hrmsAttendanceStatisticsService.getEmployeeStatisticsList(statisticsEntity);
		List<HrmsAttendanceStatistics> newList = new ArrayList<>();
		if(attendanceStatisticsList != null && attendanceStatisticsList.size() > 0) {
			attendanceStatisticsList.forEach(item ->{
				recordList.forEach(s ->{
					if(!StringUtil.isEmpty(s.getEmployeeId()) && s.getEmployeeId().equals(item.getEmployeeId())) {
						newList.add(item);
					}
				});
				
			});
		}
		
		//设置导出表头
		Map<String, String> titleMap = setStatementAllCol(newList, colList);
		colList.forEach(item->{item.setHeight(6);});  //设置单元格高度
		
		//绑定数据
		List<Map<String, Object>> dataMap = bindingDept(newList,recordList,entity.getAttendanceDate());
		
		
		for(Map<String,Object> map : dataMap) {
			
			Map<String, Object> valMap = new HashMap<String, Object>();
			
			for(String key : titleMap.keySet()) {
				
				valMap.put(key, map.get(key));
			}
			list.add(valMap);
		}
		
		try {
            Workbook workbook = ExcelExportUtils
                    .exportExcel(new ExportParams(filename, "数据", ExcelType.XSSF), colList, sortDept(list));

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

            OutputStream fos = response.getOutputStream();
            
            workbook.write(fos);
            fos.close();

        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
		
	}
	
	
	//科室导出绑定数据
	private List<Map<String, Object>>   bindingDept(List<HrmsAttendanceStatistics> attendanceStatisticsList ,List<HrmsAttendanceRecord> list,String date) {
			List<Map<String, Object>> result = Lists.newArrayList();
			if (CollectionUtils.isNotEmpty(list)) {
				if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
					// 将员工考勤记录根据员工ID转换成Map
					Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
							.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
//					IntStream.range(0,list.size()).forEach(i -> {
////					});
					list.stream().forEach(record -> {
						Map<String, Object> map = Maps.newHashMap();
//						map.put("no", (i+1));
						map.put("attendanceDate", date);
						map.put("employeeId", record.getEmployeeId());
						map.put("employeeNo", record.getEmployeeNo());
						map.put("employeeName", record.getEmployeeName());
						map.put("orgId", record.getOrgId());
						map.put("deptName", record.getOrgName());

						// 取出当前员工考勤统计记录
						List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
						if (CollectionUtils.isNotEmpty(statistics)) {
							for (HrmsAttendanceStatistics o : statistics) {
								if(!StringUtil.isEmpty(o.getSalaryItemCode())) {
									map.put(o.getSalaryItemCode(), o.getAttendanceDays());
								}
							}
							result.add(map);
						}
						
					});
				}
			}
			return result;
		}
	
	
	
	//导出排序
	private List<Map<String, Object>> sortDept(List<Map<String, Object>> list){
		List<Map<String, Object>> resultList  = new ArrayList<>();
		if(list != null && list.size() > 0) {
			//根据科室分组
			Map<String, List<Map<String, Object>>> groupList = list.stream().collect(Collectors.groupingBy(e -> e.get("deptName").toString()));  
			groupList.forEach((k,v)->{
		        	Collections.sort(v, new Comparator<Map<String, Object>>() {
		            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
		                String name1 = o1.get("employeeName").toString() ;//name1是从你list里面拿出来的一个 
		                String name2 = o2.get("employeeName").toString() ; //name1是从你list里面拿出来的第二个name
		                return name1.compareTo(name2);
		            }
		        });
		        resultList.addAll(v);
			});
		}
		IntStream.range(0,resultList.size()).forEach(i->{
			resultList.get(i).put("no", (i+1) );
		});
		return resultList;
	}
	private List<HrmsAttendanceStatistics> getDeptDetailsStatisticsList(Page page,HrmsAttendanceRecord entity){
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setIsQQ(entity.getIsQQ());
		statisticsEntity.setAttendanceNumber(attendanceCode);
		statisticsEntity.setDeptCheck(entity.getDeptCheck());
		statisticsEntity.setReviewDepart(entity.getReviewDepart());
		return hrmsAttendanceStatisticsService.getEmployeeStatisticsList(statisticsEntity);
	}
	
	private List<Map<String, Object>> groupDetails(Page page,HrmsAttendanceRecord entity) {
		List<Map<String, Object>> result = Lists.newArrayList();
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			// 查询员工考勤统计记录
			List<HrmsAttendanceStatistics> attendanceStatisticsList = getDeptDetailsStatisticsList(page,entity);
			if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					 map.put("attendanceDate", entity.getAttendanceDate());
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							if(!StringUtil.isEmpty(o.getSalaryItemCode())) {
								map.put(o.getSalaryItemCode(), o.getAttendanceDays());
							}
						}
						result.add(map);
					}
					
				});
			}
		}
		return result;
	}
	
	
	//全勤统计
	@Override
	public List<Map<String, Object>> getStatementAllList(Page page, HrmsAttendanceRecord entity) {
		
		List<String> dateSection = new ArrayList<>();
		if(!StringUtil.isEmpty(entity.getAttendanceDate())) {
			String[] split = entity.getAttendanceDate().split(" ~ ");
			dateSection = DateUtils.getDateSection(split[0], split[1]);
		}else { //查询当前年月
			dateSection.add(DateUtils.getPresentTimeStr().substring(0, 7));
		}
		List<Map<String, Object>> packagingAllDay = new ArrayList<>();
		dateSection.forEach( item -> {   //根据年查询数据
			entity.setAttendanceDate(item);
			List<HrmsAttendanceStatistics> attendanceStatisticsList = getStatementAll(entity);
			List<Map<String, Object>> list = packagingAllDay(entity,page,attendanceStatisticsList);
			packagingAllDay.addAll(list);
		});
		return sort(packagingAllDay);
	}
	
	//排序
	private List<Map<String, Object>> sort(List<Map<String, Object>> list){
		List<Map<String, Object>> resultList  = new ArrayList<>();
		if(list != null && list.size() > 0) {
			//根据科室分组
			Map<String, List<Map<String, Object>>> groupList = list.stream().collect(Collectors.groupingBy(e -> e.get("orgId").toString()));  
			groupList.forEach((k,v)->{
		        	Collections.sort(v, new Comparator<Map<String, Object>>() {
		            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
		                String name1 = o1.get("employeeName").toString() ;//name1是从你list里面拿出来的一个 
		                String name2 = o2.get("employeeName").toString() ; //name1是从你list里面拿出来的第二个name
		                return name1.compareTo(name2);
		            }
		        });
		        resultList.addAll(v);
			});
		}
		return resultList;
	}
	
	//获取数据
	private List<HrmsAttendanceStatistics> getStatementAll(HrmsAttendanceRecord entity) {
		// 按月份取出统计表数据
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setOrgId(entity.getOrgId());
		statisticsEntity.setRskqCheckStatus("1");
		statisticsEntity.setAttendanceNumber(attendanceCode);
		statisticsEntity.setIsQQ(entity.getIsQQ());  //查询是否全勤
		statisticsEntity.setAttendanceDays(Long.valueOf(getMonthDays(entity.getAttendanceDate())));
		List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
				.getStatementAllList(statisticsEntity);
		return attendanceStatisticsList;
	}
	
	//封装全勤请假数据
	private List<Map<String, Object>> packagingAllDay(HrmsAttendanceRecord entity,Page page,List<HrmsAttendanceStatistics> attendanceStatisticsList) {
		List<Map<String, Object>> result = Lists.newArrayList();
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			// 查询员工考勤统计记录
			if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					map.put("attendanceDate", record.getAttendanceDate());
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							map.put(o.getSalaryItemCode(), o.getAttendanceDays());
						}
						result.add(map);
					}
				});
			}
		}
		return result;
	}
	 
	

	
	//晚夜班统计
	@Override
	public List<Map<String, Object>> getStatementSpecialList(Page page, HrmsAttendanceRecord entity) {
		
		List<String> dateSection = new ArrayList<>();
		if(!StringUtil.isEmpty(entity.getAttendanceDate())) {
			String[] split = entity.getAttendanceDate().split(" ~ ");
			dateSection = DateUtils.getDateSection(split[0], split[1]);
		}else { //查询当前年月
			dateSection.add(DateUtils.getPresentTimeStr().substring(0, 7));
		}
		List<Map<String, Object>> packagingAllDay = new ArrayList<>();
		dateSection.forEach( item -> {   //根据年查询数据
			entity.setAttendanceDate(item);
			List<HrmsAttendanceStatistics> attendanceStatisticsList = getStatementSpecial(entity);
			List<Map<String, Object>> list = packagingSpecialDay(entity,page,attendanceStatisticsList);
			packagingAllDay.addAll(list);
		});
		return sort(packagingAllDay);
	}
	
	//获取统计数据
	private  List<HrmsAttendanceStatistics> getStatementSpecial(HrmsAttendanceRecord entity){
		// 查询员工特殊考勤统计记录
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setOrgId(entity.getOrgId());
		statisticsEntity.setRskqCheckStatus(entity.getApprovalStatus());
		statisticsEntity.setAttendanceNumber(attendanceCode);
		statisticsEntity.setIsQQ(entity.getIsQQ());  //查询是否全勤
		statisticsEntity.setAttendanceDays(Long.valueOf(getMonthDays(entity.getAttendanceDate())));
		List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService
				.getStatementSpecialList(statisticsEntity);
		return attendanceStatisticsList;
	}
	
	//封装晚夜班数据
	private List<Map<String, Object>> packagingSpecialDay(HrmsAttendanceRecord entity,Page page,List<HrmsAttendanceStatistics> attendanceStatisticsList) {
		List<Map<String, Object>> result = Lists.newArrayList();
		List<HrmsAttendanceRecord> list = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		if (CollectionUtils.isNotEmpty(list)) {
			if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					map.put("attendanceDate", record.getAttendanceDate());
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						BigDecimal count = new BigDecimal(0);
						for (HrmsAttendanceStatistics o : statistics) {
							map.put(o.getSalaryItemCode(), o.getAttendanceDays());
							if(o.getAttendanceDays() > 0) {
								BigDecimal day = new BigDecimal(o.getAttendanceDays());
								BigDecimal price = o.getAttendanceItemAmount();
								BigDecimal _val = price.multiply(day);
								map.put(o.getSalaryItemCode()+"_jbf", _val);
								count = count.add(_val);
							}
						}
						map.put("jbf_count", count);
						result.add(map);
					}
				});
			}
		}
		return result;
	}
	
	@Override
	public List<JdGridTableEntity> getTableHeadColsBySpecial() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		 result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,false, false, false, "",true)); // 考勤日期
		result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100, false, true, false, "", true)); // 员工ID
		result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100, false, false, false, "", true)); // 员工工号
		result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100, false, false, false, "", true)); // 员工姓名
		result.add(new JdGridTableEntity("科ID", "deptId", "", 100, false, true, false, "", true)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", true)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				JdGridTableEntity entity_jbf = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName()+"_个数");
				entity_jbf.setLabel(item.getSalaryItemName()+"_合计");
				
				entity.setName(item.getSalaryItemCode());
				entity_jbf.setName(item.getSalaryItemCode()+"_jbf");
				
				entity.setWidth(100);
				entity_jbf.setWidth(100);

				entity.setHidden(false);
				entity_jbf.setSortable(false);
				
				entity.setEditable(false);
				entity_jbf.setEditable(false);
				
				entity.setIndex(item.getSalaryItemId());
				entity_jbf.setIndex(item.getSalaryItemId()+"_jbf");
				
				result.add(entity);
				result.add(entity_jbf);
			}
		}
		result.add(new JdGridTableEntity("加班费合计", "jbf_count", "", 100, false, false, false, "", false)); // 机构名称
		return result;
	}
	
	//导出全勤/请假统计统计
	@Override
	public void exportStatementAllList(Page page, HrmsAttendanceRecord entity, HttpServletResponse response) {
		
		String filename = entity.getTitle();
		
		//表头
		List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
		
		//导出整理后的数据
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		List<String> dateSection = new ArrayList<>();
		if(!StringUtil.isEmpty(entity.getAttendanceDate())) {
			String[] split = entity.getAttendanceDate().split(" ~ ");
			dateSection = DateUtils.getDateSection(split[0], split[1]);
		}else { //查询当前年月
			dateSection.add(DateUtils.getPresentTimeStr().substring(0, 7));
		}
		List<Map<String, Object>> packagingAllDay = new ArrayList<>();
		List<HrmsAttendanceStatistics> staLit  = new ArrayList<>();
		dateSection.forEach( item -> {   //根据年查询数据
			entity.setAttendanceDate(item);
			List<HrmsAttendanceStatistics>  attendanceStatisticsList = getStatementAll(entity);
			staLit.addAll(attendanceStatisticsList);
			List<Map<String, Object>> resultList = packagingAllDay(entity,page,attendanceStatisticsList);
			packagingAllDay.addAll(resultList);
		});
		
		
		//绑定数据
		List<Map<String, Object>> dataMap = sort(packagingAllDay);
		
		//设置导出表头
		Map<String, String> titleMap = setStatementAllCol(staLit, colList);
		colList.forEach(item ->{item.setHeight(7);});  //设置表格高度
		Integer index =1;
		for(Map<String,Object> map : dataMap) {
			
			Map<String, Object> valMap = new HashMap<String, Object>();
			
			for(String key : titleMap.keySet()) {
				
				valMap.put(key, map.get(key));
			}
			valMap.put("no", index);
			list.add(valMap);
			index++;
		}
		
		try {
            Workbook workbook = ExcelExportUtils
                    .exportExcel(new ExportParams(filename, "数据", ExcelType.XSSF), colList, list);

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();

        } catch (FileNotFoundException e) {
        	log.error("导出数据异常"+e.getMessage());
        } catch (IOException e) {
        	log.error("导出数据异常"+e.getMessage());
        }
		
	}
	

	

	
	
	/**  
	 * <p> @Title: setStatementAllCol</p>
	 * <p> @Description: 设置全勤请假统计表头</p>
	 * <p> @Param: </p>
	 * <p> @Return: Map<String,String></p>
	 * <P> @Date: 2020年8月4日  下午4:46:01 </p>
	 * <p> <AUTHOR>
	 */  
	private Map<String, String> setStatementAllCol(List<HrmsAttendanceStatistics> attendanceStatisticsList,List<ExcelExportEntity> colList) {

		// 获取所有表头
		Map<String, String> titleMap = new LinkedHashMap<>();

		for (HrmsAttendanceStatistics record : attendanceStatisticsList) {

			titleMap.put(record.getSalaryItemCode(), record.getAttendanceItemName());
		}
		colList.add(new ExcelExportEntity("序号", "no",8));
		colList.add(new ExcelExportEntity("考勤日期", "attendanceDate"));
		ExcelExportEntity colEntity = new ExcelExportEntity("员工工号", "employeeNo");
		colList.add(colEntity);
        colList.add(new ExcelExportEntity("员工姓名", "employeeName"));
        colList.add(new ExcelExportEntity("科室名称", "deptName"));
		if (!titleMap.isEmpty()) {
			for (String titel : titleMap.keySet()) {
				if(!StringUtil.isEmpty(titel)) {
					colEntity = new ExcelExportEntity(titleMap.get(titel), titel);
					colEntity.setWidth(8);
					colEntity.setStatistics(true);
					colList.add(colEntity);
				}
		
			}
		}
		titleMap.put("no", "序号");
		titleMap.put("attendanceDate", "考勤日期");
		titleMap.put("employeeNo", "员工工号");
		titleMap.put("employeeName", "员工姓名");
		titleMap.put("deptName", "科室名称");
		return titleMap;
	}
	
	
	
	//特殊出勤导出
	@Override
	public void exportStatementSpecialList(Page page, HrmsAttendanceRecord entity, HttpServletResponse response) {
		
		String filename = entity.getTitle();
		//表头
		List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
		
		//导出整理后的数据
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		
		List<String> dateSection = new ArrayList<>();
		if(!StringUtil.isEmpty(entity.getAttendanceDate())) {
			String[] split = entity.getAttendanceDate().split(" ~ ");
			dateSection = DateUtils.getDateSection(split[0], split[1]);
		}else { //查询当前年月
			dateSection.add(DateUtils.getPresentTimeStr().substring(0, 7));
		}
		List<Map<String, Object>> packagingAllDay = new ArrayList<>();
		List<HrmsAttendanceStatistics> staLit  = new ArrayList<>();
		dateSection.forEach( item -> {   //根据年查询数据
			entity.setAttendanceDate(item);
			List<HrmsAttendanceStatistics>  attendanceStatisticsList = getStatementSpecial(entity);
			staLit.addAll(attendanceStatisticsList);
			List<Map<String, Object>> resultList = packagingSpecialDay(entity,page,attendanceStatisticsList);
			packagingAllDay.addAll(resultList);
		});
		
		
		//设置导出表头
		Map<String, String> titleMap = setStatementSpecialCol(staLit, colList);
		colList.forEach(item ->{item.setHeight(7);}); 
	
		List<Map<String, Object>> dataMap = sort(packagingAllDay);	//绑定数据sort
		Integer index =1;
		for(Map<String,Object> map : dataMap) {
			
			Map<String, Object> valMap = new HashMap<String, Object>();
			
			for(String key : titleMap.keySet()) {
				
				valMap.put(key, map.get(key));
			}
			valMap.put("no", index);
			list.add(valMap);
			index++;
		}
		
		try {
            Workbook workbook = ExcelExportUtils
                    .exportExcel(new ExportParams(filename, "数据", ExcelType.XSSF), colList, list);

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();

        } catch (FileNotFoundException e) {
        	log.error("导出数据异常"+e.getMessage());
        } catch (IOException e) {
        	log.error("导出数据异常"+e.getMessage());
        }
		
		
	}
	
	private Map<String, String> setStatementSpecialCol(List<HrmsAttendanceStatistics> attendanceStatisticsList,List<ExcelExportEntity> colList) {

		// 获取所有表头
		Map<String, String> titleMap = new LinkedHashMap<>();
		for (HrmsAttendanceStatistics record : attendanceStatisticsList) {
			titleMap.put(record.getSalaryItemCode(), record.getAttendanceItemName()+"_个数");
			titleMap.put(record.getSalaryItemCode()+"_jbf", record.getAttendanceItemName()+"_合计");
		}
		titleMap.put("jbf_count", "加班费合计");
		colList.add(new ExcelExportEntity("序号", "no",8));
		colList.add(new ExcelExportEntity("考勤日期", "attendanceDate"));
		ExcelExportEntity colEntity = new ExcelExportEntity("员工工号", "employeeNo");
        colList.add(colEntity);
        colList.add(new ExcelExportEntity("员工姓名", "employeeName"));
        colList.add(new ExcelExportEntity("科室名称", "deptName"));
		if (!titleMap.isEmpty()) {
			for (String titel : titleMap.keySet()) {
				colEntity = new ExcelExportEntity(titleMap.get(titel), titel);
				colEntity.setStatistics(true);
//				colEntity.setType(type);
				colEntity.setWidth(10);
				colList.add(colEntity);
			}
		}
		titleMap.put("no", "序号");
		titleMap.put("attendanceDate", "考勤日期");
		titleMap.put("employeeNo", "员工工号");
		titleMap.put("employeeName", "员工姓名");
		titleMap.put("deptName", "科室名称");
		return titleMap;
	}
	
	//根据时间获取月份天数
	private int getMonthDays(String date) {
		if(!StringUtil.isEmpty(date)) {
			String[] _split = date.split("-");
			int year = Integer.valueOf(_split[0]);
			int month = Integer.valueOf(_split[1]);
			Calendar c = Calendar.getInstance();
			c.set(year, month, 0); // 输入类型为int类型
			return  c.get(Calendar.DAY_OF_MONTH); // 得到月份天数
		}
		return 0;
	}

	//人事考勤带表头 
	@Override
	public List<JdGridTableEntity> getTableHeadColsByPerson() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("考勤日期", "attendanceDate", "", 100,false, false, false, "",true)); // 考勤日期
		result.add(new JdGridTableEntity("员工ID", "employeeId", "", 100, false, true, false, "", true)); // 员工ID
		result.add(new JdGridTableEntity("员工工号", "employeeNo", "", 100, false, false, false, "", true)); // 员工工号
		result.add(new JdGridTableEntity("员工姓名", "employeeName", "", 100, false, false, false, "", true)); // 员工姓名
		result.add(new JdGridTableEntity("科室ID", "deptId", "", 100, false, true, false, "", true)); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "deptName", "", 100, false, false, false, "", true)); // 机构名称

		HrmsSalaryItem salaryItemEntity = new HrmsSalaryItem();
		salaryItemEntity.setDataCategory(CommonContants.DATA_CATEGORY_ATTENDANCE);
		List<HrmsSalaryItem> attendanceItemList = hrmsSalaryItemService.getList(salaryItemEntity);
		if (CollectionUtils.isNotEmpty(attendanceItemList)) {
			for (HrmsSalaryItem item : attendanceItemList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(item.getSalaryItemName());
				entity.setName(item.getSalaryItemCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				if (SalaryItemTypeEnum.ITEM_TYPE_3.getKey().equals(item.getSalaryItemType())) {
					entity.setEditable(true);
				} else {
					entity.setEditable(false);
				}
				entity.setIndex(item.getSalaryItemId());
				result.add(entity);
			}
		}
		return result;
	}

	@Override
	public List<HrmsAttendanceRecord> getUnsettledDataList(Page page, HrmsAttendanceRecord entity) {
		return hrmsAttendanceRecordMapper.getUnsettledDataList(page,entity);
	}

	@Override
	public void getclerk(Map<String, Object> map) {
		// TODO Auto-generated method stub
		
	}
	
	//全勤详情导出
	@Override
	public void exportAttendance(Page page, HrmsAttendanceRecord entity, HttpServletResponse response) {
		
		String filename = entity.getTitle();
		
		//表头
		List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
		
		//导出整理后的数据
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		// 查询所有数据
		List<HrmsAttendanceRecord> recordList = hrmsAttendanceRecordMapper.getStatisticsListByDeptList(page, entity);
		HrmsAttendanceStatistics statisticsEntity = new HrmsAttendanceStatistics();
		statisticsEntity.setAttendanceDate(entity.getAttendanceDate());
		statisticsEntity.setIsQQ(entity.getIsQQ()); //
		statisticsEntity.setAttendanceNumber(attendanceCode);
		if("1".equals(entity.getIsQQ())) {
			statisticsEntity.setOvertime(entity.getIsQQ());
		}
		
		List<HrmsAttendanceStatistics> attendanceStatisticsList = hrmsAttendanceStatisticsService.getEmployeeStatisticsList(statisticsEntity);
		List<HrmsAttendanceStatistics> newAttendanceStatisticsList = new ArrayList<>();
		
		attendanceStatisticsList.forEach(item ->{
			recordList.forEach(s ->{
				if(!StringUtil.isEmpty(s.getEmployeeId()) && s.getEmployeeId().equals(item.getEmployeeId())) {
					newAttendanceStatisticsList.add(item);
				}
			});
			
		});
		
		//过滤掉空的值
//		List<HrmsAttendanceStatistics> newCollect = attendanceStatisticsList.stream().filter(s -> s.getEmployeeId()).collect(Collectors.toList());
		
		//设置导出表头
		Map<String, String> titleMap = setStatementAllCol(newAttendanceStatisticsList, colList);
		colList.forEach(item ->{item.setHeight(7);});  //设置表格高度
		//绑定数据
		List<Map<String, Object>> dataMap = bindingData(recordList,newAttendanceStatisticsList,entity.getAttendanceDate());  //数据绑定
		
		for(Map<String,Object> map : dataMap) {
			
			Map<String, Object> valMap = new HashMap<String, Object>();
			
			for(String key : titleMap.keySet()) {
				
				valMap.put(key, map.get(key));
			}
			list.add(valMap);
		}
		
		try {
            Workbook workbook = ExcelExportUtils
                    .exportExcel(new ExportParams(filename, "数据", ExcelType.XSSF), colList, sortDept(list));

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename="
                    + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");

            OutputStream fos = response.getOutputStream();
            workbook.write(fos);
            fos.close();

        } catch (Exception e) {
            log.error("数据导出异常："+e,e.getMessage());
        }
		
	}
	
	private List<Map<String, Object>>  bindingData(List<HrmsAttendanceRecord> list,List<HrmsAttendanceStatistics> attendanceStatisticsList,String attendanceDate) {
		List<Map<String, Object>> result = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(list)) {
			// 查询员工考勤统计记录
			if (CollectionUtils.isNotEmpty(attendanceStatisticsList)) {
				// 将员工考勤记录根据员工ID转换成Map
				Map<String, List<HrmsAttendanceStatistics>> attendanceStatisticsMap = attendanceStatisticsList.stream()
						.collect(Collectors.groupingBy(HrmsAttendanceStatistics::getEmployeeId));
				list.stream().forEach(record -> {
					Map<String, Object> map = Maps.newHashMap();
					 map.put("attendanceDate", attendanceDate);
					map.put("employeeId", record.getEmployeeId());
					map.put("employeeNo", record.getEmployeeNo());
					map.put("employeeName", record.getEmployeeName());
					map.put("orgId", record.getOrgId());
					map.put("deptName", record.getOrgName());

					// 取出当前员工考勤统计记录
					List<HrmsAttendanceStatistics> statistics = attendanceStatisticsMap.get(record.getEmployeeId());
					if (CollectionUtils.isNotEmpty(statistics)) {
						for (HrmsAttendanceStatistics o : statistics) {
							if(!StringUtil.isEmpty(o.getSalaryItemCode())) {
								map.put(o.getSalaryItemCode(), o.getAttendanceDays());
							}
						}
						result.add(map);
					}
					
				});
			}
		}
		return result;
	}

	@Override
	public List<HrmsAttendanceRecord> selectDidNotReport(String date) {
		// TODO Auto-generated method stub
		return hrmsAttendanceRecordMapper.selectDidNotReport(date);
	}

	@Override
	@Transactional
	public void deleteAll(String employeeId, String date) throws Exception {
		//判断没有审核才能删除
		HrmsAttendanceRecord record = new HrmsAttendanceRecord();
		record.setAttendanceDate(date);
		record.setEmployeeId(employeeId);
		HrmsAttendanceRecord selectOne = hrmsAttendanceRecordMapper.selectOne(record);
		if(selectOne != null &&  "2".equals(selectOne.getApprovalStatus())) {
			//已审核不能删除了
			throw new RuntimeException("已审核，不能清空");
		}else {
			 Map<String,Object> map = new HashMap<>();
			 List<String> mlist = Arrays.asList(employeeId.split(","));
			 map.put("list", mlist);
			 map.put("date", date);
			hrmsAttendanceDetailService.batchDeleteByDateAndEmpIds(map);	// 删除明细
			hrmsAttendanceRecordMapper.delete(record);	// 删除审核记录
			hrmsAttendanceStatisticsService.deleteByParam(map);	// 删除统计
		}
	}

	@Override
	public int checkDeptAudit(Map<String, Object> map) {
		return hrmsAttendanceRecordMapper.checkDeptAudit(map);
	}

}
