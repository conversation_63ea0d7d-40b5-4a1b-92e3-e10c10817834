package cn.trasen.hrms.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.hrms.dao.HrmsResearchCostMapper;
import cn.trasen.hrms.dao.HrmsResearchProgressMapper;
import cn.trasen.hrms.dao.HrmsResearchTopicMapper;
import cn.trasen.hrms.dao.HrmsResearchTopicUrgeMapper;
import cn.trasen.hrms.model.HrmsResearchCost;
import cn.trasen.hrms.model.HrmsResearchProgress;
import cn.trasen.hrms.model.HrmsResearchTopic;
import cn.trasen.hrms.model.HrmsResearchTopicUrge;
import cn.trasen.hrms.service.HrmsResearchTopicService;
import cn.trasen.hrms.service.HrmsResearchTopicUrgeService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.ListUtils;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsResearchTopicServiceImpl
 * @Description TODO
 * @date 2021��11��6�� ����3:43:08
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsResearchTopicServiceImpl implements HrmsResearchTopicService {

	@Autowired
	private HrmsResearchTopicMapper mapper;
	
	@Autowired
	private HrmsResearchProgressMapper hrmsResearchProgressMapper;
	
	@Autowired
	private HrmsResearchCostMapper hrmsResearchCostMapper;
	
	@Autowired
	private InformationFeignService informationFeignService;

	@Autowired
	private HrmsResearchTopicUrgeService HrmsResearchTopicUrgeService;
	
	@Autowired
	private HrmsResearchTopicUrgeMapper hrmsResearchTopicUrgeMapper;
	
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsResearchTopic record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		record.setStatus(1);
		record.setTotalPriceUse(new BigDecimal(0));
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());

		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsResearchTopic record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		HrmsResearchTopic hrmsResearchTopic = mapper.selectByPrimaryKey(record.getId());
		if(null == record.getStatus() || (null != record.getStatus() && 2 != record.getStatus())) {
			
			StringBuffer sb = new StringBuffer();
			if(StringUtils.isNoneBlank(record.getProjectSource()) && !hrmsResearchTopic.getProjectSource().equals(record.getProjectSource())) {
				sb.append("项目来源由“").append(hrmsResearchTopic.getProjectSource()).append("”");
				sb.append("变更为“").append(record.getProjectSource()).append("”；");
			}
			if(StringUtils.isNoneBlank(record.getProjectNumber()) && !hrmsResearchTopic.getProjectNumber().equals(record.getProjectNumber())) {
				sb.append("项目编号由“").append(hrmsResearchTopic.getProjectNumber()).append("”");
				sb.append("变更为“").append(record.getProjectNumber()).append("”；");
			}
			if(StringUtils.isNoneBlank(record.getTopicName()) && !hrmsResearchTopic.getTopicName().equals(record.getTopicName())) {
				sb.append("课题名称由“").append(hrmsResearchTopic.getTopicName()).append("”");
				sb.append("变更为“").append(record.getTopicName()).append("”；");
			}
			if(null != record.getStartTime() && !hrmsResearchTopic.getStartTime().equals(record.getStartTime())) {
				sb.append("开始时间由“").append(DateUtil.format(hrmsResearchTopic.getStartTime(), "yyyy-MM-dd")).append("”");
				sb.append("变更为“").append(DateUtil.format(record.getStartTime(),"yyyy-MM-dd")).append("”；");
			}
			if(null != record.getEndTime() && !hrmsResearchTopic.getEndTime().equals(record.getEndTime())) {
				sb.append("结束时间由“").append(DateUtil.format(hrmsResearchTopic.getEndTime(),"yyyy-MM-dd")).append("”");
				sb.append("变更为“").append(DateUtil.format(record.getEndTime(),"yyyy-MM-dd")).append("”；");
			}
			if(null != record.getHandlerUser() && !hrmsResearchTopic.getHandlerUser().equals(record.getHandlerUser())) {
				sb.append("负责人由“").append(hrmsResearchTopic.getHandlerUserName()).append("”");
				sb.append("变更为“").append(record.getHandlerUserName()).append("”；");
			}
			if(null != record.getParticipateUser() && !hrmsResearchTopic.getParticipateUser().equals(record.getParticipateUser())) {
				List<String> oldParticipateUserNameArr = new ArrayList<>();
				if(StringUtils.isNotBlank(hrmsResearchTopic.getParticipateUserName())) {
					oldParticipateUserNameArr = Arrays.asList(hrmsResearchTopic.getParticipateUserName().split(","));
				}
				List<String> participateUserNameArr = new ArrayList<>();
				if(StringUtils.isNotBlank(record.getParticipateUserName())) {
					participateUserNameArr = Arrays.asList(record.getParticipateUserName().split(","));
				}
		        List<String> aList= ListUtils.getAddaListThanbList(participateUserNameArr,oldParticipateUserNameArr);
		        if(aList != null && aList.size() > 0) {
		        	 sb.append("参与人员新增：");
		        }
		        for (String string : aList) {
		        	 sb.append(string).append(",");
				}
		        sb.append("；");
		        List<String> bList= ListUtils.getReduceaListThanbList(participateUserNameArr,oldParticipateUserNameArr);
		        if(bList != null && bList.size() > 0) {
		       	 	sb.append("参与人员去除：");
		        }
		        for (String string : bList) {
		        	sb.append(string).append(",");
				}
		        sb.append("；");
			}
			if(null != record.getGrants() && !hrmsResearchTopic.getGrants().equals(record.getGrants().setScale(2, RoundingMode.HALF_UP))) {
				sb.append("上级资助金额由“").append(hrmsResearchTopic.getGrants()).append("”");
				sb.append("变更为“").append(record.getGrants()).append("”；");
			}
			if(null != record.getAmount() && !hrmsResearchTopic.getAmount().equals(record.getAmount().setScale(2, RoundingMode.HALF_UP))) {
				sb.append("医院配套金额由“").append(hrmsResearchTopic.getAmount()).append("”");
				sb.append("变更为“").append(record.getAmount().setScale(2, RoundingMode.HALF_UP)).append("”；");
			}
			
			if(sb.length() > 0) {
				sb = sb.deleteCharAt(sb.length() - 1);
				HrmsResearchProgress hrmsResearchProgress = new HrmsResearchProgress();
				hrmsResearchProgress.setId(IdGeneraterUtils.nextId());
				hrmsResearchProgress.setTopicId(record.getId());
				hrmsResearchProgress.setCreateDate(new Date());
				hrmsResearchProgress.setUpdateDate(new Date());
				hrmsResearchProgress.setIsDeleted("N");
				hrmsResearchProgress.setProgressType("2");
				if (user != null) {
					hrmsResearchProgress.setCreateUser(user.getUsercode());
					hrmsResearchProgress.setCreateUserName(user.getUsername());
					hrmsResearchProgress.setUpdateUser(user.getUsercode());
					hrmsResearchProgress.setUpdateUserName(user.getUsername());
				}
				
				hrmsResearchProgress.setChangeRemark(sb.toString());
				hrmsResearchProgress.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsResearchProgressMapper.insertSelective(hrmsResearchProgress);
			}
			
			StringBuffer content = new StringBuffer();
			content.append("您负责的科研课题“").append(hrmsResearchTopic.getTopicName()).append("”由管理员变更，请您知悉！");
			content.append("；变更内容：").append(sb);
			
			NoticeReq notice = NoticeReq.builder()
					.content(content.toString())
					.noticeType("1")
					.receiver(hrmsResearchTopic.getHandlerUser())
					.sender("admin")
					.senderName("系统管理员")
					.subject("课题变更提醒")
					.toUrl("/research/topic").source("科研课题")
					.build();
			informationFeignService.sendNotice(notice);
		}
		
		//结题提醒
		if(null != record.getStatus() && 2 == record.getStatus()) {
			StringBuffer sb = new StringBuffer();
			sb.append("您负责的科研课题“").append(hrmsResearchTopic.getTopicName()).append("”已结题，请您知悉！");
			
			NoticeReq notice = NoticeReq.builder()
					.content(sb.toString())
					.noticeType("1")
					.receiver(hrmsResearchTopic.getHandlerUser())
					.sender("admin")
					.senderName("系统管理员")
					.subject("结题提醒")
					.toUrl("/research/topic").source("科研课题")
					.build();
			informationFeignService.sendNotice(notice);
		}
		
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public void deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		HrmsResearchTopic hrmsResearchTopic = mapper.selectByPrimaryKey(id);
		StringBuffer content = new StringBuffer();
		content.append("您负责的科研课题“").append(hrmsResearchTopic.getTopicName()).append("”已被管理员删除，如有疑问，请联系课题管理员！");
		
		NoticeReq notice = NoticeReq.builder()
				.content(content.toString())
				.noticeType("1")
				.receiver(hrmsResearchTopic.getHandlerUser())
				.sender("admin")
				.senderName("系统管理员")
				.subject("课题删除提醒")
				.toUrl("/research/topic").source("科研课题")
				.build();
		informationFeignService.sendNotice(notice);
		
		HrmsResearchTopic record = new HrmsResearchTopic();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		mapper.updateByPrimaryKeySelective(record);
		
		HrmsResearchProgress progress = new HrmsResearchProgress();
		progress.setTopicId(id);
		progress.setIsDeleted("Y");
		progress.setUpdateDate(new Date());
		progress.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		progress.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		hrmsResearchProgressMapper.updateByTopicKeySelective(progress);
		
		HrmsResearchCost cost = new HrmsResearchCost();
		cost.setTopicId(id);
		cost.setIsDeleted("Y");
		cost.setUpdateDate(new Date());
		cost.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		cost.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		hrmsResearchCostMapper.updateByTopicKeySelective(cost);
		
	}

	@Override
	public HrmsResearchTopic selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsResearchTopic> getDataSetList(Page page, HrmsResearchTopic record) {
		Boolean right = UserInfoHolder.getRight("TOPIC_MASTER");
		Example example = new Example(HrmsResearchTopic.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		//根据当前登录账号机构编码过滤查询数据
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if(null != record.getDataRefTable()) {
			criteria.andEqualTo("status", record.getDataRefTable());
		}else {
			criteria.andEqualTo("status",1);
		}
		if(!right) {
			criteria.andEqualTo("handlerUser", UserInfoHolder.getCurrentUserCode());
		}
		if(StringUtils.isNotBlank(record.getTopicName())) {
			criteria.andLike("topicName", "%" + record.getTopicName() + "%");
		}
		if(StringUtils.isNotBlank(record.getHandlerUserName())) {
			criteria.andLike("handlerUserName", "%" + record.getHandlerUserName() + "%");
		}
		
		List<HrmsResearchTopic> records = mapper.selectByExampleAndRowBounds(example, page);
		
		for (HrmsResearchTopic hrmsResearchTopic : records) {
			if(right) {
				hrmsResearchTopic.setIsResearchAdmin("1");
			}
			//计算时间进度  
			int total = 0;
			if(hrmsResearchTopic.getEndTime().compareTo(hrmsResearchTopic.getStartTime()) == 0) {
				total = 1;
			}else {
				total = DateUtils.getDifferDays(hrmsResearchTopic.getEndTime(),hrmsResearchTopic.getStartTime());	
			}
		    int past = DateUtils.getDifferDays(new Date(),hrmsResearchTopic.getStartTime());
		    double percent = (past/(double)total) * 100;
		    if(percent <= 0) {
		    	percent = Math.abs(percent);
		    }
			hrmsResearchTopic.setTimeProgress(String.format("%.0f", percent));
		   
			Integer urgeNumber =  hrmsResearchTopicUrgeMapper.selectByTopicId(hrmsResearchTopic.getId());
			hrmsResearchTopic.setUrgeNumber(urgeNumber);
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public HrmsResearchTopic selectResearchDetails(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsResearchTopic hrmsResearchTopic = mapper.selectByPrimaryKey(id);
		List<HrmsResearchProgress> progressList = hrmsResearchProgressMapper.selectByTopicId(id);
		hrmsResearchTopic.setProgressList(progressList);
		List<HrmsResearchCost> costList = hrmsResearchCostMapper.selectByTopicId(id);
		hrmsResearchTopic.setCostList(costList);
		return hrmsResearchTopic;
	}

	@Override
	public List<HrmsResearchTopic> selectRemindHrmsResearchTopicList() {
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return mapper.selectRemindHrmsResearchTopicList(ssoOrgCode);
	}

	@Override
	public Integer updateDateBYid(String id) {
		// TODO Auto-generated method stub
		return mapper.updateDateBYid(id);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer UrgeProcessing(HrmsResearchTopic record) {
		// TODO Auto-generated method stub
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		StringBuffer content = new StringBuffer();
		Integer urgeNumber =  hrmsResearchTopicUrgeMapper.selectByTopicId(record.getId());
		content.append(user.getUsername()+"催办了“").append(record.getTopicName()).append("”，请尽快处理！");
		content.append("目前该课题催办"+(urgeNumber+1)+"次");
		content.append("；催办内容：").append(record.getUrgeProcessingContent());
		//content.append("<br>消息时间："+new Date());
		NoticeReq notice = NoticeReq.builder()
				.content(content.toString())
				.noticeType("1")
				.receiver(record.getHandlerUser())
				.sender(user.getUsercode())
				.senderName(user.getUsername())
				.subject("课题催办提醒")
				.toUrl("/research/topic").source("科研课题")
				.build();
		informationFeignService.sendNotice(notice);
		
		HrmsResearchTopicUrge hrmsResearchTopicUrge = new HrmsResearchTopicUrge();
		hrmsResearchTopicUrge.setUrgeProcessingContent(record.getUrgeProcessingContent());
		hrmsResearchTopicUrge.setTopicId(record.getId());
		return HrmsResearchTopicUrgeService.save(hrmsResearchTopicUrge);
	}
}
