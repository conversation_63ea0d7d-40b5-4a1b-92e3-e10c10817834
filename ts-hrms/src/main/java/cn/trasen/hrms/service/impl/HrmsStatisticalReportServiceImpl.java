package cn.trasen.hrms.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.bean.base.JobtitleBasicReq;
import cn.trasen.homs.bean.base.JobtitleBasicResp;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsJobtitleBasicFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.bean.EmployeeJobtitleListResp;
import cn.trasen.hrms.bean.GetEmployeeAnalysisReportTableDataResult;
import cn.trasen.hrms.bean.GetEmployeeDistributionReportTableDataResult;
import cn.trasen.hrms.common.EnumInterfaceData;
import cn.trasen.hrms.common.JdGridTableEntity;
import cn.trasen.hrms.common.KeyValue;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsStatisticalReportMapper;
import cn.trasen.hrms.model.HrmsDictInfo;
import cn.trasen.hrms.model.HrmsStatisticalReportEntity;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsJobtitleInfoService;
import cn.trasen.hrms.service.HrmsStatisticalReportService;


/**
 * @Title: HrmsStatisticalReportServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 统计报表 业务层接口实现类
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年5月21日 上午9:11:55
 * @version V1.0
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsStatisticalReportServiceImpl implements HrmsStatisticalReportService {

	@Autowired
	HrmsStatisticalReportMapper hrmsStatisticalReportMapper;

	@Autowired
	HrmsDictInfoService hrmsDictInfoService;

	@Autowired
	HrmsJobtitleBasicFeignService hrmsJobtitleBasicService;

	@Autowired
	HrmsOrganizationFeignService hrmsOrganizationService;


	@Autowired
	HrmsJobtitleInfoService hrmsJobtitleInfoService;

	@Autowired
	HrmsOrganizationFeignService hrmsOrganizationFeignService;

	@Value("${ygtwqtsydwCode:}")
	private String ygtwqtsydwCode;  //医共体外部事业单位

	/**
	 * @Title: getGenderReportTableHead
	 * @Description: 获取性别统计报表列表表头
	 * @Return List<JdGridTableEntity>
	 * <AUTHOR>
	 * @date 2020年6月1日 下午1:52:13
	 */
	@Override
	public List<JdGridTableEntity> getGenderReportTableHead() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("科室ID", "orgId", "", 100, false, true, false, "")); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "orgName", "", 100, false, false, false, "")); // 机构名称

		List<KeyValue> genderEnumList = EnumInterfaceData.getGenderTypeList();
		if (CollectionUtils.isNotEmpty(genderEnumList)) {
			for (KeyValue o : genderEnumList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(o.getText());
				entity.setName(o.getCode());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}
		return result;
	}

	/**
	 * @Title: getGenderReportTableData
	 * @Description: 获取性别统计报表列表数据
	 * @Return List<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2020年6月1日 下午1:56:34
	 */
	@Override
	public List<Map<String, Object>> getGenderReportTableData(HrmsStatisticalReportEntity entity) {

		entity.setYgtwqtsydwCode(ygtwqtsydwCode);
		if (CollectionUtils.isEmpty(entity.getGenderList())) {
			List<String> typeList = Lists.newArrayList();
			for (KeyValue kv : EnumInterfaceData.getGenderTypeList()) {
				typeList.add(kv.getCode());
			}
			entity.setGenderList(typeList);
		}

		// System.out.println(entity.getOrgIdList().size()+"------------");

		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsStatisticalReportMapper.getGenderReportTableData(entity);
	}

	@Override
	public List<Map<String, Object>> getGenderReportTableDataAll() {

		List<String> orgIdList = new ArrayList<String>();
		if (UserInfoHolder.ISADMIN()) {
			orgIdList = new ArrayList<String>();
		} else {
			for (String s : UserInfoHolder.getOrgRang().replace("'", "").replace("(", "").replace(")", "").split(",")) {
				if (StringUtils.isBlank(s) == false && (!s.equals("ZZSFYBJY"))) {
					orgIdList.add(s);
				}
			}
		}

		List<String> orgIdNewList = new ArrayList<String>();
		if (orgIdList.size() > 0) {
			List<HrmsOrganizationResp> hrmsOrganizations = hrmsOrganizationService
					.getHrmsOrganizationBeanAndNextList(orgIdList).getObject();

			for (HrmsOrganizationResp hrmsOrganization : hrmsOrganizations) {
				orgIdNewList.add(hrmsOrganization.getOrganizationId());
			}
			if (orgIdNewList.size() < 1) {
				orgIdNewList.add("0");
			}
		}

		orgIdNewList = orgIdNewList.stream().distinct().collect(Collectors.toList());

		// TODO Auto-generated method stub
		HrmsStatisticalReportEntity entity = new HrmsStatisticalReportEntity();
		entity.setOrgIdList(orgIdNewList);
		List<String> typeList = Lists.newArrayList();
		for (KeyValue kv : EnumInterfaceData.getGenderTypeList()) {
			typeList.add(kv.getCode());
		}
		entity.setGenderList(typeList);
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsStatisticalReportMapper.getGenderReportTableDataAll(entity);
	}

	/**
	 * @param entity
	 * @Title: getAgeReportTableData
	 * @Description: 获取年龄段统计报表列表数据
	 * @Return List<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午9:53:04
	 */
	@Override
	public List<Map<String, Object>> getAgeReportTableData(HrmsStatisticalReportEntity entity) {
		entity.setYgtwqtsydwCode(ygtwqtsydwCode);
		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return hrmsStatisticalReportMapper.getAgeReportTableData(entity);
	}

	@Override
	public List<Map<String, Object>> getAgeReportTableDataAll() {

		List<String> orgIdList = new ArrayList<String>();
		if (UserInfoHolder.ISADMIN()) {
			orgIdList = new ArrayList<String>();
		} else {
			for (String s : UserInfoHolder.getOrgRang().replace("'", "").replace("(", "").replace(")", "").split(",")) {
				if (StringUtils.isBlank(s) == false && (!s.equals("ZZSFYBJY"))) {
					orgIdList.add(s);
				}
			}
		}

		List<String> orgIdNewList = new ArrayList<String>();
		if (orgIdList.size() > 0) {
			List<HrmsOrganizationResp> hrmsOrganizations = hrmsOrganizationService
					.getHrmsOrganizationBeanAndNextList(orgIdList).getObject();

			for (HrmsOrganizationResp hrmsOrganization : hrmsOrganizations) {
				orgIdNewList.add(hrmsOrganization.getOrganizationId());
			}
			if (orgIdNewList.size() < 1) {
				orgIdNewList.add("0");
			}
		}

		orgIdNewList = orgIdNewList.stream().distinct().collect(Collectors.toList());
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsStatisticalReportMapper.getAgeReportTableDataAll(orgIdNewList,ssoOrgCode);
	}

	/**
	 * @Title: getEducationReportTableHead
	 * @Description: 获取学历统计报表列表表头
	 * @Return List<JdGridTableEntity>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:20:30
	 */
	@Override
	public List<JdGridTableEntity> getEducationReportTableHead() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("科室ID", "orgId", "", 100, false, true, false, "")); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "orgName", "", 300, false, false, false, "")); // 机构名称

		List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.EDUCATION_TYPE);
		if (CollectionUtils.isNotEmpty(dictInfos)) {
			for (HrmsDictInfo o : dictInfos) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(o.getDictName());
				entity.setName(o.getDictValue());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}
		return result;
	}

	/**
	 * @Title: getEducationReportTableData
	 * @Description: 获取学历统计报表列表数据
	 * @Return List<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2020年5月21日 上午10:47:31
	 */
	@Override
	public List<Map<String, Object>> getEducationReportTableData(HrmsStatisticalReportEntity entity) {
		entity.setYgtwqtsydwCode(ygtwqtsydwCode);
		List<String> eduTypeList = entity.getEduTypeList();
		if (CollectionUtils.isEmpty(eduTypeList)) { // 当前端没有选择学历类型，默认统计所有类型
			List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.EDUCATION_TYPE);
			if (CollectionUtils.isNotEmpty(dictInfos)) {
				eduTypeList = Lists.newArrayList();
				for (HrmsDictInfo d : dictInfos) {
					eduTypeList.add(d.getDictValue());
				}
			}
		}

		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsStatisticalReportMapper.getEducationReportTableData(eduTypeList, entity.getOrgIdList(),ssoOrgCode);
	}

	/**
	 * @Title: getEmployeeCategoryReportTableHead
	 * @Description: 获取员工类别统计报表列表表头
	 * @Return List<JdGridTableEntity>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午4:16:50
	 */
	@Override
	public List<JdGridTableEntity> getEmployeeCategoryReportTableHead() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("科室ID", "orgId", "", 100, false, true, false, "")); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "orgName", "", 300, false, false, false, "")); // 机构名称

		List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.EMPLOYEE_CATEGORY);
		if (CollectionUtils.isNotEmpty(dictInfos)) {
			for (HrmsDictInfo o : dictInfos) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(o.getDictName());
				entity.setName(o.getDictValue());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}
		return result;
	}

	/**
	 * @param entity
	 * @Title: getEmployeeCategoryReportTableData
	 * @Description: 获取员工类别统计报表列表数据
	 * @Return List<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2020年5月22日 下午4:41:29
	 */
	@Override
	public List<Map<String, Object>> getEmployeeCategoryReportTableData(HrmsStatisticalReportEntity entity) {
		List<String> employeeCategoryList = Lists.newArrayList();
		List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.EMPLOYEE_CATEGORY);
		if (CollectionUtils.isNotEmpty(dictInfos)) {
			for (HrmsDictInfo d : dictInfos) {
				employeeCategoryList.add(d.getDictValue());
			}
		}

		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<Map<String, Object>> employeeCategoryReportTableData = hrmsStatisticalReportMapper.getEmployeeCategoryReportTableData(employeeCategoryList,
				entity.getOrgIdList(),ssoOrgCode);
//       if (employeeCategoryReportTableData.size()>0) {
//			Collections.sort(employeeCategoryReportTableData, new Comparator<Map<String, Object>>() {
//	            @Override
//	            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
//	                Integer id1 = (Integer) o1.get("ksfb_no");
//	                Integer id2 = (Integer) o2.get("ksfb_no");
//	                // 升序
//	                 return id1.compareTo(id2);
//	                // 降序
//	                 // return id2.compareTo(id1);
//	            }
//	        });
//       }

		return employeeCategoryReportTableData;
	}

	@Override
	public List<JdGridTableEntity> getJobTitleReportTableHead() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("科室ID", "orgId", "", 100, false, true, false, "")); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "orgName", "", 200, false, false, false, "")); // 机构名称

		JobtitleBasicReq basic = new JobtitleBasicReq();
		basic.setJobtitleBasicGrade(3);
		List<JobtitleBasicResp> basicList = hrmsJobtitleBasicService.getJobtitleBasicList(basic).getObject();
		if (CollectionUtils.isNotEmpty(basicList)) {
			for (JobtitleBasicResp o : basicList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(o.getJobtitleBasicName());
				entity.setName(o.getJobtitleBasicId());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> getJobTitleReportTableData(HrmsStatisticalReportEntity entity) {
		JobtitleBasicReq basic = new JobtitleBasicReq();
		basic.setJobtitleBasicGrade(3);
		List<JobtitleBasicResp> basicList = hrmsJobtitleBasicService.getJobtitleBasicList(basic).getObject();
		List<String> jobBasicList = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(basicList)) {
			for (JobtitleBasicResp b : basicList) {
				jobBasicList.add(b.getJobtitleBasicId());
			}
		}
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsStatisticalReportMapper.getJobTitleReportTableData(jobBasicList, entity.getOrgIdList(),ssoOrgCode);
	}

	@Override
	public List<JdGridTableEntity> getJobTitleLevelReportTableHead() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("科室ID", "orgId", "", 100, false, true, false, "")); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "orgName", "", 200, false, false, false, "")); // 机构名称

		JobtitleBasicReq basic = new JobtitleBasicReq();
		basic.setJobtitleBasicGrade(2);
		List<JobtitleBasicResp> basicList = hrmsJobtitleBasicService.getJobtitleBasicList(basic).getObject();
		if (CollectionUtils.isNotEmpty(basicList)) {
			for (JobtitleBasicResp o : basicList) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel("(" + o.getClassificationName() + ")" + o.getJobtitleBasicName());
				entity.setName(o.getJobtitleBasicId());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> getJobTitleLevelReportTableData(HrmsStatisticalReportEntity entity) {
		entity.setYgtwqtsydwCode(ygtwqtsydwCode);
		JobtitleBasicReq basic = new JobtitleBasicReq();
		basic.setJobtitleBasicGrade(2);
		List<JobtitleBasicResp> basicList = hrmsJobtitleBasicService.getJobtitleBasicList(basic).getObject();
		List<String> jobBasicList = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(basicList)) {
			for (JobtitleBasicResp b : basicList) {
				jobBasicList.add(b.getJobtitleBasicId());
			}
		}

		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		return hrmsStatisticalReportMapper.getJobTitleLevelReportTableData(jobBasicList, entity.getOrgIdList(),ssoOrgCode);
	}

	@Override
	public List<JdGridTableEntity> getEmployeeIdentityReportTableHead() {
		List<JdGridTableEntity> result = Lists.newArrayList();
		result.add(new JdGridTableEntity("科室ID", "orgId", "", 100, false, true, false, "")); // 机构ID
		result.add(new JdGridTableEntity("科室名称", "orgName", "", 240, false, false, false, "")); // 机构名称

		List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.PERSONAL_IDENTITY); //个人身份
		List<HrmsDictInfo> dictInfos2 = hrmsDictInfoService.getDictInfoListByDictType(DictContants.ESTABLISHMENT_TYPE); //编制类型
		if (CollectionUtils.isNotEmpty(dictInfos2)) {
			for (HrmsDictInfo o : dictInfos2) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(o.getDictName());
				entity.setName("BZ" + o.getDictValue());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}

		if (CollectionUtils.isNotEmpty(dictInfos)) {
			for (HrmsDictInfo o : dictInfos) {
				JdGridTableEntity entity = new JdGridTableEntity();
				entity.setLabel(o.getDictName());
				entity.setName(o.getDictValue());
				entity.setWidth(100);
				entity.setHidden(false);
				entity.setSortable(false);
				entity.setEditable(false);
				result.add(entity);
			}
		}
		result.add(new JdGridTableEntity("总人数", "count", "", 300, false, false, false, "")); // 机构名称
		return result;
	}

	@Override
	public List<Map<String, Object>> getEmployeeIdentityReportTableData(HrmsStatisticalReportEntity entity) {

		entity.setYgtwqtsydwCode(ygtwqtsydwCode);
		List<Map<String, Object>> data1 = getEstablishmentTypeReportTableData(entity);

		List<String> employeeIdentityList = Lists.newArrayList();
		List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.PERSONAL_IDENTITY);
		if (CollectionUtils.isNotEmpty(dictInfos)) {
			for (HrmsDictInfo d : dictInfos) {
				employeeIdentityList.add(d.getDictValue());
			}
		}
		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<Map<String, Object>> data2 = hrmsStatisticalReportMapper.getEmployeeIdentityReportTableData(employeeIdentityList,
				entity.getOrgIdList(),ssoOrgCode);  //岗位类别统计

		//统计数据
		for (int i = 0; i < data2.size(); i++) {
			Integer count = 0;
			for (int k = 0; k < employeeIdentityList.size(); k++) {
				Map<String, Object> map = data2.get(i);
				count += Integer.valueOf(map.get(employeeIdentityList.get(k)).toString());
			}
			data2.get(i).put("count", count);
		}


		Map<String, Object> mergeResult = mergeResult(data1, data2, "orgId");
		List<Map<String, Object>> object = (List<Map<String, Object>>) mergeResult.get("result");
		//按科室排序 ksfb_no 如果为null则为0
        Collections.sort(object, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
            	//Integer id1 =  (Integer)o1.get("ksfb_no");
            	Integer id1 =  Integer.valueOf(o1.get("ksfb_no").toString());
                Integer id2 =  Integer.valueOf(o2.get("ksfb_no").toString());
                // 升序
                 return id1.compareTo(id2);
                // 降序
                 // return id2.compareTo(id1);
            }
        });

		
		return object;
	}

	//合并数据
	private static Map<String, Object> mergeResult(List<Map<String, Object>> m1, List<Map<String, Object>> m2, String key) {
		Map<String, Object> result = new HashMap<String, Object>();
		m1.addAll(m2);
		Set<String> set = new HashSet<>();
		List<Map<String, Object>> resultList = m1.stream().collect(Collectors.groupingBy(o -> {
			// 暂存所有key
			set.addAll(o.keySet());
			return o.get(key);
		})).entrySet().stream().map(o -> {
			// 合并
			Map<String, Object> map = o.getValue().stream().flatMap(m -> {
				return m.entrySet().stream();
			}).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> b));
			// 为没有key的赋值0
//			set.stream().forEach(k -> {
//				if (!map.containsKey(k))
//					map.put(k, 0);
//			});
			return map;
		}).sorted((map1, map2) -> {
			return map1.get(key).toString().compareTo(map2.get(key).toString());
		}).collect(Collectors.toList());
		result.put("result", resultList);
		return result;

	}


	//获取编制类型统计
	private List<Map<String, Object>> getEstablishmentTypeReportTableData(HrmsStatisticalReportEntity entity) {
		List<String> employeeIdentityList = Lists.newArrayList();
		List<HrmsDictInfo> dictInfos = hrmsDictInfoService.getDictInfoListByDictType(DictContants.ESTABLISHMENT_TYPE);
		if (CollectionUtils.isNotEmpty(dictInfos)) {
			for (HrmsDictInfo d : dictInfos) {
				employeeIdentityList.add(d.getDictValue());
			}
		}
		if (entity.getOrgIdList() == null || entity.getOrgIdList().size() < 1) {
			entity.setOrgIdList(getMyOrgIDs());
		}

		if (entity.getOrgIdList() == null) {
			return new ArrayList<Map<String, Object>>();
		}
		//getEstablishmentTypeReportTableData
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<Map<String, Object>> date = hrmsStatisticalReportMapper.getEstablishmentTypeReportTableData(employeeIdentityList,
				entity.getOrgIdList(),ssoOrgCode);  //岗位类别统计
		return date;
	}

	/**
	 * @param orgIds
	 * @return
	 * @Title: getEmployeeDistributionReportTableDataResult
	 * @Description: 人员分布情况
	 * @return: List<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2021年3月23日 上午9:52:10
	 */
	public List<Map<String, Object>> getEmployeeDistributionReportTableDataResult(String orgIds) {

		List<String> orgIdList = new ArrayList<String>();
		if (StringUtils.isBlank(orgIds) == false) {
			for (String s : orgIds.split(",")) {
				if (StringUtils.isBlank(s) == false && (!s.equals("ZZSFYBJY"))) {
					orgIdList.add(s);
				}
			}

		} else {

			if (UserInfoHolder.ISADMIN()) {
				orgIdList = new ArrayList<String>();
			} else {
				orgIdList = new ArrayList<String>();
			}

		}

		if (!StringUtil.isEmpty(orgIds) && orgIds.indexOf("ZZSFYBJY") >= 0) {
			if (UserInfoHolder.ISADMIN()) {
				orgIdList.add("ZZSFYBJY");
			}
		}

		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<GetEmployeeDistributionReportTableDataResult> getEmployeeDistributionReportTableDataResults = hrmsStatisticalReportMapper
				.getEmployeeDistributionReportTableData(orgIdList,ssoOrgCode);

		List<GetEmployeeDistributionReportTableDataResult> getEmployeeDistributionReportTableDataResultDetails = hrmsStatisticalReportMapper
				.getEmployeeDistributionReportTableDataDetail(orgIdList,ssoOrgCode);

		List<HrmsOrganizationResp> heHrmsOrganizations = hrmsOrganizationService.getHrmsOrganizationBeanAndNextList(orgIdList).getObject();

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		// 岗位
		List<HrmsDictInfo> hrmsDictInfos = hrmsDictInfoService.getDictInfoListByDictType("job_description_type");

		for (HrmsOrganizationResp hrmsOrganization : heHrmsOrganizations) {

			Integer zrs = 0;// 总人数
			Integer zbrs = 0;// 在编人数
			Integer bars = 0;// 编外人数
			Integer bazrs = 0;// 备案制人数
			for (GetEmployeeDistributionReportTableDataResult getEmployeeDistributionReportTableDataResult : getEmployeeDistributionReportTableDataResults) {
				if (getEmployeeDistributionReportTableDataResult.getOrgId()
						.equals(hrmsOrganization.getOrganizationId())) {
					zrs = zrs + getEmployeeDistributionReportTableDataResult.getEmployeeCount();

					if (getEmployeeDistributionReportTableDataResult.getEstablishmentType() != null) {
						if (getEmployeeDistributionReportTableDataResult.getEstablishmentType().equals(1)) {
							zbrs = zbrs + getEmployeeDistributionReportTableDataResult.getEmployeeCount();
						} else if (getEmployeeDistributionReportTableDataResult.getEstablishmentType().equals(3)) {
							bars = bars + getEmployeeDistributionReportTableDataResult.getEmployeeCount();
						} else if (getEmployeeDistributionReportTableDataResult.getEstablishmentType().equals(7)) {
							bazrs = bazrs + getEmployeeDistributionReportTableDataResult.getEmployeeCount();
						}
					}
				}
			}

			Map<String, Object> m1 = new HashMap<String, Object>();
			// 科室名称
			m1.put("ksmc", hrmsOrganization.getName());
			// 总人数
			m1.put("zrs", zrs);
			// 在编人数
			m1.put("zbrs", zbrs);
			// 编外书
			m1.put("bars", bars);
			// 备案制人数
			m1.put("bazrs", bazrs);

			for (HrmsDictInfo hrmsDictInfo : hrmsDictInfos) {

				Map<String, Object> gwxxbzMap = new HashMap<String, Object>();

				Map<String, Object> gwxxjbMap = new HashMap<String, Object>();
				gwxxjbMap.put("gwxx-jb-wz", 0);// 无证
				gwxxjbMap.put("gwxx-jb-cj", 0); // 初级
				gwxxjbMap.put("gwxx-jb-zj", 0);// 中级
				gwxxjbMap.put("gwxx-jb-fgj", 0);// 副高级
				gwxxjbMap.put("gwxx-jb-zgj", 0);// 正高级
				for (GetEmployeeDistributionReportTableDataResult d : getEmployeeDistributionReportTableDataResultDetails) {
					if (d.getJobDescriptionType() == null || d.getEstablishmentType() == null) {
						continue;
					}
					if (d.getOrgId().equals(hrmsOrganization.getOrganizationId()) && d.getEstablishmentType().equals(1)
							&& d.getJobDescriptionType().toString().equals(hrmsDictInfo.getDictValue())) {
						if (org.apache.commons.lang.StringUtils.isBlank(d.getJobtitleBasicName())) {
							gwxxjbMap.put("gwxx-jb-wz", d.getEmployeeCount());// 无证
						} else if (d.getJobtitleBasicName().indexOf("初级") >= 0) {
							gwxxjbMap.put("gwxx-jb-cj", 1); // 初级
						} else if (d.getJobtitleBasicName().indexOf("中级") >= 0) {
							gwxxjbMap.put("gwxx-jb-zj", 1);// 中级
						} else if (d.getJobtitleBasicName().indexOf("副高级") >= 0) {
							gwxxjbMap.put("gwxx-jb-fgj", 1);// 副高级
						} else if (d.getJobtitleBasicName().indexOf("正高级") >= 0) {
							gwxxjbMap.put("gwxx-jb-zgj", 1);// 正高级
						}
					}

				}

				// 在编统计
				gwxxbzMap.put("gwxx-zb", gwxxjbMap);

				gwxxjbMap = new HashMap<String, Object>();
				gwxxjbMap.put("gwxx-jb-wz", 0);// 无证
				gwxxjbMap.put("gwxx-jb-cj", 0); // 初级
				gwxxjbMap.put("gwxx-jb-zj", 0);// 中级
				gwxxjbMap.put("gwxx-jb-fgj", 0);// 副高级
				gwxxjbMap.put("gwxx-jb-zgj", 0);// 正高级
				for (GetEmployeeDistributionReportTableDataResult d : getEmployeeDistributionReportTableDataResultDetails) {

					if (d.getJobDescriptionType() == null || d.getEstablishmentType() == null) {
						continue;
					}

					if (d.getOrgId().equals(hrmsOrganization.getOrganizationId()) && d.getEstablishmentType().equals(3)
							&& d.getJobDescriptionType().toString().equals(hrmsDictInfo.getDictValue())) {
						if (org.apache.commons.lang.StringUtils.isBlank(d.getJobtitleBasicName())) {
							gwxxjbMap.put("gwxx-jb-wz", d.getEmployeeCount());// 无证
						} else if (d.getJobtitleBasicName().indexOf("初级") >= 0) {
							gwxxjbMap.put("gwxx-jb-cj", 1); // 初级
						} else if (d.getJobtitleBasicName().indexOf("中级") >= 0) {
							gwxxjbMap.put("gwxx-jb-zj", 1);// 中级
						} else if (d.getJobtitleBasicName().indexOf("副高级") >= 0) {
							gwxxjbMap.put("gwxx-jb-fgj", 1);// 副高级
						} else if (d.getJobtitleBasicName().indexOf("正高级") >= 0) {
							gwxxjbMap.put("gwxx-jb-zgj", 1);// 正高级
						}
					}

				}
				// 编歪统计
				gwxxbzMap.put("gwxx-bw", gwxxjbMap);

				Map<String, Object> gwxxMap = new HashMap<String, Object>();
				gwxxMap.put("gwmc", hrmsDictInfo.getDictName());
				gwxxMap.put("v", gwxxbzMap);
				m1.put("gwxx-" + hrmsDictInfo.getDictValue(), gwxxMap);

			}

			dataList.add(m1);
		}

		// System.out.println("----------------\r\n" + JSON.toJSONString(dataList) +
		// "\r\n--------------");
		return dataList;

	}

	/**
	 * @return
	 * @Title: getMyOrgIDs
	 * @Description: TODO
	 * @return: List<String> size=0全部 null 无数据
	 * <AUTHOR>
	 * @date 2021年3月24日 下午3:09:17
	 */
	private List<String> getMyOrgIDs() {
		if (UserInfoHolder.ISADMIN()) {
			return new ArrayList<String>();
		} else {
			List<String> orgIdList = Lists.newArrayList();
			// System.out.println(UserInfoHolder.getOrgRang()+"----------");
			for (String s : UserInfoHolder.getOrgRang().replace("'", "").replace("(", "").replace(")", "").split(",")) {
				if (StringUtils.isBlank(s) == false) {
					orgIdList.add(s);
				}
			}
			// System.out.println(orgIdList.size()+"----------");
			if (orgIdList.size() < 1) {
				return null;
			}
			return orgIdList;
		}
	}

//	/**
//	 * 获取人员分析报表
//	* @Title: getEmployeeAnalysisReportTable 
//	* @Description: TODO
//	* @return   
//	* @return: List<com.google.common.collect.Maps>   
//	* <AUTHOR>
//	* @date 2021年4月1日 上午9:38:37
//	 */
//	public List<Map<String, Object>>  getEmployeeAnalysisReportTable()
//	{
//		
//		List<Map<String, Object>> dataList=new ArrayList<Map<String,Object>>();
//		GetEmployeeDistributionReportTableDataParameterType dataParameterType=new GetEmployeeDistributionReportTableDataParameterType();
//		Map<String, Object> emp =hrmsStatisticalReportMapper.getEmployeeAnalysisReportTable(dataParameterType);
//		
//		//管理人员
//		dataParameterType.setPostCategory("3");
//		Map<String, Object> emp_gw_glry =hrmsStatisticalReportMapper.getEmployeeAnalysisReportTable(dataParameterType);
//		
//		//管理人员-女
//		dataParameterType.setGender("1");
//		Map<String, Object> emp_gw_glry_nv =hrmsStatisticalReportMapper.getEmployeeAnalysisReportTable(dataParameterType);
//		
//		//管理人员-少数名族
//		dataParameterType.setSsmz("1");
//		Map<String, Object> emp_gw_glry_ssmz =hrmsStatisticalReportMapper.getEmployeeAnalysisReportTable(dataParameterType);
//		
//		Map<String,Object> dataMap = initgetEmployeeAnalysisData(emp,true);
//		dataMap.put("post", "");
//		dataMap.put("project", "总计");
//		dataList.add(dataMap);
//		
//		
//		//管理人员-女
//		dataMap = initgetEmployeeAnalysisData(emp_gw_glry_nv,false);	
//		dataMap.put("post", "管理人员");
//		dataMap.put("project", "其中：1、女");
//		dataMap.put("sum", emp_gw_glry_nv.get("numCount"));
//		dataMap.put("nvCount", "");
//		dataList.add(dataMap);
//		
//		//管理人员-少数名族
//		dataMap = initgetEmployeeAnalysisData(emp_gw_glry_ssmz,false);
//		dataMap.put("post", "管理人员");
//		dataMap.put("project", "2、少数民族");
//		dataMap.put("sum", emp_gw_glry_ssmz.get("numCount"));
//		dataMap.put("ssmzCount","");
//		dataList.add(dataMap);
//		
//		//管理人员
//		dataMap = initgetEmployeeAnalysisData(emp_gw_glry,false);
//		dataMap.put("post", "管理人员");
//		dataMap.put("project", "七级职员（科级正职）");
//		dataList.add(dataMap);
//		
//		return dataList;
//	}

//	private Map<String,Object> initgetEmployeeAnalysisData(Map<String,Object> map,boolean all)
//	{
//		Map<String,Object> dataMap = Maps.newLinkedHashMap();
//		//第一行
//		dataMap.put("post", "");
//		dataMap.put("project", "总计");
//		dataMap.put("sum", map.get("numCount"));
//		dataMap.put("nvCount", map.get("nvCount"));
//		dataMap.put("ssmzCount", map.get("ssmzCount"));
//		dataMap.put("zgdyCount", map.get("zgdyCount"));
//		dataMap.put("xlYjsCount", map.get("xlYjsCount"));
//		dataMap.put("xlBkCount", map.get("xlBkCount"));
//		dataMap.put("xlZkCount", map.get("xlZkCount"));
//		dataMap.put("xlZzCount", map.get("xlZzCount"));
//		dataMap.put("xlGzCount", map.get("xlGzCount"));
//		dataMap.put("xlCzCount", map.get("xlCzCount"));
//		dataMap.put("nl35xCount", map.get("nl35xCount"));
//		dataMap.put("nl41d45Count", map.get("nl41d45Count"));
//		dataMap.put("nl46d50Count", map.get("nl46d50Count"));
//		dataMap.put("nl51d55Count", map.get("nl51d55Count"));
//		dataMap.put("nl56d59Count", map.get("nl56d59Count"));
//		dataMap.put("nl60sCount", map.get("nl60sCount"));
//		
//		if(all)
//		{
//		dataMap.put("gwZj4Count", map.get("gwZj4Count"));
//		dataMap.put("gwZj5Count", map.get("gwZj5Count"));
//		dataMap.put("gwZj6Count", map.get("gwZj6Count"));
//		dataMap.put("gwZj7Count", map.get("gwZj7Count"));
//		dataMap.put("gwZj8Count", map.get("gwZj8Count"));
//		dataMap.put("gwZj9Count", map.get("gwZj9Count"));
//		dataMap.put("gwZj10Count", map.get("gwZj10Count"));
//		dataMap.put("gwZj11Count", map.get("gwZj11Count"));
//		dataMap.put("gwZj12Count", map.get("gwZj12Count"));
//		dataMap.put("gwZj13Count", map.get("gwZj13Count"));
//		dataMap.put("gwGl7Count", map.get("gwGl7Count"));
//		dataMap.put("gwGl8Count", map.get("gwGl8Count"));
//		dataMap.put("gwGl9Count", map.get("gwGl9Count"));
//		dataMap.put("gwGq2Count", map.get("gwGq2Count"));
//		dataMap.put("gwGq3Count", map.get("gwGq3Count"));
//		}
//		return dataMap;
//	}

	private Map<String, Object> initgetEmployeeAnalysisData(List<GetEmployeeAnalysisReportTableDataResult> list,
															EmployeeDistributionReportTableDataParameterType bean, boolean all) {
		Map<String, Object> dataMap = Maps.newLinkedHashMap();
		// 第一行
		dataMap.put("post", "");
		dataMap.put("project", "");
		dataMap.put("name1", "");
		dataMap.put("name2", "");
		dataMap.put("name3", "");
		// Stream<GetEmployeeAnalysisReportTableDataResult> fiStream = list.stream();

		Predicate<GetEmployeeAnalysisReportTableDataResult> predicate = d -> d.getNum() > 0;

		if (bean != null) {

			if (bean.getPredicate() != null) {
				predicate = predicate.and(bean.getPredicate());
			}

			if (bean.getPostCategory() != null) {
				predicate = predicate.and(d -> (d.getPostCategory().equals(bean.getPostCategory())));
			}

			if (bean.getPostId() != null) {
				predicate = predicate.and(d -> (d.getPostId().equals(bean.getPostId())));
			}
			if (bean.getExcludePostId() != null) {

				for (String s : bean.getExcludePostId()) {
					predicate = predicate.and(d -> (!d.getPostId().equals(s)));
				}

			}

			if (bean.getGender() != null) {
				predicate = predicate.and(d -> (d.getGender().equals("1")));
			}
			if (bean.getSsmz() != null) {
				predicate = predicate.and(d -> (!d.getNationality().equals("1")));
			}

			if (bean.getOperationOrg() != null) {
				predicate = predicate.and(d -> (!d.getOperationOrg().equals("")));
			}

			if (bean.getJobDescriptionType() != null) {
				predicate = predicate.and(d -> (d.getJobDescriptionType().equals(bean.getJobDescriptionType())));
			}
		}

		dataMap.put("sum",
				list.stream().filter(predicate).mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());
		dataMap.put("nvCount", list.stream().filter(predicate.and(d -> (d.getGender().equals("1"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());
		dataMap.put("ssmzCount", list.stream().filter(predicate.and(d -> (!d.getEducationType().equals("1"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("zgdyCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("1"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("xlYjsCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("1"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("xlBkCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("2"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("xlZkCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("3"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("xlZzCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("4"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("xlGzCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("5"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("xlCzCount", list.stream().filter(predicate.and(d -> (d.getPoliticalStatus().equals("6"))))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl35xCount", list.stream().filter(predicate.and(d -> (d.getAge() <= 35)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl36d40Count", list.stream().filter(predicate.and(d -> (d.getAge() >= 36 && d.getAge() <= 40)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl41d45Count", list.stream().filter(predicate.and(d -> (d.getAge() >= 41 && d.getAge() <= 45)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl46d50Count", list.stream().filter(predicate.and(d -> (d.getAge() >= 46 && d.getAge() <= 50)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl51d55Count", list.stream().filter(predicate.and(d -> (d.getAge() >= 51 && d.getAge() <= 55)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl56d59Count", list.stream().filter(predicate.and(d -> (d.getAge() >= 56 && d.getAge() <= 59)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		dataMap.put("nl60sCount", list.stream().filter(predicate.and(d -> (d.getAge() >= 60)))
				.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		if (all) {

			dataMap.put("gwZj13Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027703"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj12Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027701"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj11Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027699"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj10Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027697"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj9Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027695"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj8Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027693"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj7Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027691"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj6Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027689"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj5Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027687"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwZj4Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027685"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwGl9Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027675"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwGl8Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027673"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwGl7Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027671"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwGq2Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027649"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

			dataMap.put("gwGq3Count",
					list.stream().filter(predicate.and(d -> (d.getPostId().equals("251644092496027651"))))
							.mapToInt(GetEmployeeAnalysisReportTableDataResult::getNum).sum());

		}
		return dataMap;
	}

	/**
	 * 获取人员分析报表
	 *
	 * @return
	 * @Title: getEmployeeAnalysisReportTable
	 * @Description: TODO
	 * @return: List<com.google.common.collect.Maps>
	 * <AUTHOR>
	 * @date 2021年4月1日 上午9:38:37
	 */
	public List<Map<String, Object>> getEmployeeAnalysisReportTable(String establishmentType, String orgIds) {

		List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject();

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<GetEmployeeAnalysisReportTableDataResult> list = hrmsStatisticalReportMapper
				.getEmployeeAnalysisReportTableA(establishmentType, orgIdNewList,ssoOrgCode);

		Map<String, Object> dataMap = initgetEmployeeAnalysisData(list, null, true);
		dataMap.put("post", "");
		dataMap.put("project", "总计");
		dataList.add(dataMap);

		EmployeeDistributionReportTableDataParameterType bean = new EmployeeDistributionReportTableDataParameterType();
		bean.setPostCategory("3");

		// 管理人员
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "小计");
//		dataMap.put("sum", emp_gw_glry_nv.get("numCount"));
//		dataMap.put("nvCount", "");
		dataList.add(dataMap);

		// 管理人员-女
		bean.setGender("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "其中：1、女");
		dataList.add(dataMap);
//		
//		//管理人员-少数名族
		bean.setGender(null);
		bean.setSsmz("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "2、少数民族");
		dataList.add(dataMap);

//		//管理人员-七级职员（科级正职）
		bean.setGender(null);
		bean.setSsmz(null);
		bean.setPostId("251644092496027671");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "七级职员（科级正职）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027673");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "八级职员（科级副职）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027675");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "九级职员（科员）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027677");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "十级职员（办事员）");
		dataList.add(dataMap);

		bean.setPostId(null);
		bean.setExcludePostId(new String[]{"251644092496027671", "251644092496027673", "251644092496027675",
				"251644092496027677"});
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "管理人员");
		dataMap.put("project", "其他等级人员");
		dataList.add(dataMap);

		// 专技人员
		bean = new EmployeeDistributionReportTableDataParameterType();
		bean.setPostCategory("2");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "小计");
		dataList.add(dataMap);

		bean.setPredicate(d -> (d.getConcurrentPosition().equals("232085375396192256")
				|| d.getConcurrentPosition().equals("232085412499005440")
				|| d.getConcurrentPosition().equals("232085460628643840")));
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "在管理后勤岗位工作的");
		dataList.add(dataMap);
		bean.setPredicate(null);

		bean.setOperationOrg("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "具有职业资格的");
		dataList.add(dataMap);
		bean.setOperationOrg(null);

		// 专业技术人员-女
		bean.setGender("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "女");
		dataList.add(dataMap);
		bean.setGender(null);
//		
//		//专业技术人员-少数名族
		bean.setSsmz("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "少数民族");
		dataList.add(dataMap);
		bean.setSsmz(null);

//		//专业技术人员
		List<Map<String, Object>> tempList = getInitgetEmployeeZYJSRYAnalysisData(list, null, null, "专业技术人员汇总", "");
		tempList.remove(0);
		dataList.addAll(tempList);

		bean.setExcludePostId(new String[]{"251644092496027685", "251644092496027687", "251644092496027689",
				"251644092496027691", "251644092496027693", "251644092496027695", "251644092496027697",
				"251644092496027699", "251644092496027701"});
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "");
		dataMap.put("name1", "");
		dataMap.put("name2", "十三级");
		dataList.add(dataMap);
		bean.setExcludePostId(null);

		// 卫生专业技术人才 合计
		bean = new EmployeeDistributionReportTableDataParameterType();
		bean.setPostCategory("2");
		Predicate<GetEmployeeAnalysisReportTableDataResult> predicate = (d -> (d.getJobDescriptionType().equals("6")
				|| d.getJobDescriptionType().equals("5") || d.getJobDescriptionType().equals("10")
				|| d.getJobDescriptionType().equals("8") || d.getJobDescriptionType().equals("9")));

		bean.setPredicate(predicate);
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "卫生专业技术人才");
		dataMap.put("name1", "合计");
		dataMap.put("name2", "");
		dataMap.put("name3", "");
		dataList.add(dataMap);
		bean.setPredicate(null);

		// 卫生专业技术人才-临床-小计
		dataList.addAll(getInitgetEmployeeZYJSRYAnalysisData(list, "6", null, "卫生专业技术人才", "临床"));

		// 卫生专业技术人才-护理
		dataList.addAll(getInitgetEmployeeZYJSRYAnalysisData(list, "5", null, "卫生专业技术人才", "护理"));

		// 卫生专业技术人才-护理-小计
		predicate = (d -> (d.getJobDescriptionType().equals("9") || d.getJobDescriptionType().equals("10")));
		bean.setPredicate(predicate);
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", "卫生专业技术人才");
		dataMap.put("name1", "医技");
		dataMap.put("name2", "小计");
		dataMap.put("name3", "");
		dataList.add(dataMap);
		bean.setPredicate(null);

		// 卫生专业技术人才-医技-影像-小计
		dataList.addAll(getInitgetEmployeeZYJSRYAnalysisData(list, "10", null, "卫生专业技术人才", "医技-影像"));

		// 专业技术人员-检验
		dataList.addAll(getInitgetEmployeeZYJSRYAnalysisData(list, "9", null, "卫生专业技术人才", "医技-检验"));

		// 专业技术人员-药械
		dataList.addAll(getInitgetEmployeeZYJSRYAnalysisData(list, "8", null, "卫生专业技术人才", "药械"));

		// 专业技术人员-其他专业技术人才
		dataList.addAll(getInitgetEmployeeZYJSRYAnalysisData(list, null,
				d -> (d.getJobDescriptionType().equals("") || d.getJobDescriptionType().equals("1")
						|| d.getJobDescriptionType().equals("2") || d.getJobDescriptionType().equals("3")
						|| d.getJobDescriptionType().equals("4") || d.getJobDescriptionType().equals("7")),
				"其他专业技术人才", ""));

		bean = new EmployeeDistributionReportTableDataParameterType();
		bean.setPostCategory("1");

		// 管理人员
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "小计");
		dataList.add(dataMap);

		// 管理人员-女
		bean.setGender("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "其中：1、女");
		dataList.add(dataMap);
//		
//		//管理人员-少数名族
		bean.setGender(null);
		bean.setSsmz("1");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "2、少数民族");
		dataList.add(dataMap);

//		//管理人员-一级岗位（高级技师）
		bean.setGender(null);
		bean.setSsmz(null);
		bean.setPostId("251644092491833344");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "一级岗位（高级技师）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027649");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "二级岗位（技师）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027651");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "三级岗位（高级工）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027653");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "四级岗位（中级工）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027655");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "五级岗位（初级工）");
		dataList.add(dataMap);

		bean.setPostId("251644092496027657");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "工勤技能人员");
		dataMap.put("project", "普通工");
		dataList.add(dataMap);

		bean = new EmployeeDistributionReportTableDataParameterType();
		bean.setPredicate(d -> (d.getPostCategory().equals("") || d.getPostCategory().equals("4")
				|| d.getPostCategory().equals("5")));
		// 其他从业人员
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "其他从业人员");
		dataMap.put("project", "");
		dataList.add(dataMap);
		return dataList;
	}

	/**
	 * 返回专业技术人员通用列表
	 *
	 * @return
	 * @Title: getInitgetEmployeeZYJSRYAnalysisData
	 * @Description: TODO
	 * @return: List<Map < String, Object>>
	 * <AUTHOR>
	 * @date 2021年4月2日 上午9:59:04
	 */
	private List<Map<String, Object>> getInitgetEmployeeZYJSRYAnalysisData(
			List<GetEmployeeAnalysisReportTableDataResult> list, String jobDescriptionType,
			Predicate<GetEmployeeAnalysisReportTableDataResult> predicate, String project, String name) {
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		EmployeeDistributionReportTableDataParameterType bean = new EmployeeDistributionReportTableDataParameterType();
		if (StringUtils.isBlank(jobDescriptionType) == false) {
			bean.setJobDescriptionType(jobDescriptionType);
		}
		if (predicate != null) {
			bean.setPredicate(predicate);
		}
		bean.setPostCategory("2");

		Map<String, Object> dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "小计");
		dataMap.put("name3", "");
		dataList.add(dataMap);

		bean.setPostId("251644092496027685");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "高级岗位");
		dataMap.put("name3", "四级");
		dataList.add(dataMap);

		bean.setPostId("251644092496027687");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "高级岗位");
		dataMap.put("name3", "五级");
		dataList.add(dataMap);

		bean.setPostId("251644092496027689");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "高级岗位");
		dataMap.put("name3", "六级");
		dataList.add(dataMap);

		bean.setPostId("251644092496027691");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "高级岗位");
		dataMap.put("name3", "七级");
		dataList.add(dataMap);

		bean.setPostId("251644092496027693");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "中级岗位");
		dataMap.put("name3", "八级");
		dataList.add(dataMap);
		bean.setPostId("251644092496027695");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "中级岗位");
		dataMap.put("name3", "九级");
		dataList.add(dataMap);
		bean.setPostId("251644092496027697");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "中级岗位");
		dataMap.put("name3", "十级");
		dataList.add(dataMap);

		bean.setPostId("251644092496027699");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "初级岗位");
		dataMap.put("name3", "十一级");
		dataList.add(dataMap);
		bean.setPostId("251644092496027701");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "初级岗位");
		dataMap.put("name3", "十二级");
		dataList.add(dataMap);
		bean.setPostId("251644092496027703");
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "初级岗位");
		dataMap.put("name3", "十三级");
		dataList.add(dataMap);

		bean.setPostId(null);
		bean.setExcludePostId(new String[]{"251644092496027685", "251644092496027687", "251644092496027689",
				"251644092496027691", "251644092496027693", "251644092496027695", "251644092496027697",
				"251644092496027699", "251644092496027701", "251644092496027703"});
		dataMap = initgetEmployeeAnalysisData(list, bean, false);
		dataMap.put("post", "专业技术人员");
		dataMap.put("project", project);
		dataMap.put("name1", name);
		dataMap.put("name2", "其他等次岗位");
		dataList.add(dataMap);
		return dataList;
	}

	private static class EmployeeDistributionReportTableDataParameterType {

		String postCategory;
		String gender;
		String ssmz;
		String postId;

		String[] excludePostId;

		// 营业资格证书
		String operationOrg;

		String jobDescriptionType;

		Predicate<GetEmployeeAnalysisReportTableDataResult> predicate = null;

		public Predicate<GetEmployeeAnalysisReportTableDataResult> getPredicate() {
			return predicate;
		}

		public void setPredicate(Predicate<GetEmployeeAnalysisReportTableDataResult> predicate) {
			this.predicate = predicate;
		}

		public String getJobDescriptionType() {
			return jobDescriptionType;
		}

		public void setJobDescriptionType(String jobDescriptionType) {
			this.jobDescriptionType = jobDescriptionType;
		}

		public String getOperationOrg() {
			return operationOrg;
		}

		public void setOperationOrg(String operationOrg) {
			this.operationOrg = operationOrg;
		}

		public String[] getExcludePostId() {
			return excludePostId;
		}

		public void setExcludePostId(String[] excludePostId) {
			this.excludePostId = excludePostId;
		}

		public String getPostId() {
			return postId;
		}

		public void setPostId(String postId) {
			this.postId = postId;
		}

		public String getPostCategory() {
			return postCategory;
		}

		public void setPostCategory(String postCategory) {
			this.postCategory = postCategory;
		}

		public String getGender() {
			return gender;
		}

		public void setGender(String gender) {
			this.gender = gender;
		}

		public String getSsmz() {
			return ssmz;
		}

		public void setSsmz(String ssmz) {
			this.ssmz = ssmz;
		}
	}

	public static void main(String[] args) {
		Map<String, Object> a = Maps.newHashMap();
		Map<String, Object> b = Maps.newHashMap();
		Map<String, Object> c = Maps.newHashMap();
		a.put("1", "1");
		b.put("2", "2");
		c.put("3", "3");
		Set<Map<String, Object>> set = Sets.newHashSet();
		set.add(a);
		set.add(b);
		set.add(c);
		Set<Map<String, Object>> seta = Sets.newHashSet();
		seta.add(a);
		seta.add(b);
		seta.add(c);
		set.addAll(seta);
		System.out.println(set);
	}


	@Override
	/**
	 * 专技人员职称报表
	 *
	 * <AUTHOR>
	 * @date 2021/11/17 17:12
	 */
	public Map<String, Object> getZJRYZCBB() {
		List<EmployeeJobtitleListResp> employeeJobtitleListRespList =
				hrmsJobtitleInfoService.getEmployeejobtitleList();

		for (EmployeeJobtitleListResp employeeJobtitleListResp : employeeJobtitleListRespList) {
			if (StringUtils.isBlank(employeeJobtitleListResp.getJobtitleLevelText())) {
				employeeJobtitleListResp.setJobtitleLevelText("");
			}
			if (StringUtils.isBlank(employeeJobtitleListResp.getPersonalIdentityText())) {
				employeeJobtitleListResp.setPersonalIdentityText("");
			}
		}
		Map<String, Object> data = Maps.newLinkedHashMap();
		Map<String, Object> zcdata = Maps.newLinkedHashMap();
		Map<String, Object> zgmap = Maps.newLinkedHashMap();
		Map<String, Object> zgempmap = Maps.newLinkedHashMap();

		Map<String, Object> fgmap = Maps.newLinkedHashMap();
		Map<String, Object> fgempmap = Maps.newLinkedHashMap();
		Map<String, Object> zjmap = Maps.newLinkedHashMap();
		Map<String, Object> zjempmap = Maps.newLinkedHashMap();

		Map<String, Object> cjmap = Maps.newLinkedHashMap();
		Map<String, Object> cjempmap = Maps.newLinkedHashMap();

		Map<String, Integer> zgrsmap = Maps.newLinkedHashMap();
		Map<String, Integer> fgrsmap = Maps.newLinkedHashMap();
		Map<String, Integer> zjrsmap = Maps.newLinkedHashMap();
		Map<String, Integer> cjrsmap = Maps.newLinkedHashMap();


		List<String> orgIds;

		List<String> reqOrgIds=new ArrayList<>();
		reqOrgIds.add("368417053658959872");
		List<HrmsOrganizationResp> hrmsOrganizationRespList=hrmsOrganizationFeignService.getHrmsOrganizationBeanAndNextList(reqOrgIds).getObject();
		Map<String, List<String>> orgClassMap = Maps.newLinkedHashMap();
		orgIds = hrmsOrganizationRespList.stream().map(HrmsOrganizationResp::getOrganizationId).collect(Collectors.toList());
		orgClassMap.put("临床人员", orgIds);


		reqOrgIds=new ArrayList<>();
		reqOrgIds.add("368417053793177600");
		hrmsOrganizationRespList=hrmsOrganizationFeignService.getHrmsOrganizationBeanAndNextList(reqOrgIds).getObject();
		orgIds = hrmsOrganizationRespList.stream().map(HrmsOrganizationResp::getOrganizationId).collect(Collectors.toList());
		orgClassMap.put("行政、职能、后勤", orgIds);


		reqOrgIds=new ArrayList<>();
		reqOrgIds.add("368417053713485824");
		hrmsOrganizationRespList=hrmsOrganizationFeignService.getHrmsOrganizationBeanAndNextList(reqOrgIds).getObject();
		orgIds = hrmsOrganizationRespList.stream().map(HrmsOrganizationResp::getOrganizationId).collect(Collectors.toList());
		orgClassMap.put("医技科室", orgIds);

		reqOrgIds=new ArrayList<>();
		reqOrgIds.add("368417053755428864");
		hrmsOrganizationRespList=hrmsOrganizationFeignService.getHrmsOrganizationBeanAndNextList(reqOrgIds).getObject();
		orgIds = hrmsOrganizationRespList.stream().map(HrmsOrganizationResp::getOrganizationId).collect(Collectors.toList());
		orgClassMap.put("药剂科室", orgIds);


		reqOrgIds=new ArrayList<>();
		reqOrgIds.add("368417053839314944");
		hrmsOrganizationRespList=hrmsOrganizationFeignService.getHrmsOrganizationBeanAndNextList(reqOrgIds).getObject();
		orgIds = hrmsOrganizationRespList.stream().map(HrmsOrganizationResp::getOrganizationId).collect(Collectors.toList());
		orgClassMap.put("其他分院", orgIds);

		List<String> empList;
		////医疗 -医师	护理-护士	医技-技师	药学制剂-药师
		//String[] zcmz = {"医疗", "护理", "医技", "药学制剂", "其他"};
		//String[] zcmz = {"医师", "护士", "技师", "药师", "其他"};

		Map<String,String> gwMap= Maps.newLinkedHashMap();
		gwMap.put("医疗","医师");
		gwMap.put("护理","护士");
		gwMap.put("医技","技师");
		gwMap.put("药学制剂","药师");
		gwMap.put("其他","其他");


		for (Map.Entry<String, List<String>> entry : orgClassMap.entrySet()) {
			zcdata =  Maps.newLinkedHashMap();
			zgmap = Maps.newLinkedHashMap();
			zgempmap = Maps.newLinkedHashMap();
			fgmap = Maps.newLinkedHashMap();
			fgempmap = Maps.newLinkedHashMap();
			zjmap = Maps.newLinkedHashMap();
			zjempmap = Maps.newLinkedHashMap();
			cjmap = Maps.newLinkedHashMap();
			cjempmap = Maps.newLinkedHashMap();


			for (Map.Entry<String, String> n : gwMap.entrySet()) {
				empList = getZJRYZCBB(employeeJobtitleListRespList, "正高",n.getValue(), entry.getValue());
				zgmap.put(n.getKey(), empList.size());
				zgempmap.put(n.getKey(), org.apache.commons.lang.StringUtils.join(empList.toArray(), ","));
				zgrsmap.put(n.getKey(), zgrsmap.getOrDefault(n.getKey(), 0) + empList.size());
			}
			for (Map.Entry<String, String> n : gwMap.entrySet()) {
				empList = getZJRYZCBB(employeeJobtitleListRespList, "副高", n.getValue(), entry.getValue());
				fgmap.put(n.getKey(), empList.size());
				fgempmap.put(n.getKey(), org.apache.commons.lang.StringUtils.join(empList.toArray(), ","));
				fgrsmap.put(n.getKey(), fgrsmap.getOrDefault(n.getKey(), 0) + empList.size());
			}
			for (Map.Entry<String, String> n : gwMap.entrySet()) {
				empList = getZJRYZCBB(employeeJobtitleListRespList, "中级", n.getValue(), entry.getValue());
				zjmap.put(n.getKey(), empList.size());
				zjempmap.put(n.getKey(), org.apache.commons.lang.StringUtils.join(empList.toArray(), ","));
				zjrsmap.put(n.getKey(), zjrsmap.getOrDefault(n.getKey(), 0) + empList.size());
			}
			for (Map.Entry<String, String> n : gwMap.entrySet()) {
				empList = getZJRYZCBB(employeeJobtitleListRespList, "初级", n.getValue(), entry.getValue());
				cjmap.put(n.getKey(), empList.size());
				cjempmap.put(n.getKey(), org.apache.commons.lang.StringUtils.join(empList.toArray(), ","));
				cjrsmap.put(n.getKey(), cjrsmap.getOrDefault(n.getKey(), 0) + empList.size());
			}
			zcdata.put("正高", zgmap);
			zcdata.put("副高", fgmap);
			zcdata.put("中级", zjmap);
			zcdata.put("初级", cjmap);
			data.put(entry.getKey() + "人数", zcdata);

			zcdata =  Maps.newLinkedHashMap();
			zcdata.put("正高", zgempmap);
			zcdata.put("副高", fgempmap);
			zcdata.put("中级", zjempmap);
			zcdata.put("初级", cjempmap);
			data.put(entry.getKey() + "名单", zcdata);
		}
		zcdata = Maps.newLinkedHashMap();
		zcdata.put("正高", zgrsmap);
		zcdata.put("副高", fgrsmap);
		zcdata.put("中级", zjrsmap);
		zcdata.put("初级", cjrsmap);
		data.put("总人数", zcdata);
		return data;
	}


	private List<String> getZJRYZCBB(List<EmployeeJobtitleListResp> employeeJobtitleListResps,
									 String jobtitleLevel,
									 String personalIdentity, List<String> orgIds) {
		List<String> empList = new ArrayList<>();

		for (EmployeeJobtitleListResp employeeJobtitleListResp : employeeJobtitleListResps) {
			if (orgIds.contains(employeeJobtitleListResp.getOrgId())) {
				if (personalIdentity.equals("其他")) {
					if (employeeJobtitleListResp.getJobtitleLevelText().indexOf(jobtitleLevel) >= 0) {
//"医疗-", "护理", "医技", "药学制剂"
						//医疗 -医师	护理-护士	医技-技师	药学制剂-药师
						if (!
								(employeeJobtitleListResp.getPersonalIdentityText().indexOf("医师") >= 0 ||
										employeeJobtitleListResp.getPersonalIdentityText().indexOf("护士") >= 0 ||
										employeeJobtitleListResp.getPersonalIdentityText().indexOf("技师") >= 0 ||
										employeeJobtitleListResp.getPersonalIdentityText().indexOf("药师") >= 0)
						) {
							empList.add(employeeJobtitleListResp.getEmployeeName());
						}
					}
				} else {
					if (
							employeeJobtitleListResp.getJobtitleLevelText().indexOf(jobtitleLevel) >= 0 &&
									employeeJobtitleListResp.getPersonalIdentityText().indexOf(personalIdentity) >= 0) {
						empList.add(employeeJobtitleListResp.getEmployeeName());
					}
				}

			}
		}
		return empList;
	}
}