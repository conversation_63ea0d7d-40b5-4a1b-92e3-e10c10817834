package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsFamilyInfoMapper;
import cn.trasen.hrms.model.HrmsFamilyInfo;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsFamilyInfoService;
import tk.mybatis.mapper.entity.Example;

/**   
 * @Title: HrmsFamilyInfoServiceImpl.java 
 * @Package cn.trasen.hrms.service.impl 
 * @Description: 员工家庭信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月12日 下午5:40:34 
 * @version V1.0   
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsFamilyInfoServiceImpl implements HrmsFamilyInfoService {

	@Autowired
	HrmsFamilyInfoMapper hrmsFamilyInfoMapper;
	@Autowired
	HrmsDictInfoService hrmsDictInfoService;

	/**
	 * @Title: insert
	 * @Description: 新增家庭信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int insert(HrmsFamilyInfo entity) {
		entity.setFamilyInfoId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		return hrmsFamilyInfoMapper.insert(entity);
	}

	/**
	 * @Title: update
	 * @Description: 更新家庭信息
	 * @Param: entity
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsFamilyInfo entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsFamilyInfoMapper.updateByPrimaryKeySelective(entity);
	}

	/**
	 * @Title: deleted
	 * @Description: 删除家庭信息
	 * @Param: id
	 * @Return: int
	 * <AUTHOR>
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		HrmsFamilyInfo familyInfo = hrmsFamilyInfoMapper.selectByPrimaryKey(id);
		if (familyInfo != null) {
			familyInfo.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return hrmsFamilyInfoMapper.updateByPrimaryKeySelective(familyInfo);
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取家庭信息列表
	 * @Param: page
	 * @param entity
	 * @Return: List<HrmsFamilyInfo>
	 * <AUTHOR>
	 */
	@Override
	public List<HrmsFamilyInfo> getDataList(Page page, HrmsFamilyInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsFamilyInfo.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getMemberName())) { // 成员姓名
			example.and().andLike("memberName", "%" + entity.getMemberName() + "%");
		}

		return hrmsFamilyInfoMapper.selectByExampleAndRowBounds(example, page);
	}

	/**
	 * @Title: getList
	 * @Description: 查询家庭信息列表(不分页)
	 * @param entity
	 * @Return List<HrmsFamilyInfo>
	 * <AUTHOR>
	 * @date 2020年4月21日 下午2:40:30
	 */
	@Override
	public List<HrmsFamilyInfo> getList(HrmsFamilyInfo entity) {
		Assert.hasText(entity.getEmployeeId(), "employeeId must be not null.");

		Example example = new Example(HrmsFamilyInfo.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("employeeId", entity.getEmployeeId());
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if (StringUtils.isNotBlank(entity.getFamilyInfoId())) {
			example.and().andEqualTo("familyInfoId", entity.getFamilyInfoId());
		}

		List<HrmsFamilyInfo> list = hrmsFamilyInfoMapper.selectByExample(example);
		if (CollectionUtils.isNotEmpty(list)) {
			Map<String, String> relationshipMap = hrmsDictInfoService.convertDictMap(DictContants.PERSONNEL_RELATIONSHIP); // 人员关系字典
			Map<String, String> politicalStatusMap = hrmsDictInfoService.convertDictMap(DictContants.POLITICAL_STATUS); // 政治面貌字典
			for (HrmsFamilyInfo family : list) {
				family.setRelationshipText(relationshipMap.get(family.getRelationship())); // 与本人关系文本值
				family.setPoliticalStatusText(politicalStatusMap.get(family.getPoliticalStatus())); // 政治面貌文本值
			}
		}
		return list;
	}

}
