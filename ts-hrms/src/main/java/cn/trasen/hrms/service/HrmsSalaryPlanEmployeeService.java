package cn.trasen.hrms.service;

import java.util.List;

import cn.trasen.hrms.model.HrmsSalaryPlanEmployee;

/**   
 * @Title: HrmsSalaryPlanEmployeeService.java 
 * @Package cn.trasen.hrms.service 
 * @Description: 薪酬方案人员 业务层接口
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司 
 * @date 2020年3月30日 上午10:47:48 
 * @version V1.0   
 */
public interface HrmsSalaryPlanEmployeeService {

	/**
	 * @Title: insert
	 * @Description: 新增薪酬方案人员
	 * @param entity
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年4月29日 上午9:12:50
	 */
	int insert(HrmsSalaryPlanEmployee entity);

	/**
	 * @Title: getSalaryPlanEmployee
	 * @Description: 获取薪酬方案人员Id集合
	 * @param salaryPlanId 薪酬方案ID
	 * @Return List<String>
	 * <AUTHOR>
	 * @date 2020年4月28日 下午5:56:45
	 */
	List<String> getSalaryPlanEmployeeIds(String salaryPlanId);

	/**
	 * @Title: getListByPlanId
	 * @Description: 根据薪酬方案ID获取薪酬方案人员列表
	 * @param salaryPlanId
	 * @return
	 * @Return List<HrmsSalaryPlanEmployee>
	 * <AUTHOR>
	 * @date 2020年4月7日 下午2:48:01
	 */
	List<HrmsSalaryPlanEmployee> getListByPlanId(String salaryPlanId);

}
