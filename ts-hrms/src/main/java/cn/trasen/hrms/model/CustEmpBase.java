package cn.trasen.hrms.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

@Table(name = "cust_emp_base")
@Setter
@Getter
public class CustEmpBase {
    /**
     * 员工ID
     */
    @Id
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 工号
     */
    @Column(name = "employee_no")
    @ApiModelProperty(value = "工号")
    private String employeeNo;

    /**
     * 发薪号
     */
    @Column(name = "emp_payroll")
    @ApiModelProperty(value = "发薪号")
    private String empPayroll;

    /**
     * HIS系统员工工号
     */
    @Column(name = "his_employee_no")
    @ApiModelProperty(value = "HIS系统员工工号")
    private String hisEmployeeNo;

    /**
     * 姓名
     */
    @Column(name = "employee_name")
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /**
     * 组织机构
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构")
    private String orgId;

    /**
     * 企业微信授权用户ID
     */
    @Column(name = "open_id")
    @ApiModelProperty(value = "企业微信授权用户ID")
    private String openId;

    /**
     * 员工状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "员工状态")
    private String employeeStatus;

    /**
     * 是否启用: 1=是; 0=否;
     */
    @Column(name = "is_enable")
    @ApiModelProperty(value = "是否启用: 1=是; 0=否;")
    private String isEnable;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 身份证号
     */
    @Column(name = "identity_number")
    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

    /**
     * 手机号码
     */
    @Column(name = "phone_number")
    @ApiModelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String avatar;

    /**
     * 签章图片
     */
    @Column(name = "signature_img_name")
    @ApiModelProperty(value = "签章图片")
    private String signatureImgName;

    /**
     * 邮箱签名
     */
    @Column(name = "emp_signimg")
    @ApiModelProperty(value = "邮箱签名")
    private String empSignimg;

    /**
     * 入职日期
     */
    @Column(name = "entry_date")
    @ApiModelProperty(value = "入职日期")
    private String entryDate;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /**
     * 年龄
     */
    @Column(name = "emp_age")
    @ApiModelProperty(value = "年龄")
    private String empAge;

    /**
     * 姓名拼音码
     */
    @Column(name = "name_spell")
    @ApiModelProperty(value = "姓名拼音码")
    private String nameSpell;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 车牌号码
     */
    @Column(name = "carNo")
    @ApiModelProperty(value = "车牌号码")
    private String carno;

    /**
     * 员工昵称
     */
    @Column(name = "emp_nick_name")
    @ApiModelProperty(value = "员工昵称")
    private String empNickName;

    /**
     * 移动短号
     */
    @Column(name = "emp_business_phone")
    @ApiModelProperty(value = "移动短号")
    private String empBusinessPhone;

    /**
     * 员工排序
     */
    @Column(name = "emp_sort")
    @ApiModelProperty(value = "员工排序")
    private Integer empSort;

    /**
     * 工龄
     */
    @Column(name = "year_work")
    @ApiModelProperty(value = "工龄")
    private String yearWork;

    /**
     * 年假天数
     */
    @Column(name = "year_days")
    @ApiModelProperty(value = "年假天数")
    private String yearDays;

    /**
     * 已休年假天数
     */
    @Column(name = "year_number")
    @ApiModelProperty(value = "已休年假天数")
    private String yearNumber;

    /**
     * 职务id
     */
    @Column(name = "position_id")
    @ApiModelProperty(value = "职务id")
    private String positionId;

    /**
     * 人员类别
     */
    @Column(name = "org_attributes")
    @ApiModelProperty(value = "人员类别")
    private String orgAttributes;

    /**
     * 岗位类型（经开）
     */
    @Column(name = "post_type")
    @ApiModelProperty(value = "岗位类型（经开）")
    private String postType;

    /**
     * 岗位名称（浏阳中）
     */
    @Column(name = "personal_identity")
    @ApiModelProperty(value = "岗位名称（浏阳中）")
    private String personalIdentity;

    /**
     * 岗位属性（经开）
     */
    @Column(name = "job_attributes")
    @ApiModelProperty(value = "岗位属性（经开）")
    private String jobAttributes;

    /**
     * 转正日期
     */
    @Column(name = "positive_time")
    @ApiModelProperty(value = "转正日期")
    private String positiveTime;

    /**
     * 是否显示个人手机号码  否：0，是：1
     */
    @Column(name = "is_display_phone_no")
    @ApiModelProperty(value = "是否显示个人手机号码  否：0，是：1")
    private String isDisplayPhoneNo;

    /**
     * 是否接收短信提醒  否：0，是：1
     */
    @Column(name = "is_sms_reminder")
    @ApiModelProperty(value = "是否接收短信提醒  否：0，是：1")
    private String isSmsReminder;

    /**
     * 是否使用电子签章  否：0，是：1
     */
    @Column(name = "is_use_signature")
    @ApiModelProperty(value = "是否使用电子签章  否：0，是：1")
    private String isUseSignature;

    /**
     * 是否接收语音提醒  否：0，是：1
     */
    @Column(name = "is_voice_reminder")
    @ApiModelProperty(value = "是否接收语音提醒  否：0，是：1")
    private String isVoiceReminder;

    /**
     * 是否接收微信消息推送  否：0，是：1
     */
    @Column(name = "is_wx_reminder")
    @ApiModelProperty(value = "是否接收微信消息推送  否：0，是：1")
    private String isWxReminder;

    /**
     * 密码到期日期
     */
    @Column(name = "password_expire_date")
    @ApiModelProperty(value = "密码到期日期")
    private Date passwordExpireDate;

    /**
     * 兼职科室id
     */
    @Column(name = "organization_parttime_id")
    @ApiModelProperty(value = "兼职科室id")
    private String organizationParttimeId;

    /**
     * 兼职科室名称
     */
    @Column(name = "organization_parttime_name")
    @ApiModelProperty(value = "兼职科室名称")
    private String organizationParttimeName;

    /**
     * 是否启用流程代理   否：0，是：1
     */
    @Column(name = "is_enable_process_agent")
    @ApiModelProperty(value = "是否启用流程代理   否：0，是：1")
    private String isEnableProcessAgent;

    /**
     * 代理开始时间
     */
    @Column(name = "agent_start_time")
    @ApiModelProperty(value = "代理开始时间")
    private Date agentStartTime;

    /**
     * 代理结束时间
     */
    @Column(name = "agent_end_time")
    @ApiModelProperty(value = "代理结束时间")
    private Date agentEndTime;

    /**
     * 代理人ID
     */
    @Column(name = "agent_ids")
    @ApiModelProperty(value = "代理人ID")
    private String agentIds;

    /**
     * 代理人姓名
     */
    @Column(name = "agent_names")
    @ApiModelProperty(value = "代理人姓名")
    private String agentNames;

    /**
     * 允许上传附件大小（M）
     */
    @Column(name = "upload_file_size")
    @ApiModelProperty(value = "允许上传附件大小（M）")
    private Integer uploadFileSize;

    /**
     * 院区编码
     */
    @Column(name = "hosp_code")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 员工人脸
     */
    @Column(name = "employee_face")
    @ApiModelProperty(value = "员工人脸")
    private String employeeFace;

    /**
     * 是否有设工证
     */
    @ApiModelProperty(value = "是否有设工证")
    private String swc;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建部门编码")
    private String createDept;

    /**
     * 创建部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    private String orgName;
}