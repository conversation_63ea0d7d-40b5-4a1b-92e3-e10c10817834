package cn.trasen.hrms.model;

import io.swagger.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.*;

/**    
  * <P> @Description: 考勤明细</p>
  * <P> @Date: 2020年7月10日  下午1:53:21 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
@Table(name = "hrms_attendance_detail")
@Setter
@Getter
public class HrmsAttendanceDetail {


    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "attendance_detail_id")
    @ApiModelProperty(value = "主键ID")
    private String attendanceDetailId;

    /**
     * 考勤记录ID
     */
    @Column(name = "attendance_record_id")
    @ApiModelProperty(value = "考勤记录ID")
    private String attendanceRecordId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "员工ID")
    private String employeeId;

    /**
     * 考勤项目ID
     */
    @Column(name = "attendance_item_id")
    @ApiModelProperty(value = "考勤项目ID")
    private String attendanceItemId;

    /**
     * 考勤天数
     */
    @Column(name = "attendance_date")
    @ApiModelProperty(value = "考勤日期")
    private String attendanceDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 企业ID
     */
    @Column(name = "enterprise_id")
    @ApiModelProperty(value = "企业ID")
    private String enterpriseId;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 组织机构ID
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "组织机构ID")
    private String orgId;

    /**
     * 组织机构名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "组织机构名称")
    private String orgName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;
    
    
    /**
     * 考勤项目名称
     */
    @Transient
    @ApiModelProperty(value = "考勤项目名称")
    private String attendanceItemName;
    
    /**
     * 考勤项目金额
     */
    @Transient
    @ApiModelProperty(value = "考勤项目金额")
    private BigDecimal attendanceItemAmount;
    
    /**
     * 考勤项目类型
     */
    @Transient
    @ApiModelProperty(value = "考勤项目类型")
    private String attendanceItemType;
    
}