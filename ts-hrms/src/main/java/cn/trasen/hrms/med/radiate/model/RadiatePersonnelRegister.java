package cn.trasen.hrms.med.radiate.model;

import io.swagger.annotations.*;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.jeecgframework.poi.excel.annotation.Excel;

import lombok.*;

/**
 * @Description 放射人员登记
 * @date 2025-05-12 11:52:53
 * <AUTHOR> @version 1.0
 */
@Table(name = "med_radiate_personnel_register")
@Setter
@Getter
public class RadiatePersonnelRegister {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "employee_id")
    @ApiModelProperty(value = "人员id")
    private String employeeId;

    /**
     * 人员工号
     */
    @Excel(name = "工号*")
    @Column(name = "employee_no")
    @ApiModelProperty(value = "人员工号")
    private String employeeNo;

    /**
     * 人员姓名
     */
    @Excel(name = "姓名*")
    @Column(name = "employee_name")
    @ApiModelProperty(value = "人员姓名")
    private String employeeName;

    /**
     * 人员科室id
     */
    @Column(name = "org_id")
    @ApiModelProperty(value = "人员科室id")
    private String orgId;

    /**
     * 人员科室名称
     */
    @Column(name = "org_name")
    @ApiModelProperty(value = "人员科室名称")
    private String orgName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String birthday;

    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String education;

    /**
     * 技术职称
     */
    @Column(name = "technical_title")
    @ApiModelProperty(value = "技术职称")
    private String technicalTitle;

    /**
     * 状态
     */
    @Column(name = "employee_status")
    @ApiModelProperty(value = "状态")
    private String employeeStatus;

    /**
     * 拟从事介入手术类别
     */
    @Excel(name = "拟从事介入手术类别")
    @Column(name = "operation_type")
    @ApiModelProperty(value = "拟从事介入手术类别")
    private String operationType;

    /**
     * 拟从事放射类别
     */
    @Excel(name = "拟从事放射类别(多个,隔开)*")
    @Column(name = "radiation_type")
    @ApiModelProperty(value = "拟从事放射类别")
    private String radiationType;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人账号
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人账号")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 创建人所属部门编码
     */
    @Column(name = "create_dept")
    @ApiModelProperty(value = "创建人所属部门编码")
    private String createDept;

    /**
     * 创建人所属部门名称
     */
    @Column(name = "create_dept_name")
    @ApiModelProperty(value = "创建人所属部门名称")
    private String createDeptName;

    /**
     * 更新人账号
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人账号")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标记
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标记")
    private String isDeleted;

    /**
     * 机构编码
     */
    @Column(name = "sso_org_code")
    @ApiModelProperty(value = "机构编码")
    private String ssoOrgCode;

    /**
     * 机构名称
     */
    @Column(name = "sso_org_name")
    @ApiModelProperty(value = "机构名称")
    private String ssoOrgName;
    
    @Transient
    @ApiModelProperty(value = "性别文本")
    private String sexText;
    
    @Transient
    @ApiModelProperty(value = "学历文本")
    private String educationText;
    
    @Transient
    @ApiModelProperty(value = "员工状态文本")
    private String employeeStatusText;
    
    @Transient
    @ApiModelProperty(value = "放射类别文本")
    private String radiationTypeText;
    
    @Transient
    @ApiModelProperty(value = "体检日期")
    private String checkupDate;
    
    @Transient
    @ApiModelProperty(value = "体检ID")
    private String checkupId;
    
    @Transient
    @ApiModelProperty(value = "体检状态:0异常,1正常")
    private String checkupStatus;
    
    @Transient
    @ApiModelProperty(value = "本次体检结果")
    private String checkupCurrentResult;
    
    @Transient
    @ApiModelProperty(value = "复检次数")
    private Integer checkupAbnormalCount;
    
    @Transient
    @ApiModelProperty(value = "最新复检结果：0-异常，1-正常")
    private String checkupLatestResult;
    
    @Transient
    @ApiModelProperty(value = "证书量")
    private Integer certificateCount;
    
    @Transient
    @ApiModelProperty(value = "培训次数")
    private Integer trainingCount;
    
    @Transient
    @ApiModelProperty(value = "剂量监测次数")
    private Integer doseCount;
    
    @Transient
    @ApiModelProperty(value = "体检次数")
    private Integer checkupCount;
    
    @Transient
    @ApiModelProperty(value = "体检到期日期")
    private String checkupEndDate;

    @Excel(name = "登记类别")
    @Transient
    @ApiModelProperty(value = "登记类别文本")
    private String registerCategoryText;

    @Excel(name = "登记类型")
    @Transient
    @ApiModelProperty(value = "登记类型文本")
    private String registerTypeText;
    
    @Excel(name = "监测周期")
    @Transient
    @ApiModelProperty(value = "监测周期文本")
    private String monitorCycleText;
    
    @Excel(name = "监测次数")
    @Transient
    @ApiModelProperty(value = "监测次数")
    private Integer monitorTimes;
    
    @Transient
    @ApiModelProperty(value = "监测设置列表-用于导入")
    private List<RadiateMonitor> monitorList;
    
    @Transient
    @ApiModelProperty(value = "身份证号")
    private String empIdcard;
    
//    @Transient
//    @ApiModelProperty(value = "培训到期日期")
//    private String trainingEndDate;
}