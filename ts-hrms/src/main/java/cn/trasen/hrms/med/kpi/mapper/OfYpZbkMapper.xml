<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.kpi.dao.OfYpZbkMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.med.kpi.model.OfYpZbk">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="BUSI_DATE" jdbcType="VARCHAR" property="busiDate" />
    <id column="BUSI_CODE" jdbcType="VARCHAR" property="busiCode" />
    <id column="YQDM" jdbcType="VARCHAR" property="yqdm" />
    <id column="KSDM" jdbcType="VARCHAR" property="ksdm" />
    <id column="YSDM" jdbcType="VARCHAR" property="ysdm" />
    <id column="DIM_TYPE" jdbcType="VARCHAR" property="dimType" />
    <id column="ITEM_CODE" jdbcType="VARCHAR" property="itemCode" />
    <result column="KSMC" jdbcType="VARCHAR" property="ksmc" />
    <result column="YSMC" jdbcType="VARCHAR" property="ysmc" />
    <result column="DIM_DEPT" jdbcType="VARCHAR" property="dimDept" />
    <result column="DIM_EMP" jdbcType="VARCHAR" property="dimEmp" />
    <result column="F_VAL" jdbcType="DECIMAL" property="fVal" />
    <result column="ITEM_TYPE" jdbcType="VARCHAR" property="itemType" />
    <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
    <result column="INSERT_TIME" jdbcType="TIMESTAMP" property="insertTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  
  	<select id="getYsTopByCode" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
		select  YSDM,YSMC,sum(F_VAL) as F_VAL  from  med_of_yp_zbk where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd}
		 	<!-- <if test="busiCode != null and busiCode != ''">
				 AND BUSI_CODE=#{busiCode} 
		 	</if> -->
		 		AND BUSI_CODE  IN
		 		<foreach collection="busiCodeList" item="busiCodeItem" open="(" separator="," close=")">
							#{busiCodeItem}
						</foreach> 
		 	<if test="yqdm != null and yqdm != ''">
				 AND YQDM=#{yqdm}
			</if>
			GROUP  BY  YSDM,YSMC   ORDER  BY  F_VAL  DESC
			<if test="ranking != null and ranking != ''">
				LIMIT  #{ranking}
			</if>
	  </select>
	  
	   <select id="getKsTopByCode" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
		select  KSDM,KSMC,sum(F_VAL) as F_VAL  from  med_of_yp_zbk where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd}
		 	<!-- <if test="busiCode != null and busiCode != ''">
				 AND BUSI_CODE=#{busiCode} 
		 	</if> -->
		 		AND BUSI_CODE  IN
		 		<foreach collection="busiCodeList" item="busiCodeItem" open="(" separator="," close=")">
							#{busiCodeItem}
						</foreach> 
		 	<if test="yqdm != null and yqdm != ''">
				 AND YQDM=#{yqdm}
			</if>
			GROUP  BY  KSDM,KSMC   ORDER  BY  F_VAL  DESC
			<if test="ranking != null and ranking != ''">
				LIMIT  #{ranking}
			</if>
	  </select>
	  
	   <select id="getYsTopByFzFmCode" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
	   select YSDM,YSMC,case when FM=0 then 0 else FZ/FM end as F_VAL  FROM (
		select  YSDM,YSMC,IFNULL(sum(case when BUSI_CODE IN
		<foreach collection="fzCodeList" item="fzCodeItem" open="(" separator="," close=")">
							#{fzCodeItem}
			</foreach> 
		then F_VAL else 0 end),0) as FZ,IFNULL(sum(case when BUSI_CODE  IN  
		<foreach collection="fmCodeList" item="fmCodeItem" open="(" separator="," close=")">
							#{fmCodeItem}
			</foreach> 
		then F_VAL else 0 end),0) as FM   from  med_of_yp_zbk 
				where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd}  and  BUSI_CODE  IN 
		 		<foreach collection="busiCodeList" item="busiCodeItem" open="(" separator="," close=")">
							#{busiCodeItem}
				</foreach> 
		 	<if test="yqdm != null and yqdm != ''">
				 AND YQDM=#{yqdm}
			</if>
			GROUP  BY  YSDM,YSMC
		)Z  ORDER BY F_VAL DESC 
			<if test="ranking != null and ranking != ''">
				LIMIT  #{ranking}
			</if>
	  </select>
	  
	 <select id="getKsTopByFzFmCode" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
	   select KSDM,KSMC,case when FM=0 then 0 else FZ/FM end as F_VAL  FROM (
		select  KSDM,KSMC,IFNULL(sum(case when BUSI_CODE IN 
		<foreach collection="fzCodeList" item="fzCodeItem" open="(" separator="," close=")">
							#{fzCodeItem}
			</foreach>
		then F_VAL else 0 end),0) as FZ,IFNULL(sum(case when BUSI_CODE IN 
		<foreach collection="fmCodeList" item="fmCodeItem" open="(" separator="," close=")">
							#{fmCodeItem}
			</foreach>
		then F_VAL else 0 end),0) as FM   from  med_of_yp_zbk 
				where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd}  and  BUSI_CODE  IN 
				<foreach collection="busiCodeList" item="busiCodeItem" open="(" separator="," close=")">
							#{busiCodeItem}
				</foreach>
		 	<if test="yqdm != null and yqdm != ''">
				 AND YQDM=#{yqdm}
			</if>
			GROUP  BY  KSDM,KSMC
		)Z  ORDER BY F_VAL DESC 
			<if test="ranking != null and ranking != ''">
				LIMIT  #{ranking}
			</if>
	  </select>
	  
	  <select id="getKsYpZb" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
		SELECT t.* FROM 
		(
		select 
			KSDM,
			KSMC,
			CF_HGS,
			CF_CCZS,
			CASE 
				WHEN CF_CCZS=0 THEN 0 
				ELSE CF_HGS/CF_CCZS 
			END AS CF_HGL,
			ZY_GEYCFYSJ,
		    KJYW_LJDDS,
		    HZ_ZYTSZH,
		    CASE 
		        WHEN HZ_ZYTSZH=0 THEN 0 
		        ELSE KJYW_LJDDS/HZ_ZYTSZH
		    END AS KJYW_SYQD, 
		    DLYP_ZRKJE,
		    QYYP_ZRKJE,
		    CASE 
		        WHEN QYYP_ZRKJE=0 THEN 0 
		        ELSE DLYP_ZRKJE/QYYP_ZRKJE 
		    END AS DLYP_CGL,
		    DLYP_ZSRJE,
		    QYYP_ZSRJE,
		    CASE 
		        WHEN QYYP_ZSRJE=0 THEN 0 
		        ELSE DLYP_ZSRJE/QYYP_ZSRJE
		    END AS DLYP_SYL,
		    ZDYP_XHJE,
		    ZDYP_XHJE_TQ,
		    CASE 
		        WHEN ZDYP_XHJE_TQ=0 THEN 0 
		        ELSE ZDYP_XHJE/ZDYP_XHJE_TQ 
		    END AS ZDYP_XHJE_ZF,
		    YP_XHJE,
		    PJ_KCJE,
		    CASE 
		        WHEN PJ_KCJE=0 THEN 0 
		        ELSE YP_XHJE/PJ_KCJE 
		    END AS YP_KCZZL,
		    CYPSMS_SYZBL,
		    CYPSMS_SYZBL_CCZS,
		    CASE 
		        WHEN CYPSMS_SYZBL_CCZS=0 THEN 0 
		        ELSE CYPSMS_SYZBL/CYPSMS_SYZBL_CCZS 
		    END AS CYPSMS_SYZBL_ZB,
		    YP_ZSR,
		    ZSR,
		    CASE 
		        WHEN ZSR=0 THEN 0 
		        ELSE YP_ZSR/ZSR 
		    END AS YP_ZSR_ZB,
		    MZ_DCFS,
		    MZ_YP_ZSR,
		    MZ_ZSR,
		    CASE 
		        WHEN MZ_ZSR=0 THEN 0 
		        ELSE MZ_YP_ZSR/MZ_ZSR 
		    END AS MZ_YP_ZSR_ZB,
		    ZY_YP_ZSR,
		    ZY_ZSR,
		    CASE
		        WHEN ZY_ZSR=0 THEN 0 
		        ELSE ZY_YP_ZSR/ZY_ZSR 
		    END AS ZY_YP_ZSR_ZB,
		    GZHZ_SYFW_HGS,
		    GZHZ_SYFW_ZS,
		    CASE 
		        WHEN GZHZ_SYFW_ZS=0 THEN 0 
		        ELSE GZHZ_SYFW_HGS/GZHZ_SYFW_ZS 
		    END AS GZHZ_SYFW_FHL,
		    MZ_CYPCF,
		    KZLYWXH,
		    CSYZYY  
		        FROM (
					select  
				  		KSDM,
						KSMC,
						IFNULL(SUM(CASE WHEN BUSI_CODE='10014' THEN F_VAL else 0 end),0) CF_HGS,
						IFNULL(SUM(CASE WHEN BUSI_CODE='10015' THEN F_VAL else 0 end),0) CF_CCZS,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10005' THEN F_VAL else 0 end),0) ZY_GEYCFYSJ,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10016' THEN F_VAL else 0 end),0) KJYW_LJDDS,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10017' THEN F_VAL else 0 end),0) HZ_ZYTSZH,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10008' THEN F_VAL else 0 end),0) DLYP_ZRKJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10009' THEN F_VAL else 0 end),0) QYYP_ZRKJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10010' THEN F_VAL else 0 end),0) DLYP_ZSRJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10011' THEN F_VAL else 0 end),0) QYYP_ZSRJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10018' THEN F_VAL else 0 end),0) ZDYP_XHJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10019' THEN F_VAL else 0 end),0) ZDYP_XHJE_TQ,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10012' THEN F_VAL else 0 end),0) YP_XHJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10013' THEN F_VAL else 0 end),0) PJ_KCJE,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10020' THEN F_VAL else 0 end),0) CYPSMS_SYZBL,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10021' THEN F_VAL else 0 end),0) CYPSMS_SYZBL_CCZS,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10001','20001') THEN F_VAL else 0 end),0) YP_ZSR,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10002','20002') THEN F_VAL else 0 end),0) ZSR,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10001') THEN F_VAL else 0 end),0) MZ_YP_ZSR,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10002') THEN F_VAL else 0 end),0) MZ_ZSR,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('20001') THEN F_VAL else 0 end),0) ZY_YP_ZSR,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('20002') THEN F_VAL else 0 end),0) ZY_ZSR,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10006' THEN F_VAL else 0 end),0) MZ_DCFS,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10022' THEN F_VAL else 0 end),0) GZHZ_SYFW_HGS,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10023' THEN F_VAL else 0 end),0) GZHZ_SYFW_ZS,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10024' THEN F_VAL else 0 end),0) MZ_CYPCF,
			            IFNULL(SUM(CASE WHEN BUSI_CODE='10007' THEN F_VAL else 0 end),0) KZLYWXH,
			            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10004','20004') THEN F_VAL else 0 end),0) CSYZYY
		       	   from  med_of_yp_zbk 
				where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd} 
				<if test="yqdm != null and yqdm != ''">
				 	AND YQDM=#{yqdm}
				</if>
				<if test="ksmc != null and ksmc != ''">
				 	AND KSMC  like  concat('%',#{ksmc},'%')   
				</if>
				and  BUSI_CODE  IN ('10001','10002','10003','10004','10005','10006','10007','10008','10009','10010','10011','10012','10013','10014','10015','10016','10017','10018','10019','10020','10021','10022','10023','10024','20001','20002','20004')
		             GROUP  BY   KSDM,KSMC
		)Z
		<if test="sortId != null and sortId != ''">
			order by  ${sortId}   ${sortWay}   
		</if>
		) t
		WHERE  ZY_GEYCFYSJ > 0 OR YP_ZSR_ZB > 0 OR MZ_YP_ZSR_ZB > 0 OR ZY_YP_ZSR_ZB > 0 OR MZ_DCFS > 0 
			OR CYPSMS_SYZBL > 0 OR DLYP_CGL > 0 OR DLYP_SYL > 0 OR CF_HGL > 0 OR KZLYWXH > 0 OR MZ_CYPCF > 0
			OR KJYW_SYQD > 0 OR CSYZYY > 0
	  </select>
	  
	  <select id="getYsYpZb" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
		select YSDM,YSMC,CF_HGS,CF_CCZS,CASE WHEN CF_CCZS=0 THEN 0 ELSE CF_HGS/CF_CCZS END AS CF_HGL,ZY_GEYCFYSJ,
		        CYPSMS_SYZBL,YP_ZSR,ZSR,CASE WHEN ZSR=0 THEN 0 ELSE YP_ZSR/ZSR END AS YP_ZSR_ZB,MZ_DCFS,
		        MZ_YP_ZSR,MZ_ZSR,CASE WHEN MZ_ZSR=0 THEN 0 ELSE MZ_YP_ZSR/MZ_ZSR END AS MZ_YP_ZSR_ZB,
		        ZY_YP_ZSR,ZY_ZSR,CASE WHEN ZY_ZSR=0 THEN 0 ELSE ZY_YP_ZSR/ZY_ZSR END AS ZY_YP_ZSR_ZB,
		        MZ_CYPCF,CSYZYY  FROM (
			select  YSDM,YSMC,IFNULL(SUM(CASE WHEN BUSI_CODE='10014' THEN F_VAL else 0 end),0) CF_HGS,IFNULL(SUM(CASE WHEN BUSI_CODE='10015' THEN F_VAL else 0 end),0) CF_CCZS,
		                IFNULL(SUM(CASE WHEN BUSI_CODE='10005' THEN F_VAL else 0 end),0) ZY_GEYCFYSJ,
		                IFNULL(SUM(CASE WHEN BUSI_CODE='10020' THEN F_VAL else 0 end),0) CYPSMS_SYZBL,
		                IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10001','20001') THEN F_VAL else 0 end),0) YP_ZSR,IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10002','20002') THEN F_VAL else 0 end),0) ZSR,
		                IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10001') THEN F_VAL else 0 end),0) MZ_YP_ZSR,IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10002') THEN F_VAL else 0 end),0) MZ_ZSR,
		                IFNULL(SUM(CASE WHEN BUSI_CODE IN ('20001') THEN F_VAL else 0 end),0) ZY_YP_ZSR,IFNULL(SUM(CASE WHEN BUSI_CODE IN ('20002') THEN F_VAL else 0 end),0) ZY_ZSR,
		                IFNULL(SUM(CASE WHEN BUSI_CODE='10006' THEN F_VAL else 0 end),0) MZ_DCFS,
		                IFNULL(SUM(CASE WHEN BUSI_CODE='10024' THEN F_VAL else 0 end),0) MZ_CYPCF,
		                IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10004','20004') THEN F_VAL else 0 end),0) CSYZYY
		                from  med_of_yp_zbk 
				where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd} 
				<if test="yqdm != null and yqdm != ''">
				 	AND YQDM=#{yqdm}
				</if>
				<if test="ysmc != null and ysmc != ''">
				 	AND ysmc  like  concat('%',#{ysmc},'%')   
				</if>
				and  BUSI_CODE  IN ('10001','10002','10004','10005','10006','10014','10015','10020','10024','20001','20002','20004')
		             GROUP  BY   YSDM,YSMC
		)Z
		<if test="sortId != null and sortId != ''">
			order by  ${sortId}   ${sortWay}   
		</if>
	  </select>
	  
	   <select id="getYpZbSumAll" resultType="Map"  parameterType="cn.trasen.hrms.med.kpi.model.OfYpZbk">
		select	
			CF_HGS,
			CF_CCZS,
			CASE 
				WHEN CF_CCZS=0 THEN 0 
				ELSE CF_HGS/CF_CCZS 
			END AS CF_HGL,
			ZY_GEYCFYSJ,
		    KJYW_LJDDS,
		    HZ_ZYTSZH,
		    CASE 
			    WHEN HZ_ZYTSZH=0 THEN 0
			    ELSE KJYW_LJDDS/HZ_ZYTSZH
			END AS KJYW_SYQD,
		    DLYP_ZRKJE,
		    QYYP_ZRKJE,
		    CASE 
			    WHEN QYYP_ZRKJE=0 THEN 0 
			    ELSE DLYP_ZRKJE/QYYP_ZRKJE 
			END AS DLYP_CGL,
		    DLYP_ZSRJE,
		    QYYP_ZSRJE,
		    CASE 
			    WHEN QYYP_ZSRJE=0 THEN 0 
				ELSE DLYP_ZSRJE/QYYP_ZSRJE 
			END AS DLYP_SYL,
		    ZDYP_XHJE,
		    ZDYP_XHJE_TQ,
		    CASE 
			    WHEN ZDYP_XHJE_TQ=0 THEN 0 
			    ELSE ZDYP_XHJE/ZDYP_XHJE_TQ 
			END AS ZDYP_XHJE_ZF,
		    YP_XHJE,
		    PJ_KCJE,
		    CASE 
			    WHEN PJ_KCJE=0 THEN 0 
			    ELSE YP_XHJE/PJ_KCJE 
			END AS YP_KCZZL,
		    CYPSMS_SYZBL,
		    CYPSMS_SYZBL_CCZS,
		    CASE 
			    WHEN CYPSMS_SYZBL_CCZS=0 THEN 0 
			    ELSE CYPSMS_SYZBL/CYPSMS_SYZBL_CCZS 
			END AS CYPSMS_SYZBL_ZB,
		    YP_ZSR,
		    ZSR,
		    CASE 
			    WHEN ZSR=0 THEN 0 
			    ELSE YP_ZSR/ZSR 
			END AS YP_ZSR_ZB,
			MZ_DCFS,
		    MZ_YP_ZSR,
		    MZ_ZSR,
		    CASE 
			    WHEN MZ_ZSR=0 THEN 0 
			    ELSE MZ_YP_ZSR/MZ_ZSR 
			END AS MZ_YP_ZSR_ZB,
		    ZY_YP_ZSR,
		    ZY_ZSR,
		    CASE 
			    WHEN ZY_ZSR=0 THEN 0 
		    	ELSE ZY_YP_ZSR/ZY_ZSR 
		    END AS ZY_YP_ZSR_ZB,
		    GZHZ_SYFW_HGS,
		    GZHZ_SYFW_ZS,
		    CASE 
			    WHEN GZHZ_SYFW_ZS=0 THEN 0 
			    ELSE GZHZ_SYFW_HGS/GZHZ_SYFW_ZS 
			END AS GZHZ_SYFW_FHL,
			MZ_CYPCF,
			KZLYWXH,
			CSYZYY  
		FROM (
			select  	
				IFNULL(SUM(CASE WHEN BUSI_CODE='10014' THEN F_VAL else 0 end),0) CF_HGS,
				IFNULL(SUM(CASE WHEN BUSI_CODE='10015' THEN F_VAL else 0 end),0) CF_CCZS,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10005' THEN F_VAL else 0 end),0) ZY_GEYCFYSJ,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10016' THEN F_VAL else 0 end),0) KJYW_LJDDS,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10017' THEN F_VAL else 0 end),0) HZ_ZYTSZH,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10008' THEN F_VAL else 0 end),0) DLYP_ZRKJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10009' THEN F_VAL else 0 end),0) QYYP_ZRKJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10010' THEN F_VAL else 0 end),0) DLYP_ZSRJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10011' THEN F_VAL else 0 end),0) QYYP_ZSRJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10018' THEN F_VAL else 0 end),0) ZDYP_XHJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10019' THEN F_VAL else 0 end),0) ZDYP_XHJE_TQ,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10012' THEN F_VAL else 0 end),0) YP_XHJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10013' THEN F_VAL else 0 end),0) PJ_KCJE,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10020' THEN F_VAL else 0 end),0) CYPSMS_SYZBL,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10021' THEN F_VAL else 0 end),0) CYPSMS_SYZBL_CCZS,
	            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10001','20001') THEN F_VAL else 0 end),0) YP_ZSR,
	            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10002','20002') THEN F_VAL else 0 end),0) ZSR,
	            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10001') THEN F_VAL else 0 end),0) MZ_YP_ZSR,
	            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10002') THEN F_VAL else 0 end),0) MZ_ZSR,
	            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('20001') THEN F_VAL else 0 end),0) ZY_YP_ZSR,
	            IFNULL(SUM(CASE WHEN BUSI_CODE IN ('20002') THEN F_VAL else 0 end),0) ZY_ZSR,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10006' THEN F_VAL else 0 end),0) MZ_DCFS,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10022' THEN F_VAL else 0 end),0) GZHZ_SYFW_HGS,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10023' THEN F_VAL else 0 end),0) GZHZ_SYFW_ZS,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10024' THEN F_VAL else 0 end),0) MZ_CYPCF,
	            IFNULL(SUM(CASE WHEN BUSI_CODE='10007' THEN F_VAL else 0 end),0) KZLYWXH,
            	IFNULL(SUM(CASE WHEN BUSI_CODE IN ('10004','20004') THEN F_VAL else 0 end),0) CSYZYY
		                from  med_of_yp_zbk 
				where  1=1  and BUSI_DATE BETWEEN  #{busiDateBegin} AND #{busiDateEnd} 
				<if test="yqdm != null and yqdm != ''">
				 	AND YQDM=#{yqdm}
				</if>
				and  BUSI_CODE  IN ('10001','10002','10003','10004','10005','10006','10007','10008','10009','10010','10011','10012','10013','10014','10015','10016','10017','10018','10019','10020','10021','10022','10023','10024','20001','20002','20004')
		           
		)Z
	  </select>
</mapper>