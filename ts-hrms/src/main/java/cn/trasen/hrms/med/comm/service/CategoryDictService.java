package cn.trasen.hrms.med.comm.service;

import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.hrms.med.comm.model.CategoryDict;

/**
 * @ClassName CommCategoryDictService
 * @Description TODO
 * @date 2024��11��29�� ����11:39:00
 * <AUTHOR>
 * @version 1.0
 */
public interface CategoryDictService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2024��11��29�� ����11:39:00
	 * <AUTHOR>
	 */
	Integer save(CategoryDict record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2024��11��29�� ����11:39:00
	 * <AUTHOR>
	 */
	Integer update(CategoryDict record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2024��11��29�� ����11:39:00
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CommCategoryDict
	 * @date 2024��11��29�� ����11:39:00
	 * <AUTHOR>
	 */
	CategoryDict selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CommCategoryDict>
	 * @date 2024��11��29�� ����11:39:00
	 * <AUTHOR>
	 */
	DataSet<CategoryDict> getDataSetList(Page page, CategoryDict record);
	
	List<CategoryDict> getTreeBycodeAndLv(CategoryDict record);
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据字典编码获取一个状态正常的字典
	  -- 作者: GW
	  -- 创建时间: 2024年12月2日
	  -- @param code
	  -- @return
	  -- =============================================
	 */
	CategoryDict getOneNormalByCode(String code);
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 根据字典编码，获取状态正常的 like'code%'
	  -- 作者: GW
	  -- 创建时间: 2024年12月2日
	  -- @param code
	  -- @return
	  -- =============================================
	 */
	List<CategoryDict> getNormalsByCode(String code);
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 判断是否有权限
	  -- 作者: GW
	  -- 创建时间: 2024年12月20日
	  -- @param authType  1查看权限2操作权限
	  -- @param code  字典编码
	  -- @return
	  -- =============================================
	 */
	boolean isAuth(String authType,String code);
}
