package cn.trasen.hrms.med.cslt.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.dao.HrmsOrganizationMapper;
import cn.trasen.hrms.med.comm.model.FieldMapping;
import cn.trasen.hrms.med.comm.service.FieldMappingService;
import cn.trasen.hrms.med.cslt.dao.CsltAppyMapper;
import cn.trasen.hrms.med.cslt.dao.CsltScduDetlMapper;
import cn.trasen.hrms.med.cslt.dao.CsltScduMapper;
import cn.trasen.hrms.med.cslt.model.CsltAppy;
import cn.trasen.hrms.med.cslt.model.CsltAppySyncHis;
import cn.trasen.hrms.med.cslt.service.CsltAppyService;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;
import cn.trasen.hrms.model.CustEmpBase;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsOrganization;
import cn.trasen.hrms.service.CustEmpBaseService;
import cn.trasen.hrms.utils.CommonUtils;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedCsltAppyServiceImpl
 * @Description TODO
 * @date 2024��11��17�� ����10:28:07
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Log4j2
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CsltAppyServiceImpl implements CsltAppyService {

	@Autowired
	private CsltAppyMapper mapper;
	@Autowired
	DictItemFeignService dictItemFeignService;
	@Autowired
	CsltScduMapper scduMapper;
	@Autowired
	CsltScduDetlMapper scduDetlMapper;
	
	@Autowired
	HrmsOrganizationMapper hrmsOrganizationMapper;
	@Autowired
	CustEmpBaseService custEmpBaseService;
	@Autowired
	FieldMappingService fieldMappingService;
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(CsltAppy record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setCreateDept(user.getDeptcode());
			record.setCreateDeptName(user.getDeptname());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getDeptcode());
			record.setSsoOrgName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}
	@Transactional(readOnly = false)
	@Override
	public Integer update(CsltAppy record) {
		CsltAppy old = mapper.selectByPrimaryKey(record);
		if(null != old){
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			String actStatus = old.getActStatus();
			//取消安排   
			if("0".equals(record.getActStatus())){
				if(!"1".equals(actStatus)){
					throw new RuntimeException(StrUtil.format("该条会诊申请信息的安排状态为{}，不能撤销!", actStatus));
				}
				old.setActStatus("0");
				old.setIsActOt("0");
				old.setIsActAbn("0");
				old.setActTime(null);
				old.setActUser(null);
				old.setActUserName(null);
				old.setActEmployeeId(null);
				old.setActEmployeeNo(null);
				old.setActEmployeeName(null);
				old.setActJobtitle(null);
				old.setActTel(null);
				old.setActStartTime(null);
				old.setActEndTime(null);
				old.setNotcFlag(null);
			}else if("1".equals(record.getActStatus())){//安排： 自动判断是否超时
				if(!"0".equals(actStatus)){
					throw new RuntimeException(StrUtil.format("该条会诊申请信息的安排状态为{}，不能安排!", actStatus));
				}
				//两个时间相差的小时数， 小的在前，大的在后
				Long l = DateUtil.between(old.getAppyTime(),DateUtil.date(), DateUnit.HOUR,false);
				old.setActStatus("1");
				old.setIsActOt(l > 24 ? "1" : "0");
				List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
				if(CollUtil.isNotEmpty(lvs)){
					DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(old.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
					if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
						//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
						old.setIsActAbn(StrUtil.startWith(lv.getItemName(), StrUtil.replace(record.getActJobtitle(), "正主任", "主任")) ? "0" : "1"); 
					}
				}
				old.setActTime(new Date());
				old.setActEmployeeId(record.getActEmployeeId());
				old.setActEmployeeNo(record.getActEmployeeNo());
				old.setActEmployeeName(record.getActEmployeeName());
				old.setActJobtitle(record.getActJobtitle());
				old.setActTel(record.getActTel());
				old.setActStartTime(record.getActStartTime());
				old.setActEndTime(record.getActEndTime());
				old.setNotcFlag(record.getNotcFlag());
				if (user != null) {
					old.setActUser(user.getUsercode());
					old.setActUserName(user.getUsername());
					old.setActUser(user.getUsercode());
					old.setActUserName(user.getUsername());
				}
			}
			old.setUpdateDate(new Date());
			if (user != null) {
				old.setUpdateUser(user.getUsercode());
				old.setUpdateUserName(user.getUsername());
			}
			return mapper.updateByPrimaryKeySelective(old);
		}else{
			return 0;
		}
	}
	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CsltAppy record = new CsltAppy();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CsltAppy selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CsltAppy record = mapper.selectByPrimaryKey(id);
		if(null != record){
			List<DictItemResp> types = dictItemFeignService.getDictItemByTypeCode("cslt_type").getObject();
			List<DictItemResp> molds = dictItemFeignService.getDictItemByTypeCode("cslt_mold").getObject();
			List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
			List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
			DictItemResp type = types.stream().filter(j -> StrUtil.equals(record.getCsltType(), j.getItemCode())).findFirst().orElse(null);
			record.setCsltTypeName(null == type ? record.getCsltType() : type.getItemName());
			DictItemResp mold = molds.stream().filter(j -> StrUtil.equals(record.getCsltMold(), j.getItemCode())).findFirst().orElse(null);
			record.setCsltMoldName(null == mold ? record.getCsltMold() : mold.getItemName());
			DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(record.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
			record.setCsltLvName(null == lv ? record.getCsltLv() : lv.getItemName());
			DictItemResp area = areas.stream().filter(j -> StrUtil.equals(record.getHospArea(), j.getItemCode())).findFirst().orElse(null);
			record.setHospAreaName(null == area ? record.getHospArea() : area.getItemName());
		}
		return record;
	}
	@Override
	public DataSet<CsltAppy> getDataSetList(Page page, CsltAppy record) {
		Example example = new Example(CsltAppy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StrUtil.isNotEmpty(record.getCsltType())){
			criteria.andEqualTo("csltType", record.getCsltType());
		}
		if(StrUtil.isNotEmpty(record.getCsltMold())){
			criteria.andEqualTo("csltMold", record.getCsltMold());
		}
		if(StrUtil.isNotEmpty(record.getCsltLv())){
			criteria.andEqualTo("csltLv", record.getCsltLv());
		}
		if(StrUtil.isNotEmpty(record.getHospArea())){
			criteria.andEqualTo("hospArea", record.getHospArea());
		}
		if(StrUtil.isNotEmpty(record.getActStatus())){
			criteria.andIn("actStatus", Arrays.asList(record.getActStatus().split(",")));
		}
		if(StrUtil.isNotEmpty(record.getCsltStatus())){
			criteria.andIn("csltStatus", Arrays.asList(record.getCsltStatus().split(",")));
		}
		if(StrUtil.isNotEmpty(record.getCsltOrgId())){
			criteria.andEqualTo("csltOrgId", record.getCsltOrgId());
		}
		if(ObjectUtil.isNotEmpty(record.getCsltTime())){
			criteria.andEqualTo("csltTime", record.getCsltTime());
		}
		List<CsltAppy> records = mapper.selectByExampleAndRowBounds(example, page);
		if(CollUtil.isNotEmpty(records)){
			records = fillDict(records);
			records.forEach(i -> {
				i.setRemainingHour(24 - DateUtil.between(DateUtil.date(),i.getAppyTime(), DateUnit.HOUR,true));
				i.setPlanCsltTime(StrUtil.format("{}至{}", DateUtil.format(i.getActStartTime(), "MM-dd HH:mm"),DateUtil.format(i.getActEndTime(), "MM-dd HH:mm")));
			});
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * 
	  -- =============================================
	  -- 功能描述: 定时更新申请单的  是否安排超时、是否会诊超时 状态
	  -- 作者: GW
	  -- 创建时间: 2024年11月30日
	  -- 
	  -- =============================================
	 */
	@Transactional(readOnly = false)
	@Override
	public void updtIsOtStatus() {
		Example example = new Example(CsltAppy.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andCondition(" ((act_status = '0' and is_act_ot = '0') or (cslt_status = '0' and is_cslt_ot = '0'))");
		criteria.andIsNotNull("appyTime");
		List<CsltAppy> appys = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(appys)){
			Date now = DateUtil.date();
			List<String> actLIds = appys.stream()
					.filter(i -> "0".equals(i.getActStatus()) && "0".equals(i.getIsActOt()) && DateUtil.between(i.getAppyTime(),now, DateUnit.HOUR,false) > 24)
						.map(i -> i.getId()).collect(Collectors.toList());
			if(CollUtil.isNotEmpty(actLIds)){
				mapper.updtIsActOtStatus(actLIds);
			}
			List<String> csltIds = appys.stream()
					.filter(i -> "0".equals(i.getCsltStatus()) && "0".equals(i.getIsCsltOt()) && DateUtil.between(i.getAppyTime(),now, DateUnit.HOUR,false) > 24)
						.map(i -> i.getId()).collect(Collectors.toList());
			if(CollUtil.isNotEmpty(csltIds)){
				mapper.updtIsCsltOtStatus(csltIds);
			}
		}
	}
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 将传入的申请信息，填充字典值
	  -- 作者: GW
	  -- 创建时间: 2024年12月28日
	  -- @param appys
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<CsltAppy> fillDict(List<CsltAppy> appys){
		List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
		List<DictItemResp> types = dictItemFeignService.getDictItemByTypeCode("cslt_type").getObject();
		List<DictItemResp> molds = dictItemFeignService.getDictItemByTypeCode("cslt_mold").getObject();
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		//添加会诊评分字典
		List<DictItemResp> hzmdScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzmd").getObject();
		List<DictItemResp> hzzlScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzzl").getObject();
		List<DictItemResp> hzbyxScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzbyx").getObject();
		List<DictItemResp> hzmddfScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzmddf").getObject();
		List<DictItemResp> hzyjScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzyj").getObject();
		
		if(CollUtil.isNotEmpty(appys)){
			appys.forEach(i -> {
				DictItemResp type = types.stream().filter(j -> StrUtil.equals(i.getCsltType(), j.getItemCode())).findFirst().orElse(null);
				i.setCsltTypeName(null == type ? i.getCsltType() : type.getItemName());
				DictItemResp mold = molds.stream().filter(j -> StrUtil.equals(i.getCsltMold(), j.getItemCode())).findFirst().orElse(null);
				i.setCsltMoldName(null == mold ? i.getCsltMold() : mold.getItemName());
				DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(i.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
				i.setCsltLvName(null == lv ? i.getCsltLv() : lv.getItemName());
				DictItemResp area = areas.stream().filter(j -> StrUtil.equals(i.getHospArea(), j.getItemCode())).findFirst().orElse(null);
				i.setHospAreaName(null == area ? i.getHospArea() : area.getItemName());
				i.setActStatusName(StrUtil.equals(i.getActStatus(), "1") ? "已安排" : "未安排");
				i.setCsltStatusName(StrUtil.equals(i.getCsltStatus(), "1") ? "已会诊" : "未会诊");
				//添加会诊评分字典
				DictItemResp hzmdScore = hzmdScoreList.stream().filter(j -> StrUtil.equals(i.getHzmdScore(), j.getItemCode())).findFirst().orElse(null);
				i.setHzmdScoreText(null == hzmdScore ? i.getHzmdScore() : hzmdScore.getItemName());
				DictItemResp hzzlScore = hzzlScoreList.stream().filter(j -> StrUtil.equals(i.getHzzlScore(), j.getItemCode())).findFirst().orElse(null);
				i.setHzzlScoreText(null == hzzlScore ? i.getHzzlScore() : hzzlScore.getItemName());
				DictItemResp hzbyxScore = hzbyxScoreList.stream().filter(j -> StrUtil.equals(i.getHzbyxScore(), j.getItemCode())).findFirst().orElse(null);
				i.setHzbyxScoreText(null == hzbyxScore ? i.getHzbyxScore() : hzbyxScore.getItemName());
				DictItemResp hzmddfScore = hzmddfScoreList.stream().filter(j -> StrUtil.equals(i.getHzmddfScore(), j.getItemCode())).findFirst().orElse(null);
				i.setHzmddfScoreText(null == hzmddfScore ? i.getHzmddfScore() : hzmddfScore.getItemName());
				DictItemResp hzyjScore = hzyjScoreList.stream().filter(j -> StrUtil.equals(i.getHzyjScore(), j.getItemCode())).findFirst().orElse(null);
				i.setHzyjScoreText(null == hzyjScore ? i.getHzyjScore() : hzyjScore.getItemName());
			});
		}
		return appys;
	}
	
	@Override
	public CsltAppy fillDictAppy(CsltAppy appy){
		List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
		List<DictItemResp> types = dictItemFeignService.getDictItemByTypeCode("cslt_type").getObject();
		List<DictItemResp> molds = dictItemFeignService.getDictItemByTypeCode("cslt_mold").getObject();
		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		//添加会诊评分字典
		List<DictItemResp> hzmdScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzmd").getObject();
		List<DictItemResp> hzzlScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzzl").getObject();
		List<DictItemResp> hzbyxScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzbyx").getObject();
		List<DictItemResp> hzmddfScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzmddf").getObject();
		List<DictItemResp> hzyjScoreList = dictItemFeignService.getDictItemByTypeCode("cslt_hzyj").getObject();
		
		if(appy != null){
				DictItemResp type = types.stream().filter(j -> StrUtil.equals(appy.getCsltType(), j.getItemCode())).findFirst().orElse(null);
				appy.setCsltTypeName(null == type ? appy.getCsltType() : type.getItemName());
				DictItemResp mold = molds.stream().filter(j -> StrUtil.equals(appy.getCsltMold(), j.getItemCode())).findFirst().orElse(null);
				appy.setCsltMoldName(null == mold ? appy.getCsltMold() : mold.getItemName());
				DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(appy.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
				appy.setCsltLvName(null == lv ? appy.getCsltLv() : lv.getItemName());
				DictItemResp area = areas.stream().filter(j -> StrUtil.equals(appy.getHospArea(), j.getItemCode())).findFirst().orElse(null);
				appy.setHospAreaName(null == area ? appy.getHospArea() : area.getItemName());
				appy.setActStatusName(StrUtil.equals(appy.getActStatus(), "1") ? "已安排" : "未安排");
				appy.setCsltStatusName(StrUtil.equals(appy.getCsltStatus(), "1") ? "已会诊" : "未会诊");
				//添加会诊评分字典
				DictItemResp hzmdScore = hzmdScoreList.stream().filter(j -> StrUtil.equals(appy.getHzmdScore(), j.getItemCode())).findFirst().orElse(null);
				appy.setHzmdScoreText(null == hzmdScore ? appy.getHzmdScore() : hzmdScore.getItemName());
				DictItemResp hzzlScore = hzzlScoreList.stream().filter(j -> StrUtil.equals(appy.getHzzlScore(), j.getItemCode())).findFirst().orElse(null);
				appy.setHzzlScoreText(null == hzzlScore ? appy.getHzzlScore() : hzzlScore.getItemName());
				DictItemResp hzbyxScore = hzbyxScoreList.stream().filter(j -> StrUtil.equals(appy.getHzbyxScore(), j.getItemCode())).findFirst().orElse(null);
				appy.setHzbyxScoreText(null == hzbyxScore ? appy.getHzbyxScore() : hzbyxScore.getItemName());
				DictItemResp hzmddfScore = hzmddfScoreList.stream().filter(j -> StrUtil.equals(appy.getHzmddfScore(), j.getItemCode())).findFirst().orElse(null);
				appy.setHzmddfScoreText(null == hzmddfScore ? appy.getHzmddfScore() : hzmddfScore.getItemName());
				DictItemResp hzyjScore = hzyjScoreList.stream().filter(j -> StrUtil.equals(appy.getHzyjScore(), j.getItemCode())).findFirst().orElse(null);
				appy.setHzyjScoreText(null == hzyjScore ? appy.getHzyjScore() : hzyjScore.getItemName());
		}
		return appy;
	}
	
	@Transactional(readOnly = false)
	@Override
	public void updateOrSaveCsltAppyByHis() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT A.ID AS CSLT_APPY_ID,B.ID AS  APPY_NO,A.\"LEVEL\" AS CSLT_TYPE,A.JZ_FLAG AS CSLT_MOLD,ORD.HOITEM_ID AS CSLT_LV,DEPT3.ZXKSMLID AS HOSP_AREA,B.CON_DEPT AS CSLT_ORG_ID,DEPT3.NAME AS CSLT_ORG_NAME,A.APPLY_DATE AS CSLT_TIME, "
    				+ "        INP.INPATIENT_ID AS PATN_ID,INP.NAME AS PATN_NAME,CASE WHEN INP.SEXCODE=1 THEN '男' WHEN  INP.SEXCODE=2 THEN '女' ELSE '未知' END AS PATN_GEND,TRUNC(MONTHS_BETWEEN(SYSDATE,INP.BIRTHDAY)/12) AS  PATN_AGE, "
    				+ "        BED.BED_NO AS PATN_BEDNO,INP.INPATIENT_NO AS PATN_INP_NO,DEPT2.NAME AS PATN_ORG_NAME,INP.IN_DIAGNOSIS AS PATN_ICD_NAME,NVL(ORD.BOOK_DATE,A.APPLY_DATE)  AS APPY_TIME,DEPT.NAME AS  APPY_ORG_NAME,EMP.NAME AS APPY_EMP_NAME,EMP.HOME_TEL AS APPY_TEL, "
    				+ "        A.CONTENT AS ILLHIS,A.INTENT AS PUP,CASE WHEN PLAN_CON_DATE IS NULL THEN 0 ELSE 1 END AS  ACT_STATUS,CASE WHEN CON_DATE IS NULL THEN 0 ELSE 1 END AS  CSLT_STATUS,PLAN_CON_DATE AS ACT_TIME,B.CON_DOC AS ACT_USER,EMP2.NAME AS ACT_USER_NAME,B.ARR_CON_DOC AS ACT_EMPLOYEE_ID, "
    				+ "        EMP3.NAME AS ACT_EMPLOYEE_NAME,B.ARR_CON_DOC AS ACT_EMPLOYEE_NO,EMP3.TECHNICAL_NAME AS ACT_JOBTITLE,EMP3.HOME_TEL AS  ACT_TEL,CASE WHEN PLAN_CON_SJD IS NULL THEN NULL ELSE TO_CHAR(B.PLAN_CON_DATE,'YYYY-MM-DD') || ' ' || SUBSTR(B.PLAN_CON_SJD,1,5)  || ':00' END AS ACT_START_TIME, "
    				+ "        CASE WHEN PLAN_CON_SJD IS NULL THEN NULL ELSE TO_CHAR(B.PLAN_CON_DATE,'YYYY-MM-DD') || ' ' || SUBSTR(B.PLAN_CON_SJD,8,11)  || ':00' END AS ACT_END_TIME,B.CON_DATE AS FNS_TIME,B.CON_DOC AS FNS_EMPLOYEE_ID,EMP2.NAME AS FNS_EMPLOYEE_NAME, "
    				+ "        B.CON_DOC AS FNS_EMPLOYEE_NO,EMP2.TECHNICAL_NAME AS FNS_JOBTITLE,EMP2.HOME_TEL AS  FNS_TEL,B.CON_ASSESS_CONTENT AS APPY_ORG_DSCR,B.CONTENT AS CSLT_ORG_DSCR, CASE WHEN B.DELETE_BIT=1 THEN 'Y' ELSE 'N' END AS IS_DELETED,A.APPLY_DOC,A.DEPT_ID AS APPLY_ORG_ID,DEPT.ZXKSMLID AS APPY_HOSP_AREA,A.APPLY_DOC AS APPLY_EMP_ID,B.HZMD_SCORE,B.HZZL_SCORE,B.HZBYX_SCORE,B.HZMDDF_SCORE,B.HZYJ_SCORE,B.CON_DOC_TYPE  "
    				+ " ");
    		sb.append(" FROM ODSZYV10.ZY_CONSULTATION A LEFT JOIN ODSZYV10.ZY_CON_MX B ON A.ID=B.P_ID "
    				+ "         LEFT JOIN    ODSZYV10.ZY_ORDERRECORD ORD ON B.ORDER_ID=ORD.ORDER_ID "
    				+ "        LEFT JOIN  ODSZYV10.BASE_DEPT_PROPERTY DEPT ON A.DEPT_ID=DEPT.DEPT_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT2 ON A.DEPT_BR=DEPT2.DEPT_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT3 ON B.CON_DEPT=DEPT3.DEPT_ID "
    				+ "        LEFT JOIN ( SELECT  B.INPATIENT_ID,B.BED_ID,A.INPATIENT_NO,A.NAME,A.SEX AS SEXCODE,A.BIRTHDAY,B.IN_DIAGNOSIS  FROM ODSZYV10.BASE_PATIENT_PROPERTY A,ODSZYV10.ZY_INPATIENT B WHERE A.PATIENT_ID=B.PATIENT_ID) INP ON A.INPATIENT_ID=INP.INPATIENT_ID "
    				+ "        LEFT JOIN ODSZYV10.ZY_BEDDICTION BED ON  INP.BED_ID=BED.BED_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP ON A.APPLY_DOC=EMP.EMPLOYEE_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP2 ON B.CON_DOC=EMP2.EMPLOYEE_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP3 ON B.ARR_CON_DOC=EMP3.EMPLOYEE_ID ");
    		sb.append("  WHERE A.\"LEVEL\"=0 AND A.JZ_FLAG=0  "
    				+ "AND ( (A.APPLY_DATE >   TRUNC(SYSDATE -7))  OR (B.CON_DATE >  TRUNC(SYSDATE -7)) OR (B.ACCEPT_DATE >   TRUNC(SYSDATE -7)) "
    				+ "OR (B.CON_ASSESS_DATE >   TRUNC(SYSDATE -7)) OR (B.PLAN_CON_DATE >   TRUNC(SYSDATE -7)) "
    				+ ") ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<CsltAppySyncHis> CsltAppyList = HnsrmyyHisJdbcUtil.queryCsltAppySyncHis(sb.toString());//执行语句返回结果
    		log.info("===========CsltAppyList:"+CsltAppyList.size());
    		if(CollUtil.isNotEmpty(CsltAppyList)){
    			for(CsltAppySyncHis csltAppySyncHis : CsltAppyList) {
    				CsltAppy appy = mapper.selectByPrimaryKey(csltAppySyncHis.getCsltAppyId());//根据申请id查询是否已经存在申请单数据
    				
    				if(appy != null) {//存在申请数据，只更新安排、完成、评价字段
    					if(!StringUtil.isEmpty(csltAppySyncHis.getIsDeleted())) {
    						appy.setIsDeleted(csltAppySyncHis.getIsDeleted());
    					}
	    				//判断拉取的该数据是否已经安排，设置安排字段
	    				if("1".equals(csltAppySyncHis.getActStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getActEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 安排时间  相差的小时数
	    					Long l = DateUtil.between(DateUtil.parse(csltAppySyncHis.getAppyTime()),DateUtil.parse(csltAppySyncHis.getActTime()), DateUnit.HOUR,false);
	    					
	    					if(emp !=null) {
	    						appy.setActEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	        					appy.setActEmployeeName(emp.getEmployeeName());
	    					}else {
	    						appy.setActEmployeeId(csltAppySyncHis.getActEmployeeId()); //ID 未获取到本系统的，则存HIS的
	        					appy.setActEmployeeName(csltAppySyncHis.getActEmployeeName());
	    					}
	    					appy.setIsActOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							appy.setIsActAbn(StrUtil.startWith(lv.getItemName(), csltAppySyncHis.getActJobtitle()) ? "0" : "1"); 
	    						}
	    					}
	    					appy.setActUser(csltAppySyncHis.getActUser());
	    					appy.setActUserName(csltAppySyncHis.getActUserName());
	    					appy.setActStatus(csltAppySyncHis.getActStatus());
	    					appy.setActEmployeeNo(csltAppySyncHis.getActEmployeeNo());
	    					appy.setActJobtitle(csltAppySyncHis.getActJobtitle());
	    					appy.setActTel(csltAppySyncHis.getActTel());
	    					appy.setActStartTime(DateUtil.parse(csltAppySyncHis.getActStartTime(),"yyyy-MM-dd HH:mm:ss"));
	    					appy.setActEndTime(DateUtil.parse(csltAppySyncHis.getActEndTime(),"yyyy-MM-dd HH:mm:ss"));
	    				}
	    				
	    				//判断拉取的该数据是否已经完成，设置完成字段
	    				if("1".equals(csltAppySyncHis.getCsltStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getFnsEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 完成会诊时间  相差的小时数
	    					Long l = DateUtil.between(appy.getAppyTime(),DateUtil.parse(csltAppySyncHis.getFnsTime()), DateUnit.HOUR,false);
	    					if(emp !=null) {
	    						appy.setFnsEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	    						appy.setFnsEmployeeName(emp.getEmployeeName()); //
	    					}else {
	    						appy.setFnsEmployeeId(csltAppySyncHis.getFnsEmployeeId()); //ID  未获取到本系统的，则存HIS的
	    						appy.setFnsEmployeeName(csltAppySyncHis.getFnsEmployeeName()); //
	    					}
	    					appy.setIsCsltOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							String csltLv = csltAppySyncHis.getCsltLv(); //申请会诊级别 //29464 主任  29318 副主任   28841 主治
	    							String fnsJobtitle = csltAppySyncHis.getFnsJobtitle();  //完成会诊级别  主治医师 正主任医师  副主任医师
	    							if("29464".equals(csltLv) && ("副主任医师".equals(fnsJobtitle) || "主治医师".equals(fnsJobtitle))) {
	    								appy.setIsCsltAbn("1");
	    							}else if("29318".equals(csltLv) &&  "主治医师".equals(fnsJobtitle)) {
	    								appy.setIsCsltAbn("1");
	    							}else {
	    								appy.setIsCsltAbn("0");
	    							}
	    							//appy.setIsCsltAbn(StrUtil.startWith(lv.getItemName(), StrUtil.replace(csltAppySyncHis.getFnsJobtitle(), "正主任", "主任")) ? "0" : "1"); 
	    						}
	    					}
	    					appy.setCsltStatus(csltAppySyncHis.getCsltStatus());
	    					appy.setFnsTime(DateUtil.parse(csltAppySyncHis.getFnsTime(),"yyyy-MM-dd HH:mm:ss"));
	    					appy.setFnsEmployeeNo(csltAppySyncHis.getFnsEmployeeNo());
	    					appy.setFnsJobtitle(csltAppySyncHis.getFnsJobtitle());
	    					appy.setFnsTel(csltAppySyncHis.getFnsTel());
	    					appy.setAppyOrgDscr(csltAppySyncHis.getAppyOrgDscr());
	    					appy.setCsltOrgDscr(csltAppySyncHis.getCsltOrgDscr());
	    					appy.setHzmdScore(csltAppySyncHis.getHzmdScore());//评分
	    					appy.setHzzlScore(csltAppySyncHis.getHzzlScore());
	    					appy.setHzbyxScore(csltAppySyncHis.getHzbyxScore());
	    					appy.setHzmddfScore(csltAppySyncHis.getHzmddfScore());
	    					appy.setHzyjScore(csltAppySyncHis.getHzyjScore());
	    				}
	    				appy.setUpdateDate(DateUtil.date());
	    				appy.setConDocType(csltAppySyncHis.getConDocType());
	    				//appy.setUpdateUser(csltAppySyncHis.getAppyEmpId());
	    				//appy.setUpdateUserName(csltAppySyncHis.getAppyEmpName());
	    				mapper.updateByPrimaryKey(appy);
    				}else {
    					appy = new CsltAppy();
    					appy.setId(csltAppySyncHis.getCsltAppyId());
    					BeanUtil.copyProperties(csltAppySyncHis, appy);
    					
    					//将传入的科室ID转换为本系统的orgId
    					/*
    					List<String> orgIds = hrmsOrganizationMapper.convertOrg(csltAppySyncHis.getCsltOrgId());
    					*/
    					List<String> orgIds = hrmsOrganizationMapper.convertOrgBusiSystem(csltAppySyncHis.getCsltOrgId(),"HIS","2");
    					String orgId = CollUtil.isEmpty(orgIds) ? csltAppySyncHis.getCsltOrgId() : orgIds.get(0);
    					HrmsOrganization org = hrmsOrganizationMapper.selectByPrimaryKey(orgId);
    					if(org != null) {
    						appy.setCsltOrgId(org.getOrganizationId());//替换为本系统科室
        					appy.setCsltOrgName(org.getName());
    					}else {
    						appy.setCsltOrgId(csltAppySyncHis.getCsltOrgId());//没找到,存为his科室
        					appy.setCsltOrgName(csltAppySyncHis.getCsltOrgName());
    					}
    					
    					//将传入的申请科室ID转换为本系统的orgId
    					/*
    					List<String> appyOrgIds = hrmsOrganizationMapper.convertOrg(csltAppySyncHis.getAppyOrgId());
    					*/
    					List<String> appyOrgIds = hrmsOrganizationMapper.convertOrgBusiSystem(csltAppySyncHis.getAppyOrgId(),"HIS","2");
    					String appyOrgId = CollUtil.isEmpty(appyOrgIds) ? csltAppySyncHis.getAppyOrgId() : appyOrgIds.get(0);
    					HrmsOrganization appyOrg = hrmsOrganizationMapper.selectByPrimaryKey(appyOrgId);
    					if(appyOrg !=null) {
    						appy.setAppyOrgId(appyOrg.getOrganizationId());
    						appy.setAppyOrgName(appyOrg.getName());
    					}else {
    						appy.setAppyOrgId(csltAppySyncHis.getAppyOrgId());
    						appy.setAppyOrgName(csltAppySyncHis.getAppyOrgName());
    					}
    					
    					//判断拉取的该数据是否已经安排，设置安排字段
	    				if("1".equals(csltAppySyncHis.getActStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getActEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 安排时间  相差的小时数
	    					Long l = DateUtil.between(DateUtil.parse(csltAppySyncHis.getAppyTime()),DateUtil.parse(csltAppySyncHis.getActTime()), DateUnit.HOUR,false);
	    					
	    					if(emp !=null) {
	    						appy.setActEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	        					appy.setActEmployeeName(emp.getEmployeeName());
	    					}else {
	    						appy.setActEmployeeId(csltAppySyncHis.getActEmployeeId()); //ID 未获取到本系统的，则存HIS的
	        					appy.setActEmployeeName(csltAppySyncHis.getActEmployeeName());
	    					}
	    					appy.setIsActOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							appy.setIsActAbn(StrUtil.startWith(lv.getItemName(), csltAppySyncHis.getActJobtitle()) ? "0" : "1"); 
	    						}
	    					}
	    					//appy.setActStartTime(DateUtil.parse(csltAppySyncHis.getActStartTime(),"yyyy-MM-dd HH:mm:ss"));
	    					//appy.setActEndTime(DateUtil.parse(csltAppySyncHis.getActEndTime(),"yyyy-MM-dd HH:mm:ss"));
	    				}else {
	    					appy.setIsActOt("0");
	    					appy.setIsActAbn("0");
	    				}
	    				
	    				
	    				//判断拉取的该数据是否已经完成，设置完成字段
	    				if("1".equals(csltAppySyncHis.getCsltStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getFnsEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 完成会诊时间  相差的小时数
	    					Long l = DateUtil.between(appy.getAppyTime(),DateUtil.parse(csltAppySyncHis.getFnsTime()), DateUnit.HOUR,false);
	    					if(emp !=null) {
	    						appy.setFnsEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	    						appy.setFnsEmployeeName(emp.getEmployeeName()); //
	    					}else {
	    						appy.setFnsEmployeeId(csltAppySyncHis.getFnsEmployeeId()); //ID  未获取到本系统的，则存HIS的
	    						appy.setFnsEmployeeName(csltAppySyncHis.getFnsEmployeeName()); //
	    					}
	    					appy.setIsCsltOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							String csltLv = csltAppySyncHis.getCsltLv(); //申请会诊级别 //29464 主任  29318 副主任   28841 主治
	    							String fnsJobtitle = csltAppySyncHis.getFnsJobtitle();  //完成会诊级别  主治医师 正主任医师  副主任医师
	    							if("29464".equals(csltLv) && ("副主任医师".equals(fnsJobtitle) || "主治医师".equals(fnsJobtitle))) {
	    								appy.setIsCsltAbn("1");
	    							}else if("29318".equals(csltLv) &&  "主治医师".equals(fnsJobtitle)) {
	    								appy.setIsCsltAbn("1");
	    							}else {
	    								appy.setIsCsltAbn("0");
	    							}
	    							//appy.setIsCsltAbn(StrUtil.startWith(lv.getItemName(), StrUtil.replace(csltAppySyncHis.getFnsJobtitle(), "正主任", "主任")) ? "0" : "1"); 
	    						}
	    					}
	    					//appy.setFnsTime(DateUtil.parse(csltAppySyncHis.getFnsTime(),"yyyy-MM-dd HH:mm:ss"));
	    				}else {
	    					appy.setIsCsltOt("0");
	    					appy.setIsCsltAbn("0");
	    				}
    					
    					appy.setCsltTime(DateUtil.parse(csltAppySyncHis.getCsltTime(),"yyyy-MM-dd HH:mm:ss"));
    					appy.setCreateDate(DateUtil.date());
    					appy.setCreateUser(csltAppySyncHis.getAppyEmpId());
    					appy.setCreateUserName(csltAppySyncHis.getAppyEmpName());
    					appy.setCreateDept(csltAppySyncHis.getAppyOrgId());
    					appy.setCreateDeptName(csltAppySyncHis.getAppyOrgName());
    					appy.setUpdateDate(DateUtil.date());
    					appy.setUpdateUser(csltAppySyncHis.getAppyEmpId());
    					appy.setUpdateUserName(csltAppySyncHis.getAppyEmpName());
    					appy.setSsoOrgName("定时任务");
    					appy.setConDocType(csltAppySyncHis.getConDocType());
    					mapper.insert(appy);
    				}
    			}
    		}
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库数据异常：" + e.getMessage());
    	}
	}
	
	
	@Transactional(readOnly = false)
	@Override
	public void updateOrSaveCsltAppyByHis2() {
		// TODO Auto-generated method stub
		try {    		
			StringBuilder sb = new StringBuilder();
			
    		sb.append(" SELECT A.ID || '-' || B.ID AS CSLT_APPY_ID,B.ID AS  APPY_NO,A.\"LEVEL\" AS CSLT_TYPE,A.JZ_FLAG AS CSLT_MOLD,ORD.HOITEM_ID AS CSLT_LV,DEPT3.ZXKSMLID AS HOSP_AREA,B.CON_DEPT AS CSLT_ORG_ID,DEPT3.NAME AS CSLT_ORG_NAME,A.APPLY_DATE AS CSLT_TIME, "
    				+ "        INP.INPATIENT_ID AS PATN_ID,INP.NAME AS PATN_NAME,CASE WHEN INP.SEXCODE=1 THEN '男' WHEN  INP.SEXCODE=2 THEN '女' ELSE '未知' END AS PATN_GEND,TRUNC(MONTHS_BETWEEN(SYSDATE,INP.BIRTHDAY)/12) AS  PATN_AGE, "
    				+ "        BED.BED_NO AS PATN_BEDNO,INP.INPATIENT_NO AS PATN_INP_NO,DEPT2.NAME AS PATN_ORG_NAME,INP.IN_DIAGNOSIS AS PATN_ICD_NAME,NVL(ORD.BOOK_DATE,A.APPLY_DATE)  AS APPY_TIME,DEPT.NAME AS  APPY_ORG_NAME,EMP.NAME AS APPY_EMP_NAME,EMP.HOME_TEL AS APPY_TEL, "
    				+ "        A.CONTENT AS ILLHIS,A.INTENT AS PUP,CASE WHEN PLAN_CON_DATE IS NULL THEN 0 ELSE 1 END AS  ACT_STATUS,CASE WHEN CON_DATE IS NULL THEN 0 ELSE 1 END AS  CSLT_STATUS,PLAN_CON_DATE AS ACT_TIME,B.CON_DOC AS ACT_USER,EMP2.NAME AS ACT_USER_NAME,B.ARR_CON_DOC AS ACT_EMPLOYEE_ID, "
    				+ "        EMP3.NAME AS ACT_EMPLOYEE_NAME,B.ARR_CON_DOC AS ACT_EMPLOYEE_NO,EMP3.TECHNICAL_NAME AS ACT_JOBTITLE,EMP3.HOME_TEL AS  ACT_TEL,CASE WHEN PLAN_CON_SJD IS NULL THEN NULL ELSE TO_CHAR(B.PLAN_CON_DATE,'YYYY-MM-DD') || ' ' || SUBSTR(B.PLAN_CON_SJD,1,5)  || ':00' END AS ACT_START_TIME, "
    				+ "        CASE WHEN PLAN_CON_SJD IS NULL THEN NULL ELSE TO_CHAR(B.PLAN_CON_DATE,'YYYY-MM-DD') || ' ' || SUBSTR(B.PLAN_CON_SJD,8,11)  || ':00' END AS ACT_END_TIME,B.CON_DATE AS FNS_TIME,B.CON_DOC AS FNS_EMPLOYEE_ID,EMP2.NAME AS FNS_EMPLOYEE_NAME, "
    				+ "        B.CON_DOC AS FNS_EMPLOYEE_NO,EMP2.TECHNICAL_NAME AS FNS_JOBTITLE,EMP2.HOME_TEL AS  FNS_TEL,B.CON_ASSESS_CONTENT AS APPY_ORG_DSCR,B.CONTENT AS CSLT_ORG_DSCR, CASE WHEN B.DELETE_BIT=1 THEN 'Y' ELSE 'N' END AS IS_DELETED,A.APPLY_DOC,A.DEPT_ID AS APPLY_ORG_ID,DEPT.ZXKSMLID AS APPY_HOSP_AREA,A.APPLY_DOC AS APPLY_EMP_ID,B.HZMD_SCORE,B.HZZL_SCORE,B.HZBYX_SCORE,B.HZMDDF_SCORE,B.HZYJ_SCORE,B.CON_DOC_TYPE  "
    				+ " ");
    		sb.append(" FROM ODSZYV10.ZY_CONSULTATION A LEFT JOIN ODSZYV10.ZY_CON_MX B ON A.ID=B.P_ID "
    				+ "         LEFT JOIN    ODSZYV10.ZY_ORDERRECORD ORD ON B.ORDER_ID=ORD.ORDER_ID "
    				+ "        LEFT JOIN  ODSZYV10.BASE_DEPT_PROPERTY DEPT ON A.DEPT_ID=DEPT.DEPT_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT2 ON A.DEPT_BR=DEPT2.DEPT_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_DEPT_PROPERTY DEPT3 ON B.CON_DEPT=DEPT3.DEPT_ID "
    				+ "        LEFT JOIN ( SELECT  B.INPATIENT_ID,B.BED_ID,A.INPATIENT_NO,A.NAME,A.SEX AS SEXCODE,A.BIRTHDAY,B.IN_DIAGNOSIS  FROM ODSZYV10.BASE_PATIENT_PROPERTY A,ODSZYV10.ZY_INPATIENT B WHERE A.PATIENT_ID=B.PATIENT_ID) INP ON A.INPATIENT_ID=INP.INPATIENT_ID "
    				+ "        LEFT JOIN ODSZYV10.ZY_BEDDICTION BED ON  INP.BED_ID=BED.BED_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP ON A.APPLY_DOC=EMP.EMPLOYEE_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP2 ON B.CON_DOC=EMP2.EMPLOYEE_ID "
    				+ "        LEFT JOIN ODSZYV10.BASE_EMPLOYEE_PROPERTY EMP3 ON B.ARR_CON_DOC=EMP3.EMPLOYEE_ID ");
    		sb.append("  WHERE ((A.\"LEVEL\"=0 AND A.JZ_FLAG=1) OR (A.\"LEVEL\"=2 AND A.JZ_FLAG=0))   "
    				+ "AND ( (A.APPLY_DATE >   TRUNC(SYSDATE -7))  OR (B.CON_DATE >  TRUNC(SYSDATE -7)) OR (B.ACCEPT_DATE >   TRUNC(SYSDATE -7)) "
    				+ "OR (B.CON_ASSESS_DATE >   TRUNC(SYSDATE -7)) OR (B.PLAN_CON_DATE >   TRUNC(SYSDATE -7)) "
    				+ ") ");
    		
			//sb.append("select  id as csltAppyId,appy_No as appyNo,patn_Name as patnName from  med_cslt_appy  where id = ？  ");
    		//List<CsltAppySyncHis> CsltAppyList = 	HnsrmyyHisJdbcUtil.query(sb.toString(),CsltAppySyncHis.class);//执行语句返回结果,反射映射有问题
    		log.info("===========sql:"+sb.toString());
    		List<CsltAppySyncHis> CsltAppyList = HnsrmyyHisJdbcUtil.queryCsltAppySyncHis(sb.toString());//执行语句返回结果
    		log.info("===========CsltAppyList:"+CsltAppyList.size());
    		if(CollUtil.isNotEmpty(CsltAppyList)){
    			for(CsltAppySyncHis csltAppySyncHis : CsltAppyList) {
    				CsltAppy appy = mapper.selectByPrimaryKey(csltAppySyncHis.getCsltAppyId());//根据申请id查询是否已经存在申请单数据
    				
    				if(appy != null) {//存在申请数据，只更新安排、完成、评价字段
    					if(!StringUtil.isEmpty(csltAppySyncHis.getIsDeleted())) {
    						appy.setIsDeleted(csltAppySyncHis.getIsDeleted());
    					}
	    				//判断拉取的该数据是否已经安排，设置安排字段
	    				if("1".equals(csltAppySyncHis.getActStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getActEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 安排时间  相差的小时数
	    					Long l = DateUtil.between(DateUtil.parse(csltAppySyncHis.getAppyTime()),DateUtil.parse(csltAppySyncHis.getActTime()), DateUnit.HOUR,false);
	    					
	    					if(emp !=null) {
	    						appy.setActEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	        					appy.setActEmployeeName(emp.getEmployeeName());
	    					}else {
	    						appy.setActEmployeeId(csltAppySyncHis.getActEmployeeId()); //ID 未获取到本系统的，则存HIS的
	        					appy.setActEmployeeName(csltAppySyncHis.getActEmployeeName());
	    					}
	    					appy.setIsActOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							appy.setIsActAbn(StrUtil.startWith(lv.getItemName(), csltAppySyncHis.getActJobtitle()) ? "0" : "1"); 
	    						}
	    					}
	    					appy.setActUser(csltAppySyncHis.getActUser());
	    					appy.setActUserName(csltAppySyncHis.getActUserName());
	    					appy.setActStatus(csltAppySyncHis.getActStatus());
	    					appy.setActEmployeeNo(csltAppySyncHis.getActEmployeeNo());
	    					appy.setActJobtitle(csltAppySyncHis.getActJobtitle());
	    					appy.setActTel(csltAppySyncHis.getActTel());
	    					appy.setActStartTime(DateUtil.parse(csltAppySyncHis.getActStartTime(),"yyyy-MM-dd HH:mm:ss"));
	    					appy.setActEndTime(DateUtil.parse(csltAppySyncHis.getActEndTime(),"yyyy-MM-dd HH:mm:ss"));
	    				}
	    				
	    				//判断拉取的该数据是否已经完成，设置完成字段
	    				if("1".equals(csltAppySyncHis.getCsltStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getFnsEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 完成会诊时间  相差的小时数
	    					Long l = DateUtil.between(appy.getAppyTime(),DateUtil.parse(csltAppySyncHis.getFnsTime()), DateUnit.HOUR,false);
	    					if(emp !=null) {
	    						appy.setFnsEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	    						appy.setFnsEmployeeName(emp.getEmployeeName()); //
	    					}else {
	    						appy.setFnsEmployeeId(csltAppySyncHis.getFnsEmployeeId()); //ID  未获取到本系统的，则存HIS的
	    						appy.setFnsEmployeeName(csltAppySyncHis.getFnsEmployeeName()); //
	    					}
	    					appy.setIsCsltOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							String csltLv = csltAppySyncHis.getCsltLv(); //申请会诊级别 //29464 主任  29318 副主任   28841 主治
	    							String fnsJobtitle = csltAppySyncHis.getFnsJobtitle();  //完成会诊级别  主治医师 正主任医师  副主任医师
	    							if("29464".equals(csltLv) && ("副主任医师".equals(fnsJobtitle) || "主治医师".equals(fnsJobtitle))) {
	    								appy.setIsCsltAbn("1");
	    							}else if("29318".equals(csltLv) &&  "主治医师".equals(fnsJobtitle)) {
	    								appy.setIsCsltAbn("1");
	    							}else {
	    								appy.setIsCsltAbn("0");
	    							}
	    							//appy.setIsCsltAbn(StrUtil.startWith(lv.getItemName(), StrUtil.replace(csltAppySyncHis.getFnsJobtitle(), "正主任", "主任")) ? "0" : "1"); 
	    						}
	    					}
	    					appy.setCsltStatus(csltAppySyncHis.getCsltStatus());
	    					appy.setFnsTime(DateUtil.parse(csltAppySyncHis.getFnsTime(),"yyyy-MM-dd HH:mm:ss"));
	    					appy.setFnsEmployeeNo(csltAppySyncHis.getFnsEmployeeNo());
	    					appy.setFnsJobtitle(csltAppySyncHis.getFnsJobtitle());
	    					appy.setFnsTel(csltAppySyncHis.getFnsTel());
	    					appy.setAppyOrgDscr(csltAppySyncHis.getAppyOrgDscr());
	    					appy.setCsltOrgDscr(csltAppySyncHis.getCsltOrgDscr());
	    					appy.setHzmdScore(csltAppySyncHis.getHzmdScore());//评分
	    					appy.setHzzlScore(csltAppySyncHis.getHzzlScore());
	    					appy.setHzbyxScore(csltAppySyncHis.getHzbyxScore());
	    					appy.setHzmddfScore(csltAppySyncHis.getHzmddfScore());
	    					appy.setHzyjScore(csltAppySyncHis.getHzyjScore());
	    				}
	    				appy.setUpdateDate(DateUtil.date());
	    				//appy.setUpdateUser(csltAppySyncHis.getAppyEmpId());
	    				//appy.setUpdateUserName(csltAppySyncHis.getAppyEmpName());
	    				appy.setConDocType(csltAppySyncHis.getConDocType());
	    				mapper.updateByPrimaryKey(appy);
    				}else {
    					appy = new CsltAppy();
    					appy.setId(csltAppySyncHis.getCsltAppyId());
    					BeanUtil.copyProperties(csltAppySyncHis, appy);
    					
    					//将传入的科室ID转换为本系统的orgId
    					/*
    					List<String> orgIds = hrmsOrganizationMapper.convertOrg(csltAppySyncHis.getCsltOrgId());
    					*/
    					List<String> orgIds = hrmsOrganizationMapper.convertOrgBusiSystem(csltAppySyncHis.getCsltOrgId(),"HIS","2");
    					String orgId = CollUtil.isEmpty(orgIds) ? csltAppySyncHis.getCsltOrgId() : orgIds.get(0);
    					HrmsOrganization org = hrmsOrganizationMapper.selectByPrimaryKey(orgId);
    					if(org != null) {
    						appy.setCsltOrgId(org.getOrganizationId());//替换为本系统科室
        					appy.setCsltOrgName(org.getName());
    					}else {
    						appy.setCsltOrgId(csltAppySyncHis.getCsltOrgId());//没找到,存为his科室
        					appy.setCsltOrgName(csltAppySyncHis.getCsltOrgName());
    					}
    					
    					//将传入的申请科室ID转换为本系统的orgId
    					/*
    					List<String> appyOrgIds = hrmsOrganizationMapper.convertOrg(csltAppySyncHis.getAppyOrgId());
    					*/
    					List<String> appyOrgIds = hrmsOrganizationMapper.convertOrgBusiSystem(csltAppySyncHis.getAppyOrgId(),"HIS","2");
    					String appyOrgId = CollUtil.isEmpty(appyOrgIds) ? csltAppySyncHis.getAppyOrgId() : appyOrgIds.get(0);
    					HrmsOrganization appyOrg = hrmsOrganizationMapper.selectByPrimaryKey(appyOrgId);
    					if(appyOrg !=null) {
    						appy.setAppyOrgId(appyOrg.getOrganizationId());
    						appy.setAppyOrgName(appyOrg.getName());
    					}else {
    						appy.setAppyOrgId(csltAppySyncHis.getAppyOrgId());
    						appy.setAppyOrgName(csltAppySyncHis.getAppyOrgName());
    					}
    					
    					//判断拉取的该数据是否已经安排，设置安排字段
	    				if("1".equals(csltAppySyncHis.getActStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getActEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 安排时间  相差的小时数
	    					Long l = DateUtil.between(DateUtil.parse(csltAppySyncHis.getAppyTime()),DateUtil.parse(csltAppySyncHis.getActTime()), DateUnit.HOUR,false);
	    					
	    					if(emp !=null) {
	    						appy.setActEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	        					appy.setActEmployeeName(emp.getEmployeeName());
	    					}else {
	    						appy.setActEmployeeId(csltAppySyncHis.getActEmployeeId()); //ID 未获取到本系统的，则存HIS的
	        					appy.setActEmployeeName(csltAppySyncHis.getActEmployeeName());
	    					}
	    					appy.setIsActOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							appy.setIsActAbn(StrUtil.startWith(lv.getItemName(), csltAppySyncHis.getActJobtitle()) ? "0" : "1"); 
	    						}
	    					}
	    					
	    					//appy.setActStartTime(DateUtil.parse(csltAppySyncHis.getActStartTime(),"yyyy-MM-dd HH:mm:ss"));
	    					//appy.setActEndTime(DateUtil.parse(csltAppySyncHis.getActEndTime(),"yyyy-MM-dd HH:mm:ss"));
	    				}else {
	    					appy.setIsActOt("0");
	    					appy.setIsActAbn("0");
	    				}
	    				
	    				
	    				//判断拉取的该数据是否已经完成，设置完成字段
	    				if("1".equals(csltAppySyncHis.getCsltStatus())){
	    					String currentEmployeeId = fieldMappingService.getCurrentVal(new FieldMapping.Pk("HIS","EMPLOYEE_ID",csltAppySyncHis.getFnsEmployeeId()));
	    					CustEmpBase emp = custEmpBaseService.selectById(currentEmployeeId);
	    					//申请时间  和 完成会诊时间  相差的小时数
	    					Long l = DateUtil.between(appy.getAppyTime(),DateUtil.parse(csltAppySyncHis.getFnsTime()), DateUnit.HOUR,false);
	    					if(emp !=null) {
	    						appy.setFnsEmployeeId(emp.getEmployeeId()); //ID 存本系统的，因为要统计被邀、安排、完成的是不是同一个医生， 其他的 HIS传什么就存什么
	    						appy.setFnsEmployeeName(emp.getEmployeeName()); //
	    					}else {
	    						appy.setFnsEmployeeId(csltAppySyncHis.getFnsEmployeeId()); //ID  未获取到本系统的，则存HIS的
	    						appy.setFnsEmployeeName(csltAppySyncHis.getFnsEmployeeName()); //
	    					}
	    					appy.setIsCsltOt(l > 24 ? "1" : "0");
	    					List<DictItemResp> lvs = dictItemFeignService.getDictItemByTypeCode("cslt_lv").getObject();
	    					if(CollUtil.isNotEmpty(lvs)){
	    						DictItemResp lv = lvs.stream().filter(j -> StrUtil.equals(csltAppySyncHis.getCsltLv(), j.getItemCode())).findFirst().orElse(null);
	    						if(null != lv && StrUtil.isNotEmpty(lv.getItemName())){
	    							//是否异常会诊  排班资源中的会诊级别  与 完成医生的技术职称进行比较   申请的是主任医生，实际完成的是  主治医生
	    							String csltLv = csltAppySyncHis.getCsltLv(); //申请会诊级别 //29464 主任  29318 副主任   28841 主治
	    							String fnsJobtitle = csltAppySyncHis.getFnsJobtitle();  //完成会诊级别  主治医师 正主任医师  副主任医师
	    							if("29464".equals(csltLv) && ("副主任医师".equals(fnsJobtitle) || "主治医师".equals(fnsJobtitle))) {
	    								appy.setIsCsltAbn("1");
	    							}else if("29318".equals(csltLv) &&  "主治医师".equals(fnsJobtitle)) {
	    								appy.setIsCsltAbn("1");
	    							}else {
	    								appy.setIsCsltAbn("0");
	    							}
	    							//appy.setIsCsltAbn(StrUtil.startWith(lv.getItemName(), StrUtil.replace(csltAppySyncHis.getFnsJobtitle(), "正主任", "主任")) ? "0" : "1"); 
	    						}
	    					}
	    					//appy.setFnsTime(DateUtil.parse(csltAppySyncHis.getFnsTime(),"yyyy-MM-dd HH:mm:ss"));
	    				}else {
	    					appy.setIsCsltOt("0");
	    					appy.setIsCsltAbn("0");
	    				}
    					
    					appy.setCsltTime(DateUtil.parse(csltAppySyncHis.getCsltTime(),"yyyy-MM-dd HH:mm:ss"));
    					appy.setCreateDate(DateUtil.date());
    					appy.setCreateUser(csltAppySyncHis.getAppyEmpId());
    					appy.setCreateUserName(csltAppySyncHis.getAppyEmpName());
    					appy.setCreateDept(csltAppySyncHis.getAppyOrgId());
    					appy.setCreateDeptName(csltAppySyncHis.getAppyOrgName());
    					appy.setUpdateDate(DateUtil.date());
    					appy.setUpdateUser(csltAppySyncHis.getAppyEmpId());
    					appy.setUpdateUserName(csltAppySyncHis.getAppyEmpName());
    					appy.setSsoOrgName("定时任务2");
    					appy.setConDocType(csltAppySyncHis.getConDocType());
    					mapper.insert(appy);
    				}
    			}
    		}
		}catch(Exception e) {
    		e.printStackTrace();
    		log.error("获取影子库数据异常：" + e.getMessage());
    	}
	}
	
	
	@Override
	@Transactional(readOnly = false)
	public void syncHisEmployee() {
		
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT a.employee_id AS employeeId,name AS employeeName,dept_id AS orgId,FUN_BASE_HOSPTID(dept_id) AS hospCode,");
		sql.append("BASE_SOCIAL_NO AS identityNumber,sex AS gender,a.HOME_TEL AS phoneNumber,a.BRITHDAY AS birthday,");
		sql.append("a.BASE_WORKTIME AS workStartDate,a.BASE_ENTRYTIME AS entryDate,a.rylx AS orgAttributes,u.CODE AS hisEmployeeNo,");
		sql.append("u.PASSWORD AS hispwd,a.TECHNICAL_NAME AS technical");
		sql.append(" FROM base_employee_property a LEFT JOIN BASE_EMP_DEPT_ROLE b ON a.employee_id = b.employee_id");
		sql.append(" LEFT JOIN base_user u ON a.employee_id = u.EMPLOYEE_ID");
		sql.append(" WHERE delete_bit = 0 AND b.\"DEFAULT\" = '1'");
		
		List<HrmsEmployee> hisEmpList = HnsrmyyHisJdbcUtil.syncHisEmployee(sql.toString());
		
		Set<String> employeeIds = custEmpBaseService.findAllEmployeeId();
		
		for (HrmsEmployee hrmsEmployee : hisEmpList) {
			if(!employeeIds.contains(hrmsEmployee.getEmployeeId())){
				hrmsEmployee.setCreateDate(new Date());
				hrmsEmployee.setIsDeleted("N");
				hrmsEmployee.setCreateUser("admin");
				hrmsEmployee.setCreateUserName("admin");
				hrmsEmployee.setEmployeeStatus("1");
				hrmsEmployee.setId(IdGeneraterUtils.nextId());
				
				List<Map<String,String>> orgIds = hrmsOrganizationMapper.selectByPlatformId(hrmsEmployee.getOrgId());
				if(CollectionUtils.isNotEmpty(orgIds)) {
					hrmsEmployee.setOrgId(orgIds.get(0).get("oa_dept_id"));
				}else {
					hrmsEmployee.setOrgId("432816904746987520");
				}
				
				custEmpBaseService.initEmployee(hrmsEmployee);
			}else {
				custEmpBaseService.updateCodePwd(hrmsEmployee);
			}
		}
	}
	
	
	@Override
	@Transactional(readOnly = false)
	public void syncHrmEmployee() {
		
		//先获取token
		Map<String,String> params = new HashMap<>();
		params.put("ID", "yiwu001");
		params.put("ATOKEN", "eWl3dUAxMjM=");
		
		String tokenStr = HttpUtil.post("http://192.168.10.29:8081/ws/HXR/GetAccessTokenByID", JSON.toJSONString(params));
		
		JSONObject tokenObj = JSON.parseObject(tokenStr);
		
		String token = tokenObj.getString("CONTENT");
		
		Map<String,String> params2 = new HashMap<>();
		params2.put("ID", "yiwu001");
		params2.put("ACCESSTOKEN", token);
		params2.put("TABLENAME", "A01");
		params2.put("DATATYPE", "FULL");
		params2.put("dataSys", "yiwu");
		params2.put("STARTDATE", "");
		params2.put("ENDDATE", "");
		
		String empStr = HttpUtil.post("http://192.168.10.29:8081/ws/Basic/GetHRInformation", JSON.toJSONString(params2));
		JSONObject empObj = JSON.parseObject(empStr);
		
		JSONArray jsonArray = empObj.getJSONArray("DATAS");
		
		//List<Map<String,Object>> dataList = new ArrayList<>();
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject obj = jsonArray.getJSONObject(i);
			Map<String,Object> data = new HashMap<>();
			
			data.put("identityNumber", obj.getString("A0177"));//证件号码
			
			String employeeStatus = obj.getString("A011575");//人员状态 BM_RYSX
			if("01".equals(employeeStatus) || "15".equals(employeeStatus)  || "18".equals(employeeStatus)) {
				data.put("employeeStatus", "1"); 
			}else if("03".equals(employeeStatus) || "04".equals(employeeStatus)) {
				data.put("employeeStatus", "8");
			}else if("05".equals(employeeStatus)) {
				data.put("employeeStatus", "4"); 
			}else if("20".equals(employeeStatus)) {
				data.put("employeeStatus", "6"); 
			}else if("99".equals(employeeStatus)) {
				data.put("employeeStatus", "99");
			}else {
				data.put("employeeStatus", "1");
			}
			
			data.put("phoneNumber", obj.getString("A01274"));//手机号码
			
			//data.put("gender", obj.get("A0107"));  //性别   字典BM_AX
			//data.put("birthday", obj.get("A0111"));//出生日期
			//data.put("employee_name", obj.get("A0101"));  //姓名
			//data.put("employee_no", obj.get("A0190"));//工号
			//data.put("hosp_code", obj.get("A011539")); // 院区  BM_YQ
			
			String nationality = obj.getString("A0121");//民族  BM_AE
			if("01".equals(nationality)) {
				data.put("nationality", "1");
			}else if("02".equals(nationality)) {
				data.put("nationality", "2");
			}else if("03".equals(nationality)) {
				data.put("nationality", "3");
			}else if("04".equals(nationality)) {
				data.put("nationality", "4");
			}else if("05".equals(nationality)) {
				data.put("nationality", "5");
			}else if("06".equals(nationality)) {
				data.put("nationality", "6");
			}else if("07".equals(nationality)) {
				data.put("nationality", "7");
			}else if("08".equals(nationality)) {
				data.put("nationality", "8");
			}else if("09".equals(nationality)) {
				data.put("nationality", "9");
			}else {
				data.put("nationality", nationality);
			}
			
			String politicalStatus = obj.getString("A011644");//政治面貌
			if("中共党员".equals(politicalStatus)) {
				data.put("politicalStatus", "1"); 
			}else if("民盟盟员".equals(politicalStatus)) {
				data.put("politicalStatus", "5"); 
			}else if("致公党党员".equals(politicalStatus)) {
				data.put("politicalStatus", "9"); 
			}else if("中国民主促进会会员".equals(politicalStatus)) {
				data.put("politicalStatus", "13"); 
			}else if("农工民主党员".equals(politicalStatus)) {
				data.put("politicalStatus", "8"); 
			}else if("九三学社社员".equals(politicalStatus)) {
				data.put("politicalStatus", "10"); 
			}else if("中国国民党革命委员会会员".equals(politicalStatus)) {
				data.put("politicalStatus", "12"); 
			}else {
				data.put("politicalStatus", "4"); 
			}
			
			data.put("empTitleName", obj.getString("A011494")); //行政职务
			
			data.put("establishmentType", obj.getString("A0191"));//编制类型
			
			data.put("technical", obj.getString("A011590"));//现聘专业技术职务中文
			
			String workStartDate = obj.getString("A0141");//参加工作日期
			if(StringUtils.isNotBlank(workStartDate)) {
				workStartDate = workStartDate.replace("/", "-");
				data.put("workStartDate", workStartDate);
			}
			
			data.put("firstEducationType", obj.getString("A01085"));  //最高学历  BM_GBXL
			
			String entryDate = obj.getString("A0144");//入院日期
			if(StringUtils.isNotBlank(entryDate)) {
				entryDate = entryDate.replace("/", "-");
				data.put("entryDate", entryDate);
			}
			
			//data.put("emp_type", obj.get("A011586"));
			
			
			//data.put("emp_id", obj.get("A0188"));//职工ID
			//data.put("technical", obj.get("A011500"));  //现聘专业技术职务名称  BM_ZCFLXG
			//data.put("xpzjzw_start_date", obj.get("A011502")); //现聘专技职务聘任起始日期
			
			//data.put("emp_dept_id", obj.get("A011587"));
			//data.put("emp_dept_name", obj.get("A011588")); //所在部门对应中文
			//data.put("gender_text", obj.get("A011589"));//性别对应中文
			
			//data.put("technical_text", obj.get("A011591"));
			//data.put("hosp_code_text", obj.get("A011592"));//院区中文
			//data.put("employee_status_text", obj.get("A011593"));//人员状态中文
			//data.put("emp_type_text", obj.get("A011594"));//人员类型中文
			//data.put("deleted", obj.get("DELETED"));
			
		//	dataList.add(data);
			
			custEmpBaseService.updateByIdentityNumber(data);
			
		}
		
		//mapper.deleteLxhrmsData();
		
		//发送数据量过大  拆分数据 
		//List<List<Map<String,Object>>> saveList = CommonUtils.averageAssign(dataList,1000);
		
		//使用并行流插入数据
//		saveList.stream().forEach(ls ->{
//			mapper.batchInsert(ls);	
//        });
		
	}
	
	
	
	
	
}
