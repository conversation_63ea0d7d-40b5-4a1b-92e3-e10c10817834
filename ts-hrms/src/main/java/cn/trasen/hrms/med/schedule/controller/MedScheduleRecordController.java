package cn.trasen.hrms.med.schedule.controller;

import java.io.OutputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.med.schedule.model.MedScheduleRecord;
import cn.trasen.hrms.med.schedule.model.ScheduleEmployee;
import cn.trasen.hrms.med.schedule.service.MedScheduleAuthorityService;
import cn.trasen.hrms.med.schedule.service.MedScheduleRecordService;
import cn.trasen.hrms.utils.ExcelStyleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MedScheduleRecordController
 * @Description TODO
 * @date 2025��3��29�� ����2:58:59
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "排班记录-新")
public class MedScheduleRecordController {

	private transient static final Logger logger = LoggerFactory.getLogger(MedScheduleRecordController.class);

	@Autowired
	private MedScheduleRecordService medScheduleRecordService;
	
	@Autowired
	private MedScheduleAuthorityService medScheduleAuthorityService;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	/**
	 * @Title saveMedScheduleRecord
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/scheduleRecord/save")
	public PlatformResult<String> saveMedScheduleRecord(@RequestBody MedScheduleRecord record) {
		try {
			medScheduleRecordService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateMedScheduleRecord
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/scheduleRecord/update")
	public PlatformResult<String> updateMedScheduleRecord(@RequestBody MedScheduleRecord record) {
		try {
			medScheduleRecordService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectMedScheduleRecordById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<MedScheduleRecord>
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/scheduleRecord/{id}")
	public PlatformResult<MedScheduleRecord> selectMedScheduleRecordById(@PathVariable String id) {
		try {
			MedScheduleRecord record = medScheduleRecordService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteMedScheduleRecordById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/scheduleRecord/delete/{id}")
	public PlatformResult<String> deleteMedScheduleRecordById(@PathVariable String id) {
		try {
			medScheduleRecordService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectMedScheduleRecordList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<MedScheduleRecord>
	 * @date 2025��3��29�� ����2:58:59
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/scheduleRecord/list")
	public DataSet<MedScheduleRecord> selectMedScheduleRecordList(Page page, MedScheduleRecord record) {
		return medScheduleRecordService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "保存排班信息", notes = "保存排班信息")
	@PostMapping(value = "/api/scheduleRecord/saveMedScheduleRecord")
	public PlatformResult<String> saveMedScheduleRecord(@RequestBody ScheduleEmployee record) {
		try {
			medScheduleRecordService.batchInsert(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询个人排班信息", notes = "查询个人排班信息")
	@PostMapping(value = "/api/scheduleRecord/getPersonMedScheduleRecord")
	public PlatformResult<List<MedScheduleRecord>> getPersonMedScheduleRecord(@RequestBody ScheduleEmployee record) {
		try {
			return PlatformResult.success(medScheduleRecordService.getPersonMedScheduleRecord(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "查询排班数据", notes = "查询排班数据")
	@GetMapping("/api/scheduleRecord/getScheduleRecordList")
	public DataSet<ScheduleEmployee> getScheduleRecordList(Page page,ScheduleEmployee record) {
		return medScheduleAuthorityService.getScheduleRecordList(page, record);
	}
	
	@ApiOperation(value = "复制上月排班", notes = "复制上月排班")
	@PostMapping(value = "/api/scheduleRecord/copyScheduleRecord")
	public PlatformResult<String> copyScheduleRecord(@RequestBody ScheduleEmployee record) {
		try {
			medScheduleRecordService.copyScheduleRecord(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "清空排班", notes = "清空排班")
	@PostMapping(value = "/api/scheduleRecord/clearScheduleRecord")
	public PlatformResult<String> clearScheduleRecord(@RequestBody ScheduleEmployee record) {
		try {
			medScheduleRecordService.clearScheduleRecord(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "科室排班统计（移动端）", notes = "科室排班统计（移动端）")
	@PostMapping(value = "/api/scheduleRecord/statisticsScheduleRecordM")
	public PlatformResult<List<Map<String,Object>>> statisticsScheduleRecordM(@RequestBody ScheduleEmployee record) {
		try {
			return PlatformResult.success(medScheduleRecordService.statisticsScheduleRecordM(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "科室排班完成率(移动端）", notes = "科室排班完成率(移动端）")
	@PostMapping(value = "/api/scheduleRecord/finishRateScheduleRecordM")
	public PlatformResult<List<Map<String,Object>>> finishRateScheduleRecordM(@RequestBody ScheduleEmployee record) {
		try {
			return PlatformResult.success(medScheduleRecordService.finishRateScheduleRecordM(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "科室人员未排班日期(移动端）", notes = "科室人员未排班日期(移动端）")
	@PostMapping(value = "/api/scheduleRecord/unfinishScheduleDareM")
	public PlatformResult<List<Map<String,String>>> unfinishScheduleDareM(@RequestBody ScheduleEmployee record) {
		try {
			return PlatformResult.success(medScheduleRecordService.unfinishScheduleDareM(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	@ApiOperation(value = "排班导出", notes = "排班导出")
	@GetMapping(value = "/api/scheduleRecord/exportScheduleRecord")
	public void exportScheduleRecord(ScheduleEmployee record,HttpServletResponse response, Page page) {
		try {
			
			if(StringUtils.isEmpty(record.getStartDate()) && StringUtils.isEmpty(record.getEndDate())) {
				record.setStartDate(DateUtil.format(DateUtil.beginOfMonth(new Date()), "yyyy-MM-dd"));
				record.setEndDate(DateUtil.format(DateUtil.endOfMonth(new Date()), "yyyy-MM-dd"));
			}
			
			// 查询需要导出的字段
			List<String> dateList = getDateRangeWithWeekday(record.getStartDate(),record.getEndDate(),"yyyy-MM-dd EEEE");
			
//			record.setDateList(dateList);
//			
//		    List<Map<String,Object>> rows = medScheduleRecordService.selectExportScheduleRecord(record);
		    /**
		     * 修改导出排班数据
		     * <AUTHOR>
		     * @update 2025-07-12 10:00:00
		     */
			Map<String, String> dateMap = getDateRangeWithWeekdayMap(record.getStartDate(),record.getEndDate(),"yyyy-MM-dd EEEE");
			LinkedList<Map<String,Object>> rows = new LinkedList<>();
			DataSet<ScheduleEmployee> dataSet = medScheduleAuthorityService.getScheduleRecordList(page, record);
			List<ScheduleEmployee> scheduleEmployeeList = dataSet.getRows();
			
		    
		    List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
			List<DictItemResp> orgAttributes = dictItemFeignService.getDictItemByTypeCode("ORG_ATTRIBUTES").getObject();
		    
		    int i = 1;
//		    for (Map<String, Object> map : rows) {
//		    	map.put("序号", i);
//		    	
//		    	DictItemResp area = areas.stream().filter(j -> StrUtil.equals((String)map.get("hospCode"), j.getItemCode())).findFirst().orElse(null);
//				map.put("归属院区", null == area ? (String)map.get("hospCode") : area.getItemName());
//				
//		    	
//				map.put("归属科室", map.get("orgName"));
//				map.put("姓名", map.get("employeeName"));
//				DictItemResp orgAttribute = orgAttributes.stream().filter(j -> StrUtil.equals((String)map.get("orgAttributes"), j.getItemCode())).findFirst().orElse(null);
//				map.put("人员类型", null == orgAttribute ? (String)map.get("orgAttributes") : orgAttribute.getItemName());
//				map.put("职称", map.get("technical"));
//				map.put("联系方式", map.get("phoneNumber"));
//				
//		    	i++;
//			}
		    
		    for(ScheduleEmployee employee : scheduleEmployeeList){
		    	Map<String,Object> map = new HashMap<>();
		    	map.put("序号", i);
		    	map.put("姓名", employee.getEmployeeName());
		    	DictItemResp orgAttribute = orgAttributes.stream().filter(j -> StrUtil.equals(employee.getOrgAttributes(), j.getItemCode())).findFirst().orElse(null);
		    	map.put("人员类型", null == orgAttribute ? (String)employee.getOrgAttributes() : orgAttribute.getItemName());
		    	map.put("归属科室", employee.getOrgName());
		    	map.put("分组名称", employee.getGroupName());
		    	DictItemResp area = areas.stream().filter(j -> StrUtil.equals(employee.getHospCode(), j.getItemCode())).findFirst().orElse(null);
				map.put("归属院区", null == area ? employee.getHospCode() : area.getItemName());
				map.put("职称", employee.getTechnical());
				map.put("联系方式", employee.getPhoneNumber());
				//加载日期及其排班数据
				List<MedScheduleRecord> scheduleList = employee.getScheduleRecords();
				if(null == scheduleList || scheduleList.size() == 0){
					for(String sheduleDate : dateMap.keySet()){
						map.put(dateMap.get(sheduleDate), "");
					}
				} else {
					for(String sheduleDate : dateMap.keySet()){
						boolean exist = false;
						StringBuffer classesName = new StringBuffer();
						String remark = "-";
						for(MedScheduleRecord schedule : scheduleList){
							if(sheduleDate.equals(schedule.getScheduleDate())){
								if(exist){
									classesName.append(",");
								}
								classesName.append(schedule.getClassesName());
								if(!ObjectUtils.isEmpty(schedule.getRemark())){
									remark = schedule.getRemark();
								}
								exist = true;
							}
						}
						if(!exist){
							map.put(dateMap.get(sheduleDate), "");
						} else {
							classesName.append(",备注：" + remark);
							map.put(dateMap.get(sheduleDate), classesName.toString());
						}
					}
				}
				rows.add(map);
		    	i++;
		    }
		    
		    List<String> headList = new ArrayList<>();
			headList.add("序号");
			headList.add("姓名");
			headList.add("人员类型");
			headList.add("归属科室");
			headList.add("分组名称");
			headList.add("归属院区");
			headList.add("职称");
			headList.add("联系方式");
			
			headList.addAll(dateList);
			
			List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();

			setColList(colList, headList);
			
			String filename = record.getStartDate() + "~" + record.getEndDate() + "排班汇总情况";

			ExportParams exportParams = new ExportParams(filename, "排班汇总情况", ExcelType.XSSF);
			exportParams.setStyle(ExcelStyleUtil.class);
			
			Workbook workbook = ExcelExportUtil.exportExcel(exportParams,colList,rows);
			
			workbook.getSheet("排班汇总情况").getRow(1).setHeight((short) (12*50));
			// 新增：设置自适应高度的代码
			CellStyle wrapStyle = workbook.createCellStyle();
			wrapStyle.setWrapText(true);
			wrapStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
			wrapStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中（可选）

			Sheet sheet = workbook.getSheet("排班汇总情况");

			int startRowIndex = 2;
			for (int index = startRowIndex; index <= sheet.getLastRowNum(); index++) {
			    Row row = sheet.getRow(index);
			    if (row == null) continue;
			    
			    for (int j = 0; j < row.getLastCellNum(); j++) {
			        Cell cell = row.getCell(j);
			        if (cell != null) {
			            cell.setCellStyle(wrapStyle);
			        }
			    }
			    
			    sheet.autoSizeColumn(i);
			    row.setHeight((short)-1);
			    
			    if (row.getHeight() > 500) {
			        row.setHeight((short)500);
			    }
			}
			
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			response.setHeader("Content-disposition",
					"attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xls");

			OutputStream fos = response.getOutputStream();
			workbook.write(fos);
			fos.close();
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
		}
	}
	
	private void setColList(List<ExcelExportEntity> colList, List<String> fieldList) {
		
		ExcelExportEntity colEntity = null;

		if (CollectionUtils.isNotEmpty(fieldList)) {

			for (String field : fieldList) {

				colEntity = new ExcelExportEntity(field, field);
				colEntity.setWidth(12);
				colEntity.setHeight(12);

				colList.add(colEntity);
				
			}
		}
	}
	
	public static List<String> getDateRangeWithWeekday(String startStr, String endStr, String pattern) {
        // 解析字符串为 LocalDate 对象（支持如 "yyyy-MM-dd" 等格式）
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startStr, inputFormatter);
        LocalDate endDate = LocalDate.parse(endStr, inputFormatter);

        // 生成日期范围
        List<String> dates = new ArrayList<>();
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern).withLocale(java.util.Locale.CHINA);
        
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        for (int i = 0; i <= daysBetween; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            dates.add(currentDate.format(outputFormatter));
        }
        return dates;
	}
	
	public static Map<String, String> getDateRangeWithWeekdayMap(String startStr, String endStr, String pattern) {
		Map<String, String> map = new HashMap<>();
        // 解析字符串为 LocalDate 对象（支持如 "yyyy-MM-dd" 等格式）
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startStr, inputFormatter);
        LocalDate endDate = LocalDate.parse(endStr, inputFormatter);

        // 生成日期范围
        List<String> dates = new ArrayList<>();
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern).withLocale(java.util.Locale.CHINA);
        
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        for (int i = 0; i <= daysBetween; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            dates.add(currentDate.format(outputFormatter));
            map.put(currentDate.format(inputFormatter), currentDate.format(outputFormatter));
        }
        return map;
	}
	
}
