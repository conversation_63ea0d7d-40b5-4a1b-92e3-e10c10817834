package cn.trasen.hrms.med.delivery.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.ResultData;
import cn.trasen.hrms.med.delivery.dao.MedDeliveryDictMapper;
import cn.trasen.hrms.med.delivery.model.MedDeliveryDict;
import cn.trasen.hrms.med.delivery.service.MedDeliveryDictService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedDeliveryDictServiceImpl
 * @Description TODO
 * @date 2025��5��20�� ����3:31:11
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class MedDeliveryDictServiceImpl implements MedDeliveryDictService {

	@Autowired
	private MedDeliveryDictMapper mapper;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(MedDeliveryDict record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		record.setStatus("1");
		record.setDictType("1");
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		if(StringUtils.isEmpty(record.getImportData())) {
			List<MedDeliveryDict> repeatData = getRepeatData(record);
			if(CollUtil.isNotEmpty(repeatData)) {
				Assert.isTrue(false, "该数据已存在，请勿重复提交！");
			}
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(MedDeliveryDict record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
//		List<MedDeliveryDict> repeatData = getRepeatData(record);
//		if(CollUtil.isNotEmpty(repeatData)) {
//			Assert.isTrue(false, "该数据已存在，请勿重复提交！");
//		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		MedDeliveryDict record = new MedDeliveryDict();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public MedDeliveryDict selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<MedDeliveryDict> getDataSetList(Page page, MedDeliveryDict record) {
		Example example = new Example(MedDeliveryDict.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		if(StrUtil.isNotBlank(record.getDictType())) {
			criteria.andEqualTo("dictType", record.getDictType());
		}
		
		if(StrUtil.isNotBlank(record.getStatus())) {
			criteria.andEqualTo("status", record.getStatus());
		}
		
		if(StrUtil.isNotBlank(record.getProjectName())) {
			criteria.andLike("projectName","%" + record.getProjectName() + "%");
		}
		if(StrUtil.isNotBlank(record.getConnotation())) {
			criteria.andLike("connotation","%" + record.getConnotation() + "%");
		}
		if(StrUtil.isNotBlank(record.getSampleType())) {
			criteria.andLike("sampleType","%" + record.getSampleType() + "%");
		}
		if(StrUtil.isNotBlank(record.getTestOrg())) {
			criteria.andLike("testOrg","%" + record.getTestOrg() + "%");
		}
		
		
		
		List<MedDeliveryDict> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public PlatformResult importMedDeliveryDict(List<MedDeliveryDict> list) {
		
		List<DictItemResp> sampleTypeDic = dictItemFeignService.getDictItemByTypeCode("SAMPLE_TYPE").getObject(); //样本类型
		List<DictItemResp> testOrgDic = dictItemFeignService.getDictItemByTypeCode("TEST_ORG").getObject(); //外送检验机构名称
		
		List<String> fileIsnulls; //为空的字段
		List<String> fileDictNotFunds; //字典不能匹配字段
		
		List<ResultData> badList = new ArrayList<>();//失败信息
		
		Integer successCnt = 0;
		Integer errorCnt = 0;
		
		ResultData badData;
		DictItemResp dict;
		
		int i = 0;
		for (MedDeliveryDict medDeliveryDict : list) {
			
			i++;
			
			fileIsnulls = new ArrayList<String>();
			fileDictNotFunds = new ArrayList<String>();
			
			if(StrUtil.isEmpty(medDeliveryDict.getProjectName())){
				fileIsnulls.add("项目名称");
			}
			if(StrUtil.isEmpty(medDeliveryDict.getConnotation())){
				fileIsnulls.add("项目内涵");
			}
			if(StrUtil.isEmpty(medDeliveryDict.getSampleType())){
				fileIsnulls.add("样本类型");
			}
			if(StrUtil.isEmpty(medDeliveryDict.getSampleDose())){
				fileIsnulls.add("样本量");
			}
			if(StrUtil.isEmpty(medDeliveryDict.getTestOrg())){
				fileIsnulls.add("外送检验机构名称");
			}
			if(StrUtil.isEmpty(medDeliveryDict.getCost())){
				fileIsnulls.add("费用");
			}
			
			if(CollUtil.isNotEmpty(fileIsnulls)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】为空!", i + 1,StrUtil.join("、", fileIsnulls)));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			dict = sampleTypeDic.stream().filter(j -> StrUtil.equals(medDeliveryDict.getSampleType(), j.getItemName())).findFirst().orElse(null);
			if(null == dict){
				fileDictNotFunds.add(StrUtil.format("样本类型->{}", medDeliveryDict.getSampleType()));
			}
			
			dict = testOrgDic.stream().filter(j -> StrUtil.equals(medDeliveryDict.getTestOrg(), j.getItemName())).findFirst().orElse(null);
			if(null == dict){
				fileDictNotFunds.add(StrUtil.format("外送检验机构名称->{}", medDeliveryDict.getTestOrg()));
			}
			
			if(CollUtil.isNotEmpty(fileDictNotFunds)){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的【{}】无法匹配字典值!", i + 1,StrUtil.join("、", fileDictNotFunds)));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			if(CollUtil.isNotEmpty(getRepeatData(medDeliveryDict))){
				badData = new ResultData();
				badData.setData(StrUtil.format("第【{}】行的数据{}系统中已存在!", i + 1,medDeliveryDict.getProjectName()));
				badList.add(badData);
				errorCnt++;
				continue;
			}
			
			medDeliveryDict.setImportData("Y");
			save(medDeliveryDict);
			successCnt++;
			
		}
		
		if(CollUtil.isNotEmpty(badList)){
			return PlatformResult.failure(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt),badList);
		}else{
			return PlatformResult.success(StrUtil.format("信息导入 ,总条数:{}、成功:{}、失败{}", list.size(),successCnt,errorCnt));
		}
	}
	
	
	public List<MedDeliveryDict> getRepeatData(MedDeliveryDict record) {
		Example example = new Example(MedDeliveryDict.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		
		if(StringUtils.isNotBlank(record.getId())) {
			criteria.andNotEqualTo("id", record.getId());
		}
		
		criteria.andEqualTo("projectName", record.getProjectName());
		criteria.andEqualTo("sampleType", record.getSampleType());
		criteria.andEqualTo("testOrg", record.getTestOrg());
		
		return  mapper.selectByExample(example);
	}
	
}
