package cn.trasen.hrms.med.major.controller;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.med.deptHonor.model.MedDeptHonorWinner;
import cn.trasen.hrms.med.major.model.MajorEvents;
import cn.trasen.hrms.med.major.service.MajorEventsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName MajorEventsController
 * @Description TODO
 * @date 2024��11��11�� ����3:12:51
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "医务大事记")
public class MajorEventsController {

	private transient static final Logger logger = LoggerFactory.getLogger(MajorEventsController.class);

	@Autowired
	private MajorEventsService majorEventsService;

	/**
	 * @Title saveCustMedMajorEvents
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��11�� ����3:12:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/majorEvents/save")
	public PlatformResult<String> saveCustMedMajorEvents(@RequestBody MajorEvents record) {
		try {
			majorEventsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCustMedMajorEvents
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2024��11��11�� ����3:12:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/majorEvents/update")
	public PlatformResult<String> updateCustMedMajorEvents(@RequestBody MajorEvents record) {
		try {
			majorEventsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCustMedMajorEventsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CustMedMajorEvents>
	 * @date 2024��11��11�� ����3:12:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/majorEvents/{id}")
	public PlatformResult<MajorEvents> selectCustMedMajorEventsById(@PathVariable String id) {
		try {
			MajorEvents record = majorEventsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCustMedMajorEventsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2024��11��11�� ����3:12:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/majorEvents/delete/{id}")
	public PlatformResult<String> deleteCustMedMajorEventsById(@PathVariable String id) {
		try {
			majorEventsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCustMedMajorEventsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CustMedMajorEvents>
	 * @date 2024��11��11�� ����3:12:51
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/majorEvents/list")
	public DataSet<MajorEvents> selectCustMedMajorEventsList(Page page, MajorEvents record) {
		return majorEventsService.getDataSetList(page, record);
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 审批
	  -- 作者: GW
	  -- 创建时间: 2024年11月13日
	  -- @param record
	  -- @return
	  -- =============================================
	 */
	@ApiOperation(value = "审批", notes = "审批")
	@PostMapping("/api/majorEvents/approval")
	public PlatformResult<String> approval(@RequestBody List<MajorEvents> records) {
		try {
			majorEventsService.approval(records);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 导出医务大事记
	  -- 作者: GW
	  -- 创建时间: 2024年12月2日
	  -- @param request
	  -- @param response
	  -- @param page
	  -- @param record
	  -- =============================================
	 */
	@ApiOperation(value = "导出", notes = "导出")
	@GetMapping("/api/majorEvents/export")
    public void export(HttpServletRequest request, HttpServletResponse response,MajorEvents record) {
		try {
			Page page = new Page();
			page.setPageSize(65535);
			page.setPageNo(1);
			DataSet<MajorEvents> result = majorEventsService.getDataSetList(page, record);
			List<MajorEvents> list = result.getRows();
			if (CollUtil.isNotEmpty(list)) {
				String filename = "医务大事记";
				List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
				ExcelExportEntity e = new ExcelExportEntity("序号", "no");
				colList.add(e);
				e = new ExcelExportEntity("事件日期", "eventsDate");
				colList.add(e);
				e = new ExcelExportEntity("事件类型", "eventsTypeName");
				colList.add(e);
				e = new ExcelExportEntity("事件名称", "eventsName");
				colList.add(e);
				e = new ExcelExportEntity("事件描述", "eventsDscr");
				colList.add(e);
				e = new ExcelExportEntity("事件影响", "eventsInfl");
				colList.add(e);
				e = new ExcelExportEntity("处理措施", "dspoMes");
				colList.add(e);
				e = new ExcelExportEntity("调查与分析", "srvyAna");
				colList.add(e);
				e = new ExcelExportEntity("改进与建议", "imprAdv");
				colList.add(e);
				e = new ExcelExportEntity("审批状态", "approvalStatus");
				colList.add(e);
				e = new ExcelExportEntity("审批时间", "approvalTime");
				colList.add(e);
				e = new ExcelExportEntity("审批人", "approvalUserName");
				colList.add(e);
				e = new ExcelExportEntity("审批意见", "approvalDscr");
				colList.add(e);
				e = new ExcelExportEntity("操作人", "updateUserName");
				colList.add(e);
				List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
				for(int no = 0; no < list.size() ; no++){
					Map<String,Object> m = BeanUtil.beanToMap(list.get(no));
					m.put("no", no + 1);
					m.put("approvalStatus", "1".equals(list.get(no).getApprovalStatus()) ? "已审批" : "未审批");
					dataList.add(m);
				}
				Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(filename, filename, ExcelType.XSSF), colList, dataList);
				response.setContentType("application/vnd.ms-excel");
				response.setCharacterEncoding("UTF-8");
				response.setHeader("Content-disposition", "attachment; filename=" + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
				OutputStream fos = response.getOutputStream();
				workbook.write(fos);
				fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
		}
	}
}
