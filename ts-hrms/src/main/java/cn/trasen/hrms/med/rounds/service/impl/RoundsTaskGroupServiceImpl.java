package cn.trasen.hrms.med.rounds.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.med.rounds.dao.RoundsTaskGroupMapper;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroup;
import cn.trasen.hrms.med.rounds.model.RoundsTaskGroupRules;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupRulesService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupRulesService;
import cn.trasen.hrms.med.rounds.service.RoundsTaskGroupService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName RoundsTaskGroupServiceImpl
 * @Description TODO
 * @date 2025��3��7�� ����3:27:11
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class RoundsTaskGroupServiceImpl implements RoundsTaskGroupService {

	@Autowired
	private RoundsTaskGroupMapper mapper;
	
	@Autowired
	private RoundsTaskGroupRulesService  taskGroupRulesService;
	

	@Transactional(readOnly = false)
	@Override
	public Integer save(RoundsTaskGroup record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setStatus("1");//扣分待提交
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(RoundsTaskGroup record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		RoundsTaskGroup record = new RoundsTaskGroup();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public RoundsTaskGroup selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<RoundsTaskGroup> getDataSetList(Page page, RoundsTaskGroup record) {
		Example example = new Example(RoundsTaskGroup.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<RoundsTaskGroup> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<RoundsTaskGroup> getByTaskId(String taskId) {
		// TODO Auto-generated method stub
		//List<RoundsTaskGroup> roundsTaskGroupList = mapper.getByTaskId(taskId);
		//根据任务id查询对应的组以及对应细则
		RoundsTaskGroup roundsTaskGroup  =  new RoundsTaskGroup();
		roundsTaskGroup.setTaskId(taskId);
		List<RoundsTaskGroup> roundsTaskGroupList = mapper.getRoundsTaskGroupByParam(roundsTaskGroup);
		if(CollectionUtils.isNotEmpty(roundsTaskGroupList)) {
			for(RoundsTaskGroup taskGroup : roundsTaskGroupList) {
				RoundsTaskGroupRules roundsTaskGroupRules = new RoundsTaskGroupRules();
				roundsTaskGroupRules.setTaskId(taskId);
				roundsTaskGroupRules.setSchedulingId(taskGroup.getSchedulingId());
				roundsTaskGroupRules.setGroupId(taskGroup.getGroupId());
				List<RoundsTaskGroupRules> roundsTaskGroupRulesList =taskGroupRulesService.getByTaskGroupId(roundsTaskGroupRules);
				taskGroup.setRoundsTaskGroupRulesList(roundsTaskGroupRulesList);
			}
		}
		return roundsTaskGroupList;
	}

	@Override
	public List<RoundsTaskGroup> getRoundsTaskGroupByParam(RoundsTaskGroup record) {
		// TODO Auto-generated method stub
		return mapper.getRoundsTaskGroupByParam(record);
	}
}
