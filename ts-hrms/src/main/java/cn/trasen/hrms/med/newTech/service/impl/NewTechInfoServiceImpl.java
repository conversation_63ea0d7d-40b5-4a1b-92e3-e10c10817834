package cn.trasen.hrms.med.newTech.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.hrms.interfaceRegister.dao.CommInterfaceRegisterMapper;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceLogs;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceRegister;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceLogsService;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceRegisterService;
import cn.trasen.hrms.med.newTech.dao.NewTechEevaluationMapper;
import cn.trasen.hrms.med.newTech.dao.NewTechInfoMapper;
import cn.trasen.hrms.med.newTech.dao.NewTechParticipantsMapper;
import cn.trasen.hrms.med.newTech.dao.NewTechPatientInfoMapper;
import cn.trasen.hrms.med.newTech.emun.IsNewTechEmun;
import cn.trasen.hrms.med.newTech.emun.IsRstdTechEmun;
import cn.trasen.hrms.med.newTech.emun.TechClassEmun;
import cn.trasen.hrms.med.newTech.emun.TechStatusEmun;
import cn.trasen.hrms.med.newTech.emun.TechTypeEmun;
import cn.trasen.hrms.med.newTech.model.NewTechEevaluation;
import cn.trasen.hrms.med.newTech.model.NewTechInfo;
import cn.trasen.hrms.med.newTech.model.NewTechParticipants;
import cn.trasen.hrms.med.newTech.model.NewTechPatientInfo;
import cn.trasen.hrms.med.newTech.service.NewTechEevaluationService;
import cn.trasen.hrms.med.newTech.service.NewTechInfoService;
import cn.trasen.hrms.med.newTech.service.NewTechParticipantsService;
import cn.trasen.hrms.med.newTech.service.NewTechPatientInfoService;
import cn.trasen.hrms.med.newTech.vo.EmployeeRespVo;
import cn.trasen.hrms.med.qua.dao.MedDoctorRoleMapper;
import cn.trasen.hrms.med.qua.dao.QuaAuthCfgMapper;
import cn.trasen.hrms.med.qua.dao.QuaAuthItemDetlMapper;
import cn.trasen.hrms.med.qua.dao.QuaAuthMgtMapper;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;
import cn.trasen.hrms.med.qua.model.QuaAuthItemDetl;
import cn.trasen.hrms.med.qua.model.QuaAuthMgtParam;
import cn.trasen.hrms.med.qua.model.QuaMgtAndDetl;
import cn.trasen.hrms.med.qua.service.QuaAuthCfgService;
import cn.trasen.hrms.med.qua.service.QuaAuthItemDetlService;
import cn.trasen.hrms.utils.CommonUtils;
import cn.trasen.hrms.utils.HnsrmyyHisJdbcUtil;
import cn.trasen.hrms.utils.HttpClient;
import cn.trasen.hrms.utils.StrConvertPinyin;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName NewTechInfoServiceImpl
 * @Description 新技术新项目服务实现类
 * @date 2025-05-13 16:23:40
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class NewTechInfoServiceImpl implements NewTechInfoService {
	
	/**
	 * 新技术准入，项目组人员子表数据
	 */
	@Value("${participantsTableName:zt_xmzryqk}")
	private String participantsTableName;
	
	/**
	 * 新技术评估，项目开展患者情况
	 */
	@Value("${patientInfoTableName:zt_xmkzqk}")
	private String patientInfoTableName;

	//1通山版本（已废弃，目前和文山一个版本）  2文山版本（HIS代理）  其他的是标准版本（平台标准）
	@Value("${hisRequestVersion:99}")
	private String hisRequestVersion;

	@Autowired
	private NewTechInfoMapper mapper;
	
	@Autowired
	private QuaAuthItemDetlService quaAuthItemDetlService;
	
	@Autowired
	private QuaAuthCfgService quaAuthCfgService;
	
	@Autowired
	private NewTechParticipantsService newTechParticipantsService;
	
	@Autowired
	private NewTechPatientInfoService newTechPatientInfoService;
	
	@Autowired
	private NewTechEevaluationService newTechEevaluationService;
	
	@Autowired
	private QuaAuthItemDetlMapper quaAuthItemDetlMapper;
	
	@Autowired
	private NewTechParticipantsMapper newTechParticipantsMapper;
	
	@Autowired
	private NewTechEevaluationMapper newTechEevaluationMapper;
	
	@Autowired
	private NewTechPatientInfoMapper newTechPatientInfoMapper;

	@Autowired
    DictItemFeignService dictItemFeignService;
	
	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;
	
	@Autowired
	private CommInterfaceRegisterService commInterfaceRegisterService;
	
	@Autowired
	private CommInterfaceRegisterMapper commInterfaceRegisterMapper;
	
	@Autowired
	private CommInterfaceLogsService commInterfaceLogsService;

	@Autowired
	private QuaAuthCfgMapper quaAuthCfgMapper;
	
	@Autowired
	private QuaAuthMgtMapper quaAuthMgtMapper;
	
	@Autowired
	private MedDoctorRoleMapper medDoctorRoleMapper;
	
	//是否开启医务定时任务（省人医）
	@Value("${csltFlag:99}")
	private String csltFlag; 

	@Transactional(readOnly = false)
	@Override
	public Integer save(NewTechInfo record) {
		//校验伦理号唯一性
		if(StrUtil.isEmpty(record.getTechCode())){
			throw new RuntimeException("伦理号不能为空!");
		}
		Example example = new Example(NewTechInfo.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("techCode", record.getTechCode());
		List<NewTechInfo> tschList = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(tschList)){
			throw new RuntimeException("该伦理号已存在!");
		}
		
		if(ObjectUtils.isEmpty(record.getId())){
			record.setId(IdGeneraterUtils.nextId());
		}
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(NewTechInfo record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		NewTechInfo record = new NewTechInfo();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public NewTechInfo selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		
		NewTechInfo record = mapper.selectById(id);

		List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
		List<DictItemResp> lvHosps = dictItemFeignService.getDictItemByTypeCode("auth_lv_hosp").getObject();
		List<DictItemResp> lvNats = dictItemFeignService.getDictItemByTypeCode("auth_lv_nat").getObject();
		DictItemResp area = areas.stream().filter(j -> StrUtil.equals(record.getProHospCode(), j.getItemCode())).findFirst().orElse(null);
		record.setProHospArea(null == area ? record.getProHospCode() : area.getItemName());
		DictItemResp lv = lvHosps.stream().filter(j -> StrUtil.equals(record.getAuthLvHosp(), j.getItemCode())).findFirst().orElse(null);
		record.setAuthLvHosp(null == lv ?record.getAuthLvHosp() : lv.getItemName());
		DictItemResp nat = lvNats.stream().filter(j -> StrUtil.equals(record.getAuthLvNat(), j.getItemCode())).findFirst().orElse(null);
		record.setAuthLvNat(null == nat ?record.getAuthLvNat() : nat.getItemName());
		//获取参与人员列表
		List<NewTechParticipants> participants = newTechParticipantsService.selectByNewTechId(id);
		if(CollUtil.isNotEmpty(participants)){
			participants.forEach(i -> {
				DictItemResp area1 = areas.stream().filter(j -> StrUtil.equals(i.getHospArea(), j.getItemCode())).findFirst().orElse(null);
				i.setHospArea(null == area1 ? i.getHospArea() : area1.getItemName());
			});
		}
		record.setParticipants(participants);
		
		//开展信息
		record.setPatients(newTechPatientInfoService.selectByNewTechCode(record.getTechCode()));
		
		return record;
	}


	@Override
	public NewTechInfo selectByNewTechCode(String newTechCode) {
		Example example = new Example(NewTechInfo.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("techCode", newTechCode);
		List<NewTechInfo> tschList = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(tschList)){
			return selectById(tschList.get(0).getId());
		}
		return null;
	}

	@Override
	public DataSet<NewTechInfo> getDataSetList(Page page, NewTechInfo record) {
		//数据权限-个人看个人，科主任看本科室，医务部管理员看全部
		if(!UserInfoHolder.ISADMIN() && !UserInfoHolder.ISALL() && !UserInfoHolder.getRight("YWGLY")){ 
			//判断是否是科主任-为空则不是，否则是
			List<String> orgIds = quaAuthItemDetlMapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
			
			if(CollectionUtils.isEmpty(orgIds)) {
				record.setProEmployeeNo(UserInfoHolder.getCurrentUserCode()); //普通用户看自己的
			}else {
				String orgRang = UserInfoHolder.getOrgRang();
				if(StrUtil.isNotEmpty(orgRang)){
					orgRang = StrUtil.replace(StrUtil.removeSuffix(StrUtil.removePrefix(orgRang, "("), ")"), "'", "");
					orgIds.addAll(ListUtil.of(orgRang.split(",")));
				}
				orgIds = orgIds.stream().distinct().collect(Collectors.toList());
				record.setOrgIdList(orgIds);
			}
		}
		
		List<NewTechInfo> records = mapper.selectList(page, record);

		if(CollUtil.isNotEmpty(records)){
			List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
			List<DictItemResp> lvHosps = dictItemFeignService.getDictItemByTypeCode("auth_lv_hosp").getObject();
			List<DictItemResp> lvNats = dictItemFeignService.getDictItemByTypeCode("auth_lv_nat").getObject();
			records.forEach(i -> {
				DictItemResp area = areas.stream().filter(j -> StrUtil.equals(i.getProHospCode(), j.getItemCode())).findFirst().orElse(null);
				i.setProHospArea(null == area ? i.getProHospCode() : area.getItemName());
				DictItemResp lv = lvHosps.stream().filter(j -> StrUtil.equals(i.getAuthLvHosp(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvHosp(null == lv ?i.getAuthLvHosp() : lv.getItemName());
				DictItemResp nat = lvNats.stream().filter(j -> StrUtil.equals(i.getAuthLvNat(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvNat(null == nat ?i.getAuthLvNat() : nat.getItemName());
			});
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public List<NewTechInfo> selectNewTechInfoList(NewTechInfo record) {
		Example example = new Example(NewTechInfo.class);
		Example.Criteria criteria = example.createCriteria();
		//状态
		if(!ObjectUtils.isEmpty(record.getTechStatus())){
			criteria.andEqualTo("techStatus", record.getTechStatus());
		}
		//伦理号
		if(!ObjectUtils.isEmpty(record.getTechCode())){
			criteria.andEqualTo("techCode", record.getTechCode());
		}
		//关键字
		if(StrUtil.isNotEmpty(record.getCondition())){
			criteria.andCondition(StrUtil.format("(tech_name like '%{}%' or tech_code like '%{}%')", record.getCondition(),record.getCondition()));
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		example.setOrderByClause("update_date desc");
		List<NewTechInfo> records = mapper.selectByExample(example);
		if(CollUtil.isNotEmpty(records)){
			for(NewTechInfo info: records){
				info.setItemName(info.getTechCode());
				info.setItemValue(info.getTechCode());
			}
		}
		return records;
	}

	@Override
	public List<NewTechEevaluation> selectEevaluationList(String techCode) {
		Example example = new Example(NewTechEevaluation.class);
		Example.Criteria criteria = example.createCriteria();
		//伦理号
		if(!ObjectUtils.isEmpty(techCode)){
			criteria.andEqualTo("techCode", techCode);
		}
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		example.setOrderByClause("update_date desc");
		List<NewTechEevaluation> records = newTechEevaluationMapper.selectByExample(example);
		return records;
	}


	@Transactional(readOnly = false)
	@Override
	public void insertNewTechData(Map<String, Object> formData) {
		// 流程实例id
		String workflowInstId = formData.get("workflowInstId").toString();
		//业务ID
		String businessId = formData.get("L_BusinessId").toString();
		// 准入结果-只有批准才新增记录
		String L_applyResult = formData.get("L_applyResult").toString();
		if(ObjectUtils.isEmpty(L_applyResult) || !L_applyResult.equals("批准")){
			return;
		}
		NewTechInfo newTechInfo = new NewTechInfo();
		newTechInfo.setId(workflowInstId);
		newTechInfo.setBusinessId(businessId);
		newTechInfo.setTechName(String.valueOf(formData.get("L_techName")));
		newTechInfo.setTechCode(String.valueOf(formData.get("L_techCode")));
		newTechInfo.setStartDate(new Date());
		//状态-开展中
		newTechInfo.setTechStatus(TechStatusEmun.RUNNNING.getCode());
		//项目分类：1-Ⅰ类，2-Ⅱ类，3-Ⅲ类
		String techClass = String.valueOf(formData.get("L_techClass"));
		if(!ObjectUtils.isEmpty(techClass) && !techClass.equals("null")){
			if(techClass.equals(TechClassEmun.I.getName())){
				techClass = TechClassEmun.I.getCode();
			} else if(techClass.equals(TechClassEmun.II.getName())){
				techClass = TechClassEmun.II.getCode();
			} else if(techClass.equals(TechClassEmun.III.getName())){
				techClass = TechClassEmun.III.getCode();
			}
		}
		newTechInfo.setTechClass(techClass);
		//技术类型：1-手术类，2-治疗性操作，3-检验检查类，4-其他类
		String operation = String.valueOf(formData.get("L_operation"));
//		String treatment = String.valueOf(formData.get("L_treatment"));
//		String examination = String.valueOf(formData.get("L_examination"));
//		String other = String.valueOf(formData.get("L_other"));
		String techType = null;
		if(!ObjectUtils.isEmpty(operation) && !operation.equals("null") && operation.equals(TechTypeEmun.OPERATION.getName())){
			techType = TechTypeEmun.OPERATION.getCode();
		} else if(!ObjectUtils.isEmpty(operation) && !operation.equals("null") && operation.equals(TechTypeEmun.TREATMENT.getName())){
			techType = TechTypeEmun.TREATMENT.getCode();
		} else if(!ObjectUtils.isEmpty(operation) && !operation.equals("null") && operation.equals(TechTypeEmun.EXAMINATION.getName())){
			techType = TechTypeEmun.EXAMINATION.getCode();
		} else if(!ObjectUtils.isEmpty(operation) && !operation.equals("null") && operation.equals(TechTypeEmun.OTHER.getName())){
			techType = TechTypeEmun.OTHER.getCode();
		}
		newTechInfo.setTechType(techType);
		//是否新技术-是
		newTechInfo.setIsNewTech(IsNewTechEmun.IS.getCode());
		//是否为限制类技术：0-否，1-是
		String isRstdTech = String.valueOf(formData.get("L_isRstdTech"));
		if(!ObjectUtils.isEmpty(isRstdTech) && !isRstdTech.equals("null")){
			isRstdTech = isRstdTech.equals(IsRstdTechEmun.IS.getName()) ? IsRstdTechEmun.IS.getCode() : IsRstdTechEmun.NOT.getCode();
			newTechInfo.setIsRstdTech(isRstdTech);
		}
		//项目负责人
		String proEmployeeNo = String.valueOf(formData.get("L_proEmployeeId"));
		newTechInfo.setProEmployeeNo(proEmployeeNo);
		newTechInfo.setProEmployeeId(proEmployeeNo);
		EmployeeRespVo employeeResp = newTechParticipantsMapper.selectByEmployeeNo(proEmployeeNo);
		if(employeeResp != null){
			newTechInfo.setProEmployeeName(employeeResp.getEmployeeName());
		}
		//评估次数为0
		newTechInfo.setEvaluationCount(0);
		

		List<DictItemResp> lvHosps = dictItemFeignService.getDictItemByTypeCode("auth_lv_hosp").getObject();
		List<DictItemResp> lvNats = dictItemFeignService.getDictItemByTypeCode("auth_lv_nat").getObject();
		//如果是手术类
		if(techType.equals(TechTypeEmun.OPERATION.getCode())){
			//手术/操作 名称
			String itemName = String.valueOf(formData.get("L_itemName"));
			if(!ObjectUtils.isEmpty(itemName) && !itemName.equals("null")){
				newTechInfo.setItemOpName(itemName);
			}
			//手术/操作 编码
			String itemCode = String.valueOf(formData.get("L_itemCode"));
			if(!ObjectUtils.isEmpty(itemCode) && !itemCode.equals("null")){
				newTechInfo.setItemOpCode(itemCode);
			}
			//手术院内分级
			String authLvHosp = String.valueOf(formData.get("L_authLvHosp"));
			if(!ObjectUtils.isEmpty(authLvHosp) && !authLvHosp.equals("null")){
				DictItemResp area1 = lvHosps.stream().filter(j -> StrUtil.equals(authLvHosp, j.getItemName())).findFirst().orElse(null);
				newTechInfo.setAuthLvHosp(null == area1 ? authLvHosp : area1.getItemCode());
				newTechInfo.setAuthLvHospText(authLvHosp);
			}
			//手术国家分级
			String authLvNat = String.valueOf(formData.get("L_authLvNat"));
			if(!ObjectUtils.isEmpty(authLvNat) && !authLvNat.equals("null")){
				DictItemResp area1 = lvNats.stream().filter(j -> StrUtil.equals(authLvNat, j.getItemName())).findFirst().orElse(null);
				newTechInfo.setAuthLvNat(null == area1 ? authLvNat : area1.getItemCode());
				newTechInfo.setAuthLvNatText(authLvNat);
			}
		} else if(techType.equals(TechTypeEmun.TREATMENT.getCode())){
			//治疗操作名称
			String treatItemName = String.valueOf(formData.get("L_treatItemName"));
			if(!ObjectUtils.isEmpty(treatItemName) && !treatItemName.equals("null")){
				newTechInfo.setItemOpName(treatItemName);
			}
			//治疗操作编码
			String treatItemCode = String.valueOf(formData.get("L_treatItemCode"));
			if(!ObjectUtils.isEmpty(treatItemCode) && !treatItemCode.equals("null")){
				newTechInfo.setItemOpCode(treatItemCode);
			}
			
			//治疗操作院内分级
			String treatAuthLvHosp = String.valueOf(formData.get("L_treatAuthLvHosp"));
			if(!ObjectUtils.isEmpty(treatAuthLvHosp) && !treatAuthLvHosp.equals("null")){
				DictItemResp area1 = lvHosps.stream().filter(j -> StrUtil.equals(treatAuthLvHosp, j.getItemName())).findFirst().orElse(null);
				newTechInfo.setAuthLvHosp(null == area1 ? treatAuthLvHosp : area1.getItemCode());
			}
			//治疗操作国家分级
			String treatAuthLvNat = String.valueOf(formData.get("L_treatAuthLvNat"));
			if(!ObjectUtils.isEmpty(treatAuthLvNat) && !treatAuthLvNat.equals("null")){
				DictItemResp area1 = lvNats.stream().filter(j -> StrUtil.equals(treatAuthLvNat, j.getItemName())).findFirst().orElse(null);
				newTechInfo.setAuthLvNat(null == area1 ? treatAuthLvNat : area1.getItemCode());
			}
		}
		//项目周期
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String cycleStartDate = String.valueOf(formData.get("L_cycleStartDate"));
		if(!ObjectUtils.isEmpty(cycleStartDate) && !cycleStartDate.equals("null")){
			try {
				newTechInfo.setCycleStartDate(sdf.parse(cycleStartDate));
			} catch (ParseException e) {
				log.error("项目周期开始日期转化失败");
			}
		}
		String cycleEndDate = String.valueOf(formData.get("L_cycleEndDate"));
		if(!ObjectUtils.isEmpty(cycleEndDate) && !cycleEndDate.equals("null")){
			try {
				newTechInfo.setCycleEndDate(sdf.parse(cycleEndDate));
			} catch (ParseException e) {
				log.error("项目周期结束日期转化失败");
			}
		}
		//申请时间
		String applyDate = String.valueOf(formData.get("L_applyDate"));
		if(!ObjectUtils.isEmpty(applyDate) && !applyDate.equals("null")){
			try {
				newTechInfo.setApplyDate(sdf.parse(applyDate));
			} catch (ParseException e) {
				log.error("申请日期转化失败");
			}
		}
		//参与项目人员
		saveParticipants(newTechInfo);
		
		/**
		 * 推送授权总览-手术权限-项目负责人添加一条数据
		 * 手术类型-手术类和治疗操作类才有
		 */
		if(!ObjectUtils.isEmpty(techType) 
				&& (techType.equals(TechTypeEmun.OPERATION.getCode()) || techType.equals(TechTypeEmun.TREATMENT.getCode()))){
			saveQuaAuth(newTechInfo);
		}

		//新增新技术项目记录
		save(newTechInfo);
	}
	
	/**
	 * 保存参与项目人员
	 * @param newTechInfo
	 */
	private void saveParticipants(NewTechInfo newTechInfo){
		//根据子表表名，动态查询关联数据  participantsTableName
		List<Map<String,Object>> result = newTechParticipantsMapper.getChildDataByWorkFlowId(newTechInfo.getId(), participantsTableName);
		if(CollUtil.isNotEmpty(result)) {
			result.stream().forEach(vo->{
				NewTechParticipants newTechParticipants = new NewTechParticipants();
				newTechParticipants.setNewTechId(newTechInfo.getId());
				String code = (String) vo.get("code");
				String zw = (String) vo.get("zw");
				if(!ObjectUtils.isEmpty(code)){
					//根据用户ID查询人员信息
		   			//根据负责人ID关联查询信息填充其他信息
					EmployeeRespVo employeeResp = newTechParticipantsMapper.selectByEmployeeNo(code);
					if(employeeResp != null){
						newTechParticipants.setEmployeeId(employeeResp.getEmployeeId());
						newTechParticipants.setEmployeeNo(employeeResp.getEmployeeNo());
						newTechParticipants.setEmployeeName(employeeResp.getEmployeeName());
						newTechParticipants.setJobtitle(zw);
						newTechParticipants.setTel(employeeResp.getTel());
						newTechParticipants.setOrgId(employeeResp.getOrgId());
						newTechParticipants.setOrgName(employeeResp.getOrgName());
						newTechParticipants.setDegree(employeeResp.getDegree());
						newTechParticipantsService.save(newTechParticipants);
					}
				}
			});
		}
	}
	
	/**
	 * 保存开展患者
	 * @param workflowInstId
	 * @param newTechCode
	 */
	private void savePatientInfo(String workflowInstId, String newTechCode){
		//根据子表表名，动态查询关联数据  participantsTableName
		List<Map<String,Object>> result = newTechParticipantsMapper.getChildDataByWorkFlowId(workflowInstId, patientInfoTableName);
		if(CollUtil.isNotEmpty(result)) {
			result.stream().forEach(vo->{
				NewTechPatientInfo newTechPatientInfo = new NewTechPatientInfo();
				newTechPatientInfo.setTechCode(newTechCode);
				String in_patient_no = (String) vo.get("in_patient_no");//病例号
				String patient_name = (String) vo.get("patient_name");//姓名
				String sex = (String) vo.get("sex");//姓别
				String main_diagnosis = (String) vo.get("main_diagnosis");//主要诊断
				String operation_date = (String) vo.get("operation_date");//手术日期
				String is_complaint_or_dispute = (String) vo.get("is_complaint_or_dispute");//是否有医疗投诉纠纷
				String is_severe_complications = (String) vo.get("is_severe_complications");//有无并发症
				String is_adverse_event = (String) vo.get("is_adverse_event");//是否有不良事件
				String is_unplanned_reoperation = (String) vo.get("is_unplanned_reoperation");//是否有非计划再次手术
				String in_hosp_days = (String) vo.get("in_hosp_days");//入院天数
				String sum_fee = (String) vo.get("sum_fee");//总费用
				if(!ObjectUtils.isEmpty(in_patient_no) && !ObjectUtils.isEmpty(patient_name)){
					newTechPatientInfo.setInPatientNo(in_patient_no);
					newTechPatientInfo.setPatientName(patient_name);
					newTechPatientInfo.setSex(sex);
					newTechPatientInfo.setMainDiagnosis(main_diagnosis);
					newTechPatientInfo.setOperationDate(operation_date);
					newTechPatientInfo.setIsComplaintOrDispute(is_complaint_or_dispute);
					newTechPatientInfo.setIsSevereComplications(is_severe_complications);
					newTechPatientInfo.setIsAdverseEvent(is_adverse_event);
					newTechPatientInfo.setIsUnplannedReoperation(is_unplanned_reoperation);
					newTechPatientInfo.setInHospDays(in_hosp_days);
					newTechPatientInfo.setSumFee(sum_fee);
					newTechPatientInfoService.save(newTechPatientInfo);
				}
			});
		}
	}
	
	/**
	 * 保存资质授权总览-手术授权
	 * 1、全新的技术，即手术编码在我们的手术目录里面没有的手术，准入成功之后，手术目录新增一条手术编码，手术授权新增一条。
	 * 2、常规技术，但是方式方法改变的新技术，即手术编码已存在手术目录， 准入成功后分2种情况：
	 * 1）医生已授权这项常规但是是新技术的权限，那就不需要在手术授权中新增一条（是否新技术还是“否”）。
	 * 2）医生未授权这项常规但是是新技术的权限，就需要在手术授权中新增一条，并标记该条授权是新技术产生的（是否新技术还是否）。
	 */
	private void saveQuaAuth(NewTechInfo newTechInfo){
		PlatformResult<GlobalSetting> obj = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = obj.getObject().getOrgCode();
		
		Example example = new Example(QuaAuthCfg.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("itemCode", newTechInfo.getItemOpCode());
		List<QuaAuthCfg> records = quaAuthCfgMapper.selectByExample(example);
		if(CollUtil.isEmpty(records)){
			//全新技术
			saveQuaAuthCfg(newTechInfo, orgCode);
		} else {
			QuaAuthCfg quaAuthCfg = records.get(0);
			//将新技术的关于常规手术的信息补充
			newTechInfo.setItemOpName(quaAuthCfg.getItemName());
			newTechInfo.setAuthLvHosp(quaAuthCfg.getAuthLvHosp());
			newTechInfo.setAuthLvNat(quaAuthCfg.getAuthLvNat());
			String quaAuthType = quaAuthCfg.getQuaAuthType();
			//手术类-A01A01A01A02   治疗操作性-A01A01A01A04
			if(ObjectUtils.isEmpty(quaAuthType) && quaAuthType.equals("A01A01A01A02")){
				newTechInfo.setTechType(TechTypeEmun.OPERATION.getCode());
			} else if(ObjectUtils.isEmpty(quaAuthType) && quaAuthType.equals("A01A01A01A04")){
				newTechInfo.setTechType(TechTypeEmun.TREATMENT.getCode());
			}
			
			//判断负责人是否有该项新技术的手术权限
			Page page = new Page();
			QuaAuthMgtParam record = new QuaAuthMgtParam();
			record.setItemCode(newTechInfo.getItemOpCode());
			record.setEmpCode(newTechInfo.getProEmployeeNo());
			List<QuaMgtAndDetl> result = quaAuthMgtMapper.getQuaMgtAndDetl(page, record);
			if(CollUtil.isEmpty(result)){
				//项目负责人没权限，则新增一条手术权限， 并推送到his
				saveQuaAuthItemDetl(newTechInfo, quaAuthCfg, orgCode);
			} else {
				log.info("============常规技术，申请人手术权限已存在，无需推送HIS==================");
			}
		}
	}
	
	/**
	 * 保存全新技术
	 * @param newTechInfo
	 * @param orgCode
	 */
	private void saveQuaAuthCfg(NewTechInfo newTechInfo, String orgCode){
		QuaAuthItemDetl quaAuthItemDetl = new QuaAuthItemDetl();
		//资质授权类型根据技术类型进行选择
		String techType = newTechInfo.getTechType();
		//手术类-A01A01A01A02   治疗操作性-A01A01A01A04
		String quaAuthType = "";
		if(techType.equals(TechTypeEmun.OPERATION.getCode())){
			quaAuthType = "A01A01A01A02";
		} else if(techType.equals(TechTypeEmun.TREATMENT.getCode())){
			quaAuthType = "A01A01A01A04";
		}
		//手术/操作 编码
		quaAuthItemDetl.setItemCode(newTechInfo.getItemOpCode());
		//手术/操作名称
		quaAuthItemDetl.setItemName(newTechInfo.getItemOpName());
		
		//资质类型-手术权限
		quaAuthItemDetl.setQuaAuthType(quaAuthType);
		//授权状态(0失效1正常)
		quaAuthItemDetl.setAuthStatus("1");
		//关联流程ID
		quaAuthItemDetl.setWorkflowId(newTechInfo.getId());
		quaAuthItemDetl.setAuthLvHosp(newTechInfo.getAuthLvHosp());
		quaAuthItemDetl.setAuthLvNat(newTechInfo.getAuthLvNat());
		//项目负责人
		quaAuthItemDetl.setEmployeeId(newTechInfo.getProEmployeeId());
		//根据负责人ID关联查询信息填充其他信息
		EmployeeRespVo employeeResp = newTechParticipantsMapper.selectByEmployeeNo(newTechInfo.getProEmployeeId());
		if(employeeResp != null){
			quaAuthItemDetl.setEmployeeNo(employeeResp.getEmployeeNo());
			quaAuthItemDetl.setEmployeeName(employeeResp.getEmployeeName());
			quaAuthItemDetl.setIdentityNumber(employeeResp.getIdentityNumber());
			quaAuthItemDetl.setJobtitle(employeeResp.getJobtitle());
			quaAuthItemDetl.setTel(employeeResp.getTel());
			quaAuthItemDetl.setOrgId(employeeResp.getOrgId());
			quaAuthItemDetl.setOrgName(employeeResp.getOrgName());
			//申请时间
			quaAuthItemDetl.setAppyTime(newTechInfo.getApplyDate());
			//生效时间-在资质授权里面是endtime，begntime已弃用
			quaAuthItemDetl.setEndtime(new Date());
		}
		
		QuaAuthCfg quaAuthCfg = new QuaAuthCfg();
		quaAuthCfg.setIsMiniOprn("0");
		//是否新技术项目
		quaAuthCfg.setIsNewTech(newTechInfo.getIsNewTech());
		//是否限制性技术
		quaAuthCfg.setIsRstdTech(newTechInfo.getIsRstdTech());
		quaAuthCfg.setItemCode(quaAuthItemDetl.getItemCode());
		quaAuthCfg.setItemName(quaAuthItemDetl.getItemName());
		//资质类型-手术权限
		quaAuthCfg.setQuaAuthType(quaAuthType);
		quaAuthCfg.setAuthLvHosp(newTechInfo.getAuthLvHosp());
		quaAuthCfg.setAuthLvNat(newTechInfo.getAuthLvNat());
		//状态启用
		quaAuthCfg.setIsEnable("1");
		quaAuthCfg.setId(IdGeneraterUtils.nextId());
		quaAuthCfgService.save(quaAuthCfg);
		
		//标记该条是新技术授权的
		quaAuthItemDetl.setAuthByNewTech("1");
		quaAuthItemDetlService.save(quaAuthItemDetl);
		
		log.info("============全新技术推送HIS==================");
		
		//推送手术字典
		pushQuaAuthCfg(quaAuthCfg, newTechInfo, orgCode, false);
		
	    //推送手术授权
	    pushQuaAuthItemDetl(quaAuthItemDetl, newTechInfo, orgCode, quaAuthCfg.getId());
		
	}
	
	/**
	 * 推送手术字典到his
	 * @param quaAuthCfg
	 * @param newTechInfo
	 * @param orgCode
	 * @param del --删除标识
	 */
	private void pushQuaAuthCfg(QuaAuthCfg quaAuthCfg, NewTechInfo newTechInfo, String orgCode, boolean del){
		log.info("========新技术推送HIS手术字典回调==========" + newTechInfo.getTechName());
		
		String itemNamePy = StrUtil.isNotEmpty(quaAuthCfg.getItemName()) ? PinyinUtil.getFirstLetter(quaAuthCfg.getItemName(), "").toUpperCase() : "";
		String itemNameWb = StrUtil.isNotEmpty(quaAuthCfg.getItemName()) ? StrConvertPinyin.getWBCode(quaAuthCfg.getItemName()).toUpperCase() : "";
		
		//省人医推送给HIS
	    if("hnsrmyy".equals(orgCode)) {
	    	Map<String,String> pushMap = new HashMap<>();
			pushMap.put("sscode", quaAuthCfg.getItemCode());
			pushMap.put("ssname", quaAuthCfg.getItemName());
			pushMap.put("pycode", itemNamePy);
			pushMap.put("wbcode", itemNameWb);
			String techType = newTechInfo.getTechType();//技术类型：1-手术类，2-治疗操作类，3-检验检查类，4-其他类
			//手术类型1治疗性操作2诊断性操作3介入治疗4手术
			if(TechTypeEmun.OPERATION.getCode().equals(techType)){
				pushMap.put("sstype", "4");
			} else if(TechTypeEmun.TREATMENT.getCode().equals(techType)){
				pushMap.put("sstype", "1");
			}
			//是否四级手术 非4级，1-四级
			pushMap.put("sjss", quaAuthCfg.getAuthLvNat());
			//是否微创手术 0否，1是
			pushMap.put("wcss", quaAuthCfg.getIsMiniOprn());
			//院内手术级别,1一级手术,2 二级手术,3 三级手术，4四级手术
			pushMap.put("ynjb", quaAuthCfg.getAuthLvHosp());
			//是否新技术，1是，0否
			pushMap.put("isnew", newTechInfo.getIsNewTech());
			//有效标识，0有效，1无效
			pushMap.put("Flag", quaAuthCfg.getIsEnable());
	    	commInterfaceRegisterService.pushHnsrmyyItem(newTechInfo.getProEmployeeNo(), newTechInfo.getProEmployeeName(), pushMap);
	    }else {
	    	//推送平台
	    	long startTime = System.currentTimeMillis(); 
	    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	    	Example example = new Example(CommInterfaceRegister.class);
	    	Example.Criteria criteria = example.createCriteria();
	    	criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
	    	criteria.andEqualTo("callType", "1");
	    	
	    	CommInterfaceRegister commInterfaceRegister = null;
	    	String interfaceName = null;
	    	boolean pushFlag = false;//推送标识
			Map<String,Object> requestParams = new HashMap<>();
	    	if(!del){
	    		interfaceName = "集成平台-保存手术字典";
	    		criteria.andEqualTo("interfaceName", interfaceName);
	    		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
	    		if(CollectionUtils.isNotEmpty(registerList)) {
	    			commInterfaceRegister = registerList.get(0);
	    			if("1".equals(commInterfaceRegister.getStatus())) {//接口启用
	    				pushFlag = true;
	    				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
	    					requestParams.put("ServiceName", "OuterBaseDataService");
	    					requestParams.put("InterfaceName", "SaveOperationDict");
	    					requestParams.put("IsCompress", "false");
	    					
	    					Map<String,Object> Parameter = new HashMap<>();
	    					Parameter.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	    					Parameter.put("operatorId", newTechInfo.getProEmployeeId());
	    					Parameter.put("operatorName", newTechInfo.getProEmployeeName());
	    					Parameter.put("createUserId",  newTechInfo.getProEmployeeId());
	    					Parameter.put("updateUserId",  newTechInfo.getProEmployeeId());
	    					Parameter.put("createDate",  sdf.format(new Date()));
	    					Parameter.put("updateDate",  sdf.format(new Date()));
	    					Parameter.put("isDelete", "N");
	    					Parameter.put("operCode", quaAuthCfg.getItemCode());
	    					Parameter.put("opername", quaAuthCfg.getItemName());
	    					Parameter.put("wbCode", itemNameWb);
	    					Parameter.put("pyCode", itemNamePy);
	    					//状态 0-无效 1 -有效
	    					Parameter.put("status", quaAuthCfg.getIsEnable());
	    					Parameter.put("backup1", quaAuthCfg.getItemCode());
	    					Parameter.put("backup2", quaAuthCfg.getItemName());
	    					Parameter.put("back_up1", quaAuthCfg.getItemCode());
	    					Parameter.put("back_up2", quaAuthCfg.getItemName());//医保手术名称
	    					//是否新技术（0-否，1-是）
	    					Parameter.put("is_newskill", newTechInfo.getIsNewTech());
	    					//是否限制性技术（0-否，1-是）
	    					Parameter.put("is_confineskill", newTechInfo.getIsRstdTech());
	    					Parameter.put("levelCode", newTechInfo.getAuthLvHosp());
	    					Parameter.put("levelName", newTechInfo.getAuthLvHospText());
	    					
	    					requestParams.put("Parameter", Parameter);
	    				} else {
	    					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	    					requestParams.put("operatorId", newTechInfo.getProEmployeeId());
	    					requestParams.put("operatorName", newTechInfo.getProEmployeeName());
	    					requestParams.put("createUserId",  newTechInfo.getProEmployeeId());
	    					requestParams.put("updateUserId",  newTechInfo.getProEmployeeId());
	    					requestParams.put("createDate",  sdf.format(new Date()));
	    					requestParams.put("updateDate",  sdf.format(new Date()));
	    					requestParams.put("isDelete", "N");
	    					requestParams.put("operCode", quaAuthCfg.getItemCode());
	    					requestParams.put("opername", quaAuthCfg.getItemName());
	    					requestParams.put("wbCode", itemNameWb);
	    					requestParams.put("pyCode", itemNamePy);
	    					//状态 0-无效 1 -有效
	    					requestParams.put("status", quaAuthCfg.getIsEnable());
	    					requestParams.put("backup1", quaAuthCfg.getItemCode());
	    					requestParams.put("backup2", quaAuthCfg.getItemName());
	    					requestParams.put("back_up1", quaAuthCfg.getItemCode());
	    					requestParams.put("back_up2", quaAuthCfg.getItemName());//医保手术名称
	    					//是否新技术（0-否，1-是）
	    					requestParams.put("is_newskill", newTechInfo.getIsNewTech());
	    					//是否限制性技术（0-否，1-是）
	    					requestParams.put("is_confineskill", newTechInfo.getIsRstdTech());
	    					requestParams.put("levelCode", newTechInfo.getAuthLvHosp());
	    					requestParams.put("levelName", newTechInfo.getAuthLvHospText());
	    				}
	    			}
	    		}
	    		
	    	} else {//删除手术字典
	    		interfaceName = "集成平台-删除手术字典";
	    		criteria.andEqualTo("interfaceName", interfaceName);
	    		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
	    		if(CollectionUtils.isNotEmpty(registerList)) {
	    			commInterfaceRegister = registerList.get(0);
	    			if("1".equals(commInterfaceRegister.getStatus())) {//接口启用
	    				pushFlag = true;
	    				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
	    					requestParams.put("ServiceName", "OuterBaseDataService");
	    					requestParams.put("InterfaceName", "SaveOperationDict");
	    					requestParams.put("IsCompress", "false");

	    					Map<String,Object> Parameter = new HashMap<>();
	    					Parameter.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	    					Parameter.put("operatorId", newTechInfo.getProEmployeeId());
	    					Parameter.put("operatorName", newTechInfo.getProEmployeeName());
	    					Parameter.put("operCode", quaAuthCfg.getItemCode());
	    					//状态  1:正常 0:停用
	    					Parameter.put("status", "0");
	    					
	    					requestParams.put("Parameter", Parameter);
	    					
	    				} else {
	    					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
	    					requestParams.put("operatorId", newTechInfo.getProEmployeeId());
	    					requestParams.put("operatorName", newTechInfo.getProEmployeeName());
	    					requestParams.put("operCode", quaAuthCfg.getItemCode());
	    					//状态  1:正常 0:停用
	    					requestParams.put("status", "0");
	    				}
	    			}
	    		}
	    	}
	    	
	    	if(!pushFlag){
	    		return;
	    	}
	    	
	    	long endTime = System.currentTimeMillis();  
			
	    	String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
			
			String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
			Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
			String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
			JSONObject reuslt = JSON.parseObject(bodyStr);
			
			CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
			commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
			commInterfaceLogs.setInterfaceName(interfaceName);
			commInterfaceLogs.setInterworkPlatform("集成平台");
			commInterfaceLogs.setRequestUrl(requestUrl);
			commInterfaceLogs.setRequestParams(jsonString);
			commInterfaceLogs.setResponseParams(bodyStr);
			
			if("200".equals(reuslt.getString("Code"))) {
				commInterfaceLogs.setResponseStatus("1");
			}else {
				commInterfaceLogs.setResponseStatus("2");
			}
			
			commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
			commInterfaceLogsService.save(commInterfaceLogs);
			log.info("========新技术推送HIS手术字典回调结束==========");
	    }
	}
	
	/**
	 * 推送手术授权到his
	 * 
	 * @param quaAuthItemDetl
	 * @param newTechInfo
	 * @param orgCode
	 * @param quaAuthCfgId 手术字典ID
	 */
	private void pushQuaAuthItemDetl(QuaAuthItemDetl quaAuthItemDetl, NewTechInfo newTechInfo, String orgCode, String quaAuthCfgId){
		log.info("========新技术推送HIS手术授权回调==========" + newTechInfo.getTechName());
	    
	    //省人医推送给HIS
	    if("hnsrmyy".equals(orgCode)) {
	    	List<Map<String,String>> pushList = new ArrayList<>();
			Map<String,String> pushObj = new HashMap<>();
			pushObj.put("doctorId", quaAuthItemDetl.getEmployeeNo());
			pushObj.put("doctorName", quaAuthItemDetl.getEmployeeName());
			pushObj.put("SSCODE", quaAuthItemDetl.getItemCode());
			pushObj.put("ssname", quaAuthItemDetl.getItemName());
			pushObj.put("deptID", quaAuthItemDetl.getOrgId());
			pushObj.put("deptName", quaAuthItemDetl.getOrgName());
			//有效标识，0有效，1删除
			if("0".equals(quaAuthItemDetl.getAuthStatus())){
				pushObj.put("flag", "1");
			} else if("1".equals(quaAuthItemDetl.getAuthStatus())){
				pushObj.put("flag", "0");
			}
			
			pushList.add(pushObj);
	    	commInterfaceRegisterService.pushHnsrmyyOperation(newTechInfo.getProEmployeeNo(), newTechInfo.getProEmployeeName(), pushList);
	    }else {
	    	//推送平台
	    	long startTime = System.currentTimeMillis(); 
	    	Example example = new Example(CommInterfaceRegister.class);
	    	Example.Criteria criteria = example.createCriteria();
	    	criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
	    	criteria.andEqualTo("callType", "1");
    		criteria.andEqualTo("interfaceName", "集成平台-保存人员手术权限");
    		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
    		if(CollectionUtils.isNotEmpty(registerList)) {
    			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
    			if("1".equals(commInterfaceRegister.getStatus())) {//接口启用
    				Map<String, String> empInfo = medDoctorRoleMapper.selectHisEmployeeNoByCode(newTechInfo.getProEmployeeNo());
    				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
    				
    				Map<String,Object> requestParams = new HashMap<>();
    				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
    					
    					requestParams.put("ServiceName", "OuterBaseDataService");
    					requestParams.put("InterfaceName", "SaveMemberOperPower");
    					requestParams.put("IsCompress", "false");
    					
    					Map<String,Object> Parameter = new HashMap<>();
    					Parameter.put("code",  empInfo.get("his_employee_no"));
    					Parameter.put("updateBy",  "1");
    					Parameter.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
    					
    					Map<String,Object> operPowerInfo = new HashMap<>();
    					
    					operPowerInfo.put("operPowerUpdateType", "0");
    					
    					List<Map<String,Object>> menberOperPowers = new ArrayList<>();
    					
						Map<String,Object> power = new HashMap<>();
						power.put("operId", quaAuthCfgId); // 手术字典的ID需要his修改
						power.put("operName", quaAuthItemDetl.getItemName());
						power.put("icdCode", quaAuthItemDetl.getItemCode());
						//0：无权限，1：有权限
						power.put("powerValue", quaAuthItemDetl.getAuthStatus());
						
						menberOperPowers.add(power);
    					
    					operPowerInfo.put("menberOperPowers", menberOperPowers);
    					
    					Parameter.put("operPowerInfo", operPowerInfo);
    					
    					requestParams.put("Parameter", Parameter);
    				} else {
    					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
    					requestParams.put("updateBy", "1");
    					
    					requestParams.put("code", empInfo.get("his_employee_no"));
    					
    					Map<String, Object> operPowerInfo = new HashMap<>();
    					operPowerInfo.put("operPowerUpdateType", "0");
    					
    					List<Map<String,Object>> menberOperPowers = new ArrayList<>();
    					
    					Map<String,Object> power = new HashMap<>();
						power.put("operId", quaAuthCfgId); // 手术字典的ID需要his修改
						power.put("operName", quaAuthItemDetl.getItemName());
						power.put("icdCode", quaAuthItemDetl.getItemCode());
						//0：无权限，1：有权限
						power.put("powerValue", quaAuthItemDetl.getAuthStatus());
						
						menberOperPowers.add(power);
    					
    					operPowerInfo.put("menberOperPowers", menberOperPowers);
    					
    					requestParams.put("operPowerInfo", operPowerInfo);
    				}
    				
    				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
    				
    				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
    				
    				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
    				JSONObject reuslt = JSON.parseObject(bodyStr);
    				
    				long endTime = System.currentTimeMillis();  
    				
    				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
    				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
    				commInterfaceLogs.setInterfaceName("集成平台-保存人员手术权限");
    				commInterfaceLogs.setInterworkPlatform("集成平台");
    				commInterfaceLogs.setRequestUrl(requestUrl);
    				commInterfaceLogs.setRequestParams(jsonString);
    				commInterfaceLogs.setResponseParams(bodyStr);
    				
    				if("200".equals(reuslt.getString("Code"))) {
    					commInterfaceLogs.setResponseStatus("1");
    				}else {
    					commInterfaceLogs.setResponseStatus("2");
    				}
    				
    				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
    				commInterfaceLogsService.save(commInterfaceLogs);
    			}
    		}
	    }
	    
	    log.info("========新技术推送HIS手术授权回调结束==========" + newTechInfo.getTechName());
	}
	
	/**
	 * 新增负责人的手术权限
	 * @param newTechInfo
	 * @param quaAuthCfg
	 */
	private void saveQuaAuthItemDetl(NewTechInfo newTechInfo, QuaAuthCfg quaAuthCfg, String orgCode){
		QuaAuthItemDetl quaAuthItemDetl = new QuaAuthItemDetl();
		//资质授权类型根据技术类型进行选择
		String techType = newTechInfo.getTechType();
		//手术类-A01A01A01A02   治疗操作性-A01A01A01A04
		String quaAuthType = "";
		if(techType.equals(TechTypeEmun.OPERATION.getCode())){
			quaAuthType = "A01A01A01A02";
		} else if(techType.equals(TechTypeEmun.TREATMENT.getCode())){
			quaAuthType = "A01A01A01A04";
		}
		//手术/操作 编码
		quaAuthItemDetl.setItemCode(quaAuthCfg.getItemCode());
		//手术/操作名称
		quaAuthItemDetl.setItemName(quaAuthCfg.getItemName());
		
		//资质类型-手术权限
		quaAuthItemDetl.setQuaAuthType(quaAuthType);
		//授权状态(0失效1正常)
		quaAuthItemDetl.setAuthStatus("1");
		//关联流程ID
		quaAuthItemDetl.setWorkflowId(newTechInfo.getId());
		quaAuthItemDetl.setAuthLvHosp(quaAuthCfg.getAuthLvHosp());
		quaAuthItemDetl.setAuthLvNat(quaAuthCfg.getAuthLvNat());
		//项目负责人
		quaAuthItemDetl.setEmployeeId(newTechInfo.getProEmployeeId());
		//根据负责人ID关联查询信息填充其他信息
		EmployeeRespVo employeeResp = newTechParticipantsMapper.selectByEmployeeNo(newTechInfo.getProEmployeeId());
		if(employeeResp != null){
			quaAuthItemDetl.setEmployeeNo(employeeResp.getEmployeeNo());
			quaAuthItemDetl.setEmployeeName(employeeResp.getEmployeeName());
			quaAuthItemDetl.setIdentityNumber(employeeResp.getIdentityNumber());
			quaAuthItemDetl.setJobtitle(employeeResp.getJobtitle());
			quaAuthItemDetl.setTel(employeeResp.getTel());
			quaAuthItemDetl.setOrgId(employeeResp.getOrgId());
			quaAuthItemDetl.setOrgName(employeeResp.getOrgName());
			//申请时间
			quaAuthItemDetl.setAppyTime(newTechInfo.getApplyDate());
			//生效时间-在资质授权里面是endtime，begntime已弃用
			quaAuthItemDetl.setEndtime(new Date());
			//标记该条是新技术授权的
			quaAuthItemDetl.setAuthByNewTech("1");
			quaAuthItemDetlService.save(quaAuthItemDetl);
			//推送手术授权到his
			pushQuaAuthItemDetl(quaAuthItemDetl, newTechInfo, orgCode, quaAuthCfg.getId());
		}
		
	}

	@Transactional(readOnly = false)
	@Override
	public void transferNormal(Map<String, Object> formData) {
		// 流程实例id
		String workflowInstId = formData.get("workflowInstId").toString();
		//业务ID
		String businessId = formData.get("L_BusinessId").toString();
		// 伦理号
		String newTechCode = formData.get("L_techCode").toString();
		// 是否同意
		String result = formData.get("L_result").toString();
		if(!ObjectUtils.isEmpty(result) && result.equals("不同意该技术项目转为常规项目")){
			return;
		}
		
		//更新新技术新项目管理的状态由“开展中”变更为“转常规”
		NewTechInfo newTechInfo = selectByNewTechCode(newTechCode);
		if(null == newTechInfo){
			throw new RuntimeException("没有该伦理号的新技术!");
		}
		newTechInfo.setTechStatus(TechStatusEmun.NORMAL.getCode());
		newTechInfo.setTransformDate(new Date());
		newTechInfo.setNormalWorkflowInstId(workflowInstId);
		newTechInfo.setNormalBusinessId(businessId);
		mapper.updateByPrimaryKeySelective(newTechInfo);
		
		PlatformResult<GlobalSetting> obj = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = obj.getObject().getOrgCode();
		
		//手术类型-手术类和治疗操作类
		String techType = newTechInfo.getTechType();
		if(!ObjectUtils.isEmpty(techType) && (techType.equals(TechTypeEmun.OPERATION.getCode()) || techType.equals(TechTypeEmun.TREATMENT.getCode()))){
			//更新资质授权总览“是否新技术”标记为“否”
			log.info("============查询新技术授权的数据==转常规================");
			List<QuaAuthItemDetl> quaAuthItemDetlList = quaAuthItemDetlService.selectByWorkflowId(newTechInfo.getId(), "1");
			if(CollUtil.isNotEmpty(quaAuthItemDetlList)){
				QuaAuthItemDetl quaAuthItemDetl = quaAuthItemDetlList.get(0);
				String itemCode = quaAuthItemDetl.getItemCode();
				//根据手术编码查询关联的是否新技术项目
				List<QuaAuthCfg> quaAuthCfgList = quaAuthCfgService.getByItemCode(itemCode);
				if(CollUtil.isNotEmpty(quaAuthCfgList)){
					QuaAuthCfg quaAuthCfg = quaAuthCfgList.get(0);
					//判断手术字典是否是新技术标识，如果是"否"-则是常规技术  是否新技术项目0否1是
					String isNewTech = quaAuthCfg.getIsNewTech();
					if("1".equals(isNewTech)){
						quaAuthCfg.setIsNewTech(IsNewTechEmun.NOT.getCode());
						quaAuthCfg.setIsEnable("1");
						quaAuthCfgService.update(quaAuthCfg);
						//推送HIS的手术字典
						pushQuaAuthCfg(quaAuthCfg, newTechInfo, orgCode, false);
					}
				}
				
				//将新技术授权的标记改为0
				quaAuthItemDetl.setAuthByNewTech("0");
				quaAuthItemDetlService.update(quaAuthItemDetl);
			} else {
				log.info("============无新技术授权的数据，不用推送HIS==================");
			}
			
		}
		
	}

	@Transactional(readOnly = false)
	@Override
	public void evaluation(Map<String, Object> formData) {
		//流程实例ID
		String workflowInstId = formData.get("workflowInstId").toString();
		//流程业务ID
		String businessId = formData.get("L_BusinessId").toString();
		//流程发起人
		String processBy = formData.get("L_LaunchUserName").toString();
		//发起科室
		String processDept = formData.get("L_LaunchDeptName").toString();
		// 伦理号
		String newTechCode = formData.get("L_techCode").toString();
		//更新新技术新项目管理的评估次数，评估时间和评估结果
		NewTechInfo newTechInfo = selectByNewTechCode(newTechCode);
		if(null == newTechInfo){
			throw new RuntimeException("没有该伦理号的新技术!");
		}
		// 新技术是否中止
		//评估结果-合格
//		String evaluationResult = String.valueOf(formData.get("L_result1"));
		//同意
		String evaluationAgree = String.valueOf(formData.get("L_result2"));
		Integer evaluationCount = newTechInfo.getEvaluationCount();
		evaluationCount = evaluationCount == null ? 1 : evaluationCount + 1;
		newTechInfo.setEvaluationCount(evaluationCount);
		newTechInfo.setEvaluationDate(new Date());
//		newTechInfo.setEvaluationResult(evaluationResult);
		
		//保存评估记录
		NewTechEevaluation newTechEevaluation = new NewTechEevaluation();
		newTechEevaluation.setWorkflowInstId(workflowInstId);
		newTechEevaluation.setBusinessId(businessId);
		newTechEevaluation.setTechCode(newTechCode);
		newTechEevaluation.setEvaluationDate(new Date());
//		newTechEevaluation.setEvaluationResult(evaluationResult);
		newTechEevaluation.setProcessBy(processBy);
		newTechEevaluation.setProcessDept(processDept);
		
		//保存开展患者
		savePatientInfo(workflowInstId, newTechCode);
		
		//评审通过-继续
		if(!ObjectUtils.isEmpty(evaluationAgree) && evaluationAgree.contains("不同意")){

			newTechInfo.setEvaluationResult("不合格");
			newTechEevaluation.setEvaluationResult("不合格");
			//评审不通过，中止
			interrupt(formData, newTechInfo);
			//填充中止开始时间和中止结束时间
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String stopDateBegin = String.valueOf(formData.get("L_stopDateBegin"));
			if(!ObjectUtils.isEmpty(stopDateBegin) && !stopDateBegin.equals("null")){
				try {
					newTechEevaluation.setStopDateBegin(sdf.parse(stopDateBegin));
				} catch (ParseException e) {
					log.error("中止开始时间转化失败");
				}
			}
			String stopDateEnd = formData.get("L_stopDateEnd").toString();
			if(!ObjectUtils.isEmpty(stopDateEnd) && !stopDateEnd.equals("null")){
				try {
					newTechEevaluation.setStopDateEnd(sdf.parse(stopDateEnd));
				} catch (ParseException e) {
					log.error("中止结束时间转化失败");
				}
			}
		} else {
			newTechInfo.setEvaluationResult("合格");
			newTechEevaluation.setEvaluationResult("合格");
		}
		mapper.updateByPrimaryKeySelective(newTechInfo);
		newTechEevaluationService.save(newTechEevaluation);
		
	}

	/**
	 * 中止新技术
	 * 
	 * @param formData
	 * @param newTechInfo
	 */
	private void interrupt(Map<String, Object> formData, NewTechInfo newTechInfo) {
		//更新新技术新项目管理的状态由“开展中”变更为“已中止”
		newTechInfo.setTechStatus(TechStatusEmun.INTERRUPT.getCode());
		newTechInfo.setEndDate(new Date());
		//中止原因
		String reason = String.valueOf(formData.get("L_reason"));
		if(!ObjectUtils.isEmpty(reason) && !reason.equals("null")){
			newTechInfo.setReason(reason);
		}
		
		mapper.updateByPrimaryKeySelective(newTechInfo);
		
		PlatformResult<GlobalSetting> obj = globalSettingsFeignService.getGlobalSetting("Y");
		String orgCode = obj.getObject().getOrgCode();
		
		//将新技术授权的手术权限-更新资质授权总览种授权结果更新为 停止授权
		//手术类型-手术类和治疗操作类
		String techType = newTechInfo.getTechType();
		if(!ObjectUtils.isEmpty(techType) 
				&& (techType.equals(TechTypeEmun.OPERATION.getCode()) || techType.equals(TechTypeEmun.TREATMENT.getCode()))){
			log.info("============查询新技术授权的数据==中止================");
			List<QuaAuthItemDetl> quaAuthItemDetlList = quaAuthItemDetlService.selectByWorkflowId(newTechInfo.getId(), "1");
			if(CollUtil.isNotEmpty(quaAuthItemDetlList)){
				QuaAuthItemDetl quaAuthItemDetl = quaAuthItemDetlList.get(0);
				
				//根据手术编码查询手术目录分级，并将状态改为“禁用”
				List<QuaAuthCfg> quaAuthCfgList = quaAuthCfgService.getByItemCode(quaAuthItemDetl.getItemCode());
				String quaAuthCfgId = null;
				if(CollUtil.isNotEmpty(quaAuthCfgList)){
					QuaAuthCfg quaAuthCfg = quaAuthCfgList.get(0);
					quaAuthCfgId = quaAuthCfg.getId();
					//判断手术字典是否是新技术标识，如果是"否"-则是常规技术  是否新技术项目0否1是
					String isNewTech = quaAuthCfg.getIsNewTech();
					if("1".equals(isNewTech)){
						quaAuthCfg.setIsEnable("0");
						quaAuthCfgService.update(quaAuthCfg);
						//推送HIS的手术字典
						pushQuaAuthCfg(quaAuthCfg, newTechInfo, orgCode, true);
					}
				}
				
				//授权结果-停止授权
				quaAuthItemDetl.setAuthStatus("0");
				quaAuthItemDetl.setEndtime(new Date());
				quaAuthItemDetlService.update(quaAuthItemDetl);
				//推送HIS的手术授权
				pushQuaAuthItemDetl(quaAuthItemDetl, newTechInfo, orgCode, quaAuthCfgId);
			} else {
				log.info("============无新技术授权的数据，不用推送HIS==================");
			}
		}
		
	}

	@Deprecated
	@Override
	public void generateNewTechPatientInfoData(String startDate) {
		try {
			log.info("=========同步新技术开展病例数据==========");
			//查询开展中的新技术-手术类和治疗操作类
			Example example = new Example(NewTechInfo.class);
			Example.Criteria criteria = example.createCriteria();
			//状态
			criteria.andEqualTo("techStatus", TechStatusEmun.RUNNNING.getCode());
			//技术类型
			List<String> techTypeList = new ArrayList<>();
			techTypeList.add(TechTypeEmun.OPERATION.getCode());
			techTypeList.add(TechTypeEmun.TREATMENT.getCode());
			criteria.andIn("techType", techTypeList);
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			List<NewTechInfo> techList = mapper.selectByExample(example);
			if(CollUtil.isEmpty(techList)){
				return;
			}
			//key为手术编码，value为新技术编码
			Map<String, String> techMap = new HashMap<>();
			StringBuilder techSb = new StringBuilder("("); 
			for(int i = 0; i < techList.size(); i++){
				NewTechInfo tech = techList.get(i);
				techMap.put(tech.getItemOpCode(), tech.getTechCode());
				techSb.append("'").append(tech.getItemOpCode()).append("'");
				if(i != techList.size() - 1){
					techSb.append(",");
				}
			}
			techSb.append(")");
			
			List<NewTechPatientInfo> patientInfoList = new ArrayList<>();
			
			//判断是否省人医抽取患者信息
			if("1".equals(csltFlag)) {
				patientInfoList = getHnsrmyyPatientInfoList(techSb);
			} else if("2".equals(hisRequestVersion)){
				//文山版本
				
			} else {
				//平台标准版本
				patientInfoList = getPlatformPatientInfoList(techMap, startDate);
			}
			
			if(CollUtil.isEmpty(patientInfoList)){
				return;
			}
    		
    		//查询所有的患者数据
    		Example exampleP = new Example(NewTechPatientInfo.class);
			Example.Criteria criteriaP = exampleP.createCriteria();
			criteriaP.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			List<NewTechPatientInfo> piList = newTechPatientInfoMapper.selectByExample(exampleP);
			Set<String> set = new HashSet<>();
			if(CollUtil.isNotEmpty(piList)){
				for(NewTechPatientInfo pi : piList){
					set.add(pi.getInPatientNo() + pi.getOperationDate());
				}
			}
    		
			//将患者数据同步到 med_new_tech_patient_info 中
			List<NewTechPatientInfo> addPatientInfoList = new ArrayList<>();
    		for(NewTechPatientInfo info : patientInfoList){
    			if(!set.contains(info.getInPatientNo() + info.getOperationDate())){
    				info.setTechCode(techMap.get(info.getTechCode()));
    				//根据住院号
    				addPatientInfoList.add(info);
    			}
    		}
    		
			
			//发送数据量过大  拆分数据 
			List<List<NewTechPatientInfo>> saveList = CommonUtils.averageAssign(addPatientInfoList, 2000);
			
			//使用并行流插入数据
			saveList.stream().forEach(ls ->{
//				mapper.batchInsert(ls);	
	        });
    		
		} catch (Exception e) {
			log.error("同步新技术开展病例数据执行失败：" + e.getMessage(),e);
		}
	}
	
	/**
	 * 获取省人医患者数据
	 * 
	 * @param techSb
	 * @return
	 */
	private List<NewTechPatientInfo> getHnsrmyyPatientInfoList(StringBuilder techSb){
		List<NewTechPatientInfo> patientInfoList = new ArrayList<>();
		//根据开展中的新技术的手术编码从影子库查询上一周期的开展病例数据
		StringBuilder sb = new StringBuilder();
		
		sb.append(" select ysssrq 拟施手术日期,yxssrq 已行手术日期,a.inpatient_no 住院号,c.name 患者姓名,a.YSSS 拟施手术,a.YSSS_ICD9 拟施手术编码, "
				+ " b.YXSS 已行手术,b.YXSS_ICD9 已行手术编码,fun_base_deptname(deptid) 申请科室,fun_base_empname(a.ZDYS) 主刀医生, "
				+ " case c.SEX when 1 then '男' when 2 then '女' else '' END 性别,c.IN_DIAGNOSIS 入院诊断 "
				+ " from ss_apprecord a "
				+ " left join ss_arrrecord b on a.sno=b.sno "
				+ " inner join ZY_VINPATIENT_ALL_YB c on a.inpatient_id=c.INPATIENT_ID "
				+ " where a.bdelete=0 and b.bdelete=0 " 
				+ " ");
		sb.append(" AND b.YXSS_ICD9 in ").append(techSb.toString());
		sb.append(" AND yxssrq  >    TRUNC(SYSDATE -1)       "
				+ " ");
		log.info("===========sql:"+sb.toString());
		patientInfoList = HnsrmyyHisJdbcUtil.queryNewTechPatientInfo(sb.toString());//执行语句返回结果
		log.info("===========patientInfoList:" + patientInfoList.size());
		return patientInfoList;
	}
	
	/**
	 * 获取平台开展患者数据
	 * 1.手术患者数据：根据 4.2.14.查询手术记录（排台后）-查询前一天的数据，返回数据的手术编码过滤新技术的患者数据
	 * 2.计算入院天数：根据病人ID集合，查询 4.2.10.查询住院患者 -返回计算入院天数  出院时间-入院时间
	 * 3.病人总费用：根据病人ID集合，查询 4.2.12.查询住院病人账目信息 -返回总费用
	 * 
	 * @param techMap
	 * @return
	 */
	private List<NewTechPatientInfo> getPlatformPatientInfoList(Map<String, String> techMap, String startDate){
		long startTime = System.currentTimeMillis();
		
		List<NewTechPatientInfo> patientInfoList = new ArrayList<>();
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询手术记录");
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String startCompleteDate = startDate;
		if(ObjectUtils.isEmpty(startDate)){
			Calendar ca = Calendar.getInstance();
			ca.setTime(new Date());//当前时间
			ca.add(Calendar.DAY_OF_YEAR, -1);//当前时间前一天
			startCompleteDate = sdf.format(ca.getTime()) + " 00:00:00";
		}
		String endCompleteDate = sdf.format(new Date()) + " 23:59:59";
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && "2".equals(hisRequestVersion)) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterClinicService");//服务名
					requestParams.put("InterfaceName", "QueryInPatientOperationRecord");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
			       	paramsMap.put("startCompleteDate", startCompleteDate);//开始手术完成日期格式：YYYY-MM-DD H24:MI:S
			       	paramsMap.put("endCompleteDate", endCompleteDate);//结束手术完成日期格式：YYYY-MM-DD H24:MI:S
			       	paramsMap.put("pageIndex", "1");
			       	paramsMap.put("pageSize", Integer.MAX_VALUE);
			       	
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					requestParams.put("startCompleteDate", startCompleteDate);//开始手术完成日期格式：YYYY-MM-DD H24:MI:S
			       	requestParams.put("endCompleteDate", endCompleteDate);//结束手术完成日期格式：YYYY-MM-DD H24:MI:S
					requestParams.put("pageIndex", "1");
					requestParams.put("pageSize", Integer.MAX_VALUE);
				}
				
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				
				log.info(bodyStr);
				
				JSONObject reuslt = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reuslt.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
				}else {
					jsonArray = reuslt.getJSONArray("list");
				}
				
				List<String> inPatientIdLList = new ArrayList<>();//新技术开展患者的住院病人ID集合
				if(null != jsonArray && jsonArray.size() > 0) {
					for (int i = 0; i < jsonArray.size(); i++) {
		       			JSONObject obj = jsonArray.getJSONObject(i);
		       			NewTechPatientInfo patientInfo =  new NewTechPatientInfo();
		       			String operationCode = (String) obj.get("operationName");//手术编码
		       			//判断手术编码是否为新技术的手术编码，是新技术的手术编码才同步
		       			if(!ObjectUtils.isEmpty(operationCode) && techMap.containsKey(operationCode)){
		       				String inPatientId = obj.getString("inPatientId");//住院病人id
		       				inPatientIdLList.add(inPatientId);
		       				patientInfo.setTechCode(techMap.get(operationCode));
		       				patientInfo.setInPatientNo(obj.getString("inPatientNo"));//住院号
		       				patientInfo.setPatientName(obj.getString("name"));//病人姓名
		       				patientInfo.setOperationDate(obj.getString("operationTime"));//手术时间
		       				patientInfo.setIsDeleted("N");
		       			}
		       			patientInfoList.add(patientInfo);
					}
				}
				
				long endTime = System.currentTimeMillis();  
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-查询手术记录");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(reuslt.getString("Code") + ":" + reuslt.getString("Message") + ":" + reuslt.getString("exception") +  ":" + jsonArray.size());
				
				if("200".equals(reuslt.getString("Code"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
				
				if(inPatientIdLList.size() > 0){
					//查询住院患者 -返回计算入院天数  出院时间-入院时间
					getInPatient(inPatientIdLList, patientInfoList);
					//查询住院病人账目信息 -返回总费用
					getPatientAccountInfo(inPatientIdLList, patientInfoList);
				}
			}
		}
		return patientInfoList;
	}
	
	/**
	 * 查询住院患者 -返回计算入院天数  出院时间-入院时间
	 * @param inPatientIdLList
	 * @param patientInfoList
	 */
	private void getInPatient(List<String> inPatientIdLList, List<NewTechPatientInfo> patientInfoList){
		Map<String, NewTechPatientInfo> patientInfoMap = null;
        if (patientInfoList != null && patientInfoList.size() > 0) {
        	patientInfoMap = patientInfoList.stream().collect(Collectors.toMap(NewTechPatientInfo::getInPatientNo, a -> a, (k1, k2) -> k1));
        }
        
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询住院患者");//住院患者基本信息

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);//查询配置信息
		if(CollectionUtils.isNotEmpty(registerList)) {
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			if("1".equals(commInterfaceRegister.getStatus())) {
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				Map<String,Object> requestParams = new HashMap<>();
				
				//hisRequestVersion  1通山版本  2文山版本  其他的是标准版本
				if(StringUtils.isNotBlank(hisRequestVersion) && ("2".equals(hisRequestVersion) || "1".equals(hisRequestVersion))) {
					requestParams.put("IsCompress", "false");
					requestParams.put("ServiceName", "OuterPatPayService");//服务名
					requestParams.put("InterfaceName", "QueryInPatient");//接口名
					requestParams.put("TimeOut", 15000);//超时时间
					
					Map<String,Object> paramsMap = new HashMap<>();
					paramsMap.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					paramsMap.put("ids", inPatientIdLList);//住院病人id集合
					paramsMap.put("pageIndex", 1);
					paramsMap.put("pageSize", Integer.MAX_VALUE);
			       	requestParams.put("Parameter",paramsMap);
			       	
				}else {
					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					requestParams.put("ids", inPatientIdLList);//住院病人id集合
					requestParams.put("pageIndex", 1);
					requestParams.put("pageSize", Integer.MAX_VALUE);
				}
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				JSONObject reusltObject = JSON.parseObject(bodyStr);
				
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					JSONObject data = reusltObject.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
				}else {
					jsonArray = reusltObject.getJSONArray("list");//获取list数据
				}
				if(null != jsonArray && jsonArray.size() > 0) {
					// 定义日期格式
					for (int i = 0; i < jsonArray.size(); i++) {
						JSONObject obj = jsonArray.getJSONObject(i);
						String inPatientNo = obj.getString("inPatientNo");//住院号
						if(patientInfoMap.containsKey(inPatientNo)){
							NewTechPatientInfo patientInfo = patientInfoMap.get(inPatientNo);
							patientInfo.setSex(obj.getString("sexName"));
							//计算入院天数
							String inDate = obj.getString("inDate");//入院日期格式：YYYY-MM-DD H24:MI:SS
							String outDate = obj.getString("outDate");//出院日期格式：YYYY-MM-DD H24:MI:SS
							if(!ObjectUtils.isEmpty(inDate) && !ObjectUtils.isEmpty(outDate)){
//								DateUtils.getDifferDays(startDate, endDate);
							}
							
							patientInfo.setMainDiagnosis(obj.getString("inDiagnosisName"));
//							map.put("inDiagnosisName", obj.getString("inDiagnosisName"));//入院诊断
//							map.put("outDiagnosisName", obj.getString("outDiagnosisName"));//出院诊断
						}
					}
				}
			}
		}
	}
	
	/**
	 * 查询住院病人账目信息 -返回总费用
	 * @param inPatientIdLList
	 * @param patientInfoList
	 */
	private void getPatientAccountInfo(List<String> inPatientIdLList, List<NewTechPatientInfo> patientInfoList){
		
	}

}
