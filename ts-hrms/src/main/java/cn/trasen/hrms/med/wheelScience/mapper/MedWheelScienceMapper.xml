<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.med.wheelScience.dao.MedWheelScienceMapper">
  
  <select id="getDataSetList" resultType="cn.trasen.hrms.med.wheelScience.model.MedWheelScience" parameterType="cn.trasen.hrms.med.wheelScience.model.MedWheelScience">
  		select t1.*,t2.name as wheelOrgName,t3.name as docOrgName,t4.name as wheelFinishDeptName,
  		t5.position_name as docPostName from med_wheel_science t1
		LEFT JOIN comm_organization t2 ON t1.wheel_org_id = t2.organization_id
		LEFT JOIN comm_organization t3 ON t1.doc_org_id = t3.organization_id
		LEFT JOIN comm_organization t4 ON t1.wheel_finish_dept = t4.organization_id
		LEFT JOIN comm_position t5 on t1.doc_post = t5.position_id
		where t1.is_deleted = 'N'
  		<if test="docCode != null and docCode != ''">
  			and doc_code = #{docCode}
  		</if>
  		<if test="docName != null and docName != ''">
  			and (
				doc_code like concat('%',#{docName},'%')
				or doc_name like concat('%',#{docName},'%')
				or doc_phone like concat('%',#{docName},'%')
			) 
  		</if>
  		<if test="docArea != null and docArea != ''">
  			and doc_area = #{docArea}
  		</if>
  		<if test="docTitle != null and docTitle != ''">
  			and doc_title = #{docTitle}
  		</if>
  		<if test="wheelResult != null and wheelResult != ''">
  			and wheel_result = #{wheelResult}
  		</if>
  		<if test="wheelFinish != null and wheelFinish != ''">
  			and wheel_finish = #{wheelFinish}
  		</if>
  		<if test="status != null and status != ''">
  			and status = #{status}
  		</if>
  		<if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
			and wheel_start_date &lt;= #{endDate} and wheel_end_date >= #{startDate}
  		</if>
  		<if test="orgIdList != null and orgIdList.size() > 0">
        	 and wheel_org_id in
	        <foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
        <if test="orgIds != null and orgIds.size() > 0">
        	 and wheel_org_id in
	        <foreach collection="orgIds" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        </if>
  </select>
  
  <update id="updateEmpOrgInfo" parameterType="String">
  		update cust_emp_base set org_id = #{wheelFinishDept}
  		where employee_no = #{docCode}
  </update>
  
  <select id="selectWheelData" resultType="String">
  		select t1.id  from med_wheel_science t1
		LEFT JOIN cust_emp_base t2 on t1.doc_code = t2.employee_no
		LEFT JOIN cust_emp_info t3 on t2.employee_id = t3.info_id
		where t1.is_deleted = 'N' and t2.is_deleted = 'N' and t1.status in ('1','2')
		and (t3.technical like '%医师%' or t3.technical like '%医生%') 
		and t1.wheel_start_date &lt;= CONCAT(CURDATE(), ' 23:59:59') 
		and t1.wheel_end_date >= CONCAT(CURDATE(), ' 00:00:00')
  </select>
</mapper>