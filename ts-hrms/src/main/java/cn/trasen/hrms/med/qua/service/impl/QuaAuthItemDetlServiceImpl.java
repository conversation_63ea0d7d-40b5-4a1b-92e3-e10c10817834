package cn.trasen.hrms.med.qua.service.impl;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.hrms.Employee;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.interfaceRegister.dao.CommInterfaceRegisterMapper;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceLogs;
import cn.trasen.hrms.interfaceRegister.model.CommInterfaceRegister;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceLogsService;
import cn.trasen.hrms.interfaceRegister.service.CommInterfaceRegisterService;
import cn.trasen.hrms.med.comm.model.CategoryDict;
import cn.trasen.hrms.med.comm.service.CategoryDictService;
import cn.trasen.hrms.med.cslt.model.CsltScdu;
import cn.trasen.hrms.med.qua.dao.MedDoctorRoleMapper;
import cn.trasen.hrms.med.qua.dao.QuaAuthAuditDetlMapper;
import cn.trasen.hrms.med.qua.dao.QuaAuthItemDetlMapper;
import cn.trasen.hrms.med.qua.model.ItemAndAudit;
import cn.trasen.hrms.med.qua.model.ItemAndCfgDto;
import cn.trasen.hrms.med.qua.model.QuaAuthAuditDetl;
import cn.trasen.hrms.med.qua.model.QuaAuthCfg;
import cn.trasen.hrms.med.qua.model.QuaAuthItemDetl;
import cn.trasen.hrms.med.qua.service.QuaAuthAuditDetlService;
import cn.trasen.hrms.med.qua.service.QuaAuthCfgService;
import cn.trasen.hrms.med.qua.service.QuaAuthItemDetlService;
import cn.trasen.hrms.utils.CommonUtils;
import cn.trasen.hrms.utils.HttpClient;
import lombok.extern.log4j.Log4j2;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName MedQuaAuthItemDetlServiceImpl
 * @Description TODO
 * @date 2024��12��13�� ����9:25:26
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Log4j2
public class QuaAuthItemDetlServiceImpl implements QuaAuthItemDetlService {

	@Autowired
	private QuaAuthItemDetlMapper mapper;
	@Autowired
	private DictItemFeignService dictItemFeignService;
	@Autowired
	private CategoryDictService categoryDictService;
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationService;
	@Autowired
	private QuaAuthAuditDetlService auditDetlService;
	@Autowired
	private QuaAuthAuditDetlMapper auditDetlMapper;
	@Autowired
	private QuaAuthCfgService quaAuthCfgService;
	@Autowired
	private GlobalSettingsFeignService globalSettingsFeignService;
	@Autowired
	private CommInterfaceRegisterMapper commInterfaceRegisterMapper;
	@Autowired
	private CommInterfaceLogsService commInterfaceLogsService;
	@Autowired
	private MedDoctorRoleMapper medDoctorRoleMapper;
	@Value("${hisRequestVersion:}")
	private String hisRequestVersion;
	
	@Autowired
	private CommInterfaceRegisterService commInterfaceRegisterService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(QuaAuthItemDetl record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setCreateDept(user.getDeptcode());
			record.setCreateDeptName(user.getDeptname());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(user.getDeptcode());
			record.setSsoOrgName(user.getDeptname());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(QuaAuthItemDetl record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		QuaAuthItemDetl record = new QuaAuthItemDetl();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public QuaAuthItemDetl selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<QuaAuthItemDetl> getDataSetList(Page page, QuaAuthItemDetl record) {
		Example example = createExample(record);
		List<QuaAuthItemDetl> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), fillDict(records));
	}

	//TODO 待删除
	@Override
	public List<ItemAndAudit> selectItemAndAudit(Map<String,Object> params) {
		if(null == params.get("mgtId")) return null;
		if(!ReUtil.isMatch("0|1|2|3", Convert.toStr(params.get("auditStatus")))) return null;
		List<ItemAndAudit> totals = mapper.selectItemAndAudit(params);
		if(CollUtil.isNotEmpty(totals)){
			//如果没有审核信息的置为0
			totals.forEach(i -> {
				if(StrUtil.isEmpty(i.getAuditSeqNo())){
					i.setAuditSeqNo("0");
				}
			});
		}
		List<ItemAndAudit> result = totals.stream().filter(i -> StrUtil.equals(Convert.toStr(params.get("auditStatus")), i.getAuditSeqNo())).collect(Collectors.toList()); //当前审核节点的数据
		if(CollUtil.isEmpty(result)){ //如果当前节点没有审核数据，则查询上一节点的审核数据
			result = totals.stream().filter(i -> StrUtil.equals(Convert.toStr(Convert.toInt(params.get("auditStatus")) - 1), i.getAuditSeqNo())).collect(Collectors.toList()); 
		}
		if(CollUtil.isNotEmpty(result)){
			List<DictItemResp> lvHosps = dictItemFeignService.getDictItemByTypeCode("auth_lv_hosp").getObject();
			List<DictItemResp> lvNats = dictItemFeignService.getDictItemByTypeCode("auth_lv_nat").getObject();
			result.forEach(i -> {
				DictItemResp lv = lvHosps.stream().filter(j -> StrUtil.equals(i.getAuthLvHosp(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvHospName(null == lv ?i.getAuthLvHosp() : lv.getItemName());
				DictItemResp nat = lvNats.stream().filter(j -> StrUtil.equals(i.getAuthLvNat(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvNatName(null == nat ?i.getAuthLvHosp() : nat.getItemName());
			});
		}
		return result;
	}
	@Override
	public Example createExample(QuaAuthItemDetl record){
		Example example = new Example(CsltScdu.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StrUtil.isNotEmpty(record.getAuthStatus())){
			criteria.andEqualTo("authStatus", record.getAuthStatus());
		}
		if(StrUtil.isNotEmpty(record.getAuditStatus())){
			criteria.andEqualTo("auditStatus", record.getAuditStatus());
		}
		if(StrUtil.isNotEmpty(record.getQuaAuthType())){
			criteria.andEqualTo("quaAuthType", record.getQuaAuthType());
		}
		if(StrUtil.isNotEmpty(record.getHospArea())){
			criteria.andEqualTo("hospArea", record.getHospArea());
		}
		if(StrUtil.isNotEmpty(record.getJobtitle())){
			criteria.andLike("jobtitle", "%" + record.getJobtitle() + "%");
		}
		if(StrUtil.isNotEmpty(record.getEmployeeNo())){
			criteria.andCondition(StrUtil.format("(employee_no like '%{}%' or employee_name like '%{}%' or item_code like '%{}%' or item_name like '%{}%')",
					record.getEmployeeNo(),record.getEmployeeNo(),record.getEmployeeNo(),record.getEmployeeNo()));
		}
		if(StrUtil.isNotEmpty(record.getOrgId())){
			criteria.andIn("orgId", ListUtil.of(record.getOrgId().split(",")));
		}else if(!UserInfoHolder.ISADMIN() && !UserInfoHolder.ISALL() && !UserInfoHolder.getRight("yzygyly")){
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if(null != user){
				List<String> orgIds = hrmsOrganizationService.getHrmsOrganizationAndNextList(user.getDeptId()).getObject();
				String orgRang = UserInfoHolder.getOrgRang();
				if(StrUtil.isNotEmpty(orgRang)){
					orgRang = StrUtil.replace(StrUtil.removeSuffix(StrUtil.removePrefix(orgRang, "("), ")"), "'", "");
					orgIds.addAll(ListUtil.of(orgRang.split(",")));
				}
				criteria.andIn("orgId", orgIds.stream().distinct().collect(Collectors.toList()));
			}
		}
		return example;
	}

	/**
	 * 
	  -- =============================================
	  -- 功能描述: 将传入的申请信息，填充字典值
	  -- 作者: GW
	  -- 创建时间: 2024年12月28日
	  -- @param records
	  -- @return
	  -- =============================================
	 */
	@Override
	public List<QuaAuthItemDetl> fillDict(List<QuaAuthItemDetl> records){
		if(CollUtil.isNotEmpty(records)){
			List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
			List<CategoryDict> types = categoryDictService.getNormalsByCode("A01");
			List<DictItemResp> lvHosps = dictItemFeignService.getDictItemByTypeCode("auth_lv_hosp").getObject();
			List<DictItemResp> lvNats = dictItemFeignService.getDictItemByTypeCode("auth_lv_nat").getObject();
			records.forEach(i -> {
				CategoryDict type = types.stream().filter(j -> StrUtil.equals(i.getQuaAuthType(),j.getCode())).findFirst().orElse(null);
				i.setQuaAuthTypeName(null == type ? i.getQuaAuthType() : type.getName());
				DictItemResp area = areas.stream().filter(j -> StrUtil.equals(i.getHospArea(), j.getItemCode())).findFirst().orElse(null);
				i.setHospAreaName(null == area ? i.getHospArea() : area.getItemName());
				DictItemResp lvHosp = lvHosps.stream().filter(j -> StrUtil.equals(i.getAuthLvHosp(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvHospName(null == lvHosps ? i.getAuthLvHosp() : lvHosp.getItemName());
				DictItemResp LvNat = lvNats.stream().filter(j -> StrUtil.equals(i.getAuthLvNat(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvNatName(null == LvNat ? i.getAuthLvNat() : LvNat.getItemName());
			});
		}
		return records;
	}

	/**
	 * 
	  -- =============================================
	  -- 功能描述: 审批界面列表
	  -- 作者: GW
	  -- 创建时间: 2025年1月9日
	  -- @param params
	  -- @return
	  -- =============================================
	 */
	@Override
	public DataSet<ItemAndCfgDto> selectItemAndCfg(Page page,Map<String, Object> params) {

		List<String> status = new ArrayList<String>();
		
		String auditStatus = "0";  //0科主任 1大学科主任审批  2医务部审批  3医务部部长审批  4办结
		if("2".equals(params.get("index"))) { //已审批查询
			if("15335".equals(UserInfoHolder.getCurrentUserCode()) || "14637".equals(UserInfoHolder.getCurrentUserCode())
					|| "14704".equals(UserInfoHolder.getCurrentUserCode())) {
				status.addAll(ListUtil.of("3","4"));
			//	auditStatus = "2";
				
				if("15335".equals(UserInfoHolder.getCurrentUserCode())) { //天心阁1 
					params.put("hospArea", "1");
				}
				
				if("14704".equals(UserInfoHolder.getCurrentUserCode())) { // 马王堆 2  
					params.put("hospArea", "2");
				}
				
				if("14637".equals(UserInfoHolder.getCurrentUserCode())) { // 岳麓区3 
					params.put("hospArea", "3");
				}
				
			}else if("12516".equals(UserInfoHolder.getCurrentUserCode())) {
				status.addAll(ListUtil.of("4"));
			//	auditStatus = "3";
			}else {
				
				List<String> bigOrgIdList = mapper.selectBigManageDept(UserInfoHolder.getCurrentUserCode());
				List<String> orgIdList = mapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
				
				if(CollectionUtils.isEmpty(bigOrgIdList) && CollectionUtils.isEmpty(orgIdList)) {
					return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), null);
				}
				
				if(CollectionUtils.isNotEmpty(orgIdList) && CollectionUtils.isEmpty(bigOrgIdList)) {
					status.addAll(ListUtil.of("1","2","3","4"));
				}
				
				if(CollectionUtils.isNotEmpty(bigOrgIdList)) {
					status.addAll(ListUtil.of("2","3","4"));
				}
				
				bigOrgIdList.addAll(orgIdList);
				
				params.put("orgIdList", bigOrgIdList);
			
			}
		}else { //待审批查询
			
			List<String> orgIdList = mapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
			
			List<String> bigOrgIdList = mapper.selectBigManageDept(UserInfoHolder.getCurrentUserCode());
			
			if(CollectionUtils.isEmpty(orgIdList) && CollectionUtils.isEmpty(bigOrgIdList) && 
					!"15335".equals(UserInfoHolder.getCurrentUserCode()) && !"14637".equals(UserInfoHolder.getCurrentUserCode())
					&& !"14704".equals(UserInfoHolder.getCurrentUserCode()) && !"12516".equals(UserInfoHolder.getCurrentUserCode())) {
				return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), ListUtil.empty());
			}
			
			if(CollectionUtils.isNotEmpty(orgIdList) && CollectionUtils.isEmpty(bigOrgIdList)) {
				status.add("0");
				params.put("orgIdList", orgIdList);
			}else if(CollectionUtils.isEmpty(orgIdList) && CollectionUtils.isNotEmpty(bigOrgIdList)) {
				status.add("1");
				params.put("orgIdList", bigOrgIdList);
			}else if(CollectionUtils.isNotEmpty(orgIdList) && CollectionUtils.isNotEmpty(bigOrgIdList)) {
				//本科室的审批状态等于0  其他科室的等于1 
				params.put("statusFlag", "Y");
				params.put("orgIdList", orgIdList);
				params.put("bigOrgIdList", bigOrgIdList);
			}else {
				
				if("15335".equals(UserInfoHolder.getCurrentUserCode()) || "14637".equals(UserInfoHolder.getCurrentUserCode())
						|| "14704".equals(UserInfoHolder.getCurrentUserCode())) {
					auditStatus = "2";
					
					if("15335".equals(UserInfoHolder.getCurrentUserCode())) { //天心阁1 
						params.put("hospArea", "1");
					}
					
					if("14704".equals(UserInfoHolder.getCurrentUserCode())) { // 马王堆 2  
						params.put("hospArea", "2");
					}
					
					if("14637".equals(UserInfoHolder.getCurrentUserCode())) { // 岳麓区3 
						params.put("hospArea", "3");
					}
					
				}else if("12516".equals(UserInfoHolder.getCurrentUserCode())) {
					auditStatus = "3";
				}
				status.add(auditStatus);
			}
		}
		
		if(null != params.get("orgId")) {
			String orgId = (String) params.get("orgId");
			List<String> orgIds = Arrays.asList(orgId.split(","));
			params.put("orgIds", orgIds);
		}
		
		params.put("auditStatus", status);
			
		List<ItemAndCfgDto> list = mapper.selectItemAndCfg(page,params);
		
		if(CollUtil.isNotEmpty(list)){
			
			List<DictItemResp> areas = dictItemFeignService.getDictItemByTypeCode("hosp_area").getObject();
			List<CategoryDict> types = categoryDictService.getNormalsByCode("A01");
			List<DictItemResp> lvHosps = dictItemFeignService.getDictItemByTypeCode("auth_lv_hosp").getObject();
			List<DictItemResp> lvNats = dictItemFeignService.getDictItemByTypeCode("auth_lv_nat").getObject();
			
			//查所有节点的审核明细
			Example example = new Example(QuaAuthAuditDetl.class);
			Example.Criteria criteria = example.createCriteria();
			//criteria.andEqualTo("auditSeqNo", auditStatus);
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andIn("itemDetlId", list.stream().map(i -> i.getId()).collect(Collectors.toList()));
			
			List<QuaAuthAuditDetl> audits = auditDetlMapper.selectByExample(example);
			
			for (ItemAndCfgDto i : list) {
				
				CategoryDict type = types.stream().filter(j -> StrUtil.equals(i.getQuaAuthType(),j.getCode())).findFirst().orElse(null);
				i.setQuaAuthTypeName(null == type ? i.getQuaAuthType() : type.getName());
				DictItemResp area = areas.stream().filter(j -> StrUtil.equals(i.getHospArea(), j.getItemCode())).findFirst().orElse(null);
				i.setHospAreaName(null == area ? i.getHospArea() : area.getItemName());
				DictItemResp lvHosp = lvHosps.stream().filter(j -> StrUtil.equals(i.getAuthLvHosp(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvHospName(null == lvHosp ? i.getAuthLvHosp() : lvHosp.getItemName());
				DictItemResp LvNat = lvNats.stream().filter(j -> StrUtil.equals(i.getAuthLvNat(), j.getItemCode())).findFirst().orElse(null);
				i.setAuthLvNatName(null == LvNat ? i.getAuthLvNat() : LvNat.getItemName());
				
				if("1".equals(i.getAuditStatus())){
					i.setAuditStatusName("审批中(大学科主任)");
				}else if("2".equals(i.getAuditStatus())) {
					if("1".equals(i.getHospArea())) { //天心阁1 
						i.setAuditStatusName("审批中(李倩)");
					}
					
					if("2".equals(i.getHospArea())) { // 马王堆 2  
						i.setAuditStatusName("审批中(谭双香)");
					}
					
					if("3".equals(i.getHospArea())) { // 岳麓区3 
						i.setAuditStatusName("审批中(卿凯)");
					}
					
				}else if("3".equals(i.getAuditStatus())) {
					i.setAuditStatusName("审批中(翁晓军)");
				}else if("4".equals(i.getAuditStatus())) {
					i.setAuditStatusName("审批办结");
				}else {
					i.setAuditStatusName("待审批");
				}
				
//				i.setAuditStatusName("1".equals(i.getAuditStatus()) ? "审批中(李倩)" : 
//					"2".equals(i.getAuditStatus()) ? "审批中(翁晓军)" : 
//						"3".equals(i.getAuditStatus()) ? "审批办结" : "待审批");
				i.setIdentityNumber(StrUtil.isEmpty(i.getIdentityNumber()) ? "" 
						: StrUtil.sub(i.getIdentityNumber(),0, 6) + "********" + StrUtil.sub(i.getIdentityNumber(),i.getIdentityNumber().length() - 4,i.getIdentityNumber().length()));
				i.setAuthStatusName("1".equals(i.getAuthStatus()) ? "继续授权" : "停止授权");
				i.setIsMiniOprnName("1".equals(i.getIsMiniOprn()) ? "是" : "否");
				i.setIsNewTechName("1".equals(i.getIsNewTech()) ? "是" : "否");
				i.setIsRstdTechName("1".equals(i.getIsRstdTech()) ? "是" : "否");
				i.setIsMustAttName("1".equals(i.getIsMustAtt()) ? "是" : "否");
				
				//填充审核信息
				QuaAuthAuditDetl audit = audits.stream().filter(j -> StrUtil.equals(i.getId(), j.getItemDetlId())).findFirst().orElse(null);
			
				//如果当前节点没有审核数据，则查询上一节点的审核数据
//				if(null == audit){
//					audit = audits.stream()
//							.filter(j -> StrUtil.equals(i.getId(), j.getItemDetlId()) && StrUtil.equals(i.getAuditStatus(), Convert.toStr(j.getAuditSeqNo() - 1))).findFirst().orElse(null);
//				}
				
				if(null != audit){
					
					if("2".equals(params.get("index"))) {
						if(StringUtils.isNotBlank(audit.getAuditFlag3())) {
							i.setAuditDscr(audit.getAuditDscr3());
							i.setAuditFlag(audit.getAuditFlag3());
							i.setAuditFiles(audit.getAuditFiles3());
						}else if(StringUtils.isNotBlank(audit.getAuditFlag2())) {
							i.setAuditDscr(audit.getAuditDscr2());
							i.setAuditFlag(audit.getAuditFlag2());
							i.setAuditFiles(audit.getAuditFiles2());
						}else {
							i.setAuditDscr(audit.getAuditDscr());
							i.setAuditFlag(audit.getAuditFlag());
							i.setAuditFiles(audit.getAuditFiles());
						}
					}else {
						if("0".equals(auditStatus)) {
							i.setAuditDscr(audit.getAuditDscr());
							i.setAuditFlag(audit.getAuditFlag());
							i.setAuditFiles(audit.getAuditFiles());
						}
						if("1".equals(auditStatus)) {
							if(StringUtils.isNotBlank(audit.getAuditFlag2())) {
								i.setAuditDscr(audit.getAuditDscr2());
								i.setAuditFlag(audit.getAuditFlag2());
								i.setAuditFiles(audit.getAuditFiles2());
							}else {
								i.setAuditDscr(audit.getAuditDscr());
								i.setAuditFlag(audit.getAuditFlag());
								i.setAuditFiles(audit.getAuditFiles());
							}
						}
						if("2".equals(auditStatus)) {
							if(StringUtils.isNotBlank(audit.getAuditFlag3())) {
								i.setAuditDscr(audit.getAuditDscr3());
								i.setAuditFlag(audit.getAuditFlag3());
								i.setAuditFiles(audit.getAuditFiles3());
							}else if(StringUtils.isNotBlank(audit.getAuditFlag2())) {
								i.setAuditDscr(audit.getAuditDscr2());
								i.setAuditFlag(audit.getAuditFlag2());
								i.setAuditFiles(audit.getAuditFiles2());
							}else {
								i.setAuditDscr(audit.getAuditDscr());
								i.setAuditFlag(audit.getAuditFlag());
								i.setAuditFiles(audit.getAuditFiles());
							}
						}
					}
					
				}
				
				//还未产生审核或暂存数据时，默认继续授权
				if(StrUtil.isEmpty(i.getAuditFlag())){
					i.setAuditFlag("1");
				}
			}
			
		}
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	
	/**
	 * 
	  -- =============================================
	  -- 功能描述: 审核
	  -- 作者: GW
	  -- 创建时间: 2025年1月9日
	  -- @param param
	  -- =============================================
	 */
	@Override
	@Transactional(readOnly = false)
	public synchronized void audit(Map<String, Object> param) {
		
		String auditStatus = "0";
		
//		List<String> orgIdList = mapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
//		
//		List<String> bigOrgIdList = mapper.selectBigManageDept(UserInfoHolder.getCurrentUserCode());
//		
//		if(CollectionUtils.isNotEmpty(orgIdList)) {
//			param.put("orgIdList", orgIdList);
//		}else {
//			if(CollectionUtils.isNotEmpty(bigOrgIdList)) {
//				auditStatus = "1";
//			}else if("15335".equals(UserInfoHolder.getCurrentUserCode()) || "14637".equals(UserInfoHolder.getCurrentUserCode())
//					|| "14704".equals(UserInfoHolder.getCurrentUserCode())) {
//				auditStatus = "2";
//				
//				if("15335".equals(UserInfoHolder.getCurrentUserCode())) { //天心阁1 
//					param.put("hospArea", "1");
//				}
//				
//				if("14637".equals(UserInfoHolder.getCurrentUserCode())) { // 马王堆 2  
//					param.put("hospArea", "2");
//				}
//				
//				if("14704".equals(UserInfoHolder.getCurrentUserCode())) { // 岳麓区3 
//					param.put("hospArea", "3");
//				}
//				
//			}else if("12516".equals(UserInfoHolder.getCurrentUserCode())) {
//				auditStatus = "3";
//			}
//		}
		List<String> status = new ArrayList<String>();
		
		List<String> orgIdList = mapper.selectManageDept(UserInfoHolder.getCurrentUserCode());
		
		List<String> bigOrgIdList = mapper.selectBigManageDept(UserInfoHolder.getCurrentUserCode());
		
		if(CollectionUtils.isNotEmpty(orgIdList) && CollectionUtils.isEmpty(bigOrgIdList)) {
			auditStatus = "0";
			status.add("0");
			param.put("orgIdList", orgIdList);
		}else if(CollectionUtils.isEmpty(orgIdList) && CollectionUtils.isNotEmpty(bigOrgIdList)) {
			auditStatus = "1";
			status.add("1");
			param.put("orgIdList", bigOrgIdList);
		}else if(CollectionUtils.isNotEmpty(orgIdList) && CollectionUtils.isNotEmpty(bigOrgIdList)) {
			//本科室的审批状态等于0  其他科室的等于1 
			auditStatus = "1";
			param.put("statusFlag", "Y");
			param.put("orgIdList", orgIdList);
			param.put("bigOrgIdList", bigOrgIdList);
		}else {
			
			if("15335".equals(UserInfoHolder.getCurrentUserCode()) || "14637".equals(UserInfoHolder.getCurrentUserCode())
					|| "14704".equals(UserInfoHolder.getCurrentUserCode())) {
				auditStatus = "2";
				
				if("15335".equals(UserInfoHolder.getCurrentUserCode())) { //天心阁1 
					param.put("hospArea", "1");
				}
				
				if("14704".equals(UserInfoHolder.getCurrentUserCode())) { // 马王堆 2  
					param.put("hospArea", "2");
				}
				
				if("14637".equals(UserInfoHolder.getCurrentUserCode())) { // 岳麓区3 
					param.put("hospArea", "3");
				}
				
			}else if("12516".equals(UserInfoHolder.getCurrentUserCode())) {
				auditStatus = "3";
			}
			
			status.add(auditStatus);
		}
		
		if(null != param.get("orgId")) {
			String orgId = (String) param.get("orgId");
			List<String> orgIds = Arrays.asList(orgId.split(","));
			param.put("orgIds", orgIds);
		}
		
		param.put("auditStatus", status);
		
		Page page = new Page();
		page.setPageNo(1);
		page.setPageSize(Integer.MAX_VALUE);
		
		List<ItemAndCfgDto> list = mapper.selectItemAndCfg(page,param);
		
		List<QuaAuthAuditDetl> saveList = new ArrayList<>();
		List<String> updateList = new ArrayList<>();
		List<String> quaAuthItemDetlList = new ArrayList<>();
		
		List<String> itemIds = new ArrayList<>();
		for (ItemAndCfgDto itemAndCfgDto : list) {
			itemIds.add(itemAndCfgDto.getId());
		}
		
		List<List<String>> averageAssign = CommonUtils.averageAssign(itemIds,2000);
		
		List<QuaAuthAuditDetl> audit = new ArrayList<>();
		
		averageAssign.parallelStream().forEach(ls ->{
			List<QuaAuthAuditDetl> auditList = auditDetlService.getByItemIdsAndSeqno(ls, null);
			audit.addAll(auditList);
        });
		
		
		for (ItemAndCfgDto itemAndCfgDto : list) {
			
			boolean add = true;
			
			for (QuaAuthAuditDetl quaAuthAuditDetl : audit) {
				if(quaAuthAuditDetl.getItemDetlId().equals(itemAndCfgDto.getId())) {
					add = false;
					break;
				}
			}
			
			QuaAuthAuditDetl quaAuthAuditDetl = new QuaAuthAuditDetl();
			quaAuthAuditDetl.setItemDetlId(itemAndCfgDto.getId());
			if(add) {
				quaAuthAuditDetl.setId(IdGeneraterUtils.nextId());
				quaAuthAuditDetl.setAuditFlag("1");
				quaAuthAuditDetl.setAuditSeqNo(1);
				quaAuthAuditDetl.setCreateDate(new Date());
				quaAuthAuditDetl.setCreateUser(UserInfoHolder.getCurrentUserCode());
				quaAuthAuditDetl.setCreateUserName(UserInfoHolder.getCurrentUserName());
				quaAuthAuditDetl.setIsDeleted("N");
			}else {
				quaAuthAuditDetl.setId(itemAndCfgDto.getId());
			}
			
			quaAuthItemDetlList.add(itemAndCfgDto.getId());
			
			if(add) {
				saveList.add(quaAuthAuditDetl);
			}else {
				updateList.add(itemAndCfgDto.getId());
			}
		}
		
		//发送数据量过大  拆分数据 
		List<List<QuaAuthAuditDetl>> saveAuditDetlList = CommonUtils.averageAssign(saveList,1000);
		
		//使用并行流插入数据
		saveAuditDetlList.parallelStream().forEach(ls ->{
			auditDetlMapper.batchInsert(ls);
        });
		
		QuaAuthAuditDetl quaAuthAuditDetlUpdate = new QuaAuthAuditDetl();
		quaAuthAuditDetlUpdate.setUpdateDate(new Date());
		quaAuthAuditDetlUpdate.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		quaAuthAuditDetlUpdate.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		if("0".equals(auditStatus)) {
			quaAuthAuditDetlUpdate.setAuditSeqNo(1);
		}
		
		if("1".equals(auditStatus)) {
			quaAuthAuditDetlUpdate.setAuditSeqNo(2);
		}
		
		if("2".equals(auditStatus)) {
			quaAuthAuditDetlUpdate.setAuditSeqNo(3);
		}
		
		if("3".equals(auditStatus)) {
			quaAuthAuditDetlUpdate.setAuditSeqNo(4);
		}
		
		if(CollectionUtils.isNotEmpty(updateList)) {
			Example example = new Example(QuaAuthAuditDetl.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andIn("itemDetlId", updateList);
			
			auditDetlMapper.updateByExampleSelective(quaAuthAuditDetlUpdate, example);
		}
		
		QuaAuthItemDetl item = new QuaAuthItemDetl();
		item.setItemIds(quaAuthItemDetlList);
		
		if("0".equals(auditStatus)) {
			item.setAuditStatus("1");
		}
		
		if("1".equals(auditStatus)) {
			item.setAuditStatus("2");
		}
		
		if("2".equals(auditStatus)) {
			item.setAuditStatus("3");
		}
		
		if("3".equals(auditStatus)) {
			item.setAuditStatus("4");
			item.setEndtime(new Date());
		}
		
		mapper.updateQuaAuthItemDetl(item);
	}

	/**
	 * 
	  -- =============================================
	  -- 功能描述: 医师资质授权-暂存
	  -- 作者: GW
	  -- 创建时间: 2025年1月9日
	  -- @param param
	  -- =============================================
	 */
	@Override
	@Transactional(readOnly = false)
	public void temporaryStorage(QuaAuthAuditDetl record) {
		
		String auditStatus = "0";
		
		List<String> bigOrgIdList = mapper.selectBigManageDept(UserInfoHolder.getCurrentUserCode());
		
		if(CollectionUtils.isNotEmpty(bigOrgIdList)) {
			auditStatus = "1";
		}else if("15335".equals(UserInfoHolder.getCurrentUserCode()) || "14637".equals(UserInfoHolder.getCurrentUserCode())
				|| "14704".equals(UserInfoHolder.getCurrentUserCode())) {
			auditStatus = "2";
		}else if("12516".equals(UserInfoHolder.getCurrentUserCode())) {
			auditStatus = "3";
		}
		
		if("0".equals(record.getAuditFlag())) {
			
			QuaAuthItemDetl quaAuthItemDetl = new QuaAuthItemDetl();
			quaAuthItemDetl.setId(record.getItemDetlId());
			quaAuthItemDetl.setAuthStatus("0");
			mapper.updateByPrimaryKeySelective(quaAuthItemDetl);
			
			QuaAuthAuditDetl auditDetl = auditDetlService.getByItemIdAndSeqno(record.getItemDetlId(),null);
			
			if(null != auditDetl) {
				
				if("0".equals(auditStatus)) {
					auditDetl.setAuditDscr(record.getAuditDscr());
					auditDetl.setAuditFiles(record.getAuditFiles());
					auditDetl.setAuditFlag(record.getAuditFlag());
					auditDetl.setAuditSeqNo(0);
				}
				if("1".equals(auditStatus)) {
					auditDetl.setAuditDscr2(record.getAuditDscr());
					auditDetl.setAuditFiles2(record.getAuditFiles());
					auditDetl.setAuditFlag2(record.getAuditFlag());
					auditDetl.setAuditSeqNo(1);
				}
				
				if("2".equals(auditStatus)) {
					auditDetl.setAuditDscr3(record.getAuditDscr());
					auditDetl.setAuditFiles3(record.getAuditFiles());
					auditDetl.setAuditFlag3(record.getAuditFlag());
					auditDetl.setAuditSeqNo(2);
				}
				
				auditDetlService.updateByPrimaryKey(auditDetl);
			}else {
				if("0".equals(auditStatus)) {
					record.setAuditDscr(record.getAuditDscr());
					record.setAuditFiles(record.getAuditFiles());
					record.setAuditFlag(record.getAuditFlag());
					record.setAuditSeqNo(0);
				}
				if("1".equals(auditStatus)) {
					
					record.setAuditDscr2(record.getAuditDscr());
					record.setAuditFiles2(record.getAuditFiles());
					record.setAuditFlag2(record.getAuditFlag());
					record.setAuditSeqNo(1);
					
					record.setAuditDscr(null);
					record.setAuditFiles(null);
					//record.setAuditFlag(null);
				}
				
				if("2".equals(auditStatus)) {
					
					record.setAuditDscr3(record.getAuditDscr());
					record.setAuditFiles3(record.getAuditFiles());
					record.setAuditFlag3(record.getAuditFlag());
					record.setAuditSeqNo(2);
					
					record.setAuditDscr(null);
					record.setAuditFiles(null);
					//record.setAuditFlag(null);
				}
				
				auditDetlService.save(record);
			}
			
		}else {
			
			QuaAuthItemDetl quaAuthItemDetl = new QuaAuthItemDetl();
			quaAuthItemDetl.setId(record.getItemDetlId());
			quaAuthItemDetl.setAuthStatus("1");
			mapper.updateByPrimaryKeySelective(quaAuthItemDetl);
			
			auditDetlService.deleteByItemId(record.getItemDetlId(),auditStatus);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void saveOrUpdate(String json,String L_BusinessId,String L_LaunchUserCode,String L_LaunchUserName,
			String L_LaunchDeptCode,String L_LaunchDeptName) {
		
		JSONArray jsonObject =	JSONObject.parseArray(json);
		
		List<Map<String,String>> pushList = new ArrayList<>();
		
		Map<String,String> orgdata = mapper.selectOrgData(L_LaunchDeptCode);
		    
	    for (int i = 0; i < jsonObject.size(); i++) {
   			JSONObject obj = jsonObject.getJSONObject(i);
   			String itemCode = obj.getString("itemCode");
   			
   			Example example = new Example(QuaAuthItemDetl.class);
   			Example.Criteria criteria = example.createCriteria();
   			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
   			criteria.andEqualTo("itemCode", itemCode);
   			criteria.andEqualTo("employeeNo", L_LaunchUserCode);
   			List<QuaAuthItemDetl> list = mapper.selectByExample(example);
   			
   			if(CollectionUtils.isNotEmpty(list)) {
   				QuaAuthItemDetl quaAuthItemDetl = list.get(0);
   				
   				quaAuthItemDetl.setAuthStatus(obj.getString("auditFlag"));
   				quaAuthItemDetl.setEndtime(new Date());
   				quaAuthItemDetl.setAuditDscr(obj.getString("auditScr"));
   				quaAuthItemDetl.setWorkflowId(L_BusinessId);
   				quaAuthItemDetl.setAuditStatus("3");
   				
   				mapper.updateByPrimaryKeySelective(quaAuthItemDetl);
   				
   				Map<String,String> pushObj = new HashMap<>();
   				pushObj.put("doctorId", L_LaunchUserCode);
   				pushObj.put("doctorName", L_LaunchUserName);
   				pushObj.put("SSCODE", quaAuthItemDetl.getItemCode());
   				pushObj.put("ssname", quaAuthItemDetl.getItemName());
   				
   				if(StringUtils.isNotBlank(orgdata.get("PLATFORM_ID"))){
   					pushObj.put("deptID", orgdata.get("PLATFORM_ID"));
   				}else {
   					pushObj.put("deptID", orgdata.get("organization_id"));
   				}
   				
   				pushObj.put("deptName", orgdata.get("name"));
   				
   				String auditFlag = obj.getString("auditFlag");
   				if("0".equals(auditFlag)) {
   					pushObj.put("flag", "1");
   				}else {
   					pushObj.put("flag", "0");
   				}
   				
   				pushList.add(pushObj);
   			}else {
   				QuaAuthItemDetl quaAuthItemDetl = new QuaAuthItemDetl();
   				quaAuthItemDetl.setAuthStatus(obj.getString("auditFlag"));
   				quaAuthItemDetl.setEndtime(new Date());
   				quaAuthItemDetl.setAuditDscr(obj.getString("auditScr"));
   				quaAuthItemDetl.setWorkflowId(L_BusinessId);
   				quaAuthItemDetl.setAuditStatus("3");
   				
   				QuaAuthCfg quaAuthCfg = quaAuthCfgService.selectById(obj.getString("id"));
   				
   				quaAuthItemDetl.setQuaAuthType(quaAuthCfg.getQuaAuthType());
   				quaAuthItemDetl.setAuthLvHosp(quaAuthCfg.getAuthLvHosp());
   				quaAuthItemDetl.setAuthLvNat(quaAuthCfg.getAuthLvNat());
   				quaAuthItemDetl.setItemCode(quaAuthCfg.getItemCode());
   				quaAuthItemDetl.setItemName(quaAuthCfg.getItemName());
   				quaAuthItemDetl.setAppyTime(new Date());
   				quaAuthItemDetl.setEmployeeId(L_LaunchUserCode);
   				quaAuthItemDetl.setEmployeeNo(L_LaunchUserCode);
   				quaAuthItemDetl.setEmployeeName(L_LaunchUserName);
   				
   				Map<String,String> empinfo = mapper.selectEmpInfo(L_LaunchUserCode);
   				quaAuthItemDetl.setIdentityNumber(empinfo.get("identity_number"));
   				quaAuthItemDetl.setTel(empinfo.get("phone_number"));
   				quaAuthItemDetl.setOrgId(empinfo.get("org_id"));
   				quaAuthItemDetl.setOrgName(empinfo.get("orgName"));
   				quaAuthItemDetl.setHospArea(empinfo.get("hosp_code"));
   				quaAuthItemDetl.setJobtitle(empinfo.get("technical"));
   				
   				save(quaAuthItemDetl);
   				
   				Map<String,String> pushObj = new HashMap<>();
   				pushObj.put("doctorId", L_LaunchUserCode);
   				pushObj.put("doctorName", L_LaunchUserName);
   				pushObj.put("SSCODE", quaAuthCfg.getItemCode());
   				pushObj.put("ssname", quaAuthCfg.getItemName());
   				
   				if(StringUtils.isNotBlank(orgdata.get("PLATFORM_ID"))){
   					pushObj.put("deptID", orgdata.get("PLATFORM_ID"));
   				}else {
   					pushObj.put("deptID", orgdata.get("organization_id"));
   				}
   				
   				pushObj.put("deptName", orgdata.get("name"));
   				
   				String auditFlag = obj.getString("auditFlag");
   				if("0".equals(auditFlag)) {
   					pushObj.put("flag", "1");
   				}else {
   					pushObj.put("flag", "0");
   				}
   				
   				pushList.add(pushObj);
   			}
		}
	    
	    PlatformResult<GlobalSetting> obj = globalSettingsFeignService.getGlobalSetting("Y");
	    GlobalSetting globalSetting = obj.getObject();
	    
	    //省人医推送给HIS
	    if("hnsrmyy".equals(globalSetting.getOrgCode())) {
	    	commInterfaceRegisterService.pushHnsrmyyOperation(L_LaunchUserCode, L_LaunchUserName, pushList);
	    }else {
	    	//推送平台
	    	pushPlatformOperPowers(L_LaunchUserCode,jsonObject);
	    }
	}

	private void pushPlatformOperPowers(String L_LaunchUserCode,JSONArray jsonObject) {
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "1");
		criteria.andEqualTo("interfaceName", "集成平台-保存人员手术权限");
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(registerList)) {
			
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			Map<String, String> empInfo = medDoctorRoleMapper.selectHisEmployeeNoByCode(L_LaunchUserCode);
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				if(StringUtils.isNotBlank(hisRequestVersion) && ("1".equals(hisRequestVersion) || "2".equals(hisRequestVersion))) {
					
					requestParams.put("ServiceName", "OuterBaseDataService");
					requestParams.put("InterfaceName", "SaveMemberOperPower");
					requestParams.put("IsCompress", "false");
					
					Map<String,Object> Parameter = new HashMap<>();
					Parameter.put("code",  empInfo.get("his_employee_no"));
					Parameter.put("updateBy",  "1");
					Parameter.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					
					Map<String,Object> operPowerInfo = new HashMap<>();
					
					operPowerInfo.put("operPowerUpdateType", "0");
					
					List<Map<String,Object>> menberOperPowers = new ArrayList<>();
					for (int i = 0; i < jsonObject.size(); i++) {
					   		JSONObject obj = jsonObject.getJSONObject(i);
					   		
					   		QuaAuthCfg quaAuthCfg = quaAuthCfgService.selectById(obj.getString("id"));
					   		
							Map<String,Object> power = new HashMap<>();
							power.put("operId", obj.getString("id")); // 需要his修改
							power.put("operName", quaAuthCfg.getItemName());
							power.put("icdCode", quaAuthCfg.getItemCode());
							if(StringUtils.isBlank(obj.getString("auditFlag"))) {
								power.put("powerValue", "1");
							}else {
								power.put("powerValue", obj.getString("auditFlag"));
							}
							
							menberOperPowers.add(power);
						}
					
					operPowerInfo.put("menberOperPowers", menberOperPowers);
					
					Parameter.put("operPowerInfo", operPowerInfo);
					
					requestParams.put("Parameter", Parameter);
					
				}else {
					requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
					requestParams.put("updateBy", "1");
					
					requestParams.put("code", empInfo.get("his_employee_no"));
					
					Map<String, Object> operPowerInfo = new HashMap<>();
					operPowerInfo.put("operPowerUpdateType", "0");
					
					List<Map<String,Object>> menberOperPowers = new ArrayList<>();
					
					 for (int i = 0; i < jsonObject.size(); i++) {
				   		JSONObject obj = jsonObject.getJSONObject(i);
				   		
				   		QuaAuthCfg quaAuthCfg = quaAuthCfgService.selectById(obj.getString("id"));
				   		
						Map<String,Object> power = new HashMap<>();
						power.put("operId", obj.getString("id")); // 需要his修改
						power.put("operName", quaAuthCfg.getItemName());
						power.put("icdCode", quaAuthCfg.getItemCode());
						if(StringUtils.isBlank(obj.getString("auditFlag"))) {
							power.put("powerValue", "1");
						}else {
							power.put("powerValue", obj.getString("auditFlag"));
						}
						
						menberOperPowers.add(power);
					}
					
					operPowerInfo.put("menberOperPowers", menberOperPowers);
					
					requestParams.put("operPowerInfo", operPowerInfo);
				}
				
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				JSONObject reuslt = JSON.parseObject(bodyStr);
				
				long endTime = System.currentTimeMillis();  
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-保存人员手术权限");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(bodyStr);
				
				if("200".equals(reuslt.getString("Code"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void removeEmployee(String employeeIds) {
		
		if(StringUtils.isNotBlank(employeeIds)) {
			String[] employeeIdArray = employeeIds.split(",");
			for (String employeeId : employeeIdArray) {
				mapper.removeEmployee(employeeId,"23405568","1");
			}
		}
		
	}

	@Override
	@Transactional(readOnly = false)
	public void removeinEmployee(String employeeIds, String orgId) {
		
		if(StringUtils.isNotBlank(employeeIds) && StringUtils.isNotBlank(orgId)) {
			
			String hospCode = mapper.selectHospCode(orgId);
			
			String[] employeeIdArray = employeeIds.split(",");
			for (String employeeId : employeeIdArray) {
				mapper.removeEmployee(employeeId,orgId,hospCode);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void syncPlatformOperPowers() {
		long startTime = System.currentTimeMillis();    
		
		Example example = new Example(CommInterfaceRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("callType", "2");
		criteria.andEqualTo("interfaceName", "集成平台-查询人员手术权限");
		List<CommInterfaceRegister> registerList = commInterfaceRegisterMapper.selectByExample(example);
		
		if(CollectionUtils.isNotEmpty(registerList)) {
			
			CommInterfaceRegister commInterfaceRegister = registerList.get(0);
			
			if("1".equals(commInterfaceRegister.getStatus())) {
				
				Map<String, String> sign = HttpClient.toSign(commInterfaceRegister);
				
				Map<String,Object> requestParams = new HashMap<>();
				requestParams.put("orgCode", commInterfaceRegister.getPlatformOrgCode());
				
				String jsonString = JSONObject.toJSONString(requestParams,SerializerFeature.WriteNullStringAsEmpty); // 数据
				
				String requestUrl = commInterfaceRegister.getInterfaceIp() + commInterfaceRegister.getInterfaceAddress() + "?appId=" + commInterfaceRegister.getPlatformAppId();
				String bodyStr = HttpClient.doPostJson(requestUrl, jsonString, sign);
				
				JSONObject reuslt = JSON.parseObject(bodyStr);
				JSONArray jsonArray = null;
				if(StringUtils.isNotBlank(hisRequestVersion) && "1".equals(hisRequestVersion)) {
					JSONObject data = reuslt.getJSONObject("data");
					JSONObject Value = data.getJSONObject("Value");
					jsonArray = Value.getJSONArray("list");
				}else {
					jsonArray = reuslt.getJSONArray("list");
				}
				
				if(null != jsonArray && jsonArray.size() > 0) {
					mapper.deleteQuaAuthItemDetl();//清空数据
					
					List<QuaAuthItemDetl> quaAuthItemDetlList = new ArrayList<>();
					for (int i = 0; i < jsonArray.size(); i++) {
		       			JSONObject obj = jsonArray.getJSONObject(i);
		       			QuaAuthItemDetl quaAuthItemDetl =  new QuaAuthItemDetl();
		       			quaAuthItemDetl.setId(obj.getString("id"));
		       			
		       			//his工号和id跟医务系统对不上 需要映射匹配
						Map<String,String> empMap = medDoctorRoleMapper.selectByHisEmployeeNo(obj.getString("memberCode"));//code 需要his添加
						if(null != empMap && StringUtils.isNotBlank(empMap.get("employee_id"))) {
							quaAuthItemDetl.setEmployeeId(empMap.get("employee_id"));
							quaAuthItemDetl.setEmployeeNo(empMap.get("employee_no"));
							
			       			//quaAuthItemDetl.setEmployeeId(obj.getString("code"));  
			       			//quaAuthItemDetl.setEmployeeNo(obj.getString("code"));
			       			quaAuthItemDetl.setMgtId(obj.getString("operId"));
			       			
			       			quaAuthItemDetl.setItemCode(obj.getString("icdCode"));
			       			quaAuthItemDetl.setItemName(obj.getString("operName"));
			       			quaAuthItemDetl.setAuthStatus(obj.getString("powerValue"));
			       			
			       			QuaAuthCfg quaAuthCfg = quaAuthCfgService.selectById(obj.getString("operId"));
			   				
			   				quaAuthItemDetl.setQuaAuthType(quaAuthCfg.getQuaAuthType());
			   				quaAuthItemDetl.setAuthLvHosp(quaAuthCfg.getAuthLvHosp());
			   				quaAuthItemDetl.setAuthLvNat(quaAuthCfg.getAuthLvNat());
			       			
			       			quaAuthItemDetl.setCreateDate(new Date());
			       			quaAuthItemDetl.setCreateUser("admin");
			       			quaAuthItemDetl.setCreateUserName("admin");
			       			quaAuthItemDetl.setIsDeleted("N");
			       			
			       			quaAuthItemDetlList.add(quaAuthItemDetl);
						}
					}
					
					if(CollectionUtils.isNotEmpty(quaAuthItemDetlList)){
						//发送数据量过大  拆分数据 
						List<List<QuaAuthItemDetl>> saveList = CommonUtils.averageAssign(quaAuthItemDetlList,1000);
						
						//使用并行流插入数据
						saveList.stream().forEach(ls ->{
							mapper.batchInsert(ls);	
				        });
					}
					
				}
				
				long endTime = System.currentTimeMillis();  
				
				CommInterfaceLogs commInterfaceLogs = new CommInterfaceLogs();
				commInterfaceLogs.setRegisterId(commInterfaceRegister.getId());
				commInterfaceLogs.setInterfaceName("集成平台-查询人员手术权限");
				commInterfaceLogs.setInterworkPlatform("集成平台");
				commInterfaceLogs.setRequestUrl(requestUrl);
				commInterfaceLogs.setRequestParams(jsonString);
				commInterfaceLogs.setResponseParams(reuslt.getString("Code") + ":" + reuslt.getString("Message") + ":" + reuslt.getString("exception") + ":" + jsonArray.size());
				
				if("200".equals(reuslt.getString("Code"))) {
					commInterfaceLogs.setResponseStatus("1");
				}else {
					commInterfaceLogs.setResponseStatus("2");
				}
				
				commInterfaceLogs.setTakeTime((endTime - startTime) + "ms");
				commInterfaceLogsService.save(commInterfaceLogs);
			}
		}
	}

	@Override
	public List<QuaAuthItemDetl> selectByWorkflowId(String workflowId, String authByNewTech) {
		Example example = new Example(QuaAuthItemDetl.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("workflowId", workflowId);
		if(!ObjectUtils.isEmpty(authByNewTech)){
			criteria.andEqualTo("authByNewTech", authByNewTech);
		}
		List<QuaAuthItemDetl> records = mapper.selectByExample(example);
		return records;
	}
	
	
}
