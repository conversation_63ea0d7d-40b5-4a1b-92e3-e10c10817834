package cn.trasen.hrms.dutyRoster.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.trasen.hrms.dutyRoster.enums.HrmsDutyRosterOpTypeEnum;
import cn.trasen.hrms.dutyRoster.service.HrmsDutyRosterOperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dutyRoster.dao.HrmsDutyRosterMapper;
import cn.trasen.hrms.dutyRoster.model.HrmsDutyRoster;
import cn.trasen.hrms.dutyRoster.model.HrmsDutyRosterUser;
import cn.trasen.hrms.dutyRoster.service.HrmsDutyRosterService;
import cn.trasen.hrms.dutyRoster.service.HrmsDutyRosterUserService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsDutyRosterServiceImpl
 * @Description TODO
 * @date 2023��9��8�� ����10:11:54
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsDutyRosterServiceImpl implements HrmsDutyRosterService {

	@Resource
	private HrmsDutyRosterMapper mapper;
	
	@Autowired
	HrmsDutyRosterUserService hrmsDutyRosterUserService;

	@Autowired
	private HrmsDutyRosterOperateLogService dutyRosterOperateLogService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsDutyRoster record) {
		
		//不能重名
		Example example = new Example(HrmsDutyRoster.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("rosterName", record.getRosterName());
		List<HrmsDutyRoster> list = mapper.selectByExample(example);
		if(list != null && list.size() > 0) {
			 throw new BusinessException("值班群组名称不能重复");
		}
		
		String groupUserString = record.getGroupUserString();
		if(StringUtil.isEmpty(groupUserString)) {
			 throw new BusinessException("群组人员不能为空");
		}
		String dutyRosterId = String.valueOf(IdWork.id.nextId());
		record.setId(dutyRosterId);
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		}
		
		//插入子表
		String[] split = record.getGroupUserString().split(",");
		String[] split2 = record.getGroupUserNames().split(",");
		for(int i =0;i<split.length;i++) {
			HrmsDutyRosterUser _bean = new HrmsDutyRosterUser();
			_bean.setEmployeeName(split2[i]);
			_bean.setEmployeeNo(split[i]);
			_bean.setRosterGroupId(dutyRosterId);
			_bean.setSeqNo(999);
			hrmsDutyRosterUserService.save(_bean);
		}
		
		if(record.getGroupUserNames().charAt(record.getGroupUserNames().length()-1) != ',') { 
			record.setGroupUserNames(record.getGroupUserNames()+ ",");
		}
		if(record.getGroupUserString().charAt(record.getGroupUserString().length()-1) != ',') { 
			record.setGroupUserString(record.getGroupUserString()+ ",");
		}
		Integer count = mapper.insertSelective(record);
		//保存操作日志
		dutyRosterOperateLogService.saveOperateLog(record,null, "值班分组",record.getId(),HrmsDutyRosterOpTypeEnum.ADD_GROUP,"新增分组-"+record.getRosterName());
		return count;
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsDutyRoster record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}

		List<HrmsDutyRosterUser> list = hrmsDutyRosterUserService.getALlByGroupId(record.getId());

		Map<String,Integer> zhongcnegMap = list.stream().filter(item -> "1".equals(item.getZhongceng())).collect(Collectors.toMap(HrmsDutyRosterUser::getEmployeeNo, HrmsDutyRosterUser::getSeqNo));

		//删掉比原来少的人
		hrmsDutyRosterUserService.deleteRosterGroupId(record.getId());

		//新增的人员插入子表
		//插入子表
		String[] split = record.getGroupUserString().split(",");
		String[] split2 = record.getGroupUserNames().split(",");
		for(int i =0;i<split.length;i++) {
			
			HrmsDutyRosterUser _bean = new HrmsDutyRosterUser();
			_bean.setEmployeeName(split2[i]);
			_bean.setEmployeeNo(split[i]);
			_bean.setRosterGroupId(record.getId());
			_bean.setSeqNo(999);
			if (zhongcnegMap.containsKey(split[i])){
				_bean.setZhongceng("1");
				_bean.setSeqNo(zhongcnegMap.get(split[i]));
			}
			hrmsDutyRosterUserService.save(_bean);
		}
		
		if(record.getGroupUserNames().charAt(record.getGroupUserNames().length()-1) != ',') { 
			record.setGroupUserNames(record.getGroupUserNames()+ ",");
		}
		if(record.getGroupUserString().charAt(record.getGroupUserString().length()-1) != ',') { 
			record.setGroupUserString(record.getGroupUserString()+ ",");
		}
		//保存操作日志
		HrmsDutyRoster oldEntity = selectById(record.getId());
		dutyRosterOperateLogService.saveOperateLog(record,oldEntity, "值班分组",record.getId(),HrmsDutyRosterOpTypeEnum.UPDATE_GROUP,"修改分组-"+record.getRosterName());
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsDutyRoster record = new HrmsDutyRoster();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//保存操作日志
		dutyRosterOperateLogService.saveOperateLog(null,null, "值班分组",record.getId(),HrmsDutyRosterOpTypeEnum.DELETE_GROUP,"删除分组-"+record.getRosterName());
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsDutyRoster selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsDutyRoster> getDataSetList(Page page, HrmsDutyRoster record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//值班管理员
		Boolean right = UserInfoHolder.getRight("ZBGLY_ZBGLY");
		if( !UserInfoHolder.ISADMIN() && !right) {
			record.setControlsIds(UserInfoHolder.getCurrentUserCode());
		}
		List<HrmsDutyRoster> records  = mapper.getDataSetList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	/**
	 * 移除人员信息
	 * @param rosterGroupId
	 * @param employeeNo
	 * @param employeeName
	 * @return
	 */
	@Override
	public Integer removeUser(String rosterGroupId, String employeeNo, String employeeName) {
		Map<String,String> par = new HashMap<String, String>();
		par.put("rosterGroupId", rosterGroupId);
		par.put("employeeNo", employeeNo+",");
		par.put("employeeName", employeeName+",");
		//保存操作日志
		HrmsDutyRoster oldEntity = selectById(rosterGroupId);
		//删除分组人员
		Integer count = mapper.removeUser(par);
		HrmsDutyRoster entity = selectById(rosterGroupId);
		dutyRosterOperateLogService.saveOperateLog(entity,oldEntity, "值班分组",rosterGroupId,HrmsDutyRosterOpTypeEnum.REMOVE_GROUP_MEMBER,"移除员工-"+employeeName);
		return count;
	}
	
	
	
	@Override
	public List<HrmsDutyRoster> getAllData(HrmsDutyRoster record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return mapper.select(record);
	}
	
}
