<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsEmployeeMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsEmployee">
		<!-- WARNING - @mbg.generated -->
		<id column="employee_id" jdbcType="VARCHAR" property="employeeId" />
		<result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
		<result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
		<result column="org_id" jdbcType="VARCHAR" property="orgId" />
		<result column="used_name" jdbcType="VARCHAR" property="usedName" />
		<result column="gender" jdbcType="CHAR" property="gender" />
		<result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
		<result column="identity_number" jdbcType="VARCHAR" property="identityNumber" />
		<result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
		<result column="landline_number" jdbcType="VARCHAR" property="landlineNumber" />
		<result column="employee_category" jdbcType="CHAR" property="employeeCategory" />
		<result column="employee_status" jdbcType="CHAR" property="employeeStatus" />
		<result column="establishment_type" jdbcType="CHAR" property="establishmentType" />
		<result column="birthplace" jdbcType="VARCHAR" property="birthplace" />
		<result column="nationality" jdbcType="CHAR" property="nationality" />
		<result column="political_status" jdbcType="CHAR" property="politicalStatus" /> 
		<result column="avatar" jdbcType="VARCHAR" property="avatar" />
		<result column="address" jdbcType="VARCHAR" property="address" />
		<result column="residence_address" jdbcType="VARCHAR" property="residenceAddress" />
		<result column="postcode" jdbcType="VARCHAR" property="postcode" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="marriage_status" jdbcType="CHAR" property="marriageStatus" />
		<result column="health_status" jdbcType="CHAR" property="healthStatus" />
		<result column="blood_group" jdbcType="VARCHAR" property="bloodGroup" />
		<result column="name_stroke" jdbcType="VARCHAR" property="nameStroke" />
		<result column="name_spell" jdbcType="VARCHAR" property="nameSpell" />
		<result column="personal_profile" jdbcType="VARCHAR" property="personalProfile" />
		<result column="post_id" jdbcType="VARCHAR" property="postId" />
		<result column="salary_level_id" jdbcType="VARCHAR" property="salaryLevelId" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
		<result column="is_enable" jdbcType="CHAR" property="isEnable" />
	</resultMap>

	<select id="getPageList" parameterType="cn.trasen.hrms.model.HrmsEmployee" resultType="cn.trasen.hrms.model.HrmsEmployee">
			SELECT 
				t22.dict_name as operation_type,
			  	t10.jobtitle_basic_name as jobtitleBasicName,
			  	t1.name_spell,
				t1.employee_id, 
				t1.employee_id AS id, 
				t1.employee_no, 
				t1.employee_name, 
				t1.org_id,  
				t1.identity_number,
				t1.birthday, 
				t1.phone_number, 
				t1.employee_category, 
				t1.position_id, 
				t1.post_id, 
				t1.salary_level_id, 
				IF(t1.employee_status = '1' AND t1.is_retire='1','6',t1.employee_status) AS employee_status,
				t1.establishment_type, 
				t1.birthplace, 
				t1.nationality, 
				t1.political_status,  
				t1.gender, 
				t1.email, 
				t1.marriage_status, 
				t1.blood_group, 
				t1.is_enable, 
				t2.old_employee_no AS oldEmployeeNo, 
				t2.entry_date AS entryDate, 
				t2.party_date AS partyDate, 
				t2.check_work_depart AS checkWorkDepart, 
				t2.review_depart AS reviewDepart, 
				t2.unit_start_date AS unitStartDate,
				t3.name AS orgName,
				t4.post_name AS postName,
				t5.salary_level_name AS salaryLevelName,
				t6.position_name AS positionName,
				t8.jobtitle_basic_name AS jobtitleName,
				t7.assessment_date, 
				t9.degree,
				t9.education_type educationType, 
				t9.school_name,
				t9.professional,
				t9.end_time,
				t9.start_time,
				t2.work_start_date AS workStartDate,
				t7.inaugural_Date,
				t7.downward_date,
				t2.first_education_type,
				t2.work_nature,
				t4.post_category AS postCategory,
				t2.job_description_type_time,
				t2.personal_identity,
				t2.job_description_type,
				t2.post_type,
				t2.is_leader,
				t2.concurrent_position, 
				t2.concurrent_position_time,
				t7.acquisition_date,
				t2.authorized_org,
				t2.employ_duty,
				t2.employ_duty_date,
				t2.employ_duty_equally_date,
				t2.employ_duty_duration,
				t2.compliance_training,
				t2.doctor_qualification_certificate,
				t2.midwife,
				t2.operation_date,
				t2.operation_org,
				t2.operation_scope,
				t2.operation_number,
				t2.is_veteran,
				t2.unit_name,
				t2.start_employ_date,
				t2.end_employ_date,
				t1.address, 
				t1.residence_address,
				t2.born_address_name,
				t1.remark,
				t2.archive_address,
				t20.position_name as concurrentPosition,
				t21.dict_name AS engagePost,
				t21.post_name AS engageLevel
			FROM (SELECT * FROM cust_emp_base a1,cust_emp_info a2 WHERE a1.employee_id = a2.info_id) t1
			LEFT JOIN hrms_employee_extend t2 ON t1.employee_id = t2.employee_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN hrms_post t4 ON t4.post_id = t1.post_id
			LEFT JOIN hrms_salary_level t5 ON t5.salary_level_id = t1.salary_level_id
			LEFT JOIN hrms_position t6 ON t6.position_id = t1.position_id
			LEFT JOIN(
				SELECT e.* FROM hrms_jobtitle_info e
				INNER JOIN (
				SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
				) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
			)t7 ON t7.employee_id = t1.employee_id  AND t7.is_deleted='N'
			LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
			LEFT JOIN (
				SELECT e.* FROM hrms_education_info e
				INNER JOIN (
				SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
				) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
			) t9 ON t9.employee_id = t1.employee_id AND t9.is_deleted='N'
			LEFT JOIN comm_jobtitle_basic t10 ON  t8.jobtitle_basic_pid = t10.jobtitle_basic_id
			LEFT JOIN hrms_position t20 ON t20.position_id = t2.concurrent_position		
			LEFT JOIN (
				SELECT MAX(t1.employ_duty_equally_date) AS  employ_duty_equally_date,t1.employee_id,t2.post_name,t3.dict_name FROM hrms_post_information t1
				LEFT JOIN hrms_post t2 ON t1.post_id=t2.post_id
				LEFT JOIN hrms_dict_info t3 ON t2.post_category = t3.dict_value AND t3.dict_type='post_category' AND t3.is_deleted='N'
				GROUP BY t1.employee_id
			)  t21 ON t1.employee_id = t21.employee_id
			LEFT JOIN 	hrms_dict_info t22 ON t2.operation_type = t22.dict_value AND t22.dict_type='operation_type' AND t22.is_deleted='N'	
			WHERE t1.is_deleted = 'N' 
			and employee_status IN('1','5','6','9','10','11','12','13','8')
		and t1.sso_org_code=#{ssoOrgCode}
		<if test="isEnable != null and isEnable != ''">
			<![CDATA[ AND t1.is_enable = #{isEnable} ]]>
		</if>
		<if test="establishmentType != null and establishmentType != ''">
			<![CDATA[ AND t1.establishment_type = #{establishmentType} ]]>
		</if>
		<if test="gender !=null and gender !=''">
			and t1.gender =#{gender}
		</if>
	
		<if test="employeeNo != null and employeeNo != ''">
			AND ((t1.employee_no LIKE CONCAT('%',#{employeeNo},'%') )
			 or (t1.employee_name LIKE CONCAT('%',#{employeeNo},'%') ) 
			 or (t1.name_spell LIKE CONCAT('%',#{employeeNo},'%') ))
		</if>
		
		<if test="orgId != null and orgId != ''">
			<![CDATA[ AND t1.org_id = #{orgId} ]]>
		</if>
		<if test="employeeStatus != null and employeeStatus != ''">
			<![CDATA[ AND t1.employee_status = #{employeeStatus} ]]>
		</if>
		<if test="personalIdentity != null and personalIdentity != ''">
			<![CDATA[ AND t2.personal_identity = #{personalIdentity} ]]>
		</if>
		
		<!-- 时间筛选条件 -->
		<if test="birthdayStartTime != null and birthdayStartTime != '' and birthdayEndTime != null and birthdayEndTime != ''">
			<![CDATA[ and  t1.birthday >= #{birthdayStartTime} AND  t1.birthday <= #{birthdayEndTime} ]]>
		</if>
		
		<if test="workStartDateStart != null and workStartDateStart != '' and workStartDateEnd != null and workStartDateEnd != ''">
			<![CDATA[ and  t2.work_start_date >= #{workStartDateStart} AND  t2.work_start_date <= #{workStartDateEnd} ]]>
		</if>
		
		<if test="entryDateStart != null and entryDateStart != '' and entryDateEnd != null and entryDateEnd != ''">
			<![CDATA[ and  t2.entry_date >= #{entryDateStart} AND  t2.entry_date <= #{entryDateEnd} ]]>
		</if>
		
		<!-- 学历 -->
		<if test="educationType != null and educationType != ''">
			<![CDATA[ and  t9.education_type = #{educationType} ]]>
		</if>

		<if test="workNature != null and workNature != ''">
			<![CDATA[ and  t2.work_nature = #{workNature} ]]>
		</if>

		<if test="doctorQualificationCertificate != null and doctorQualificationCertificate != ''">
			<![CDATA[ and  t2.doctor_qualification_certificate = #{doctorQualificationCertificate} ]]>
		</if>

		<if test="midwife != null and midwife != ''">
			<![CDATA[ and  t2.midwife = #{midwife} ]]>
		</if>

		
		<if test="educationDegree != null and educationDegree != ''">
			<![CDATA[ and  t9.degree = #{educationDegree} ]]>
		</if>
		<if test="politicalStatus != null and politicalStatus != ''">
			<![CDATA[ and  t1.political_status = #{politicalStatus} ]]>
		</if>
		
		<if test="jobtitleBasicName != null and jobtitleBasicName != ''">
			<![CDATA[ and  t10.jobtitle_basic_name = #{jobtitleBasicName} ]]>
		</if>
		
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND t1.org_id in
			<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		
		<if test="htOrgIdList != null and htOrgIdList != ''">
			AND t1.org_id in ${htOrgIdList}
		</if>
		
		<if test="ygtwbdwCode != null and ygtwbdwCode != ''">
			AND t1.org_id != #{ygtwbdwCode}
		</if>
		
		<if test="userCode != null and userCode != ''">
			<![CDATA[ AND t1.employee_no = #{userCode} ]]>
		</if>
	</select>
	
	
	<!-- 花名册查询 -->
	<select id="getRosterDataList" parameterType="cn.trasen.hrms.model.HrmsEmployee" resultType="cn.trasen.hrms.model.HrmsEmployee">
			SELECT
				DISTINCT 
				t1.employee_id,
				t1.name_spell, 
				t1.employee_id AS id, 
				t1.employee_no, 
				t1.employee_name,
				t1.gender, 
				t1.org_id, 
				t3.name AS orgName, 
				t1.identity_number,
				t1.birthday, 
				t1.phone_number, 
				i.employee_category, 
				t1.position_id, 
				i.post_id,
				t1.create_date,
				i.establishment_type,
				IF(i.is_retire='1','1',t1.employee_status) AS employee_status,
				i.birthplace, 
				i.nationality, 
				i.political_status,  
				i.marriage_status, 
				i.blood_group, 
				t1.entry_date AS entryDate, 
				i.party_date AS partyDate, 
				i.check_work_depart AS checkWorkDepart, 
				i.unit_start_date AS unitStartDate,
				t4.post_name AS postName,
				t6.position_name AS positionName,
				t8.jobtitle_basic_name AS jobtitleName,
				t7.assessment_date, 
				t9.degree,
				t9.education_type educationType, 
				t9.school_name,
				t9.professional,
				t9.end_time,
				t9.start_time,
				DATE_FORMAT(i.work_start_date, '%Y-%m-%d') AS workStartDate,
				DATE_FORMAT(t7.inaugural_Date, '%Y-%m-%d') as inaugural_Date,
				t7.downward_date,
				i.first_education_type,
				i.work_nature,
				t4.post_category AS postCategory, 
				i.job_deion_type_time,
				t1.personal_identity,
				i.job_deion_type,
				t1.post_type,
				i.is_leader,
				i.concurrent_position, 
				i.concurrent_position_time,
				DATE_FORMAT(t7.acquisition_date, '%Y-%m-%d') acquisition_date,
				i.authorized_org,
				i.employ_duty,
				i.employ_duty_date,
				i.employ_duty_equally_date,
				i.employ_duty_duration,
				i.compliance_training,
				i.doctor_qualification_certificate,
				i.midwife,
				i.operation_date,
				i.operation_org,
				i.operation_scope,
				i.operation_number,
				i.is_veteran,
				i.unit_name,
				i.start_employ_date,
				i.end_employ_date,
				i.address, 
				i.residence_address,
				i.born_address_name,
				t1.remark,
				i.archive_address ,
				t20.position_name AS concurrentPosition,
				i.operation_type,
				t10.jobtitle_basic_name AS jobtitleBasicName,
				tte.incident_time as retireDateExport,
				t9.end_time as endTimeExport
			FROM cust_emp_base t1
			left join cust_emp_info i on t1.employee_id = i.info_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN comm_post t4 ON t4.post_id = i.post_id
			LEFT JOIN comm_position t6 ON t6.position_id = t1.position_id
			<choose>
				<when test="_databaseId=='kingbase' or _databaseId=='postgresql'">
					LEFT JOIN( select e.* from (SELECT distinct employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id) t 
						INNER JOIN	 hrms_jobtitle_info e					
					     ON t.employee_id = e.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N'
					)t7 ON t7.employee_id = t1.employee_id  AND t7.is_deleted='N'
					LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
					LEFT JOIN (
						SELECT e.* FROM (SELECT distinct employee_id,MIN(highest_level) highest_level,min(degree_number) degree_number FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id) t
						INNER JOIN  hrms_education_info e ON t.employee_id = e.employee_id AND t.highest_level = e.highest_level and e.degree_number = t.degree_number WHERE e.is_deleted = 'N' 
					) t9 ON t9.employee_id = t1.employee_id AND t9.is_deleted='N'
				</when>
				<otherwise>
					LEFT JOIN(
						SELECT e.* FROM hrms_jobtitle_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
					)t7 ON t7.employee_id = t1.employee_id  AND t7.is_deleted='N'
					LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
					LEFT JOIN (
						SELECT e.* FROM hrms_education_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
					) t9 ON t9.employee_id = t1.employee_id AND t9.is_deleted='N'
				</otherwise>
			</choose>
			LEFT JOIN comm_jobtitle_basic t10 ON  t8.jobtitle_basic_pid = t10.jobtitle_basic_id
			LEFT JOIN comm_position t20 ON t20.position_id = i.concurrent_position
				LEFT JOIN (
				SELECT employee_id,establishment_type,incident_time FROM hrms_personnel_incident WHERE incident_category='2' AND is_deleted='N' AND approval_status='4'
			) tte ON t1.employee_id = tte.employee_id
			WHERE t1.is_deleted = 'N' and t1.employee_no not in ('admin','ts')
			and t1.sso_org_code=#{ssoOrgCode}
		<!-- 档案类型 -->
		<if test="archivesType != null and archivesType != '' and archivesType != 'null'.toString()">
			 AND t1.archives_type = #{archivesType}
		</if>
        <!-- 员工状态 -->
        <if test="multiEmployeeStatus != null and multiEmployeeStatus != ''">
            AND t1.employee_status in (${multiEmployeeStatus})
        </if>
		<!-- 编制类别 -->
		<if test="establishmentType != null and establishmentType != ''">
			<if test='queryStatus =="8"'>
				AND tte.establishment_type = #{establishmentType}
			</if>

			<if test='queryStatus !="8"' >
				AND i.establishment_type = #{establishmentType}
			</if>
		</if>
		<!-- 员工状态 -->
		<if test="employeeStatus != null and employeeStatus !='' and queryStatus !='8888'">
			AND t1.employee_status = #{employeeStatus}
		</if>
		<if test='queryStatus =="8888"'>
			and (t1.employee_status IS NULL OR t1.employee_status = '')
		</if>
		<if test="identityNumber!=null and identityNumber!=''">
  			and t1.identity_number like concat('',#{identityNumber},'%')
  		</if>
  		
  		<if test="empPayroll!=null and empPayroll!=''">
	  		and t1.emp_payroll like concat('',#{empPayroll},'%')
	  	</if>
		
		<!-- 中层领导-->
		<if test="midwife != null and midwife != ''">
			and  i.shifouzhongcengganbu = #{midwife}
		</if>
		<!-- 中医骨干-->
		<if test="doctorQualificationCertificate != null and doctorQualificationCertificate != ''">
			and  i.shifouxinglinrencai = #{doctorQualificationCertificate}
		</if>

		<if test="employeeName != null and employeeName != ''">
			AND ((t1.employee_no LIKE CONCAT('%',#{employeeName},'%') )
			or (t1.employee_name LIKE CONCAT('%',#{employeeName},'%') )
			or (t3.name LIKE CONCAT('%',#{employeeName},'%') )
			or (t1.name_spell LIKE CONCAT('%',#{employeeName},'%') ))
		</if>
		
		<if test="employeeStatuses != null and employeeStatuses.size() > 0">
		and (t1.employee_status in
			<foreach collection="employeeStatuses" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
        )
		</if>
  	
  	
  		<if test="establishmentTypes != null and establishmentTypes.size() > 0">
			<if test='queryStatus =="8"'>
				and (tte.establishment_type in
				<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				)
			</if>
			<if test='queryStatus !="8"'>
				and (i.establishment_type in
				<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
				)
			</if>
		</if>
	
		<if test="employeeCategorys != null and employeeCategorys.size() > 0">
			and (i.employee_category in
			<foreach collection="employeeCategorys" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
		
		<if test="politicalStatuses != null and politicalStatuses.size() > 0">
			and (i.political_status in
			<foreach collection="politicalStatuses" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
		
		<if test="nationalityes != null and nationalityes.size() > 0">
			and (i.nationality in
			<foreach collection="nationalityes" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
	
	
		<if test="marriageStatuses != null and marriageStatuses.size() > 0">
			and (i.marriage_status in
			<foreach collection="marriageStatuses" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
	
	
		<if test="positionNames != null and positionNames.size() > 0">
			and (t1.position_id in
			<foreach collection="positionNames" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
		
		<if test="personalIdentitys != null and personalIdentitys.size() > 0">
			and (t1.personal_identity in
			<foreach collection="personalIdentitys" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
	
		<if test="plgws != null and plgws.size() > 0">
			and (i.plgw in
			<foreach collection="plgws" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
	
		<if test="operationTypes != null and operationTypes.size() > 0">
			and (i.operation_type in
			<foreach collection="operationTypes" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
	
		<if test="educationTypes != null and educationTypes.size() > 0">
			and (t9.education_type in
			<foreach collection="educationTypes" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
		
		<if test="jobtitleNames != null and jobtitleNames.size() > 0">
			and (t7.jobtitle_name in
			<foreach collection="jobtitleNames" index="index" item="item" open="(" separator="," close=")">
	            #{item}
	        </foreach>
	        )
		</if>
		
		<if test="birthdayStartTime!=null and birthdayStartTime!=''">
	  		and t1.birthday >=  #{birthdayStartTime}
	  	</if>
  	
	  	<if test="birthdayEndTime!=null and birthdayEndTime!=''">
	  		and #{birthdayEndTime} >=t1.birthday
	  	
	  	</if>
	  	
	  	<if test="shifouguipeirenyuan!=null and shifouguipeirenyuan!=''">
	  		and i.shifouguipeirenyuan = #{shifouguipeirenyuan}
	  	</if>

		<if test="zhuanyeyingcai!=null and zhuanyeyingcai!=''">
			and i.zhuanyeyingcai = #{zhuanyeyingcai}
		</if>
	
	</select>
	
	<select id="findDetailById" parameterType="string" resultType="cn.trasen.hrms.model.HrmsEmployee">
		SELECT 
				t1.employee_id, 
				t1.employee_no, 
				t1.employee_name, 
				t1.org_id, 
				i.used_name,
				t1.gender, 
				t1.birthday,
				t1.identity_number, 
				t1.phone_number, 
				i.employee_category, 
				t1.employee_status, 
				i.establishment_type, 
				i.birthplace, 
				i.nationality,
				i.political_status, 
				t1.avatar, 
				i.address, 
				i.residence_address, 
				i.postcode,
				t1.email, 
				i.marriage_status, 
				i.health_status, 
				i.blood_group, 
				t1.name_spell, 
				i.personal_profile, 
				t1.position_id, 
				i.post_id, 
				i.salary_level_id, 
				i.enterprise_id, 
				t1.create_date, 
				t1.create_user, 
				t1.create_user_name, 
				t1.update_date,
				t1.update_user, 
				t1.update_user_name, 
				t1.is_enable, 
				t1.is_deleted, 
				t1.remark,
				t1.salary_appoint,
				t1.entry_date AS entryDate,
				t2.operation_type,
				t2.old_employee_no AS oldEmployeeNo, 
				t2.his_employee_no AS hisEmployeeNo, 
				t2.entry_date AS entryDate,
				t2.retire_date AS retireDate, 
				t2.quit_date AS quitDate, 
				t2.reemployment_date AS reemploymentDate,
				t2.party_date AS partyDate, 
				t2.work_start_date AS workStartDate, 
				t2.unit_start_date AS unitStartDate,
				t2.personal_identity AS personalIdentity, 
				t2.work_nature AS workNature, 
				t2.good_at AS goodAt,
				t2.check_work_depart AS checkWorkDepart, 
				t2.review_depart AS reviewDepart, 
				t2.upgrade_flag AS upgradeFlag,
				t2.improve_flag AS improveFlag, 
				t2.is_duplicate_entry AS isDuplicateEntry, 
				t2.emergency_contact AS emergencyContact,
				t2.emergency_tel AS emergencyTel, 
				t2.probation_salary AS probationSalary, 
				t2.regular_salary AS regularSalary,
				t2.buy_social_date AS buySocialDate, 
				t2.buy_provident_date AS buyProvidentDate, 
				t2.salary_remark AS salaryRemark,
				t3.name AS orgName,
				t4.post_category AS postCategory, 
				t4.post_name AS postName,
				t5.salary_level_name AS salaryLevelName, 
				t5.salary_level_category AS salaryLevelCategory,
				t6.position_name AS positionName,
				t2.first_education_type,
				t2.born_address,
				t2.born_address_name,
				t2.Job_description_type,
				t2.is_leader,
				t2.post_type,
				t2.job_description_type_time,
				t2.concurrent_position, 
				t2.concurrent_position_time,
				t2.post_type,
				t2.authorized_org,
				t2.employ_duty,
				t2.employ_duty_date,
				t2.employ_duty_equally_date,
				t2.employ_duty_duration,
				t7.end_time,
				t7.start_time,
				t7.education_type,
				t2.compliance_training,
				t2.doctor_qualification_certificate,
				t2.midwife,
				t2.start_employ_date,
				t2.end_employ_date,
				t2.is_veteran,
				t2.unit_name,
				t2.business_id,
				t2.business_id2,
				t2.business_id3,
				t2.operation_date,
				t2.operation_org,
				t2.operation_scope,
				t2.operation_number,
				t9.jobtitle_basic_name as employDuty,
				t2.archive_address	
			FROM (
				SELECT
					employee_name,employee_no,org_id,employee_id,gender,birthday,
					employee_status,positive_time,retirement_time,
					position_id,year_work,bankcardname,emp_age,
					establishment_type,salary_appoint,is_deleted,
					personal_identity,identity_number,bankcardno,
					used_name,phone_number,landline_number,employee_category,
					birthplace,email,marriage_status,health_status,
					blood_group,null as name_stroke,name_spell,personal_profile,post_id,salary_level_id,
					enterprise_id,create_date,create_user,create_user_name,update_date,
					update_user,update_user_name,is_enable,remark,avatar,address,
					entry_date,'N' as is_temp,nationality,political_status,residence_address,postcode
				FROM cust_emp_base base
					left join cust_emp_info inf on base.employee_id = inf.info_id
				UNION ALL
				SELECT
					employee_name,employee_no,org_id,id as employee_id,gender,birthday,
					(case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
					null as position_id,null as year_work,null as bankcardname,null as emp_age,
					tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
					tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
					employee_name as used_name,phone_number,null as landline_number,null as employee_category,
					null as  birthplace,null as email,null as marriage_status,null as health_status,
					null as blood_group,null as name_stroke,null as name_spell,null as personal_profile,null as post_id,null as salary_level_id,
					null as enterprise_id,create_date, create_user,create_user_name,update_date,
					update_user,update_user_name,1 as is_enable,remark,null as avatar,null as address,
					join_date as entry_date,'Y' as is_temp,null as nationality,null as political_status,
					null as residence_address,null as postcode
				FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
			) t1	
			left join cust_emp_info i on t1.employee_id = i.info_id
			LEFT JOIN hrms_employee_extend t2 ON t1.employee_id = t2.employee_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN hrms_post t4 ON t4.post_id = i.post_id
			LEFT JOIN hrms_salary_level t5 ON t5.salary_level_id = i.salary_level_id
			LEFT JOIN hrms_position t6 ON t6.position_id = t1.position_id
			<choose>
				<when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
				LEFT JOIN (
					SELECT e.* FROM hrms_education_info e
					INNER JOIN (
					SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
					) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' 
				) t7 ON t7.employee_id = t1.employee_id AND t7.is_deleted='N'
				LEFT JOIN(
					SELECT e.* FROM hrms_jobtitle_info e
					INNER JOIN (
					SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
					) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N'				
				</when>
				<otherwise>
					LEFT JOIN (
						SELECT e.* FROM hrms_education_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
					) t7 ON t7.employee_id = t1.employee_id AND t7.is_deleted='N'
					LEFT JOIN(
						SELECT e.* FROM hrms_jobtitle_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
				</otherwise>
			</choose>
			)t8 ON t8.employee_id = t1.employee_id  AND t8.is_deleted='N'
			LEFT JOIN comm_jobtitle_basic t9 ON t8.jobtitle_name=t9.jobtitle_basic_id
			WHERE t1.employee_id = #{employeeId} and t1.is_deleted = 'N'
	</select>

	<select id="isExistEmployeeByCode" parameterType="string" resultType="Integer">
		  select count(1) from (
		   SELECT
				employee_name,employee_no,org_id,employee_id,is_deleted
			FROM cust_emp_base
			UNION ALL
			SELECT
				employee_name,employee_no,org_id,id as employee_id,is_deleted
			FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
		  ) t where t.employee_no = #{employeeNo} and t.is_deleted='N'
	</select>


	<select id="findDetailByIds" parameterType="java.util.List" resultType="cn.trasen.hrms.model.HrmsEmployee">
			select 
				t1.employee_id, 
				t1.employee_no, 
				t1.employee_name, 
				t1.org_id, 
				i.used_name,
				t1.gender, 
				t1.birthday,
				t1.identity_number, 
				t1.phone_number, 
				i.employee_category, 
				t1.employee_status, 
				i.establishment_type, 
				i.birthplace, 
				i.nationality,
				i.political_status, 
				t1.avatar, 
				i.address, 
				i.residence_address, 
				i.postcode,
				t1.email, 
				i.marriage_status, 
				i.health_status, 
				i.blood_group, 
				t1.name_spell, 
				i.personal_profile, 
				t1.position_id, 
				i.post_id, 
				i.salary_level_id, 
				i.enterprise_id, 
				t1.create_date, 
				t1.create_user, 
				t1.create_user_name, 
				t1.update_date,
				t1.update_user, 
				t1.update_user_name, 
				t1.is_enable, 
				t1.is_deleted, 
				t1.remark,
				t2.operation_type,
				t2.old_employee_no AS oldEmployeeNo, 
				t2.his_employee_no AS hisEmployeeNo, 
				t2.entry_date AS entryDate,
				t2.retire_date AS retireDate, 
				t2.quit_date AS quitDate, 
				t2.reemployment_date AS reemploymentDate,
				t2.party_date AS partyDate, 
				t2.work_start_date AS workStartDate, 
				t2.unit_start_date AS unitStartDate,
				t2.personal_identity AS personalIdentity, 
				t2.work_nature AS workNature, 
				t2.good_at AS goodAt,
				t2.check_work_depart AS checkWorkDepart, 
				t2.review_depart AS reviewDepart, 
				t2.upgrade_flag AS upgradeFlag,
				t2.improve_flag AS improveFlag, 
				t2.is_duplicate_entry AS isDuplicateEntry, 
				t2.emergency_contact AS emergencyContact,
				t2.emergency_tel AS emergencyTel, 
				t2.probation_salary AS probationSalary, 
				t2.regular_salary AS regularSalary,
				t2.buy_social_date AS buySocialDate, 
				t2.buy_provident_date AS buyProvidentDate, 
				t2.salary_remark AS salaryRemark,
				t3.name AS orgName,
				t4.post_category AS postCategory, 
				t4.post_name AS postName,
				t5.salary_level_name AS salaryLevelName, 
				t5.salary_level_category AS salaryLevelCategory,
				t6.position_name AS positionName,
				t2.first_education_type,
				t2.born_address,
				t2.born_address_name,
				t2.Job_description_type,
				t2.is_leader,
				t2.post_type,
				t2.job_description_type_time,
				t2.concurrent_position, 
				t2.concurrent_position_time,
				t2.post_type,
				t2.authorized_org,
				t2.employ_duty,
				t2.employ_duty_date,
				t2.employ_duty_equally_date,
				t2.employ_duty_duration,
				t7.end_time,
				t7.start_time,
				t7.education_type,
				t2.compliance_training,
				t2.doctor_qualification_certificate,
				t2.midwife,
				t2.start_employ_date,
				t2.end_employ_date,
				t2.is_veteran,
				t2.unit_name,
				t2.business_id,
				t2.business_id2,
				t2.business_id3,
				t2.operation_date,
				t2.operation_org,
				t2.operation_scope,
				t2.operation_number,
				t9.jobtitle_basic_name as employDuty,
				t2.archive_address
			FROM (
				SELECT
					employee_name,employee_no,org_id,employee_id,gender,birthday,
					employee_status,positive_time,retirement_time,
					position_id,year_work,bankcardname,emp_age,
					establishment_type,salary_appoint,is_deleted,
					personal_identity,identity_number,bankcardno,
					used_name,phone_number,landline_number,employee_category,
					birthplace,email,marriage_status,health_status,
					blood_group,null as name_stroke,name_spell,personal_profile,post_id,salary_level_id,
					enterprise_id,create_date,create_user,create_user_name,update_date,
					update_user,update_user_name,is_enable,remark,avatar,address,
					entry_date,'N' as is_temp,nationality,political_status,residence_address,postcode
				FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
				UNION ALL
				SELECT
					employee_name,employee_no,org_id,id as employee_id,gender,birthday,
					(case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
					null as position_id,null as year_work,null as bankcardname,null as emp_age,
					tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
					tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
					employee_name as used_name,phone_number,null as landline_number,null as employee_category,
					null as  birthplace,null as email,null as marriage_status,null as health_status,
					null as blood_group,null as name_stroke,null as name_spell,null as personal_profile,null as post_id,null as salary_level_id,
					null as enterprise_id,create_date, create_user,create_user_name,update_date,
					update_user,update_user_name,1 as is_enable,remark,null as avatar,null as address,
					join_date as entry_date,'Y' as is_temp,null as nationality,null as political_status,
					null as residence_address,null as postcode
				FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
			) t1
			left join cust_emp_info i on t1.employee_id = i.info_id
			LEFT JOIN hrms_employee_extend t2 ON t1.employee_id = t2.employee_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN hrms_post t4 ON t4.post_id = i.post_id
			LEFT JOIN hrms_salary_level t5 ON t5.salary_level_id = i.salary_level_id
			LEFT JOIN hrms_position t6 ON t6.position_id = t1.position_id
			<choose>
				<when test="_databaseId=='kingbase' or _databaseId=='postgresql'">
					LEFT JOIN (
						SELECT e.* FROM hrms_education_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' 
					) t7 ON t7.employee_id = t1.employee_id AND t7.is_deleted='N'
					LEFT JOIN(
						SELECT e.* FROM hrms_jobtitle_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N'
				</when>
				<otherwise>
					LEFT JOIN (
						SELECT e.* FROM hrms_education_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
					) t7 ON t7.employee_id = t1.employee_id AND t7.is_deleted='N'
					LEFT JOIN(
						SELECT e.* FROM hrms_jobtitle_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
				</otherwise>
			</choose>
			)t8 ON t8.employee_id = t1.employee_id  AND t8.is_deleted='N'
			LEFT JOIN comm_jobtitle_basic t9 ON t8.jobtitle_name=t9.jobtitle_basic_id
		WHERE  t1.is_deleted='N' and t1.employee_id in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="findByEmployeeNo" parameterType="string" resultType="cn.trasen.hrms.model.HrmsEmployee">
		<![CDATA[
			SELECT
				t1.employee_id, 
				t1.employee_no, 
				t1.employee_name, 
				t1.org_id, 
				i.used_name,
				t1.gender, 
				t1.birthday, 
				t1.identity_number, 
				t1.phone_number, 
				i.employee_category, 
				t1.employee_status, 
				i.establishment_type, 
				i.birthplace, 
				i.nationality,
				i.political_status, 
				t1.avatar, 
				i.address, 
				i.residence_address, 
				i.postcode,
				t1.email, 
				i.marriage_status, 
				i.health_status, 
				i.blood_group, 
				t1.name_spell, 
				i.personal_profile, 
				t1.position_id, 
				i.post_id, 
				i.salary_level_id, 
				i.enterprise_id, 
				t1.create_date, 
				t1.create_user, 
				t1.create_user_name, 
				t1.update_date,
				t1.update_user, 
				t1.update_user_name, 
				t1.is_enable, 
				t1.is_deleted, 
				t1.remark,
				t1.personal_identity,
				t3.name AS orgName,
				t4.post_category AS postCategory, 
				t4.post_name AS postName,
				t5.salary_level_name AS salaryLevelName, 
				t5.salary_level_category AS salaryLevelCategory,
				t6.position_name AS positionName,
				t8.jobtitle_basic_name AS jobtitleName,
				i.technical,
				t1.hosp_code
			FROM (
				SELECT
						employee_name,employee_no,org_id,employee_id,gender,birthday,
						employee_status,positive_time,retirement_time,
						position_id,year_work,bankcardname,emp_age,
						establishment_type,salary_appoint,is_deleted,
						personal_identity,identity_number,bankcardno,
						used_name,phone_number,landline_number,employee_category,
						birthplace,email,marriage_status,health_status,
						blood_group,null as name_stroke,name_spell,personal_profile,post_id,salary_level_id,
						enterprise_id,create_date,create_user,create_user_name,update_date,
						update_user,update_user_name,is_enable,remark,avatar,address,
						entry_date,'N' as is_temp,nationality,political_status,residence_address,postcode,hosp_code
					FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
					UNION ALL
					SELECT
						employee_name,employee_no,org_id,id as employee_id,gender,birthday,
						(case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
						null as position_id,null as year_work,null as bankcardname,null as emp_age,
						tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
						tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
						employee_name as used_name,phone_number,null as landline_number,null as employee_category,
						null as  birthplace,null as email,null as marriage_status,null as health_status,
						null as blood_group,null as name_stroke,null as name_spell,null as personal_profile,null as post_id,null as salary_level_id,
						null as enterprise_id,create_date, create_user,create_user_name,update_date,
						update_user,update_user_name,1 as is_enable,remark,null as avatar,null as address,
						join_date as entry_date,'Y' as is_temp,null as nationality,null as political_status,
						null as residence_address,null as postcode,NULL AS hosp_code
					FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
			) t1
			left join cust_emp_info i on t1.employee_id = i.info_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN hrms_post t4 ON t4.post_id = i.post_id
			LEFT JOIN hrms_salary_level t5 ON t5.salary_level_id = i.salary_level_id
			LEFT JOIN hrms_position t6 ON t6.position_id = t1.position_id
			LEFT JOIN hrms_jobtitle_info t7 ON t7.employee_id = t1.employee_id and t7.highest_level=1 and t7.is_deleted = 'N'
			LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
			WHERE t1.employee_no = #{employeeNo} AND t1.is_deleted='N'
		]]>
	</select>

	<!-- 分页获取所有员工信息 -->
	<select id="pagesAllEmployeeByParam" parameterType="cn.trasen.hrms.model.HrmsEmployee" resultType="cn.trasen.hrms.model.HrmsEmployee">
		SELECT
				t1.employee_id,
				t1.employee_no,
				t1.employee_name,
				t1.org_id,
				i.used_name,
				t1.gender,
				t1.birthday,
				t1.identity_number,
				t1.phone_number,
				i.employee_category,
				t1.employee_status,
				i.establishment_type,
				i.birthplace,
				i.nationality,
				i.political_status,
				t1.avatar,
				i.address,
				i.residence_address,
				i.postcode,
				t1.email,
				i.marriage_status,
				i.health_status,
				i.blood_group,
				t1.name_spell,
				i.personal_profile,
				t1.position_id,
				i.post_id,
				i.salary_level_id,
				i.enterprise_id,
				t1.create_date,
				t1.create_user,
				t1.create_user_name,
				t1.update_date,
				t1.update_user,
				t1.update_user_name,
				t1.is_enable,
				t1.is_deleted,
				t1.remark,
				t1.personal_identity,
				t3.name AS orgName,
				t4.post_category AS postCategory,
				t4.post_name AS postName,
				t5.salary_level_name AS salaryLevelName,
				t5.salary_level_category AS salaryLevelCategory,
				t6.position_name AS positionName,
				t8.jobtitle_basic_name AS jobtitleName
			FROM (
				SELECT
						employee_name,employee_no,org_id,employee_id,gender,birthday,
						employee_status,positive_time,retirement_time,
						position_id,year_work,bankcardname,emp_age,
						establishment_type,salary_appoint,is_deleted,
						personal_identity,identity_number,bankcardno,
						used_name,phone_number,landline_number,employee_category,
						birthplace,email,marriage_status,health_status,
						blood_group,null as name_stroke,name_spell,personal_profile,post_id,salary_level_id,
						enterprise_id,create_date,create_user,create_user_name,update_date,
						update_user,update_user_name,is_enable,remark,avatar,address,
						entry_date,'N' as is_temp,nationality,political_status,residence_address,postcode
					FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
					UNION ALL
					SELECT
						employee_name,employee_no,org_id,id as employee_id,gender,birthday,
						(case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
						null as position_id,null as year_work,null as bankcardname,null as emp_age,
						tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
						tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
						employee_name as used_name,phone_number,null as landline_number,null as employee_category,
						null as  birthplace,null as email,null as marriage_status,null as health_status,
						null as blood_group,null as name_stroke,null as name_spell,null as personal_profile,null as post_id,null as salary_level_id,
						null as enterprise_id,create_date, create_user,create_user_name,update_date,
						update_user,update_user_name,1 as is_enable,remark,null as avatar,null as address,
						join_date as entry_date,'Y' as is_temp,null as nationality,null as political_status,
						null as residence_address,null as postcode
					FROM hrms_employee_temporary et where et.is_sync_salary = 'Y'
			) t1
			left join cust_emp_info i on t1.employee_id = i.info_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN hrms_post t4 ON t4.post_id = i.post_id
			LEFT JOIN hrms_salary_level t5 ON t5.salary_level_id = i.salary_level_id
			LEFT JOIN hrms_position t6 ON t6.position_id = t1.position_id
			LEFT JOIN hrms_jobtitle_info t7 ON t7.employee_id = t1.employee_id and t7.highest_level=1 and t7.is_deleted = 'N'
			LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
            where t1.is_deleted = 'N'
			<if test="employeeNo != null and employeeNo != ''">
				AND ((t1.employee_no LIKE CONCAT('%',#{employeeNo},'%') )
				or (t1.employee_name LIKE
				CONCAT('%',#{employeeNo},'%'))
				or (t1.name_spell LIKE CONCAT('%',#{employeeNo},'%') ))
			</if>
			<if test="employeeName != null and employeeName != ''">
				AND (
				(t1.employee_no LIKE CONCAT('%',#{employeeName},'%'))
				or (t1.employee_name LIKE CONCAT('%',#{employeeName},'%'))
				or (t1.name_spell LIKE CONCAT('%',#{employeeName},'%'))
				or (t3.NAME LIKE CONCAT('%',#{employeeName},'%'))
				or (t1.phone_number LIKE CONCAT('%',#{employeeName},'%'))
				)
			</if>
			<if test="personalIdentity != null and personalIdentity != ''">
				AND t1.personal_identity = #{personalIdentity}
			</if>

			<if test="gender != null and gender != ''">
				AND t1.gender = #{gender}
			</if>
			<if test="orgIdList != null and orgIdList.size() > 0">
				AND t1.org_id in
				<foreach collection="orgIdList" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="employeeStatusList != null and employeeStatusList.size() > 0">
				AND t1.employee_status in
				<foreach collection="employeeStatusList" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
	</select>

	<select id="findByEmployeeNos" parameterType="java.util.List" resultType="cn.trasen.hrms.model.HrmsEmployee">
		SELECT 
			e.*,
			i.*,
			o.name AS orgName
		FROM cust_emp_base e
		left join cust_emp_info i on e.employee_id = i.info_id
		LEFT JOIN comm_organization o ON e.org_id = o.organization_id and o.is_deleted = 'N'
		WHERE e.is_deleted = 'N'
		AND e.employee_no in
		<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
	<insert id="batchInsert">
		<![CDATA[
			INSERT INTO cust_emp_base 
			(
				employee_id, 
				employee_no, 
				employee_name,
				org_id,
				used_name, 
				gender,
				birthday,
				identity_number,
				phone_number,
				landline_number,
				employee_category,
				employee_status,
				establishment_type,
				birthplace,
				nationality,
				political_status,
				address,
				residence_address,
				postcode,
				email,
				position_id,
				post_id,
				salary_level_id,
				remark,
				is_enable,
				create_date, 
				create_user, 
				is_deleted 
			) 
			VALUES 
		]]>
		<foreach collection="list" item="item" index="index" separator=",">
			<![CDATA[
			(
				#{item.employeeId}, 
				#{item.employeeNo}, 
				#{item.employeeName},
				#{item.orgId}, 
				#{item.usedName}, 
				#{item.gender}, 
				#{item.birthday}, 
				#{item.identityNumber}, 
				#{item.phoneNumber}, 
				#{item.landlineNumber}, 
				#{item.employeeCategory}, 
				#{item.employeeStatus}, 
				#{item.establishmentType}, 
				#{item.birthplace}, 
				#{item.nationality}, 
				#{item.politicalStatus}, 
				#{item.address}, 
				#{item.residenceAddress}, 
				#{item.postcode},
				#{item.email}, 
				#{item.positionId}, 
				#{item.postId}, 
				#{item.salaryLevelId}, 
				#{item.remark}, 
				#{item.isEnable},
				#{item.createDate}, 
				#{item.createUser}, 
				#{item.isDeleted}
			)
			]]>
		</foreach>
	</insert>
	
	<!-- 人力资源分布 start -->
	
		<!-- 统计人数 -->
	<select id="zytjNumber" resultType="cn.trasen.hrms.utils.MapUtil" parameterType="java.util.List">
		SELECT 
			IF(IFNULL(personal_identity,'')='',10,personal_identity) as k,COUNT(1) as v
		FROM
		cust_emp_base
		WHERE is_deleted='N' and employee_name not in ('admin','ts') and sso_org_code=#{ssoOrgCode}
		and employee_status IN ('1','5','6','9','10','11','12','13','99')
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		GROUP BY IF(IFNULL(personal_identity,'')='',10,personal_identity)
	</select>
	
	<!-- 统计男性 -->
	<select id="male" resultType="cn.trasen.hrms.utils.MapUtil">
		SELECT personal_identity AS k,
			SUM(IF(`gender`=0,score,0)) AS v
		FROM(
		    SELECT IF( IFNULL( personal_identity, '' )= '', 10, personal_identity ) as personal_identity,gender,COUNT(1) AS score
		   FROM
		cust_emp_base
		WHERE is_deleted='N' and employee_name not in ('admin','ts')
		and sso_org_code=#{ssoOrgCode}
		AND employee_status IN ('1','5','6','9','10','11','12','13','99')
				<if test="orgIdList != null and orgIdList.size() > 0">
			AND org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		
		GROUP BY IF(IFNULL(personal_identity,'')='',10,personal_identity),gender
		)AS A 
		GROUP BY personal_identity
	</select>
	
	<select id="woman" resultType="cn.trasen.hrms.utils.MapUtil">
		SELECT personal_identity AS k,
			SUM(IF(`gender`=1,score,0)) AS v
		FROM(
		    SELECT IF(IFNULL( personal_identity, '' )= '', 10, personal_identity ) as personal_identity,gender,COUNT(1) AS score
		   FROM
		cust_emp_base
		WHERE is_deleted='N' and sso_org_code=#{ssoOrgCode} 
		and employee_name not in ('admin','ts') AND employee_status IN ('1','5','6','9','10','11','12','13','99')
	 	AND org_id != '338227434429353984'
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		GROUP BY IF(IFNULL(personal_identity,'')='',10,personal_identity),gender
		)AS A 
		GROUP BY personal_identity
	</select>
	
	<!-- 查询年龄分布 -->
	<select id="twenty" resultType="cn.trasen.hrms.utils.MapUtil" >
		SELECT
			IF(IFNULL(t1.personal_identity,'')='',10,t1.personal_identity) AS k,
			SUM(CASE WHEN TIMESTAMPDIFF(YEAR, t1.birthday, CURDATE()) &gt;=  ${param1} AND TIMESTAMPDIFF(YEAR, t1.birthday, CURDATE()) &lt;=  ${param2}  THEN 1 ELSE 0 END)  AS v
		FROM cust_emp_base t1
		WHERE t1.is_deleted='N' and t1.sso_org_code=#{ssoOrgCode}
		and t1.employee_no not in ('admin','ts')
		 AND t1.employee_status IN ('1','5','6','9','10','11','12','13','99')
		
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND t1.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		
		GROUP BY IF(IFNULL(t1.personal_identity,'')='',10,t1.personal_identity)
	</select>
	
	<select id="education" resultType="cn.trasen.hrms.utils.MapUtil" >
	SELECT 
		IF(IFNULL(t1.personal_identity,'')='',10,t1.personal_identity) AS k,
		COUNT(t1.employee_id) AS v
	FROM  cust_emp_base t1 
	LEFT JOIN (
		SELECT ed.employee_id,
		ed.school_name,ed.education_type,dict.ITEM_NAME AS education_type_name ,ed.start_time,ed.end_time,ed.professional
		FROM hrms_education_info ed
		LEFT JOIN comm_dict_item dict ON ed.education_type = dict.item_code AND dict.dic_type_id = 'education_type'
		WHERE ed.is_deleted = 'N' AND ed.highest_level=1 GROUP BY ed.employee_id 
	) t2 ON t1.employee_id = t2.employee_id
	WHERE t1.employee_status IN (1,6,12,9,10,11,13,99)
	AND t1.is_deleted = 'N'  AND t1.employee_no not in ('admin','ts')
		and t1.sso_org_code=#{ssoOrgCode}
		AND t1.org_id != '338227434429353984' 
		<if test="orgIdList != null and orgIdList.size() > 0">
			AND t1.org_id IN
			<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
				#{orgId}
			</foreach>
		</if>
		AND ${param1} 
		GROUP BY IF(IFNULL(t1.personal_identity,'')='',10,t1.personal_identity)
	</select>
	
	<!-- 职称统计 -->
	<select id="technical" resultType="cn.trasen.hrms.utils.MapUtil" >
		SELECT 
			IF(IFNULL(t1.personal_identity,'')='',10,t1.personal_identity) AS k,
			COUNT(t1.employee_id) AS v
		FROM  cust_emp_base t1 
		LEFT JOIN (
			SELECT info.employee_id,MIN(info.highest_level) highest_level,info.jobtitle_level,b.jobtitle_basic_name AS jobtitleLevelName,info.assessment_date
			,b.jobtitle_basic_pid AS jobtitleBasicPid
			FROM hrms_jobtitle_info info
			INNER JOIN comm_jobtitle_basic b ON info.jobtitle_level = b.jobtitle_basic_id
			WHERE info.is_deleted = 'N' AND info.highest_level=1 GROUP BY info.employee_id
		) t2 ON t1.employee_id = t2.employee_id
		WHERE t1.employee_status IN (1,6,12,9,10,11,13,99)
		AND t1.is_deleted = 'N'  AND t1.employee_no not in ('admin','ts')
		and t1.sso_org_code=#{ssoOrgCode}
		AND  ${param1} 
	 	<if test="orgIdList != null and orgIdList.size() > 0">
				AND t1.org_id IN
				<foreach collection="orgIdList" item="orgId" open="(" separator="," close=")">
					#{orgId}
				</foreach>
		</if>
		GROUP BY IF(IFNULL(t1.personal_identity,'')='',10,t1.personal_identity)
		  
	</select>
	
	<!-- 人力资源分布 end -->
	
	<!-- 身份证和手机号重复验证 -->
	<select id="getEmployeeIdentityAndPhone" parameterType="cn.trasen.hrms.model.HrmsEmployee" 
		resultType="cn.trasen.hrms.model.HrmsEmployee">
		SELECT * FROM cust_emp_base 
		where 1=1 
		and ((identity_number = #{identityNumber}) or (phone_number = #{phoneNumber}) )
	
	</select>
		<!-- 身份证和手机号重复验证 -->
	<select id="getEmployeeIdentityAndPhoneByEmp" parameterType="cn.trasen.hrms.model.HrmsEmployee" 
		resultType="cn.trasen.hrms.model.HrmsEmployee">
		SELECT * FROM cust_emp_base 
		where 1=1 
		and ((identity_number = #{identityNumber}) or (phone_number = #{phoneNumber}) )
		and employee_id != #{employeeId}
	</select>
	
	<select id="getPeriodEmployeeDetail" parameterType="string" resultType="cn.trasen.hrms.model.HrmsEmployee">
			SELECT 
				t1.employee_id, 
				t1.employee_no, 
				t1.employee_name, 
				t1.org_id, 
				t1.gender, 
				t1.birthday, 
				t1.identity_number, 
				t1.phone_number,
				i.employee_category, 
				t1.employee_status, 
				t1.avatar, 
				i.address, 
				i.residence_address, 
				t1.entry_date AS entryDate,
				i.retire_date AS retireDate, 
				i.party_date AS partyDate, 
				i.work_start_date AS workStartDate, 
				i.unit_start_date AS unitStartDate,
				t1.personal_identity AS personalIdentity, 
				i.work_nature AS workNature,
				i.first_education_type,
				i.born_address,
				i.born_address_name,
				i.job_deion_type,
				i.is_leader,
				t1.post_type,
				i.job_deion_type_time,
				i.midwife,
				i.start_employ_date,
				i.end_employ_date,
				t1.position_Id,
				i.operation_scope,
				t6.position_name AS positionName,
				t3.name AS orgName,  
				t4.post_category AS postCategory, 
				t4.post_name AS postName,
				t7.end_time,
				t7.start_time,
				t7.education_type,
				t9.jobtitle_basic_name AS employDuty,
				t10.item_Name AS operationType
			FROM cust_emp_base t1
			left join cust_emp_info i on t1.employee_id = i.info_id
			LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
			LEFT JOIN hrms_post t4 ON t4.post_id = i.post_id
			LEFT JOIN hrms_salary_level t5 ON t5.salary_level_id = i.salary_level_id
			LEFT JOIN comm_position t6 ON t6.position_id = t1.position_id
			LEFT JOIN (
				SELECT e.* FROM hrms_education_info e
				INNER JOIN (
				SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
				) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
			) t7 ON t7.employee_id = t1.employee_id AND t7.is_deleted='N'
			LEFT JOIN(
				SELECT e.* FROM hrms_jobtitle_info e
				INNER JOIN (
				SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
				) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
			)t8 ON t8.employee_id = t1.employee_id  AND t8.is_deleted='N'
			LEFT JOIN comm_jobtitle_basic t9 ON t8.jobtitle_name=t9.jobtitle_basic_id
			LEFT JOIN comm_dict_item t10 ON i.operation_type=t10.item_code AND t10.dic_type_id='operation_type' 
			WHERE t1.employee_id = #{employeeId}
	</select>

	<select id="getEntranceHospital" resultType="cn.trasen.hrms.model.HrmsEmployee" parameterType="cn.trasen.hrms.model.HrmsEmployee">
		SELECT  
			t3.name AS orgName,
			t1.position_Id,
			i.operation_scope AS operationScope,
			post.post_name,
			t10.item_name AS operationType,
			t1.entry_date,
			t11.item_name AS postCategory 
		FROM cust_emp_base t1
		left join cust_emp_info i on t1.employee_id = i.info_id
		LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
		LEFT JOIN comm_post post ON post.post_id = i.gwdj
		LEFT JOIN comm_dict_item t10 ON i.operation_type=t10.item_code AND t10.dic_type_id='operation_type' AND t10.is_deleted='N'
		LEFT JOIN comm_dict_item t11 ON i.plgw=t11.item_code AND t11.dic_type_id='post_category' AND t11.is_deleted='N'
		WHERE t1.employee_id = #{employeeId} and t1.sso_org_code=#{ssoOrgCode}

	</select>
	
	<select id="getWarningBirthday" parameterType="Map" resultType="Map">
		SELECT a.* FROM
		(
			SELECT t.employee_name,
				t.birthday as upgradeDate,
				t.employee_no,
				t.employee_id,
				t.phone_number,
				t.gender,
				t.org_id,
				t.`name` as deptName,
				CASE
			WHEN t.b >= -1 THEN t.b ELSE t.a
			END remainingDays 
			FROM
				(
					SELECT
						e.employee_name,
						e.birthday,
						e.employee_no,
						e.employee_id,
						e.phone_number,
						e.gender,
						e.org_id,
						o.name,
						DATEDIFF(CONCAT(DATE_FORMAT(NOW(), '%Y') + 1,DATE_FORMAT(e.birthday, '-%m-%d')),NOW()) a,
						DATEDIFF(CONCAT(DATE_FORMAT(NOW(), '%Y'),DATE_FORMAT(e.birthday, '-%m-%d')),NOW()) b
					FROM (select * from cust_emp_base base , cust_emp_info info where base.employee_id = info.info_id) e
				    LEFT JOIN hrms_organization o on e.org_id = o.organization_id
				    where e.is_deleted = 'N'
						and e.employee_status not in ('4','7','8')
				    <if test="compareSql != null and compareSql != ''">
			  			${compareSql}
			  		</if>
				) t
		) a
		WHERE
		a.remainingDays BETWEEN -1 AND #{advanceDays}
	</select>
	
	<select id="getWarningRetireData" parameterType="Map" resultType="Map">
		select
			e.employee_id,
			e.employee_name,
			e.employee_no,
			e.org_id,
			o.`name` as deptName,
			e.gender,
			e.phone_number,
			e.birthday,
			STR_TO_DATE(DATE_ADD(birthday,INTERVAL #{conditionsNumbers} year), '%Y-%m-%d') as upgradeDate,
			DATEDIFF(DATE_ADD(birthday,INTERVAL #{conditionsNumbers} year),now()) as remainingDays
		from (select * from cust_emp_base base , cust_emp_info info
			where base.employee_id = info.info_id) e
		LEFT JOIN hrms_organization o on e.org_id = o.organization_id
		where e.is_deleted = 'N' and  e.birthday is not null
		and e.employee_status not in ('4','7','8')
		<if test="is_expired == null or is_expired == ''">
			and DATEDIFF(DATE_ADD(birthday,INTERVAL #{conditionsNumbers} year),now()) BETWEEN -1 AND #{advanceDays}
		</if>
		<if test="is_expired != null and is_expired == '1'.toString()">
			and DATEDIFF(now(),DATE_ADD(birthday,INTERVAL #{conditionsNumbers} year)) > 0
		</if>

		<if test="compareSql != null and compareSql != ''">
  			${compareSql}
  		</if>
	</select>
	
	<select id="getWarningPartyData" parameterType="Map" resultType="Map">
		select 
			e.employee_id,
			e.employee_name,
			e.employee_no,
			e.org_id,
			o.`name` as deptName,
			e.gender,
			e.phone_number,
			e.party_date,
			DATE_ADD(party_date,INTERVAL #{conditionsNumbers} year) as upgradeDate,
			DATEDIFF( DATE_ADD(party_date,INTERVAL #{conditionsNumbers} year),now()) as remainingDays
		from (select * from cust_emp_base base , cust_emp_info info where base.employee_id = info.info_id) e
		LEFT JOIN hrms_organization o on e.org_id = o.organization_id
		where e.is_deleted = 'N'
		and e.employee_status not in ('4','7','8')
		and e.party_date is not null
		and DATEDIFF(DATE_ADD(party_date,INTERVAL #{conditionsNumbers} year),now()) BETWEEN -1 AND #{advanceDays}
		<if test="compareSql != null and compareSql != ''">
  			${compareSql}
  		</if>	
	</select>
	
	<select id="getWarningEntryData" parameterType="Map" resultType="Map">
		select 
			e.employee_id,
			e.employee_name,
			e.employee_no,
			e.org_id,
			o.`name` as deptName,
			e.gender,
			e.phone_number,
			e.entry_date,
			DATE_ADD(entry_date,INTERVAL #{conditionsNumbers} year) as upgradeDate,
			DATEDIFF(DATE_ADD(entry_date,INTERVAL #{conditionsNumbers} year),now()) as remainingDays
		from (select * from cust_emp_base base , cust_emp_info info where base.employee_id = info.info_id) e
		LEFT JOIN hrms_organization o on e.org_id = o.organization_id
		where e.is_deleted = 'N'
		and e.employee_status not in ('4','7','8')
	    and entry_date is not null
		and DATEDIFF(DATE_ADD(entry_date,INTERVAL #{conditionsNumbers} year),now()) BETWEEN -1 AND #{advanceDays}
		<if test="compareSql != null and compareSql != ''">
  			${compareSql}
  		</if>
	</select>
	
	<select id="getEducationByEmpId" resultType="cn.trasen.hrms.model.HrmsEducationInfo" parameterType="java.lang.String">
		SELECT ed.employee_id,
		ed.school_name,ed.education_type,dict.ITEM_NAME AS educationTypeText ,ed.start_time,ed.end_time,ed.professional
		FROM hrms_education_info ed
		LEFT JOIN comm_dict_item dict ON ed.education_type = dict.item_code AND dict.dic_type_id = 'education_type'
		WHERE ed.is_deleted = 'N' and ed.employee_id=#{employee_id} AND ed.highest_level=1 
		<choose>
			<when test="_databaseId=='postgresql' or _databaseId=='kingbase'">
			</when>
			<otherwise>
				GROUP BY ed.employee_id 
			</otherwise>
		</choose>
	</select>
	
	<!-- 修改员工绩效系数 -->
	<update id="updateCoefficient">
		UPDATE cust_emp_info  SET coefficient=#{coefficient}
		WHERE info_id=#{employeeId} 
	</update>
	
	<select id="getEmployeeDetailByIdentityNumber"  resultType="cn.trasen.homs.bean.base.EmployeeResp">
			
			SELECT employee_id,employee_name,employee_no,org_id, LOWER(identity_number) AS  identityNumber 
			FROM cust_emp_base 
			WHERE 1=1 
			<if test="identityNumberList != null and identityNumberList.size() > 0">
			AND LOWER(identity_number) IN
			<foreach collection="identityNumberList" item="identityNumber" open="(" separator="," close=")">
				#{identityNumber}
			</foreach>
		</if>
	</select>
	
	<select id="getRy7day" resultType="cn.trasen.homs.bean.base.EmployeeResp">
		select employee_id, employee_name,employee_no, entry_date,positive_time from cust_emp_base 
		where  TIMESTAMPDIFF(DAY,entry_date,NOW()) =7
	</select>
	
	<select id="getRy3day" resultType="cn.trasen.homs.bean.base.EmployeeResp">
		select employee_id, employee_name,employee_no, entry_date,positive_time from cust_emp_base 
		where  TIMESTAMPDIFF(DAY,entry_date,NOW()) =3
	</select>
	<select id="getRy27day" resultType="cn.trasen.homs.bean.base.EmployeeResp">
		select employee_id, employee_name,employee_no, entry_date,positive_time from cust_emp_base 
		where  TIMESTAMPDIFF(DAY,entry_date,NOW()) =27
	</select>
	
	<select id="getRy57day" resultType="cn.trasen.homs.bean.base.EmployeeResp">
		select employee_id, employee_name,employee_no, entry_date,positive_time from cust_emp_base 
		where  TIMESTAMPDIFF(DAY,entry_date,NOW()) = 57
	</select>
	
	<select id="getRysyqday" resultType="cn.trasen.homs.bean.base.EmployeeResp">
		select employee_id, employee_name,employee_no, entry_date,positive_time from cust_emp_base 
		where  TIMESTAMPDIFF(DAY,positive_time,NOW()) = -15
	</select>
	
	<update id="updateDisable" parameterType="java.lang.String">
		UPDATE cust_emp_base  SET is_enable=#{status}
		WHERE employee_id=#{id} 
	</update>
	
	<select id="getEmployeeByEmployeeNo" parameterType="java.lang.String" resultType="java.util.HashMap">
		SELECT * FROM cust_emp_base
		left join cust_emp_info on employee_id = info_id
		WHERE IS_DELETED='N'  AND employee_no=#{employeeNo}  
		ORDER BY UPDATE_DATE DESC LIMIT 1
	</select>
	
	<update id="updateAge" >
		UPDATE cust_emp_base t SET 
		emp_age=(SUBSTRING(NOW(),1,4) - SUBSTRING(identity_number,7,4)-(IF(DATE_FORMAT(NOW(),'%m%d')-SUBSTRING(identity_number,11,4)>0,0,1)))
		where t.identity_number is not null and t.identity_number != '' 
		and (LENGTH(identity_number) = 15 or LENGTH(identity_number) = 18)
	</update>
	
	<update id="updateAgeSection">
		UPDATE cust_emp_base SET generation =
			CASE WHEN emp_age &lt; 25 THEN '25岁以下' 
		     WHEN emp_age &gt;= 25 AND emp_age &lt;= 35  THEN '25-35' 
		     WHEN emp_age &gt;= 36 AND emp_age &lt;= 45 THEN '36-45' 
		     WHEN emp_age &gt;= 46 AND emp_age &lt;= 55 THEN '46-55' 
		     WHEN emp_age &lt; 55 THEN '55以上' 
		    END
		WHERE emp_age IS NOT NULL and emp_age != ''
	</update>

	<update id="updateSseniority">
			UPDATE cust_emp_base SET 
		year_work= TIMESTAMPDIFF(YEAR, entry_date, CURRENT_DATE()) 
		WHERE 1=1 and entry_date is not null and entry_date != ''
	</update>
	<select id="getPreviewInfo" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT 
			e.employee_name as '姓名',
			e.employee_no as '工号',
			o.name as '部门',
			m.item_name as '编制类型',
			t.item_name as '岗位',
			'' as '职务'
		 FROM cust_emp_base e
		left join cust_emp_info i on e.employee_id = i.info_id
		left join comm_organization o on e.org_id = o.organization_id
		left join comm_dict_item m on  m.item_code = i.establishment_type and m.DIC_TYPE_ID = 'establishment_type'
		left join comm_dict_item t on t.item_code = e.personal_identity and t.DIC_TYPE_ID = 'personal_identity'
		where e.employee_id = #{employeeId}
	</select>

    <select id="employeeListStatus" resultType="cn.trasen.hrms.model.HrmsStatusTypeVo">
		select 
			COUNT(1) as employeeCount,
			e.employee_status as employeeStatus 
		from
		 cust_emp_base e
		where e.is_deleted ='N' and e.sso_org_code= #{ssoOrgCode} and e.employee_no not in ('admin','ts')
		<if test="archivesType != null and archivesType != ''">
			and archives_type = #{archivesType}
		</if>
		GROUP BY e.employee_status
		ORDER BY e.employee_status desc
	</select>
	
	<select id="getEmployeeList" parameterType="cn.trasen.hrms.model.HrmsEmployee" resultMap="BaseResultMap">
		SELECT * FROM cust_emp_base base
		left join cust_emp_info info on base.employee_id = info.info_id
		where base.is_deleted = 'N'
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			and base.sso_org_code = #{ssoOrgCode}
		</if>
		<if test="orgId != null and orgId != ''">
			and base.org_id = #{orgId}
		</if>
		<if test="employeeNo != null and employeeNo != ''">
			and base.employee_no = #{employeeNo}
		</if>
		<if test="employeeName != null and employeeName != ''">
			and base.employee_name = #{employeeName}
		</if>
		<if test="employeeStatus != null and employeeStatus != ''">
			and base.employee_status = #{employeeStatus}
		</if>
		<if test="isEnable != null and isEnable != ''">
			and base.is_enable = #{isEnable}
		</if>
		<if test="identityNumber != null and identityNumber != ''">
			and base.identity_number = #{identityNumber}
		</if>
		<if test="phoneNumber != null and phoneNumber != ''">
			and base.phone_number = #{phoneNumber}
		</if>

	</select>
	
	<select id="getExportPhoto" parameterType="cn.trasen.hrms.model.HrmsEmployee" resultType="Map">
			SELECT
				t1.employee_no,
				t1.employee_name,
				t1.identity_number,
				t2.* 
			FROM
				cust_emp_base t1
				LEFT JOIN comm_file_attachment t2 ON SUBSTRING_INDEX( t1.avatar, '/', - 1 ) = t2.id 
				left join cust_emp_info i on t1.employee_id = i.info_id
				LEFT JOIN comm_organization t3 ON t1.org_id = t3.organization_id
				LEFT JOIN comm_post t4 ON t4.post_id = i.post_id
				LEFT JOIN comm_position t6 ON t6.position_id = t1.position_id
			<choose>
				<when test="_databaseId=='kingbase' or _databaseId=='postgresql'">
					LEFT JOIN( select e.* from (SELECT distinct employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id) t 
						INNER JOIN	 hrms_jobtitle_info e					
					     ON t.employee_id = e.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N'
					)t7 ON t7.employee_id = t1.employee_id  AND t7.is_deleted='N'
					LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
					LEFT JOIN (
						SELECT e.* FROM (SELECT distinct employee_id,MIN(highest_level) highest_level,min(degree_number) degree_number FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id) t
						INNER JOIN  hrms_education_info e ON t.employee_id = e.employee_id AND t.highest_level = e.highest_level and e.degree_number = t.degree_number WHERE e.is_deleted = 'N' 
					) t9 ON t9.employee_id = t1.employee_id AND t9.is_deleted='N'
				</when>
				<otherwise>
					LEFT JOIN(
						SELECT e.* FROM hrms_jobtitle_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_jobtitle_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
					)t7 ON t7.employee_id = t1.employee_id  AND t7.is_deleted='N'
					LEFT JOIN comm_jobtitle_basic t8 ON t8.jobtitle_basic_id = t7.jobtitle_name
					LEFT JOIN (
						SELECT e.* FROM hrms_education_info e
						INNER JOIN (
						SELECT employee_id,MIN(highest_level) highest_level FROM hrms_education_info WHERE is_deleted = 'N' GROUP BY employee_id
						) t ON e.employee_id = t.employee_id AND e.highest_level = t.highest_level WHERE e.is_deleted = 'N' GROUP BY e.employee_id
					) t9 ON t9.employee_id = t1.employee_id AND t9.is_deleted='N'
				</otherwise>
			</choose>
			LEFT JOIN comm_jobtitle_basic t10 ON  t8.jobtitle_basic_pid = t10.jobtitle_basic_id
			LEFT JOIN comm_position t20 ON t20.position_id = i.concurrent_position
			<if test='queryStatus =="14"'>
				LEFT JOIN (
				SELECT employee_id,establishment_type FROM hrms_personnel_incident WHERE incident_category='2' AND is_deleted='N' AND approval_status='4'
				) tte ON t1.employee_id = tte.employee_id
			</if>
			WHERE
				t1.avatar IS NOT NULL 
				AND t1.avatar != '' 
				AND t1.is_deleted = 'N'
				and t1.employee_no not in ('admin','ts')
				and t1.sso_org_code=#{ssoOrgCode}
				<if test="archivesType != null and archivesType != '' and archivesType!='null'.toString()">
					and t1.archives_type = #{archivesType}
				</if>
				<!-- 员工状态 -->
				<if test="multiEmployeeStatus != null and multiEmployeeStatus != ''">
					 AND t1.employee_status in (${multiEmployeeStatus}) 
				</if>
				<!-- 编制类别 -->
				<if test="establishmentType != null and establishmentType != ''">
					<if test='queryStatus =="14"'>
						AND tte.establishment_type = #{establishmentType}
					</if>
		
					<if test='queryStatus !="14"' >
						AND i.establishment_type = #{establishmentType}
					</if>
				</if>
				<!-- 员工状态 -->
				<if test="employeeStatus != null and employeeStatus !='' and queryStatus !='8888'">
					AND t1.employee_status = #{employeeStatus}
				</if>
				<if test='queryStatus =="8888"'>
					and (t1.employee_status IS NULL OR t1.employee_status = '')
				</if>
				<if test="identityNumber!=null and identityNumber!=''">
		  			and t1.identity_number like concat('',#{identityNumber},'%')
		  		</if>
		  		
		  		<if test="empPayroll!=null and empPayroll!=''">
			  		and t1.emp_payroll like concat('',#{empPayroll},'%')
			  	</if>
				
				<!-- 中层领导-->
				<if test="midwife != null and midwife != ''">
					and  i.shifouzhongcengganbu = #{midwife}
				</if>
				<!-- 中医骨干-->
				<if test="doctorQualificationCertificate != null and doctorQualificationCertificate != ''">
					and  i.shifouxinglinrencai = #{doctorQualificationCertificate}
				</if>
		
				<if test="employeeName != null and employeeName != ''">
					AND ((t1.employee_no LIKE CONCAT('%',#{employeeName},'%') )
					or (t1.employee_name LIKE CONCAT('%',#{employeeName},'%') )
					or (t1.name_spell LIKE CONCAT('%',#{employeeName},'%') ))
				</if>
				
				<if test="employeeStatuses != null and employeeStatuses.size() > 0">
				and (t1.employee_status in
					<foreach collection="employeeStatuses" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
		        )
				</if>
		  	
		  	
		  		<if test="establishmentTypes != null and establishmentTypes.size() > 0">
					<if test='queryStatus =="14"'>
						and (tte.establishment_type in
						<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
							#{item}
						</foreach>
						)
					</if>
					<if test='queryStatus !="14"'>
						and (i.establishment_type in
						<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
							#{item}
						</foreach>
						)
					</if>
				</if>
			
				<if test="employeeCategorys != null and employeeCategorys.size() > 0">
					and (i.employee_category in
					<foreach collection="employeeCategorys" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
				
				<if test="politicalStatuses != null and politicalStatuses.size() > 0">
					and (i.political_status in
					<foreach collection="politicalStatuses" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
				
				<if test="nationalityes != null and nationalityes.size() > 0">
					and (i.nationality in
					<foreach collection="nationalityes" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
			
			
				<if test="marriageStatuses != null and marriageStatuses.size() > 0">
					and (i.marriage_status in
					<foreach collection="marriageStatuses" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
			
			
				<if test="positionNames != null and positionNames.size() > 0">
					and (t1.position_id in
					<foreach collection="positionNames" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
				
				<if test="personalIdentitys != null and personalIdentitys.size() > 0">
					and (t1.personal_identity in
					<foreach collection="personalIdentitys" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
			
				<if test="plgws != null and plgws.size() > 0">
					and (i.plgw in
					<foreach collection="plgws" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
			
				<if test="operationTypes != null and operationTypes.size() > 0">
					and (i.operation_type in
					<foreach collection="operationTypes" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
			
				<if test="educationTypes != null and educationTypes.size() > 0">
					and (t9.education_type in
					<foreach collection="educationTypes" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
				
				<if test="jobtitleNames != null and jobtitleNames.size() > 0">
					and (t7.jobtitle_name in
					<foreach collection="jobtitleNames" index="index" item="item" open="(" separator="," close=")">
			            #{item}
			        </foreach>
			        )
				</if>
				
				<if test="birthdayStartTime!=null and birthdayStartTime!=''">
			  		and t1.birthday >=  #{birthdayStartTime}
			  	</if>
		  	
			  	<if test="birthdayEndTime!=null and birthdayEndTime!=''">
			  		and #{birthdayEndTime} >=t1.birthday
			  	
			  	</if>
			  	
			  	<if test="shifouguipeirenyuan!=null and shifouguipeirenyuan!=''">
			  		and i.shifouguipeirenyuan = #{shifouguipeirenyuan}
			  	</if>

				<if test="zhuanyeyingcai!=null and zhuanyeyingcai!=''">
					and i.zhuanyeyingcai = #{zhuanyeyingcai}
				</if>
	</select>


	<!-- 获取薪酬方案可选员工列表 -->
	<select id="searchEmployee2OptionDatas" resultType="cn.trasen.hrms.salary.DTO.CheckPersonnel" parameterType="cn.trasen.hrms.salary.DTO.SearchListTable">
		SELECT t2.employee_no,t2.employee_name,t2.employee_id,
		t2.establishment_type,
		t2.personal_identity,
		t2.employee_status,
		t2.entry_date,
		t2.positive_time,
		t2.retirement_time,
		t3.name as orgName,
		t4.id as optionId,
		t4.option_name as optionName,
		t2.is_temp,t2.plgw,t2.gwdj,t2.salary_level_id as salaryLevel
		FROM (
		SELECT
		employee_name,employee_no,org_id,employee_id,gender,birthday,
		employee_status,positive_time,retirement_time,
		position_id,year_work,bankcardname,emp_age,
		establishment_type,salary_appoint,is_deleted,
		personal_identity,identity_number,bankcardno,
		entry_date,'N' as is_temp,plgw,gwdj,salary_level_id
		FROM cust_emp_base base,cust_emp_info inf where base.employee_id = inf.info_id
		UNION ALL
		SELECT
		employee_name,employee_no,org_id,id as employee_id,gender,birthday,
		(case when tmp_employee_status = '1' then '1' when tmp_employee_status = '2' then '4' else '' end) as employee_status,null as positive_time,null as retirement_time,
		null as position_id,null as year_work,null as bankcardname,null as emp_age,
		tmp_establishment as establishment_type,(select 1 from hrms_newsalary_basicitem_emp where employee_id = et.id group by employee_id) as salary_appoint,is_deleted,
		tmp_position as personal_identity,identity_number,bank_card_number as bankcardno,
		join_date as entry_date,'Y' as is_temp,
		null as plgw,null as gwdj,null as salary_level_id
		FROM  hrms_employee_temporary et where et.is_sync_salary = 'Y'
		) t2
		LEFT JOIN hrms_newsalary_option_emp t1 ON t1.employee_id = t2.employee_id and t1.is_deleted = 'N'
		LEFT JOIN comm_organization t3 ON t2.org_id = t3.organization_id
		LEFT JOIN hrms_newsalary_option t4 on t1.option_id = t4.id and t4.is_deleted = 'N'
		WHERE t2.is_deleted='N' and t2.employee_status in ('1','5','6','8','9','11','12','88','99')
		<if test="optionId != null and optionId !=''">
			AND t1.option_id=#{optionId}
		</if>
		<if test="gwdj != null and gwdj !=''">
			AND t2.gwdj=#{gwdj}
		</if>
		<if test="salaryLevelId != null and salaryLevelId != ''">
			and t2.salary_level_id = #{salaryLevelId}
		</if>
		<if test="employeeName != null and employeeName !=''">
			and ( t2.employee_name like CONCAT('%',#{employeeName},'%') or
			t2.employee_no like CONCAT('%',#{employeeName},'%') )
		</if>
		<if test="orgList != null and orgList.size() > 0">
			and (t2.org_id in
			<foreach collection="orgList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
		<if test="orgId != null and orgId!=''">
			and t2.org_id = #{orgId}
		</if>
		<if test='employeeStatus != null and employeeStatus.size() > 0'>
			and t2.employee_status in
			<foreach collection="employeeStatus" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test='establishmentTypes != null and establishmentTypes.size() > 0'>
			and t2.establishment_type in
			<foreach collection="establishmentTypes" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="isTemp != null and isTemp != ''">
			and t2.is_temp = #{isTemp}
		</if>
		<if test="orgName != null and orgName !=''">
			and  t3.name like CONCAT('%',#{orgName},'%')
		</if>
		order by COALESCE(CAST(employee_no AS UNSIGNED),9999)
	</select>

	<!-- 获取省市区数据 -->
	<select id="getAllCityData" resultType="map">
			select id,name from comm_city where is_deleted = 'N'
	</select>
</mapper>