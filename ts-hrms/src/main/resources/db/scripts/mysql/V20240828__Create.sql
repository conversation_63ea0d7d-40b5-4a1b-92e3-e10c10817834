CREATE TABLE IF NOT EXISTS `hrms_advancement_incident`  (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `employee_no` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `employee_name` varchar(50) DEFAULT NULL COMMENT '员工名字',
  `employee_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `old_plgw` varchar(36) DEFAULT NULL COMMENT '旧的岗位类别',
  `new_plgw` varchar(36) DEFAULT NULL COMMENT '新的岗位类别',
  `old_gwdj` varchar(36) DEFAULT NULL COMMENT '旧的岗位等级',
  `new_gwdj` varchar(36) DEFAULT NULL COMMENT '新的岗位等级',
  `new_salary_level_id` varchar(36) DEFAULT NULL COMMENT '旧的薪级等级',
  `old_salary_level_id` varchar(36) DEFAULT NULL COMMENT '新的薪级等级',
  `new_salary_level_type` varchar(36) DEFAULT NULL COMMENT '新的薪级类别',
  `old_salary_level_type` varchar(36) DEFAULT NULL COMMENT '旧的薪级类别',
  `effective_date` varchar(30) DEFAULT NULL COMMENT '生效日期',
  `reason` varchar(300) DEFAULT NULL COMMENT '变动原因',
  `approval_status` tinyint(1) DEFAULT NULL COMMENT '审批状态: 1=未审批; 2=审批中; 3=已退回; 4=已审批',
  `source_type` tinyint(1) DEFAULT NULL COMMENT '0.档案修改 1.晋升修改 2.薪酬修改',
  `stop_event` tinyint(1) DEFAULT NULL COMMENT '1：已停止',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者IaD',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(50) DEFAULT NULL,
  `sso_org_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `IDX_EMPID` (`employee_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='岗位晋级事件';


CREATE TABLE IF NOT EXISTS `hrms_newsalary_changes_detailed`  (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `employee_no` varchar(50) DEFAULT NULL COMMENT '员工工号',
  `employee_name` varchar(50) DEFAULT NULL COMMENT '员工名字',
  `employee_id` varchar(50) DEFAULT NULL COMMENT '员工id',
  `adjust_value` varchar(50) DEFAULT NULL COMMENT '调整值',
  `reason` varchar(300) DEFAULT NULL COMMENT '调薪原因',
  `now_value` varchar(50) DEFAULT NULL COMMENT '现有值',
  `abnormal_items` varchar(50) DEFAULT NULL COMMENT '异动项',
  `salary_category` tinyint(1) DEFAULT NULL COMMENT '薪酬类别(1.岗位类别2.薪级类别3.薪酬项目4.员工状态)',
  `establishment_type` varchar(50) DEFAULT NULL COMMENT '编制类型',
  `personal_identity` varchar(50) DEFAULT NULL COMMENT '岗位名称',
  `source_type` tinyint(1) DEFAULT NULL COMMENT '0.档案修改 1.晋升修改 2.薪酬修改',
  `effective_date` varchar(50) DEFAULT NULL COMMENT '生效日期',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT NULL COMMENT '删除标识: Y=是; N=否;',
  `sso_org_name` varchar(50) DEFAULT NULL,
  `sso_org_code` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `EMP_IDX` (`employee_id`) COMMENT '员工id',
  KEY `EMP_NO` (`employee_no`) COMMENT '员工编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='异动明细表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_advancement_incident' and COLUMN_NAME = 'job_deion_type_time' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_advancement_incident` ADD `job_deion_type_time` datetime DEFAULT NULL COMMENT ''任职时间'' ',
                   'select ''INFO: job_deion_type_time 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_newsalary_payroll_detail_upload' and COLUMN_NAME = 'payroll_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_newsalary_payroll_detail_upload` ADD `payroll_date` varchar(36) DEFAULT NULL COMMENT ''上报月份'' ',
                   'select ''INFO: payroll_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
