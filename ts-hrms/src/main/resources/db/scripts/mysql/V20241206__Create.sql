/**
添加薪酬核算详情和核算页面隐藏字段
 */
set @exist := (select count(1) from comm_dict_type where TYPE_CODE ='salary_table_column_hidden');
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_type` (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `IS_DELETED`)
	VALUES (''salary_table_column_hidden'', ''salary_table_column_hidden'', ''薪酬核算列表隐藏列名'', ''薪酬核算列表隐藏列名'', ''HRMS'', ''admin'', ''admin'', now(), ''N'');',
                   'select ''INFO: salary_table_column_hidden字典类型数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846657678481555456'', ''salary_table_column_hidden'', ''personal_identity_text'', ''岗位'', NULL, NULL, ''8'', ''HRMS'', ''admin'', ''admin'', now(), ''admin'', ''admin'', now(), ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''personal_identity_text'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846663768107646976'', ''salary_table_column_hidden'', ''establishment_type_text'', ''编制类型'', NULL, NULL, ''9'', ''HRMS'', ''admin'', ''admin'', now(), ''admin'', ''admin'', now(), ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''establishment_type_text'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846663813502599168'', ''salary_table_column_hidden'', ''bankcardNo'', ''银行卡号'', NULL, NULL, ''10'', ''HRMS'', ''admin'', ''admin'', now(), ''admin'', ''admin'', now(), ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''bankcardNo'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667311963176960'', ''salary_table_column_hidden'', ''employee_no'', ''工号'', NULL, NULL, ''1'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''employee_no'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667364668801024'', ''salary_table_column_hidden'', ''employee_name'', ''姓名'', NULL, NULL, ''2'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''employee_name'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667408209870848'', ''salary_table_column_hidden'', ''orgName'', ''部门'', NULL, NULL, ''3'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''orgName'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667455110578176'', ''salary_table_column_hidden'', ''employee_status_text'', ''员工状态'', NULL, NULL, ''4'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''employee_status_text'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667539877462016'', ''salary_table_column_hidden'', ''option_name'', ''薪酬组'', NULL, NULL, ''5'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''option_name'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
 VALUES (''hid_col_846667587927408640'', ''salary_table_column_hidden'', ''send_status_text'', ''发送状态'', NULL, NULL, ''6'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''send_status_text'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667637147566080'', ''salary_table_column_hidden'', ''is_view_text'', ''查看状态'', NULL, NULL, ''7'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''is_view_text'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667818429579264'', ''salary_table_column_hidden'', ''update_user_name'', ''处理人'', NULL, NULL, ''11'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''update_user_name'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`)
VALUES (''hid_col_846667860364230656'', ''salary_table_column_hidden'', ''update_date'', ''最后处理日期'', NULL, NULL, ''12'', ''HRMS'', ''admin'', ''admin'', now(), NULL, NULL, NULL, ''N'', NULL, NULL, ''******************'', ''北海市第二人民医院'', ''update_date'', ''0'', ''2'', NULL, NULL);',
                   'select ''INFO: salary_table_column_hidden字典项数据已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
薪酬方案人员排序
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_newsalary_option_emp' and COLUMN_NAME in ('sort_num') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_newsalary_option_emp` ADD COLUMN `sort_num`  integer(5) NULL DEFAULT 999 COMMENT ''薪酬方案人员排序'' AFTER `option_id`;',
                   'select ''INFO: sort_num 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
薪酬发放记录表
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_newsalary_payroll' and COLUMN_NAME in ('sort_num') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_newsalary_payroll` ADD COLUMN `sort_num`  integer(5) NULL DEFAULT 999 COMMENT ''薪酬方案人员排序'' AFTER `send_method`;',
                   'select ''INFO: sort_num 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
销假上报表
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_cancel_leave_report' and COLUMN_NAME in ('leave_workflow_id') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_cancel_leave_report` ADD COLUMN `leave_workflow_id`  varchar(50) NULL DEFAULT NULL COMMENT ''请假流程实例id'' AFTER `workflow_id`;',
                   'select ''INFO: leave_workflow_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;



/**
薪酬方案薪酬项目表（工资条）
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_newsalary_item' and COLUMN_NAME in ('is_total','total_title_name','total_sort_no') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_newsalary_item`
ADD COLUMN `is_total`  char(1) NULL COMMENT ''是否合计 1-是'' AFTER `is_enable`,
ADD COLUMN `total_title_name`  varchar(50) NULL COMMENT ''合计标题名称'' AFTER `is_total`,
ADD COLUMN `total_sort_no`  int(11) NULL DEFAULT 999 COMMENT ''汇总合计项排序'' AFTER `total_title_name`;',
                   'select ''INFO: is_total, total_title_name,total_sort_no 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
员工调动表
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_personnel_change' and COLUMN_NAME in ('file_id') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_personnel_change`
ADD COLUMN `file_id`  varchar(2000) NULL COMMENT ''附件id'' AFTER `is_deleted`',
                   'select ''INFO: file_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
更新历史数据
 */
update hrms_personnel_change c,comm_file_attachment a
set c.file_id = a.business_id
where c.personnel_change_id = a.business_id and a.is_deleted = 'N';

/**
薪酬项目提醒表字段长度调整
 */
ALTER TABLE `hrms_newsalary_item_remind_setting`
MODIFY COLUMN `back_payment_date`  varchar(50)  NULL DEFAULT NULL COMMENT '补缴开始月份' AFTER `is_back_payment`;

/**
薪酬项目提醒记录字段长度调整
 */
ALTER TABLE `hrms_newsalary_item_remind_record`
MODIFY COLUMN `back_payment_date`  varchar(50)  NULL DEFAULT NULL COMMENT '补缴开始月份' AFTER `is_back_payment`;
ALTER TABLE `hrms_newsalary_item_remind_record`
MODIFY COLUMN `remark`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注' AFTER `handle_status`;

/**
2025-01-20
 */
/**
值班数据表添加值班排序日期
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_duty_roster_report' and COLUMN_NAME in ('duty_sort_date') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `hrms_duty_roster_report`
ADD COLUMN `duty_sort_date`  varchar(50) NULL COMMENT ''值班排序日期'' AFTER `report_date`;',
                   'select ''INFO: duty_sort_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
值班报表，添加值班排序员工
 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_duty_roster_report' and COLUMN_NAME in ('duty_sort_employee_no') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_duty_roster_report`
ADD COLUMN `duty_sort_employee_no` varchar(50) NULL COMMENT ''值班排序工号'' AFTER `report_date`;',
                   'select ''INFO: duty_sort_employee_no 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
/**
值班数据表历史值班排序字段数据设置
 */
update hrms_duty_roster_report set duty_sort_date = report_date where duty_sort_date is null and report_date is not null;

/**
值班管理操作日志
 */
CREATE TABLE IF NOT EXISTS  `hrms_duty_roster_operate_log` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
	`roster_group_id` varchar(50) DEFAULT NULL COMMENT '分组id',
	`op_module` varchar(50) DEFAULT NULL COMMENT '操作模块名称',
  `op_type` char(2) DEFAULT NULL COMMENT '操作类型:1-新增分组 2-编辑分组 3-删除分组 4-移除分组成员 5-编辑分组成员 6-自动值班 7-手动值班',
  `op_before_value` varchar(2000) DEFAULT NULL COMMENT '操作前数据',
  `op_after_value` varchar(2000) DEFAULT NULL COMMENT '操作后数据',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(30) DEFAULT NULL COMMENT '创建者ID',
  `create_user_name` varchar(30) DEFAULT NULL COMMENT '创建者姓名',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user` varchar(30) DEFAULT NULL COMMENT '更新者ID',
  `update_user_name` varchar(30) DEFAULT NULL COMMENT '更新者姓名',
  `is_deleted` char(1) DEFAULT 'N' COMMENT '删除标识: Y=是; N=否;',
  `sso_org_code` varchar(50) DEFAULT NULL,
  `sso_org_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_groupId` (`roster_group_id`) USING BTREE,
  KEY `idx_opmodule` (`op_module`),
  KEY `idx_optype` (`op_type`)
) ENGINE=InnoDB CHARACTER SET = utf8mb4 COMMENT='值班管理操作日志';
/**
2025-01-22--年假设置
 */
/**
年假设置
 */
CREATE TABLE IF NOT EXISTS `hrms_annual_leave_setting`  (
  `id` varchar(50) NOT NULL COMMENT '主键id',
	`year` varchar(10) NULL DEFAULT NULL COMMENT '年份',
	`is_putoff_next_year` char(1) NULL DEFAULT '0' COMMENT '是否延用到次年 0-否 1-是',
	`next_year_cutoff_time` varchar(50) NULL DEFAULT NULL COMMENT '次年截止时间(月-日)',
	`pre_use_next_annual_leave` char(1) NULL DEFAULT '0' COMMENT '是否允许提前请次年年假 0-否 1-是',
	`is_rule_to_next_year` char(1) NULL DEFAULT '0' COMMENT '规则是否延用到次年 0-否 1-是',
	`is_lapsed_leave_convert_wage` char(1) NULL DEFAULT '0' COMMENT '失效年假是否允许折算工资 0-否 1-是',
  `is_converted_by_date` char(1) NULL DEFAULT '0' COMMENT '离职或退休人员是否按日期折算年假 0-否 1-是',
  `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(36) NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(30) NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(36) NULL DEFAULT NULL COMMENT '修改人',
  `update_user_name` varchar(30) NULL DEFAULT NULL COMMENT '修改人名称',
  `is_deleted` char(1) NULL DEFAULT NULL COMMENT '删除标识',
  `sso_org_code` varchar(36) NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(36) NULL DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '年假设置';

/**
年假计算规则
 */
CREATE TABLE IF NOT EXISTS `hrms_annual_leave_days_rule`  (
  `id` varchar(50) NOT NULL COMMENT '主键id',
	`year` varchar(10) NULL DEFAULT NULL COMMENT '年份',
	`start_year_limit` int(4) NULL DEFAULT NULL COMMENT '开始年限',
	`end_year_limit` int(4) NULL DEFAULT NULL COMMENT '结束年限',
	`total_sick_leave_days` int(4) NULL DEFAULT NULL COMMENT '病假累计天数',
	`total_personal_leave_days` int(4) NULL DEFAULT NULL COMMENT '事假累计天数',
	`annual_leave_days` int(4) NULL DEFAULT 0 COMMENT '年假天数',
  `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(36) NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(30) NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(36) NULL DEFAULT NULL COMMENT '修改人',
  `update_user_name` varchar(30) NULL DEFAULT NULL COMMENT '修改人名称',
  `is_deleted` char(1) NULL DEFAULT NULL COMMENT '删除标识',
  `sso_org_code` varchar(36) NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(36) NULL DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '年假计算规则';

/**
年假折算工资规则表
 */
CREATE TABLE IF NOT EXISTS `hrms_annual_leave_wage_rules`  (
  `id` varchar(50) NOT NULL COMMENT '主键id',
	`year` varchar(10) NULL DEFAULT NULL COMMENT '年份',
	`jobtitle` varchar(50) NULL DEFAULT NULL COMMENT '职称等级',
	`wage_amount` decimal(18,2) NULL DEFAULT NULL COMMENT '工资金额（元/天）',
  `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(36) NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(30) NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(36) NULL DEFAULT NULL COMMENT '修改人',
  `update_user_name` varchar(30) NULL DEFAULT NULL COMMENT '修改人名称',
  `is_deleted` char(1) NULL DEFAULT NULL COMMENT '删除标识',
  `sso_org_code` varchar(36) NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(36) NULL DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '年假折算工资规则表';


/**
年假设置操作记录
 */
CREATE TABLE IF NOT EXISTS `hrms_annual_leave_operation_record`  (
  `id` varchar(50) NOT NULL COMMENT '主键id',
	`year` varchar(10) NULL DEFAULT NULL COMMENT '年份',
	`oper_type` char(1) NULL DEFAULT NULL COMMENT '操作类型 1-新增、2-修改、3-删除',
  `oper_function` char(1) NULL DEFAULT NULL COMMENT '操作功能 1-年假设置 2-年假计算规则，3-折算工资规则',
	`before_content` varchar(1000) NULL DEFAULT '0' COMMENT '操作前内容',
	`after_content` varchar(1000) NULL DEFAULT '0' COMMENT '操作后内容',
  `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(36) NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(30) NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(36) NULL DEFAULT NULL COMMENT '修改人',
  `update_user_name` varchar(30) NULL DEFAULT NULL COMMENT '修改人名称',
  `is_deleted` char(1) NULL DEFAULT NULL COMMENT '删除标识',
  `sso_org_code` varchar(36) NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(36) NULL DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '年假设置操作记录';

/**
员工年假表
 */
CREATE TABLE IF NOT EXISTS `hrms_annual_leave_emp`  (
  `id` varchar(50) NOT NULL COMMENT '主键id',
	`year` varchar(10) NULL DEFAULT NULL COMMENT '年份',
	`employee_id` varchar(50) NULL DEFAULT NULL COMMENT '员工id',
	`available_annual_leave_days`  int(4) NULL DEFAULT '0' COMMENT '可休年假',
	`remaining_annual_leave_days`  int(4) NULL DEFAULT '0' COMMENT '上年结余可用年假',
	`use_annual_leave_days` int(4) NULL DEFAULT '0' COMMENT '已休年假',
	`lapsed_annual_leave_days` int(4) NULL DEFAULT '0' COMMENT '已失效年假',
	`is_convert_wage` char(1) NULL DEFAULT '0' COMMENT '失效年假是否已折现 0-否 1-是',
	`is_update` char(1) NULL DEFAULT '0' COMMENT '是否已修改 0-否 1-是',
  `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(36) NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(30) NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(36) NULL DEFAULT NULL COMMENT '修改人',
  `update_user_name` varchar(30) NULL DEFAULT NULL COMMENT '修改人名称',
  `is_deleted` char(1) NULL DEFAULT NULL COMMENT '删除标识',
  `sso_org_code` varchar(36) NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(36) NULL DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '员工年假表';


/**
员工年假表历史表
 */
CREATE TABLE IF NOT EXISTS `hrms_annual_leave_emp_history`  (
  `id` varchar(50) NOT NULL COMMENT '主键id',
	`year` varchar(10) NULL DEFAULT NULL COMMENT '年份',
	`employee_id` varchar(50) NULL DEFAULT NULL COMMENT '员工id',
	`available_annual_leave_days`  int(4) NULL DEFAULT '0' COMMENT '可休年假',
	`remaining_annual_leave_days`  int(4) NULL DEFAULT '0' COMMENT '上年结余可用年假',
	`use_annual_leave_days` int(4) NULL DEFAULT '0' COMMENT '已休年假',
	`lapsed_annual_leave_days` int(4) NULL DEFAULT '0' COMMENT '已失效年假',
	`is_convert_wage` char(1) NULL DEFAULT '0' COMMENT '失效年假是否已折现 0-否 1-是',
	`is_update` char(1) NULL DEFAULT '0' COMMENT '是否已修改 0-否 1-是',
  `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `create_user` varchar(36) NULL DEFAULT NULL COMMENT '创建人',
  `create_user_name` varchar(30) NULL DEFAULT NULL COMMENT '创建人名称',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `update_user` varchar(36) NULL DEFAULT NULL COMMENT '修改人',
  `update_user_name` varchar(30) NULL DEFAULT NULL COMMENT '修改人名称',
  `is_deleted` char(1) NULL DEFAULT NULL COMMENT '删除标识',
  `sso_org_code` varchar(36) NULL DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` varchar(36) NULL DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '员工年假表历史表';

set @exist := (select count(1) from comm_dict_item where DIC_TYPE_ID = 'tmp_adjust_item' and ITEM_CODE = 'njzx' and IS_DELETED = 'N');
set @sqlstmt := if(@exist= 0, 'INSERT INTO `ts_base_oa`.`comm_dict_item` (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`,
 `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`,
 `is_enable`, `sso_org_code`, `sso_org_name`)
 VALUES (''828840733602521_njzx'', ''tmp_adjust_item'', ''njzx'', ''年假折现'', NULL, NULL, 5, ''HRMS'', ''admin'', ''admin'', ''2025-02-08 11:50:19'', NULL, NULL, NULL, ''N'', NULL,
  NULL, NULL, NULL, ''njzx'', 0, ''1'', NULL, NULL);',
                   'select ''INFO: 年假折现 薪酬调整字典项已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


/** 员工年假表 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_annual_leave_emp' and COLUMN_NAME in ('deduct_annual_leave_days') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_annual_leave_emp`
ADD COLUMN `deduct_annual_leave_days` int(4) NULL  DEFAULT ''0'' COMMENT ''扣除年假天数'' AFTER `is_update`;',
                   'select ''INFO: deduct_annual_leave_days 扣除年假天数.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/** 员工年假历史表 */
set @exist := (select count(1) from information_schema.columns where table_name = 'hrms_annual_leave_emp_history' and COLUMN_NAME in ('deduct_annual_leave_days') and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_annual_leave_emp_history`
ADD COLUMN `deduct_annual_leave_days` int(4) NULL  DEFAULT ''0'' COMMENT ''扣除年假天数'' AFTER `is_update`;',
                   'select ''INFO: deduct_annual_leave_days 扣除年假天数.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

/**
员工档案-个人信息分组 手动添加 工龄开始时间（字段编码：glkssj） 字段
 */
 /**
档案属性
*/
INSERT ignore INTO `ts_base_oa`.`cust_emp_field` (`id`, `group_id`, `field_class`, `show_name`, `field_name`, `field_type`, `field_length`,
 `prompt_text`, `is_only`, `is_must`, `is_disabled`, `is_remove_duplicate`, `data_format`, `treatment_method`, `data_source`, `dict_source`,
 `option_value`, `show_pass_by_val`, `show_pass_by_date`, `seq`, `field_show`, `field_sort`, `field_width`, `field_align`, `field_sortable`,
 `field_index`, `field_fixed`, `field_export`, `is_salary`, `calculation_role`, `serial_number_rule`, `is_multiple`, `is_allow_deleted`, `is_hide`,
 `decimal_digit`, `sso_org_code`, `create_date`, `create_user`, `create_user_name`, `update_user`, `update_user_name`, `update_date`, `is_deleted`)
 VALUES ('5FA5DC320C854E3681E66CB22718glkssj', '1', '2', '工龄开始时间', 'glkssj', 'date', 50, '', 0, 0, 0, 0, 'yyyy-MM-dd', NULL, 1,
  NULL, '', NULL, NULL, 45, 0, 999, 120, 'left', NULL, NULL, 0, NULL, 0, '', '', 0, 1, 0, NULL, 'bhsdermyy', '2025-02-10 07:28:32',
   NULL, NULL, 'admin', 'admin', '2025-02-10 17:44:46', 'N');

/**
员工档案
*/
set @exist := (select count(1) from information_schema.columns
               where table_name = 'cust_emp_info' and COLUMN_NAME = 'glkssj' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`cust_emp_info` ADD `glkssj` varchar(36) DEFAULT NULL COMMENT ''工龄开始时间'' ',
                   'select ''INFO: archives_type 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
