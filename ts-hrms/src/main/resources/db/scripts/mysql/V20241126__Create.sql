/**
医疗专项项目
 */
CREATE TABLE IF NOT EXISTS `med_sped_item` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `item_code` VARCHAR(50) DEFAULT NULL COMMENT '项目编号',
  `item_name` VARCHAR(100) DEFAULT NULL COMMENT '项目名称',
  `item_tagt` VARCHAR(2000) DEFAULT NULL COMMENT '项目目标',
  `item_context` VARCHAR(2000) DEFAULT NULL COMMENT '项目背景',
  `item_resper` VARCHAR(20) DEFAULT NULL COMMENT '项目负责人',
  `item_resper_name` VARCHAR(20) DEFAULT NULL COMMENT '项目负责人名称',
  `resper_org` VARCHAR(20) DEFAULT NULL COMMENT '负责人科室',
  `resper_org_name` VARCHAR(50) DEFAULT NULL COMMENT '负责人科室名称',
  `capt_souc` VARCHAR(2000) DEFAULT NULL COMMENT '资金来源',
  `budg_amt` decimal(12,2) DEFAULT NULL COMMENT '预算总金额',
  `impe_org_name` VARCHAR(200) DEFAULT NULL COMMENT '实施单位',
  `start_date` datetime DEFAULT NULL COMMENT '开始日期',
  `end_date` datetime DEFAULT NULL COMMENT '结束日期',
  `eval_kpi` VARCHAR(2000) DEFAULT NULL COMMENT '评估指标',
  `ethic_appr_no` VARCHAR(50) DEFAULT NULL COMMENT '伦理审批号',
  `ethic_council_name` VARCHAR(200) DEFAULT NULL COMMENT '伦理委员会名称',
  `coop_org_name` VARCHAR(200) DEFAULT NULL COMMENT '合作单位',
  `file_key` VARCHAR(50) DEFAULT NULL COMMENT '附件KEY',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医疗专项项目';
/**
医务大事件
 */
CREATE TABLE IF NOT EXISTS `med_major_events` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `events_date` datetime DEFAULT NULL COMMENT '事件日期',
  `events_type` VARCHAR(20) DEFAULT NULL COMMENT '事件类型',
  `events_name` VARCHAR(100) DEFAULT NULL COMMENT '事件名称',
  `events_dscr` VARCHAR(2000) DEFAULT NULL COMMENT '事件描述',
  `events_infl` VARCHAR(2000) DEFAULT NULL COMMENT '事件影响',
  `dspo_mes` VARCHAR(2000) DEFAULT NULL COMMENT '处理措施',
  `srvy_ana` VARCHAR(2000) DEFAULT NULL COMMENT '调查与分析',
  `impr_adv` VARCHAR(2000) DEFAULT NULL COMMENT '改进与建议',
  `approval_status` char(1) DEFAULT '0' COMMENT '审批状态',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approval_user` VARCHAR(50) DEFAULT NULL COMMENT '审批人账号',
  `approval_user_name` VARCHAR(50) DEFAULT NULL COMMENT '审批人姓名',
  `approval_dscr` VARCHAR(1000) DEFAULT NULL COMMENT '审批意见',
  `file_key` VARCHAR(50) DEFAULT NULL COMMENT '附件KEY',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医务大事件';
/**
医务大事件-相关人员
 */
CREATE TABLE IF NOT EXISTS `med_major_events_emp` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `events_id` VARCHAR(50) NOT NULL COMMENT '医务大事记ID',
  `employee_no` VARCHAR(30) DEFAULT NULL COMMENT '工号',
  `employee_name` VARCHAR(50) DEFAULT NULL COMMENT '姓名',
  `gender` char(1) DEFAULT NULL COMMENT '性别',
  `personal_identity` VARCHAR(50) DEFAULT NULL COMMENT '岗位',
  `org_id` VARCHAR(36) DEFAULT NULL COMMENT '组织机构',
  `remark` VARCHAR(50) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医务大事件-相关人员';
/**
医务大事件-相关患者
 */
CREATE TABLE IF NOT EXISTS `med_major_events_patn` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `events_id` VARCHAR(50) NOT NULL COMMENT '医务大事记ID',
  `inp_no` VARCHAR(50) DEFAULT NULL COMMENT '住院号',
  `bedno` VARCHAR(50) DEFAULT NULL COMMENT '床号',
  `patn_name` VARCHAR(50) DEFAULT NULL COMMENT '患者姓名',
  `patn_gend` VARCHAR(50) DEFAULT NULL COMMENT '患者性别',
  `dept_name` VARCHAR(100) DEFAULT NULL COMMENT '所属科室',
  `remark` VARCHAR(50) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医务大事件-相关患者';
/**
会诊申请单
 */
CREATE TABLE IF NOT EXISTS `med_cslt_appy` (
  `id` VARCHAR(50) NOT NULL COMMENT '申请单ID',
  `appy_no` VARCHAR(50) DEFAULT NULL COMMENT '申请单号',
  `cslt_type` VARCHAR(20) DEFAULT NULL COMMENT '会诊类别',
  `cslt_mold` VARCHAR(20) DEFAULT NULL COMMENT '会诊类型',
  `cslt_lv` VARCHAR(20) DEFAULT NULL COMMENT '会诊级别',
  `hosp_area` VARCHAR(20) DEFAULT NULL COMMENT '院区',
  `cslt_org_id` VARCHAR(50) DEFAULT NULL COMMENT '被邀科室ID',
  `cslt_org_name` VARCHAR(100) DEFAULT NULL COMMENT '被邀科室名称',
  `cslt_time` datetime DEFAULT NULL COMMENT '申请会诊时间',
  `cslt_employee_id` VARCHAR(50) DEFAULT NULL COMMENT '被邀医生ID',
  `cslt_employee_no` VARCHAR(50) DEFAULT NULL COMMENT '被邀医生工号',
  `cslt_employee_name` VARCHAR(50) DEFAULT NULL COMMENT '被邀医生姓名',
  `cslt_employee_tel` VARCHAR(20) DEFAULT NULL COMMENT '被邀医生联系电话',
  `patn_id` VARCHAR(50) DEFAULT NULL COMMENT '患者ID',
  `patn_name` VARCHAR(50) DEFAULT NULL COMMENT '患者姓名',
  `patn_gend` VARCHAR(20) DEFAULT NULL COMMENT '性别',
  `patn_age` VARCHAR(8) DEFAULT NULL COMMENT '年龄',
  `patn_bedno` VARCHAR(8) DEFAULT NULL COMMENT '床号',
  `patn_inp_no` VARCHAR(20) DEFAULT NULL COMMENT '住院号',
  `patn_org_name` VARCHAR(100) DEFAULT NULL COMMENT '科室',
  `patn_icd_name` VARCHAR(200) DEFAULT NULL COMMENT '诊断',
  `appy_time` datetime DEFAULT NULL COMMENT '申请时间',
  `appy_org_name` VARCHAR(100) DEFAULT NULL COMMENT '申请科室',
  `appy_emp_name` VARCHAR(50) DEFAULT NULL COMMENT '申请人',
  `appy_tel` VARCHAR(20) DEFAULT NULL COMMENT '申请人联系电话',
  `illhis` VARCHAR(1000) DEFAULT NULL COMMENT '病史及检查',
  `pup` VARCHAR(1000) DEFAULT NULL COMMENT '会诊目的',
  `act_status` char(1) not null DEFAULT '0' COMMENT '安排状态0未安排1已安排',
  `is_act_ot` char(1) not null DEFAULT '0' COMMENT '是否安排超时0否1是',
  `is_act_abn` char(1) not null DEFAULT '0' COMMENT '是否安排异常0否1是',
  `cslt_status` char(1) not null DEFAULT '0' COMMENT '会诊状态0未会诊1已会诊',
  `is_cslt_ot` char(1) not null DEFAULT '0' COMMENT '是否会诊超时0否1是',
  `is_cslt_abn` char(1) not null DEFAULT '0' COMMENT '是否会诊异常0否1是',
  `act_time` datetime DEFAULT NULL COMMENT '完成会诊时间',
  `act_user` VARCHAR(50) DEFAULT NULL COMMENT '安排人账号',
  `act_user_name` VARCHAR(50) DEFAULT NULL COMMENT '安排人名称',
  `act_employee_id` VARCHAR(50) DEFAULT NULL COMMENT '安排会诊医生ID',
  `act_employee_name` VARCHAR(50) DEFAULT NULL COMMENT '安排会诊医生名称',
  `act_employee_no` VARCHAR(20) DEFAULT NULL COMMENT '安排医生工号',
  `act_jobtitle` VARCHAR(50) DEFAULT NULL COMMENT '安排医生技术职称',
  `act_tel` VARCHAR(20) DEFAULT NULL COMMENT '安排医生手机号码',
  `act_start_time` datetime DEFAULT NULL COMMENT '计划会诊开始时间',
  `act_end_time` datetime DEFAULT NULL COMMENT '计划会诊结束时间',
  `notc_flag` char(1) DEFAULT NULL COMMENT '是否发送通知',
  `fns_time` datetime DEFAULT NULL COMMENT '完成会诊时间',
  `fns_employee_id` VARCHAR(50) DEFAULT NULL COMMENT '完成医生ID',
  `fns_employee_name` VARCHAR(50) DEFAULT NULL COMMENT '完成医生姓名',
  `fns_employee_no` VARCHAR(20) DEFAULT NULL COMMENT '完成医生工号',
  `fns_jobtitle` VARCHAR(50) DEFAULT NULL COMMENT '完成医生技术职称',
  `fns_tel` VARCHAR(20) DEFAULT NULL COMMENT '完成医生手机号码',
  `appy_org_dscr` VARCHAR(1000) DEFAULT NULL COMMENT '会诊意见(申请科室)',
  `cslt_org_dscr` VARCHAR(1000) DEFAULT NULL COMMENT '会诊意见(会诊科室)',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会诊申请单';
/**
会诊排班
 */
CREATE TABLE IF NOT EXISTS `med_cslt_scdu` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `scdu_no` VARCHAR(200) DEFAULT NULL COMMENT '编号',
  `cslt_type` VARCHAR(20) DEFAULT NULL COMMENT '会诊类别',
  `cslt_mold` VARCHAR(20) DEFAULT NULL COMMENT '会诊类型',
  `cslt_lv` VARCHAR(20) DEFAULT NULL COMMENT '会诊级别',
  `hosp_area` VARCHAR(20) DEFAULT NULL COMMENT '院区',
  `org_id` VARCHAR(50) DEFAULT NULL COMMENT '科室ID',
  `org_name` VARCHAR(100) DEFAULT NULL COMMENT '科室名称',
  `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '医生ID',
  `employee_name` VARCHAR(50) DEFAULT NULL COMMENT '医生姓名',
  `employee_no` VARCHAR(50) DEFAULT NULL COMMENT '医生工号',
  `employee_tel` VARCHAR(20) DEFAULT NULL COMMENT '医生联系电话',
  `cslt_scp` VARCHAR(100) DEFAULT NULL COMMENT '会诊范围',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会诊排班';
/**
会诊排班-明细
 */
CREATE TABLE IF NOT EXISTS `med_cslt_scdu_detl` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `scdu_id` VARCHAR(50) NOT NULL COMMENT '会诊排班ID',
  `scdu_date` datetime NOT NULL COMMENT '排班日期',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `push_flag` char(1) NOT NULL DEFAULT '0' COMMENT '推送标记0否1是',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会诊排班-明细';
/**
会诊排班操作记录
 */
CREATE TABLE IF NOT EXISTS `med_cslt_scdu_oprt` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `scdu_id` VARCHAR(50) NOT NULL COMMENT '会诊排班ID',
  `scdu_no` VARCHAR(200) DEFAULT NULL COMMENT '编号',
  `oprt_matt` VARCHAR(200) DEFAULT NULL COMMENT '操作事项',
  `oprt_rslt` char(1) DEFAULT '1' COMMENT '操作结果0失败1成功',
  `message` VARCHAR(1000) DEFAULT NULL COMMENT '过程信息',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会诊排班操作记录';
/**
积分奖惩登记
 */
CREATE TABLE IF NOT EXISTS `med_integral_record` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `integral_user` VARCHAR(50) DEFAULT NULL COMMENT '医师工号',
  `integral_user_name` VARCHAR(20) DEFAULT NULL COMMENT '医师姓名',
  `integral_org` datetime DEFAULT NULL COMMENT '所属科室',
  `integral_type` VARCHAR(20) DEFAULT NULL COMMENT '积分类型',
  `integral_score` VARCHAR(20) DEFAULT NULL COMMENT '积分数额',
  `integral_date` datetime DEFAULT NULL COMMENT '发生日期',
  `reason` VARCHAR(2000) DEFAULT NULL COMMENT '奖惩原因',
  `related_matters` VARCHAR(2000) DEFAULT NULL COMMENT '相关事项',
  `change_history` VARCHAR(2000) DEFAULT NULL COMMENT '积分变更历史',
  `feedback` VARCHAR(2000) DEFAULT NULL COMMENT '反馈与建议',
  `remark` VARCHAR(2000) DEFAULT NULL COMMENT '备注',
  `files` VARCHAR(500) DEFAULT NULL COMMENT '附件',
  `status` VARCHAR(2) DEFAULT NULL COMMENT '审核状态 0待审核 1审核通过  2审核不通过',
  `examine_user` VARCHAR(50) DEFAULT NULL COMMENT '审核人工号',
  `examine_user_name` VARCHAR(50) DEFAULT NULL COMMENT '审核人名称',
  `examine_date` datetime DEFAULT NULL COMMENT '审核时间',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` VARCHAR(2) DEFAULT NULL COMMENT '删除标示',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分奖惩登记';
/**
工作计划参与人
 */
CREATE TABLE IF NOT EXISTS `med_workplan_player` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `workplan_id` VARCHAR(50) DEFAULT NULL COMMENT '工作计划id',
  `player_user` VARCHAR(50) DEFAULT NULL COMMENT '参与人工号',
  `player_user_name` VARCHAR(50) DEFAULT NULL COMMENT '参与人姓名',
  `player_identity` VARCHAR(50) DEFAULT NULL COMMENT '参与人岗位',
  `player_gender` VARCHAR(5) DEFAULT NULL COMMENT '参与人性别',
  `player_org` VARCHAR(50) DEFAULT NULL COMMENT '参与人科室',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作计划参与人';
/**
工作计划管理
 */
CREATE TABLE IF NOT EXISTS `med_workplan_record` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `workplan_user` VARCHAR(50) DEFAULT NULL COMMENT '责任人工号',
  `workplan_user_name` VARCHAR(50) DEFAULT NULL COMMENT '责任人名称',
  `workplan_org` VARCHAR(50) DEFAULT NULL COMMENT '责任人科室',
  `workplan_identity` VARCHAR(50) DEFAULT NULL COMMENT '责任人岗位',
  `start_time` VARCHAR(50) DEFAULT NULL COMMENT '预计开始时间',
  `end_time` VARCHAR(50) DEFAULT NULL COMMENT '预计结束时间',
  `priority` VARCHAR(20) DEFAULT NULL COMMENT '优先级',
  `complete_status` VARCHAR(20) DEFAULT NULL COMMENT '完成状态',
  `workplan_name` VARCHAR(200) DEFAULT NULL COMMENT '工作计划名称',
  `workplan_content` VARCHAR(2000) DEFAULT NULL COMMENT '工作内容',
  `resource` VARCHAR(2000) DEFAULT NULL COMMENT '资源需求',
  `target` VARCHAR(2000) DEFAULT NULL COMMENT '目标和指标',
  `feedback` VARCHAR(2000) DEFAULT NULL COMMENT '反馈与改进',
  `files` VARCHAR(500) DEFAULT NULL COMMENT '附件',
  `remark` VARCHAR(2000) DEFAULT NULL COMMENT '备注',
  `status` VARCHAR(2) DEFAULT NULL COMMENT '审核状态 0待审核 1审核通过  2审核不通过',
  `examine_user` VARCHAR(50) DEFAULT NULL COMMENT '审核人工号',
  `examine_user_name` VARCHAR(50) DEFAULT NULL COMMENT '审核人名称',
  `examine_date` datetime DEFAULT NULL COMMENT '审核时间',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` VARCHAR(2) DEFAULT NULL COMMENT '删除标示',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作计划管理';
/**
授权项目配置
 */
CREATE TABLE IF NOT EXISTS `med_qua_auth_cfg` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键',
	  `qua_auth_type` VARCHAR(50) DEFAULT NULL COMMENT '资质授权类型',
	  `item_code` VARCHAR(50) DEFAULT NULL COMMENT '项目编码',
	  `item_name` VARCHAR(200) DEFAULT NULL COMMENT '项目名称',
	  `auth_lv_hosp` char(1) DEFAULT NULL COMMENT '授权级别(院标)',
	  `auth_lv_nat` char(1) DEFAULT NULL COMMENT '授权级别(国标)',
	  `is_mini_oprn` char(1) DEFAULT '0' COMMENT '是否微创手术0否1是',
	  `is_new_tech` char(1) DEFAULT '0' COMMENT '是否新技术项目0否1是',
	  `is_rstd_tech` char(1) DEFAULT '0' COMMENT '是否限制性技术0否1是',
	  `is_must_att` char(1)  DEFAULT '0' COMMENT '是否需要附件0否1是',
	  `is_enable` char(1)  DEFAULT '1' COMMENT '是否启用0否1是',
	  `remark` VARCHAR(1000) COMMENT '备注',
	  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
	  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
	  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
	  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
	  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
	  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
	  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
	  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
	  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
	  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
	  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
  	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资质授权';
/**
分类字典
 */
CREATE TABLE IF NOT EXISTS `comm_category_dict` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `pid` VARCHAR(50) DEFAULT NULL COMMENT '父节点',
  `name` VARCHAR(50) NOT NULL COMMENT '名称',
  `code` VARCHAR(50) NOT NULL COMMENT '编码',
  `has_child` char(1) NOT NULL DEFAULT '0' COMMENT '是否有子节点0否1是',
  `level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '层级',
  `description` VARCHAR(200) DEFAULT NULL COMMENT '描述',
  `sort_order` tinyint(4) DEFAULT NULL COMMENT '排序',
  `is_enable` char(1) NOT NULL DEFAULT '1' COMMENT '是否启用0否1是',
  `see_emp_ids` VARCHAR(2000) DEFAULT NULL COMMENT '可查看人IDS',
  `oprt_emp_ids` VARCHAR(2000) DEFAULT NULL COMMENT '可操作人IDS',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类字典';
/**
医师资质管理
 */
CREATE TABLE IF NOT EXISTS `med_qua_auth_mgt` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `qua_auth_type` VARCHAR(50) NOT NULL COMMENT '资质授权类型',
  `employee_id` VARCHAR(50) NOT NULL COMMENT '医生ID',
  `employee_no` VARCHAR(50) NOT NULL COMMENT '医生工号',
  `employee_name` VARCHAR(50) NOT NULL COMMENT '医生姓名',
  `identity_number` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
  `jobtitle` VARCHAR(50) DEFAULT NULL COMMENT '技术职称',
  `tel` VARCHAR(50) DEFAULT NULL COMMENT '联系电话',
  `org_id` VARCHAR(50) DEFAULT NULL COMMENT '所在科室ID',
  `org_name` VARCHAR(100) DEFAULT NULL COMMENT '所在科室名称',
  `hosp_area` VARCHAR(20) DEFAULT NULL COMMENT '所在院区',
  `workflow_id` VARCHAR(50) DEFAULT NULL COMMENT '流程实例id',
  `audit_status` char(1) NOT NULL DEFAULT '0' COMMENT '审批状态',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) NOT NULL DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医师资质管理';
/**
医师资质授权附件
 */
CREATE TABLE IF NOT EXISTS `med_qua_auth_file` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `mgt_id` VARCHAR(50) DEFAULT NULL COMMENT '资质授权管理ID',
  `item_detl_id` VARCHAR(50) DEFAULT NULL COMMENT '授权项目明细ID',
  `file_key` VARCHAR(50) DEFAULT NULL COMMENT '附件KEY',
  `file_name` VARCHAR(100) DEFAULT NULL COMMENT '附件名称',
  `file_save_name` VARCHAR(100) DEFAULT NULL COMMENT '附件保存路径',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) DEFAULT NULL  COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医师资质授权附件';
/**
医师资质授权项目明细
 */
CREATE TABLE IF NOT EXISTS `med_qua_auth_item_detl` (
  `id` VARCHAR(50) NOT NULL COMMENT '主键',
  `mgt_id` VARCHAR(50) DEFAULT NULL COMMENT '资质授权管理ID',
  `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '医生ID',
  `employee_no` VARCHAR(50) DEFAULT NULL COMMENT '医生工号',
  `employee_name` VARCHAR(50) DEFAULT NULL COMMENT '医生姓名',
  `identity_number` VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
  `jobtitle` VARCHAR(50) DEFAULT NULL COMMENT '技术职称',
  `tel` VARCHAR(50) DEFAULT NULL COMMENT '联系电话',
  `org_id` VARCHAR(50) DEFAULT NULL COMMENT '所在科室ID',
  `org_name` VARCHAR(100) DEFAULT NULL COMMENT '所在科室名称',
  `hosp_area` VARCHAR(20) DEFAULT NULL COMMENT '所在院区',
  `qua_auth_type` VARCHAR(50) DEFAULT NULL COMMENT '资质授权类型',
  `audit_status` char(1) DEFAULT '0' COMMENT '审批状态',
  `auth_lv_hosp` char(1) DEFAULT NULL COMMENT '授权级别(院标)',
  `auth_lv_nat` char(1) DEFAULT NULL COMMENT '授权级别(国标)',
  `seq_no` smallint(6) DEFAULT NULL COMMENT '序号',
  `item_code` VARCHAR(50) DEFAULT NULL COMMENT '项目编码',
  `item_name` VARCHAR(200) DEFAULT NULL COMMENT '项目名称',
  `appy_time` datetime DEFAULT NULL COMMENT '申请时间',
  `begntime` datetime DEFAULT NULL COMMENT '生效时间',
  `endtime` datetime DEFAULT NULL COMMENT '失效时间',
  `auth_status` char(1) DEFAULT NULL DEFAULT '1' COMMENT '授权状态(0失效1正常)',
  `AUDIT_DSCR` VARCHAR(50) DEFAULT NULL COMMENT '审批',
  `workflow_id` VARCHAR(50) DEFAULT NULL COMMENT '流程id',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `create_user` VARCHAR(50) DEFAULT NULL COMMENT '创建人账号',
  `create_user_name` VARCHAR(50) DEFAULT NULL COMMENT '创建人名称',
  `create_dept` VARCHAR(50) DEFAULT NULL COMMENT '创建人所属部门编码',
  `create_dept_name` VARCHAR(200) DEFAULT NULL COMMENT '创建人所属部门名称',
  `update_user` VARCHAR(50) DEFAULT NULL COMMENT '更新人账号',
  `update_user_name` VARCHAR(50) DEFAULT NULL COMMENT '更新人名称',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) DEFAULT 'N' COMMENT '删除标记',
  `sso_org_code` VARCHAR(50) DEFAULT NULL COMMENT '机构编码',
  `sso_org_name` VARCHAR(50) DEFAULT NULL COMMENT '机构名称',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='医师资质授权项目明细';

/**
系统字段映射关系表
 */
CREATE TABLE IF NOT EXISTS `comm_field_mapping` (
  `sys_code` VARCHAR(50) NOT NULL COMMENT '系统编码',
  `field_code` VARCHAR(50) NOT NULL COMMENT '字段',
  `current_val` VARCHAR(50) NOT NULL COMMENT '字段当前系统的值',
  `mapping_val` VARCHAR(200) NOT NULL COMMENT '映射系统值',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` char(1) not NULL DEFAULT 'N' COMMENT '删除标记',
  PRIMARY KEY (`sys_code`,`field_code`,`current_val`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统字段映射关系表';


set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_cslt_appy' and COLUMN_NAME = 'appy_org_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_cslt_appy` ADD `appy_org_id` varchar(50) COMMENT ''申请科室id'' ',
                   'select ''INFO: appy_org_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_cslt_appy' and COLUMN_NAME = 'appy_hosp_area' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_cslt_appy` ADD `appy_hosp_area` varchar(20) COMMENT ''申请科室所属院区'' ',
                   'select ''INFO: appy_hosp_area 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_cslt_appy' and COLUMN_NAME = 'appy_emp_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_cslt_appy` ADD `appy_emp_id` varchar(50) COMMENT ''申请人id'' ',
                   'select ''INFO: appy_emp_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

create table IF NOT EXISTS med_quality_apply
(
   id                   varchar(50) not null,
   apply_name           varchar(20) comment '姓名',
   apply_code           varchar(50) comment '工号',
   apply_area           varchar(50) comment '院区',
   apply_org_id         varchar(50) comment '科室',
   apply_idcard         varchar(20) comment '身份证号',
   apply_technical      varchar(50) comment '技术职称',
   apply_phone          varchar(20) comment '联系方式',
   apply_date           varchar(20) comment '申请时间',
   apply_start_date     date comment '任期开始时间',
   apply_end_date       date comment '任期结束时间',
   work_experience      varchar(1000) comment '临床工作经历',
   apply_term_time      varchar(20) comment '应任期时长',
   apply_real_date      date comment '实际结束时间',
   apply_real_time      varchar(20) comment '实际任期时长',
   apply_search_date    date comment '查询用时间',
   apply_files          varchar(1000) comment '附件',
   apply_status         varchar(5) comment '状态',
   apply_result         varchar(5) comment '考核结果',
   workflow_id         varchar(50) comment '流程id',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
);

create table IF NOT EXISTS med_quality_punish
(
   id                   varchar(50) not null comment '主键',
   quality_id           varchar(50) comment '申请表id',
   punish_end_date      date comment '处罚结束时间',
   punish_result        varchar(200) comment '处罚结果',
   punish_date          date comment '处罚时间',
   punish_remark        varchar(1000) comment '处罚原因',
   primary key (id)
);


create table IF NOT EXISTS med_doctor_role
(
   id                   varchar(50) not null comment '主键',
   employee_id          varchar(50) comment '员工id',
   employee_no          varchar(50) comment '员工工号',
   ptcfq                varchar(5) comment '普通处方权',
   kjywcfq              varchar(5) comment '抗菌药物处方权',
   zyypq                varchar(5) comment '中药饮片权',
   tsjkjywhzq           varchar(5) comment '特殊级抗菌药物会诊权',
   mzypcfq              varchar(5) comment '麻醉药品处方权',
   kzlywcfq             varchar(5) comment '抗肿瘤药物处方权',
   jsypcfq              varchar(5) comment '精神药品处方权',
   dxypcfq              varchar(5) comment '毒性药品处方权',
   fsxypcfq             varchar(5) comment '放射性药品处方权',
   zzrsypcfq            varchar(5) comment '终止妊娠药品处方权',
   xdcfq            	varchar(5) comment '协定处方权',
   ssdjqx            	varchar(5) comment '手术等级权限',
   gwypdjqx            	varchar(5) comment '高危药品等级权限',
   yjsyqx            	varchar(5) comment '越级使用权限',
   workflow_id			varchar(50) comment '工作流id',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
);

create table IF NOT EXISTS med_doctor_role_opt
(
   id                   varchar(50) not null,
   business_id			varchar(50) comment '业务id',
   employee_id          varchar(50) comment '医师id',
   employee_no          varchar(50) comment '医师工号',
   role_type            varchar(5) comment '权限类型',
   opt_title			varchar(50) comment '操作标题',
   opt_type             varchar(5) comment '操作类型  1继续授权 2停止授权',
   opt_file             varchar(1000) comment '操作附件',
   opt_remark           varchar(1000) comment '操作备注',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
);

create table IF NOT EXISTS med_pharmacist_role
(
   id                   varchar(50) not null comment '主键',
   employee_id          varchar(50) comment '员工id',
   employee_no          varchar(50) comment '员工工号',
   workflow_id          varchar(50) comment '流程id',
   role_type            varchar(50) comment '类型 1处方调剂权限  2临床药学技术权限',
   role_value           varchar(50) comment '选项值',
   auth_status          varchar(1) comment '授权状态  0停止授权 2继续授权',
   opt_remark           varchar(500) comment '操作备注',
   opt_file             varchar(500) comment '操作附件',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
);

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_qua_auth_cfg' and COLUMN_NAME = 'icd_code' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_qua_auth_cfg` ADD `icd_code` varchar(50) NULL COMMENT ''手术ICD'' ',
                   'select ''INFO: icd_code 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_qua_auth_cfg' and COLUMN_NAME = 'icd_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_qua_auth_cfg` ADD `icd_name` varchar(50) NULL COMMENT ''手术ICD名称'' ',
                   'select ''INFO: icd_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_qua_auth_cfg' and COLUMN_NAME = 'oper_id' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_qua_auth_cfg` ADD `oper_id` varchar(50) NULL COMMENT ''HIS字典ID'' ',
                   'select ''INFO: oper_id 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_quality_apply' and COLUMN_NAME = 'is_overdue' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_quality_apply` ADD `is_overdue` varchar(5) NULL COMMENT ''是否超期任职'' ',
                   'select ''INFO: is_overdue 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_quality_apply' and COLUMN_NAME = 'opt_date' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_quality_apply` ADD `opt_date` varchar(20) NULL COMMENT ''考核日期/中止日期'' ',
                   'select ''INFO: opt_date 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_quality_apply' and COLUMN_NAME = 'stop_remark' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_quality_apply` ADD `stop_remark` varchar(500) NULL COMMENT ''中止原因'' ',
                   'select ''INFO: stop_remark 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_quality_apply' and COLUMN_NAME = 'disable_time' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_quality_apply` ADD `disable_time` varchar(10) NULL COMMENT ''禁用申请时间'' ',
                   'select ''INFO: disable_time 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_quality_apply' and COLUMN_NAME = 'expire_status' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_quality_apply` ADD `expire_status` varchar(5) NULL COMMENT ''过期状态'' ',
                   'select ''INFO: expire_status 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_doctor_role' and COLUMN_NAME = 'hzqx' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_doctor_role` ADD `hzqx` varchar(5) NULL COMMENT ''会诊权'' ',
                   'select ''INFO: hzqx 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_doctor_role' and COLUMN_NAME = 'sxqx' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_doctor_role` ADD `sxqx` varchar(5) NULL COMMENT ''输血权'' ',
                   'select ''INFO: sxqx 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


CREATE TABLE IF NOT EXISTS med_schedule_group(
    `id` VARCHAR(50) NOT NULL  COMMENT '' ,
    `group_name` VARCHAR(50)   COMMENT '分组名称' ,
    `group_emp_id` VARCHAR(2000)   COMMENT '分组人员id' ,
    `group_emp_name` VARCHAR(2000)   COMMENT '分组人员名称' ,
    `group_sort` INT   COMMENT '' ,
    `create_date` DATETIME   COMMENT '创建时间' ,
    `create_user` VARCHAR(50)   COMMENT '创建人' ,
    `create_user_name` VARCHAR(50)   COMMENT '创建人名称' ,
    `update_user` VARCHAR(50)   COMMENT '更新人' ,
    `update_user_name` VARCHAR(50)   COMMENT '更新人名称' ,
    `update_date` DATETIME   COMMENT '更新时间' ,
    `is_deleted` VARCHAR(1)   COMMENT '删除标示' ,
    PRIMARY KEY (id)
)  COMMENT = '排班分组';

ALTER TABLE `ts_base_oa`.`med_schedule_group` 
MODIFY COLUMN `group_sort` int(11) NULL DEFAULT 999;

CREATE TABLE IF NOT EXISTS med_schedule_authority(
    `id` VARCHAR(50) NOT NULL  COMMENT '主键' ,
    `manage_name` VARCHAR(500)   COMMENT '排班管理员名称' ,
    `manage_user_code` VARCHAR(500)   COMMENT '排班管理员code' ,
    `schedule_name` VARCHAR(4000)   COMMENT '考勤组范围名称' ,
    `schedule_org` VARCHAR(4000)   COMMENT '考勤组范围机构id' ,
    `schedule_user` VARCHAR(4000)   COMMENT '考勤组范围用户编码' ,
    `authority_remark` VARCHAR(1000)   COMMENT '备注' ,
    `create_date` DATETIME   COMMENT '创建时间' ,
    `create_user` VARCHAR(50)   COMMENT '创建人' ,
    `create_user_name` VARCHAR(50)   COMMENT '创建人名称' ,
    `update_user` VARCHAR(50)   COMMENT '更新人' ,
    `update_user_name` VARCHAR(50)   COMMENT '更新人名称' ,
    `update_date` DATETIME   COMMENT '更新时间' ,
    `is_deleted` VARCHAR(1)   COMMENT '删除标示' ,
    `sso_org_code` VARCHAR(50)   COMMENT '机构编码' ,
    `sso_org_name` VARCHAR(50)   COMMENT '机构名称' ,
    PRIMARY KEY (id)
)  COMMENT = '排班权限设置';

CREATE TABLE IF NOT EXISTS med_schedule_authority_sort(
    `id` VARCHAR(50) NOT NULL  COMMENT '' ,
    `authority_id` VARCHAR(50)   COMMENT '' ,
    `employee_id` VARCHAR(50)   COMMENT '' ,
    `sort` INT   COMMENT '' ,
    PRIMARY KEY (id)
)  COMMENT = '排班人员排序';

CREATE TABLE IF NOT EXISTS med_schedule_classes(
    `id` VARCHAR(50) NOT NULL  COMMENT '主键' ,
    `type_id` VARCHAR(50)   COMMENT '班次类别id' ,
    `classes_name` VARCHAR(50)   COMMENT '班次名称' ,
    `classes_days` VARCHAR(50)   COMMENT '出勤天数' ,
    `classes_color` VARCHAR(50)   COMMENT '排班颜色' ,
    `classes_type` VARCHAR(50)   COMMENT '班次类型' ,
    `classes_hours` VARCHAR(50)   COMMENT '班次时长' ,
    `classes_remark` VARCHAR(200)   COMMENT '班次备注' ,
    `classes_worktime` VARCHAR(1000)   COMMENT '考勤时间 多个分号隔开' ,
    `classes_status` VARCHAR(2)   COMMENT '状态 0禁用  1启用' ,
    `classes_use_names` VARCHAR(2000)   COMMENT '使用范围名称' ,
    `classes_use_org` VARCHAR(2000)   COMMENT '使用范围科室' ,
    `classes_use_user` VARCHAR(2000)   COMMENT '使用范围人员' ,
    `holiday_type` VARCHAR(2)   COMMENT '休息设置  1按国家法定节假日休息 2周末不算休息，法定节日休息  3周末、法定节日都不算休息' ,
    `create_date` DATETIME   COMMENT '创建时间' ,
    `create_user` VARCHAR(50)   COMMENT '创建人' ,
    `create_user_name` VARCHAR(50)   COMMENT '创建人名称' ,
    `update_user` VARCHAR(50)   COMMENT '更新人' ,
    `update_user_name` VARCHAR(50)   COMMENT '更新人名称' ,
    `update_date` DATETIME   COMMENT '更新时间' ,
    `is_deleted` VARCHAR(1)   COMMENT '删除标示' ,
    `sso_org_code` VARCHAR(50)   COMMENT '机构编码' ,
    `sso_org_name` VARCHAR(50)   COMMENT '机构名称' ,
    PRIMARY KEY (id)
)  COMMENT = '排班班次';

CREATE TABLE IF NOT EXISTS med_schedule_group_user(
    `id` VARCHAR(50) NOT NULL  COMMENT '' ,
    `group_id` VARCHAR(50)   COMMENT '' ,
    `employee_id` VARCHAR(50)   COMMENT '' ,
    `emp_sort` INT   COMMENT '' ,
    `create_user` VARCHAR(50)   COMMENT '' ,
    PRIMARY KEY (id)
)  COMMENT = '排班分组用户表';

CREATE TABLE IF NOT EXISTS med_schedule_record(
    `id` VARCHAR(50) NOT NULL  COMMENT '主键' ,
    `schedule_date` VARCHAR(50)   COMMENT '排班日期' ,
    `employee_id` VARCHAR(50)   COMMENT '员工id' ,
    `type_id` VARCHAR(50)   COMMENT '类别id' ,
    `classes_id` VARCHAR(50)   COMMENT '班次id' ,
    `emp_org_id` VARCHAR(50)   COMMENT '人员机构id' ,
    `group_id` VARCHAR(50)   COMMENT '分组id' ,
    `remark` VARCHAR(200)   COMMENT '备注' ,
    `sync` VARCHAR(2)  DEFAULT '0' COMMENT '是否同步数据' ,
    `template` VARCHAR(2)  DEFAULT '0' COMMENT '是否模板数据' ,
    `create_date` DATETIME   COMMENT '创建时间' ,
    `create_user` VARCHAR(50)   COMMENT '创建人' ,
    `create_user_name` VARCHAR(50)   COMMENT '创建人名称' ,
    `update_user` VARCHAR(50)   COMMENT '更新人' ,
    `update_user_name` VARCHAR(50)   COMMENT '更新人名称' ,
    `update_date` DATETIME   COMMENT '更新时间' ,
    `is_deleted` VARCHAR(1)   COMMENT '删除标示' ,
    `sso_org_code` VARCHAR(50)   COMMENT '机构编码' ,
    `sso_org_name` VARCHAR(50)   COMMENT '机构名称' ,
    PRIMARY KEY (id)
)  COMMENT = '排班记录';

CREATE TABLE IF NOT EXISTS med_schedule_template(
    `id` VARCHAR(50) NOT NULL  COMMENT '主键' ,
    `classes_id` VARCHAR(50)   COMMENT '班次id' ,
    `schedule_week` VARCHAR(50)   COMMENT '排班周次' ,
    `type_id` VARCHAR(50)   COMMENT '类型id' ,
    `employee_id` VARCHAR(50)   COMMENT '排班人员id' ,
    `emp_org_id` VARCHAR(50)   COMMENT '排班人员机构id' ,
    `create_date` DATETIME   COMMENT '创建时间' ,
    `create_user` VARCHAR(50)   COMMENT '创建人' ,
    `create_user_name` VARCHAR(50)   COMMENT '创建人名称' ,
    `update_user` VARCHAR(50)   COMMENT '更新人' ,
    `update_user_name` VARCHAR(50)   COMMENT '更新人名称' ,
    `update_date` DATETIME   COMMENT '更新时间' ,
    `is_deleted` VARCHAR(1)   COMMENT '删除标示' ,
    PRIMARY KEY (id)
)  COMMENT = '排班模板';

CREATE TABLE IF NOT EXISTS med_schedule_type(
    `id` VARCHAR(50) NOT NULL  COMMENT '主键' ,
    `type_name` VARCHAR(50)   COMMENT '类型名称' ,
    `sort` INT   COMMENT '排序' ,
    `create_date` DATETIME   COMMENT '创建时间' ,
    `create_user` VARCHAR(50)   COMMENT '创建人' ,
    `create_user_name` VARCHAR(50)   COMMENT '创建人名称' ,
    `update_user` VARCHAR(50)   COMMENT '更新人' ,
    `update_user_name` VARCHAR(50)   COMMENT '更新人名称' ,
    `update_date` DATETIME   COMMENT '更新时间' ,
    `is_deleted` VARCHAR(1)   COMMENT '删除标示' ,
    `sso_org_code` VARCHAR(50)   COMMENT '机构编码' ,
    `sso_org_name` VARCHAR(50)   COMMENT '机构名称' ,
    PRIMARY KEY (id)
)  COMMENT = '排班班次类型';

create table IF NOT EXISTS med_schedule_compose_classes
(
   id                   varchar(50) not null comment '主键',
   compose_name         varchar(100) comment '组合班次名称',
   compose_content      varchar(2000) comment '组合班次内容',
   compose_content_id   varchar(2000) comment '组合班次内容id',
   remark               varchar(200) comment '备注',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   sso_org_code         varchar(50),
   primary key (id)
)COMMENT = '组合班次';

create table IF NOT EXISTS med_doctor_level
(
   id                   varchar(50) not null,
   apply_dept_id        varchar(50) comment '申请科室id',
   apply_dept_name      varchar(50) comment '申请科室',
   apply_user_code      varchar(50) comment '申请人code',
   apply_user_name      varchar(50) comment '申请人名称',
   tenure_dept_id       varchar(500) comment '申请任职科室id',
   tenure_dept_name     varchar(500) comment '申请任职科室',
   tenure_start_date    varchar(20) comment '任职开始日期',
   tenure_end_date      varchar(20) comment '任职结束日期',
   apply_type           varchar(20) comment '申请类型',
   authorize_type       varchar(50) comment '授权类型',
   tenure_status        varchar(5) comment '任职状态',
   apply_date           varchar(50) comment '申请时间',
   summary              varchar(2000) comment '小结',
   remark               varchar(2000) comment '职责描述',
   workflow_id          varchar(50) comment '流程id',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
)COMMENT = '医生授权管理';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_schedule_classes' and COLUMN_NAME = 'holiday_classes' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_schedule_classes` ADD `holiday_classes` varchar(5) NULL COMMENT ''是否休息班次  0否 1是'' ',
                   'select ''INFO: holiday_classes 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


create table IF NOT EXISTS med_delivery_dict
(
   id                   varchar(50) not null,
   project_name         varchar(200) comment '项目名称',
   connotation          varchar(1000) comment '项目内涵',
   purpose              varchar(500) comment '检验目的',
   sample_type          varchar(100) comment '样本类型',
   sample_dose          varchar(100) comment '样本量',
   test_org             varchar(100) comment '外送检验机构',
   test_mean            varchar(100) comment '检测方式',
   cost                 varchar(20) comment '费用',
   fee_item             varchar(500) comment '物价收费条目',
   fee_code             varchar(50) comment '收费编码',
   fee_details          varchar(500) comment '收费详情',
   fee_price            varchar(50) comment '收费价格',
   price_judge          varchar(500) comment '物价判断',
   exc_dept             varchar(100) comment '执行科室（天、岳）',
   exc_dept_m           varchar(100) comment '执行科室（马）',
   remark               varchar(2000) comment '备注',
   status               varchar(2) comment '状态',
   dict_type            varchar(2) comment '字典类型  1、无物价  2、有物价',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
)COMMENT = '外送检验字典';

create table IF NOT EXISTS med_delivery_record
(
   id                   varchar(50) not null comment '主键',
   workflow_id          varchar(50) comment '流程id',
   dict_id              varchar(50) comment '字典id',
   project_name         varchar(200) comment '项目名称',
   apply_name           varchar(50) comment '申请医师',
   apply_tel            varchar(20) comment '联系电话',
   patient_name         varchar(50) comment '患者姓名',
   in_patient_no        varchar(50) comment '住院号/门诊号',
   sex                  varchar(10) comment '性别',
   age                  varchar(10) comment '年龄',
   diagnosis            varchar(2000) comment '主要诊断',
   illness              varchar(2000) comment '患者病情简介',
   sample_type          varchar(50) comment '样本类型',
   sample_dose          varchar(50) comment '样本量',
   test_org             varchar(100) comment '外送检验机构名称',
   cost                 varchar(50) comment '费用',
   reason               varchar(1000) comment '外送原因',
   connotation          varchar(1000) comment '项目内涵',
   apply_date           varchar(20) comment '申请日期',
   apply_org            varchar(50) comment '申请科室',
   apply_area           varchar(50) comment '申请院区',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
)COMMENT = '外送检验记录';

create table IF NOT EXISTS med_crisis_value
(
   id                   varchar(50) not null comment '主键',
   visit_id             varchar(50) comment '就诊id',
   request_id           varchar(50) comment '申请单id',
   visit_type           varchar(5) comment '就诊类型 1-门诊 2-住院',
   visit_no             varchar(50) comment '门诊号/住院',
   patient_id           varchar(50) comment '患者id',
   name                 varchar(20) comment '患者姓名',
   sex_name             varchar(5) comment '性别',
   age                  varchar(5) comment '年龄',
   bed_no               varchar(10) comment '床号',
   tel                  varchar(20) comment '联系电话',
   request_dept_name    varchar(50) comment '申请科室',
   diagnosis_name       varchar(1000) comment '临床诊断',
   report_no            varchar(50) comment '检查报告编号',
   sample_name      	varchar(200) comment '标本名称',
   item_name      		varchar(200) comment '项目名称',
   order_item_name      varchar(200) comment '检查项目',
   panic_value          varchar(1000) comment '危急值内容',
   report_date          varchar(20) comment '上报日期',
   sign_user_name       varchar(20) comment '上报人',
   sign_dept_name       varchar(50) comment '上报科室',
   bed_doctor_id        varchar(50) comment '管床医生ID',
   bed_doctor_code      varchar(50) comment '管床医生CODE',
   bed_doctor_name      varchar(20) comment '管床医生姓名',
   main_doctor_id       varchar(50) comment '主治医生ID',
   main_doctor_name     varchar(20) comment '主治医生姓名',
   chief_doctor_id      varchar(50) comment '主任医生ID',
   chief_doctor_name    varchar(20) comment '主任医生姓名',
   receive_status       varchar(5) comment '接受状态0:未接收 1:已接收',
   status               varchar(5) comment '1:已通知(仅通知类消息) 0:未处理 1:已处理',
   process_date         varchar(20) comment '处理时间',
   process_desc         varchar(1000) comment '处理措施',
   process_user_id      varchar(50) comment '处理人ID',
   process_user_name    varchar(20) comment '处理人姓名',
   create_date          datetime comment '创建时间',
   create_user          varchar(50) comment '创建人',
   create_user_name     varchar(50) comment '创建人名称',
   update_user          varchar(50) comment '更新人',
   update_user_name     varchar(50) comment '更新人名称',
   update_date          datetime comment '更新时间',
   is_deleted           varchar(1) comment '删除标示',
   primary key (id)
)COMMENT = '危急值记录';


create table IF NOT EXISTS med_his_employee
(
   employee_id          varchar(50) not null,
   employee_no          varchar(50),
   employee_name        varchar(50),
   org_id               varchar(50),
   org_name             varchar(50),
   primary key (employee_id)
)COMMENT = 'HIS员工记录表';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_his_employee' and COLUMN_NAME = 'org_name' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_his_employee` ADD `org_name` varchar(50) NULL COMMENT ''科室名称'' ',
                   'select ''INFO: org_name 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;


set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_doctor_role' and COLUMN_NAME = 'fsxyw' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_doctor_role` ADD `fsxyw` varchar(5) DEFAULT ''0'' COMMENT ''放射性药物'' ',
                   'select ''INFO: fsxyw 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_doctor_role' and COLUMN_NAME = 'tpzjsyw' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_doctor_role` ADD `tpzjsyw` varchar(5) DEFAULT ''0'' COMMENT ''糖皮质激素药物'' ',
                   'select ''INFO: tpzjsyw 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

set @exist := (select count(1) from information_schema.columns
               where table_name = 'med_doctor_role' and COLUMN_NAME = 'mzyszg' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`med_doctor_role` ADD `mzyszg` varchar(5) DEFAULT ''0'' COMMENT ''麻醉医师资格'' ',
                   'select ''INFO: mzyszg 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS hrms_out_record_xx  (
  id varchar(50)  NOT NULL COMMENT '主键',
  employee_id varchar(36)  NULL DEFAULT NULL COMMENT '员工id',
  province_type varchar(10)  NULL DEFAULT NULL COMMENT '出差范围',
  apply_time varchar(20)  NULL DEFAULT NULL COMMENT '申请时间',
  out_type varchar(20)  NULL DEFAULT NULL COMMENT '外出类型',
  jobs varchar(20)  NULL DEFAULT NULL COMMENT '岗位类别',
  start_time varchar(30)  NULL DEFAULT NULL COMMENT '外出开始时间',
  end_time varchar(30)  NULL DEFAULT NULL COMMENT '外出结束时间',
  out_days varchar(20)  NULL DEFAULT NULL COMMENT '外出天数',
  out_address varchar(1000)  NULL DEFAULT NULL COMMENT '外出地点',
  out_remark varchar(2000)  NULL DEFAULT NULL COMMENT '外出事由',
  file_id varchar(1000)  NULL DEFAULT NULL COMMENT '附件id',
  file_name varchar(1000)  NULL DEFAULT NULL COMMENT '附件名称',
  file_path varchar(2000)  NULL DEFAULT NULL COMMENT '附件路径',
  apply_user varchar(20)  NULL DEFAULT NULL COMMENT '申请人',
  apply_user_name varchar(20)  NULL DEFAULT NULL COMMENT '申请人名称',
  apply_dept varchar(20)  NULL DEFAULT NULL COMMENT '申请人部门',
  apply_dept_name varchar(50)  NULL DEFAULT NULL COMMENT '申请人部门名称',
  create_date datetime NULL DEFAULT NULL COMMENT '创建时间',
  create_user varchar(20)  NULL DEFAULT NULL COMMENT '创建人',
  create_user_name varchar(20)  NULL DEFAULT NULL COMMENT '创建人名称',
  update_date datetime NULL DEFAULT NULL COMMENT '更新时间',
  update_user varchar(20)  NULL DEFAULT NULL COMMENT '更新人',
  update_user_name varchar(20)  NULL DEFAULT NULL COMMENT '更新人名称',
  is_deleted varchar(5)  NULL DEFAULT NULL COMMENT '删除标示',
  work_id varchar(50)  NULL DEFAULT NULL COMMENT '流程id',
  status varchar(1)  NULL DEFAULT NULL,
  real_start_time varchar(255)  NULL DEFAULT NULL COMMENT '实际外出开始时间',
  real_end_time varchar(255)  NULL DEFAULT NULL COMMENT '实际外出结束时间',
  primary key (id)
)COMMENT = '下乡';

set @exist := (select count(1) from information_schema.columns
               where table_name = 'hrms_suggestion_box' and COLUMN_NAME = 'box_type' and table_schema = database());
set @sqlstmt := if(@exist= 0, 'ALTER TABLE `ts_base_oa`.`hrms_suggestion_box` ADD `box_type` varchar(5) DEFAULT ''0'' COMMENT ''意见箱类型'' ',
                   'select ''INFO: box_type 字段已存在.''');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
