<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.dispute.dao.DiregisterStatisticsMapper">
  
  <select id="eventTypeStatistics" parameterType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp" resultType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp">
  
  	select s.event_type,count(1) as count from di_register s where 1=1 and s.is_deleted='N'
  	
  	
  	<if test="startDate!=null and startDate!=''">
  		AND s.create_date >= #{startDate}
  	</if>
  	
  	<if test="endDate!=null and endDate!=''">
  		AND  #{endDate} >= s.create_date 
  	</if>
  	
  	group by s.event_type
  	 
  </select>
  
  
  <select id="complainedOrgStatistics" parameterType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp" resultType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp">
  
  	select s.complained_org,s.complained_org_name,count(1) as count from di_register s where 1=1 and s.is_deleted='N'
  	
  	
  	<if test="startDate!=null and startDate!=''">
  		AND s.create_date >= #{startDate}
  	</if>
  	
  	<if test="endDate!=null and endDate!=''">
  		AND  #{endDate} >= s.create_date 
  	</if>
  	
  	group by s.complained_org,s.complained_org_name
  	 
  </select>
  
  <select id="overviewStatistics" parameterType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp" resultType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp">
  
		
		select s.event_type,count(1) as registerNum,s.complained_org,s.complained_org_name,a1.settleNum  from di_register s 

		left join (
		
		select a.event_type,count(1) as settleNum,a.complained_org,a.complained_org_name   from di_register a 
		
		where 1=1 and a.is_deleted = 'N' and a.status=8 group by a.event_type,a.complained_org,a.complained_org_name
		
		) a1 on a1.event_type = s.event_type and a1.complained_org = s.complained_org
		
		where s.is_deleted='N' group by s.event_type,s.complained_org,s.complained_org_name
   	


  	 
  </select>
  
  <select id="myHandleStatistics"  parameterType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp" resultType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp">
  	select count(1) as count from di_register s where s.is_deleted='N' and s.status=4 
   		
   		<if test="createUser!=null and createUser!=''">
   		
	   		and s.handled_user_code =  #{createUser}
	   		
	   	</if>
   		
  </select>
  
  <select id="myApprovalStatistics"  parameterType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp" resultType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp">
  
		
   	select count(1) as count from di_register s 
   	
   	inner  join wf_instance_info info on s.id = info.BUSINESS_ID
   	
   	inner join wf_task task on info.WF_INSTANCE_ID = task.WF_INSTANCE_ID 
   	
   	where s.is_deleted='N' and s.status=5 
   	<if test="createUser!=null and createUser!=''">
   		and task.ASSIGNEE_NO =  #{createUser}
   	</if>
   	


  	 
  </select>
  
  <select id="myClosedCaseStatistics" parameterType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp"  resultType="cn.trasen.oa.dispute.model.DiregisterStatisticsResp">
	select count(1) as count from di_register s 

	where s.status = 8 
	<if test="createUser!=null and createUser!=''">
   		and (s.registrant_user =  #{createUser} or s.report_user =  #{createUser})
   	</if>
   	
  	 
  </select>
  
  
</mapper>