package cn.trasen.oa.check.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.cloud.commons.lang.StringUtils;

import cn.trasen.BootComm.utils.ApplicationUtils;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.check.dao.ToaCheckRouteContentMapper;
import cn.trasen.oa.check.dao.ToaCheckRouteDetailMapper;
import cn.trasen.oa.check.dao.ToaCheckRouteMapper;
import cn.trasen.oa.check.model.ToaCheckRoute;
import cn.trasen.oa.check.model.ToaCheckRouteContent;
import cn.trasen.oa.check.model.ToaCheckRouteDetail;
import cn.trasen.oa.check.service.ToaCheckRouteService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName ToaCheckRouteServiceImpl
 * @Description TODO
 * @date 2021��8��4�� ����11:20:17
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class ToaCheckRouteServiceImpl implements ToaCheckRouteService {

	@Autowired
	private ToaCheckRouteMapper mapper;
	
	@Autowired
	private ToaCheckRouteDetailMapper toaCheckRouteDetailMapper;
	
	@Autowired
	private ToaCheckRouteContentMapper toaCheckRouteContentMapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(ToaCheckRoute record) {
		String routeId = ApplicationUtils.GUID32();
		record.setId(routeId);
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		record.setStatus(0);
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		if(CollectionUtils.isNotEmpty(record.getCheckRouteDetails())) {
			
			for(ToaCheckRouteDetail detail : record.getCheckRouteDetails()) {
				
				String routeDetailId = ApplicationUtils.GUID32();
				detail.setRouteId(routeId);
				detail.setId(routeDetailId);
				detail.setCreateDate(new Date());
				detail.setUpdateDate(new Date());
				detail.setIsDeleted(Contants.IS_DELETED_FALSE);
				detail.setCreateUser(user.getUsercode());
				detail.setCreateUserName(user.getUsername());
				detail.setUpdateUser(user.getUsercode());
				detail.setUpdateUserName(user.getUsername());
				detail.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				toaCheckRouteDetailMapper.insertSelective(detail);
				
				if(CollectionUtils.isNotEmpty(detail.getCheckRouteContents())) {
					
					int i = 1;
					for(ToaCheckRouteContent toaCheckRouteContent : detail.getCheckRouteContents()) {
					
						toaCheckRouteContent.setId(ApplicationUtils.GUID32());
						toaCheckRouteContent.setRouteDetailId(routeDetailId);
						toaCheckRouteContent.setRouteId(routeId);
						toaCheckRouteContent.setSeq(i);
						toaCheckRouteContent.setCreateDate(new Date());
						toaCheckRouteContent.setUpdateDate(new Date());
						toaCheckRouteContent.setIsDeleted(Contants.IS_DELETED_FALSE);
						toaCheckRouteContent.setCreateUser(user.getUsercode());
						toaCheckRouteContent.setCreateUserName(user.getUsername());
						toaCheckRouteContent.setUpdateUser(user.getUsercode());
						toaCheckRouteContent.setUpdateUserName(user.getUsername());
						toaCheckRouteContent.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						toaCheckRouteContentMapper.insertSelective(toaCheckRouteContent);
						i++;
					}
				}
				
			}
			
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(ToaCheckRoute record) {
		record.setUpdateDate(new Date());
		record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		record.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		if(CollectionUtils.isNotEmpty(record.getCheckRouteDetails())) {
			
			deleteCheckRoteDetailByRouteId(record.getId());
			
			deleteCheckRouteContentByRouteId(record.getId());
			
			for(ToaCheckRouteDetail detail : record.getCheckRouteDetails()) {
				
				String routeDetailId = ApplicationUtils.GUID32();
				detail.setRouteId(record.getId());
				detail.setId(routeDetailId);
				detail.setCreateDate(new Date());
				detail.setUpdateDate(new Date());
				detail.setIsDeleted(Contants.IS_DELETED_FALSE);
				detail.setCreateUser(UserInfoHolder.getCurrentUserCode());
				detail.setCreateUserName(UserInfoHolder.getCurrentUserName());
				detail.setUpdateUser(UserInfoHolder.getCurrentUserCode());
				detail.setUpdateUserName(UserInfoHolder.getCurrentUserName());
				detail.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				toaCheckRouteDetailMapper.insertSelective(detail);
				
				if(CollectionUtils.isNotEmpty(detail.getCheckRouteContents())) {
					
					for(ToaCheckRouteContent toaCheckRouteContent : detail.getCheckRouteContents()) {
					
						toaCheckRouteContent.setId(ApplicationUtils.GUID32());
						toaCheckRouteContent.setRouteDetailId(routeDetailId);
						toaCheckRouteContent.setRouteId(record.getId());
						toaCheckRouteContent.setCreateDate(new Date());
						toaCheckRouteContent.setUpdateDate(new Date());
						toaCheckRouteContent.setIsDeleted(Contants.IS_DELETED_FALSE);
						toaCheckRouteContent.setCreateUser(UserInfoHolder.getCurrentUserCode());
						toaCheckRouteContent.setCreateUserName(UserInfoHolder.getCurrentUserName());
						toaCheckRouteContent.setUpdateUser(UserInfoHolder.getCurrentUserCode());
						toaCheckRouteContent.setUpdateUserName(UserInfoHolder.getCurrentUserName());
						toaCheckRouteContent.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						toaCheckRouteContentMapper.insertSelective(toaCheckRouteContent);
					}
				}
				
			}
			
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	private void deleteCheckRoteDetailByRouteId(String routeId) {
		
		Example example = new Example(ToaCheckRouteDetail.class);
		
		example.and().andEqualTo("routeId",routeId).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		
		ToaCheckRouteDetail record = new ToaCheckRouteDetail();
		
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		
		toaCheckRouteDetailMapper.updateByExampleSelective(record, example);
		
	}
	
	private void deleteCheckRouteContentByRouteId(String routeId) {
		
		Example example = new Example(ToaCheckRouteContent.class);
		
		example.and().andEqualTo("routeId",routeId).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		
		ToaCheckRouteContent record = new ToaCheckRouteContent();
		
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		
		toaCheckRouteContentMapper.updateByExampleSelective(record, example);
		
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		ToaCheckRoute record = new ToaCheckRoute();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public ToaCheckRoute selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		ToaCheckRoute record = mapper.selectByPrimaryKey(id);
		
		if(null!=record.getId()) {
			
			Example example = new Example(ToaCheckRouteDetail.class);
			
			example.createCriteria().andEqualTo("routeId",record.getId()).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
			
			example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
			
			List<ToaCheckRouteDetail> checkRouteDetails = toaCheckRouteDetailMapper.selectByExample(example);
			
			
			if(CollectionUtils.isNotEmpty(checkRouteDetails)) {
				
				for(ToaCheckRouteDetail detail : checkRouteDetails) {
					
					Example example2 = new Example(ToaCheckRouteContent.class);
					
					example2.createCriteria().andEqualTo("routeDetailId",detail.getId()).andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
					
					example2.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
					
					example2.orderBy("seq").asc();
					
					List<ToaCheckRouteContent> checkRouteContents = toaCheckRouteContentMapper.selectByExample(example2);
					
					detail.setCheckRouteContents(checkRouteContents);
				}
			}
			
			record.setCheckRouteDetails(checkRouteDetails);
		}
		
		
		return  record;
	}

	@Override
	public DataSet<ToaCheckRoute> getDataSetList(Page page, ToaCheckRoute record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<ToaCheckRoute> records = mapper.getList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	/**
	 * 
	* @Title: queryAddressByRouteId  
	* @Description: 根据路线Id查询路线下地点列表
	* @Params: @param routeId
	* @Params: @return      
	* @Return: List<ToaCheckRouteDetail>
	* <AUTHOR>
	* @date:2021年8月9日
	* @Throws
	 */
	public List<ToaCheckRouteDetail> queryAddressByRouteId(String routeId) {
		
		
		return toaCheckRouteDetailMapper.queryAddressByRouteId(routeId);
	}
	
	/**
	 * 
	* @Title: enable  
	* @Description: 启用路线
	* @Params: @param id
	* @Params: @return      
	* @Return: Integer
	* <AUTHOR>
	* @date:2021年8月13日
	* @Throws
	 */
	@Transactional(readOnly = false)
	public Integer enable(String id) {
		Assert.hasText(id, "ID不能为空.");
		ToaCheckRoute record = new ToaCheckRoute();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setStatus(0);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	/**
	 * 
	* @Title: deactivate  
	* @Description: 停用路线
	* @Params: @param id
	* @Params: @return      
	* @Return: Integer
	* <AUTHOR>
	* @date:2021年8月13日
	* @Throws
	 */
	@Transactional(readOnly = false)
	public Integer deactivate(String id) {
		Assert.hasText(id, "ID不能为空.");
		ToaCheckRoute record = new ToaCheckRoute();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setStatus(1);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}
	
	/**
	 * 
	* @Title: queryRouteContentByDetailId  
	* @Description: 根据路线下地点id查询地点检查内容列表
	* @Params: @param routeDetailId
	* @Params: @return      
	* @Return: List<ToaCheckRouteDetail>
	* <AUTHOR>
	* @date:2021年8月9日
	* @Throws
	 */
	public List<ToaCheckRouteContent> queryRouteContentByDetailId(String routeDetailId) {
		
		Example example = new Example(ToaCheckRouteContent.class);
		
		example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		
		example.and().andEqualTo("routeDetailId",routeDetailId);
		
		return toaCheckRouteContentMapper.selectByExample(example);
	}
	
	/**
	 * 
	* @Title: checkRouteName  
	* @Description: 新增修改时校验名称是否重复
	* @Params: @param routeName
	* @Params: @return      
	* @Return: boolean
	* <AUTHOR>
	* @date:2021年9月1日
	* @Throws
	 */
	public boolean checkRouteName(ToaCheckRoute record) {
		
		Example example = new Example(ToaCheckRoute.class);
		
		example.createCriteria().andEqualTo("isDeleted",Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		
		example.and().andEqualTo("routeName",record.getRouteName());
		
		
		if(StringUtils.isNotBlank(record.getId())) {
			
			example.and().andNotEqualTo("id", record.getId());
		}
		
		List<ToaCheckRoute> list = mapper.selectByExample(example);
		
		if(CollectionUtils.isEmpty(list)) {
			return true;
		}
		return false;
	}
}
