<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.party.dao.PartyBuildingApplyMapper">
	<resultMap id="BaseResultMap" type="cn.trasen.oa.party.model.PartyBuildingApply">
		<!-- WARNING - @mbg.generated -->
		<result column="id" jdbcType="VARCHAR" property="id" />
		<result column="apply_status" jdbcType="INTEGER" property="applyStatus" />
		<result column="apply_user_id" jdbcType="VARCHAR" property="applyUserId" />
		<result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
		<result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
		<result column="apply_dept_code" jdbcType="VARCHAR" property="applyDeptCode" />
		<result column="apply_dept_name" jdbcType="VARCHAR" property="applyDeptName" />
		<result column="apply_post" jdbcType="VARCHAR" property="applyPost" />
		<result column="apply_gender" jdbcType="VARCHAR" property="applyGender" />
		<result column="apply_age" jdbcType="VARCHAR" property="applyAge" />
		<result column="apply_birthday" jdbcType="DATE" property="applyBirthday" />
		<result column="apply_nation" jdbcType="VARCHAR" property="applyNation" />
		<result column="apply_highest_degree" jdbcType="VARCHAR" property="applyHighestDegree" />
		<result column="apply_school" jdbcType="VARCHAR" property="applySchool" />
		<result column="apply_phone" jdbcType="VARCHAR" property="applyPhone" />
		<result column="apply_date" jdbcType="DATE" property="applyDate" />
		<result column="apply_talker_code" jdbcType="VARCHAR" property="applyTalkerCode" />
		<result column="apply_talker_name" jdbcType="VARCHAR" property="applyTalkerName" />
		<result column="apply_talk_date" jdbcType="DATE" property="applyTalkDate" />
		<result column="apply_talk_remark" jdbcType="VARCHAR" property="applyTalkRemark" />
		<result column="apply_talk_files" jdbcType="VARCHAR" property="applyTalkFiles" />
		<result column="apply_talk_status" jdbcType="INTEGER" property="applyTalkStatus" />
		<result column="apply_remark" jdbcType="VARCHAR" property="applyRemark" />
		<result column="apply_files" jdbcType="VARCHAR" property="applyFiles" />
		<result column="activist_date" jdbcType="DATE" property="activistDate" />
		<result column="activist_remark" jdbcType="VARCHAR" property="activistRemark" />
		<result column="activist_files" jdbcType="VARCHAR" property="activistFiles" />
		<result column="develop_date" jdbcType="DATE" property="developDate" />
		<result column="develop_remark" jdbcType="VARCHAR" property="developRemark" />
		<result column="develop_files" jdbcType="VARCHAR" property="developFiles" />
		<result column="develop_introducer_id" jdbcType="VARCHAR" property="developIntroducerId" />
		<result column="develop_introducer_name" jdbcType="VARCHAR" property="developIntroducerName" />
		<result column="prepare_date" jdbcType="DATE" property="prepareDate" />
		<result column="prepare_remark" jdbcType="VARCHAR" property="prepareRemark" />
		<result column="prepare_files" jdbcType="VARCHAR" property="prepareFiles" />
		<result column="full_date" jdbcType="DATE" property="fullDate" />
		<result column="full_remark" jdbcType="VARCHAR" property="fullRemark" />
		<result column="full_files" jdbcType="VARCHAR" property="fullFiles" />
		<result column="branch_id" jdbcType="VARCHAR" property="branchId" />
		<result column="branch_name" jdbcType="VARCHAR" property="branchName" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
		<result column="is_inflow" jdbcType="INTEGER" property="isInflow" />
		<result column="talk_create_date" jdbcType="TIMESTAMP" property="talkCreateDate" />
		<result column="activist_create_date" jdbcType="TIMESTAMP" property="activistCreateDate" />
		<result column="develop_create_date" jdbcType="TIMESTAMP" property="developCreateDate" />
		<result column="prepare_create_date" jdbcType="TIMESTAMP" property="prepareCreateDate" />
		<result column="full_create_date" jdbcType="TIMESTAMP" property="fullCreateDate" />
		<result column="head_img" jdbcType="VARCHAR" property="headImg" />
		<result column="apply_identitynumber" jdbcType="VARCHAR" property="applyIdentitynumber" />
		<result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
		<result column="organization_short_name" jdbcType="VARCHAR" property="organizationShortName" />
		<result column="inflow_date" jdbcType="DATE" property="inflowDate" />
		<result column="inflow_remark" jdbcType="VARCHAR" property="inflowRemark" />
		<result column="inflow_files" jdbcType="VARCHAR" property="inflowFiles" />
		<result column="outflow_branch_name" jdbcType="VARCHAR" property="outflowBranchName" />
		<result column="outflow_place" jdbcType="VARCHAR" property="outflowPlace" />
		<result column="outflow_place_name" jdbcType="VARCHAR" property="outflowPlaceName" />
		<result column="outflow_place_phone" jdbcType="VARCHAR" property="outflowPlacePhone" />
		<result column="apply_org_attributes" jdbcType="VARCHAR" property="applyOrgAttributes" />
		<result column="activist_recommended_files" jdbcType="VARCHAR" property="activistRecommendedFiles" />
		<result column="activist_inspection_registration_files" jdbcType="VARCHAR"
			property="activistInspectionRegistrationFiles" />
		<result column="activist_cultivate_files" jdbcType="VARCHAR" property="activistCultivateFiles" />
		<result column="develop_autobiography_files" jdbcType="VARCHAR" property="developAutobiographyFiles" />
		<result column="develop_solicit_opinions_files" jdbcType="VARCHAR" property="developSolicitOpinionsFiles" />
		<result column="develop_confirm_meeting_files" jdbcType="VARCHAR" property="developConfirmMeetingFiles" />
		<result column="develop_training_form_files" jdbcType="VARCHAR" property="developTrainingFormFiles" />
		<result column="develop_political_review_files" jdbcType="VARCHAR" property="developPoliticalReviewFiles" />
		<result column="prepare_examination_files" jdbcType="VARCHAR" property="prepareExaminationFiles" />
		<result column="prepare_publicity_situation_files" jdbcType="VARCHAR" property="preparePublicitySituationFiles" />
		<result column="prepare_prequalification_files" jdbcType="VARCHAR" property="preparePrequalificationFiles" />
		<result column="prepare_receive_record_files" jdbcType="VARCHAR" property="prepareReceiveRecordFiles" />
		<result column="full_oath_situation_files" jdbcType="VARCHAR" property="fullOathSituationFiles" />
		<result column="full_educational_inspection_files" jdbcType="VARCHAR" property="fullEducationalInspectionFiles" />
		<result column="full_formal_application_files" jdbcType="VARCHAR" property="fullFormalApplicationFiles" />
		<result column="full_opinion_meeting_files" jdbcType="VARCHAR" property="fullOpinionMeetingFiles" />
		<result column="full_publicity_situation_files" jdbcType="VARCHAR" property="fullPublicitySituationFiles" />
		<result column="full_receive_record_files" jdbcType="VARCHAR" property="fullReceiveRecordFiles" />
	</resultMap>

	<update id="delTalk" parameterType="cn.trasen.oa.party.model.PartyBuildingApply">
		update toa_party_building_apply set apply_talker_code = null,apply_talker_name = null,
		apply_talk_status = 0,apply_talk_remark = null,apply_talk_date = null,apply_talk_files = null where 1=1
		<!-- <if test=" id !=null and id !='' "> -->
		and id=#{id}
		<!-- </if > -->
	</update>

	<select id="selectApplyStatusNumber" parameterType="cn.trasen.oa.party.model.PartyBuildingApply"
		resultType="Map">
		select ifnull(count(1),0) as number,ifnull(sum(case when apply_status=0 then 1 else 0 end),0)
		applyNumber,ifnull(sum(case when apply_status=1 then 1 else 0 end),0) activistNumber,
		ifnull(sum(case when apply_status=3 then 1 else 0 end),0) developNumber,ifnull(sum(case when apply_status=4 then 1 else 0 end),0)
		prepareNumber,ifnull(sum(case when apply_status=5 then 1 else 0 end),0) fullNumber
		from toa_party_building_apply where is_deleted = 'N'
		<if test=" createUser !=null and createUser !=''  ">
			and create_user=#{createUser}
		</if>
	</select>

	<select id="selectPersonByApplyStatus" parameterType="cn.trasen.oa.party.model.PartyBuildingApply"
		resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply where is_deleted = 'N'
		<if test=" applyUserName !=null and applyUserName !='' ">
			and apply_user_name like concat(concat('%', #{applyUserName}), '%')
		</if>
		<if test=" applyDeptCode !=null and applyDeptCode !='' ">
			and apply_dept_code=#{applyDeptCode}
		</if>
		<if test=" applyTalkStatus !=null  ">
			and apply_talk_status=#{applyTalkStatus}
		</if>
		<if test=" applyTalkerCode !=null and applyTalkerCode !=''  ">
			and apply_talker_code=#{applyTalkerCode}
		</if>
		<if test=" createUser !=null and createUser !=''  ">
			and create_user=#{createUser}
		</if>
		<if test=" applyStatus !=null and  applyStatus == 0 "><!-- 谈话人 -->
			and apply_status=0
		</if>
		<choose>
			<when test="_databaseId=='kingbase'">
				<if test=" applyStatus !=null and  applyStatus == 1 "><!--列为入党积极分子:1.已谈话;2.申请日期已满6个月 -->
					and apply_status=0 and apply_talk_status=1 and DATE_FORMAT(now(),'%Y-%m-%d') >= add_months(apply_date,6)
				</if>
				<if test=" applyStatus !=null and  applyStatus == 3 "><!--拟定发展对象:1.已被列为入党积极分子;2.入党积极分子日期已满1年 -->
					and apply_status=1 and DATE_FORMAT(now(),'%Y-%m-%d') >= add_months(activist_date,12)
				</if>
				<if test=" applyStatus !=null and  applyStatus == 4 "><!--拟定预备党员:1.已被拟定发展对象;2.拟定发展对象日期已满1年 -->
					and apply_status=3 and DATE_FORMAT(now(),'%Y-%m-%d') >= add_months(develop_date,12)
				</if>
				<if test=" applyStatus !=null and  applyStatus == 5 "><!--正式党员:1.已被拟定预备党员;2.申请日期已满6个月 -->
					and apply_status=4 and adddate(CURDATE(), -7 ) >= add_months(prepare_date,12)
				</if>			
			</when>
			<otherwise>
				<if test=" applyStatus !=null and  applyStatus == 1 "><!--列为入党积极分子:1.已谈话;2.申请日期已满6个月 -->
					and apply_status=0 and apply_talk_status=1 and DATE_FORMAT(now(),'%Y-%m-%d') >=
					date_add(DATE_FORMAT(apply_date,'%Y-%m-%d'),interval 6 month)
				</if>
				<if test=" applyStatus !=null and  applyStatus == 3 "><!--拟定发展对象:1.已被列为入党积极分子;2.入党积极分子日期已满1年 -->
					and apply_status=1 and DATE_FORMAT(now(),'%Y-%m-%d') >= date_add(DATE_FORMAT(activist_date,'%Y-%m-%d'),interval 1
					year)
				</if>
				<if test=" applyStatus !=null and  applyStatus == 4 "><!--拟定预备党员:1.已被拟定发展对象;2.拟定发展对象日期已满1年 -->
					and apply_status=3 and DATE_FORMAT(now(),'%Y-%m-%d') >= date_add(DATE_FORMAT(develop_date,'%Y-%m-%d'),interval 1
					year)
				</if>
				<if test=" applyStatus !=null and  applyStatus == 5 "><!--正式党员:1.已被拟定预备党员;2.申请日期已满6个月 -->
					and apply_status=4 and date_add(DATE_FORMAT(now(),'%Y-%m-%d'),interval -7 day) >=
					date_add(DATE_FORMAT(prepare_date,'%Y-%m-%d'),interval 1 year)
				</if>
			</otherwise>
		</choose>
		<if test=" isInflow !=null  ">
			and is_inflow=#{isInflow}
		</if>
		<if test="isInflowList != null and isInflowList.size() > 0">
			and (is_inflow in
			<foreach collection="isInflowList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
	</select>

	<select id="selectApplyAgeEducation" parameterType="cn.trasen.oa.party.model.PartyBuildingApply"
		resultType="Map">
		select count(id) as hjzs,sum(case when apply_gender in ('1','女') then 1 else 0 end) xb_nv,sum(case when apply_gender
		in ('0','男') then 1 else 0 end) xb_nan,sum(case when apply_nation !='汉族' and apply_nation is not null then 1 else 0
		end) ssmz,
		<choose>
			<when test="_databaseId=='kingbase'">
				sum(case when apply_birthday> add_months(CURDATE(),-300) then 1 else 0 end) nl_25,
				sum(case when apply_birthday between add_months(CURDATE(), -360) and add_months(CURDATE(),-300) then 1 else 0 end) nl_26,
				sum(case when apply_birthday between add_months(CURDATE(), -420) and add_months(CURDATE(),-360) then 1 else 0 end)
				nl_31,
				sum(case when apply_birthday between add_months(CURDATE(),-480 ) and add_months(CURDATE(),-420) then 1 else 0 end) nl_36,
				sum(case when apply_birthday between add_months(CURDATE(),-540) and add_months(CURDATE(),-480) then 1 else 0 end)
				nl_41,
				sum(case when apply_birthday between add_months(CURDATE(),-720) and add_months(CURDATE(),-540) then 1 else 0 end) nl_46,
				sum(case when add_months(CURDATE(),-720) > apply_birthday then 1 else 0 end) nl_61,
			</when>
			<otherwise>
				sum(case when apply_birthday> date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -25 year) then 1 else 0 end) nl_25,
				sum(case when apply_birthday between date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -30 year) and date_add(DATE_FORMAT(now()
				,'%Y-%m-%d'),interval -25 year) then 1 else 0 end) nl_26,
				sum(case when apply_birthday between date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -35 year) and date_add(DATE_FORMAT(now()
				,'%Y-%m-%d'),interval -30 year) then 1 else 0 end) nl_31,
				sum(case when apply_birthday between date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -40 year) and date_add(DATE_FORMAT(now()
				,'%Y-%m-%d'),interval -35 year) then 1 else 0 end) nl_36,
				sum(case when apply_birthday between date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -45 year) and date_add(DATE_FORMAT(now()
				,'%Y-%m-%d'),interval -40 year) then 1 else 0 end) nl_41,
				sum(case when apply_birthday between date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -60 year) and date_add(DATE_FORMAT(now()
				,'%Y-%m-%d'),interval -45 year) then 1 else 0 end) nl_46,
				sum(case when date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -60 year) > apply_birthday then 1 else 0 end) nl_61,
			</otherwise>
		</choose>
		sum(case when apply_highest_degree in ('博士','7') then 1 else 0 end) xl_bs,sum(case when apply_highest_degree in ('研究生','1')
		then 1 else 0 end) xl_yjs,
		sum(case when apply_highest_degree in ('本科','2') then 1 else 0 end) xl_dxbk,sum(case when apply_highest_degree in ('大专','3')
		then 1 else 0 end) xl_dxzk,
		sum(case when apply_highest_degree in ('中专','4') then 1 else 0 end) xl_zz,sum(case when apply_highest_degree in ('高中','5')
		then 1 else 0 end) xl_gzzj,
		sum(case when apply_highest_degree in ('初中及以下','6') then 1 else 0 end) xl_czjyx,sum(case when apply_highest_degree in
		('规培','9') then 1 else 0 end) xl_gp,
		sum(case when apply_highest_degree in ('硕士','10') then 1 else 0 end) xl_ss
		from toa_party_building_apply where is_deleted='N' and apply_status in (4,5)
		<if test=" prepareStartDate !=null and prepareStartDate !='' and prepareEndDate !=null and prepareEndDate !='' "><!-- 拟定预备党员日期 -->
			and prepare_date between #{prepareStartDate} and #{prepareEndDate}
		</if>
		<if test=" createUser !=null and createUser !=''  ">
			and create_user=#{createUser}
		</if>
		<if test="orgAttributesList != null and orgAttributesList.size() > 0">
			and (apply_org_attributes in
			<foreach collection="orgAttributesList" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			)
		</if>
	</select>

	<select id="selectTalkerTwoDay" resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply 
		where is_deleted='N' and apply_talk_status=0 
		<choose>
			<when test="_databaseId=='kingbase'">
				and DATE_FORMAT(create_date,'%Y-%m-%d') = adddate(CURDATE(),-2)
			</when>
			<otherwise>
				and DATE_FORMAT(create_date,'%Y-%m-%d') = date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -2 day)
			</otherwise>
		</choose> 
	</select>

	<select id="selectTalkerThirtyDay" resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply 
		where is_deleted='N' and apply_talk_status=0
		<choose>
			<when test="_databaseId=='kingbase'">
				and DATE_FORMAT(apply_date,'%Y-%m-%d') = adddate(CURDATE(), -30)
			</when>
			<otherwise>
				and DATE_FORMAT(apply_date,'%Y-%m-%d') = date_add(DATE_FORMAT(now() ,'%Y-%m-%d'),interval -30 day)
			</otherwise>
		</choose>
	</select>

	<select id="selectActivistMessage" resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply 
		where is_deleted='N' and apply_status=0 and apply_talk_status=1 
		<choose>
			<when test="_databaseId=='kingbase'">
				and DATE_FORMAT(now(),'%Y-%m-%d')=add_months(apply_date,6)
			</when>
			<otherwise>
				and DATE_FORMAT(now(),'%Y-%m-%d')=date_add(DATE_FORMAT(apply_date,'%Y-%m-%d'),interval 6 month)
			</otherwise>
		</choose>
	</select>

	<select id="selectDevelopMessage" resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply 
		where is_deleted='N' and apply_status=1
		<choose>
			<when test="_databaseId=='kingbase'">
				and DATE_FORMAT(now(),'%Y-%m-%d') = add_months(activist_date,12)
			</when>
			<otherwise>
				and DATE_FORMAT(now(),'%Y-%m-%d') = date_add(DATE_FORMAT(activist_date,'%Y-%m-%d'),interval 1 year)
			</otherwise>
		</choose>
	</select>
	<select id="selectPrepareMessage" resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply 
		where is_deleted='N' and apply_status=3 
		<choose>
			<when test="_databaseId=='kingbase'">
				and DATE_FORMAT(now(),'%Y-%m-%d') = add_months(develop_date,12)
			</when>
			<otherwise>
				and DATE_FORMAT(now(),'%Y-%m-%d') = date_add(DATE_FORMAT(develop_date,'%Y-%m-%d'),interval 1 year)
			</otherwise>
		</choose>
	</select>
	<select id="selectFullMessage" resultType="cn.trasen.oa.party.model.PartyBuildingApply">
		select * from toa_party_building_apply 
		where is_deleted='N' and apply_status=4 
		<choose>
			<when test="_databaseId=='kingbase' or _databaseId=='postgresql'">
				and adddate(CURDATE(),-7) = add_months(prepare_date,12)
			</when>
			<otherwise>
				and date_add(DATE_FORMAT(now(),'%Y-%m-%d'),interval -7 day) = date_add(DATE_FORMAT(prepare_date,'%Y-%m-%d'),interval 1 year)
			</otherwise>
		</choose> 
	</select>
</mapper>