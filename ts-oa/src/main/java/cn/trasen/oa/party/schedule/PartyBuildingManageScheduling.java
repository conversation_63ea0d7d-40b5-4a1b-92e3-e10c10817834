package cn.trasen.oa.party.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import cn.trasen.oa.party.service.PartyBuildingApplyService;
import cn.trasen.oa.party.service.PartyBuildingManageService;
import cn.trasen.oa.party.service.PartyBuildingTeamService;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class PartyBuildingManageScheduling {

	@Autowired
	private PartyBuildingManageService partyBuildingManageService; 
	
	@Autowired
	private PartyBuildingTeamService partyBuildingTeamService;
	
	@Autowired
	private PartyBuildingApplyService partyBuildingApplyService;
	
	//10分钟同步一次
	//@Scheduled(cron = "0 */10 * * * ?")
	@Scheduled(cron = "0 0 1 * * ?")//每天凌晨1点同步一次
	public void timeSynchronizationParty() {
		//同步人员档案党员数据
		try {
			partyBuildingManageService.timeSynchronizationParty();
		} catch (Exception e) {
			log.error("同步人员档案党员数据" + e.getMessage(), e);
		}
	}
	
		//换届到期提醒：换届到期前一周早上8:00
		@Scheduled(cron = "0 0 8 * * ?")
		public void timePartyBuildingTeamExpiration() {
			//同步人员档案党员数据
			try {
				partyBuildingTeamService.timePartyBuildingTeamExpiration();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
		
		
		//创建日期超过2天还未填报谈话记录、提醒
		@Scheduled(cron = "0 5 8 * * ?")
		public void timeTalkerTwoDay() {
			//同步人员档案党员数据
			try {
				partyBuildingApplyService.timeTalkerTwoDay();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
		
		//申请日期30天还未填报谈话记录、提醒
		@Scheduled(cron = "0 5 8 * * ?")
		public void timeTalkerThirtyDay() {
			//同步人员档案党员数据
			try {
				partyBuildingApplyService.timeTalkerThirtyDay();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
		
		//入党申请日期已满6个月+已上传谈话记录、可拟定为积极分子提醒
		@Scheduled(cron = "0 10 8 * * ?")
		public void timeActivistMessage() {
			//同步人员档案党员数据
			try {
				partyBuildingApplyService.timeActivistMessage();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
		
		//列为入党积极分子日期满一年+已被列为入党积极分子、可拟定为发展对象提醒
		@Scheduled(cron = "0 10 8 * * ?")
		public void timeDevelopMessage() {
			//同步人员档案党员数据
			try {
				partyBuildingApplyService.timeDevelopMessage();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
		
		//已被拟定发展对象+拟定发展对象日期满一年、可拟定为预备党员提醒
		@Scheduled(cron = "0 10 8 * * ?")
		public void timePrepareMessage() {
			//同步人员档案党员数据
			try {
				partyBuildingApplyService.timePrepareMessage();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
		
		//拟定预备党员日期满1年前一周、可拟定为正式党员提醒
		@Scheduled(cron = "0 10 8 * * ?")
		public void timeFullMessage() {
			//同步人员档案党员数据
			try {
				partyBuildingApplyService.timeFullMessage();
			} catch (Exception e) {
				log.error("同步人员档案党员数据" + e.getMessage(), e);
			}
		}
}
