package cn.trasen.oa.vehicle.model;

import io.swagger.annotations.*;

import java.util.Date;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_vehicle_apply_refuel")
@Setter
@Getter
public class VehicleApplyRefuel {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 预约记录id
     */
    @Column(name = "apply_id")
    @ApiModelProperty(value = "预约记录id")
    private String applyId;

    /**
     * 车辆id
     */
    @Column(name = "vehicle_id")
    @ApiModelProperty(value = "车辆id")
    private String vehicleId;

    /**
     * 加油日期
     */
    @Column(name = "refuel_date")
    @ApiModelProperty(value = "加油日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date refuelDate;

    /**
     * 加油升数
     */
    @Column(name = "refuel_litre")
    @ApiModelProperty(value = "加油升数")
    private String refuelLitre;

    /**
     * 支付方式
     */
    @Column(name = "refuel_pay")
    @ApiModelProperty(value = "支付方式  1现金支付   2油卡支付  3其他   4现金加油")
    private String refuelPay;

    /**
     * 加油类型
     */
    @Column(name = "refuel_type")
    @ApiModelProperty(value = "加油类型")
    private String refuelType;
    
    
    @Column(name = "refuel_id")
    @ApiModelProperty(value = "油卡id")
    private String refuelId;

    /**
     * 油卡卡号
     */
    @Column(name = "refuel_no")
    @ApiModelProperty(value = "油卡卡号")
    private String refuelNo;

    /**
     * 费用
     */
    @Column(name = "refuel_price")
    @ApiModelProperty(value = "油卡费用")
    private String refuelPrice;
    
    /**
     * 
     */
    @Column(name = "cash_price")
    @ApiModelProperty(value = "现金费用")
    private String cashPrice;
    

    /**
     * 附件
     */
    @Column(name = "refuel_files")
    @ApiModelProperty(value = "附件")
    private String refuelFiles;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
}