package cn.trasen.oa.vehicle.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.vehicle.model.VehicleDriverAccident;
import cn.trasen.oa.vehicle.service.VehicleDriverAccidentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName VehicleDriverAccidentController
 * @Description TODO
 * @date 2023��5��4�� ����11:29:07
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "司机事故记录")
public class VehicleDriverAccidentController {

	private transient static final Logger logger = LoggerFactory.getLogger(VehicleDriverAccidentController.class);

	@Autowired
	private VehicleDriverAccidentService vehicleDriverAccidentService;

	/**
	 * @Title saveVehicleDriverAccident
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��4�� ����11:29:07
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/vehicleDriverAccident/save")
	public PlatformResult<String> saveVehicleDriverAccident(@RequestBody VehicleDriverAccident record) {
		try {
			vehicleDriverAccidentService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateVehicleDriverAccident
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��5��4�� ����11:29:07
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/vehicleDriverAccident/update")
	public PlatformResult<String> updateVehicleDriverAccident(@RequestBody VehicleDriverAccident record) {
		try {
			vehicleDriverAccidentService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectVehicleDriverAccidentById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<VehicleDriverAccident>
	 * @date 2023��5��4�� ����11:29:07
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/vehicleDriverAccident/{id}")
	public PlatformResult<VehicleDriverAccident> selectVehicleDriverAccidentById(@PathVariable String id) {
		try {
			VehicleDriverAccident record = vehicleDriverAccidentService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteVehicleDriverAccidentById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��5��4�� ����11:29:07
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/vehicleDriverAccident/delete/{id}")
	public PlatformResult<String> deleteVehicleDriverAccidentById(@PathVariable String id) {
		try {
			vehicleDriverAccidentService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectVehicleDriverAccidentList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<VehicleDriverAccident>
	 * @date 2023��5��4�� ����11:29:07
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/vehicleDriverAccident/list")
	public DataSet<VehicleDriverAccident> selectVehicleDriverAccidentList(Page page, VehicleDriverAccident record) {
		return vehicleDriverAccidentService.getDataSetList(page, record);
	}
}
