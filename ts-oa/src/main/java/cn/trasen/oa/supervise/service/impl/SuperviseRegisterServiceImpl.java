package cn.trasen.oa.supervise.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.supervise.dao.SuperviseRegisterMapper;
import cn.trasen.oa.supervise.model.OperationData;
import cn.trasen.oa.supervise.model.SuperviseLogs;
import cn.trasen.oa.supervise.model.SuperviseRegister;
import cn.trasen.oa.supervise.model.SuperviseTask;
import cn.trasen.oa.supervise.model.SuperviseType;
import cn.trasen.oa.supervise.service.SuperviseLogsService;
import cn.trasen.oa.supervise.service.SuperviseRegisterService;
import cn.trasen.oa.supervise.service.SuperviseTaskService;
import cn.trasen.oa.supervise.service.SuperviseTypeService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName SuperviseRegisterServiceImpl
 * @Description TODO
 * @date 2024��3��25�� ����3:46:57
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class SuperviseRegisterServiceImpl implements SuperviseRegisterService {

	@Autowired
	private SuperviseRegisterMapper mapper;
	
	@Autowired
	private SuperviseLogsService superviseLogsService;
	
	@Autowired
	private SuperviseTaskService superviseTaskService;
	
	@Autowired
	private SuperviseTypeService superviseTypeService;
	
	@Autowired
	private InformationFeignService informationFeignService;
	
	@Autowired
	AppConfigProperties appConfigProperties;

	@Transactional(readOnly = false)
	@Override
	public Integer save(SuperviseRegister record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		record.setActualDate(record.getCompleteDate());
		
		Integer overdueDay = record.getOverdueDay();
		if(null == overdueDay) {
			overdueDay = 1;
		}
		
		record.setOverdueDay(overdueDay);
		
		Date actualDate = record.getCompleteDate(); //完成时间
		
		long betweenDay = DateUtil.betweenDay(new Date(), actualDate, true); 
		
		//0未超期  1即将超期  2已超期
		if(actualDate.getTime() >= new Date().getTime()) {
			record.setIsOverdue("0");
		}
		if(betweenDay <= overdueDay) {
			record.setIsOverdue("1");
		} 
		if(actualDate.getTime() <= new Date().getTime()) {
			record.setIsOverdue("2");
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(record.getRegisterType());
		
		//督办人设置
		if(!"0".equals(record.getRegisterStatus())){
			
			record.setRegisterUser(superviseType.getSuperviseUserCode()); 
			record.setRegisterName(superviseType.getSuperviseUserName());

		}
		
		String registerUrgency = record.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		//通知督办人
		if("1".equals(record.getRegisterStatus())) {
			StringBuffer content = new StringBuffer();
			content.append(user.getUsername()).append("登记了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
			content.append("-").append(record.getRegisterMatter()).append(",任务当前进度为【待指派】,等待您的处理！");
			
			sendMessage(record.getRegisterUser(), content,"【待指派提醒】" + record.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + record.getId() + "&form=supervision&index=0&menuIndex=1");
		}
		
		//任务
		String copyLeader = "";
		if(CollectionUtils.isNotEmpty(record.getSuperviseTaskList())) {
			
			if(record.getSuperviseTaskList().size() > 1) {
				record.setMultipleDept("1");
			}else {
				record.setMultipleDept("0");
			}
			
			List<SuperviseTask> superviseTaskList = record.getSuperviseTaskList();
			
			for (SuperviseTask superviseTask : superviseTaskList) {
				
				superviseTask.setTaskStatus(record.getRegisterStatus());
				superviseTask.setRegisterId(record.getId());
				
				Map<String,String> map = mapper.selectOrgByTaskCode(superviseTask.getTaskCode());
				
				superviseTask.setTaskDeptCode(map.get("organization_id"));
				superviseTask.setTaskDeptName(map.get("name"));
				
				//通知承办人
				if("2".equals(record.getRegisterStatus())) {
					StringBuffer content = new StringBuffer();
					content.append(user.getUsername()).append("指派了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
					content.append("-").append(record.getRegisterMatter()).append(",任务当前进度为【办理中】,等待您的处理！");
					
					sendMessage(superviseTask.getTaskCode(), content,"【待办理提醒】" + record.getRegisterMatter(),"/ts-web-oa/task/task-handling",
							appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + record.getId() + "&form=handling&index=0&menuIndex=2");
				}
				
				
				//通知分管领导
				if("1".equals(superviseTask.getCopyLeader()) && "2".equals(record.getRegisterStatus())) {
					copyLeader = mapper.selectCopyLeader(superviseTask.getTaskCode());
					
					if(StringUtils.isNotBlank(copyLeader)) {
						
						StringBuffer content = new StringBuffer();
						content.append(user.getUsername()).append("抄送了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
						content.append("-").append(record.getRegisterMatter()).append(",任务当前进度为【办理中】,处理人为【").append(superviseTask.getTaskName());
						content.append("】,请悉知！");
						
						sendMessage(copyLeader, content,"【抄送提醒】" + record.getRegisterMatter(),"/ts-web-oa/task/task-to-my",
								appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + record.getId() + "&form=copy&index=-1&menuIndex=3");
					}
				}
				
				superviseTaskService.save(superviseTask);
			}
		}
		
		StringBuffer leaderCodes = new StringBuffer();
		StringBuffer leaderNames = new StringBuffer();
		if(StringUtils.isNoneBlank(copyLeader)) {
			String[] copyLeaders = copyLeader.split(",");
			List<Map<String,String>> leader = mapper.selectCopyLeaderName(Arrays.asList(copyLeaders));
			for (Map<String, String> map : leader) {
				leaderCodes.append(map.get("employee_no")).append(",");
				leaderNames.append(map.get("employee_name")).append(",");
			}
			
			leaderCodes = leaderCodes.deleteCharAt(leaderCodes.length() - 1); 
			leaderNames = leaderNames.deleteCharAt(leaderNames.length() - 1); 
			
			record.setCopyLeaderCode(leaderCodes.toString());
			record.setCopyLeaderName(leaderNames.toString());
			
		}
		
		
		//登记或者指派记录登记日志
		if("1".equals(record.getRegisterStatus()) || "2".equals(record.getRegisterStatus())) { 
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("1");
			superviseLogs.setRegisterId(record.getId());
			superviseLogsService.save(superviseLogs);
		}
		
		//指派操作记录指派日志
		if("2".equals(record.getRegisterStatus())) {
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("2");
			superviseLogs.setRegisterId(record.getId());
			superviseLogs.setLogRemark(record.getTaskDesc());
			superviseLogs.setLogFile(record.getTaskFile());
			superviseLogs.setLogDate(record.getTaskDate());
			superviseLogs.setCopyLeaderName(leaderNames.toString());
			superviseLogsService.save(superviseLogs);
		}
		
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(SuperviseRegister record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		Integer overdueDay = record.getOverdueDay();
		if(null == overdueDay) {
			overdueDay = 1;
		}
		
		record.setOverdueDay(overdueDay);
		
		Date actualDate = record.getCompleteDate(); //完成时间
		
		long betweenDay = DateUtil.betweenDay(new Date(), actualDate, true); 
		
		//0未超期  1即将超期  2已超期
		if(actualDate.getTime() >= new Date().getTime()) {
			record.setIsOverdue("0");
		}
		if(betweenDay <= overdueDay) {
			record.setIsOverdue("1");
		} 
		if(actualDate.getTime() <= new Date().getTime()) {
			record.setIsOverdue("2");
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(record.getRegisterType());
		
		if(!"0".equals(record.getRegisterStatus())){
			//督办人设置
			record.setRegisterUser(superviseType.getSuperviseUserCode()); 
			record.setRegisterName(superviseType.getSuperviseUserName());

		}
		
		record.setActualDate(record.getCompleteDate());
		
		String registerUrgency = record.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		//通知督办人
		if("1".equals(record.getRegisterStatus())) {
			StringBuffer content = new StringBuffer();
			content.append(user.getUsername()).append("登记了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
			content.append("-").append(record.getRegisterMatter()).append(",任务当前进度为【待指派】,等待您的处理！");
			
			sendMessage(record.getRegisterUser(), content,"【待指派提醒】" + record.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + record.getId() + "&form=supervision&index=0&menuIndex=1");
		}
		
		//任务
		String copyLeader = "";
		if(CollectionUtils.isNotEmpty(record.getSuperviseTaskList())) {
			
			superviseTaskService.deleteByRegisterId(record.getId());
			
			if(record.getSuperviseTaskList().size() > 1) {
				record.setMultipleDept("1");
			}else {
				record.setMultipleDept("0");
			}
			
			List<SuperviseTask> superviseTaskList = record.getSuperviseTaskList();
			for (SuperviseTask superviseTask : superviseTaskList) {
				
				superviseTask.setTaskStatus(record.getRegisterStatus());
				superviseTask.setRegisterId(record.getId());
				
				Map<String,String> map = mapper.selectOrgByTaskCode(superviseTask.getTaskCode());
				
				superviseTask.setTaskDeptCode(map.get("organization_id"));
				superviseTask.setTaskDeptName(map.get("name"));
				
				superviseTaskService.save(superviseTask);
				
				//通知承办人
				if("2".equals(record.getRegisterStatus())) {
					StringBuffer content = new StringBuffer();
					content.append(user.getUsername()).append("指派了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
					content.append("-").append(record.getRegisterMatter()).append(",任务当前进度为【办理中】,等待您的处理！");
					
					sendMessage(superviseTask.getTaskCode(), content,"【待办理提醒】" + record.getRegisterMatter(),"/ts-web-oa/task/task-handling",
							appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + record.getId() + "&form=handling&index=0&menuIndex=2");
				}
				
				//通知分管领导
				if("1".equals(superviseTask.getCopyLeader()) && "2".equals(record.getRegisterStatus())) {
					
					copyLeader = mapper.selectCopyLeader(superviseTask.getTaskCode());
					
					if(StringUtils.isNotBlank(copyLeader)) {
						
						StringBuffer content = new StringBuffer();
						content.append(user.getUsername()).append("抄送了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
						content.append("-").append(record.getRegisterMatter()).append(",任务当前进度为【办理中】,处理人为【").append(superviseTask.getTaskName());
						content.append("】,请悉知！");
						
						sendMessage(copyLeader, content,"【抄送提醒】" + record.getRegisterMatter(),"/ts-web-oa/task/task-to-my",
								appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + record.getId() + "&form=copy&index=-1&menuIndex=3");
					}
				}
			}
		}
		
		StringBuffer leaderCodes = new StringBuffer();
		StringBuffer leaderNames = new StringBuffer();
		if(StringUtils.isNoneBlank(copyLeader)) {
			String[] copyLeaders = copyLeader.split(",");
			List<Map<String,String>> leader = mapper.selectCopyLeaderName(Arrays.asList(copyLeaders));
			for (Map<String, String> map : leader) {
				leaderCodes.append(map.get("employee_no")).append(",");
				leaderNames.append(map.get("employee_name")).append(",");
			}
			
			leaderCodes = leaderCodes.deleteCharAt(leaderCodes.length() - 1); 
			leaderNames = leaderNames.deleteCharAt(leaderNames.length() - 1); 
			
			record.setCopyLeaderCode(leaderCodes.toString());
			record.setCopyLeaderName(leaderNames.toString());
			
		}
		
		//登记或者指派记录登记日志
		if("1".equals(record.getRegisterStatus()) || "2".equals(record.getRegisterStatus())) { 
			
			List<SuperviseLogs> superviseLogsList = superviseLogsService.getSuperviseLogsList(record.getId());
			
			boolean flag = true;
			for (SuperviseLogs superviseLogs : superviseLogsList) {
				if("1".equals(superviseLogs.getLogType())) {
					flag = false;
					break;
				}
			}
			
			if(flag) {
				SuperviseLogs superviseLogs = new SuperviseLogs();
				superviseLogs.setLogType("1");
				superviseLogs.setRegisterId(record.getId());
				superviseLogsService.save(superviseLogs);
			}
		}
		
		//指派操作记录指派日志
		if("2".equals(record.getRegisterStatus())) {
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("2");
			superviseLogs.setRegisterId(record.getId());
			superviseLogs.setLogRemark(record.getTaskDesc());
			superviseLogs.setLogFile(record.getTaskFile());
			superviseLogs.setLogDate(record.getTaskDate());
			superviseLogsService.save(superviseLogs);
		}
		
		return mapper.updateByPrimaryKey(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		SuperviseRegister record = new SuperviseRegister();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public SuperviseRegister selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(id);
		
		List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(id);
		
		StringBuffer taskNames = new StringBuffer();
		for (SuperviseTask superviseTask : taskList) {
			taskNames.append(superviseTask.getTaskName()).append(",");
		}
		if (StringUtils.isNotBlank(taskNames.toString())) {
			taskNames = taskNames.deleteCharAt(taskNames.length() - 1);
			superviseRegister.setAssigneeNames(taskNames.toString());
		}
		superviseRegister.setSuperviseTaskList(taskList);
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		superviseRegister.setRegisterTypeName(superviseType.getTypeName());
		
		//当前办理人
		if("1".equals(superviseRegister.getRegisterStatus())) { 
			superviseRegister.setCurrentHandleUser(superviseRegister.getRegisterName());
		}else if("2".equals(superviseRegister.getRegisterStatus())) { 
			superviseRegister.setCurrentHandleUser(superviseRegister.getAssigneeNames());
		}else if("3".equals(superviseRegister.getRegisterStatus())) { 
			superviseRegister.setCurrentHandleUser(superviseRegister.getCheckName());
		}else if("4".equals(superviseRegister.getRegisterStatus())) { 
			superviseRegister.setCurrentHandleUser(superviseRegister.getApproveName());
		}else {
			superviseRegister.setCurrentHandleUser(null);
		}
		
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<SuperviseRegister> getDataSetList(Page page, SuperviseRegister record) {
		
		//Boolean right = UserInfoHolder.getRight("SUPERVISE_MASTER");
		
		if("1".equals(record.getIndex()) || "3".equals(record.getIndex()) || "1".equals(record.getTaskIndex()) || "2".equals(record.getTaskIndex())) {
			record.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
		}else if("2".equals(record.getIndex())) {
			record.setRegisterUser(UserInfoHolder.getCurrentUserCode());
		}
			
		
		if(StringUtils.isNotBlank(page.getSidx())) {
			record.setSidx(page.getSidx());
		}else {
			record.setSidx("t1.create_date");
		}
		if(StringUtils.isNotBlank(page.getSord())) {
			record.setSord(page.getSord());
		}else {
			record.setSord("desc");
		}
		
		List<SuperviseRegister> records = mapper.getDataSetList(record, page);
		for (SuperviseRegister superviseRegister : records) {
			//当前办理人
			if("1".equals(superviseRegister.getRegisterStatus())) { 
				superviseRegister.setCurrentHandleUser(superviseRegister.getRegisterName());
			}else if("2".equals(superviseRegister.getRegisterStatus())) { 
				superviseRegister.setCurrentHandleUser(superviseRegister.getAssigneeNames());
			}else if("3".equals(superviseRegister.getRegisterStatus())) { 
				superviseRegister.setCurrentHandleUser(superviseRegister.getCheckName());
			}else if("4".equals(superviseRegister.getRegisterStatus())) { 
				superviseRegister.setCurrentHandleUser(superviseRegister.getApproveName());
			}else {
				superviseRegister.setCurrentHandleUser(null);
			}
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	@Transactional(readOnly = false)
	public void delay(OperationData record) {
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("7");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getDelayReamrk());
		superviseLogs.setLogFile(record.getDelayFiles());
		superviseLogs.setLogDate(record.getDelayDate());
		superviseLogsService.save(superviseLogs);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		superviseRegister.setDelayDate(record.getDelayDate());
		superviseRegister.setActualDate(record.getDelayDate());
		
		Integer overdueDay = superviseRegister.getOverdueDay();
		
		Date actualDate = superviseRegister.getActualDate(); //完成时间
		
		long betweenDay = DateUtil.betweenDay(new Date(), actualDate, true); 
		
		//0未超期  1即将超期  2已超期
		if(actualDate.getTime() >= new Date().getTime()) {
			superviseRegister.setIsOverdue("0");
		}
		if(betweenDay <= overdueDay) {
			superviseRegister.setIsOverdue("1");
		} 
		if(actualDate.getTime() <= new Date().getTime()) {
			superviseRegister.setIsOverdue("2");
		}
		
		mapper.updateByPrimaryKeySelective(superviseRegister);
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		//督办人延期提醒 处理人 登记人
		StringBuffer content = new StringBuffer();
		content.append(UserInfoHolder.getCurrentUserName()).append("延期了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
		content.append("-").append(superviseRegister.getRegisterMatter()).append(",延期日期为【").append(record.getDelayDate()).append("】,请悉知!");
		
		List<SuperviseTask> task = superviseTaskService.selectByRegisterId(superviseRegister.getId());
		StringBuffer receiver = new StringBuffer();
		for (SuperviseTask superviseTask : task) {
			receiver.append(superviseTask.getTaskCode()).append(",");
		}
		
		if(receiver.length() > 0) {
			//处理人
			sendMessage(receiver.toString(), content,"【延期提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-handling",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
		}
		
		//登记人
		sendMessage(superviseRegister.getCreateUser(), content,"【延期提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-registration",
				appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
		
	}

	@Override
	@Transactional(readOnly = false)
	public void cancel(OperationData record) {
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("8");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getCancelReamrk());
		superviseLogs.setLogFile(record.getCancelFiles());
		superviseLogsService.save(superviseLogs);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		StringBuffer receiver = new StringBuffer();
		
		if("4".equals(superviseRegister.getRegisterStatus())) {
			receiver.append(superviseRegister.getRegisterUser()).append(",").append(superviseRegister.getCheckCode()).append(",");
			receiver.append(superviseRegister.getApproveCode()).append(",");
		}
		if("3".equals(superviseRegister.getRegisterStatus())) {
			receiver.append(superviseRegister.getRegisterUser()).append(",").append(superviseRegister.getCheckCode()).append(",");
		}
		if("2".equals(superviseRegister.getRegisterStatus())) {
			receiver.append(superviseRegister.getRegisterUser()).append(",");
		}	
		
		superviseRegister.setRegisterStatus("6");
		
		mapper.updateByPrimaryKeySelective(superviseRegister);
		
		List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
		for (SuperviseTask superviseTask : taskList) {
			superviseTask.setTaskStatus("6");
			superviseTaskService.update(superviseTask);
		}
		
		//登记人撤销 需要提醒督办人 办理人 验收人 批示人
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		StringBuffer content = new StringBuffer();
		content.append(UserInfoHolder.getCurrentUserName()).append("撤销了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
		content.append("-").append(superviseRegister.getRegisterMatter()).append(",撤销原因为:").append(record.getCancelReamrk()).append(",请悉知!");
		
		
		
		if(CollectionUtils.isNotEmpty(taskList)) {
			for (SuperviseTask superviseTask : taskList) {
				receiver.append(superviseTask.getTaskCode()).append(",");
			}
		}
		
		if(null != receiver && receiver.length() > 0) {
//			NoticeReq noticeVo = NoticeReq.builder()
//					.content(content.toString())
//					.noticeType("3")
//					.subject("【撤销提醒】" + superviseRegister.getRegisterMatter())
//					.sender(UserInfoHolder.getCurrentUserCode())
//					.senderName(UserInfoHolder.getCurrentUserCode())
//					.receiver(receiver.toString())
//					.wxSendType("2")
//					.source("任务督办")
//					.toUrl("/index")
//					.build();
//			 informationFeignService.sendNotice(noticeVo);
			 sendMessage(superviseRegister.getCreateUser(), content,"【撤销提醒】" + superviseRegister.getRegisterMatter(),"/index",
						appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void close(OperationData record) {
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("9");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getCloseReamrk());
		superviseLogs.setLogFile(record.getCloseFlies());
		superviseLogsService.save(superviseLogs);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		superviseRegister.setRegisterStatus("7");
		mapper.updateByPrimaryKeySelective(superviseRegister);
		
		
		List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
		for (SuperviseTask superviseTask : taskList) {
			superviseTask.setTaskStatus("7");
			superviseTaskService.update(superviseTask);
		}
		
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		//督办人延期提醒 处理人 登记人
		StringBuffer content = new StringBuffer();
		content.append(UserInfoHolder.getCurrentUserName()).append("终止了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
		content.append("-").append(superviseRegister.getRegisterMatter()).append(",终止原因为:").append(record.getCancelReamrk()).append(",请悉知!");
		
		StringBuffer receiver = new StringBuffer();
		
		if("4".equals(superviseRegister.getRegisterStatus())) {
			receiver.append(superviseRegister.getRegisterUser()).append(",").append(superviseRegister.getCheckCode()).append(",");
			receiver.append(superviseRegister.getApproveCode()).append(",");
		}
		if("3".equals(superviseRegister.getRegisterStatus())) {
			receiver.append(superviseRegister.getRegisterUser()).append(",").append(superviseRegister.getCheckCode()).append(",");
		}
		if("2".equals(superviseRegister.getRegisterStatus())) {
			receiver.append(superviseRegister.getRegisterUser()).append(",");
		}
		
		if(CollectionUtils.isNotEmpty(taskList)) {
			for (SuperviseTask superviseTask : taskList) {
				receiver.append(superviseTask.getTaskCode()).append(",");
			}
		}
		
//		NoticeReq noticeVo = NoticeReq.builder()
//				.content(content.toString())
//				.noticeType("3")
//				.subject("【终止提醒】" + superviseRegister.getRegisterMatter())
//				.sender(UserInfoHolder.getCurrentUserCode())
//				.senderName(UserInfoHolder.getCurrentUserCode())
//				.receiver(receiver.toString())
//				.wxSendType("2")
//				.source("任务督办")
//				.toUrl("/index")
//				.build();
//		 informationFeignService.sendNotice(noticeVo);
		 
		 sendMessage(superviseRegister.getCreateUser(), content,"【终止提醒】" + superviseRegister.getRegisterMatter(),"/index",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
		
	}

	@Override
	@Transactional(readOnly = false)
	public void handle(OperationData record) {
		
		SuperviseTask superviseTask = superviseTaskService.selectById(record.getTaskId());
		superviseTask.setTaskStatus("3");
		superviseTask.setTaskHandleRemark(record.getHandleReamrk());
		superviseTask.setTaskHandleDate(record.getHandleDate());
		superviseTaskService.update(superviseTask);
		
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("3");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getHandleReamrk());
		superviseLogs.setLogFile(record.getHandleFlies());
		superviseLogs.setLogDate(record.getHandleDate());
		superviseLogsService.save(superviseLogs);
		
		//判断是否最后一个办理任务  如果是最后办理任务则需要修改验收状态
		List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
		
		boolean flag = true;
		StringBuffer taskName = new StringBuffer();
		for (SuperviseTask task : taskList) {
			superviseTask.getTaskStatus();
			taskName.append(task.getTaskName()).append(",");
			if("2".equals(task.getTaskStatus())) {
				flag = false;
				break;
			}
		}
		
		taskName = taskName.deleteCharAt(taskName.length() - 1);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		if(flag) {
			superviseRegister.setId(record.getRegisterId());
			superviseRegister.setRegisterStatus("3");
			mapper.updateByPrimaryKeySelective(superviseRegister);
			
			String registerUrgency = superviseRegister.getRegisterUrgency();
			if("1".equals(registerUrgency)) {
				registerUrgency = "普通";
			}else if("2".equals(registerUrgency)) {
				registerUrgency = "紧急";
			}else {
				registerUrgency = "非常紧急";
			}
			
			SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
			
			//处理人办完了需要提醒给验收人
			StringBuffer content = new StringBuffer();
			content.append(taskName).append("处理的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
			content.append("-").append(superviseRegister.getRegisterMatter()).append(",任务当前进度为【待验收】,等待您的处理!");
			
			//验收人
			sendMessage(superviseRegister.getCheckCode(), content,"【待验收提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-handling",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=handling&index=0&menuIndex=2");
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void feedback(OperationData record) {
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("12");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getFeedbackReamrk());
		superviseLogs.setLogFile(record.getFeedbackFlies());
		superviseLogs.setLogDate(record.getFeedbackDate());
		superviseLogsService.save(superviseLogs);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		String assigneeNames = mapper.selectAssigneeNames(record.getRegisterId());
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		StringBuffer content = new StringBuffer();
		content.append(UserInfoHolder.getCurrentUserName()).append("反馈了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
		content.append("-").append(superviseRegister.getRegisterMatter()).append(",任务当前进度为【办理中】");
		content.append("，处理人为").append(assigneeNames).append(",请悉知！");
		
		
		//给督办人发消息
		sendMessage(superviseRegister.getRegisterUser(), content,"【反馈提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
				appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-supervision?fromPage=workBench&index=0");
		
//		if(superviseRegister.getCreateUser().equals(superviseRegister.getRegisterUser())) {//如果登记人和督办人是同一个人则只发一次
//			//给督办人发消息
//			sendMessage(superviseRegister.getRegisterUser(), content,"【反馈提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
//					"");
//		}else {
//			//给登记人发消息
//			sendMessage(superviseRegister.getCreateUser(), content,"【反馈提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-registration","");
//			
//			//给督办人发消息
//			sendMessage(superviseRegister.getRegisterUser(), content,"【反馈提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision","");
//		}
		
	}
	
	@Override
	public List<SuperviseLogs> feedbackDetails(OperationData record) {
		return superviseLogsService.selectByFeedbackDetails(record.getRegisterId());
	}

	@Override
	@Transactional(readOnly = false)
	public void urgent(OperationData record) {
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("6");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getUrgentRemark());
		superviseLogsService.save(superviseLogs);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		//催办当前处理人
		StringBuffer content = new StringBuffer();
		content.append(UserInfoHolder.getCurrentUserName()).append("催办了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
		content.append("-").append(superviseRegister.getRegisterMatter()).append(",等待您的处理!");
		
		String receiver = "";
		String tourl = "";
		String url = "";
		if("1".equals(superviseRegister.getRegisterStatus())) { 
			receiver = superviseRegister.getRegisterUser();
			tourl = "/ts-web-oa/task/task-supervision";
			url = appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-supervision?fromPage=workBench&index=0";
		}else if("2".equals(superviseRegister.getRegisterStatus())) { 
			receiver = mapper.selectAssigneeCodes(superviseRegister.getId());
			tourl = "/ts-web-oa/task/task-handling";
			url = appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-handling?index=0";
		}else if("3".equals(superviseRegister.getRegisterStatus())) { 
			receiver = superviseRegister.getCheckCode();
			tourl = "/ts-web-oa/task/task-handling";
			url = appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-handling?index=0";
		}else if("4".equals(superviseRegister.getRegisterStatus())) { 
			receiver = superviseRegister.getApproveCode();
			tourl = "/ts-web-oa/task/task-handling";
			url = appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-handling?index=0";
		}
		
		//处理人
		if(StringUtils.isNoneBlank(receiver)) {
			sendMessage(receiver, content,"【催办提醒】" + superviseRegister.getRegisterMatter(),tourl,url);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void transfer(OperationData record) {
		
		SuperviseLogs superviseLogs = new SuperviseLogs();
		superviseLogs.setLogType("10");
		superviseLogs.setRegisterId(record.getRegisterId());
		superviseLogs.setLogRemark(record.getTransferRemark());
		superviseLogs.setLogFile(record.getTransferFlies());
		superviseLogs.setLogDate(record.getTransferDate());
		superviseLogsService.save(superviseLogs);
		
		SuperviseTask superviseTask = superviseTaskService.selectById(record.getTaskId());
		superviseTask.setTaskCode(record.getTransferUser());
		superviseTask.setTaskName(record.getTransferUserName());
		superviseTaskService.update(superviseTask);
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		StringBuffer content = new StringBuffer();
		content.append(UserInfoHolder.getCurrentUserName()).append("转办了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
		content.append("-").append(superviseRegister.getRegisterMatter()).append("给您,任务当前进度为【处理中】,等待您的处理!");
		
		sendMessage(record.getTransferUser(), content,"【转办提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-handling",
				appConfigProperties.getWxLoginUrl() + "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId()  + "&form=handling&index=0&menuIndex=2");
	}

	@Override
	@Transactional(readOnly = false)
	public void check(OperationData record) {
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		if("1".equals(record.getOperationStatus())) {
			
			String status = "";
			if(StringUtils.isNotBlank(superviseRegister.getApproveCode())) {
				status = "4";
			}else {
				status = "5";
			}
			
			List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
			for (SuperviseTask superviseTask : taskList) {
				superviseTask.setTaskStatus(status);
				superviseTaskService.update(superviseTask);
			}
			
			if("5".equals(status)) {
				superviseRegister.setFinishDate(new Date());
			}
			
			superviseRegister.setRegisterStatus(status);
			mapper.updateByPrimaryKeySelective(superviseRegister);
			
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("4");
			superviseLogs.setRegisterId(record.getRegisterId());
			superviseLogs.setLogRemark(record.getCheckRemark());
			superviseLogs.setLogFile(record.getCheckFlies());
			superviseLogs.setLogDate(record.getCheckDate());
			superviseLogsService.save(superviseLogs);
			
			if("4".equals(status)) {
				//给批示人发消息
				StringBuffer content = new StringBuffer();
				content.append(UserInfoHolder.getCurrentUserName()).append("验收了【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
				content.append("-").append(superviseRegister.getRegisterMatter()).append(",任务当前进度为【待批示】,等待您的处理!");
				
				sendMessage(superviseRegister.getApproveCode(), content,"【待批示提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-handling",
						appConfigProperties.getWxLoginUrl() + "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=handling&index=0&menuIndex=2");
			}
			
			if("5".equals(status)) {
				if(superviseRegister.getRegisterUser().equals(superviseRegister.getCreateUser())) {
					StringBuffer content = new StringBuffer();
					content.append("您好，您督办的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
					content.append("-").append(superviseRegister.getRegisterMatter()).append(",已验收完成，请知悉！");
					sendMessage(superviseRegister.getRegisterUser(), content,"【办结提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
							appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
				}else {
					StringBuffer content = new StringBuffer();
					content.append("您好，您督办的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
					content.append("-").append(superviseRegister.getRegisterMatter()).append(",已验收完成，请知悉！");
					sendMessage(superviseRegister.getRegisterUser(), content,"【办结提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
							appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
					
					StringBuffer content2 = new StringBuffer();
					content2.append("您好，您登记的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
					content2.append("-").append(superviseRegister.getRegisterMatter()).append(",已验收完成，请知悉！");
					sendMessage(superviseRegister.getCreateUser(), content2,"【办结提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-registration",
							appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
				}
			}
		}else {
			List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
			
			StringBuffer taskCodes = new StringBuffer();
			for (SuperviseTask superviseTask : taskList) {
				superviseTask.setTaskStatus("2");
				superviseTaskService.update(superviseTask);
				taskCodes.append(superviseTask.getTaskCode()).append(",");
			}
			
			taskCodes = taskCodes.deleteCharAt(taskCodes.length() - 1);
			
			superviseRegister.setRegisterStatus("2");
			mapper.updateByPrimaryKeySelective(superviseRegister);
			
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("13");
			superviseLogs.setRegisterId(record.getRegisterId());
			superviseLogs.setLogRemark(record.getCheckRemark());
			superviseLogs.setLogFile(record.getCheckFlies());
			superviseLogs.setLogDate(record.getCheckDate());
			superviseLogsService.save(superviseLogs);
			
			//给处理人发消息
			StringBuffer content = new StringBuffer();
			content.append("您好，您处理的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
			content.append("-").append(superviseRegister.getRegisterMatter()).append(",验收未通过，请尽快处理！");
			
			sendMessage(taskCodes.toString(), content,"【验收提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-handling",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-handling?index=0");
		}
		
	}
	
	@Override
	@Transactional(readOnly = false)
	public void approve(OperationData record) {
		
		SuperviseRegister superviseRegister = mapper.selectByPrimaryKey(record.getRegisterId());
		
		String registerUrgency = superviseRegister.getRegisterUrgency();
		if("1".equals(registerUrgency)) {
			registerUrgency = "普通";
		}else if("2".equals(registerUrgency)) {
			registerUrgency = "紧急";
		}else {
			registerUrgency = "非常紧急";
		}
		
		SuperviseType superviseType = superviseTypeService.selectById(superviseRegister.getRegisterType());
		
		if("1".equals(record.getOperationStatus())) {
			List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
			for (SuperviseTask superviseTask : taskList) {
				superviseTask.setTaskStatus("5");
				superviseTaskService.update(superviseTask);
			}
			
			superviseRegister.setRegisterStatus("5");
			superviseRegister.setFinishDate(new Date());
			mapper.updateByPrimaryKeySelective(superviseRegister);
			
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("5");
			superviseLogs.setRegisterId(record.getRegisterId());
			superviseLogs.setLogRemark(record.getApproveRemark());
			superviseLogs.setLogFile(record.getApproveFlies());
			superviseLogs.setLogDate(record.getApproveDate());
			superviseLogsService.save(superviseLogs);
			
			
			if(superviseRegister.getRegisterUser().equals(superviseRegister.getCreateUser())) {
				StringBuffer content = new StringBuffer();
				content.append("您好，您督办的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
				content.append("-").append(superviseRegister.getRegisterMatter()).append(",已批示完成，请知悉！");
				sendMessage(superviseRegister.getRegisterUser(), content,"【办结提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
						appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
			}else {
				StringBuffer content = new StringBuffer();
				content.append("您好，您督办的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
				content.append("-").append(superviseRegister.getRegisterMatter()).append(",已批示完成，请知悉！");
				sendMessage(superviseRegister.getRegisterUser(), content,"【办结提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-supervision",
						appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
				
				StringBuffer content2 = new StringBuffer();
				content2.append("您好，您登记的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
				content2.append("-").append(superviseRegister.getRegisterMatter()).append(",已批示完成，请知悉！");
				sendMessage(superviseRegister.getCreateUser(), content2,"【办结提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-registration",
						appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-detail?id=" + superviseRegister.getId() + "&form=copy&index=-1&menuIndex=3");
			}
			
		}else {
			List<SuperviseTask> taskList = superviseTaskService.selectByRegisterId(record.getRegisterId());
			for (SuperviseTask superviseTask : taskList) {
				superviseTask.setTaskStatus("3");
				superviseTaskService.update(superviseTask);
			}
			
			superviseRegister.setRegisterStatus("3");
			mapper.updateByPrimaryKeySelective(superviseRegister);
			
			SuperviseLogs superviseLogs = new SuperviseLogs();
			superviseLogs.setLogType("14");
			superviseLogs.setRegisterId(record.getRegisterId());
			superviseLogs.setLogRemark(record.getApproveRemark());
			superviseLogs.setLogFile(record.getApproveFlies());
			superviseLogs.setLogDate(record.getApproveDate());
			superviseLogsService.save(superviseLogs);
			
			//给验收人发消息
			StringBuffer content = new StringBuffer();
			content.append("您好，您验收的【").append(registerUrgency).append("】的").append(superviseType.getTypeName());
			content.append("-").append(superviseRegister.getRegisterMatter()).append(",批示未通过，请尽快处理！");
			
			sendMessage(superviseRegister.getCheckCode(), content,"【批示提醒】" + superviseRegister.getRegisterMatter(),"/ts-web-oa/task/task-handling",
					appConfigProperties.getWxLoginUrl()+ "ts-mobile-oa/pages/task/task-handling?index=0");
		}
	}

	private void sendMessage(String receiver, StringBuffer content,String subject,String toUrl,String url) {
		NoticeReq noticeVo = NoticeReq.builder()
				.content(content.toString())
				.noticeType("3")
				.subject(subject)
				.sender(UserInfoHolder.getCurrentUserCode())
				.senderName(UserInfoHolder.getCurrentUserName())
				.receiver(receiver)
				.wxSendType("1")
				.source("任务督办")
				.toUrl(toUrl)
				//.url(appConfigProperties.getWxLoginUrl()+"pages/information/information-details?informationId="+information.getId())
				.url(url)
				.build();
		 informationFeignService.sendNotice(noticeVo);
	}

	@Override
	public List<SuperviseRegister> superviseRegisterByTypeId(String typeId) {
		Example example = new Example(SuperviseRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("registerType", typeId);
		return mapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public void updateSuperviseRegister(SuperviseRegister superviseRegister) {
		mapper.updateByPrimaryKeySelective(superviseRegister);
	}

	@Override
	@Transactional(readOnly = false)
	public void updateOverdue() {
		Example example = new Example(SuperviseRegister.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<SuperviseRegister> superviseRegisterList = mapper.selectByExample(example);
		
		for (SuperviseRegister superviseRegister : superviseRegisterList) {
			
			Integer overdueDay = superviseRegister.getOverdueDay();
			if(null == overdueDay) {
				overdueDay = 1;
			}
			Date actualDate = superviseRegister.getCompleteDate(); //完成时间
			
			long betweenDay = DateUtil.betweenDay(new Date(), actualDate, true); 
			
			//0未超期  1即将超期  2已超期
			if(actualDate.getTime() >= new Date().getTime()) {
				superviseRegister.setIsOverdue("0");
			}
			if(betweenDay <= overdueDay) {
				superviseRegister.setIsOverdue("1");
			} 
			if(actualDate.getTime() <= new Date().getTime()) {
				superviseRegister.setIsOverdue("2");
			}
			
			mapper.updateByPrimaryKeySelective(superviseRegister);
		}
		
		
	}
}
