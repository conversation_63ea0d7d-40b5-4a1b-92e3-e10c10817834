<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.fund.dao.FundSupportingApplyMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.fund.model.FundSupportingApply">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="entry_item_id" jdbcType="VARCHAR" property="entryItemId" />
    <result column="supporting_fund" jdbcType="DECIMAL" property="supportingFund" />
    <result column="files_id" jdbcType="VARCHAR" property="filesId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
     <result column="status" jdbcType="INTEGER" property="status" />
     <result column="workflow_id" jdbcType="VARCHAR" property="workflowId" />
      <result column="type_approver_code" jdbcType="VARCHAR" property="typeApproverCode" />
       <result column="type_approver_name" jdbcType="VARCHAR" property="typeApproverName" />
        <result column="leader_approver_code" jdbcType="VARCHAR" property="leaderApproverCode" />
         <result column="leader_approver_name" jdbcType="VARCHAR" property="leaderApproverName" />
  </resultMap>
  
  
   <select id="getDataSetList"   resultType="Map"  parameterType="cn.trasen.oa.fund.model.FundEntry">
  	  select distinct a.item_code as itemCode,a.item_name as itemName,a.leader_user AS leaderUser,a.leader_user_name as leaderUserName,a.dept_code AS deptCode,a.dept_name AS deptName,
  	  a.telephone,a.file_number AS fileNumber,a.fund_type_id AS fundTypeId,a.fund_type_name AS fundTypeName,a.approved_item_fund AS approvedItemFund,
  	  b.id,B.entry_item_id AS entryItemId,B.supporting_fund AS supportingFund,B.files_id AS filesId,B.remark,B.create_date AS createDate,B.create_user AS createUser,b.create_user_name as createUserName,b.status,b.workflow_id as workflowId,
  	  b.type_approver_code as typeApproverCode,b.type_approver_name as typeApproverName,b.leader_approver_code as leaderApproverCode,b.leader_approver_name as leaderApproverName
  	  		,<if test="index == 2">
				 (select   GROUP_CONCAT(ASSIGNEE_NO)  from  wf_task  wft    where  wft.wf_instance_id=b.workflow_id)  assigneeNo,
				 (select   GROUP_CONCAT(ASSIGNEE_NAME)  from  wf_task  wft    where  wft.wf_instance_id=b.workflow_id) assigneeName,
			  </if> 
		   	  1 as bz 
  	  		 from toa_fund_supporting_apply b LEFT JOIN  toa_fund_entry a  ON b.entry_item_id=a.id
  	  		 left join wf_instance_info inst on b.id = inst.BUSINESS_ID  
  	  		<if test="index == 1">
				left join wf_task task on inst.wf_instance_id = task.wf_instance_id 
			</if>
			<if test="index == 2">
				left join wf_task_his task on inst.wf_instance_id = task.wf_instance_id 
			</if>
			<if test="index == 3">
				left join wf_task_his task on inst.wf_instance_id = task.wf_instance_id 
			</if>
  	  		
  	  		 where b.is_deleted='N'
  	  		 <if test="index == 1">   <!-- 1待我办理 //或者待我提交的-->
				and ((inst.is_deleted = 'N' and inst.status in ('0','1') and task.assignee_no = #{userCode})  or  (b.create_user = #{userCode} and b.status=0))
			</if>
			<if test="index == 2">    <!-- 2我已办理   //或者我提交未办结的-->
				and ((task.IS_DELETED = 'N' and task.ACT_ASSIGNEE_NO = #{userCode} and inst.IS_DELETED = 'N'  AND inst.status in ('0','1')
				and not exists (
					select 1 from wf_task task where wf_instance_id = inst.wf_instance_id
	 				 AND task.is_deleted='N'
	 				and (task.act_assignee_no = #{userCode} or task.assignee_no = #{userCode})
	 			)
	 			)  or   (b.create_user = #{userCode}  and b.status=1) 
	 				<if test="isFundAdmin !=null and isFundAdmin !='' and isFundAdmin==1 ">
		       			or  b.status=1
		   			</if >
	 			)
			</if>
			<if test="index == 3">  <!-- 3由我发起      //我办理过已办结的   或者我提交已办结的--> 
				and ( (task.IS_DELETED = 'N' and task.ACT_ASSIGNEE_NO = #{userCode} and inst.IS_DELETED = 'N' and b.status in (2,3))   or (b.create_user = #{userCode}  and  b.status in (2,3))
				<if test="isFundAdmin !=null and isFundAdmin !='' and isFundAdmin==1 ">
		       			or  b.status in (2,3)
		   			</if >
				)
			</if>
  			<if test="condition !=null and condition !=''">
		       	and ( a.item_name like concat(concat('%', #{condition}), '%') 
		       		  or a.leader_user_name like concat(concat('%', #{condition}), '%') )
		   	</if >
		   	<if test="status !=null and status !='' ">
		       	and  a.status=#{status}
		   	</if >
		   	<if test="fundTypeId !=null and fundTypeId !='' ">
		       	and  a.fund_type_id=#{fundTypeId}
		   	</if >
  </select>
  
  
   <select id="selectById" resultType="Map" parameterType="cn.trasen.oa.fund.model.FundSupportingApply">
  	select a.item_code as itemCode,a.item_name as itemName,a.leader_user AS leaderUser,a.leader_user_name as leaderUserName,a.dept_code AS deptCode,a.dept_name AS deptName,
  	       a.telephone,a.file_number AS fileNumber,a.fund_type_id AS fundTypeId,a.fund_type_name AS fundTypeName,a.approved_item_fund AS approvedItemFund,a.files_id as itemFilesId,
  	       b.id,B.entry_item_id AS entryItemId,B.supporting_fund AS supportingFund,B.files_id AS filesId,B.remark,B.create_date AS createDate,B.create_user AS createUser,b.status,b.workflow_id as workflowId,
  	       b.type_approver_code as typeApproverCode,b.type_approver_name as typeApproverName,b.leader_approver_code as leaderApproverCode,b.leader_approver_name as leaderApproverName
  	  		 from toa_fund_supporting_apply b LEFT JOIN  toa_fund_entry a  ON b.entry_item_id=a.id
  	  		 where b.is_deleted='N'  and  b.id=#{id} 
  </select>
  
   <select id="selectApprovalDetails" resultType="Map" parameterType="cn.trasen.oa.fund.model.FundSupportingApply">
  	  select a.item_code as itemCode,a.item_name as itemName,a.leader_user AS leaderUser,a.leader_user_name as leaderUserName,a.dept_code AS deptCode,a.dept_name AS deptName,
  	 		 a.telephone,a.file_number AS fileNumber,a.fund_type_id AS fundTypeId,a.fund_type_name AS fundTypeName,a.approved_item_fund AS approvedItemFund,a.files_id as itemFilesId, task.TASK_ID as taskId,
  	  		 b.id,B.entry_item_id AS entryItemId,B.supporting_fund AS supportingFund,B.files_id AS filesId,B.remark,B.create_date AS createDate,B.create_user AS createUser,b.status,b.workflow_id as workflowId,
  	  		 task.wf_step_id as wfStepId,task.wf_step_name as wfStepName,task.assignee_no AS assigneeNo,task.assignee_name AS assigneeName,b.type_approver_code as typeApproverCode,b.type_approver_name as typeApproverName,b.leader_approver_code as leaderApproverCode,b.leader_approver_name as leaderApproverName
  	  		 from toa_fund_supporting_apply b LEFT JOIN  toa_fund_entry a  ON b.entry_item_id=a.id
  	  		 	 	 left join wf_instance_info inst on b.workflow_id = inst.WF_INSTANCE_ID  
  	  				 left join wf_task task on inst.wf_instance_id = task.wf_instance_id 
  	  		         where b.is_deleted='N'  
  	  		         <if test="id !=null and id !='' ">
		  		     	and  b.id=#{id}
		   	   	     </if > 
		   	    	 <if test="assigneeNo !=null and assigneeNo !='' ">
		  		     	and  task.assignee_no=#{assigneeNo}
		   	    	 </if > 
  </select>
  
  
  
  
    <select id="selectInstanceInfoById" resultType="cn.trasen.homs.feign.workflow.vo.WfInstanceInfo" parameterType="String">
  	  select inst.*  from toa_fund_supporting_apply  b 
  	  		  inner join wf_instance_info inst on b.workflow_id = inst.WF_INSTANCE_ID  
  	  		  where b.is_deleted='N' and b.workflow_id=#{workflowId}
  </select>
  
</mapper>