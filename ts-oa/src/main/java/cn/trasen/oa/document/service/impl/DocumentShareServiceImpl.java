package cn.trasen.oa.document.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.hrms.HrmsOrganization;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.document.dao.DocumentChannelMapper;
import cn.trasen.oa.document.dao.DocumentMapper;
import cn.trasen.oa.document.dao.DocumentShareMapper;
import cn.trasen.oa.document.model.Document;
import cn.trasen.oa.document.model.DocumentOperate;
import cn.trasen.oa.document.model.DocumentShare;
import cn.trasen.oa.document.service.DocumentOperateService;
import cn.trasen.oa.document.service.DocumentShareService;
import cn.trasen.oa.utils.CommonUtils;
import cn.trasen.oa.utils.Node;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;
/**
 * @ClassName DocumentShareServiceImpl
 * @Description 科室文档分享实现类
 * @date 2025-06-21 16:23:40
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class DocumentShareServiceImpl implements DocumentShareService {
	
	@Autowired
	private DocumentShareMapper mapper;
	
	@Autowired
	private DocumentMapper documentMapper;
	
	@Autowired
	private DocumentChannelMapper documentChannelMapper;
	
	@Autowired
	private DocumentOperateService documentOperateService;
	
	@Transactional(readOnly = false)
	@Override
	public void initDocumentShareRecord() {
		// 先查询文档库数据，过滤出被分享人和被分享科室不为空的数据
		Example example = new Example(Document.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andIsNotNull("sharetoUser");
		criteria.andIsNotNull("sharetoDept");
		criteria.andIsNotNull("createShareUser");
		List<Document> documentList = documentMapper.selectByExample(example);
		if(CollUtil.isNotEmpty(documentList)){
			//查询所有的科室数据
			List<HrmsOrganization> list = mapper.getHrmsOrganizationList(UserInfoHolder.getCurrentUserCorpCode());
			Map<String, String> map = new HashMap<>();
			if(CollUtil.isNotEmpty(list)){
				map = list.stream().collect(Collectors.toMap(HrmsOrganization::getCode, HrmsOrganization::getName));
			}
			for(Document doc : documentList){
				String sharetoUser = doc.getSharetoUser();//被分享人列表
				String sharetoUserName = doc.getSharetoUserName();
				String sharetoDept = doc.getSharetoDept();//被分享科室列表
				
				Date updateDate = doc.getUpdateDate();//分享时间
				String createShareUser = doc.getCreateShareUser();//分享人
				String createShareName = doc.getCreateShareName();//分享人姓名
				String documentId = doc.getId();//文档ID
				String ssoOrgCode = doc.getSsoOrgCode();//所属机构编码
				
				if(!ObjectUtils.isEmpty(sharetoUser)){
					String[] sharetoUserArr = sharetoUser.split(",");
					String[] sharetoUserNameArr = sharetoUserName.split(",");
					for(int i = 0; i < sharetoUserArr.length; i++){
						DocumentShare share = new DocumentShare();
						share.setDocumentId(documentId);
						share.setShareSource("1");//被分享人
						share.setShareToUser(sharetoUserArr[i]);
						share.setShareToUserName(sharetoUserNameArr[i]);
						share.setCreateDate(updateDate);//分享时间
						share.setCreateShareUser(createShareUser);
						share.setCreateShareName(createShareName);
						share.setSsoOrgCode(ssoOrgCode);
						save(share);
					}
				}
				
				if(!ObjectUtils.isEmpty(sharetoDept)){
					String[] sharetoDeptArr = sharetoDept.split(",");
					for(String deptcode : sharetoDeptArr){
						DocumentShare share = new DocumentShare();
						share.setDocumentId(documentId);
						share.setShareSource("2");//被分享科室
						share.setShareToDept(deptcode);
						if(map.containsKey(deptcode)){
							share.setShareToDeptName(map.get(deptcode));
						}
						share.setCreateDate(updateDate);//分享时间
						share.setCreateShareUser(createShareUser);
						share.setCreateShareName(createShareName);
						share.setSsoOrgCode(ssoOrgCode);
						save(share);
					}
				}
			}
		}
	}

	@Transactional(readOnly = false)
	@Override
	public Integer save(DocumentShare record) {
		//校验是否已经分享过了，根据文件ID+被分享人 或者 文件+被分享科室
		String documentId = record.getDocumentId();
		String createShareUser = record.getCreateShareUser();//分享人
		String shareToUser = record.getShareToUser();//被分享人
		String shareToDept = record.getShareToDept();//被分享科室
		if(ObjectUtils.isEmpty(documentId)){
			throw new BusinessException("被分享文档不能为空！");
		}
		if(ObjectUtils.isEmpty(shareToUser) && ObjectUtils.isEmpty(shareToDept)){
			throw new BusinessException("被分享对象不能为空！");
		}
		
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
//		if(!ObjectUtils.isEmpty(shareToUser)){
//			Example example = new Example(DocumentShare.class);
//			Example.Criteria criteria = example.createCriteria();
//			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
//			criteria.andEqualTo("createShareUser", createShareUser);
//			criteria.andEqualTo("shareToUser", shareToUser);
//			criteria.andEqualTo("documentId", documentId);
//			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
//			List<DocumentShare> list = mapper.selectByExample(example);
//			if(CollUtil.isNotEmpty(list)){
//				return 1;
//			}
//		}
//		if(!ObjectUtils.isEmpty(shareToDept)){
//			Example example = new Example(DocumentShare.class);
//			Example.Criteria criteria = example.createCriteria();
//			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
//			criteria.andEqualTo("createShareUser", createShareUser);
//			criteria.andEqualTo("shareToDept", shareToDept);
//			criteria.andEqualTo("documentId", documentId);
//			criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
//			List<DocumentShare> list = mapper.selectByExample(example);
//			if(CollUtil.isNotEmpty(list)){
//				return 1;
//			}
//		}
		
		
		if(ObjectUtils.isEmpty(record.getId())){
			record.setId(IdGeneraterUtils.nextId());
		}
		if(null == record.getCreateDate()){
			record.setCreateDate(new Date());
		}
//		record.setUpdateDate(new Date());
		record.setIsDeleted(Contants.IS_DELETED_FALSE);
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		DocumentShare record = new DocumentShare();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted(Contants.IS_DELETED_TURE);
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public List<Document> getDataList(Page page, Document document) {
		//默认按分享时间倒序排
		if(ObjectUtils.isEmpty(page.getSidx())){
			page.setSidx(" s.create_date ");
			page.setSord(" desc ");
		}
		List<Document> docList = new ArrayList<>();
        document.setChannelId(document.getNodeId());
        
        if (StringUtils.isNoneBlank(document.getCreateDateTime())) {
            Date createDate = DateUtil.parseDate(document.getCreateDateTime());
            document.setCreateDate(createDate);
        }
        //是否文档管理员
        boolean isDocAdmin = UserInfoHolder.getRight("DOC_MASTER");
        if(!isDocAdmin) {
//        	document.setUserCode(UserInfoHolder.getCurrentUserCode());
//            document.setUserDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        }
        
        //查询所有子节点
        List<Node> childList = documentChannelMapper.selectIdAndIdList();
        if(StringUtils.isNoneBlank(document.getChannelId())) {
        	List<String> findAllChildrenIds = CommonUtils.findAllChildrenIds(childList,document.getChannelId());
			findAllChildrenIds.add(document.getChannelId());
            document.setChildList(findAllChildrenIds);
        }
        // 分享类型：1-分享给我，2-我分享的
        document.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        document.setSharetoUser(UserInfoHolder.getCurrentUserCode());
        document.setSharetoDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        if(!ObjectUtils.isEmpty(document.getFromShareDate()) && document.getFromShareDate().indexOf(" ") == -1){
        	document.setFromShareDate(document.getFromShareDate() + " 00:00:00");
        }
        if(!ObjectUtils.isEmpty(document.getToShareDate()) && document.getToShareDate().indexOf(" ") == -1){
        	document.setToShareDate(document.getToShareDate() + " 23:59:59");
        }
        List<Document> documentList = mapper.findDocumentShareList(document, page);
		for (Document doc : documentList) {
			if(doc != null) {
				if (isDocAdmin || doc.getCreateUser().equals(UserInfoHolder.getCurrentUserCode())) {
					doc.setIsDocAdmin("true");
				} else {
					doc.setIsDocAdmin("false");
				}
				docList.add(doc);
			}
		}
    
        return docList;
	}

	@Transactional(readOnly = false)
	@Override
	public void bacthCancle(List<Document> recordList) {
		boolean isDocAdmin = UserInfoHolder.getRight("DOC_MASTER");
		
		for (Document document : recordList) {
			
			DocumentShare shareDocument = mapper.selectByPrimaryKey(document.getShareId());
			shareDocument.setUpdateDate(new Date());//撤回日期
			shareDocument.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			shareDocument.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		    
		    DocumentOperate documentOperate = new DocumentOperate();
		    
		    if(isDocAdmin || shareDocument.getCreateShareUser().equals(UserInfoHolder.getCurrentUserCode())) {
		    	shareDocument.setIsDeleted(Contants.IS_DELETED_TURE);
		    	Document oldDocument = documentMapper.selectByPrimaryKey(shareDocument.getDocumentId());
		    	if(null != oldDocument){
		    		documentOperate.setOptContent("执行了文档撤销分享，操作文档名称："  + oldDocument.getDocTitle());
		            //操作记录
		            documentOperateService.save(documentOperate);
		    	}
		    	mapper.updateByPrimaryKeySelective(shareDocument);
		    }
			
		}
		
	}

	@Override
	@Transactional(readOnly = false)
	public void bacthDelete(List<Document> recordList) {
		boolean isDocAdmin = UserInfoHolder.getRight("DOC_MASTER");
		for (Document document : recordList) {
			DocumentShare shareDocument = mapper.selectByPrimaryKey(document.getShareId());
			//分享来源：1-被分享人，2-被分享科室
			String shareSource = shareDocument.getShareSource();
			if(!ObjectUtils.isEmpty(shareSource) && "1".equals(shareSource)){
				//如果是删除分享给个人，直接将 shareIsDeleted 标记为 Y
				shareDocument.setShareIsDeleted(Contants.IS_DELETED_TURE);
			} else if(!ObjectUtils.isEmpty(shareSource) && "2".equals(shareSource)){
				//如果是删除分享给科室，将当前用户添加到排除用户字段 shareExcepUser
				String shareExcepUser = shareDocument.getShareExcepUser();
				if(!ObjectUtils.isEmpty(shareExcepUser)){
					shareExcepUser = UserInfoHolder.getCurrentUserCode();
				} else {
					shareExcepUser = shareExcepUser + "," + UserInfoHolder.getCurrentUserCode();
				}
				shareDocument.setShareExcepUser(shareExcepUser);
			}
		    
		    DocumentOperate documentOperate = new DocumentOperate();
		    
		    if(isDocAdmin || shareDocument.getShareToUser().equals(UserInfoHolder.getCurrentUserCode())) {
		    	Document oldDocument = documentMapper.selectByPrimaryKey(shareDocument.getDocumentId());
		    	if(null != oldDocument){
		    		documentOperate.setOptContent("执行了文档删除分享给我的，操作文档名称："  + oldDocument.getDocTitle());
		            //操作记录
		            documentOperateService.save(documentOperate);
		    	}
		    }
		    mapper.updateByPrimaryKeySelective(shareDocument);
		}
	}

}
