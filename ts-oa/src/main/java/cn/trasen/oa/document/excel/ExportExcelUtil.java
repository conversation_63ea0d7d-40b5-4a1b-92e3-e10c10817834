package cn.trasen.oa.document.excel;


import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ResourceLoader;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> .
 * @version V1.0
 * @Title: ExportExcelUtil.java
 * @Package ixe.cloud.common.util
 * @Description: Excel导出工具类(泛型)
 * @Company: 湖南创星
 * @date 2019年7月25日 下午2:43:47
 */
public class ExportExcelUtil<T> {

    private static final Logger logger = LoggerFactory.getLogger(ExportExcelUtil.class);

    /**
     * <p> @Title: exportExcel</p>
     * <p> @Description: 导出Excel</p>
     * <p> @Param: list 数据集合</p>
     * <p> @Param: exportHeaders 导出头部数据</p>
     * <p> @Param: exportFields 导出的字段数据</p>
     * <p> @Param: sheetName 表格名称</p>
     * <p> @Param: outputStream</p>
     * <p> @Return: void</p>
     * <p> <AUTHOR>
     */
    public void exportExcel(List<T> list, String exportHeaders, String exportFields, String sheetName, OutputStream outputStream) {
        HSSFWorkbook workbook = createWorkBook(list, exportHeaders, exportFields, sheetName);
        if (workbook != null) {
            try {
                workbook.write(outputStream);
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * <p> @Title: exportExcel</p>
     * <p> @Description: 导出Excel</p>
     * <p> @Param: </p>
     * <p> @Return: void</p>
     * <p> <AUTHOR>
     */
    public void exportExcel(HSSFWorkbook workbook, OutputStream outputStream) {
        if (workbook != null) {
            try {
                workbook.write(outputStream);
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * <p> @Title: exportSheet</p>
     * <p> @Description: 封装sheet</p>
     * <p> @Param: </p>
     * <p> @Return: HSSFSheet</p>
     * <p> <AUTHOR>
     */
    public void createSheet(HSSFWorkbook wwb, List<T> list, String exportHeaders, String exportFields, String sheetName) {
        createWorkBook(wwb, list, exportHeaders, exportFields, sheetName);
    }

    /**
     * <p> @Title: exportMapExcel</p>
     * <p> @Description: 根据Map<String,Object>创建Excel</p>
     * <p> Aug 18, 2019 5:02:04 PM</p>
     * <p> @param list
     * <p> @param exportHeaders
     * <p> @param exportFields
     * <p> @param sheetName
     * <p> @param outputStream void</p>
     * <p> <AUTHOR>
     */
    public void exportMapExcel(List<Map<String, Object>> list, String exportHeaders, String exportFields, String sheetName, OutputStream outputStream) {
        HSSFWorkbook workbook = createWorkMapBook(list, exportHeaders, exportFields, sheetName);
        if (workbook != null) {
            try {
                workbook.write(outputStream);
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * <p> @Title: exportNewMapExcel</p>
     * <p> @Description: 自定义表头宽度</p>
     * <p> @param list
     * <p> @param exportHeaders
     * <p> @param headerWidth
     * <p> @param exportFields
     * <p> @param sheetName
     * <p> @param outputStream</p>
     * <p> @Return: void</p>
     * <p> <AUTHOR>
     */
    public void exportNewMapExcel(List<Map<String, Object>> list, String exportHeaders, Integer[] headerWidth, String exportFields, String sheetName, OutputStream outputStream) {
        HSSFWorkbook workbook = createNewWorkMapBook(list, exportHeaders, headerWidth, exportFields, sheetName);
        if (workbook != null) {
            try {
                workbook.write(outputStream);
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * <p> @Title: createWorkMapBook</p>
     * <p> @Description: 根据Map<String,Object>创建workBook</p>
     * <p> Aug 18, 2019 5:01:23 PM</p>
     * <p> @param list
     * <p> @param exportHeaders
     * <p> @param exportFields
     * <p> @param sheetName
     * <p> @return HSSFWorkbook</p>
     * <p> <AUTHOR>
     */
    private HSSFWorkbook createWorkMapBook(List<Map<String, Object>> list, String exportHeaders, String exportFields, String sheetName) {
        HSSFWorkbook wwb = new HSSFWorkbook();
        // 创建工作薄
        HSSFSheet sheet = null;
        // 创建行
        HSSFRow row = null;
        // 表标题字体
        HSSFFont headerFont = wwb.createFont();
        headerFont.setFontName("宋体");// 字体
        headerFont.setFontHeightInPoints((short) 16);// 字号
        // 表头字体
        HSSFFont colheaderFont = wwb.createFont();
        colheaderFont.setFontName("宋体");// 字体
        colheaderFont.setFontHeightInPoints((short) 14);// 字号
        colheaderFont.setBold(true);
        // 标题字体
        HSSFFont bodyFont = wwb.createFont();
        bodyFont.setFontName("宋体");// 字体
        bodyFont.setFontHeightInPoints((short) 10);// 字号

        // 表标题样式
        HSSFCellStyle headerCellStyle = createHeaderStyle(wwb, (short) 16);
        // 表头单元格样式
        HSSFCellStyle colheaderCellStyle = createColStyle(wwb, (short) 12);
        colheaderCellStyle.setAlignment(HorizontalAlignment.CENTER);
        colheaderCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        colheaderCellStyle.setFont(colheaderFont);

        // 表体单元格样式
        HSSFCellStyle bodyCellStyle = createColStyle(wwb, (short) 10);
        bodyCellStyle.setFont(bodyFont);

        // 报表头
        String headers[];
        int defaultWidth = 13 * 256 + 184;
        String[] headerStrings = exportHeaders.split(",");
        if (headerStrings != null && headerStrings.length > 0) {
            sheet = wwb.createSheet(sheetName);
            for (int i = 0; i < headerStrings.length; i++) {
                sheet.setColumnWidth(i, defaultWidth);
            }
            if(null != sheet){
            	sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerStrings.length - 1));
            }
        }
       
        row = sheet.createRow(0); // 报表标题
        row.setHeightInPoints(50); //setHeight((short) 600);
        HSSFCell cell = row.createCell(0);
        cell.setCellValue(sheetName);
        cell.setCellStyle(headerCellStyle);
        headers = headerStrings;
        row = sheet.createRow(1);
        row.setHeight((short) 500);
        for (int j = 0; j < headerStrings.length; j++) {
            cell = row.createCell(j);
            cell.setCellValue(headers[j]);
            cell.setCellStyle(colheaderCellStyle);
        }
        createMapCell(list, row, cell, sheet, headerCellStyle, bodyCellStyle, exportFields);
        return wwb;
    }

    /**
     * <p> @Title: createNewWorkMapBook</p>
     * <p> @Description:  根据Map<String,Object>创建自定义宽度的workBook</p>
     * <p> @param list
     * <p> @param exportHeaders
     * <p> @param headerWidth
     * <p> @param exportFields
     * <p> @param sheetName
     * <p> @return</p>
     * <p> @Return: HSSFWorkbook</p>
     * <p> <AUTHOR>
     */
    private HSSFWorkbook createNewWorkMapBook(List<Map<String, Object>> list, String exportHeaders, Integer[] headerWidth, String exportFields, String sheetName) {
        HSSFWorkbook wwb = new HSSFWorkbook();
        // 创建工作薄
        HSSFSheet sheet = null;
        // 创建行
        HSSFRow row = null;
        // 表标题字体
        HSSFFont headerFont = wwb.createFont();
        headerFont.setFontName("宋体");// 字体
        headerFont.setFontHeightInPoints((short) 16);// 字号
        // 表头字体
        HSSFFont colheaderFont = wwb.createFont();
        colheaderFont.setFontName("宋体");// 字体
        colheaderFont.setFontHeightInPoints((short) 14);// 字号
        colheaderFont.setBold(true);
        // 标题字体
        HSSFFont bodyFont = wwb.createFont();
        bodyFont.setFontName("宋体");// 字体
        bodyFont.setFontHeightInPoints((short) 10);// 字号

        // 表标题样式
        HSSFCellStyle headerCellStyle = createHeaderStyle(wwb, (short) 16);
        // 表头单元格样式
        HSSFCellStyle colheaderCellStyle = createColStyle(wwb, (short) 12);
        colheaderCellStyle.setAlignment(HorizontalAlignment.CENTER);
        colheaderCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        colheaderCellStyle.setFont(colheaderFont);

        // 表体单元格样式
        HSSFCellStyle bodyCellStyle = createColStyle(wwb, (short) 10);
        bodyCellStyle.setFont(bodyFont);

        // 报表头
        String[] headers;
        int defaultWidth = 13 * 256 + 184;
        String[] headerStrings = exportHeaders.split(",");
        if (headerStrings != null && headerStrings.length > 0) {
            sheet = wwb.createSheet(sheetName);
            for (int i = 0; i < headerStrings.length; i++) {
                sheet.setColumnWidth(i, headerWidth[i]);
            }
            if(null != sheet){
            	sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerStrings.length - 1));
            }
        }
        
        row = sheet.createRow(0); // 报表标题
        row.setHeightInPoints(50); //setHeight((short) 600);
        HSSFCell cell = row.createCell(0);
        cell.setCellValue(sheetName);
        cell.setCellStyle(headerCellStyle);
        headers = headerStrings;
        row = sheet.createRow(1);
        row.setHeight((short) (33 * 20));
        for (int j = 0; j < headerStrings.length; j++) {
            cell = row.createCell(j);
            cell.setCellValue(headers[j]);
            cell.setCellStyle(colheaderCellStyle);
        }
        createMapCell(list, row, cell, sheet, headerCellStyle, bodyCellStyle, exportFields);
        return wwb;
    }

    /**
     * <p> @Title: createMapCell</p>
     * <p> @Description: 创建单元格</p>
     * <p> Aug 18, 2019 5:00:57 PM</p>
     * <p> @param list
     * <p> @param row
     * <p> @param cell
     * <p> @param sheet
     * <p> @param headerCellStyle
     * <p> @param bodyCellStyle
     * <p> @param exportFields void</p>
     * <p> <AUTHOR>
     */
    private void createMapCell(List<Map<String, Object>> list, HSSFRow row, HSSFCell cell, HSSFSheet sheet, HSSFCellStyle headerCellStyle, HSSFCellStyle bodyCellStyle, String exportFields) {
        try {
            for (int i = 0; i < list.size(); i++) {
                row = sheet.createRow(i + 2);
//				cell = row.createCell(0);
//				cell.setCellValue(i + 1);
                //cell.setCellStyle(bodyCellStyle);
                String[] fieldStrings = exportFields.split(","); // 选中的导出的字段数组
                for (int j = 0; j < fieldStrings.length; j++) {
                    String name = fieldStrings[j];
                    Object obj = list.get(i).get(name);
                    Boolean isNum = false;//data是否为数值型
                    if (obj != null || "".equals(obj)) {
                        //判断obj是否为数值型
                        isNum = obj.toString().matches("^(-?\\d+)(\\.\\d+)?$");
                    }
                    if (isNum) {
                        obj = Double.parseDouble(obj.toString());
                    }
                    cell = row.createCell(j);
                    cell = getCellValueByObject(cell, obj);
                    cell.setCellStyle(bodyCellStyle);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description:
     * @Date: 2020/2/25 17:23
     * @Param:
     * @return: void
     **/
    private void createWorkBook(HSSFWorkbook wwb, List<T> list, String exportHeaders, String exportFields, String sheetName) {
        // 创建工作薄
        HSSFSheet sheet = null;
        // 创建行
        HSSFRow row = null;
        // 表标题字体
        HSSFFont headerFont = wwb.createFont();
        headerFont.setFontName("宋体");// 字体
        headerFont.setFontHeightInPoints((short) 16);// 字号
        // 表头字体
        HSSFFont colheaderFont = wwb.createFont();
        colheaderFont.setFontName("宋体");// 字体
        colheaderFont.setFontHeightInPoints((short) 14);// 字号
        colheaderFont.setBold(true);
        // 标题字体
        HSSFFont bodyFont = wwb.createFont();
        bodyFont.setFontName("宋体");// 字体
        bodyFont.setFontHeightInPoints((short) 10);// 字号

        // 表标题样式
        HSSFCellStyle headerCellStyle = createHeaderStyle(wwb, (short) 16);
        // 表头单元格样式
        HSSFCellStyle colheaderCellStyle = createColStyle(wwb, (short) 14);
        colheaderCellStyle.setFont(colheaderFont);

        // 表体单元格样式
        HSSFCellStyle bodyCellStyle = createColStyle(wwb, (short) 10);
        bodyCellStyle.setFont(bodyFont);

        // 报表头
        String[] headers;
        int defaultWidth = 18 * 256 + 184;
        String[] headerStrings = exportHeaders.split(",");
        if (headerStrings != null && headerStrings.length > 0) {
            sheet = wwb.createSheet(sheetName);
            for (int i = 0; i < headerStrings.length; i++) {
                sheet.setColumnWidth(i, defaultWidth);
            }
            
            if(null != sheet){
            	 sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerStrings.length));
            }
        }
        
        row = sheet.createRow(0); // 报表标题
        row.setHeight((short) 600);

        HSSFCell cell = row.createCell(0);
        cell.setCellValue(sheetName);
        cell.setCellStyle(headerCellStyle);
        headers = headerStrings;
        row = sheet.createRow(1);
        row.setHeight((short) 600);

        for (int j = 0; j < headerStrings.length; j++) {
            cell = row.createCell(j);
            cell.setCellValue(headers[j]);
        }
        createCell(list, row, cell, sheet, headerCellStyle, bodyCellStyle, exportFields);
    }

    /**
     * <p> @Title: createWorkBook</p>
     * <p> @Description: 创建工作簿</p>
     * <p> @Param: </p>
     * <p> @Return: HSSFWorkbook</p>
     * <p> <AUTHOR>
     */
    private HSSFWorkbook createWorkBook(List<T> list, String exportHeaders, String exportFields, String sheetName) {
        HSSFWorkbook wwb = new HSSFWorkbook();
        // 创建工作薄
        HSSFSheet sheet = null;
        // 创建行
        HSSFRow row = null;
        HSSFRow row1 = null;

        // 表标题字体
        HSSFFont headerFont = wwb.createFont();
        headerFont.setFontName("宋体");// 字体
        headerFont.setFontHeightInPoints((short) 16);// 字号

        // 表头字体(表头、第一行)
        HSSFFont colheaderFont = wwb.createFont();
        colheaderFont.setFontName("宋体");// 字体
        colheaderFont.setFontHeightInPoints((short) 13);// 字号
        colheaderFont.setBold(true);

        // 标题字体(内容字体)
        HSSFFont bodyFont = wwb.createFont();
        bodyFont.setFontName("宋体");// 字体
        bodyFont.setFontHeightInPoints((short) 11);// 字号

        // 表标题样式
        HSSFCellStyle headerCellStyle = createHeaderStyle(wwb, (short) 16);
        headerCellStyle.setFont(headerFont);

        // 表头单元格样式(表头、第一行)
        HSSFCellStyle colheaderCellStyle = createColStyle(wwb, (short) 13);
        colheaderCellStyle.setWrapText(true);//设置自动换行
        colheaderCellStyle.setFont(colheaderFont);

        // 表体单元格样式(内容字体)
        HSSFCellStyle bodyCellStyle = createColStyle(wwb, (short) 11);
        bodyCellStyle.setWrapText(true);//设置自动换行
        bodyCellStyle.setFont(bodyFont);

        // 报表头
        String[] headers;
        int defaultWidth = 18 * 256 + 184;
        String[] headerStrings = exportHeaders.split(",");
        if (headerStrings != null && headerStrings.length > 0) {
            sheet = wwb.createSheet(sheetName);
            for (int i = 0; i < headerStrings.length; i++) {
                sheet.setColumnWidth(i, defaultWidth);
            }
            
            if(null != sheet){
            	sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerStrings.length));
            }
        }
        
        row = sheet.createRow(0); // 报表标题
        row.setHeight((short) 600);
        HSSFCell cell = row.createCell(0);
        cell.setCellValue(sheetName);
        cell.setCellStyle(headerCellStyle);
        headers = headerStrings;

        row = sheet.createRow(1);
        row.setHeight((short) 400);
        for (int j = 0; j < headerStrings.length; j++) {
            cell = row.createCell(j);
            cell.setCellValue(headers[j]);
            cell.setCellStyle(colheaderCellStyle);
        }
        createCell(list, row, cell, sheet, headerCellStyle, bodyCellStyle, exportFields);
        return wwb;
    }

    /**
     * <p> @Title: createHeaderStyle</p>
     * <p> @Description: 创建报表标题式样</p>
     * <p> @Param: </p>
     * <p> @Return: HSSFCellStyle</p>
     * <p> <AUTHOR>
     */
    private HSSFCellStyle createHeaderStyle(HSSFWorkbook wwb, short fontSize) {
        HSSFFont headerfont = wwb.createFont();
        headerfont.setBold(true);
        headerfont.setFontHeightInPoints(fontSize);

        HSSFCellStyle headerCellStyle = wwb.createCellStyle();
        headerCellStyle.setWrapText(true);
        headerCellStyle.setFont(headerfont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return headerCellStyle;
    }

    /**
     * <p> @Title: createColStyle</p>
     * <p> @Description: 设置列标题样式</p>
     * <p> @Param: </p>
     * <p> @Return: HSSFCellStyle</p>
     * <p> <AUTHOR>
     */
    private HSSFCellStyle createColStyle(HSSFWorkbook wwb, short fontSize) {
        HSSFFont colheaderfont = wwb.createFont();
        colheaderfont.setFontHeightInPoints(fontSize);
        HSSFCellStyle colheaderCellStyle = wwb.createCellStyle();
        colheaderCellStyle.setWrapText(true);
        colheaderCellStyle.setFont(colheaderfont);
//		colheaderCellStyle.setAlignment(HorizontalAlignment.CENTER);
//		colheaderCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        colheaderCellStyle.setBorderLeft(BorderStyle.THIN);
        colheaderCellStyle.setBorderTop(BorderStyle.THIN);
        colheaderCellStyle.setBorderRight(BorderStyle.THIN);
        colheaderCellStyle.setBorderBottom(BorderStyle.THIN);
        return colheaderCellStyle;
    }

    /**
     * <p> @Title: createCell</p>
     * <p> @Description: excel列赋值</p>
     * <p> @Param: </p>
     * <p> @Return: void</p>
     * <p> <AUTHOR>
     *
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     */
    private void createCell(List<T> list, HSSFRow row, HSSFCell cell, HSSFSheet sheet, HSSFCellStyle headerCellStyle, HSSFCellStyle bodyCellStyle, String exportFields) {
        try {
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    row = sheet.createRow(i + 2);
                    cell = row.createCell(0);
                    cell.setCellValue(i + 1);
                    cell.setCellStyle(bodyCellStyle);

                    T dto = list.get(i);
                    String[] fieldStrings = exportFields.split(","); // 选中的导出的字段数组
                    for (int j = 0; j < fieldStrings.length; j++) {
                        String name = fieldStrings[j];
                        String getMethodName = "get" + name.substring(0, 1).toUpperCase() + name.substring(1); // 获取方法名
                        Method method = dto.getClass().getMethod(getMethodName); // 获取dto对象中的方法名
                        Object obj = method.invoke(dto); // 获取方法名下面的值
                        cell = row.createCell(j);
                        cell = getCellValueByObject(cell, obj);
                        cell.setCellStyle(bodyCellStyle);
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * <p> @Title: getCellValueByObject</p>
     * <p> @Description: 获取单元格的值</p>
     * <p> @Param: </p>
     * <p> @Return: HSSFCell</p>
     * <p> <AUTHOR>
     */
    private HSSFCell getCellValueByObject(HSSFCell cell, Object param) {
        if (param instanceof Integer) {
            int value = ((Integer) param).intValue();
            cell.setCellValue(value);
        } else if (param instanceof String) {
            String s = (String) param;
            cell.setCellValue(s);
        } else if (param instanceof BigDecimal) {
            double doubleVal = ((BigDecimal) param).doubleValue();
            cell.setCellValue(doubleVal);
        } else if (param instanceof Double) {
            double d = ((Double) param).doubleValue();
            cell.setCellValue(d);
        } else if (param instanceof Float) {
            float f = ((Float) param).floatValue();
            cell.setCellValue(f);
        } else if (param instanceof Long) {
            long l = ((Long) param).longValue();
            cell.setCellValue(l);
        } else if (param instanceof Boolean) {
            boolean b = ((Boolean) param).booleanValue();
            cell.setCellValue(b);
        } else if (param instanceof Date) {
            Date d = (Date) param;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String time = sdf.format(d);
            cell.setCellValue(time);
        }
        return cell;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 下载导入Excel模板
     * @Date: 2020/5/18 10:40
     * @Param:
     * @return: void
     **/
    public void downloadExportExcel(String filename, String path, HttpServletResponse response, ResourceLoader resourceLoader) {
        InputStream inputStream = null;
        ServletOutputStream servletOutputStream = null;
        try {
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:" + path);
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.addHeader("charset", "utf-8");
            response.addHeader("Pragma", "no-cache");
            String encodeName = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);
            inputStream = resource.getInputStream();
            servletOutputStream = response.getOutputStream();
            IOUtils.copy(inputStream, servletOutputStream);
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (servletOutputStream != null) {
                    servletOutputStream.close();
                    servletOutputStream = null;
                }
                if (inputStream != null) {
                    inputStream.close();
                    inputStream = null;
                }
                // 召唤jvm的垃圾回收器
                System.gc();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}