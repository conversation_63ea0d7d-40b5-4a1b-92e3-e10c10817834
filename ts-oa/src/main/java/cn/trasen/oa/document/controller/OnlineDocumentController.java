package cn.trasen.oa.document.controller;


import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLConnection;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.zhuozhengsoft.pageoffice.DocumentVersion;
import com.zhuozhengsoft.pageoffice.FileSaver;
import com.zhuozhengsoft.pageoffice.OpenModeType;
import com.zhuozhengsoft.pageoffice.PageOfficeCtrl;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.document.constants.Constant;
import cn.trasen.oa.document.model.Attachment;
import cn.trasen.oa.document.model.OnlineDocument;
import cn.trasen.oa.document.model.common.DocumentConstants;
import cn.trasen.oa.document.service.AttachmentService;
import cn.trasen.oa.document.service.OnlineDocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "在线文档Controller")
@RestController
public class OnlineDocumentController {

    private static final Logger logger = LoggerFactory.getLogger(OnlineDocumentController.class);

    @Autowired
    private OnlineDocumentService onlineDocumentService;

    @Autowired
    private AttachmentService attachmentService;

    //@Autowired
    //private RedisUtil redisUtil;

    //PageOffice文件上传服务器上的物理路径
    @Value("${fileSystem}")
    private String fileSystem;

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增在线文档</p>
     * <p> @Param: </p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     */
    @ApiOperation(value = "新增在线文档", notes = "新增在线文档")
    @PostMapping("/onlineDocument/save")
    @ControllerLog(description="新增在线文档")
    public PlatformResult<String> insert(@RequestBody OnlineDocument record) {
        PlatformResult platformResult = new PlatformResult();
        try {
            // 允许的后缀名
            List<String> allowSuffixs = Lists.newArrayList();
            allowSuffixs.add(Constant.TYPE_EXCEL_XLS);
            allowSuffixs.add(Constant.TYPE_EXCEL_XLSX);
            allowSuffixs.add(Constant.TYPE_WORD_DOC);
            allowSuffixs.add(Constant.TYPE_WORD_DOCX);
            if (StringUtils.isNotBlank(record.getUploadedFile())) {
                String[] uploadedFile = record.getUploadedFile().split(",");
                List<Attachment> attachmentList = attachmentService.selectByIds(uploadedFile);
                for (Attachment attachment : attachmentList) {
                    if (!allowSuffixs.contains("." + attachment.getFileExtension())) { // 不支持的文件类型
                        platformResult.setSuccess(false);
                        platformResult.setStatusCode(0);
                        platformResult.setMessage("有不支持的文件类型、请重新上传！");
                        return platformResult;
                    }
                }
                if (onlineDocumentService.insert(record) > 0) {
                    return PlatformResult.success();
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改在线文档</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年1月13日  下午4:39:56 </p>
     */
    @ApiOperation(value = "修改在线文档信息", notes = "修改在线文档信息")
    @PostMapping("/onlineDocument/update")
    @ControllerLog(description="修改在线文档信息")
    public PlatformResult<String> update(@RequestBody OnlineDocument record) {
        try {
            if (onlineDocumentService.update(record) > 0) {
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 权限设置
     * @Date: 2020/3/9 11:56
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "权限设置", notes = "权限设置")
    @PostMapping("/onlineDocument/permissionSettings")
    public PlatformResult<String> permissionSettings(@RequestBody OnlineDocument record) {
        try {
            if (onlineDocumentService.permissionSettings(record) > 0) {
                return PlatformResult.success();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * <p> @Title: deleteById</p>
     * <p> @Description: 删除、还原、彻底删除操作方法</p>
     * <p> @Param: </p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年1月13日  上午11:15:30 </p>
     */
    @ApiOperation(value = "删除、还原、彻底删除操作方法", notes = "删除、还原、彻底删除操作方法")
    @PostMapping("/onlineDocument/method")
    public PlatformResult<String> operationMethod(@RequestBody OnlineDocument record) {
        try {
            onlineDocumentService.operationMethod(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 查询在线文档列表</p>
     * <p> @Return: DataSet<onlineDocument></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年1月13日  下午4:36:55 </p>
     */
    @ApiOperation(value = "在线文档列表", notes = "在线文档列表")
    @PostMapping("/onlineDocument/list")
    public DataSet<OnlineDocument> getDataList(Page page, OnlineDocument record) {
	    try {
    		List<OnlineDocument> list = onlineDocumentService.getDataList(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), list.size(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询回收站列表
     * @Date: 2020/3/7 15:28
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.document.model.OnlineDocument>
     **/
    @ApiOperation(value = "查询回收站列表", notes = "查询回收站列表")
    @PostMapping("/onlineDocument/recycleList")
    public DataSet<OnlineDocument> recycleList(Page page, OnlineDocument record) {
	    try {
    		List<OnlineDocument> list = onlineDocumentService.recycleList(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * <p> @Title: openOnlineDocumentForm</p>
     * <p> @Description: 在线打开Word和Excel文档</p>
     * <p> @param onlineDocument
     * <p> @param request
     * <p> @return</p>
     * <p> @Return: PlatformResult<String></p>
     * <p> <AUTHOR>
     */
    @ApiOperation(value = "在线打开Word和Excel文档", notes = "在线打开Word和Excel文档")
    @RequestMapping(value = "/onlineDocument/openOnlineDocumentForm", method = RequestMethod.GET)
    public ModelAndView openOnlineDocumentForm(HttpServletRequest request, String docType, Map<String, Object> map, String documentId, String method) {
        // 创建一个PageOfficeCtrl控件操作对象
        PageOfficeCtrl pageOfficeCtrl = new PageOfficeCtrl(request);
        // 设置PageOfficeCtrl控件的运行服务页面
        pageOfficeCtrl.setServerPage(request.getContextPath() + DocumentConstants.SERVERPAGE);
        // 设置PageOfficeCtril控件标题文字
        pageOfficeCtrl.setCaption(DocumentConstants.CAPTIONTITLE);
        // 设置PageOffice控件的文档保存页面/方法
        pageOfficeCtrl.setSaveFilePage("saveNewFile");
        // 设置PageOffice控件禁止Ctrl+S保存的JS方法
        pageOfficeCtrl.setJsFunction_AfterDocumentOpened("AfterDocumentOpened()");
        // 设置PageOffice控件是否禁用头部编辑器  false:禁用、true：不禁用
        pageOfficeCtrl.setOfficeToolbars(true);
        //map.put("method", pageOfficeCtrl.getHtmlCode(method));
        //mv.addObject("method", method);
        //pageOfficeCtrl.setSaveFilePage("saveNewFile?method=" + method);

        //PageOffice文件存储路径
        //String fileSystem = redisUtil.getTypeAndKey("PageOffice", "fileSystem");

        if ("edit".equals(method)) {
            // 设置并发时间: 单位分钟
            pageOfficeCtrl.setTimeSlice(15);
        }
        //新增、修改创建保存按钮
        if (!"view".equals(method)) {
            // 保存按钮
            pageOfficeCtrl.addCustomToolButton("保存文档", "saveAndClose", 1);
            pageOfficeCtrl.addCustomToolButton("关闭文档","Close",21);
        }
        OnlineDocument document = null;
        if (StringUtils.isNotBlank(documentId)) {
            document = onlineDocumentService.selectByPrimaryKey(documentId); //通过主键查询详情
            //map.put("document", pageOfficeCtrl.getHtmlCode(document.getId()));
            //mv.addObject("document", document);
            //pageOfficeCtrl.setSaveFilePage("saveNewFile?documentId=" + documentId);
        }

        if (document == null) {
            if ("Word".equals(docType)) {
                pageOfficeCtrl.webCreateNew(UserInfoHolder.getCurrentUserName(), DocumentVersion.Word2003);
            } else {
                pageOfficeCtrl.webCreateNew(UserInfoHolder.getCurrentUserName(), DocumentVersion.Excel2003);
            }
            //pageOfficeCtrl.setSaveFilePage("saveNewFile?docType=" + docType);
            //mv.addObject("documenttype", docType);
            //map.put("documenttype", pageOfficeCtrl.getHtmlCode(docType));
        } else {
            String basePath = fileSystem + File.separator + DocumentConstants.NEWONLINEDOCUMENTPATH; // 在线文档基础目录
            if ("1".equals(docType)) { // 修改word文档
                String filePath = basePath + File.separator + document.getId() + document.getSuffix();
                OpenModeType modeType = OpenModeType.docNormalEdit;
                if ("view".equals(method)) {
                    modeType = OpenModeType.docReadOnly;
                }
                pageOfficeCtrl.webOpen("file://" + filePath, modeType, UserInfoHolder.getCurrentUserName());
            } else { // 修改Excel文档
                String filePath = basePath + File.separator + document.getId() + document.getSuffix();
                OpenModeType modeType = OpenModeType.xlsNormalEdit;
                if ("view".equals(method)) {
                    modeType = OpenModeType.xlsReadOnly;
                }
                pageOfficeCtrl.webOpen("file://" + filePath, modeType, UserInfoHolder.getCurrentUserName());
            }
            //pageOfficeCtrl.setSaveFilePage("saveNewFile?docType=" + docType);
            //mv.addObject("documenttype", document.getDocType());
            //map.put("documenttype", pageOfficeCtrl.getHtmlCode(document.getDocType()));
        }

        pageOfficeCtrl.setSaveFilePage("saveNewFile?docType=" + docType + "&documentId=" + documentId + "&method=" + method);

        map.put("pageoffice", pageOfficeCtrl.getHtmlCode("PageOfficeCtrl1"));
        ModelAndView mv = new ModelAndView("pageOffice");
        return mv;
    }


    /**
     * @Author: Lizhihuo
     * @Description: 保存文档文件
     * @Date: 2020/3/9 11:56
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "保存文档文件", notes = "保存文档文件")
    @RequestMapping(value = "/onlineDocument/saveNewFile", method = RequestMethod.POST)
    public void saveNewFile(HttpServletRequest request, HttpServletResponse response) {
        //PageOffice文件存储路径
        //String fileSystem = redisUtil.getTypeAndKey("PageOffice", "fileSystem");

        String filePath = fileSystem + File.separator + DocumentConstants.NEWONLINEDOCUMENTPATH;
        //filePath = filePath.replace("/", "\\"); // 斜杆处理
        filePath = filePath.replace("/", System.getProperty("file.separator")).replace("\\", System.getProperty("file.separator")); // 斜杆处理

        FileSaver fs = new FileSaver(request, response);
        String docId = request.getParameter("documentId");
        String method = request.getParameter("method");
        String docType = request.getParameter("docType");

        String suffix = ""; // 后缀
        String documentType = "";
        if ("Word".equals(docType)) {
            suffix = DocumentConstants.TYPE_WORD_DOC;
            documentType = "1";
        } else {
            suffix = DocumentConstants.TYPE_EXCEL_XLS;
            documentType = "2";
        }
        OnlineDocument document = new OnlineDocument();
        if (StringUtils.isBlank(docId) || "null".equals(docId)) {
            docId = String.valueOf(IdWork.id.nextId());
            document.setId(docId);
            document.setName("新的文档");
            document.setAuthType((short) 0); // 默认为所有人公开
            document.setDocType(documentType);
            document.setSortno((short) 0);
            document.setSuffix(suffix);
            if (onlineDocumentService.insertDocument(document) == 1) {
                File file = new File(filePath);
                if (!file.exists() && !file.isDirectory()) {
                    file.mkdirs(); // 目录不存在，则创建目录。
                }
                fs.saveToFile(filePath + File.separator + docId + suffix);
                fs.setCustomSaveResult("ok"); // 设置保存结果
                fs.close();
            }
        } else {
            document = onlineDocumentService.selectByPrimaryKey(docId);
            if (onlineDocumentService.update(document) > 0) {
                fs.saveToFile(filePath + File.separator + docId + document.getSuffix());
                fs.setCustomSaveResult("ok"); // 设置保存结果
                fs.close();
            }
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 在线文档上传文件
     * @Date: 2020/3/9 11:56
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "在线文档上传文件", notes = "在线文档上传文件")
    @RequestMapping(value = "/uploadDocument", method = RequestMethod.POST)
    @ResponseBody
    public PlatformResult<String> uploadOnlineDocument(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        PlatformResult platformResult = new PlatformResult();
        //PageOffice文件存储路径
        //String fileSystem = redisUtil.getTypeAndKey("PageOffice", "fileSystem");

        String basePath = fileSystem + File.separator + DocumentConstants.NEWONLINEDOCUMENTPATH;
        //basePath = basePath.replace("/", "\\"); // 斜杆处理
        basePath = basePath.replace("/", System.getProperty("file.separator")).replace("\\", System.getProperty("file.separator")); // 斜杆处理


        // 允许的后缀名
        List<String> allowSuffixs = Lists.newArrayList();
        allowSuffixs.add(DocumentConstants.TYPE_EXCEL_XLS);
        allowSuffixs.add(DocumentConstants.TYPE_EXCEL_XLSX);
        allowSuffixs.add(DocumentConstants.TYPE_WORD_DOC);
        allowSuffixs.add(DocumentConstants.TYPE_WORD_DOCX);

        if (file != null) {
            String originalFilename = file.getOriginalFilename(); // 文件名
            String suffix = originalFilename.substring(originalFilename.lastIndexOf(".")); // 后缀
            if (originalFilename.contains("$") || originalFilename.contains(",") || originalFilename.contains(";") || originalFilename.contains("\"")) { // 含有非法字符
                platformResult.setSuccess(false);
                platformResult.setMessage("文件名含有非法字符！");
                return platformResult;
            }
            if (!allowSuffixs.contains(suffix)) { // 不支持的文件类型
                platformResult.setSuccess(false);
                platformResult.setMessage("文件名含有非法字符！");
                return platformResult;
            }
            File fileDir = new File(basePath);
            if (!fileDir.exists() && !fileDir.isDirectory()) {
                fileDir.mkdirs();
            }
            String id = String.valueOf(IdWork.id.nextId());
            String documentType = "";
            if (DocumentConstants.TYPE_WORD_DOC.equals(suffix) || DocumentConstants.TYPE_WORD_DOCX.equals(suffix)) {
                documentType = "1";
            } else {
                documentType = "2";
            }
            String filePath = basePath + File.separator + id + suffix;
            //filePath = filePath.replace("/", "\\"); // 斜杆处理
            filePath = filePath.replace("/", System.getProperty("file.separator")).replace("\\", System.getProperty("file.separator")); // 斜杆处理

            try {
                FileUtils.copyInputStreamToFile(file.getInputStream(), new File(filePath));
            } catch (IOException e) {
                platformResult.setSuccess(false);
                platformResult.setMessage("上传文件发生异常！");
                logger.error(String.format("upload exception msg=%s", e.getMessage()));
                return platformResult;
            }

            OnlineDocument document = new OnlineDocument();
            document.setId(id);
            document.setName(originalFilename);
            document.setAuthType((short) 0); // 默认为所有人公开
            document.setDocType(documentType);
            document.setCreateUser(UserInfoHolder.getCurrentUserCode());
            document.setCreateUserName(UserInfoHolder.getCurrentUserName());
            document.setSortno((short) 0);
            document.setSuffix(suffix);
            if (onlineDocumentService.insertDocument(document) < 1) {
                platformResult.setSuccess(false);
                platformResult.setMessage("上传失败！");
                return platformResult;
            }
        }
        platformResult.setSuccess(true);
        platformResult.setMessage("文件上传成功");
        platformResult.setStatusCode(200);
        return platformResult;
    }

    /**
     * @Author: Lizhihuo
     * @Description: 下载文档
     * @Date: 2020/3/9 11:56
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "在线文档上传文件", notes = "在线文档上传文件")
    @RequestMapping(value = "/downloadDoc/{documentid}", method = RequestMethod.GET)
    public void downloadDocument(HttpServletRequest request, HttpServletResponse response, @PathVariable String documentid) {
        try {
            if (StringUtils.isNotBlank(documentid)) {
                //PageOffice文件存储路径
                //String fileSystem = redisUtil.getTypeAndKey("PageOffice", "fileSystem");

                String basePath = fileSystem + File.separator + DocumentConstants.NEWONLINEDOCUMENTPATH;
                OnlineDocument document = onlineDocumentService.selectByPrimaryKey(documentid);
                if (document == null) {
                    String errorMessage = "记录不存在！";
                    OutputStream outputStream = response.getOutputStream();
                    outputStream.write(errorMessage.getBytes(Charset.forName("GB2312")));
                    outputStream.close();
                    return;
                }
                String filePath = basePath + File.separator + document.getId() + document.getSuffix();
                File file = new File(filePath);
                if (!file.exists()) {
                    String errorMessage = "文件不存在！";
                    OutputStream outputStream = response.getOutputStream();
                    outputStream.write(errorMessage.getBytes(Charset.forName("GB2312")));
                    outputStream.close();
                    return;
                }
                String contentType = URLConnection.guessContentTypeFromName(file.getName());
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                String[] name = new String[0];
                String fileName = "";
                if (document.getName().contains(".")) {
                    name = document.getName().split("\\.");
                }
                if (name.length > 1) {//存在
                    fileName = document.getName();
                } else {
                    fileName = document.getName() + document.getSuffix();
                }
                response.setContentType(contentType);
                // response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(document.getName() + document.getSuffix(), "utf-8") + "\"");
                response.setHeader("Content-Disposition", "attachment; filename=" + new String(fileName.getBytes("GBK"), "ISO8859_1"));
                response.setContentLength((int) file.length());

                InputStream inputStream = new BufferedInputStream(new FileInputStream(file));
                FileCopyUtils.copy(inputStream, response.getOutputStream());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
