package cn.trasen.oa.boardroom.service.impl;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.bpm.model.WfTask;
import cn.trasen.homs.bpm.service.WorkflowInstanceService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.message.NoticeService;
//import cn.trasen.homs.feign.workflow.vo.WfTask;
import cn.trasen.oa.boardroom.bean.BoardRoomAgendaListReq;
import cn.trasen.oa.boardroom.bean.BoardRoomAgendaSaveReq;
import cn.trasen.oa.boardroom.bean.BoardRoomApplyCancelReq;
import cn.trasen.oa.boardroom.bean.BoardRoomApplySaveReq;
import cn.trasen.oa.boardroom.bean.BoardRoomApplyTimeDetailListReq;
import cn.trasen.oa.boardroom.bean.BoardRoomApplyTimeDetailListRes;
import cn.trasen.oa.boardroom.bean.BoardRoomListParm;
import cn.trasen.oa.boardroom.bean.BoardRoomListResp;
import cn.trasen.oa.boardroom.bean.BoardRoomMeetingTimeReq;
import cn.trasen.oa.boardroom.bean.BoardRoomMeetingTimeSaveReq;
import cn.trasen.oa.boardroom.bean.BoardRoomReq;
import cn.trasen.oa.boardroom.bean.BoardRoomSignInSaveReq;
import cn.trasen.oa.boardroom.bean.BoardroomAdminListReq;
import cn.trasen.oa.boardroom.bean.BoardroomApplyCheckReq;
import cn.trasen.oa.boardroom.bean.BoardroomApplyListReq;
import cn.trasen.oa.boardroom.bean.BoardroomApplyListResp;
import cn.trasen.oa.boardroom.bean.BoardroomApplyReq;
import cn.trasen.oa.boardroom.bean.BoardroomApplyStatusReq;
import cn.trasen.oa.boardroom.bean.BoardroomApplyWorkflowListParm;
import cn.trasen.oa.boardroom.bean.BoardroomApplyWorkflowListReq;
import cn.trasen.oa.boardroom.bean.BoardroomApplyWorkflowListRes;
import cn.trasen.oa.boardroom.bean.BoardroomDeviceListResp;
import cn.trasen.oa.boardroom.bean.BoardroomSigninListReq;
import cn.trasen.oa.boardroom.bean.TaskListRes;
import cn.trasen.oa.boardroom.bo.BoardRoomApplyListInBO;
import cn.trasen.oa.boardroom.bo.BoardRoomApplyListOutBO;
import cn.trasen.oa.boardroom.bo.BoardRoomApplySaveInBO;
import cn.trasen.oa.boardroom.bo.BoardRoomHaveBookingInBO;
import cn.trasen.oa.boardroom.bo.BoardRoomMeetingCancelInBO;
import cn.trasen.oa.boardroom.bo.BoardroomApplyInBO;
import cn.trasen.oa.boardroom.commons.bean.BoardRoomAgendaBean;
import cn.trasen.oa.boardroom.commons.bean.Employee;
import cn.trasen.oa.boardroom.enums.BoardroomApplyStatusEnum;
import cn.trasen.oa.boardroom.enums.WorkflowStatusEnum;
import cn.trasen.oa.boardroom.mapper.BoardRoomApplyMapper;
import cn.trasen.oa.boardroom.mapper.BoardRoomMeetingTimeMapper;
import cn.trasen.oa.boardroom.model.BoardRoom;
import cn.trasen.oa.boardroom.model.BoardroomAdmin;
import cn.trasen.oa.boardroom.model.BoardroomAgenda;
import cn.trasen.oa.boardroom.model.BoardroomApply;
import cn.trasen.oa.boardroom.model.BoardroomMeetingTime;
import cn.trasen.oa.boardroom.model.BoardroomSignin;
import cn.trasen.oa.boardroom.properties.BoardroomProperties;
import cn.trasen.oa.boardroom.service.BoardRoomAdminService;
import cn.trasen.oa.boardroom.service.BoardRoomAgendaService;
import cn.trasen.oa.boardroom.service.BoardRoomApplyService;
import cn.trasen.oa.boardroom.service.BoardRoomDeviceService;
import cn.trasen.oa.boardroom.service.BoardRoomMeetingTimeService;
import cn.trasen.oa.boardroom.service.BoardRoomService;
import cn.trasen.oa.boardroom.service.BoardRoomSignInService;
import cn.trasen.oa.hrm.model.Schedule;
import cn.trasen.oa.hrm.service.ScheduleService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2021/10/15 14:15
 */
@Slf4j
@Service
public class BoardRoomApplyServiceImpl implements BoardRoomApplyService {

    @Autowired
    BoardRoomApplyMapper boardroomApplyMapper;

    @Autowired
    BoardRoomService boardRoomService;

    @Autowired
    BoardRoomAgendaService boardRoomAgendaService;

    @Autowired
    BoardRoomSignInService boardRoomSignInService;

    @Autowired
    WorkflowInstanceService workflowInstanceService;

    @Autowired
    HrmsEmployeeFeignService hrmsEmployeeFeignService;

    @Autowired
    WorkflowTaskService workflowTaskService;

    @Autowired
    BoardRoomMeetingTimeService boardRoomMeetingTimeService;

    @Autowired
    AppConfigProperties appConfigProperties;

    @Autowired
    ScheduleService scheduleService;

    @Autowired
    BoardroomProperties boardroomProperties;
    
    @Autowired
    BoardRoomMeetingTimeMapper boardRoomMeetingTimeMapper;
    
    @Autowired
    BoardRoomAdminService boardRoomAdminService;
    
    @Autowired
    BoardRoomDeviceService boardRoomDeviceService;

    @Override
    /**
     * 获取单个会议室
     * @param boardRoomApplyTimeDetailListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardRoomApplyTimeDetailListReq]
     * <AUTHOR>
     * @date 2021/10/16 15:10
     */
    public BoardRoomApplyTimeDetailListRes getBoardRoomApplyTimeDetail(BoardRoomApplyTimeDetailListReq boardRoomApplyTimeDetailListReq) {
        {
            List<BoardRoomApplyTimeDetailListRes> boardRoomApplyTimeDetailListResList = getBoardRoomApplyTimeDetailList(boardRoomApplyTimeDetailListReq);
            if (CollectionUtils.isEmpty(boardRoomApplyTimeDetailListResList)) {
                return null;
            }
            return boardRoomApplyTimeDetailListResList.get(0);
        }
    }


    @Override
    /**
     * 获取列表
     * @param boardRoomApplyTimeDetailListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardRoomApplyTimeDetailListReq]
     * <AUTHOR>
     * @date 2021/10/16 10:26
     */
    public List<BoardRoomApplyTimeDetailListRes> getBoardRoomApplyTimeDetailList(BoardRoomApplyTimeDetailListReq boardRoomApplyTimeDetailListReq) {

        BoardRoomListParm boardRoomListParm = new BoardRoomListParm();
        boardRoomListParm.setId(boardRoomApplyTimeDetailListReq.getBoardroomId());
        boardRoomListParm.setDeviceIdList(boardRoomApplyTimeDetailListReq.getDeviceIdList());
        boardRoomListParm.setCapacitance(boardRoomApplyTimeDetailListReq.getCapacitance());
        boardRoomListParm.setIsVideo(boardRoomApplyTimeDetailListReq.getIsVideo());
        boardRoomListParm.setApplyBeginTime(boardRoomApplyTimeDetailListReq.getStartTime());
        boardRoomListParm.setSubscribeStatus(boardRoomApplyTimeDetailListReq.getSubscribeStatus());
        boardRoomListParm.setBoardRoomStatus(1);
        boardRoomListParm.setUseRange(1);
        boardRoomListParm.setEmpCode(UserInfoHolder.getCurrentUserCode());
        boardRoomListParm.setOrgCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        List<BoardRoomListResp> boardRoomListRespList = boardRoomService.getList(boardRoomListParm);
        List<BoardRoomListResp> boardRoomListRespListNew = new ArrayList<>();
        if (boardRoomApplyTimeDetailListReq.getFreeStartTime() != null && boardRoomApplyTimeDetailListReq.getFreeEndTime() != null) {
            for (BoardRoomListResp boardRoomListResp : boardRoomListRespList) {
                if (boardRoomListResp.getDisableType() == null) {
                    boardRoomListRespListNew.add(boardRoomListResp);
                } else if(boardRoomListResp.getDisableType().equals(1)){

                    if(!(
                            (boardRoomListResp.getDisableBegintime().getTime() <= boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime()
                                    &&
                                    boardRoomListResp.getDisableEndtime().getTime() >= boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime())
                                            ||
                    (
                            (boardRoomListResp.getDisableBegintime().getTime() <= boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime()
                                    &&
                                    boardRoomListResp.getDisableEndtime().getTime() >= boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime())
                    )
                    )
                    )
                    {
                        boardRoomListRespListNew.add(boardRoomListResp);
                    }
                }
            }
            boardRoomListRespList = boardRoomListRespListNew;
        }
        if (CollectionUtils.isEmpty(boardRoomListRespList)) {
            return new ArrayList<>();
        }

        BoardRoomApplyListInBO boardRoomApplyListInBO = new BoardRoomApplyListInBO();
        BeanUtil.copyProperties(boardRoomApplyTimeDetailListReq, boardRoomApplyListInBO);
        List<String> statusList = new ArrayList<>();
        statusList.add("0");
        statusList.add("1");
        statusList.add("3");
        boardRoomApplyListInBO.setStatusList(statusList);
        
        List<BoardRoomApplyListOutBO> boardroomApplyListRespList = list(boardRoomApplyListInBO);
        List<BoardRoomApplyTimeDetailListRes> boardRoomApplyTimeDetailListResList = BeanUtil.copyToList(boardRoomListRespList, BoardRoomApplyTimeDetailListRes.class);

        for (BoardRoomListResp boardRoomListResp : boardRoomListRespList) {
            for (BoardRoomApplyTimeDetailListRes boardRoomApplyTimeDetailListRes : boardRoomApplyTimeDetailListResList) {
                if (boardRoomListResp.getId().equals(boardRoomApplyTimeDetailListRes.getId())) {

                    if (boardRoomListResp.getDisableType() != null && boardRoomListResp.getDisableType().equals(2)) {
                        BoardRoomApplyTimeDetailListRes.BoardRoomDisable boardRoomDisable = new BoardRoomApplyTimeDetailListRes.BoardRoomDisable();
                        boardRoomDisable.setBegintime("00:01");
                        boardRoomDisable.setEndtime("23:59");
                        boardRoomDisable.setRemark(boardRoomListResp.getDisableRemark());

                        boardRoomDisable.setContacts(boardRoomListResp.getDisableContacts());

                        boardRoomApplyTimeDetailListRes.setBoardRoomDisable(boardRoomDisable);
                    } else if (
                            boardRoomListResp.getDisableBegintime() != null &&
                                    boardRoomListResp.getBookingTimeEnd() != null
                    ) {

                        if (DateUtil.parse(DateUtil.format(boardRoomListResp.getDisableBegintime(), "yyyy-MM-dd"), "yyyy-MM-dd").getTime() <= boardRoomApplyTimeDetailListReq.getStartTime().getTime() && boardRoomListResp.getDisableEndtime().getTime() >= boardRoomApplyTimeDetailListReq.getStartTime().getTime()) {
                            BoardRoomApplyTimeDetailListRes.BoardRoomDisable boardRoomDisable = new BoardRoomApplyTimeDetailListRes.BoardRoomDisable();

                            if (DateUtil.format(boardRoomListResp.getDisableBegintime(), "yyyyMMdd").equals(DateUtil.format(boardRoomApplyTimeDetailListReq.getStartTime(), "yyyyMMdd"))) {
                                boardRoomDisable.setBegintime(DateUtil.format(boardRoomListResp.getDisableBegintime(), "HH:mm"));
                            } else {
                                boardRoomDisable.setBegintime("00:01");
                            }
                            if (DateUtil.format(boardRoomListResp.getDisableEndtime(), "yyyyMMdd").equals(DateUtil.format(boardRoomApplyTimeDetailListReq.getStartTime(), "yyyyMMdd"))) {
                                boardRoomDisable.setEndtime(DateUtil.format(boardRoomListResp.getDisableEndtime(), "HH:mm"));
                            } else {
                                boardRoomDisable.setEndtime("23:59");
                            }
                            boardRoomDisable.setContacts(boardRoomListResp.getDisableContacts());
                            boardRoomDisable.setRemark(boardRoomListResp.getDisableRemark());
                            boardRoomApplyTimeDetailListRes.setBoardRoomDisable(boardRoomDisable);

                        }

                    }
                    break;
                }
            }
        }


        for (BoardRoomApplyTimeDetailListRes boardRoomApplyTimeDetailListRes : boardRoomApplyTimeDetailListResList) {
            List<BoardRoomApplyListOutBO> usrBoardroomApplyListRespList = new ArrayList<>();
            for (BoardRoomApplyListOutBO boardroomApplyListResp : boardroomApplyListRespList) {
                if (boardRoomApplyTimeDetailListRes.getId().equals(boardroomApplyListResp.getBoardroomId())) {
                    usrBoardroomApplyListRespList.add(boardroomApplyListResp);
                }
            }
            boardRoomApplyTimeDetailListRes.setBoardroomApplyList(usrBoardroomApplyListRespList);
        }

        if (boardRoomApplyTimeDetailListReq.getFreeStartTime() == null || boardRoomApplyTimeDetailListReq.getFreeEndTime() == null) {
            return boardRoomApplyTimeDetailListResList;
        } else {
            List<BoardRoomApplyTimeDetailListRes> boardRoomApplyTimeDetailListResListNew = new ArrayList<>();
            for (BoardRoomApplyTimeDetailListRes boardRoomApplyTimeDetailListRes : boardRoomApplyTimeDetailListResList) {

                if (CollectionUtils.isEmpty(boardRoomApplyTimeDetailListRes.getBoardroomApplyList())) {
                    boardRoomApplyTimeDetailListResListNew.add(boardRoomApplyTimeDetailListRes);
                } else {
                    boolean have = false;
                    if (boardRoomApplyTimeDetailListReq.getFreeStartTime() != null && boardRoomApplyTimeDetailListReq.getFreeEndTime() != null) {
                        for (BoardRoomApplyListOutBO b : boardRoomApplyTimeDetailListRes.getBoardroomApplyList()) {

                            if (
                                    ((b.getStartTime().getTime() > boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime()
                                            &&
                                            b.getStartTime().getTime() < boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime())
                                            ||
                                            (b.getMeetingEndTime().getTime() > boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime()
                                                    &&
                                                    b.getMeetingEndTime().getTime() < boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime())
                                    )
                                            ||
                                            (
                                                    (b.getStartTime().getTime() < boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime()
                                                            &&
                                                            b.getMeetingEndTime().getTime() > boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime())
                                                            &&
                                                            (b.getStartTime().getTime() < boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime()
                                                                    &&
                                                                    b.getMeetingEndTime().getTime() > boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime())
                                            )
                                            ||
                                            (
                                                    (b.getStartTime().getTime() <= boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime()
                                                            &&
                                                            b.getMeetingEndTime().getTime() >= boardRoomApplyTimeDetailListReq.getFreeStartTime().getTime())
                                            )
                                            ||
                                            (
                                                    (b.getStartTime().getTime() <= boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime()
                                                            &&
                                                            b.getMeetingEndTime().getTime() >= boardRoomApplyTimeDetailListReq.getFreeEndTime().getTime())
                                            )


                            ) {
                                have = true;
                                break;
                            }
                        }
                    }
                    if (have == false) {
                        boardRoomApplyTimeDetailListResListNew.add(boardRoomApplyTimeDetailListRes);
                    }
                }
            }
            return boardRoomApplyTimeDetailListResListNew;
        }
    }


    @Override

    /**
     * 获取
     * @param boardroomApplyReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyReq]
     * <AUTHOR>
     * @date 2021/10/16 16:48
     */
    public BoardroomApplyListResp get(BoardroomApplyReq boardroomApplyReq) {
        BoardroomApplyListReq boardroomApplyListReq = new BoardroomApplyListReq();
        boardroomApplyListReq.setBoardroomId(boardroomApplyReq.getBoardroomId());
        boardroomApplyListReq.setId(boardroomApplyReq.getId());
        List<BoardroomApplyListResp> boardroomApplyListRespList = this.getList(boardroomApplyListReq);
        if (CollectionUtils.isEmpty(boardroomApplyListRespList)) {
            return null;
        }
        BoardroomApplyListResp boardroomApplyListResp = boardroomApplyListRespList.get(0);




        return boardroomApplyListResp;
    }

    @Override
    /**
     * 获取基础数据
     *
     * @param boardroomApplyReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyReq]
     * <AUTHOR>
     * @date 2021/10/19 16:26
     */
    public List<BoardroomApply> getBaseList(BoardRoomApplyListInBO boardRoomApplyListInBO) {
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (CollectionUtils.isEmpty(boardRoomApplyListInBO.getApplyIdList()) == false) {
            criteria.andIn("id", boardRoomApplyListInBO.getApplyIdList());
        }
        return boardroomApplyMapper.selectByExample(example);
    }

    @Override
    /**
     * 获取基础数据
     *
     * @param boardroomApplyReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyReq]
     * <AUTHOR>
     * @date 2021/10/19 16:26
     */
    public BoardroomApply getBase(BoardroomApplyInBO boardroomApplyInBO) {
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        
        criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        if (!StringUtils.isBlank(boardroomApplyInBO.getId())) {
            criteria.andEqualTo("id", boardroomApplyInBO.getId());
        }
        if (!StringUtils.isBlank(boardroomApplyInBO.getBusinessId())) {
            criteria.andEqualTo("businessId", boardroomApplyInBO.getBusinessId());
        }

        if (CollectionUtils.isEmpty(boardroomApplyInBO.getStatusList()) == false) {
            criteria.andIn("status", boardroomApplyInBO.getStatusList());
        }

        if (StringUtils.isBlank(boardroomApplyInBO.getBoardroomId()) == false) {
            criteria.andEqualTo("boardroomId", boardroomApplyInBO.getBoardroomId());
        }
//        if (boardroomApplyInBO.getApplyTimeInterval() != null) {
//            StringBuilder sqlStringBuilder = new StringBuilder();
//
//            sqlStringBuilder.append("( ((START_TIME>='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyStartTime(), "yyyy-MM-dd HH:mm:ss") + "' and START_TIME<='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyEndTime(), "yyyy-MM-dd HH:mm:ss") + "')");
//            sqlStringBuilder.append("or ");
//            sqlStringBuilder.append("(END_TIME>='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyStartTime(), "yyyy-MM-dd HH:mm:ss") + "' and END_TIME<='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyEndTime(), "yyyy-MM-dd HH:mm:ss") + "'))");
//
//            sqlStringBuilder.append("or ");
//
//            sqlStringBuilder.append(" ((START_TIME<='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyStartTime(), "yyyy-MM-dd HH:mm:ss") + "' and END_TIME>='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyStartTime(), "yyyy-MM-dd HH:mm:ss") + "')");
//            sqlStringBuilder.append("and ");
//            sqlStringBuilder.append("(START_TIME<='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyEndTime(), "yyyy-MM-dd HH:mm:ss") + "' and END_TIME>='" + DateUtil.format(boardroomApplyInBO.getApplyTimeInterval().getApplyEndTime(), "yyyy-MM-dd HH:mm:ss") + "')))");
//
//            criteria.andCondition(sqlStringBuilder.toString());
//        }

        if (boardroomApplyInBO.getGteStartTime() != null) {
            criteria.andGreaterThanOrEqualTo("startTime", boardroomApplyInBO.getGteStartTime());
        }


        example.setOrderByClause(" id LIMIT 1");

        return boardroomApplyMapper.selectOneByExample(example);
    }


    /**
    * 会议室是否可以预定
    * @param boardRoomHaveBookingInBO
    * @return java.lang.Integer
    * <AUTHOR>
    * @date 2022/1/20 19:00
    */
    public  Integer getHaveBooking(BoardRoomHaveBookingInBO boardRoomHaveBookingInBO) {
        return boardroomApplyMapper.getHaveBooking(boardRoomHaveBookingInBO);
    }

    /**
     * 获取预定列表
     *
     * @param boardRoomApplyListInBO
     * @return java.util.List<cn.trasen.oa.boardroom.bo.BoardRoomApplyListOutBO>
     * <AUTHOR>
     * @date 2022/1/12 15:11
     */
    public List<BoardRoomApplyListOutBO> list(BoardRoomApplyListInBO boardRoomApplyListInBO) {
    	boardRoomApplyListInBO.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<BoardRoomApplyListOutBO> boardroomApplyListRespList = boardroomApplyMapper.list(boardRoomApplyListInBO);
        Set<String> employeeNos = new HashSet<>();
        List<String> applyIdList = new ArrayList<>();
        for (BoardRoomApplyListOutBO boardRoomApplyListOutBO : boardroomApplyListRespList) {
            if (StringUtils.isBlank(boardRoomApplyListOutBO.getApplyEmp()) == false) {
                employeeNos.add(boardRoomApplyListOutBO.getApplyEmp());
            }
            applyIdList.add(boardRoomApplyListOutBO.getId());
        }
        BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
        boardroomSigninListReq.setInvitee("1");
        boardroomSigninListReq.setApplyIdList(applyIdList);
        List<BoardroomSignin> boardroomSigninList = new ArrayList<>();
        if (CollectionUtils.isEmpty(applyIdList) == false) {
            boardroomSigninList = boardRoomSignInService.getBaseList(boardroomSigninListReq);
            for (BoardroomSignin boardroomSignin : boardroomSigninList) {
                employeeNos.add(boardroomSignin.getSigninUsercode());
            }
        }

        List<EmployeeResp> employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList<>(employeeNos)).getObject();


        for (BoardRoomApplyListOutBO boardRoomApplyListOutBO : boardroomApplyListRespList) {
            boardRoomApplyListOutBO.setStatusLable(BoardroomApplyStatusEnum.getValByKey(boardRoomApplyListOutBO.getStatus()));
            for (EmployeeResp employeeResp : employeeRespList) {
                if (employeeResp.getEmployeeNo().equals(boardRoomApplyListOutBO.getApplyEmp())) {
                    Employee applyEmployee = new Employee();
                    applyEmployee.setEmployeeId(employeeResp.getEmployeeId());
                    applyEmployee.setEmployeeName(employeeResp.getEmployeeName());
                    applyEmployee.setEmployeeNo(employeeResp.getEmployeeNo());
                    applyEmployee.setPhoneNumber(employeeResp.getPhoneNumber());
                    applyEmployee.setOrgId(employeeResp.getOrgId());
                    applyEmployee.setOrgName(employeeResp.getOrgName());
                    boardRoomApplyListOutBO.setApplyEmployee(applyEmployee);
                    break;
                }
            }

            List<BoardRoomApplyListOutBO.AttendEmployee> attendEmployeeList = new ArrayList<>();
            for (BoardroomSignin boardroomSignin : boardroomSigninList) {
                if (boardroomSignin.getApplyId().equals(boardRoomApplyListOutBO.getId())) {
                    for (EmployeeResp employeeResp : employeeRespList) {
                        if (employeeResp.getEmployeeNo().equals(boardroomSignin.getSigninUsercode())) {
                            BoardRoomApplyListOutBO.AttendEmployee attendEmployee = new BoardRoomApplyListOutBO.AttendEmployee();
                            attendEmployee.setUsercode(employeeResp.getEmployeeNo());
                            attendEmployee.setUsername(employeeResp.getEmployeeName());
                            attendEmployeeList.add(attendEmployee);
                            break;
                        }
                    }
                }
            }
            boardRoomApplyListOutBO.setAttendEmployeeList(attendEmployeeList);


            BoardRoomAgendaListReq boardRoomAgendaListReq = new BoardRoomAgendaListReq();
            boardRoomAgendaListReq.setApplyId(boardRoomApplyListOutBO.getId());
            boardRoomApplyListOutBO.setAgendaList(BeanUtil.copyToList(boardRoomAgendaService.getList(boardRoomAgendaListReq), BoardRoomAgendaBean.class));
        }
        return boardroomApplyListRespList;
    }

    @Override
    /**
     * 获取列表
     * @param boardroomApplyListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyListReq]
     * <AUTHOR>
     * @date 2021/10/16 10:29
     */
    public List<BoardroomApplyListResp> getList(BoardroomApplyListReq boardroomApplyListReq) {

    	boardroomApplyListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<BoardroomApplyListResp> boardroomApplyListRespList = boardroomApplyMapper.getList(boardroomApplyListReq);


        Set<String> employeeNos = new HashSet<>();
        List<String> applyIdList = new ArrayList<>();


        for (BoardroomApplyListResp boardroomApplyListResp : boardroomApplyListRespList) {
            employeeNos.add(boardroomApplyListResp.getApplyEmp());
            applyIdList.add(boardroomApplyListResp.getId());
        }


        BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
        boardroomSigninListReq.setInvitee("1");
        boardroomSigninListReq.setApplyIdList(applyIdList);

        List<BoardroomSignin> boardroomSigninList = new ArrayList<>();
        if (CollectionUtils.isEmpty(applyIdList) == false) {
            boardroomSigninList = boardRoomSignInService.getBaseList(boardroomSigninListReq);

            for (BoardroomSignin boardroomSignin : boardroomSigninList) {
                employeeNos.add(boardroomSignin.getSigninUsercode());
            }
        }

        List<EmployeeResp> employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList<>(employeeNos)).getObject();


        for (BoardroomApplyListResp boardroomApplyListResp : boardroomApplyListRespList) {

            boardroomApplyListResp.setWfInstanceId(boardroomApplyListResp.getEventid());

            boardroomApplyListResp.setStatusLable(BoardroomApplyStatusEnum.getValByKey(boardroomApplyListResp.getStatus()));
            for (EmployeeResp employeeResp : employeeRespList) {
                if (employeeResp.getEmployeeNo().equals(boardroomApplyListResp.getApplyEmp())) {
                    BoardroomApplyListResp.ApplyEmployee applyEmployee = new BoardroomApplyListResp.ApplyEmployee();
                    applyEmployee.setEmployeeId(employeeResp.getEmployeeId());
                    applyEmployee.setEmployeeName(employeeResp.getEmployeeName());
                    applyEmployee.setEmployeeNo(employeeResp.getEmployeeNo());
                    applyEmployee.setPhoneNumber(employeeResp.getPhoneNumber());
                    applyEmployee.setOrgId(employeeResp.getOrgId());
                    applyEmployee.setOrgName(employeeResp.getOrgName());
                    boardroomApplyListResp.setApplyEmployee(applyEmployee);
                    break;
                }
            }

            List<BoardroomApplyListResp.AttendEmployee> attendEmployeeList = new ArrayList<>();
            for (BoardroomSignin boardroomSignin : boardroomSigninList) {
                if (boardroomSignin.getApplyId().equals(boardroomApplyListResp.getId())) {
                    for (EmployeeResp employeeResp : employeeRespList) {
                        if (employeeResp.getEmployeeNo().equals(boardroomSignin.getSigninUsercode())) {
                            BoardroomApplyListResp.AttendEmployee attendEmployee = new BoardroomApplyListResp.AttendEmployee();
                            attendEmployee.setUsercode(employeeResp.getEmployeeNo());
                            attendEmployee.setUsername(employeeResp.getEmployeeName());
                            attendEmployeeList.add(attendEmployee);
                            break;
                        }
                    }
                }
            }
            boardroomApplyListResp.setAttendEmployeeList(attendEmployeeList);


            BoardRoomAgendaListReq boardRoomAgendaListReq = new BoardRoomAgendaListReq();
            boardRoomAgendaListReq.setApplyId(boardroomApplyListResp.getId());
            boardroomApplyListResp.setAgendaList(BeanUtil.copyToList(boardRoomAgendaService.getList(boardRoomAgendaListReq), BoardroomApplyListResp.Agenda.class));
        }
        return boardroomApplyListRespList;
    }


    @Override
    /**
     * 新增
     *
     * @param boardRoomApplySaveReq
     * @return [cn.trasen.oa.boardroom.bean.BoardRoomApplySaveReq]
     * <AUTHOR>
     * @date 2021/10/15 14:16
     */
    @Transactional(rollbackFor = Exception.class)
    public String add(BoardRoomApplySaveReq boardRoomApplySaveReq) {
        if (CollectionUtils.isEmpty(boardRoomApplySaveReq.getAttendEmployeeNoList()) == false) {
        	log.info("参会人员no：" + boardRoomApplySaveReq.getAttendEmployeeNoList().size());
            for (String no : boardRoomApplySaveReq.getAttendEmployeeNoList()) {
            	log.info("参会人员no：" + no);
                if (StringUtils.isBlank(no)) {
                    throw new BusinessException("参会人非法！");
                }
            }
        } else {
            throw new BusinessException("参会人不能为空！");
        }

        BoardroomApplyCheckReq boardroomApplyCheckReq = new BoardroomApplyCheckReq();
        boardroomApplyCheckReq.setBoardroomId(boardRoomApplySaveReq.getBoardroomId());
        boardroomApplyCheckReq.setStartTime(boardRoomApplySaveReq.getStartTime());
        boardroomApplyCheckReq.setEndTime(boardRoomApplySaveReq.getEndTime());
        this.check(boardroomApplyCheckReq);
        BoardroomApply boardroomApply = BeanUtils.addInitBean(BoardroomApply.class);
        BeanUtil.copyProperties(boardRoomApplySaveReq, boardroomApply, "id");
        BoardRoomReq boardRoomReq = new BoardRoomReq();
        boardRoomReq.setId(boardRoomApplySaveReq.getBoardroomId());
        BoardRoom boardRoom = boardRoomService.getBase(boardRoomReq);//会议室基础信息
        //  boardroomApply.setStatus("0");
        String workflowID = "";
        String businessId = boardroomApply.getId();
        String meetingId = "";
        // List<String> meetingIdList=new ArrayList<>();
//        if ("0".equals(boardRoom.getNeedfFlow())) {
//
//        } else

        boardroomApply.setStatus("1");
        boardroomApply.setCheckTime(new Date());
        BoardRoomMeetingTimeSaveReq boardRoomMeetingTimeSaveReq = new BoardRoomMeetingTimeSaveReq();
        boardRoomMeetingTimeSaveReq.setApplyId(boardroomApply.getId());
        boardRoomMeetingTimeSaveReq.setStartTime(boardroomApply.getStartTime());
        boardRoomMeetingTimeSaveReq.setEndTime(boardroomApply.getEndTime());
        boardRoomMeetingTimeSaveReq.setBoardroomId(boardroomApply.getBoardroomId());
        boardRoomMeetingTimeSaveReq.setIswxadd("0");
        boardRoomMeetingTimeSaveReq.setStatus("0");
        boardRoomMeetingTimeSaveReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        boardRoomMeetingTimeService.add(boardRoomMeetingTimeSaveReq);
        meetingId = boardRoomMeetingTimeSaveReq.getId();


        if ("1".equals(boardRoom.getNeedfFlow())) {
            boardroomApply.setStatus("0");
            Map<String, Object> map = new HashMap<>(1);
            // 业务id
            map.put("L_BusinessId", businessId);
            //指定审批人为会议室管理员
            BoardroomAdminListReq boardroomAdminListReq = new BoardroomAdminListReq();
            boardroomAdminListReq.setBoardroomId(boardRoomApplySaveReq.getBoardroomId());
            List<BoardroomAdmin> baseList = boardRoomAdminService.getBaseList(boardroomAdminListReq);
            List<String> L_UserIds = new ArrayList<>();
            List<String> L_UserNames = new ArrayList<>();
            for (BoardroomAdmin boardroomAdmin : baseList) {
            	L_UserIds.add(boardroomAdmin.getUseCode());
            	L_UserNames.add(boardroomAdmin.getUseName());
            }
            
            if(L_UserIds.size() > 0){
            	map.put("L_UserIds", String.join(",", L_UserIds));
                map.put("L_UserNames", String.join(",", L_UserNames));
            }else{
            	throw new BusinessException("未设置会议室管理员！");
            }
            
            workflowID = workflowInstanceService.doStartProcessInstance(boardRoom.getDefaultProcessid(), map);

            if (StringUtils.isBlank(workflowID)) {
                throw new BusinessException("流程启动失败！");
            }
        }

        boardroomApply.setEventid(workflowID);
        boardroomApply.setBusinessId(businessId);
        List<BoardRoomAgendaSaveReq> boardRoomAgendaSaveReqList = new ArrayList<>();
        if (CollectionUtils.isEmpty(boardRoomApplySaveReq.getAgendaList()) == false) {
            for (BoardRoomApplySaveReq.Agenda agenda : boardRoomApplySaveReq.getAgendaList()) {
                BoardRoomAgendaSaveReq boardRoomAgendaSaveReq = BeanUtil.copyProperties(agenda, BoardRoomAgendaSaveReq.class);
                boardRoomAgendaSaveReq.setApplyId(boardroomApply.getId());
                boardRoomAgendaSaveReqList.add(boardRoomAgendaSaveReq);
            }
        }
        boardRoomAgendaService.save(boardRoomAgendaSaveReqList);
        boardroomApply.setApplyEmp(UserInfoHolder.getCurrentUserCode());
        boardroomApply.setApplyDate(new Date());
        boardroomApply.setApplyOrg(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        boardroomApply.setApplyOrgname(UserInfoHolder.getCurrentUserInfo().getDeptname());
        boardroomApply.setApplyEmpname(UserInfoHolder.getCurrentUserName());
        List<BoardRoomSignInSaveReq> boardRoomSignInSaveReqList = new ArrayList<>();
        
        //处理消息接收人
        if (CollectionUtils.isEmpty(boardRoomApplySaveReq.getAttendEmployeeNoList()) == false) {
        	 for (String no : boardRoomApplySaveReq.getAttendEmployeeNoList()) {
                 BoardRoomSignInSaveReq boardRoomSignInSaveReq = new BoardRoomSignInSaveReq();
                 boardRoomSignInSaveReq.setApplyId(boardroomApply.getId());
                 boardRoomSignInSaveReq.setInvitee("1");
                 boardRoomSignInSaveReq.setSigninUsercode(no);
                 boardRoomSignInSaveReq.setMeetingTimeId(meetingId);
                 boardRoomSignInSaveReqList.add(boardRoomSignInSaveReq);
             }
        }
        boardRoomSignInService.save(boardRoomSignInSaveReqList);
        
        boardroomApply.setOrgCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
        boardroomApply.setDestineDate(new Date());
        boardroomApply.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        boardroomApplyMapper.insertSelective(boardroomApply);

        //会议室配置了需要审批 并且 配置审批完成才推送会议通知
        boolean pushMessage = true;
        if("1".equals(boardRoom.getNeedfFlow()) && "1".equals(boardRoom.getApprovePush())){
        	pushMessage = false;
        }
        
        if (boardroomApply.getRepeatRate() > 0 && pushMessage) {
            addAfter(boardroomApply, boardRoomApplySaveReq.getAttendEmployeeNoList(), boardRoomApplySaveReq.getAgendaList());
        }

        EmployeeResp employeeResp = hrmsEmployeeFeignService.getEmployeeDetailByCode(boardroomApply.getApplyEmp()).getObject();

        List<String> receiverList = boardRoomApplySaveReq.getAttendEmployeeNoList();
       
        if(pushMessage) {
        	 NoticeReq notice =
                     NoticeReq.builder()
                             .content("时间：" + DateUtil.format(boardroomApply.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardroomApply.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardroomApply.getEndTime(), "HH:mm") + "\n" + "地点：" + boardRoom.getLocation() + "-" + boardRoom.getFloor() + "-" + boardRoom.getName() + "\n" + "主题：" + boardroomApply.getMotif()+ "\n" + "备注：" +  boardroomApply.getRemark())
                             .noticeType("3")
                             .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
                             .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                             .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                             .subject(employeeResp.getOrgName() + "-" + employeeResp.getEmployeeName() + "邀请您参加会议")
                             .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
                             .wxSendType("1")
                             .businessId(businessId)
                             .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
                             .build();
            
             NoticeService.sendAsynNotice(notice);
        }
       
        
        if(StringUtils.isNotEmpty(boardRoom.getMessagePush())) {
        	//String  messagePush  =   boardRoom.getMessagePush();
        	//List<String> messagePushReceiverList = Arrays.asList(messagePush.split(","));//将String转换成list
        	try {
        		 NoticeReq notice2 =
        	                NoticeReq.builder()
        	                        .content("时间：" + DateUtil.format(boardroomApply.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardroomApply.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardroomApply.getEndTime(), "HH:mm") + "\n" + "地点：" + boardRoom.getLocation() + "-" + boardRoom.getFloor() + "-" + boardRoom.getName() + "\n" + "主题：" + boardroomApply.getMotif()+ "\n" + "备注：" +  boardroomApply.getRemark())
        	                        .noticeType("3")
        	                        .receiver(boardRoom.getMessagePush())  //接收人用,隔开的
        	                        .sender(UserInfoHolder.getCurrentUserCode()) //发送人
        	                        .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
        	                        .subject(employeeResp.getOrgName() + "-" + employeeResp.getEmployeeName() + "预定了"+boardRoom.getName())
        	                        .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
        	                        .wxSendType("1")
        	                        .businessId(businessId)
        	                        .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
        	                        .build();
        		 NoticeService.sendAsynNotice(notice2);
		        } catch (Exception e) {
		            log.error("推送会议消息给管理人员失败,失败原因：", e.getMessage(), e);
		        }
        }


        if (!appConfigProperties.getOAOpenGatewayRibbon().getEnabled()) {
            try {
                List<Schedule> scheduleList = new ArrayList<>();
                for (BoardRoomSignInSaveReq boardRoomSignInSaveReq : boardRoomSignInSaveReqList) {
                    Schedule schedule = new Schedule();
                    schedule.setId(String.valueOf(IdWork.id.nextId()));
                    schedule.setCreateDate(new Date());
                    schedule.setCreateUser(boardRoomSignInSaveReq.getSigninUsercode());
                    schedule.setIsRemind(1);
                    schedule.setScheduleType("会议日程");
                    schedule.setScheduleDate(boardRoomApplySaveReq.getStartTime());
                    schedule.setScheduleStartTime(DateUtil.format(boardRoomApplySaveReq.getStartTime(), "HH:mm"));
                    schedule.setScheduleEndTime(DateUtil.format(boardRoomApplySaveReq.getEndTime(), "HH:mm"));
                    schedule.setScheduleSubject(boardRoomApplySaveReq.getMotif());
                    schedule.setRemindTime("15");
                    schedule.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                    scheduleList.add(schedule);
                }
                scheduleService.batchInsert(scheduleList);
            } catch (Exception e) {
                log.error("推送会议日程失败,失败原因：", e.getMessage(), e);
            }
        }
        return boardroomApply.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
    * 修改
    * @param boardRoomApplySaveInBO
    * @return void
    * <AUTHOR>
    * @date 2022/1/19 16:04
    */
    public void modify(BoardRoomApplySaveInBO boardRoomApplySaveInBO) {
    	BoardroomApply boardroomApplyNow  =  boardroomApplyMapper.selectByPrimaryKey(boardRoomApplySaveInBO.getId());
    	String status = "1";
    	if(boardroomApplyNow != null){
    		 status = boardroomApplyNow.getStatus() ;
    	}
    	
    	BoardroomApply boardroomApply = new BoardroomApply();
    	boardroomApply.setId(boardRoomApplySaveInBO.getId());
    	boardroomApply.setStatus("4");
    	boardroomApply.setDevice(boardRoomApplySaveInBO.getDevice());
    	boardroomApply.setAttendEmployeeInput(boardRoomApplySaveInBO.getAttendEmployeeInput());
    	boardroomApplyMapper.updateByPrimaryKeySelective(boardroomApply);
    	
        BoardroomApplyCheckReq boardroomApplyCheckReq = new BoardroomApplyCheckReq();
        boardroomApplyCheckReq.setBoardroomId(boardRoomApplySaveInBO.getBoardroomId());
        boardroomApplyCheckReq.setStartTime(boardRoomApplySaveInBO.getStartTime());
        boardroomApplyCheckReq.setEndTime(boardRoomApplySaveInBO.getEndTime());
        this.check(boardroomApplyCheckReq);
        
        BoardroomMeetingTime boardroomMeetingTime = new BoardroomMeetingTime();
        boardroomMeetingTime.setStartTime(boardRoomApplySaveInBO.getStartTime());
        boardroomMeetingTime.setEndTime(boardRoomApplySaveInBO.getEndTime());
        boardroomMeetingTime.setUpdateDate(new Date());
        boardroomMeetingTime.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        Example example = new Example(BoardroomMeetingTime.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("applyId", boardRoomApplySaveInBO.getId());
        boardRoomMeetingTimeMapper.updateByExampleSelective(boardroomMeetingTime,example);
       
        //boardRoomMeetingTimeService.add(boardRoomMeetingTimeSaveReq);
        
        
        boardroomApply.setStatus(status);
        BoardRoomReq boardRoomReq = new BoardRoomReq();
        boardRoomReq.setId(boardRoomApplySaveInBO.getBoardroomId());
        BoardRoom boardRoom = boardRoomService.getBase(boardRoomReq);
        if(boardroomApplyNow != null && boardRoomApplySaveInBO != null && ( !boardroomApplyNow.getStartTime().equals(boardRoomApplySaveInBO.getStartTime()) 
       		 || !boardroomApplyNow.getEndTime().equals(boardRoomApplySaveInBO.getEndTime())  ||  !boardroomApplyNow.getMotif().equals(boardRoomApplySaveInBO.getMotif()) )  ){
        	//修改了时间或者主题重新发起流程
        	 String workflowID = "";
             String businessId = boardroomApply.getId();
             if ("1".equals(boardRoom.getNeedfFlow())) {
            	////2024-01-24重新发起流程前，要先撤销原有流程，否则流程实例会有两条数据
            	 BoardroomApply BoardroomApplyWorkflow =  boardroomApplyMapper.selectByPrimaryKey(boardRoomApplySaveInBO.getId());
//            	 Map<String, Object> parameterMap = new HashMap<>();
//            	 parameterMap.put("reason", "会议修改撤销、重新发起");
//            	 parameterMap.put("workflowInstId", BoardroomApplyWorkflow.getEventid());
//            	 workflowFeignService.doUndoInstance(parameterMap);//流程撤销
            	 workflowInstanceService.doUndoInstance(BoardroomApplyWorkflow.getEventid(), "会议修改撤销、重新发起");
            	////
                 boardroomApply.setStatus("0");
                 Map<String, Object> map = new HashMap<>(1);
                 // 业务id
                 map.put("L_BusinessId", businessId);
               //指定审批人为会议室管理员
                 BoardroomAdminListReq boardroomAdminListReq = new BoardroomAdminListReq();
                 boardroomAdminListReq.setBoardroomId(boardRoomApplySaveInBO.getBoardroomId());
                 List<BoardroomAdmin> baseList = boardRoomAdminService.getBaseList(boardroomAdminListReq);
                 List<String> L_UserIds = new ArrayList<>();
                 List<String> L_UserNames = new ArrayList<>();
                 for (BoardroomAdmin boardroomAdmin : baseList) {
                	 L_UserIds.add(boardroomAdmin.getUseCode());
                	 L_UserNames.add(boardroomAdmin.getUseName());
                 }
                 
                 if(L_UserIds.size() > 0){
                	 map.put("L_UserIds", String.join(",", L_UserIds));
                     map.put("L_UserNames", String.join(",", L_UserNames));
                 }else{
                  throw new BusinessException("未设置会议室管理员！");
                 }
                 workflowID = workflowInstanceService.doStartProcessInstance(boardRoom.getDefaultProcessid(), map);

                 if (StringUtils.isBlank(workflowID)) {
                     throw new BusinessException("流程启动失败！");
                 }
             }
             boardroomApply.setEventid(workflowID);
             boardroomApply.setBusinessId(businessId);
        }
       
        
        boardroomApply.setSignOutType(boardRoomApplySaveInBO.getSignOutType());
        boardroomApply.setSignOutAdvanceMinute(boardRoomApplySaveInBO.getSignOutAdvanceMinute());
        boardroomApply.setSendRemindType(boardRoomApplySaveInBO.getSendRemindType());
        boardroomApply.setSendRemindAdvanceMinute(boardRoomApplySaveInBO.getSendRemindAdvanceMinute());
        boardroomApply.setAccessoryId(boardRoomApplySaveInBO.getAccessoryId());
        boardroomApply.setMotif(boardRoomApplySaveInBO.getMotif());
        boardroomApply.setStartTime(boardRoomApplySaveInBO.getStartTime());
        boardroomApply.setEndTime(boardRoomApplySaveInBO.getEndTime());
        boardroomApply.setAutoRefreshSignInQrCodeType(boardRoomApplySaveInBO.getAutoRefreshSignInQrCodeType());
        boardroomApply.setAutoRefreshSignInQrCodeDate(boardRoomApplySaveInBO.getAutoRefreshSignInQrCodeDate());
        BeanUtils.updateInitBean(boardroomApply);
        boardroomApplyMapper.updateByPrimaryKeySelective(boardroomApply);
        List<BoardRoomAgendaSaveReq> boardRoomAgendaSaveReqList = new ArrayList<>();
        if (CollectionUtils.isEmpty(boardRoomApplySaveInBO.getAgendaList()) == false) {
            for (BoardRoomAgendaBean agenda : boardRoomApplySaveInBO.getAgendaList()) {
                BoardRoomAgendaSaveReq boardRoomAgendaSaveReq = BeanUtil.copyProperties(agenda, BoardRoomAgendaSaveReq.class);
                boardRoomAgendaSaveReq.setApplyId(boardroomApply.getId());
                boardRoomAgendaSaveReqList.add(boardRoomAgendaSaveReq);
            }
        }
        boardRoomAgendaService.save(boardRoomAgendaSaveReqList);
        
        boolean pushMessage = true;
        if("1".equals(boardRoom.getNeedfFlow()) && "1".equals(boardRoom.getApprovePush())){
        	pushMessage = false;
        }
        
        if(pushMessage) {
        	EmployeeResp employeeResp = hrmsEmployeeFeignService.getEmployeeDetailByCode(boardroomApplyNow.getApplyEmp()).getObject();
            List<String> receiverList = boardRoomApplySaveInBO.getAttendEmployeeNoList();
            
            String content = "";
            if(boardroomApplyNow != null && boardRoomApplySaveInBO != null && ( !boardroomApplyNow.getStartTime().equals(boardRoomApplySaveInBO.getStartTime()) 
            		 || !boardroomApplyNow.getEndTime().equals(boardRoomApplySaveInBO.getEndTime())  ||  !boardroomApplyNow.getMotif().equals(boardRoomApplySaveInBO.getMotif()) )  ){
            	content =boardroomApplyNow.getMotif() + ":会议主题或时间已经发生变更"  + "\n" + "\n" + "变更后时间:" + DateUtil.format(boardRoomApplySaveInBO.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardRoomApplySaveInBO.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardRoomApplySaveInBO.getEndTime(), "HH:mm") + "\n" + "地点：" + boardRoom.getLocation() + "-" + boardRoom.getFloor() + "-" + boardRoom.getName() + "\n" + "主题：" + boardRoomApplySaveInBO.getMotif();
            
                NoticeReq notice =
                        NoticeReq.builder()
                                //.content("时间：" + DateUtil.format(boardroomApply.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardroomApply.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardroomApply.getEndTime(), "HH:mm") + "</br>" + "地点：" + boardRoom.getLocation() + "-" + boardRoom.getFloor() + "-" + boardRoom.getName() + "</br>" + "主题：" + boardroomApply.getMotif())
                        		.content(content)
                        		.noticeType("3")
                                .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
                                .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                                .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                                .subject(employeeResp.getOrgName() + "-" + employeeResp.getEmployeeName() + "邀请您参加会议")
                                .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
                                .wxSendType("1")
                                .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
                                .build();
//                informationFeignService.sendNotice(notice);
//                notice.setNoticeType("4");
//                informationFeignService.sendNotice(notice);

                NoticeService.sendAsynNotice(notice);
            }
        }
    }

    @Override
    /**
     * 检查是否可以预定
     *
     * @param boardroomApplyCheckReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyCheckReq]
     * <AUTHOR>
     * @date 2021/10/16 15:47
     */
    public void check(BoardroomApplyCheckReq boardroomApplyCheckReq) {
        Date nowTime = new Date();

        if (boardroomApplyCheckReq.getStartTime().getTime() < nowTime.getTime()) {
            throw new BusinessException("预定时间不能小于当前时间");
        }

        BoardRoomHaveBookingInBO boardRoomHaveBookingInBO = new BoardRoomHaveBookingInBO();
        boardRoomHaveBookingInBO.setApplyStartTime(boardroomApplyCheckReq.getStartTime());
        boardRoomHaveBookingInBO.setApplyEndTime(boardroomApplyCheckReq.getEndTime());
        boardRoomHaveBookingInBO.setBoardroomId(boardroomApplyCheckReq.getBoardroomId());
        Integer count = getHaveBooking(boardRoomHaveBookingInBO);
//        if(1==1)
//        {
//            throw new BusinessException("会议室已经被预定");
//        }
        if (count > 0) {
            throw new BusinessException("会议室已经被预定");
        }


        BoardRoomReq boardRoomReq = new BoardRoomReq();
        boardRoomReq.setId(boardroomApplyCheckReq.getBoardroomId());
        BoardRoomListResp boardRoomListResp = boardRoomService.get(boardRoomReq);

        Date bookingTimeBegin = DateUtil.parse(DateUtil.format(boardroomApplyCheckReq.getStartTime(), "yyyy-MM-dd") + " " + boardRoomListResp.getBookingTimeBegin() + ":00", "yyyy-MM-dd HH:mm:ss");
        Date bookingEndBegin = DateUtil.parse(DateUtil.format(boardroomApplyCheckReq.getEndTime(), "yyyy-MM-dd") + " " + boardRoomListResp.getBookingTimeEnd() + ":00", "yyyy-MM-dd HH:mm:ss");

//        System.out.println(JSON.toJSONString(boardRoomListResp)+"-----------");
//
//        System.out.println("---"+DateUtil.format(bookingTimeBegin,"yyyy-MM-dd HH:mm:ss")+"<"+DateUtil.format(boardroomApplyCheckReq.getStartTime(),"yyyy-MM-dd HH:mm:ss"));
//        System.out.println("---"+DateUtil.format(bookingEndBegin,"yyyy-MM-dd HH:mm:ss")+">"+DateUtil.format(boardroomApplyCheckReq.getStartTime(),"yyyy-MM-dd HH:mm:ss"));

        if (!(bookingTimeBegin.getTime() < boardroomApplyCheckReq.getStartTime().getTime() &&
                bookingEndBegin.getTime() > boardroomApplyCheckReq.getStartTime().getTime())) {
            throw new BusinessException("会议室不在预定范围内！");
        }
//        System.out.println("---"+DateUtil.format(bookingTimeBegin,"yyyy-MM-dd HH:mm:ss")+"<"+DateUtil.format(boardroomApplyCheckReq.getEndTime(),"yyyy-MM-dd HH:mm:ss"));
//        System.out.println("---"+DateUtil.format(bookingEndBegin,"yyyy-MM-dd HH:mm:ss")+">"+DateUtil.format(boardroomApplyCheckReq.getEndTime(),"yyyy-MM-dd HH:mm:ss"));

        if (!(bookingTimeBegin.getTime() < boardroomApplyCheckReq.getEndTime().getTime() &&
                bookingEndBegin.getTime() > boardroomApplyCheckReq.getEndTime().getTime())) {
            throw new BusinessException("会议室不在预定范围内！");
        }

        if(boardRoomListResp.getDisableType()!=null) {
            if (boardRoomListResp.getDisableType().equals(2)) {
                throw new BusinessException("会议室已经被禁用");
            }
        }
//
//        if(boardRoomListResp.getDisableBegintime()!=null&&boardRoomListResp.getDisableEndtime()!=null) {
//            if (
//                    (boardRoomListResp.getDisableBegintime().getTime() > boardroomApplyCheckReq.getStartTime().getTime()
//                            &&
//                            boardRoomListResp.getDisableEndtime().getTime() < boardroomApplyCheckReq.getStartTime().getTime()
//                    )
//
//            ) {
//                throw new BusinessException("会议室已经被禁用");
//            }
//            if (
//                    (boardRoomListResp.getDisableBegintime().getTime() > boardroomApplyCheckReq.getEndTime().getTime()
//                            &&
//                            boardRoomListResp.getDisableEndtime().getTime() < boardroomApplyCheckReq.getEndTime().getTime()
//                    )
//
//            ) {
//                throw new BusinessException("会议室已经被禁用");
//            }
//        }
    }


    @Override
    /**
     * 获取工作流列表
     * @param boardroomApplyWorkflowListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyWorkflowListReq]
     * <AUTHOR>
     * @date 2021/10/18 19:07
     */
    public DataSet<BoardroomApplyWorkflowListRes> getListWorkflowList(Page page, BoardroomApplyWorkflowListReq boardroomApplyWorkflowListReq) {
        //1由我发起2待我办理3我已办理
        BoardroomApplyWorkflowListParm boardroomApplyWorkflowListParm = new BoardroomApplyWorkflowListParm();
        BeanUtil.copyProperties(boardroomApplyWorkflowListReq, boardroomApplyWorkflowListParm);

        if (boardroomApplyWorkflowListReq.getWorkflowType().equals(1)) {
            boardroomApplyWorkflowListParm.setApplyEmp(UserInfoHolder.getCurrentUserCode());
            if (boardroomApplyWorkflowListReq.getApplyStatus() != null) {
                List<String> applyStatusList = new ArrayList<>();
                applyStatusList.add(boardroomApplyWorkflowListReq.getApplyStatus() + "");
                boardroomApplyWorkflowListParm.setApplyStatusList(applyStatusList);
            }
            boardroomApplyWorkflowListParm.setCreateStartTime(boardroomApplyWorkflowListReq.getStartTime());
            boardroomApplyWorkflowListParm.setCreateEndTime(boardroomApplyWorkflowListReq.getEndTime());
        } else if (boardroomApplyWorkflowListReq.getWorkflowType().equals(2)) {
            boardroomApplyWorkflowListParm.setAssigneeNo(UserInfoHolder.getCurrentUserCode());
            List<String> applyStatusList = new ArrayList<>();
            applyStatusList.add("0");
            boardroomApplyWorkflowListParm.setApplyStatusList(applyStatusList);
            if (page.getSidx().equals("a2.CREATE_DATE")) {
                page.setSidx("a2.DESTINE_DATE");
            }

//            boardroomApplyWorkflowListParm.setStartTime(new Date());
//            boardroomApplyWorkflowListParm.setEndTime(DateUtil.offsetDay(new Date(),365));

//            boardroomApplyWorkflowListParm.setCreateStartTime(new Date());
//            boardroomApplyWorkflowListParm.setCreateEndTime(DatesUtil.offsetDay(new Date(),365));

        } else if (boardroomApplyWorkflowListReq.getWorkflowType().equals(3)) {
            boardroomApplyWorkflowListParm.setAssigneeNo(UserInfoHolder.getCurrentUserCode());

            List<String> applyStatusList = new ArrayList<>();
            if (boardroomApplyWorkflowListReq.getApplyStatus() != null) {
                applyStatusList.add(boardroomApplyWorkflowListReq.getApplyStatus() + "");
                boardroomApplyWorkflowListParm.setApplyStatusList(applyStatusList);
            } else {
                applyStatusList.add("1");
                applyStatusList.add("2");
                applyStatusList.add("3");
            }
            boardroomApplyWorkflowListParm.setStartTime(boardroomApplyWorkflowListReq.getStartTime());
            boardroomApplyWorkflowListParm.setEndTime(boardroomApplyWorkflowListReq.getEndTime());

            boardroomApplyWorkflowListParm.setApplyStatusList(applyStatusList);

            if (page.getSidx().equals("a2.CREATE_DATE")) {
                page.setSidx("a4.FINISHED_DATE");
            }
        }

        boardroomApplyWorkflowListParm.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<BoardroomApplyWorkflowListRes> boardroomApplyWorkflowListResList = boardroomApplyMapper.getListWorkflowList(page, boardroomApplyWorkflowListParm,boardroomProperties.getWfDataBaseName());
		
        if(CollectionUtils.isEmpty(boardroomApplyWorkflowListResList))
		{
		    return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), boardroomApplyWorkflowListResList);
		}

        List<String> employeeNos = new ArrayList<>();
        for (BoardroomApplyWorkflowListRes boardroomApplyWorkflowListRes : boardroomApplyWorkflowListResList) {
            employeeNos.add(boardroomApplyWorkflowListRes.getApplyEmp());
        }


        List<EmployeeResp> employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(employeeNos).getObject();


        for (BoardroomApplyWorkflowListRes boardroomApplyWorkflowListRes : boardroomApplyWorkflowListResList) {

            for (EmployeeResp employeeResp : employeeRespList) {
                if (boardroomApplyWorkflowListRes.getApplyEmp().equals(employeeResp.getEmployeeNo())) {
                    boardroomApplyWorkflowListRes.setApplyPhoneNumber(employeeResp.getPhoneNumber());
                    boardroomApplyWorkflowListRes.setApplyOrgCode(employeeResp.getOrgCode());
                    boardroomApplyWorkflowListRes.setApplyEmpName(employeeResp.getEmployeeName());
                    boardroomApplyWorkflowListRes.setApplyOrgName(employeeResp.getOrgName());

                    break;
                }
            }

            boardroomApplyWorkflowListRes.setStatusLable(WorkflowStatusEnum.getValByKey(boardroomApplyWorkflowListRes.getStatus()));

            List<TaskListRes> taskListResList = boardroomApplyMapper.getTaskList(boardroomApplyWorkflowListRes.getWfInstanceId(),boardroomProperties.getWfDataBaseName());
            boardroomApplyWorkflowListRes.setTaskListResList(taskListResList);

        }

        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), boardroomApplyWorkflowListResList);
    }


    @Override
    /**
     * 撤销基础
     *
     * @param id
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/19 15:34
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelBase(BoardRoomApplyCancelReq boardRoomApplyCancelReq) {
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", boardRoomApplyCancelReq.getApplyId());
       // criteria.andEqualTo("applyEmp", UserInfoHolder.getCurrentUserCode());
        BoardroomApply boardroomApplyUpdate = new BoardroomApply();
        boardroomApplyUpdate.setStatus(BoardroomApplyStatusEnum.CANCEL.getKey());
        boardroomApplyUpdate.setRemark(boardRoomApplyCancelReq.getRemark());
        boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);
    }


    @Override
    /**
     * 过期
     *
     * @param id
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/19 15:34
     */
    @Transactional(rollbackFor = Exception.class)
    public void expire(BoardRoomApplyCancelReq boardRoomApplyCancelReq) {
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", boardRoomApplyCancelReq.getApplyId());
      //  criteria.andEqualTo("applyEmp", UserInfoHolder.getCurrentUserCode());
        BoardroomApply boardroomApplyUpdate = new BoardroomApply();
        boardroomApplyUpdate.setStatus(BoardroomApplyStatusEnum.EXPIRE.getKey());
        boardroomApplyUpdate.setRemark(boardRoomApplyCancelReq.getRemark());
        boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);
    }


    @Override
    /**
     * 撤销
     *
     * @param id
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/19 15:34
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancel(BoardRoomApplyCancelReq boardRoomApplyCancelReq) {
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", boardRoomApplyCancelReq.getApplyId());
     //   criteria.andEqualTo("applyEmp", UserInfoHolder.getCurrentUserCode());
        BoardroomApply boardroomApplyUpdate = new BoardroomApply();
        boardroomApplyUpdate.setStatus(BoardroomApplyStatusEnum.CANCEL.getKey());
        boardroomApplyUpdate.setRemark(boardRoomApplyCancelReq.getRemark());
        boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);
        BoardRoomMeetingTimeReq boardRoomMeetingTimeReq = new BoardRoomMeetingTimeReq();
        boardRoomMeetingTimeReq.setApplyId(boardRoomApplyCancelReq.getApplyId());
        BoardroomMeetingTime boardroomMeetingTime = boardRoomMeetingTimeService.getBase(boardRoomMeetingTimeReq);
        if (boardroomMeetingTime != null) {
            BoardRoomMeetingCancelInBO boardRoomMeetingCancelInBO = new BoardRoomMeetingCancelInBO();
            boardRoomMeetingCancelInBO.setMeetingId(boardroomMeetingTime.getId());
            boardRoomMeetingCancelInBO.setRemark(boardRoomApplyCancelReq.getRemark());
            boardRoomMeetingCancelInBO.setSendNoticeType(2);
            boardRoomMeetingCancelInBO.setCancelType(1);
            boardRoomMeetingTimeService.cancel(boardRoomMeetingCancelInBO);
        }
        
        BoardroomApply boardroomApply = boardroomApplyMapper.selectByPrimaryKey(boardRoomApplyCancelReq.getApplyId());
        
//        Map<String, Object> map = new HashMap<>();
//        map.put("reason", boardRoomApplyCancelReq.getRemark());
//        map.put("workflowInstId", boardroomApply.getEventid());

//        workflowFeignService.doUndoInstance(map);//流程撤销
        workflowInstanceService.doUndoInstance(boardroomApply.getEventid(), boardRoomApplyCancelReq.getRemark());
}

    @Override
    /**
     * 过期
     *
     * @param id
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/19 15:34
     */
    @Transactional(rollbackFor = Exception.class)
    public void expire(String id) {
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        BoardroomApply boardroomApplyUpdate = new BoardroomApply();
        boardroomApplyUpdate.setStatus(BoardroomApplyStatusEnum.EXPIRE.getKey());
        boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);
    }

    @Override
    /**
     * 通过
     *
     * @param boardroomApplyStatusReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyStatusReq]
     * <AUTHOR>
     * @date 2021/10/19 16:22
     */
    @Transactional(rollbackFor = Exception.class)
    public void pass(BoardroomApplyStatusReq boardroomApplyStatusReq) {

        if (StringUtils.isBlank(boardroomApplyStatusReq.getBusinessId())) {
            throw new BusinessException("流程业务ID不能为空！");
        }
        BoardroomApplyInBO boardroomApplyReq = new BoardroomApplyInBO();
        boardroomApplyReq.setBusinessId(boardroomApplyStatusReq.getBusinessId());
        BoardroomApply boardroomApply = getBase(boardroomApplyReq);
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", boardroomApply.getId());
        BoardroomApply boardroomApplyUpdate = new BoardroomApply();
        boardroomApplyUpdate.setStatus(BoardroomApplyStatusEnum.Y.getKey());
        boardroomApplyUpdate.setCheckTime(new Date());
        boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);

//        BoardRoomMeetingTimeSaveReq boardRoomMeetingTimeSaveReq = new BoardRoomMeetingTimeSaveReq();
//        boardRoomMeetingTimeSaveReq.setApplyId(boardroomApply.getId());
//        boardRoomMeetingTimeSaveReq.setStartTime(boardroomApply.getStartTime());
//        boardRoomMeetingTimeSaveReq.setEndTime(boardroomApply.getEndTime());
//        boardRoomMeetingTimeSaveReq.setBoardroomId(boardroomApply.getBoardroomId());
//        boardRoomMeetingTimeSaveReq.setIswxadd("0");
//        boardRoomMeetingTimeSaveReq.setStatus("0");
//        boardRoomMeetingTimeService.add(boardRoomMeetingTimeSaveReq);

//        boardRoomSignInService.updateMeetingId(boardroomApply.getId(), boardRoomMeetingTimeSaveReq.getId());

        TaskListRes taskListRes = boardroomApplyMapper.getTask(boardroomApply.getEventid(), UserInfoHolder.getCurrentUserCode(),boardroomProperties.getWfDataBaseName());

        Map<String, Object> map = new HashMap<>();
        map.put("L_TaskRemark", boardroomApplyStatusReq.getRemark());

        workflowTaskService.completeTask(taskListRes.getTaskId(), map);  //流程审批
        
        BoardRoomReq boardRoomReq = new BoardRoomReq();
        boardRoomReq.setId(boardroomApply.getBoardroomId());
        BoardRoom boardRoom = boardRoomService.getBase(boardRoomReq);//会议室基础信息
        
        boolean pushMessage = false;
        if("1".equals(boardRoom.getApprovePush())){
        	pushMessage = true;
        }
        
        if(pushMessage) {
        	
          List<String> attendEmployeeNoList = new ArrayList<>();
  
          BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
          boardroomSigninListReq.setApplyId(boardroomApply.getId());
          boardroomSigninListReq.setInvitee("1");
          List<BoardroomSignin> boardroomSigninList = boardRoomSignInService.getBaseList(boardroomSigninListReq);
          for (BoardroomSignin boardroomSignin : boardroomSigninList) {
              attendEmployeeNoList.add(boardroomSignin.getSigninUsercode());
          }
  
          if (boardroomApply.getRepeatRate()!=null&&boardroomApply.getRepeatRate() > 0) {
              List<BoardRoomApplySaveReq.Agenda> agendaList = new ArrayList<>();
              BoardRoomAgendaListReq boardRoomAgendaListReq = new BoardRoomAgendaListReq();
              boardRoomAgendaListReq.setApplyId(boardroomApply.getId());
              List<BoardroomAgenda> boardroomAgendaList = boardRoomAgendaService.getBaseList(boardRoomAgendaListReq);
              for (BoardroomAgenda boardroomAgenda : boardroomAgendaList) {
                  BoardRoomApplySaveReq.Agenda agenda = new BoardRoomApplySaveReq.Agenda();
                  agenda.setAgenda(boardroomAgenda.getAgenda());
                  agenda.setContent(boardroomAgenda.getContent());
                  agenda.setNum(boardroomAgenda.getNum());
                  agenda.setFunctionary(boardroomAgenda.getFunctionary());
                  agendaList.add(agenda);
              }
              addAfter(boardroomApply, attendEmployeeNoList, agendaList);
          }
  
          EmployeeResp employeeResp = hrmsEmployeeFeignService.getEmployeeDetailByCode(boardroomApply.getApplyEmp()).getObject();
  
          List<String> receiverList = new ArrayList<>();
          for (BoardroomSignin boardroomSignin : boardroomSigninList) {
              receiverList.add(boardroomSignin.getSigninUsercode());
          }
          
          NoticeReq notice =
                  NoticeReq.builder()
                          .content("时间：" + DateUtil.format(boardroomApply.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardroomApply.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardroomApply.getEndTime(), "HH:mm") + "\n" + "地点：" + boardRoom.getLocation() + "-" + boardRoom.getFloor() + "-" + boardRoom.getName() + "\n" + "主题：" + boardroomApply.getMotif()+ "\n" + "备注：" +  boardroomApply.getRemark())
                          .noticeType("3")
                          .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
                          .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                          .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                          .subject(employeeResp.getOrgName() + "-" + employeeResp.getEmployeeName() + "邀请您参加会议")
                          .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
                          .wxSendType("1")
                          .businessId(boardroomApplyStatusReq.getBusinessId())
                          .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
                          .build();
          
          NoticeService.sendAsynNotice(notice);
        }
    }


    /**
     * 新增以后的预定信息
     *
     * @param boardroomApply
     * @return void
     * <AUTHOR>
     * @date 2021/10/28 9:03
     */
    @Transactional(rollbackFor = Exception.class)
    public void addAfter(BoardroomApply boardroomApply, List<String> attendEmployeeNoList, List<BoardRoomApplySaveReq.Agenda> agendaList) {
        List<BoardroomApply> boardroomApplyList = new ArrayList<>();
        if (boardroomApply.getRepeatRate().equals(1)) {
            long betweenDay = DateUtil.betweenDay(boardroomApply.getStartTime(), boardroomApply.getRepeatEndTime(), true);
            if (betweenDay > 0) {
                for (int i = 0; i < betweenDay; i++) {
                    BoardroomApply afterBoardroomApply = BeanUtil.copyProperties(boardroomApply, BoardroomApply.class);
                    afterBoardroomApply.setId(String.valueOf(IdWork.id.nextId()));
                    afterBoardroomApply.setStartTime(
                            DateUtil.offset(afterBoardroomApply.getStartTime(), DateField.DAY_OF_YEAR, (1 + i))
                    );
                    afterBoardroomApply.setEndTime(
                            DateUtil.offset(afterBoardroomApply.getEndTime(), DateField.DAY_OF_YEAR, (1 + i))
                    );
                    boardroomApplyList.add(afterBoardroomApply);
                }
            }
        } else if (boardroomApply.getRepeatRate().equals(2)) {
            long betweenDay = DateUtil.betweenWeek(boardroomApply.getStartTime(), boardroomApply.getRepeatEndTime(), true);
            if (betweenDay > 0) {
                for (int i = 0; i < betweenDay; i++) {
                    BoardroomApply afterBoardroomApply = BeanUtil.copyProperties(boardroomApply, BoardroomApply.class);
                    afterBoardroomApply.setId(String.valueOf(IdWork.id.nextId()));
                    afterBoardroomApply.setStartTime(
                            DateUtil.offset(afterBoardroomApply.getStartTime(), DateField.WEEK_OF_YEAR, (1 + i))
                    );
                    afterBoardroomApply.setEndTime(
                            DateUtil.offset(afterBoardroomApply.getEndTime(), DateField.WEEK_OF_YEAR, (1 + i))
                    );
                    boardroomApplyList.add(afterBoardroomApply);
                }
            }
        } else if (boardroomApply.getRepeatRate().equals(3)) {
            long betweenDay = DateUtil.betweenMonth(boardroomApply.getStartTime(), boardroomApply.getRepeatEndTime(), true);
            if (betweenDay > 0) {
                for (int i = 0; i < betweenDay; i++) {
                    BoardroomApply afterBoardroomApply = BeanUtil.copyProperties(boardroomApply, BoardroomApply.class);
                    afterBoardroomApply.setId(String.valueOf(IdWork.id.nextId()));
                    afterBoardroomApply.setStartTime(
                            DateUtil.offset(afterBoardroomApply.getStartTime(), DateField.MONTH, (1 + i))
                    );
                    afterBoardroomApply.setEndTime(
                            DateUtil.offset(afterBoardroomApply.getEndTime(), DateField.MONTH, (1 + i))
                    );
                    boardroomApplyList.add(afterBoardroomApply);
                }
            }
        }
        for (BoardroomApply apply : boardroomApplyList) {
            try {
                apply.setEventid("");
                apply.setStatus("1");
                BoardroomApplyCheckReq boardroomApplyCheckReq = new BoardroomApplyCheckReq();
                boardroomApplyCheckReq.setBoardroomId(apply.getBoardroomId());
                boardroomApplyCheckReq.setEndTime(apply.getStartTime());
                boardroomApplyCheckReq.setStartTime(apply.getEndTime());
                check(boardroomApplyCheckReq);
                apply.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                boardroomApplyMapper.insertSelective(apply);


                BoardRoomMeetingTimeSaveReq boardRoomMeetingTimeSaveReq = new BoardRoomMeetingTimeSaveReq();
                boardRoomMeetingTimeSaveReq.setApplyId(apply.getId());
                boardRoomMeetingTimeSaveReq.setStartTime(apply.getStartTime());
                boardRoomMeetingTimeSaveReq.setEndTime(apply.getEndTime());
                boardRoomMeetingTimeSaveReq.setBoardroomId(apply.getBoardroomId());
                boardRoomMeetingTimeSaveReq.setIswxadd("0");
                boardRoomMeetingTimeSaveReq.setStatus("0");
                boardRoomMeetingTimeService.add(boardRoomMeetingTimeSaveReq);


                List<BoardRoomAgendaSaveReq> boardRoomAgendaSaveReqList = new ArrayList<>();
                if (CollectionUtils.isEmpty(agendaList) == false) {
                    for (BoardRoomApplySaveReq.Agenda agenda : agendaList) {
                        BoardRoomAgendaSaveReq boardRoomAgendaSaveReq = BeanUtil.copyProperties(agenda, BoardRoomAgendaSaveReq.class);
                        boardRoomAgendaSaveReq.setApplyId(apply.getId());
                        boardRoomAgendaSaveReqList.add(boardRoomAgendaSaveReq);
                    }
                }
                boardRoomAgendaService.save(boardRoomAgendaSaveReqList);
                List<BoardRoomSignInSaveReq> boardRoomSignInSaveReqList = new ArrayList<>();
                if (CollectionUtils.isEmpty(attendEmployeeNoList) == false) {
                    for (String no : attendEmployeeNoList) {
                        BoardRoomSignInSaveReq boardRoomSignInSaveReq = new BoardRoomSignInSaveReq();
                        boardRoomSignInSaveReq.setApplyId(boardRoomMeetingTimeSaveReq.getApplyId());
                        boardRoomSignInSaveReq.setInvitee("1");
                        boardRoomSignInSaveReq.setSigninUsercode(no);
                        boardRoomSignInSaveReq.setMeetingTimeId(boardRoomMeetingTimeSaveReq.getId());
                        boardRoomSignInSaveReqList.add(boardRoomSignInSaveReq);
                    }
                }
                boardRoomSignInService.save(boardRoomSignInSaveReqList);


                if (!appConfigProperties.getOAOpenGatewayRibbon().getEnabled()) {

                    try {
                        List<Schedule> scheduleList = new ArrayList<>();
                        for (BoardRoomSignInSaveReq boardRoomSignInSaveReq : boardRoomSignInSaveReqList) {
                            Schedule schedule = new Schedule();
                            schedule.setId(String.valueOf(IdWork.id.nextId()));
                            schedule.setCreateDate(new Date());
                            schedule.setCreateUser(boardRoomSignInSaveReq.getSigninUsercode());
                            schedule.setIsRemind(1);
                            schedule.setScheduleType("会议日程");
                            schedule.setScheduleDate(apply.getStartTime());
                            schedule.setScheduleStartTime(DateUtil.format(apply.getStartTime(), "HH:mm"));
                            schedule.setScheduleEndTime(DateUtil.format(apply.getEndTime(), "HH:mm"));
                            schedule.setScheduleSubject(apply.getMotif());
                            schedule.setRemindTime("15");
                            schedule.setScheduleContent("");
                            schedule.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
                            scheduleList.add(schedule);
                        }
                        scheduleService.batchInsert(scheduleList);
                    } catch (Exception e) {
                        log.error("推送会议日程失败,失败原因：", e.getMessage(), e);
                    }
                }

            } catch (Exception ex) {
                log.error(JSON.toJSONString(apply), ex);
            }
        }
    }


    @Override
    /**
     * 不通过
     * @param boardroomApplyStatusReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomApplyStatusReq]
     * <AUTHOR>
     * @date 2021/10/19 16:29
     */
    @Transactional(rollbackFor = Exception.class)
    public void fail(BoardroomApplyStatusReq boardroomApplyStatusReq) {

        if (StringUtils.isBlank(boardroomApplyStatusReq.getBusinessId())) {
            throw new BusinessException("流程业务ID不能为空！");
        }

        BoardroomApplyInBO boardroomApplyReq = new BoardroomApplyInBO();
        boardroomApplyReq.setBusinessId(boardroomApplyStatusReq.getBusinessId());
        BoardroomApply boardroomApply = getBase(boardroomApplyReq);
        Example example = new Example(BoardroomApply.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", boardroomApply.getId());
        BoardroomApply boardroomApplyUpdate = new BoardroomApply();
        boardroomApplyUpdate.setStatus(BoardroomApplyStatusEnum.N.getKey());
        boardroomApplyUpdate.setCheckTime(new Date());
        boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);
        boardRoomMeetingTimeService.delByApplyId(boardroomApply.getId());

        TaskListRes taskListRes = boardroomApplyMapper.getTask(boardroomApply.getEventid(), UserInfoHolder.getCurrentUserCode(),boardroomProperties.getWfDataBaseName());


        Map<String, Object> map = new HashMap<>();
        map.put("L_TaskRemark", boardroomApplyStatusReq.getRemark());
       // WfTask wfTask = workflowTaskService.selectTaskById(taskListRes.getTaskId());

        workflowTaskService.doRejectTask(taskListRes.getTaskId(), map);  //流程审批


        //workflowFeignService.doTerminateProcessInstance(wfTask.getWfInstanceId(), "结束");  //流程审批


        NoticeReq notice =
                NoticeReq.builder()
                        .content("你发起的【"+boardroomApply.getMotif()+"】流程被"+(UserInfoHolder.getCurrentUserName())+"退回，原因："+boardroomApplyStatusReq.getRemark()+"请及时处理")
                        .noticeType("3")
                        .receiver(boardroomApply.getApplyEmp())  //接收人
                        .sender(boardroomApply.getApplyEmp()) //发送人
                        .senderName(boardroomApply.getApplyEmpname()) //发送人name
                        .subject("你发起的【"+boardroomApply.getMotif()+"】流程被"+UserInfoHolder.getCurrentUserName()+"退回")
                        .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
                        .wxSendType("1")
                        .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
                        .build();
        // informationFeignService.sendNotice(notice);
        NoticeService.sendAsynNotice(notice);

    }


    @Override
    /**
     * 即将开始
     *
     * @return void
     * <AUTHOR>
     * @date 2021/10/30 14:21
     */
    public void timeSoonMeeting() {


        List<BoardroomApply> boardroomApplyList = boardroomApplyMapper.getTimeSoonMeeting(new Date());


        if (CollectionUtils.isEmpty(boardroomApplyList) == false) {
            UserLoginService.loginContext("admin");

        }
        for (BoardroomApply boardroomApply : boardroomApplyList) {

            Date nowTime = new Date();
            
            //改为没有提醒过的 15分的时候提醒 
            if(null == boardroomApply.getSendInfoLastTime()) {  //没有提醒过的
            	//当前时间 + 15分钟大于会议开始时间的 
            	if(DateUtil.offsetMinute(nowTime, 15).getTime()   >= boardroomApply.getStartTime().getTime()) { //15分钟以内开始的
            		
            		 Example example = new Example(BoardroomApply.class);
                     Example.Criteria criteria = example.createCriteria();
                     criteria.andEqualTo("id", boardroomApply.getId());
                     BoardroomApply boardroomApplyUpdate = new BoardroomApply();
                     boardroomApplyUpdate.setSendInfoLastTime(nowTime);
                     boardroomApplyMapper.updateByExampleSelective(boardroomApplyUpdate, example);


                     BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
                     boardroomSigninListReq.setApplyId(boardroomApply.getId());
                     boardroomSigninListReq.setInvitee("1");
                     boardroomSigninListReq.setSigninStatus("0");  //只有未签到的人才发送消息
                     List<BoardroomSignin> boardroomSigninList = boardRoomSignInService.getBaseList(boardroomSigninListReq);


                     BoardRoomReq boardRoomReq = new BoardRoomReq();
                     boardRoomReq.setId(boardroomApply.getBoardroomId());
                     BoardRoom boardRoom = boardRoomService.getBase(boardRoomReq);

                     Set<String> receiverList = new HashSet<>();
                     for (BoardroomSignin boardRoomSigninListRes : boardroomSigninList) {
                         receiverList.add(boardRoomSigninListRes.getSigninUsercode());
                     }
                     receiverList.add(boardroomApply.getApplyEmp());
                     NoticeReq notice =
                             NoticeReq.builder()
                                     .content("时间：" + DateUtil.format(boardroomApply.getStartTime(), "yyyy-MM-dd") + " " + DateUtil.format(boardroomApply.getStartTime(), "HH:mm") + " - " + DateUtil.format(boardroomApply.getEndTime(), "HH:mm") + "\n" + "地点：" + boardRoom.getLocation() + "-" + boardRoom.getFloor() + "-" + boardRoom.getName() + "\n" + "主题：" + boardroomApply.getMotif()+ "\n" + "备注：" +  boardroomApply.getRemark())
                                     .noticeType("3")
                                     .receiver(org.apache.commons.lang.StringUtils.join(receiverList.toArray(), ","))  //接收人
                                     .sender(UserInfoHolder.getCurrentUserCode()) //发送人
                                     .senderName(UserInfoHolder.getCurrentUserName()) //发送人name
                                     .subject("会议" + DateUtil.between(nowTime, boardroomApply.getStartTime(), DateUnit.MINUTE) + "分钟后开始")
                                     .url(appConfigProperties.getWxDomain() + "mobile-container/ts-mobile-meeting/pages/meeting-manage/my-meeting/index?fromPage=workBench&index=0")
                                     .wxSendType("1")
                                     .toUrl("/ts-web-meeting/myAgenda").source("资源管理")
                                     .build();
                     NoticeService.sendAsynNotice(notice);
            	}
            }
        }
    }


    @Override
    /**
     * 批量处理已过期
     * <AUTHOR>
     * @date 2021/11/13 14:36
     */
    public void batchExpire() {
        BoardroomApplyWorkflowListParm boardroomApplyWorkflowListParm = new BoardroomApplyWorkflowListParm();
        boardroomApplyWorkflowListParm.setWorkflowType(2);
        List<String> list = new ArrayList<>();
        list.add("0");
        boardroomApplyWorkflowListParm.setLtEndTime(new Date());
        boardroomApplyWorkflowListParm.setApplyStatusList(list);
        boardroomApplyWorkflowListParm.setAssigneeNo(UserInfoHolder.getCurrentUserCode());
        boardroomApplyWorkflowListParm.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<BoardroomApplyWorkflowListRes> boardroomApplyWorkflowListResList = boardroomApplyMapper.getListWorkflowList(boardroomApplyWorkflowListParm,boardroomProperties.getWfDataBaseName());
        for (BoardroomApplyWorkflowListRes boardroomApplyWorkflowListRes : boardroomApplyWorkflowListResList) {
            this.expire(boardroomApplyWorkflowListRes.getApplyId());
            Map<String, Object> map = new HashMap<>();
            map.put("L_TaskRemark", "已过期");
            workflowTaskService.completeTask(boardroomApplyWorkflowListRes.getTaskId(), map);  //流程审批
            WfTask wfTask = workflowTaskService.selectTaskById(boardroomApplyWorkflowListRes.getTaskId());
//            workflowFeignService.doTerminateProcessInstance(wfTask.getWfInstanceId(), "结束");  //流程审批
            workflowInstanceService.doTerminateProcessInstance(wfTask.getWfInstanceId(), "结束");
        }
    }


	@Override
	public BoardroomApply selectById(String applyId) {
		return boardroomApplyMapper.selectByPrimaryKey(applyId);
	}


	//会议周视图
	@Override
	public List<Map<String, Object>> getBoardRoomApplyWeekTimeDetailList(BoardRoomApplyTimeDetailListReq boardRoomApplyTimeDetailListReq) {
		
		Boolean right = UserInfoHolder.getRight("BOARDROOM_MANAGE");
		if(!right){
			boardRoomApplyTimeDetailListReq.setCurrentUserCode(UserInfoHolder.getCurrentUserCode());
		}
		
		if(null != boardRoomApplyTimeDetailListReq && null != boardRoomApplyTimeDetailListReq.getSubscribeStatus() && boardRoomApplyTimeDetailListReq.getSubscribeStatus() == 2){
			boardRoomApplyTimeDetailListReq.setUserCode(UserInfoHolder.getCurrentUserCode());
		}
		
		boardRoomApplyTimeDetailListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		
		//查询会议室列表 
		List<BoardRoom> boardRoomList = boardroomApplyMapper.selectBoardRoomList(boardRoomApplyTimeDetailListReq);
		
		List<BoardroomDeviceListResp> boardroomDeviceListRespList = boardRoomDeviceService.getList();
		
		for (BoardRoom boardRoom : boardRoomList) {
			 if (StringUtils.isBlank(boardRoom.getDeviceIds()) == false) {
                List<BoardRoomListResp.Device> deviceList = new ArrayList<>();
                for (String s : boardRoom.getDeviceIds().split(",")) {
                    for (BoardroomDeviceListResp boardRoomDevice : boardroomDeviceListRespList) {
                        if (s.equals(boardRoomDevice.getId())) {
                            BoardRoomListResp.Device device = new BoardRoomListResp.Device();
                            device.setId(boardRoomDevice.getId());
                            device.setName(boardRoomDevice.getName());
                            device.setIcon(boardRoomDevice.getIcon());
                            deviceList.add(device);
                            break;
                        }
                    }
                }
                boardRoom.setDeviceList(deviceList);
            }
		}
		
		Date date = null;
		if(null != boardRoomApplyTimeDetailListReq){
			date = boardRoomApplyTimeDetailListReq.getTime();
		}
		
		if(null == date){
			date = DateUtil.date();
		}
		
        // 获取本周的开始日期（默认为周一）
        Date beginOfWeek = DateUtil.beginOfWeek(date);
 
        // 获取本周的结束日期（默认为周日）
        Date endOfWeek = DateUtil.endOfWeek(date);
 
        // 获取本周所有天的日期列表
        DateRange daysOfWeek = DateUtil.range(beginOfWeek, endOfWeek, DateField.DAY_OF_WEEK);
        List<String> daysOfWeekList = new ArrayList<>(); 
        for (Date dateTime : daysOfWeek) {
        	String formatDate = DateUtil.formatDate(dateTime);
    		daysOfWeekList.add(formatDate);
		}
       
        List<Map<String, Object>> resultList = new ArrayList<>();
        
        BoardroomApplyListReq boardroomApplyListReq = new BoardroomApplyListReq();
        for (BoardRoom boardRoom : boardRoomList) {
        	Map<String, Object> result = new HashMap<>();
        	result.put("boardRoom", boardRoom);
        	
        	boardroomApplyListReq = new BoardroomApplyListReq();
        	boardroomApplyListReq.setBoardroomId(boardRoom.getId());
        	boardroomApplyListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        	List<String> statusList = new ArrayList<>();
            statusList.add("0");
            statusList.add("1");
            statusList.add("3");
            boardroomApplyListReq.setStatusList(statusList);
            
            //过滤时间段内的
            boardroomApplyListReq.setStartTime(beginOfWeek);//开始时间
            boardroomApplyListReq.setEndTime(endOfWeek);//结束时间
        	List<BoardroomApplyListResp> boardroomApplyList = boardroomApplyMapper.getList(boardroomApplyListReq);

        	Date nowTime = new Date();
        	//判断当前会议状态
        	for (BoardroomApplyListResp boardroomApplyListResp : boardroomApplyList) {
        		 if (boardroomApplyListResp.getMeetingStatus() != null) {
                     //0未开始，-1已结束,1进行中，4取消
                     if (boardroomApplyListResp.getMeetingStatus().equals(4)) {
                    	 //boardroomApplyListResp.setMeetingStatusLable("取消");
                    	 boardroomApplyListResp.setMeetingStatus(4);
                     } else if (boardroomApplyListResp.getStartTime().getTime() > (nowTime.getTime())) {
                    	 //boardroomApplyListResp.setMeetingStatusLable("未开始");
                    	 boardroomApplyListResp.setMeetingStatus(0);
                     } else if (
                    		 boardroomApplyListResp.getStartTime().getTime() < (nowTime.getTime())
                                     && boardroomApplyListResp.getMeetingEndTime().getTime() > (nowTime.getTime())
                     ) {
                    	 //boardroomApplyListResp.setMeetingStatusLable("进行中");
                    	 boardroomApplyListResp.setMeetingStatus(1);
                     } else if (boardroomApplyListResp.getMeetingEndTime().getTime() < (nowTime.getTime())) {
                    	 //boardroomApplyListResp.setMeetingStatusLable("已结束");
                    	 boardroomApplyListResp.setMeetingStatus(-1);
                     }
                 }
        		 
        		 BoardroomSigninListReq boardroomSigninListReq = new BoardroomSigninListReq();
   			  	 boardroomSigninListReq.setInvitee("1");
                 boardroomSigninListReq.setApplyId(boardroomApplyListResp.getId());
           
                 Set<String> employeeNos = new HashSet<>();
                 List<BoardroomSignin> boardroomSigninList = boardRoomSignInService.getBaseList(boardroomSigninListReq);
                 for (BoardroomSignin boardroomSignin : boardroomSigninList) {
                     employeeNos.add(boardroomSignin.getSigninUsercode());
                 }
                 List<EmployeeResp> employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList<>(employeeNos)).getObject();
                 
                 List<BoardroomApplyListResp.AttendEmployee> attendEmployeeList = new ArrayList<>();
                 for (BoardroomSignin boardroomSignin : boardroomSigninList) {
                         for (EmployeeResp employeeResp : employeeRespList) {
                             if (employeeResp.getEmployeeNo().equals(boardroomSignin.getSigninUsercode())) {
                            	 BoardroomApplyListResp.AttendEmployee attendEmployee = new BoardroomApplyListResp.AttendEmployee();
                                 attendEmployee.setUsercode(employeeResp.getEmployeeNo());
                                 attendEmployee.setUsername(employeeResp.getEmployeeName());
                                 attendEmployeeList.add(attendEmployee);
                                 break;
                             }
                         }
                 }
                 boardroomApplyListResp.setAttendEmployeeList(attendEmployeeList);//添加参会人员
        	}
        	  
//        	for (BoardroomApplyListResp boardroomApplyListResp : boardroomApplyList) {
//        		PlatformResult<EmployeeResp> employeeDetail = hrmsEmployeeFeignService.getEmployeeDetailByCode(boardroomApplyListResp.getApplyEmp());
//        		EmployeeResp employeeResp = employeeDetail.getObject();
//        		ApplyEmployee applyEmployee = new ApplyEmployee();
//        		applyEmployee.setEmployeeId(employeeResp.getId());
//                applyEmployee.setEmployeeName(employeeResp.getEmployeeName());
//                applyEmployee.setEmployeeNo(employeeResp.getEmployeeNo());
//                applyEmployee.setPhoneNumber(employeeResp.getPhoneNumber());
//                applyEmployee.setOrgId(employeeResp.getOrgId());
//                applyEmployee.setOrgName(employeeResp.getOrgName());
//        		boardroomApplyListResp.setApplyEmployee(applyEmployee);
//			}
        	
        	result.put("boardroomApplyList", boardroomApplyList);
        	
        	List<Map<String,Object>> amList = new ArrayList<>();
        	List<Map<String,Object>> pmList = new ArrayList<>();
        	
        	//计算会议室上午、下午可预约总时长是多少
        	double  boardRoomMorningHours = 0;//会议室可预订上午总时长
        	double  boardRoomAfternoonHours = 0;//会议室可预订下午总时长
        	//格式化时间
        	LocalTime LocalBookingTimeBegin = LocalTime.parse(boardRoom.getBookingTimeBegin());
        	LocalTime LocalBookingTimeEnd = LocalTime.parse(boardRoom.getBookingTimeEnd());
        	// 计算两个时间点之间的小时数
            //long hoursBetween = ChronoUnit.HOURS.between(LocalBookingTimeBegin, LocalBookingTimeEnd);
            // 确定中午12点的时间
            LocalTime noon = LocalTime.NOON;
            // 如果开始时间小于12点，结束时间大于12点，则跨越了中午
            if (LocalBookingTimeBegin.isBefore(noon) && LocalBookingTimeEnd.isAfter(noon)) {
                // 计算上午的小时数
            	boardRoomMorningHours = 12 - LocalBookingTimeBegin.toSecondOfDay()/3600.0;
                // 计算下午的小时数
            	boardRoomAfternoonHours =  LocalBookingTimeEnd.toSecondOfDay()/3600.0 - 12;
            } else if (LocalBookingTimeBegin.isBefore(noon) && (LocalBookingTimeEnd.isBefore(noon) || LocalBookingTimeEnd.equals(noon))  ) {
                // 都在上午
            	boardRoomMorningHours =LocalBookingTimeEnd.toSecondOfDay()/3600.0 - LocalBookingTimeBegin.toSecondOfDay()/3600.0;
            	boardRoomAfternoonHours = 0;
            } else if ((LocalBookingTimeBegin.isAfter(noon)  || LocalBookingTimeBegin.equals(noon)) && LocalBookingTimeEnd.isAfter(noon)) {
                // 都在下午
            	boardRoomAfternoonHours = LocalBookingTimeEnd.toSecondOfDay()/3600.0 - LocalBookingTimeBegin.toSecondOfDay()/3600.0;
                boardRoomMorningHours = 0;
            }
        	
            
        	for (String day : daysOfWeekList) {
        		double morningHours = 0;//会议室预订上午总时长
        		double afternoonHours = 0;//会议室预订下午总时长
        				
        		List<BoardroomMeetingTime> boardroomMeetingTimeList = boardroomApplyMapper.selectBoardRoomApplyTimeByDay(boardRoom.getId(),day);
        		
        		boolean isam = false;
        		boolean ispm = false;
        		
        		//判断会议时间是否包含上午下午
        		for (BoardroomMeetingTime boardroomMeetingTime : boardroomMeetingTimeList) {
        			Date startTime = boardroomMeetingTime.getStartTime();
        			Date endTime = boardroomMeetingTime.getEndTime();
        			boolean startAm = DateUtil.isAM(startTime);
        			boolean endAm = DateUtil.isAM(endTime);
        			boolean startPm = DateUtil.isPM(startTime);
        			boolean endPm = DateUtil.isPM(endTime);
        			
        			//计算已经预定的会议室上午、下午总时长
        			//格式化日期
        			LocalDateTime localDateStartTime = startTime.toInstant()
        	                .atZone(ZoneId.systemDefault())
        	                .toLocalDateTime();
        			LocalTime  localStartTime =  localDateStartTime.toLocalTime();//格式化成时间点
        			
        			LocalDateTime localDateEndTime = endTime.toInstant()
        	                .atZone(ZoneId.systemDefault())
        	                .toLocalDateTime();
        			LocalTime  localEndTime =  localDateEndTime.toLocalTime();//格式化成时间点
        			
        			 // 确定中午12点的时间
        			 LocalTime noon2 = LocalTime.NOON;
        	            // 如果开始时间小于12点，结束时间大于12点，则跨越了中午
        	            if (localStartTime.isBefore(noon2) && localEndTime.isAfter(noon2)) {
        	                // 计算上午的小时数
        	            	morningHours =morningHours + 12 - localStartTime.toSecondOfDay()/3600.0;
        	                // 计算下午的小时数
        	            	afternoonHours =afternoonHours +  localEndTime.toSecondOfDay()/3600.0 - 12;
        	            } else if (localStartTime.isBefore(noon2) && (localEndTime.isBefore(noon2) || localEndTime.equals(noon2))) {
        	                // 都在上午
        	            	morningHours =morningHours + localEndTime.toSecondOfDay()/3600.0 - localStartTime.toSecondOfDay()/3600.0;
        	            	afternoonHours =afternoonHours + 0;
        	            } else if ((localStartTime.isAfter(noon2) || localStartTime.equals(noon2)) && localEndTime.isAfter(noon2)) {
        	                // 都在下午
        	            	morningHours =morningHours + 0;
        	            	afternoonHours =afternoonHours + localEndTime.toSecondOfDay()/3600.0 - localStartTime.toSecondOfDay()/3600.0;
        	            }
        			//long totalHours = ChronoUnit.HOURS.between(localStartTime, localEndTime);//计算总小时数
        			
        			if(startAm || endAm){  //上午
        				isam = true;
        			}
        			
        			if(startPm || endPm){  //下午
        				ispm = true;
        			}
				}
        		Map<String,Object> resultMapAm =  new HashMap<String, Object>();
        		resultMapAm.put("day",day);
        		resultMapAm.put("remainingHours",String.format("%.2f",boardRoomMorningHours - morningHours));
        		resultMapAm.put("progressBar",String.format("%.4f",morningHours/boardRoomMorningHours));
        		amList.add(resultMapAm);
        		
        		Map<String,Object> resultMapPm =  new HashMap<String, Object>();
        		resultMapPm.put("day",day);
        		resultMapPm.put("remainingHours",String.format("%.2f",boardRoomAfternoonHours - afternoonHours));
        		resultMapPm.put("progressBar",String.format("%.4f",afternoonHours/boardRoomAfternoonHours));
        		pmList.add(resultMapPm);
        		/*if(isam){
        			amList.add(day);
        		}
        		
        		if(ispm){
        			pmList.add(day);
        		} */
        	}
        	
        	result.put("daysOfWeek", daysOfWeekList);
        	result.put("amList", amList);
        	result.put("pmList", pmList);
        	
        	resultList.add(result);
		}
		
		return resultList;
	}
}
