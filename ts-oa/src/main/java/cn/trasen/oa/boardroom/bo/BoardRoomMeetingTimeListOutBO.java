package cn.trasen.oa.boardroom.bo;

import cn.trasen.oa.boardroom.bean.BoardRoomMeetingTimeListRes;
import cn.trasen.oa.boardroom.bean.BoardroomApplyListResp;
import cn.trasen.oa.boardroom.bean.BoardroomSigninCountRes;
import cn.trasen.oa.boardroom.commons.bean.Employee;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/13 12:43
 */
@Getter
@Setter
public class BoardRoomMeetingTimeListOutBO {


    @ApiModelProperty(value = "会议ID")
    String meetingId;


    @ApiModelProperty(value = "预定id")
    String applyId;

    @ApiModelProperty(value = "会议室ID")
    String boardroomId;
    @ApiModelProperty(value = "会议室名称")
    String boardroomName;

    @ApiModelProperty(value = "预定时间开始时间")
    Date startTime;

    @ApiModelProperty(value = "预定时间结束")
    Date endTime;


    @ApiModelProperty(value = "会议时间结束")
    Date meetingEndTime;

    @ApiModelProperty(value = "会议主题")
    String motif;

    /**
     * 会议主题1显示2隐藏
     */
    @ApiModelProperty(value = "会议主题1显示2隐藏")
    private Integer motifType;

    @ApiModelProperty(value = "预定人code")
    private String applyEmp;


    @ApiModelProperty(value = "人数")
    String controlNumber;

    @ApiModelProperty(value = "预定人信息")
    Employee applyEmployee;

    @ApiModelProperty(value = "0未开始，-1已结束,1进行中，4取消")
    Integer meetingStatus;


    @ApiModelProperty(value = "0未开始，-1已结束,1进行中，4取消")
    String meetingStatusLable;


    @ApiModelProperty(value = " 附件id")
    private String accessoryId;


    @ApiModelProperty(value = "会议纪要最后修改时间")
    private Date summaryUpdateTime;

    @ApiModelProperty(value = " 扩展名")
    private String summaryExtension;
    @ApiModelProperty(value = " 纪要名称")
    private String summaryName;

    @ApiModelProperty(value = " 纪要大小")
    private String summarySize;

    @ApiModelProperty(value = " 纪要URL")
    private String summaryUrl;




    /**
     * 签到二维码
     */
    @ApiModelProperty(value = "签到二维码")
    private String signInQrCode;

    /**
     * 签退二维码
     */
    @ApiModelProperty(value = "签退二维码")
    private String signOutQrCode;


    @ApiModelProperty(value = " 主要议程")
    private List<BoardroomAgendaOutBO> AgendaList;

    @ApiModelProperty(value = " 统计")
    private BoardRoomSigninCountOutBO boardroomSigninCount;


    /**
     * 楼层
     */
    @ApiModelProperty(value = "楼层")
    private String floor;


    /**
     * 会议室位置
     */
    @ApiModelProperty(value = "会议室位置")
    private String location;


    /**
     * 0 普通会议室  1视频会议室 2临时会议室 3 教室 4场地
     */
    @ApiModelProperty(value = "资源类型")
    private String isVideo;


    /**
     * 0 普通会议室  1视频会议室 2临时会议室 3 教室 4场地
     */
    @ApiModelProperty(value = "资源类型")
    private String isVideoLable;



    /**
     * 会议类型ID
     */
    @ApiModelProperty(value = "会议类型ID")
    private String appTypeId;

    /**
     * 会议类型ID
     */
    @ApiModelProperty(value = "会议类型")
    private String appTypeLable;

    @ApiModelProperty(value = "1:管理员 2 预定人 3参与人：")
    private String currentEmpPower;



    /**
     * 位置指引
     */
    @ApiModelProperty(value = "位置指引")
    private String positionPicture;

    /**
     * 会议室图片
     */
    @ApiModelProperty(value = "会议室图片")

    private String emphasis;


    /**
     * 是否签到
     */
    @ApiModelProperty(value = "是否签到 0未签到 ， 1已签到,2已请假,3 迟到")
    private String currentEmpSignin;

    /**
     * 是否签到
     */
    @ApiModelProperty(value = "是否签到 0未签到 ， 1已签到,2已请假,3 迟到")
    private String currentEmpSigninLable;
    /**
     * 是否签到
     */
    @ApiModelProperty(value = "是否签到 签退状态(1：已签退)")
    private String currentEmpSignout;

    /**
     * 是否签到
     */
    @ApiModelProperty(value = "是否签到 签退状态(1：已签退)")
    private String currentEmpSignoutLable;


    /**
     * 手附件业务ID
     */
    @ApiModelProperty(value = "附件业务ID")
    private String attachmentBusinessId;

    @ApiModelProperty(value = "取消员工工号")
    String  cancelEmpNo;

    @ApiModelProperty(value = "取消时间")
    String  cancelTime;

    @ApiModelProperty(value = "取消备注")
    String  cancelRemark;

    @ApiModelProperty(value = "取消员人信息")
    Employee cancelEmployee;

    @ApiModelProperty(value = "是否要求签退 1要求2不要求")
    Integer  signOutType;

    @ApiModelProperty(value = "结束前多少分钟")
    Integer  signOutAdvanceMinute;



    /**
     * 1 自动关闭 2不自动
     */
    @ApiModelProperty(value = "1 自动关闭 2不自动")
    private Integer autoOffType;



    /**
     * 1 刷新 2不刷新
     */
    @ApiModelProperty(value = "1 刷新 2不刷新")
    private Integer autoRefreshSignInQrCodeType;
    
    /**
     *  刷新时间
     */
    @ApiModelProperty(value = "刷新时间")
    private Integer autoRefreshSignInQrCodeDate;
 
    @Setter
    @Getter
    public static class Device
    {
        @ApiModelProperty(value = "id")
        String id;

        @ApiModelProperty(value = "名称")
        String name;

        @ApiModelProperty(value = "图标")
        String icon;

    }
    
    @ApiModelProperty(value = "流程ID")
    private String wfInstId;
}