package cn.trasen.oa.boardroom.model;

import io.swagger.annotations.*;
import java.util.Date;
import javax.persistence.*;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

/**    
  * <P> @Description: 会议类型表</p>
  * <P> @Date: 2020年10月26日  下午5:31:24 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
    
@Table(name = "TOA_BOARDROOM_APPTYPE")
@Setter
@Getter
public class BoardroomApptype {
    /**
     * 会议类型表ID
     */
    @Id
    @Column(name = "ID")
    @ApiModelProperty(value = "会议类型表ID")
    private String id;

    /**
     * 会议类型名称
     */
    @Column(name = "TYPE")
    @ApiModelProperty(value = "会议类型名称")
    private String type;
    
    /**
     * 排序号
     */
    @Column(name = "NUM")
    @ApiModelProperty(value = "排序号")
    private Integer num;

    /**
     * 创建用户code
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value = "创建用户code")
    private String createUser;

    /**
     * 更新用户code
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value = "更新用户code")
    private String updateUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") 
    @Column(name = "CREATE_DATE")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8") 
    @Column(name = "UPDATE_DATE")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 是否删除
     */
    @Column(name = "IS_DELETED")
    @ApiModelProperty(value = "是否删除")
    private String isDeleted;

    /**
     * 创建用户名称
     */
    @Column(name = "CREATE_USER_NAME")
    @ApiModelProperty(value = "创建用户名称")
    private String createUserName;

    /**
     * 更新用户名称
     */
    @Column(name = "UPDATE_USER_NAME")
    @ApiModelProperty(value = "更新用户名称")
    private String updateUserName;

    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建机构
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建机构")
    private String createDept;

    /**
     * 创建机构名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建机构名称")
    private String createDeptName;
}