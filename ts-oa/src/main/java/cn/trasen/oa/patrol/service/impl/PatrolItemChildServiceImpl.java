package cn.trasen.oa.patrol.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.patrol.dao.PatrolItemChildMapper;
import cn.trasen.oa.patrol.model.PatrolItemChild;
import cn.trasen.oa.patrol.service.PatrolItemChildService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PatrolItemChildServiceImpl
 * @Description TODO
 * @date 2021ƒÍ12‘¬21»’ œ¬ŒÁ2:40:39
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PatrolItemChildServiceImpl implements PatrolItemChildService {

	@Autowired
	private PatrolItemChildMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(PatrolItemChild record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PatrolItemChild record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID‰∏çËÉΩ‰∏∫Á©∫.");
		PatrolItemChild record = new PatrolItemChild();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PatrolItemChild selectById(String id) {
		Assert.hasText(id, "ID‰∏çËÉΩ‰∏∫Á©∫.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PatrolItemChild> getDataSetList(Page page, PatrolItemChild record) {
		Example example = new Example(PatrolItemChild.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<PatrolItemChild> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
