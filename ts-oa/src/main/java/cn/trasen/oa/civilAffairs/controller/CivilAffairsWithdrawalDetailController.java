package cn.trasen.oa.civilAffairs.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.civilAffairs.model.CivilAffairsWithdrawalDetail;
import cn.trasen.oa.civilAffairs.service.CivilAffairsWithdrawalDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CivilAffairsWithdrawalDetailController支取缴存明细
 * @Description TODO
 * @date 2023��12��11�� ����4:12:45
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "CivilAffairsWithdrawalDetailController")
public class CivilAffairsWithdrawalDetailController {

	private transient static final Logger logger = LoggerFactory.getLogger(CivilAffairsWithdrawalDetailController.class);

	@Autowired
	private CivilAffairsWithdrawalDetailService civilAffairsWithdrawalDetailService;

	/**
	 * @Title saveCivilAffairsWithdrawalDetail
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��11�� ����4:12:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/civilAffairsWithdrawalDetail/save")
	public PlatformResult<String> saveCivilAffairsWithdrawalDetail(@RequestBody CivilAffairsWithdrawalDetail record) {
		try {
			civilAffairsWithdrawalDetailService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCivilAffairsWithdrawalDetail
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2023��12��11�� ����4:12:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/civilAffairsWithdrawalDetail/update")
	public PlatformResult<String> updateCivilAffairsWithdrawalDetail(@RequestBody CivilAffairsWithdrawalDetail record) {
		try {
			civilAffairsWithdrawalDetailService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCivilAffairsWithdrawalDetailById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CivilAffairsWithdrawalDetail>
	 * @date 2023��12��11�� ����4:12:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/civilAffairsWithdrawalDetail/{id}")
	public PlatformResult<CivilAffairsWithdrawalDetail> selectCivilAffairsWithdrawalDetailById(@PathVariable String id) {
		try {
			CivilAffairsWithdrawalDetail record = civilAffairsWithdrawalDetailService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCivilAffairsWithdrawalDetailById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2023��12��11�� ����4:12:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/civilAffairsWithdrawalDetail/delete/{id}")
	public PlatformResult<String> deleteCivilAffairsWithdrawalDetailById(@PathVariable String id) {
		try {
			civilAffairsWithdrawalDetailService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCivilAffairsWithdrawalDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsWithdrawalDetail>
	 * @date 2023��12��11�� ����4:12:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/civilAffairsWithdrawalDetail/list")
	public DataSet<CivilAffairsWithdrawalDetail> selectCivilAffairsWithdrawalDetailList(Page page, CivilAffairsWithdrawalDetail record) {
		return civilAffairsWithdrawalDetailService.getDataSetList(page, record);
	}
	
	
	/**
	 * @Title selectCivilAffairsWithdrawalDetailList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsWithdrawalDetail>
	 * @date 2023��12��11�� ����4:12:45
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@GetMapping("/api/civilAffairsWithdrawalDetail/PageList")
	public DataSet<CivilAffairsWithdrawalDetail> selectCivilAffairsWithdrawalDetailPageList(Page page, CivilAffairsWithdrawalDetail record) {
		return civilAffairsWithdrawalDetailService.getPageList(page, record);
	}
}
