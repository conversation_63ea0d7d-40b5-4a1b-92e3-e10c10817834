package cn.trasen.oa.civilAffairs.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.civilAffairs.dao.CivilAffairsWithdrawalDetailMapper;
import cn.trasen.oa.civilAffairs.model.CivilAffairsWithdrawalDetail;
import cn.trasen.oa.civilAffairs.service.CivilAffairsWithdrawalDetailService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilAffairsWithdrawalDetailServiceImpl
 * @Description TODO
 * @date 2023��12��11�� ����4:12:45
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CivilAffairsWithdrawalDetailServiceImpl implements CivilAffairsWithdrawalDetailService {

	@Autowired
	private CivilAffairsWithdrawalDetailMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CivilAffairsWithdrawalDetail record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CivilAffairsWithdrawalDetail record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CivilAffairsWithdrawalDetail record = new CivilAffairsWithdrawalDetail();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CivilAffairsWithdrawalDetail selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CivilAffairsWithdrawalDetail> getDataSetList(Page page, CivilAffairsWithdrawalDetail record) {
		Example example = new Example(CivilAffairsWithdrawalDetail.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		 if (StringUtils.isNotBlank(record.getWithdrawalRecordsId())) {//支取缴存ID
	            example.and().andEqualTo("withdrawalRecordsId",record.getWithdrawalRecordsId());
	        }
		 if (StringUtils.isNotBlank(record.getType())) {//类型
	            example.and().andLike("type", "%" + record.getType() + "%");
	        }
		List<CivilAffairsWithdrawalDetail> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<CivilAffairsWithdrawalDetail> getPageList(Page page, CivilAffairsWithdrawalDetail record) {
		// TODO Auto-generated method stub
		List<CivilAffairsWithdrawalDetail> records = mapper.getPageList( page,  record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
