package cn.trasen.oa.clock.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.clock.dao.WxclockDaydataMapper;
import cn.trasen.oa.clock.dao.WxclockMonthdataMapper;
import cn.trasen.oa.clock.model.WxclockMonthdata;
import cn.trasen.oa.clock.model.WxclockSetting;
import cn.trasen.oa.clock.service.WxclockMonthdataService;
import cn.trasen.oa.clock.service.WxclockSettingService;

/**
 * @ClassName WxclockMonthdataServiceImpl
 * @Description TODO
 * @date 2023��5��24�� ����3:42:54
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class WxclockMonthdataServiceImpl implements WxclockMonthdataService {

	@Autowired
	private WxclockMonthdataMapper mapper;
	
	@Autowired
	private WxclockDaydataMapper wxclockDaydataMapper;
	
	@Autowired
	private WxclockSettingService wxclockSettingService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(WxclockMonthdata record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(WxclockMonthdata record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		WxclockMonthdata record = new WxclockMonthdata();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public WxclockMonthdata selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<WxclockMonthdata> getDataSetList(Page page, WxclockMonthdata record) {
		
		
		if(StringUtils.isBlank(record.getMonth())){
			int year = DateUtil.year(new Date());
			int month = DateUtil.month(new Date()) + 1;
			record.setMonth(year + "-" + String.format("%02d",month));
		}
		
		List<WxclockMonthdata> records = mapper.getDataSetList(record, page);
		
		WxclockSetting wxclockSetting = wxclockSettingService.selectWxclockSetting();
		
		if(StringUtils.isNotBlank(wxclockSetting.getTableName())){
			String[] tableNameArray = wxclockSetting.getTableName().split(",");
			String[] startNameArray = wxclockSetting.getStartName().split(",");
			String[] endNameArray = wxclockSetting.getEndName().split(",");
			String[] reasonNameArray = wxclockSetting.getReasonName().split(",");
			String[] typeNameArray = wxclockSetting.getTypeName().split(",");
			String[] daysNameArray = wxclockSetting.getDaysName().split(",");
		
			for (WxclockMonthdata wxclockMonthdata : records) {//需要查询相关请假流程
				
				if(null != wxclockMonthdata.getStandardWorkSec()){
					wxclockMonthdata.setStandardWorkSec(wxclockMonthdata.getStandardWorkSec()/60/60);
				}
				if(null != wxclockMonthdata.getRegularWorkSec()){
					wxclockMonthdata.setRegularWorkSec(wxclockMonthdata.getRegularWorkSec()/60/60);
				}
				if(null != wxclockMonthdata.getLateDuration()){
					wxclockMonthdata.setLateDuration(wxclockMonthdata.getLateDuration()/60/60);
				}
				if(null != wxclockMonthdata.getEarlyDuration()){
					wxclockMonthdata.setEarlyDuration(wxclockMonthdata.getEarlyDuration()/60/60);
				}
				if(null != wxclockMonthdata.getAbsenteeismDuration()){
					wxclockMonthdata.setAbsenteeismDuration(wxclockMonthdata.getAbsenteeismDuration()/60/60);
				}
				
				wxclockSetting.setEmpCode(wxclockMonthdata.getEmpCode());
				
				if(StringUtils.isBlank(record.getMonth())){
					wxclockSetting.setStartDate(DateUtil.format(DateUtil.beginOfMonth(new Date()), "yyyy-MM-dd")  + " 00:00");
					wxclockSetting.setEndDate(DateUtil.format(DateUtil.endOfMonth(new Date()),"yyyy-MM-dd") + " 23:59");
				}else{
					wxclockSetting.setStartDate(record.getMonth() + "-01 00:00");
					wxclockSetting.setEndDate(record.getMonth() + "-31 23:59");
				}
				
				List<Map<String, Object>> leaveDatasAll = new ArrayList<>();
				for (int i = 0; i < tableNameArray.length; i++) {
					wxclockSetting.setTableName(tableNameArray[i]);
					wxclockSetting.setStartName(startNameArray[i]);
					wxclockSetting.setEndName(endNameArray[i]);
					wxclockSetting.setReasonName(reasonNameArray[i]);
					wxclockSetting.setTypeName(typeNameArray[i]);
					wxclockSetting.setDaysName(daysNameArray[i]);
					
					List<Map<String, Object>> leaveDatas = wxclockDaydataMapper.getLeaveData(wxclockSetting);
					
					leaveDatasAll.addAll(leaveDatas);
				}
				
				wxclockMonthdata.setLeaveDatas(leaveDatasAll);
			}
		}
		
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
