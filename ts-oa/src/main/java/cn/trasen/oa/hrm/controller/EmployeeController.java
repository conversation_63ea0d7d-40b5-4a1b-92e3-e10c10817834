package cn.trasen.oa.hrm.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.XmlUtil;
import cn.trasen.BootComm.utils.PasswordHash;
import cn.trasen.BootComm.utils.RedisUtil;
import cn.trasen.homs.core.bean.ThpsDeptReq;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.hrm.model.Employee;
import cn.trasen.oa.hrm.po.EmployeeReq;
import cn.trasen.oa.hrm.po.ResponseJson;
import cn.trasen.oa.hrm.service.EmployeeService;
import cn.trasen.oa.hrm.utils.OrgData;
import cn.trasen.oa.utils.AESUtil;
import cn.trasen.oa.utils.AESUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @version V1.0
 * @Description: 员工信息Controller层
 * @Date: 2020/1/11 16:22
 * @Author: Lizhihuo
 * @Company: 湖南创星
 */
@Api(tags = "员工信息")
@RestController
public class EmployeeController {

    private static final Logger logger = LoggerFactory.getLogger(EmployeeController.class);

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private RedisUtil redisUtil;
    
    @Value("${sso.defaultPubkey}")
    private String ssoDefaultPubkey;

 
    /** 
    * @description: 查询员工信息列表
	* @param: page
	* @param: record
    * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.oa.hrm.model.Employee>
    * @author: liyuan
    * @createTime: 2021/6/18 17:47
    */
    @ApiOperation(value = "员工信息列表", notes = "员工信息列表")
    @PostMapping("/employee/list")
    public DataSet<Employee> getDataList(Page page, Employee record) {
        try {
            List<Employee> list = employeeService.getDataList(page, record);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        }catch(Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }
    /**
     * <p> @Title: selectByPrimaryKey</p>
     * <p> @Description: 通过主键查询员工信息</p>
     * <p> @param employee
     * <p> @return</p>
     * <p> @Return: PlatformResult<Employee></p>
     * <p> <AUTHOR>
     */
    @ApiOperation(value = "通过主键查询员工信息", notes = "通过主键查询员工信息")
    @PostMapping("/employee/selectByPrimaryKey")
    public PlatformResult<Employee> selectByPrimaryKey(String userCode) {
        try {
            Employee emp = null;
            if (StringUtils.isNotBlank(userCode)) {
                emp = employeeService.selectByPrimaryKey(userCode);
            } else {
                emp = employeeService.selectByPrimaryKey(UserInfoHolder.getCurrentUserCode());
            }
            return PlatformResult.success(emp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，更新失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @throws
     * @Title: selectByUserCode
     * @Description: 根据用户code查询用户信息
     * @param: @param userCodeArray
     * @param: @return
     * @return: List<Employee>
     * @author: YueC
     * @date: 2020年3月5日 上午11:43:05
     */
    @GetMapping("/employee/selectEmpByUserCode")
    public PlatformResult<List<Employee>> selectEmpByUserCode(String userCodeStr) {
        try {
            String[] userCodeArray = userCodeStr.split(",");
            List<Employee> list = employeeService.selectByUserCode(userCodeArray);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }


    /**
     * @Author: Lizhihuo
     * @Description: 查询个人设置的基础信息(pc 、 app)
     * @Date: 2020/3/7 14:20
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<cn.trasen.oa.hrm.model.Employee>
     **/
    @ApiOperation(value = "查询个人设置的基础信息(pc、app)", notes = "查询个人设置的基础信息(pc、app)")
    @GetMapping("/employee/personalInformationSettings")
    public PlatformResult<Employee> personalInformationSettings(String userCode,String isHidden,HttpServletRequest request) {
        try {
        	 String token = "";
             if(StringUtils.isEmpty(UserInfoHolder.getToken())) {
             	token = request.getParameter("token");
             }else {
             	token = UserInfoHolder.getToken();
             }
             if(StringUtils.isEmpty(userCode)) {
             	userCode = UserInfoHolder.getCurrentUserCode();
             }
             String source = request.getParameter("source");
             Employee emp = employeeService.personalInformationSettings(userCode,token,source,isHidden);
             return PlatformResult.success(emp);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，更新失败，失败原因：" + e.getMessage());
        }
    }
    
    /**
     * @Author: Lizhihuo
     * @Description: 个人设置-员工信息修改(pc、app)
     * @Date: 2020/3/10 14:22
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<cn.trasen.oa.hrm.model.Employee>
     **/
    @ApiOperation(value = "个人设置-员工信息修改(pc、app)", notes = "个人设置-员工信息修改(pc、app)")
    @PostMapping("/employee/updateEmployee")
    public PlatformResult<String> updateEmployee(@RequestBody Employee employee) {
        try {
            employeeService.update(employee);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，更新失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @throws
     * @Title: getEmployeeByBirthday
     * @Description: 查询当天生日的员工信息
     * @param: @return
     * @return: PlatformResult<List < Employee>>
     * @author: YueC
     * @date: 2020年4月15日 上午10:55:37
     */
    @GetMapping("/employee/getEmployeeByBirthday")
    public PlatformResult<List<Employee>> getEmployeeByBirthday() {
        try {
            List<Employee> empList = employeeService.getEmployeeByBirthday();
            return PlatformResult.success(empList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
    
    @GetMapping("/employee/getEmployeeByHiredate")
    public PlatformResult<List<Employee>> getEmployeeByHiredate() {
        try {
            List<Employee> empList = employeeService.getEmployeeByHiredate();
            return PlatformResult.success(empList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 分页获取员工信息列表（pc即时通讯、假期管理）
     * @Date: 2020/4/15 14:06
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.oa.hrm.model.Employee>
     **/
    @ApiOperation(value = "分页获取员工信息列表（pc即时通讯、假期管理）", notes = "分页获取员工信息列表（pc即时通讯、假期管理）")
    @GetMapping("/employee/getEmployeeList")
    public DataSet<Employee> getEmployeeList(Page page, String empName, String empCode, String empDeptName, String empDeptCode,String searchKey) {
	    try {
    		List<Employee> list = employeeService.getEmployeeList(page, empName, empCode, empDeptName, empDeptCode,searchKey);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Description: 微信端获取员工信息列表
     * @Date: 2020/4/15 14:06
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.oa.hrm.model.Employee>
     **/
    @ApiOperation(value = "微信端获取员工信息列表", notes = "微信端获取员工信息列表")
    @GetMapping("/employee/getWXEmployeeList")
    public DataSet<Employee> getWXEmployeeList(Page page, String empName) {
    	try {
    		List<Employee> list = employeeService.getEmployeeList(page, empName, null, null, null,null);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }



    /**
     * 
     * @Title: getUsersByDeptCode   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param deptCode
     * @param: @return      
     * @return: PlatformResult<List<Employee>>  
     * @author: YueC
     * @date:   2020年9月14日 上午11:14:43    
     * @throws
     */
    @ApiOperation(value = "根据科室code查询科室人员列表", notes = "根据科室code查询科室人员列表")
    @GetMapping("/employee/getUsersByDeptCode")
    public PlatformResult<List<Employee>> getUsersByDeptCode(String deptCode) {
        try {
            String[] deptCodeArray = null;
            if (deptCode.contains(",")) {
                deptCodeArray = deptCode.split(",");
            } else {
                deptCodeArray = new String[1];
                deptCodeArray[0] = deptCode;
            }
            List<Employee> empList = employeeService.getUsersByDeptCode(deptCodeArray);
            return PlatformResult.success(empList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * @throws
     * @Title: getUsersCountByDeptCode
     * @Description: TODO(描述这个方法的作用)
     * @param: @param deptCode
     * @param: @return
     * @return: PlatformResult<String>
     * @author: YueC
     * @date: 2020年5月8日 下午6:29:59
     */
    @ApiOperation(value = "根据科室code查询科室人员总数", notes = "根据科室code查询科室人员总数量")
    @GetMapping("/employee/getUsersCountByDeptCode")
    public PlatformResult<String> getUsersCountByDeptCode(String deptCode) {
        try {
            String[] deptCodeArray = null;
            if (StringUtils.isNotEmpty(deptCode)) {
                if (deptCode.contains(",")) {
                    deptCodeArray = deptCode.split(",");
                } else {
                    deptCodeArray = new String[1];
                    deptCodeArray[0] = deptCode;
                }
            }
            Long empCount = employeeService.getUsersCountByDeptCode(deptCodeArray);
            return PlatformResult.success(String.valueOf(empCount));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 获取操作按钮的权限
     * @Date: 2020/2/22 16:36
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.util.List < cn.trasen.document.model.DocumentAccessory>>
     **/
    @ApiOperation(value = "获取操作按钮的权限", notes = "获取操作按钮的权限")
    @GetMapping("/employee/isHrmAdmin")
    public PlatformResult<Boolean> isHrmAdmin() {
        try {
            boolean isHrmAdmin = UserInfoHolder.getRight("HRM_MASTER");
            return PlatformResult.success(isHrmAdmin);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 微信端退出登录 将企业微信授权用户ID设置为空
     * @Date: 2020/6/1 16:29
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.util.Map < java.lang.String, java.lang.String>>
     **/
    @ApiOperation(value = "微信端退出登录", notes = "微信端退出登录")
    @GetMapping("/employee/wxSignOut")
    public PlatformResult<String> wxSignOut() {
        try {
            employeeService.wxSignOut(UserLoginService.getCurrentUserCode());
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }
    
    /**
     * 
     * @Title: valdiateUser   
     * @Description: 密码重置验证用户信息   
     * @param: @param userAccount
     * @param: @param userPhone
     * @param: @return      
     * @return: PlatformResult<String>  
     * @author: YueC
     * @date:   2020年6月17日 上午9:44:54    
     * @throws
     */
    @PostMapping("/employee/valdiateUser")
    public PlatformResult<String> valdiateUser(String userAccount,String userPhone) {
        try {
        	Employee emp = new Employee();
        	emp.setUserAccounts(userAccount);
        	//emp.setEmpPhone(userPhone);
        	Employee employee = employeeService.selectByEntity(emp);
        	if(null == employee || (StringUtils.isBlank(employee.getEmpPhone()) && StringUtils.isBlank(employee.getOpenId()))) {
        		//return PlatformResult.failure("未查询到此账号或者此账号的手机号和邮箱数据未维护,请重新输入或联系管理员");
        		return PlatformResult.failure("获取失败");
        	}else {
        		String token = PasswordHash.createHash(userAccount);
        		redisUtil.set("valdiateUser_" + userAccount, token, 120);
        		return PlatformResult.success(token);
        	}
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常,异常信息：" + e.getMessage());
        }
    }
    
    /**
     * 
     * @Title: resetPassword   
     * @Description: 重置密码   
     * @param: @param userAccount
     * @param: @param userPhone
     * @param: @return      
     * @return: PlatformResult<String>  
     * @author: YueC
     * @date:   2020年6月17日 下午5:49:18    
     * @throws
     */
    @ApiOperation(value = "重置密码", notes = "重置密码")
    @PostMapping("/employee/resetPassword")
    public PlatformResult<String> resetPassword(String userAccount,String newPassword,String token) {
        try {
        	
        	String tk = (String) redisUtil.get("valdiateReset_" + userAccount);
			if(!tk.equals(token)) {
				return PlatformResult.failure("非法请求,请刷新页面重试");
			}
        	
        	Employee emp = new Employee();
        	emp.setUserAccounts(userAccount);
        	Employee employee = employeeService.selectByEntity(emp);
        	if(null != employee) {
        		logger.info("新密码：" + newPassword);
        		String desEncrypt = AESUtils.desEncrypt(newPassword.trim());
        		logger.info("转码后新密码：" + desEncrypt);
        		employee.setEmpPassword(desEncrypt);
        		employeeService.resetPassword(employee);
        		return PlatformResult.success("重置密码成功");
        	}else {
        		return PlatformResult.failure("没有查询到员工信息");
        	}
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常,异常信息：" + e.getMessage());
        }
    }
    
    /**
     * 
     * @Title: selectEmpAgentId   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param userCode
     * @param: @return      
     * @return: PlatformResult<String>  
     * @author: YueC
     * @date:   2020年12月26日 下午2:56:02    
     * @throws
     */
    @ApiOperation(value = "查询流程代理人", notes = "查询流程代理人")
    @GetMapping("/employee/selectEmpAgentId")
    public PlatformResult<String> selectEmpAgentId(String userCode) {
        try {
        	List<String> empAgentId = employeeService.selectEmpAgentId(userCode);
            return PlatformResult.success(StringUtils.join(empAgentId, ","));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
    
    /**
     * 
     * @Title: selectEmpSignimg   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param userCode
     * @param: @return      
     * @return: PlatformResult<String>  
     * @author: YueC
     * @date:   2021年1月9日 上午11:52:27    
     * @throws
     */
    @ApiOperation(value = "查询邮箱签名", notes = "查询邮箱签名")
    @GetMapping("/employee/selectEmpSignimg")
    public PlatformResult<String> selectEmpSignimg(String userCode) {
        try {
        	if(StringUtils.isEmpty(userCode)) {
        		userCode = UserInfoHolder.getCurrentUserCode();
        	}
        	String empSignimg = employeeService.selectEmpSignimg(userCode);
            return PlatformResult.success(empSignimg);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
    
    /**
     * 
     * @Title: selectEmpUploadFileSize   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @param empCode
     * @param: @return      
     * @return: PlatformResult<String>  
     * @author: Yuec    
     * @date:   2021年4月14日 下午3:34:50       
     * @throws
     */
    @ApiOperation(value = "查询个人允许上传的附件大小", notes = "查询个人允许上传的附件大小")
    @GetMapping("/employee/selectEmpUploadFileSize")
    public PlatformResult<String> selectEmpUploadFileSize(String empCode) {
        try {
        	if(StringUtils.isEmpty(empCode)) {
        		empCode = UserInfoHolder.getCurrentUserCode();
        	}
        	String empUploadFileSize = employeeService.selectEmpUploadFileSize(empCode);
            return PlatformResult.success(empUploadFileSize);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
    
    /**
     * 
     * @param employee
     * @return
     */
    @ApiOperation(value = "新增或修改员工信息", notes = "新增或修改员工信息")
    @PostMapping("/employee/insertOrUpdate")
    public PlatformResult<String> insertOrUpdate(@RequestBody EmployeeReq employee) {
        try {
            employeeService.insertOrUpdate(employee);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，更新失败，失败原因：" + e.getMessage());
        }
    }
    
    @ApiOperation(value = "同步部门数据到企业微信", notes = "同步部门数据到企业微信")
    @PostMapping("/employee/syncDeptToWeixin")
    public PlatformResult<String> syncDeptToWeixin(@RequestBody ThpsDeptReq thpsDeptReq) {
        try {
            employeeService.syncDeptToWeixin(thpsDeptReq);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，同步部门数据到企业微信失败，失败原因：" + e.getMessage());
        }
    }
    
    
    
    /**
     * 
     * @Title: selectEmployeeList   
     * @Description: TODO(描述这个方法的作用)   
     * @param: @return      
     * @return: PlatformResult<List<Employee>>  
     * @author: YueC
     * @date:   2021年8月17日 下午3:55:36    
     * @throws
     */
    @ApiOperation(value = "提供给外部系统使用的用户数据", notes = "提供给外部系统使用的用户数据")
    @GetMapping("/oa/api/selectEmployeeList")
    public PlatformResult<String> selectEmployeeList() {
        try {
            List<Map<String, Object>> list = employeeService.selectEmployeeList();
            String listStr = AESUtil.encrypt(JSON.toJSONString(list),ssoDefaultPubkey);
            return PlatformResult.success(listStr);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("服务端异常，查询失败，失败原因：" + e.getMessage());
        }
    }
    
    /**
     * 该接口已移至ts-homs-BaseService
     * @param orgData
     * @return
     */
    @Deprecated
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping(value = "/addOrgData")
    public PlatformResult<String> addOrgData(@RequestBody OrgData orgData) {
    	
    	orgData.setOrgId(String.valueOf(IdWork.id.nextId()));
    	
    	String deptId =String.valueOf(IdWork.id.nextId());
    	deptId =  deptId.substring(deptId.length() - 8);
    	orgData.setDeptId(deptId);
    	orgData.setUserId(String.valueOf(IdWork.id.nextId()));
    	orgData.setOrgMapId(String.valueOf(IdWork.id.nextId()));
    	orgData.setSettingId(String.valueOf(IdWork.id.nextId()));
    	orgData.setThemeId(String.valueOf(IdWork.id.nextId()));
    	orgData.setElementId1(String.valueOf(IdWork.id.nextId()));
    	orgData.setElementId2(String.valueOf(IdWork.id.nextId()));
    	orgData.setElementId3(String.valueOf(IdWork.id.nextId()));

    	employeeService.initOrgData(orgData); 
    	
        return PlatformResult.success();
    }
    
    
    @PostMapping("/bheyy/employee/syncOrgData")
    public String syncOrgData(String extensionId) {
    	
    	String str = ResourceUtil.readUtf8Str("templates/hi7v3-out.xml");
		
		Document document = XmlUtil.parseXml(str);
		Element elementRoot = XmlUtil.getRootElement(document);
		
		Element elementId = XmlUtil.getElement(elementRoot,"id");
		elementId.setAttribute("extension", IdUtil.randomUUID());
		
		Element creationTime = XmlUtil.getElement(elementRoot,"creationTime");
		creationTime.setAttribute("value", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
		
		Element acknowledgement = XmlUtil.getElement(elementRoot,"acknowledgement");
		Element targetMessage = XmlUtil.getElement(acknowledgement,"targetMessage");
		Element targetMessage_id = XmlUtil.getElement(targetMessage,"id");
		targetMessage_id.setAttribute("extension", extensionId);
		
		String format = XmlUtil.format(document);
		format = format.replaceFirst("^<\\?xml.*\\?>", "");
		
	    return format;
    }
}
