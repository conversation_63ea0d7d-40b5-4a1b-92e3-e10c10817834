package cn.trasen.oa.political.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.patrol.model.PatrolType;
import cn.trasen.oa.political.model.Political;
import cn.trasen.oa.political.model.PoliticalType;
import tk.mybatis.mapper.common.Mapper;

public interface PoliticalTypeMapper extends Mapper<PoliticalType> {
	
	/**
	 * 
	* @Title: getListByCondition  
	* @Description: 查询分类-用于移动端树形结构
	* @Params: @param record
	* @Params: @return      
	* @Return: List<PoliticalType>
	* <AUTHOR>
	* @date:2021年12月22日
	* @Throws
	 */
	List<PoliticalType> getListByCondition(Political record);
	
	
	List<PoliticalType> getDataSetList(PoliticalType record,Page page);
	
	/**
	 * 
	 * @param 根据组织机构code获取机构父节点id
	 * @return
	 */
	String getUserParentOrgCode(@Param("code") String code);
	
	List<PoliticalType> getAllList(PoliticalType record);
	
	List<PoliticalType> getParentDeptCode(PoliticalType record);
}