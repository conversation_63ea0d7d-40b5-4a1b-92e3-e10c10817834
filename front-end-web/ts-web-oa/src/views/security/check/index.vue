<template>
  <div class="trasen-container  flex-column">
    <ts-tabs v-model="activeTab" @tab-click="search">
      <ts-tab-pane
        v-for="(item, index) in tabList"
        :key="index"
        :name="item.value"
      >
        <span slot="label">{{ item.label }}</span>
      </ts-tab-pane>
    </ts-tabs>
    <ts-search-bar
      v-model="searchForm"
      :actions="searchActions"
      :formList="searchList"
      @search="search"
    >
      <template slot="checkDate">
        <base-date-range-picker
          v-model="searchForm.checkDate"
          type="daterange"
        ></base-date-range-picker>
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_check_management"
      ref="table"
      stripe
      border
      :columns="columns"
      @refresh="handleRefreshTable"
    />
    <DialogCheckRouter ref="dialogCheckRouter" @submit="handleAddOrEdit" />
    <DialogDetail ref="dialogDetail" @submit="search" />
    <DialogPrintDetail ref="dialogPrintDetail" />
  </div>
</template>

<script>
import DialogCheckRouter from './components/dialog-check-router.vue';
import DialogDetail from './components/dialog-detail.vue';
import DialogPrintDetail from './components/dialog-print-detail.vue';
import tableMixin from './mixins/table-mixin';
export default {
  components: {
    DialogCheckRouter,
    DialogDetail,
    DialogPrintDetail
  },
  mixins: [tableMixin],
  data() {
    return {
      activeTab: '0',
      tabList: [
        {
          label: '草稿',
          value: '0'
        },
        {
          label: '待确认',
          value: '1'
        },
        {
          label: '不通过',
          value: '2'
        },
        {
          label: '整改中',
          value: '3'
        },
        {
          label: '已完成',
          value: '4'
        }
      ]
    };
  },
  created() {
    this.$nextTick(() => {
      this.search();
    });
  },
  methods: {
    search() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    handleRefreshTable(page) {
      let searchFormKey = this.searchList.map(item => item.value);
      const searchForm = Object.keys(this.searchForm)
        .filter(key => searchFormKey.includes(key))
        .reduce((result, key) => {
          result[key] = this.searchForm[key];
          return result;
        }, {});
      let { pageNo, pageSize } = page,
        data = {
          ...page,
          ...searchForm,
          status: this.activeTab,
          sidx: 'create_date'
        };

      if (data.checkDate) {
        data.startDate = data.checkDate[0] || '';
        data.endDate = data.checkDate[1] || '';
        delete data.checkDate;
      }
      this.ajax.getCheckList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },
    handleStartCheck() {
      this.$refs.dialogCheckRouter.show();
    },
    async handleAddOrEdit(row) {
      if (row.id) {
        let data = await this.getCheckDetail(row.id);
        this.$refs.dialogDetail.show(data, row && row.id ? 'edit' : 'add');
      } else this.$refs.dialogDetail.show(row, row && row.id ? 'edit' : 'add');
    },
    async handleConfirm(row) {
      let data = await this.getCheckDetail(row.id);
      data.checkResult = 0;
      data.remark = '';
      this.$refs.dialogDetail.show(data, 'confirm');
    },
    async showDetail(row) {
      let data = await this.getCheckDetail(row.id);
      this.$refs.dialogDetail.show(data, 'check');
    },
    async getCheckDetail(id) {
      let res = await this.ajax.getCheckDetailById(id);
      if (!res.success) {
        this.$message.error(res.message || '数据获取失败');
        return {};
      }
      return res.object;
    },
    handleDelete(row) {
      this.$confirm('确定要删除该数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.ajax.deletedCheck(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.$message.success('操作成功!');
            this.search();
          } else {
            this.$message.error(res.message || '操作失败!');
          }
        });
      });
    },
    async handlePrint(row) {
      let data = await this.getCheckDetail(row.id);
      this.$refs.dialogPrintDetail.show(data);
    },
    handleExport() {
      let aDom = document.createElement('a'),
        data = {
          ...this.searchForm,
          status: this.activeTab,
          pageNo: this.$refs.table.pageNo,
          pageSize: this.$refs.table.pageSize
        },
        conditionList = Object.keys(data).map(key => {
          let val = data[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      aDom.href = `/ts-oa/api/startCheck/export?${conditionList.join('&')}`;
      aDom.click();
      window.URL.revokeObjectURL(href); //释放掉blob对象
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ {
  .action-cell {
    cursor: pointer;
    color: $primary-blue;
    &:active {
      color: $primary-blue-active;
    }
  }
}
</style>
