<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :fullscreen="true"
    custom-class="add-political-modal"
    :before-close="handleClose"
  >
    <div class="add-political-modal_container">
      <el-form
        style="width:1190px;margin:0 auto;background:white;padding-top:24px"
        ref="politicalEditFrom"
        :model="formData"
        :rules="rules"
        label-width="85px"
        class="top-form-content"
      >
        <div class="inline-form-item-content">
          <el-form-item label="活动名称:" prop="politicalName">
            <el-input
              v-model="formData.politicalName"
              size="medium"
              placeholder="请输入活动名称"
              style="width: 204px;"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="活动分类:"
            prop="fkPoliticalTypeId"
            label-width="98px"
          >
            <el-select
              v-model="formData.fkPoliticalTypeId"
              size="medium"
              :clearable="true"
              placeholder="请选择活动分类"
              style="width: 260px;"
              filterable
            >
              <el-option
                v-for="(option, opIndex) of politicalTypeList"
                :key="opIndex"
                :value="option.value"
                :label="option.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="责任科室:" label-width="111px" v-if="visible">
          <input-tree
            v-model="formData.responsibilityDeptId"
            placeholder="请选择责任科室"
            :treeData="deptTreeData"
            :defaultExpandedKeys="defaultExpandedKeys"
            style="width: 160px"
            key="responsibilityDeptId"
          ></input-tree>
        </el-form-item> -->
          <el-form-item label="地点坐标:" prop="location" label-width="98px">
            <el-input
              size="medium"
              v-model="formData.location"
              placeholder="请输入活动地点"
              style="width: 204px;"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inline-form-item-content">
          <el-form-item label="组织频率:" class="radio-form-item">
            <el-radio-group v-model="formData.inspectFrequency">
              <el-radio label="BDQ">不定期</el-radio>
              <el-radio label="DQ">定期</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="执行人:"
            class="radio-form-item radio-small-size"
            label-width="142px"
          >
            <!-- <el-radio-group v-model="formData.executorType">
              <el-radio
                v-for="executor in executorList"
                :key="executor.label"
                :label="executor.label"
              >
                {{ executor.name }}
              </el-radio>
            </el-radio-group> -->
          </el-form-item>
          <el-form-item
            v-if="formData.executorType === 'ZDRY'"
            label-width="8px"
            prop="executorStr"
          >
            <el-tooltip
              :disabled="formData.executorStr.length < 16"
              :content="formData.executorStr"
            >
              <el-input
                :readonly="true"
                placeholder="请选择人员"
                size="medium"
                v-model="formData.executorStr"
                style="width: 256px;"
              >
                <img
                  style="cursor: pointer;margin-top: 5px"
                  slot="suffix"
                  width="16px"
                  height="16px"
                  src="@/assets/img/icon_users.png"
                  @click="handleClicksSelectAllUser"
                />
              </el-input>
            </el-tooltip>
          </el-form-item>
        </div>
        <div
          v-if="formData.inspectFrequency == 'DQ'"
          class="inline-form-item-content"
        >
          <el-form-item label="开始时间:" prop="startDate">
            <el-date-picker
              size="medium"
              v-model="formData.startDate"
              :type="startDateType"
              placeholder="开始时间"
              :picker-options="pickerOptions"
              style="width: 160px;"
              :value-format="startDateValueFormat"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="定期类型:"
            class="radio-form-item"
            label-width="142px"
          >
            <el-radio-group
              v-model="formData.frequencyType"
              @change="frequencyTypeChange"
            >
              <el-radio label="0">不固定</el-radio>
              <el-radio label="1">每月</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="执行周期:"
            prop="executionCycle"
            label-width="104px"
            v-if="formData.frequencyType == '0'"
          >
            <el-input
              size="medium"
              v-model="formData.executionCycle"
              style="width: 85px"
              class="short-append-input"
              @input="handleInputNumber($event, 'executionCycle')"
            >
              <span slot="append">天</span>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="formData.inspectFrequency == 'DQ'"
          class="inline-form-item-content"
        >
          <el-form-item label="提醒时间:" prop="remindTime">
            <el-time-select
              size="medium"
              v-model="formData.remindTime"
              placeholder="提醒时间"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '23:30'
              }"
              default-value="08:00"
              style="width: 160px"
            >
            </el-time-select>
          </el-form-item>
          <el-form-item
            label="延期天数上限:"
            prop="delayDays"
            label-width="142px"
          >
            <el-input
              v-model="formData.delayDays"
              size="medium"
              style="width: 85px"
              class="short-append-input"
              @input="handleInputNumber($event, 'delayDays')"
            >
              <span slot="append">天</span>
            </el-input>
          </el-form-item>
        </div>
      </el-form>

      <div class="political-item-content">
        <el-scrollbar
          ref="scroll"
          style="height: 100%;width:100%"
          wrap-class="scroll-wrap"
          view-class="scroll-view"
        >
          <political-form
            style="margin:0 auto"
            ref="politicalForm"
            :conf="formData.itemList"
            @confirm="handleConfirm"
          ></political-form>
          <!-- <div style="height:1200px"></div> -->
        </el-scrollbar>
      </div>
      <div class="footer">
        <el-button @click="handleSavePolitical" class="trasen-perpul">
          提交
        </el-button>
        <el-button @click="handleCancel">关闭</el-button>
      </div>
    </div>
    <ts-user-dept-select ref="SelectPerson" @ok="handleSelectUserOk" />
  </el-dialog>
</template>

<script>
import PoliticalForm from './political-form.vue';
import TsUserDeptSelect from '@/components/ts-user-dept-select';

const addConf = {
  state: 'edit',
  itemContent: '',
  rectificationFollowUp: true,
  itemChildList: [
    {
      state: 'edit',
      itemChildDescribe: '',
      itemChildRequired: false,
      itemChildType: 'DX',
      itemChildContent: ''
    }
  ]
};
const executorList = [
  { label: 'ZDRY', name: '指定人员' },
  { label: 'ZBRY', name: '值班人员' },
  { label: 'BXRY', name: '不限人员' }
];

export default {
  components: { PoliticalForm, TsUserDeptSelect },
  data() {
    return {
      visible: false,
      title: '新增活动',
      formData: {
        politicalName: '',
        fkPoliticalTypeId: '',
        responsibilityDeptId: '',
        location: '',
        inspectFrequency: 'BDQ',
        executorType: 'ZDRY',
        frequencyType: '0',
        startDate: '',
        remindTime: '',
        delayDays: '',
        executorUserId: '',
        executorUserName: '',
        executorStr: '',
        itemList: [],
        executionCycle: undefined,
        delayDays: undefined
      },
      rules: {
        politicalName: [
          { required: true, message: '活动名称不能为空' },
          {
            max: 40,
            message: '活动名称不能超过40字'
          }
        ],
        // executorUserId: [{ required: true, message: '人员不能为空' }],
        executorStr: [{ required: true, message: '人员不能为空' }],
        fkPoliticalTypeId: [{ required: true, message: '活动类型不能为空' }],
        location: [{ required: true, message: '地点坐标不能为空' }],
        startDate: [{ required: true, message: '开始日期不能为空' }],
        executionCycle: [{ required: true, message: '执行周期不能为空' }],
        remindTime: [{ required: true, message: '提醒时间不能为空' }],
        delayDays: [{ required: true, message: '延期上限不能为空' }]
      },
      //活动类型选项列表
      politicalTypeList: [],
      //时间选择配置
      pickerOptions: {
        /**@desc苏莉 20220817 北海提出需求去掉禁用时间 start*/
        // disabledDate: this.computePickRange
        /**@desc苏莉 20220817 end*/
      },
      deptTreeData: [],
      defaultExpandedKeys: [],
      executorList,
      startDateType: 'date',
      startDateValueFormat: 'yyyy-MM-dd'
    };
  },
  created() {
    this.getTreeHandle();
  },
  methods: {
    add(fkPoliticalTypeId) {
      this.title = '新增活动';
      this.formData.fkPoliticalTypeId = fkPoliticalTypeId;
      this.formData.itemList = [];
      this.getPoliticalTypeList();
      this.formData.itemList.push(JSON.parse(JSON.stringify(addConf)));
      this.visible = true;
    },
    edit(row) {
      this.getPoliticalTypeList();
      this.ajax.getpoliticalDetails(row.id).then(res => {
        const itemList = JSON.parse(JSON.stringify(res.object.itemList));
        itemList.forEach(item => {
          item.rectificationFollowUp =
            item.rectificationFollowUp === 'Y' ? true : false;
          item.itemChildList.forEach(itemChild => {
            itemChild.itemChildRequired =
              itemChild.itemChildRequired === 'Y' ? true : false;
          });
        });
        this.formData = Object.assign(this.formData, res.object);
        const connector =
          this.formData.executorUserName && this.formData.responsibilityDeptName
            ? ','
            : '';
        this.formData.executorStr =
          this.formData.executorUserName +
          connector +
          this.formData.responsibilityDeptName;
        if (res.object.frequencyType == null) {
          this.formData.frequencyType = 0;
        }
        this.formData.itemList = itemList;
        this.title = '编辑活动';
        this.visible = true;
      });
    },
    getPoliticalTypeList() {
      this.ajax
        .politicalTypeList({
          pageSize: 100000,
          pageNo: 1,
          sord: 'desc',
          sidx: 's.create_date'
        })
        .then(res => {
          this.politicalTypeList = res.rows || [];
          this.politicalTypeList = this.politicalTypeList.map(e => {
            return { ...e, value: e.id, label: e.politicalTypeName };
          });
        });
    },
    computePickRange(v) {
      return v.getTime() < new Date().getTime() - 86400000;
    },
    handleClose(done) {
      this.$refs.politicalEditFrom.resetFields();
      this.formData = this.$options.data().formData;
      this.executorList = JSON.parse(JSON.stringify(executorList));

      done();
    },
    handleCancel() {
      this.$refs.politicalEditFrom.resetFields();
      this.formData = this.$options.data().formData;
      this.executorList = JSON.parse(JSON.stringify(executorList));
      this.visible = false;
    },
    handleSavePolitical() {
      this.$refs.politicalForm.getData();
    },
    handleInputNumber(data, field) {
      this.$set(
        this.formData,
        field,
        String(this.formData[field]).replace(/[^\d]/g, '')
      );
    },

    async getTreeHandle() {
      try {
        const tree = await this.ajax.getTree();
        if (!tree.success) {
          throw tree.message;
        }
        this.deptTreeData = tree.object || [];
        this.defaultExpandedKeys = [this.deptTreeData[0].id];
      } catch (e) {
        this.$message.error(tree.message || '出错啦');
      }
    },
    handleConfirm(data) {
      this.$refs.politicalEditFrom.validate(async valid => {
        if (valid) {
          const itemList = JSON.parse(JSON.stringify(data));
          itemList.forEach(item => {
            item.rectificationFollowUp = item.rectificationFollowUp ? 'Y' : 'N';
            item.itemChildList.forEach(itemChild => {
              itemChild.itemChildRequired = itemChild.itemChildRequired
                ? 'Y'
                : 'N';
            });
          });
          let params = { itemList: itemList };
          params = Object.assign(this.formData, params);
          try {
            let res;
            if (this.formData.id) {
              // 编辑
              res = await this.ajax.politicalUpdate(params);
            } else {
              // 新增
              res = await this.ajax.politicalSave(params);
            }
            if (res.success) {
              this.$refs.politicalEditFrom.resetFields();
              this.formData = this.$options.data().formData;
              this.executorList = JSON.parse(JSON.stringify(executorList));
              this.visible = false;
              this.$emit('ok');
            }
          } catch (error) {}
        }
      });
    },
    handleClicksSelectAllUser() {
      let empList = [],
        empNames = [],
        empCodes = [],
        deptList = [],
        deptNames = [],
        deptCodes = [];
      if (this.formData.executorUserName && this.formData.executorUserId) {
        empNames = this.formData.executorUserName.split(',');
        empCodes = this.formData.executorUserId.split(',');
      }
      if (
        this.formData.responsibilityDeptName &&
        this.formData.responsibilityDeptId
      ) {
        deptNames = this.formData.responsibilityDeptName.split(',');
        deptCodes = this.formData.responsibilityDeptId.split(',');
      }
      empCodes.forEach((e, index) => {
        empList.push({
          empCode: e,
          empName: empNames[index]
        });
      });
      deptCodes.forEach((e, index) => {
        deptList.push({
          id: e,
          name: deptNames[index]
        });
      });
      this.$refs.SelectPerson.open('componentSelectResult', {
        appendToBody: true,
        showCheckbox: true,
        title: '选择执行人',
        empList,
        deptList: deptList,
        isRadio: false,
        userMaxLimit: 9999,
        treeDataSource: ['personalGroup'],
        systemGroupDisabled: false,
        personalGroupDisabled: false
      });
    },
    handleSelectUserOk(res) {
      const persons = res.componentSelectResult.empList || [];
      const depts = res.componentSelectResult.deptList || [];

      this.formData.executorUserId = persons
        .map(item => item.empCode)
        .join(',');
      this.formData.executorUserName = persons
        .map(item => item.empName)
        .join(',');
      this.formData.responsibilityDeptId = depts.map(item => item.id).join(',');
      this.formData.responsibilityDeptName = depts
        .map(item => item.name)
        .join(',');
      const connector =
        this.formData.executorUserName && this.formData.responsibilityDeptName
          ? ','
          : '';
      this.formData.executorStr =
        this.formData.executorUserName +
        connector +
        this.formData.responsibilityDeptName;
    },
    // 活动频率变化回调
    inspectFrequencyChange(val) {
      let _executorList = JSON.parse(JSON.stringify(executorList));
      if (val === 'DQ') {
        _executorList.pop();
        this.formData.executorType = 'ZDRY';
      } else {
        this.formData.executorType = 'BXRY';
      }
      this.executorList = _executorList;
    },
    frequencyTypeChange(val) {}
  }
};
</script>

<style lang="scss" scoped>
/deep/.add-political-modal {
  background: #f4f4f4;
  border-radius: 0 !important;
  .el-dialog__header {
    background: white;
    margin: 0 !important;
    padding: 8px 16px !important;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  }
  .el-dialog__body {
    padding: 0;
    margin: 0;
  }
  .add-political-modal_container {
    height: 100%;
    background: #f4f4f4;
    display: flex;
    flex-direction: column;
  }
}
.top-form-content {
  min-height: 185px;
  padding: 0 110px;
  border-bottom: 1px solid #e4e4e4;
  margin-bottom: 8px;
}
.inline-form-item-content {
  display: flex;
}
.bottom-form-content {
  width: 1190px;
  margin: 0 auto;
  // padding-top: 10px;
  // padding: 0 167px;
  height: calc(100% - 171px);
}
.add-political-item-btn {
  padding: 0 6px !important;
  height: 24px;
  line-height: unset !important;
  margin-bottom: 10px !important;
}
.political-item-content {
  width: 1190px;
  margin: 0 auto;
  background: white;
  max-height: calc(100% - 46px);
  overflow-y: auto;
}
.political-item:not(:last-child) {
  margin-bottom: 8px;
}
.hidden-political-item {
  display: none !important;
}
.political-item-action i {
  color: #999;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
.political-item-index {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background: #5260ffb3;
  border-radius: 2px 0px 0px 2px;
}
.political-item-title {
  width: 738px;
  line-height: 28px;
  padding: 0 8px;
  border: 1px solid #e0e6f0;
  border-radius: 0 2px 2px 0;
  border-left: none;
  margin-right: 16px;
}

/deep/ {
  .radio-form-item .el-form-item__content {
    height: 32px;
    &,
    .el-radio-group,
    .el-radio {
      display: flex;
      align-items: center;
    }
  }
  .radio-small-size .el-radio-group .el-radio:not(:last-child) {
    margin-right: 20px;
  }
  .short-append-input .el-input-group__append {
    padding: 0 9px;
    background-color: #fafafa;
  }
  .scroll-wrap {
    height: calc(100% + 17px) !important;
  }
}
.scroll-wrap {
  height: calc(100% + 17px) !important;
}
.footer {
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
