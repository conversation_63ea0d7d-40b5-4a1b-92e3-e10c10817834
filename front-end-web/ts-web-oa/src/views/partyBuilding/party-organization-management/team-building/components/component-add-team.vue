<template>
  <ts-dialog
    custom-class="component-add-team"
    append-to-body
    :visible.sync="visible"
    :title="title"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-row>
          <ts-form-item
            prop="organizationId"
            :rules="rules.required"
            label="所在党组织"
          >
            <base-select
              style="width: 100%"
              v-model="form.organizationId"
              :inputText.sync="form.organizationName"
              :loadMethod="handleGetPartyOrganization"
              label="organizationName"
              value="id"
              searchInputName="organizationName"
              :clearable="false"
            ></base-select>
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="当选日期"
              prop="teamDate"
              :rules="rules.required"
            >
              <ts-date-picker
                @change="handleComputedTeamExpire"
                :disabled="type === 'edit'"
                style="width: 100%"
                v-model="form.teamDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="任期年限"
              prop="teamLife"
              :rules="rules.required"
            >
              <ts-select
                @change="handleComputedTeamExpire"
                style="width: 100%"
                :disabled="type === 'edit'"
                v-model="form.teamLife"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of Dictionary.yearLife"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="期满日期"
              prop="teamExpire"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                :disabled="type === 'edit'"
                v-model="form.teamExpire"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <p class="team-person-title">班子成员</p>

        <ts-row style="position: relative;">
          <ts-col :span="12">
            <ts-form-item label="党内职务">
              <ts-select
                style="width: 100%"
                v-model="form.userDuty"
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of Dictionary.politicalPost"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12" style="position: relative;">
            <ts-form-item label="党员姓名">
              <base-select
                style="width: 100%"
                v-model="form.userCode"
                :inputText.sync="form.userName"
                :loadMethod="partyBuildingManageList"
                label="showManageUserName"
                value="manageUserCode"
                searchInputName="manageUserName"
                :clearable="false"
                @select="handlePersonSelect"
              />
            </ts-form-item>
          </ts-col>
          <ts-button
            class="person-save"
            type="primary"
            @click="handlePersonSave"
            >保存
          </ts-button>
        </ts-row>

        <!-- 选择的人回显 展示头像 姓名职务 可删除 -->
        <ts-form-item label="">
          <team-building-person-info
            :teamUserList="form.teamUserList"
            @handleDeletePerson="handleDeletePerson"
          />
        </ts-form-item>

        <ts-form-item label="备注">
          <ts-input
            v-model="form.teamRemark"
            type="textarea"
            class="textarea"
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件">
          <base-upload ref="teamFiles" v-model="form.teamFiles" />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import moment from 'moment';
import { deepClone } from '@/unit/commonHandle.js';
import Dictionary from '@/views/partyBuilding/dictionary.js';

import TeamBuildingPersonInfo from '@/views/partyBuilding/components/team-building-person-info.vue';

export default {
  components: { TeamBuildingPersonInfo },
  props: {},
  data() {
    return {
      disabledDate: current => {
        return current && current <= moment().subtract(1, 'days');
      },
      visible: false,
      title: '',
      type: '',

      Dictionary,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    open(opt = {}) {
      let { title, type, row } = opt;
      this.title = title;
      this.type = type;
      this.initData();

      if (this.type === 'edit') {
        this.$set(this, 'form', deepClone(row));
      }

      this.$set(this.form, 'userDuty', '书记');
      this.$set(this.form, 'userCode', '');
      this.$set(this.form, 'userName', '');
      this.$set(this.form, 'headImg', '');

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });

      this.visible = true;
      this.$forceUpdate();
    },
    handleComputedTeamExpire() {
      let { teamDate, teamLife } = this.form;
      if (teamDate && teamLife) {
        this.$set(
          this.form,
          'teamExpire',
          moment(teamDate)
            .add(teamLife, 'year')
            .format('YYYY-MM-DD')
        );
      }
    },
    initData() {
      this.$set(this.form, 'teamUserList', []);
      this.$set(this.form, 'organizationId', '');
      this.$set(this.form, 'organizationName', '');
      this.$set(this.form, 'teamDate', '');
      this.$set(this.form, 'teamLife', '');
      this.$set(this.form, 'teamExpire', '');
      this.$set(this.form, 'teamRemark', '');
      this.$set(this.form, 'teamFiles', '');
    },
    async handleGetPartyOrganization(data) {
      let res = await this.ajax.partyBuildingOrganizationList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    async partyBuildingManageList(data) {
      let res = await this.ajax.partyBuildingManageList({
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '党员数据获取失败');
        return false;
      }
      res.rows.forEach(item => {
        item.showManageUserName = `${item.manageUserName}-${item.manageDeptName}`;
      });
      return res.rows;
    },
    handlePersonSelect(item) {
      this.$set(this.form, 'headImg', item.headImg);
      this.$set(this.form, 'userCode', item.manageUserCode);
      this.$set(this.form, 'userName', item.manageUserName);
    },
    handlePersonSave() {
      const { userDuty, userCode, userName, headImg } = this.form;
      if (!userDuty || !userName) {
        this.$message.warning('请填写完整信息!');
        return false;
      }

      // 班子成员 不能重复
      const oncePerson = this.form.teamUserList.some(
        item => item.userName === userName
      );

      if (oncePerson) {
        this.$message.warning(`设置重复，请重新设置～`);
        return false;
      }

      // 班子成员 书记 副书记仅可一人
      let onlyOnce = ['书记', '副书记'];
      if (onlyOnce.includes(userDuty)) {
        const result = this.form.teamUserList.some(
          item => item.userDuty === userDuty
        );
        if (result) {
          this.$message.warning(`${userDuty}已设置，仅可一人。`);
          return false;
        }
      }

      this.form.teamUserList.push({
        userDuty,
        userName,
        userCode,
        headImg
      });
      this.form.teamUserList = this.form.teamUserList
        .sort((a, b) => {
          if (a.userDuty === '书记') {
            return -1; // 书记在副书记和委员之前
          } else if (a.userDuty === '副书记' && b.userDuty === '委员') {
            return -1; // 副书记在委员之前
          } else {
            return 1; // 其他情况，委员在最后面
          }
        })
        .map((item, index) => {
          return {
            ...item,
            userSort: index
          };
        });
      // this.$set(this.form, 'userDuty', '书记');
      this.$set(this.form, 'userCode', '');
      this.$set(this.form, 'userName', '');
      this.$set(this.form, 'headImg', '');
      this.$forceUpdate();
    },
    handleDeletePerson(item, index) {
      this.form.teamUserList.splice(index, 1);
    },
    async submit() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign({}, this.form);
      delete data.userDuty;
      delete data.userCode;
      delete data.userName;
      delete data.headImg;

      const hasSecretary = this.form.teamUserList.find(
        item => item.userDuty === '书记'
      );

      if (!hasSecretary) {
        this.$message.warning('书记未设置,请添加');
        return;
      } else {
        data.teamSecretary = hasSecretary.userName;
      }

      const hasTeamDeputySecretary = this.form.teamUserList.find(
        item => item.userDuty === '副书记'
      );
      if (hasTeamDeputySecretary) {
        data.teamDeputySecretary = hasTeamDeputySecretary.userName;
      }

      let API = null;
      if (this.type === 'add') {
        API = this.ajax.partyBuildingTeamSave;
      } else {
        API = this.ajax.partyBuildingTeamUpdate;
      }
      const res = await API(data);

      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('refreshTable');
    },
    close() {
      this.form = {};
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .component-add-team {
    width: 800px !important;
    height: 650px;

    .el-dialog__body {
      height: calc(100% - 88px);
      overflow: auto;
      padding-right: 64px !important;

      .person-save {
        position: absolute;
        right: -62px;
        top: 2px;
      }
    }
  }

  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }

  .city-select-cascader {
    .el-select {
      flex: 1;
    }
  }

  .person-icon {
    margin-top: 3px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .team-person-title {
    font-weight: 700;
    padding: 0 8px;
    padding-left: 40px;

    &::before {
      content: '1';
      background: #5260ff;
      color: #5260ff;
      width: 1px;
      height: 10px;
      margin-right: 8px;
      border-radius: 4px;
    }
  }
}
</style>
