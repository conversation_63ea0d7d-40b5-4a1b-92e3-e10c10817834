<template>
  <div class="party-member-management">
    <ts-search-bar
      v-model="searchForm"
      ref="SearchBar"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{
        statusList: ['1', '6', '12', '99', '9']
      }"
    />

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <componet-party-member-edit
      ref="ComponetPartyMemberEdit"
      @refreshTable="handleRefreshTable"
    />

    <details-party-member ref="DetailsPartyMember" />
  </div>
</template>

<script>
import Dictionary from '@/views/partyBuilding/dictionary.js';
import ComponetPartyMemberEdit from './components/componet-party-member-edit.vue';
import DetailsPartyMember from './components/details-party-member';

export default {
  components: {
    ComponetPartyMemberEdit,
    DetailsPartyMember
  },
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'manageUserName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名'
          }
        },
        {
          label: '党员类型',
          value: 'type',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            { label: '正式党员', value: 1, element: 'ts-option' },
            { label: '预备党员', value: 2, element: 'ts-option' }
          ]
        },
        {
          label: '状态',
          value: 'statusList',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        },
        {
          label: '标识',
          value: 'tipsArr',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: [
            { label: '流入党员', value: 'isInflow', element: 'ts-option' },
            { label: '流出党员', value: 'isOutflow', element: 'ts-option' },
            { label: '退休党员', value: 'isRetire', element: 'ts-option' },
            { label: '历史党员', value: 'isHistory', element: 'ts-option' },
            { label: '出国出境', value: 'isAbroad', element: 'ts-option' }
          ]
        },
        {
          label: '入党日期',
          value: 'date',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],

      loading: false,
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          align: 'center',
          width: 50,
          fixed: true
        },
        {
          label: '姓名',
          prop: 'manageUserName',
          minWidth: 70,
          align: 'center',
          fixed: true,
          formatter: row => {
            return (
              <span
                class="details-span"
                onClick={() => {
                  this.handleDetails(row);
                }}>
                {row.manageUserName}
              </span>
            );
          }
        },
        {
          label: '性别',
          prop: 'manageGender',
          align: 'center',
          minWidth: 60
        },
        {
          label: '年龄',
          prop: 'manageAge',
          align: 'center',
          minWidth: 60,
          formatter: row => {
            let age = '';
            if (row.manageBirthday) {
              age = this.$moment().diff(row.manageBirthday, 'years');
            }
            return age;
          }
        },
        {
          label: '身份证号码',
          prop: 'manageIdentitynumber',
          align: 'center',
          minWidth: 160
        },
        {
          label: '党员类型',
          prop: 'type',
          align: 'center',
          minWidth: 90,
          formatter: row => {
            const find =
              Dictionary.addPartyMemberType.find(
                item => item.value == row.type
              ) || {};
            return <span>{find.label}</span>;
          }
        },
        {
          label: '标识',
          prop: '',
          align: 'center',
          minWidth: 160,
          formatter: row => {
            let { type } = row;
            let haveTips = [
              {
                label: type == 1 ? '正式党员' : '预备党员',
                tips: type == 1 ? '正' : '备'
              }
            ];

            let dir = [
              { key: 'isInflow', label: '流入党员', tips: '入' },
              { key: 'isOutflow', label: '流出党员', tips: '出' },
              { key: 'isAbroad', label: '出国出境', tips: '境' },
              { key: 'isHistory', label: '历史党员', tips: '历' },
              { key: 'isRetire', label: '退休党员', tips: '退' }
            ];

            let tipsArr = haveTips.concat(
              dir.filter(({ key }) => row[key] && row[key] == 1)
            );
            // return tipsArr.map(item => item.label).join(',');
            return tipsArr.map(item => (
              <span class="tips-item">
                {/* <span class="tips-icon">{item.tips}</span> */}
                {item.label}
              </span>
            ));
          }
        },
        {
          label: '党籍状态',
          prop: 'partyBuildingStatus',
          align: 'center',
          minWidth: 110,
          formatter: row => {
            const find =
              Dictionary.addPartyMembershipStatus.find(
                item => item.value == row.partyBuildingStatus
              ) || {};
            return <span>{find.label}</span>;
          }
        },
        {
          label: '所属党组织简称',
          prop: 'organizationShortName',
          align: 'center',
          minWidth: 170
        },
        {
          label: '所属支部',
          prop: 'branchName',
          align: 'center',
          minWidth: 170
        },
        {
          label: '入党日期',
          prop: 'applyDate',
          align: 'center',
          minWidth: 100,
          formatter: row => {
            const label = (row.applyDate || '').slice(0, 10);
            return <span>{label}</span>;
          }
        },
        {
          label: '转为正式党员日期',
          prop: 'fullDate',
          align: 'center',
          minWidth: 120,
          formatter: row => {
            const label = (row.fullDate || '').slice(0, 10);
            return <span>{label}</span>;
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 120,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '编辑',
                event: this.handleChange
              }
            ];

            let func = { 'action-select': e => e(row) };
            return <BaseActionCell actions={arr} on={func} />;
          }
        }
      ]
    };
  },
  created() {
    this.$nextTick(() => {
      this.ajax
        .getDictItem({
          dicTypeId: 'employee_status'
        })
        .then(res => {
          let option = (res.rows || []).map(({ itemName, itemCode }) => {
            return {
              label: itemName,
              value: itemCode,
              element: 'ts-option'
            };
          });

          this.searchList.filter(
            item => item.value === 'statusList'
          )[0].childNodeList = option;
        });
    });
  },
  methods: {
    refresh() {
      this.handleRefreshTable();
    },
    search() {
      this.$refs.table.pageNo = 1;

      if (this.searchForm.tipsArr && this.searchForm.tipsArr.length > 0) {
        this.searchForm.tipsArr.forEach(item => {
          this.searchForm[item] = '1';
        });
      }

      if (this.searchForm.statusList && this.searchForm.statusList.length > 0) {
        this.searchForm.employeeStatusList = this.searchForm.statusList.join(
          ','
        );
      }

      this.searchForm.applyStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.applyEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      this.handleRefreshTable();
    },
    handleChange(row) {
      // 跳转人员档案
      this.$devopParentTypeFun({
        type: 'changePath',
        data: '/personnelmgr/employee/customPage'
      });
      // 通信main main通信webbranch 修改人事档案数据
      this.$devopParentTypeFun({
        type: 'sendMessageToOldFrame',
        detail: {
          type: 'handleEditPersonInfo',
          data: {
            id: row.manageUserCode
          }
        }
      });
      // this.$refs.ComponetPartyMemberEdit.open({
      //   title: '编辑',
      //   type: 'edit',
      //   row: {
      //     id: row.id,
      //     type: row.type,
      //     partyBuildingStatus: row.partyBuildingStatus,
      //     applyDate: row.applyDate,
      //     fullDate: row.fullDate
      //   }
      // });
    },
    handleDetails(row) {
      this.ajax.getPartyBuildingManageDetailInfo(row.id).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '详情获取失败');
          return;
        }
        let {
          manageUserName: flowUserName,
          manageGender: flowGender,
          manageDeptName: flowDeptName,
          managePhone: flowPhone,
          manageBirthday: flowBirthday,
          managePost: postName,
          partyBuildingStatus
        } = res.object;
        let flowAge = this.$moment().diff(flowBirthday, 'years');

        const find =
          Dictionary.addPartyMembershipStatus.find(
            item => item.value == partyBuildingStatus
          ) || {};
        let typeLabel = find.label;

        let row = Object.assign(
          {
            flowUserName,
            flowGender,
            flowDeptName,
            flowPhone,
            flowBirthday,
            postName,
            flowAge,
            typeLabel
          },
          res.object
        );
        this.$refs.DetailsPartyMember?.open({ row });
      });
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          isFull: '1',
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.tipsArr;
      delete searchForm.statusList;
      delete searchForm.date;
      let res = await this.ajax.partyBuildingManageList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex: index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.party-member-management {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      .tips-item {
        margin: 2px 4px 2px 0 !important;
        display: flex;
        align-items: center;
        // width: 100%;
        display: inline-block;
        justify-content: center;
        color: red;

        .tips-icon {
          background: red;
          color: #fff;
          padding: 4px;
          transform: scale(0.7);
          margin-right: 4px;
          border-radius: 4px;
          display: inline-flex;
          align-items: center;
        }
      }

      .details-span {
        color: $primary-blue;
        cursor: pointer;
      }

      .delete-span {
        color: red;
        cursor: pointer;
      }
    }
  }
}
</style>
