<template>
  <div class="efficiency-analysis-box" v-loading="loading">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :showLength="4"
      @search="refresh"
      :resetData="handleResetData"
    >
      <template slot="dateRange">
        <ts-date-picker
          style="width:120px"
          v-model="searchForm.startDate"
          mode="month"
          placeholder="请选择年份"
          format="YYYY-MM"
          :allowClear="false"
          :open="startPickerVisible"
          @openChange="handleStartPickerOpenChange"
          @panelChange="handleStartPickerChange"
        ></ts-date-picker>
        -
        <ts-date-picker
          style="width:120px"
          v-model="searchForm.endDate"
          mode="month"
          placeholder="请选择年份"
          format="YYYY-MM"
          :allowClear="false"
          :open="endPickerVisible"
          @openChange="handleEndPickerOpenChange"
          @panelChange="handleEndPickerChange"
        ></ts-date-picker>
      </template>
      <template slot="but1">
        <ts-button
          :type="dateType == 'pastTwelveMonths' ? 'primary' : ''"
          @click="handleChangeDate('pastTwelveMonths')"
        >
          近12个月
        </ts-button>
      </template>
      <template slot="but2">
        <ts-button
          :type="dateType == 'thisYear' ? 'primary' : ''"
          @click="handleChangeDate('thisYear')"
        >
          本年
        </ts-button>
      </template>
      <template slot="but3">
        <ts-button
          :type="dateType == 'lastYear' ? 'primary' : ''"
          @click="handleChangeDate('lastYear')"
        >
          去年
        </ts-button>
      </template>
    </ts-search-bar>
    <el-scrollbar wrap-style="overflow-x: hidden;">
      <statisticTarget
        ref="statisticTarget"
        :startDate="searchForm.startDate"
        :endDate="searchForm.endDate"
      />
      <div class="contanier">
        <div class="statistic-wrap col-12">
          <p class="statistic-title">流程完成情况</p>
          <div
            class="statistic-content"
            ref="workflowCompletionSituation"
          ></div>
        </div>
        <div class="statistic-wrap col-12">
          <p class="statistic-title">流程耗时分布</p>
          <div
            class="statistic-content"
            ref="workflowTimeConsumptionSituation"
          ></div>
        </div>
        <div class="statistic-wrap col-12">
          <p class="statistic-title">
            预警流程统计
            <span class="more" @click="handleCheckMoreTimeoutStatistics">
              更多
              <i class="el-icon-arrow-right"></i>
            </span>
          </p>
          <base-table
            ref="timeoutTable"
            :hasPage="false"
            :propPageSize="40"
            :pageSizes="[20, 40, 60, 80, 100, 200, 500]"
            :columns="timeoutColumns"
            @refresh="handleTimeoutTableRefresh"
          ></base-table>
        </div>
        <div class="statistic-wrap col-12">
          <p class="statistic-title">各流程总数占比分析</p>
          <div
            class="statistic-content"
            ref="workflowProportionSituation"
          ></div>
        </div>
        <div class="statistic-wrap col-24">
          <p class="statistic-title">
            流程效率统计
            <span class="more" @click="handleCheckMoreEfficiencyStatistics">
              更多
              <i class="el-icon-arrow-right"></i>
            </span>
          </p>
          <base-table
            ref="efficiencyAnalysisTable"
            :hasPage="false"
            :propPageSize="40"
            :pageSizes="[20, 40, 60, 80, 100, 200, 500]"
            :columns="efficiencyAnalysisColumns"
            @refresh="handleEfficiencyAnalysisTableRefresh"
          ></base-table>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import statisticTarget from './components/statistic-target.vue';
export default {
  components: {
    statisticTarget
  },
  data() {
    return {
      loading: false,

      searchForm: {
        startDate: this.$dayjs()
          .startOf('year')
          .format('YYYY-MM'),
        endDate: this.$dayjs()
          .endOf('year')
          .format('YYYY-MM')
      },
      searchList: [
        {
          label: '分析时间',
          value: 'dateRange'
        },
        {
          label: '',
          value: 'but1'
        },
        {
          label: '',
          value: 'but2'
        },
        {
          label: '',
          value: 'but3'
        }
      ],
      dateType: 'thisYear',
      startPickerVisible: false,
      endPickerVisible: false,

      workflowCompletionChartContent: null,
      workflowTimeConsumptionChartContent: null,
      workflowProportionChartContent: null,
      timeoutColumns: [
        {
          label: '流程标题',
          align: 'letf',
          prop: 'workflowTitle'
        },
        {
          label: '当前节点',
          align: 'letf',
          prop: 'currentStepName',
          width: 105
        },
        {
          label: '节点未审批人',
          align: 'letf',
          prop: 'assigneeName',
          width: 120
        },
        {
          label: '发起时间',
          align: 'center',
          width: 140,
          prop: 'createDate'
        },
        {
          label: '停留时长(H)',
          align: 'center',
          prop: 'stopTime',
          width: 100
        }
      ],
      efficiencyAnalysisColumns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 50,
          align: 'center'
        },
        {
          label: '流程标题',
          align: 'letf',
          prop: 'workflowName'
        },
        {
          label: '流程总数',
          align: 'center',
          prop: 'totalNumber',
          width: 100
        },
        {
          label: '已办结',
          align: 'center',
          prop: 'doneNumbers',
          width: 100
        },
        {
          label: '处理中',
          align: 'center',
          prop: 'handleNumbers',
          width: 100
        },
        {
          label: '等待超24h待办',
          align: 'center',
          prop: 'timeoutNumbers',
          width: 135
        },
        {
          label: '办结率',
          align: 'center',
          width: 120,
          formatter: row => {
            return row.doneNumbers
              ? ((row.doneNumbers / row.totalNumber) * 100).toFixed(2) + '%'
              : 0;
          }
        },
        {
          label: '平均耗时(H)',
          align: 'center',
          prop: 'avgDuration',
          width: 120
        },
        {
          label: '最高耗时(H)',
          align: 'center',
          prop: 'maxDuration',
          width: 120
        }
      ]
    };
  },
  methods: {
    handleResetData() {
      this.dateType = 'thisYear';
      return {
        startDate: this.$dayjs()
          .startOf('year')
          .format('YYYY-MM'),
        endDate: this.$dayjs()
          .endOf('year')
          .format('YYYY-MM')
      };
    },
    handleStartPickerOpenChange(status) {
      if (status) {
        this.startPickerVisible = true;
        this.endPickerVisible = false;
      } else {
        this.startPickerVisible = false;
      }
    },
    handleEndPickerOpenChange(status) {
      if (status) {
        this.endPickerVisible = true;
        this.startPickerVisible = false;
      } else {
        this.endPickerVisible = false;
      }
    },
    handleStartPickerChange(value) {
      let start = this.$dayjs(value).format('YYYY-MM');
      if (start != this.searchForm.startDate) this.dateType = 'other';
      this.$set(this.searchForm, 'startDate', start);
      this.startPickerVisible = false;
    },
    handleEndPickerChange(value) {
      let end = this.$dayjs(value).format('YYYY-MM');
      if (end != this.searchForm.endDate) this.dateType = 'other';
      this.$set(this.searchForm, 'endDate', end);
      this.endPickerVisible = false;
    },
    handleChangeDate(type) {
      this.dateType = type;
      let start,
        end,
        now = this.$dayjs();
      if (type === 'thisYear') {
        start = now.startOf('year').format('YYYY-MM');
        end = now.format('YYYY-MM');
      } else if (type === 'lastYear') {
        start = now
          .subtract(1, 'years')
          .startOf('year')
          .format('YYYY-MM');
        end = now
          .subtract(1, 'years')
          .endOf('year')
          .format('YYYY-MM');
      } else if (type === 'pastTwelveMonths') {
        start = now
          .subtract(11, 'months')
          .startOf('month')
          .format('YYYY-MM');
        end = now
          .subtract(0, 'months')
          .endOf('month')
          .format('YYYY-MM');
      }
      this.$set(this.searchForm, 'startDate', start);
      this.$set(this.searchForm, 'endDate', end);
      this.refresh();
    },
    refresh() {
      this.$refs.statisticTarget.refresh();
      this.workflowCompletionChartContent = null;
      this.workflowTimeConsumptionChartContent = null;
      this.workflowProportionChartContent = null;
      this.getWorkflowCompletionStatistics();
      this.getWorkflowTimeConsumptionSituation();
      this.getWorkflowProportionSituation();
      this.$refs.timeoutTable.triggerRefresh();
      this.$refs.efficiencyAnalysisTable.triggerRefresh();
    },
    getWorkflowCompletionStatistics() {
      this.ajax.getWorkflowCompletionStatistics(this.searchForm).then(res => {
        if (res.success) {
          let xAxis = res.object.map(item => item.workflowName);
          let yAxis = [
            res.object.map(item => item.doneNumbers),
            res.object.map(item => item.handleNumbers)
          ];
          this.workflowCompletionChart(xAxis, yAxis);
        }
      });
    },
    getWorkflowTimeConsumptionSituation() {
      this.ajax
        .getWorkflowTimeConsumptionSituation(this.searchForm)
        .then(res => {
          if (res.success) {
            let xAxis = res.object.map(item => item.workflowName);
            let yAxis = [
              res.object.map(item =>
                item.minDuration == 0 ? null : item.minDuration
              ),
              res.object.map(item =>
                item.maxDuration == 0 ? null : item.maxDuration
              ),
              res.object.map(item =>
                item.avgDuration == 0 ? null : item.avgDuration
              )
            ];
            this.workflowTimeConsumptionChart(xAxis, yAxis);
          }
        });
    },
    handleTimeoutTableRefresh(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      this.ajax.getWorkflowTimeoutList(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        this.$refs.timeoutTable.refresh({
          ...res,
          rows: res.rows
        });
      });
    },
    getWorkflowProportionSituation() {
      this.ajax.getWorkflowProportionSituation(this.searchForm).then(res => {
        if (res.success) {
          let data = res.object.map(item => {
            return {
              name: item.workflowName,
              value: item.numbers
            };
          });
          this.workflowProportionChart(data);
        }
      });
    },
    handleEfficiencyAnalysisTableRefresh(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      this.ajax.getWorkflowEfficiencySituation(data).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.efficiencyAnalysisTable.refresh({
          ...res,
          rows
        });
      });
    },
    workflowCompletionChart(xAxis, yAxis) {
      let xLabelWordLen = 6;
      const fontSize = 12; // X轴类目文字大小
      const whiteLen = 5; // X轴类目文字两侧的留白（单侧的）大小
      if (!this.workflowCompletionChartContent) {
        this.workflowCompletionChartContent = this.$echarts.init(
          this.$refs.workflowCompletionSituation
        );
        xLabelWordLen = Math.floor(
          (this.workflowCompletionChartContent.getWidth() / xAxis.length -
            whiteLen * 2) /
            fontSize
        );
      }
      let options = {
        legend: {
          show: true,
          bottom: 0,
          selectedMode: false
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '4%',
          bottom: 60
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            show: true,
            interval: 0,
            inside: false,
            formatter: function(value) {
              let tmp = [],
                n = value.length / xLabelWordLen;
              for (let i = 0; i < n; i++) {
                tmp.push(
                  value.substring(i * xLabelWordLen, (i + 1) * xLabelWordLen)
                );
              }
              return tmp.join('\n');
            }
          }
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '已审批',
            stack: 'total',
            type: 'bar', // 类型为柱状图
            data: yAxis[0], // 数据
            barWidth: '18px', // 柱条宽度
            itemStyle: {
              color: '#337cf1'
            },
            label: {
              show: true, // 显示标签
              color: '#fff', // 标签颜色
              formatter: function(params) {
                if (params.value == 0) {
                  return '';
                }
                return params.value;
              }
            },
            labelLayout: {
              hideOverlap: true //重叠隐藏
            }
          },
          {
            name: '待审批',
            type: 'bar',
            stack: 'total',
            data: yAxis[1],
            barWidth: '18px',
            itemStyle: {
              color: '#84B7F9'
            },
            label: {
              color: '#fff', // 标签颜色
              show: true,
              formatter: function(params) {
                if (params.value == 0) {
                  return '';
                }
                return params.value;
              }
            }
          }
        ]
      };
      this.workflowCompletionChartContent.clear();
      this.workflowCompletionChartContent.resize();
      this.workflowCompletionChartContent.setOption(options);
    },
    workflowTimeConsumptionChart(xAxis, yAxis) {
      let xLabelWordLen = 6;
      const fontSize = 12; // X轴类目文字大小
      const whiteLen = 5; // X轴类目文字两侧的留白（单侧的）大小
      if (!this.workflowTimeConsumptionChartContent) {
        this.workflowTimeConsumptionChartContent = this.$echarts.init(
          this.$refs.workflowTimeConsumptionSituation
        );
        xLabelWordLen = Math.floor(
          (this.workflowTimeConsumptionChartContent.getWidth() / xAxis.length -
            whiteLen * 2) /
            fontSize
        );
      }
      const colors = ['#337cf1', '#84b7f9', '#5f9cf8'];
      const series = ['平均耗时(小时)', '最高耗时(小时)'].map((name, index) => {
        return {
          name,
          type: 'bar', // 类型为柱状图
          data: yAxis[index], // 数据
          barWidth: 25, // 柱条宽度
          barMinHeight: 15,
          itemStyle: {
            color: colors[index]
          },
          label: {
            show: true, // 显示标签
            color: '#fff', // 标签颜色
            formatter: params => {
              if (params.value == 0) {
                return '';
              }
              return params.value;
            }
          }
        };
      });
      let options = {
        legend: {
          show: true,
          bottom: 0,
          selectedMode: false
        },
        grid: {
          top: '4%',
          left: '4%',
          right: '4%',
          bottom: 60
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLabel: {
            show: true,
            interval: 0,
            inside: false,
            formatter: function(value) {
              let tmp = [],
                n = value.length / xLabelWordLen;
              for (let i = 0; i < n; i++) {
                tmp.push(
                  value.substring(i * xLabelWordLen, (i + 1) * xLabelWordLen)
                );
              }
              return tmp.join('\n');
            }
          }
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        yAxis: {
          type: 'value'
        },
        series
      };
      this.workflowTimeConsumptionChartContent.clear();
      this.workflowTimeConsumptionChartContent.resize();
      this.workflowTimeConsumptionChartContent.setOption(options);
    },
    workflowProportionChart(data) {
      if (!this.workflowProportionChartContent) {
        this.workflowProportionChartContent = this.$echarts.init(
          this.$refs.workflowProportionSituation
        );
      }
      let options = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 4,
              borderColor: '#fff',
              borderWidth: 1
            },
            label: {
              formatter: '{b}\n{d}%'
            },
            avoidLabelOverlap: true,
            data
          }
        ]
      };
      this.workflowProportionChartContent.clear();
      this.workflowProportionChartContent.resize();
      this.workflowProportionChartContent.setOption(options);
    },
    handleCheckMoreTimeoutStatistics() {
      this.$router.push('/process-efficiency/process-timeout-statistics');
    },
    handleCheckMoreEfficiencyStatistics() {
      this.$router.push('/process-efficiency/process-efficiency-statistics');
    }
  }
};
</script>

<style lang="scss" scoped>
.efficiency-analysis-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px;
  display: flex;
  flex-direction: column;
}
.contanier {
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  flex-wrap: wrap;
}
.statistic-wrap {
  display: flex;
  flex-direction: column;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  height: 280px;
}
.col-24 {
  width: 100%;
}
.col-12 {
  width: calc(50% - 4px);
}
.col-18 {
  width: calc(75% - 4px);
}
.col-6 {
  width: calc(25% - 4px);
}
.statistic-wrap .statistic-title {
  color: #333;
  font-weight: bold;
  border-bottom: 1px dashed #eee;
  padding-bottom: 8px;
  margin-bottom: 8px;
  &::before {
    content: '1';
    width: 14px;
    height: 20px;
    background: #5260ff;
    color: #5260ff;
    border-radius: 4px;
    margin-right: 8px;
  }
}
.statistic-wrap .statistic-title .more {
  font-size: 14px;
  color: #666;
  line-height: 20px;
  white-space: nowrap;
  float: right;
}
.statistic-content {
  flex: 1;
}
</style>
