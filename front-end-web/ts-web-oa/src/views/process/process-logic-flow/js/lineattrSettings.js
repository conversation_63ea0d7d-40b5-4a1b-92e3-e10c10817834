export default {
  data() {
    return {
      fieldListAll: [],
      deptTreeData: [],
      defaultExpandedKeys: [],
      symbolList: [
        {
          label: '等于',
          value: '=='
        },
        {
          label: '大于',
          value: '>'
        },
        {
          label: '小于',
          value: '<'
        },
        {
          label: '大于等于',
          value: '>='
        },
        {
          label: '不等于',
          value: '!='
        },
        {
          label: '属于',
          value: 'in'
        },
        {
          label: '包含',
          value: 'like'
        }
      ],
      btnList: [
        {
          label: '添加',
          event: this.handleAdd,
          value: ''
        },
        {
          label: '清空',
          event: this.handleClear,
          value: ''
        },
        {
          label: '并且',
          event: this.handleAdd,
          value: ' and '
        },
        {
          label: '或者',
          event: this.handleAdd,
          value: ' or '
        },
        {
          label: '(',
          event: this.handleAdd,
          value: ' ( '
        },
        {
          label: ')',
          event: this.handleAdd,
          value: ' ) '
        }
      ]
    };
  },
  methods: {
    // 分类树
    async getTree() {
      const tree = await this.ajax.getWorkFlowTree();
      this.deptTreeData = tree || [];
      this.defaultExpandedKeys = [this.deptTreeData[0].id];
    },
    // 所有字段信息获取
    async getPubVarAndFormFields() {
      let param = {
        formId: this.$props.workFlow.formId,
        wfDefinitionId: this.$props.workFlow.wfDefinitionId,
        isDeleted: 'N'
      };
      let res = await this.ajax.getPubVarAndFormFields(param);
      if (res.success) {
        this.fieldListAll = res.object || [];
      } else {
        this.$message.error(res.message || '获取失败');
      }
    },
    // 获取类型名称
    getCandidateBoxsName() {
      let obj = this.fieldListAll.find(
        e => e.variableValue == this.condition.candidateBoxsval
      );
      if (obj) {
        this.condition.candidateBoxsname = obj.variableName;
      } else {
        this.condition.candidateBoxsname = '';
      }
    },
    // 人员选择下拉加载数据
    async handleGetPersonList(data) {
      let code = JSON.parse(localStorage.getItem('values')).orgCode;
      let res = await this.ajax.selectUserListByCondition({ ...data }, code);
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    // 流程角色列表
    async getWorkFlowRoleList(data) {
      let res = await this.ajax.getWorkFlowroleList({
        ...data,
        seachName: 'Y'
      });
      if (res.success == false) {
        this.$message.error(res.message || '流程角色数据获取失败');
        return false;
      }
      return res.rows;
    },
    // 清除条件
    handleClear() {
      this.condition.conditionName = '';
      this.condition.condition = '';
      this.$forceUpdate();
    },
    // 添加调教
    handleAdd(content) {
      if (this.condition.candidateBoxsname == '') {
        this.$message.error('请选择条件');
        return;
      }
      if (this.condition.symbol == '') {
        this.$message.error('请选择条件');
        return;
      }
      let conditionName = '';
      let condition = '';
      // 添加
      if (content == '') {
        let symbol = this.symbolList.find(
          e => e.value == this.condition.symbol
        );
        let value = '';
        let text = '';
        if (this.condition.candidateBoxsval == 'L_LaunchUserCode') {
          value = this.condition.usercode || '';
          text = this.condition.username || '';
        } else if (this.condition.candidateBoxsval == 'L_LaunchUserRoleCode') {
          value = this.condition.roleCode || '';
          text = this.condition.roleName || '';
        } else if (this.condition.candidateBoxsval == 'L_LaunchDeptCode') {
          value = this.condition.deptCode || '';
          text = this.condition.deptName || '';
        } else {
          text = value = this.condition.workflowInput;
        }
        conditionName = `(${this.condition.candidateBoxsname} ${symbol.label} ${text})`;
        condition = `(${this.condition.candidateBoxsval} ${symbol.value} ${value})`;
      } else {
        conditionName = content;
        condition = content;
      }
      this.condition.conditionName =
        this.condition.conditionName + conditionName;
      this.condition.condition = this.condition.conditionName + condition;
      this.$forceUpdate();
    },
    // 部门树确定回调
    handleOk(list) {
      let id = [];
      let name = [];
      list.forEach(item => {
        id.push(item.code);
        name.push(item.name);
      });
      this.condition.deptCode = id.join(',');
      this.condition.deptName = name.join(',');
    }
  }
};
