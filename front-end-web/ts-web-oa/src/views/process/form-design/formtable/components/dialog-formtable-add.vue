<template>
  <ts-dialog
    class="dialog-formtable-add"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div slot="title" class="title">
      {{ title }}
      <div class="step">
        <ul>
          <li class="sel" :class="activeTab == 0 ? 'active' : ''">
            表单设计
            <div class="line"></div>
          </li>
          <li class="sel" :class="activeTab == 1 ? 'active' : ''">
            手机端设置
            <div class="line"></div>
          </li>
          <li class="sel" :class="activeTab == 2 ? 'active' : ''">
            打印模板设置
            <div class="line"></div>
          </li>
          <li class="sel" :class="activeTab == 3 ? 'active' : ''">
            办理查阅设置
          </li>
        </ul>
      </div>
    </div>
    <div class="components">
      <component
        :is="components[activeTab]"
        :ref="components[activeTab]"
        :form="form"
        @saveOk="close"
      ></component>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button
        @click="syncPrint"
        type="primary"
        v-if="activeTab == 0 && this.form.id"
      >
        同步打印模板
      </ts-button>
      <ts-button @click="saveFormDesignNext" type="primary">{{
        activeTab == 3 ? '保存，并设计流程' : '保存，下一步'
      }}</ts-button>
      <ts-button @click="saveFormDesign" type="primary">保 存</ts-button>
      <ts-button @click="closeConfirm">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import componentsTableSet from './components-tableSet.vue';
import componentsPhoneSet from './components-phoneSet.vue';
import componentsPrintSet from './components-printSet.vue';
import componentsQuerySet from './components-querySet.vue';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  components: {
    componentsTableSet,
    componentsPhoneSet,
    componentsPrintSet,
    componentsQuerySet
  },
  data() {
    return {
      visible: false,
      form: {},
      activeTab: 0,
      title: '',
      components: [
        'componentsTableSet',
        'componentsPhoneSet',
        'componentsPrintSet',
        'componentsQuerySet'
      ]
    };
  },
  methods: {
    async open(id, activeTab = 0, form = '') {
      if (id) {
        this.title = '编辑表单';
        let res = await this.ajax.dpFormTemplateById(id);
        if (res.success) {
          this.form = res.object;
        } else {
          this.$message.error(res.message || '表单信息获取失败');
        }
      } else {
        this.title = '新增表单';
      }
      if (form != '') {
        this.form = deepClone(form);
      }
      this.activeTab = activeTab;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.componentsTableSet && this.$refs.componentsTableSet.initT();
        this.$refs.componentsPrintSet && this.$refs.componentsPrintSet.initT();
      });
    },
    saveFormDesignNext() {
      this.$refs[this.components[this.activeTab]].saveFormDesignNext(
        this.nextStep
      );
    },
    saveFormDesign() {
      this.$refs[this.components[this.activeTab]].saveFormDesignNext();
    },
    // 同步打印模板
    syncPrint() {
      let submitData = deepClone(this.form);
      submitData.printTemplate = this.$refs[
        this.components[this.activeTab]
      ].$refs.tinymc.getData();
      this.ajax.dpFormTemplateDo(submitData, 'update').then(res => {
        if (res.success) {
          this.$message.success(res.message || '成功');
        } else {
          this.$message.error(res.message || '失败');
        }
      });
    },
    async nextStep(data) {
      let res = await this.ajax.dpFormTemplateById(data.object);
      if (res.success) {
        this.form = res.object;
      } else {
        this.$message.error(res.message || '表单信息获取失败');
      }
      if (this.activeTab == 3) {
        this.close();
        this.$emit('toProcessDesign', this.form);
      } else {
        this.activeTab++;
      }
    },
    close() {
      this.visible = false;
      this.activeTab = 0;
      this.form = {};
      this.$nextTick(() => {
        this.$refs.componentsTableSet && this.$refs.componentsTableSet.initTF();
        this.$refs.componentsPrintSet && this.$refs.componentsPrintSet.initTF();
      });
    },
    async closeConfirm() {
      try {
        await this.$confirm(`当前内容未保存，确定关闭窗口？`, '提示', {
          type: 'warning'
        });
        this.close();
      } catch (e) {
        console.error(e);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-formtable-add {
  /deep/ .el-dialog__header {
    padding: 0 24px;
  }
  /deep/ .el-dialog__body {
    padding: 0;
    width: 100%;
    height: 100%;
    margin-top: 10px;
    background: none;
  }
  /deep/ .el-dialog__footer {
    width: calc(100% - 16px);
    margin: 8px;
    padding: 0;
    background: #fff;
    padding: 5px 0;
    padding-right: 20px;
  }
  .title {
    font-size: 16px;
    display: flex;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    position: relative;
    .step {
      position: absolute;
      left: 30%;
      user-select: none;
      ul {
        .sel {
          display: inline-block;
          margin-right: 30px;
          line-height: 40px;
          position: relative;
          &::before {
            position: absolute;
            left: -25px;
            top: 10px;
            content: '';
            height: 18px;
            width: 18px;
            border: 1px solid #ccc;
            border-radius: 50%;
          }
          &.active {
            &::after {
              position: absolute;
              left: -22px;
              top: 13px;
              content: '';
              height: 12px;
              width: 12px;
              border-radius: 50%;
              background-color: #ccc;
            }
          }
          .line {
            display: inline-block;
            vertical-align: top;
            border-bottom: 1px solid #ccc;
            width: 60px;
            margin-top: 20px;
          }
        }
      }
    }
  }
  .components {
    width: calc(100% - 20px);
    margin: 5px;
    height: 100%;
  }
}
</style>
