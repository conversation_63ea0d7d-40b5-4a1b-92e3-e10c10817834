<template>
  <ts-dialog
    class="dialog-import-total-duty-address-book"
    title="导入总值班表"
    width="600px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="handleCancel"
  >
    <div class="content">
      <ts-form ref="form" :model="form">
        <ts-row>
          <div class="content-file-box">
            <span class="label">总值班表</span>
            <input
              v-show="false"
              id="fileUpload"
              ref="fileUpload"
              type="file"
              :limit="1"
              @change="handleAddFile"
              accept=".xls,.xlsx"
            />
            <ts-button @click="handleUploadFile">上传文件</ts-button>
            <div class="download-text" @click="handleDownloadTemplate">
              点击下载模版
            </div>
          </div>
          <ul class="file-list">
            <li v-for="(item, index) in file" :key="index">
              <span class="file-name">{{ item.name }}</span>
              <span
                class="red layui-icon layui-icon-close"
                @click="handleDelLocalFile"
              ></span>
            </li>
          </ul>
          <div class="err-info" v-if="errorInfo.result">
            <div class="err-title">
              提示信息：
              <p v-html="errorInfo.result"></p>
            </div>

            <div v-for="(item, index) in errorInfo.errorList" :key="index">
              错误信息{{ index + 1 }}：
              <p>{{ item.data }}</p>
            </div>
          </div>
        </ts-row>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        提 交
      </ts-button>
      <ts-button :disabled="submitLoading" @click="handleCancel">
        取 消
      </ts-button>
    </span>
  </ts-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '',
      submitLoading: false,

      form: {},
      file: [],
      errorInfo: {}
    };
  },
  methods: {
    async show() {
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        this.visible = true;
      });
    },
    // 删除本地文件记录
    handleDelLocalFile() {
      this.file = [];
      this.form.file = null;
      this.$refs.fileUpload.value = null;
    },
    // 触发上传组件
    handleUploadFile() {
      const fileUpload = this.$refs.fileUpload;
      fileUpload.value = null;
      fileUpload.click();
    },
    // 获取 文件
    handleAddFile() {
      const fileUpload = document.getElementById('fileUpload');
      if (fileUpload.files.length === 0) {
        return false;
      }
      this.file = fileUpload.files;
      this.form.file = fileUpload.files[0];
      this.errorInfo = {};
    },
    // 模版下载
    handleDownloadTemplate() {
      let a = document.createElement('a');
      a.href = `/ts-oa/api/totalDutySchedule/downloadTemplate`;
      a.click();
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        if (this.file.length === 0) {
          this.$message.warning(`请上传总值班表`);
        } else {
          var formData = new FormData();
          formData.append('file', this.form.file);
          this.submitLoading = true;
          await this.ajax.importTotalDutyAddressBook(formData).then(res => {
            this.submitLoading = false;
            if (res.success) {
              this.form = {};
              this.file = [];
              if (res.object) {
                this.errorInfo = {
                  result: res.message,
                  errorList: res.object
                };
              } else {
                this.$message.success('导入成功!');
                this.$emit('submit');
                this.handleCancel();
              }
            } else {
              this.$message.error(res.message || '导入失败');
            }
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    handleCancel() {
      this.submitLoading = false;
      this.form = {};
      this.file = [];
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-import-total-duty-address-book {
  .red {
    color: red;
  }
  .content-file-box {
    display: flex;
    align-items: center;

    .label {
      width: 124px;
      text-align: right;
      padding-right: 8px;
      position: relative;
      &::before {
        content: '*';
        color: rgb(245, 108, 108);
        margin-right: 4px;
      }
    }
    .download-text {
      color: #0000ff;
      font-size: 12px;
      cursor: pointer;
      margin-left: 8px;
    }
  }
  .file-list {
    margin: 8px 0 0 124px;
    list-style: none;

    li {
      width: 100%;
      display: flex;
      justify-content: space-between;

      > .red {
        cursor: pointer;
      }
    }
  }
  .err-info {
    color: #333;
    p {
      display: contents;
      margin: 0;
    }
    .err-title,
    .err-title p {
      font-weight: bold;
    }
  }
}
</style>
