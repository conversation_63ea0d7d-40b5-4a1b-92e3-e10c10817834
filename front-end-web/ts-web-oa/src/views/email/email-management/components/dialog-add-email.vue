<template>
  <ts-dialog
    custom-class="dialog-add-email"
    :visible.sync="visible"
    fullscreen
    :title="title"
    :appendToBody="true"
  >
    <div class="content">
      <div class="left">
        <ts-form ref="form" :model="form" labelWidth="120px">
          <ts-form-item label="收件人" prop="toId" :rules="toIdRequired">
            <div class="addressee-container">
              <BaseInputSearchPerson
                v-if="!isSendToAll"
                :inputVal.sync="form.toId"
                :inputText.sync="form.toName"
                ref="BaseInputSearchPersonToId"
                popperClassName="BaseInputSearchPersonToId"
                @input-container-click="selectInput = 'toId'"
              >
                <img
                  class="icon_users"
                  slot="suffix"
                  src="@/assets/img/icon_users.png"
                  @click="handleClicksSelectAllUser('toId')"
                />
              </BaseInputSearchPerson>

              <div v-if="!isReplyOrForward" class="switch-all-person">
                <ts-switch
                  v-model="form.sendToAll"
                  active-value="1"
                  inactive-value="0"
                  @change="() => handleInitPersonSelectForm('change')"
                />
                <span>
                  发送全院
                </span>
              </div>
            </div>
          </ts-form-item>

          <ts-form-item label="抄送人" v-if="!isSendToAll">
            <div class="addressee-container">
              <BaseInputSearchPerson
                :inputVal.sync="form.ccId"
                :inputText.sync="form.ccName"
                placeholder="抄送人"
                ref="BaseInputSearchPersonCcId"
                popperClassName="BaseInputSearchPersonCcId"
                @input-container-click="selectInput = 'ccId'"
              >
                <img
                  class="icon_users"
                  slot="suffix"
                  src="@/assets/img/icon_users.png"
                  @click="handleClicksSelectAllUser('ccId')"
                />
              </BaseInputSearchPerson>

              <div class="switch-all-person" v-if="!isReplyOrForward">
                <ts-radio-group v-model="form.firstType">
                  <ts-radio label="1">优先发送内部邮件</ts-radio>
                  <ts-radio v-if="isShowOutEmail" label="2">
                    优先发送外部邮件
                  </ts-radio>
                </ts-radio-group>
              </div>
            </div>
          </ts-form-item>

          <ts-form-item
            v-if="!isSendToAll && !isReplyOrForward"
            label="密送人"
            class="form-item-tips"
          >
            <BaseInputSearchPerson
              :inputVal.sync="form.bccId"
              :inputText.sync="form.bccName"
              placeholder="密送人"
              ref="BaseInputSearchPersonBccId"
              popperClassName="BaseInputSearchPersonBccId"
              @input-container-click="selectInput = 'bccId'"
            >
              <img
                class="icon_users"
                slot="suffix"
                src="@/assets/img/icon_users.png"
                @click="handleClicksSelectAllUser('bccId')"
              />
            </BaseInputSearchPerson>
          </ts-form-item>

          <ts-form-item
            label="外部邮箱地址"
            prop="outEmailAddress"
            :rules="rules.outEmailAddress"
            v-if="isShowOutEmail && !isReplyOrForward"
          >
            <ts-input
              v-model="form.outEmailAddress"
              type="textarea"
              class="out-email-address"
              maxlength="500"
              placeholder="<EMAIL>,<EMAIL>,<EMAIL>"
              show-word-limit
            />
          </ts-form-item>

          <ts-form-item label="主题" prop="subject" :rules="rules.required">
            <ts-input
              v-model="form.subject"
              placeholder="请输入主题"
              maxlength="30"
            >
            </ts-input>
          </ts-form-item>

          <ts-form-item label="附件">
            <table-module-upload
              ref="EmailUpload"
              v-model="form.uploadedFile"
              drag
              moduleName="email"
              dragTips="点击上传或将文件拖拽到此处"
              @upload-success="handleUploadSuccess"
            />
          </ts-form-item>

          <ts-form-item class="tynimce-content-container" label="内容">
            <Tynimce
              v-if="visible"
              ref="tyni"
              moduleName="email"
              style="width: 100%;height: 100%;"
              :content.sync="form.content"
            />
          </ts-form-item>

          <ts-form-item
            label="定时发送"
            class="time"
            prop="startTiming"
            :rules="startTimingRequired"
            v-if="!isReplyOrForward"
          >
            <ts-switch
              v-model="form.timing"
              active-value="1"
              inactive-value="0"
            />
            <ts-date-picker
              v-if="form.timing === '1'"
              v-model="form.startTiming"
              class="at-regular-time"
              :disabledDate="disabledDate"
              format="YYYY-MM-DD HH:mm"
              valueFormat="YYYY-MM-DD HH:mm"
              show-time
            />
          </ts-form-item>
        </ts-form>
      </div>

      <div class="right">
        <component-frequent-contacts
          v-if="!isSendToAll"
          :dataList="recentContacts"
          @emit-click="handleClickFrequentContacts"
        />
      </div>
    </div>

    <template slot="footer">
      <div class="footer-container flex-space">
        <div class="flex-center">
          <ts-checkbox
            v-model="form.saveToOutbox"
            true-label="on"
            false-label="off"
            label="保存到发件箱"
          />
          <ts-checkbox
            v-model="form.sendToWx"
            true-label="on"
            false-label="off"
            label="微信提醒"
          />
          <ts-checkbox
            v-model="form.stateLevelBox"
            true-label="on"
            false-label="off"
            label="紧急"
          />
          <ts-checkbox
            v-model="form.readReceiptBox"
            true-label="on"
            false-label="off"
            label="回执"
            @change="handleChangeReadReceipt"
          />

          <span class="tips">
            温馨提醒：仅发送内部邮件支持微信提醒、紧急、回执功能
          </span>
        </div>
        <div>
          <ts-button type="primary" @click="handleSubmit">发送</ts-button>
          <ts-button
            v-if="!isReplyOrForward"
            type="primary"
            @click="handleDraft"
          >
            存草稿
          </ts-button>
          <ts-button @click="close">取消</ts-button>
        </div>
      </div>
    </template>
    <ts-homs-select-person ref="tsHomsSelectPerson" @ok="handleSelectUserOk" />
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import moment from 'moment';
import Tynimce from '@/components/tinymce/index';
import TableModuleUpload from '@/components/table-module-upload';
import componentFrequentContacts from './component-frequent-contacts.vue';

function validatorFunction(rule, data, callback) {
  function emailVerify(value) {
    const emailArray = value.split(',');
    const emailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    const invalidEmail = emailArray.find(email => !emailReg.test(email.trim()));
    if (invalidEmail) {
      return `${invalidEmail}的邮箱格式错误，请重新输入！`;
    }

    return false;
  }

  if (!data) {
    callback();
    return;
  }
  let result = emailVerify(data);
  if (!result) {
    callback();
    return;
  }
  callback(new Error(result));
}

export default {
  components: {
    TableModuleUpload,
    Tynimce,
    componentFrequentContacts
  },

  data() {
    return {
      visible: false,
      title: '',
      emailSign: '',
      // 签名前缀
      siginPrefix:
        "<br><br><br><hr style='margin: 0 0 10px 0;border: 0;border-bottom: 1px solid #E4E5E6;width: 50px;'>",
      disabledDate: current => {
        return current && current < moment();
      },

      recentContacts: [], // 常用联系人
      selectInput: 'toId', // 上次点击选人输入框
      parentStoreInfo: null,

      // add 写邮件
      // draftEdit 邮件编辑
      type: '',
      form: {},
      echoData: null,
      rules: {
        required: { required: true, message: '必填' },
        outEmailAddress: {
          required: false,
          trigger: 'blur',
          validator: validatorFunction
        }
      },

      keyMapping: {
        toId: 'BaseInputSearchPersonToId',
        ccId: 'BaseInputSearchPersonCcId',
        bccId: 'BaseInputSearchPersonBccId'
      }
    };
  },
  computed: {
    isSendToAll() {
      return this.form.sendToAll === '1';
    },
    isReplyOrForward() {
      return ['reply', 'replyAll', 'forward', 'informationForward'].includes(
        this.type
      );
    },
    isShowOutEmail() {
      return (
        this.parentStoreInfo &&
        this.parentStoreInfo.globalSetting?.orgCode !== 'cssdeshfly'
      );
    },
    toIdRequired() {
      return { required: this.isReplyOrForward, message: '必填' };
    },
    startTimingRequired() {
      return { required: this.form.timing === '1', message: '必填' };
    }
  },
  methods: {
    async open({ title, type, echoData }) {
      this.title = title;
      this.type = type;
      this.echoData = deepClone(echoData);
      this.parentStoreInfo = this.$getParentStoreInfo();

      this.visible = true;
      await this.handleGetEmpSignimg();
      await this.handleGetEmailContact();
      // InputSearchInit
      this.handleInputSearchInit();

      this.handleInitEmailDataAndEchoData();
      this.$nextTick(() => {
        // 回显人员
        if (type === 'edit' && !this.isSendToAll) {
          this.$refs.BaseInputSearchPersonToId.stringToListRender();
          this.$refs.BaseInputSearchPersonCcId.stringToListRender();
          this.$refs.BaseInputSearchPersonBccId.stringToListRender();
        }
      });
      document.addEventListener('keydown', this.handleKeyDown);
      document.addEventListener('click', this.handleGlobalClick);
    },

    handleInputSearchInit() {
      this.$refs.BaseInputSearchPersonToId?.init();
      this.$refs.BaseInputSearchPersonCcId?.init();
      this.$refs.BaseInputSearchPersonBccId?.init();
    },
    handleKeyDown(event) {
      const { keyCode } = event;
      const selectedRef = this.$refs[this.keyMapping[this.selectInput]];

      if (
        document.activeElement.classList.contains('text-search-input') &&
        this.selectInput &&
        keyCode === 8 &&
        selectedRef?.getInputValue().length === 0
      ) {
        selectedRef.handleBackspaceDelete();
      }
    },

    handleGlobalClick(event) {
      const popperElement = document.querySelector(
        '.base-input-search-person-popper'
      );

      if (
        popperElement &&
        !popperElement.contains(event.target) &&
        this.$refs[this.keyMapping[this.selectInput]]
      ) {
        this.$refs[this.keyMapping[this.selectInput]].visible = false;
      }
    },

    handleInitEmailDataAndEchoData() {
      let renderFile = false;
      // 初始化form
      this.handleInitForm();

      // 写邮件
      if (this.type === 'add') {
        const sigin = this.emailSign
          ? `${this.siginPrefix}${this.emailSign}`
          : '';
        this.$set(this.form, 'content', sigin);
      } else {
        // 编辑 草稿 转发 回复
        this.echoData.stateLevelBox =
          this.echoData.stateLevel == '2' ? 'on' : 'off';
        this.echoData.readReceiptBox =
          this.echoData.readReceipt == '1' ? 'on' : 'off';
        this.echoData.saveToOutbox = 'on';
        this.echoData.sendToWx = 'on';

        let {
          statusId,
          attachmentList,
          stateLevelBox,
          readReceiptBox,
          senderName,
          senderId,
          toId,
          toName,
          ccId,
          ccName,
          subject,
          content,
          forWordInformationId
        } = this.echoData;

        // 编辑 发件箱
        // 编辑 草稿
        if (this.type === 'edit') {
          this.$set(this, 'form', deepClone(this.echoData));
          renderFile = true;
        }

        // 回复
        if (this.isReplyOrForward) {
          this.$set(this.form, 'replyId', statusId);
          this.$set(this.form, 'attachmentList', attachmentList);
          this.$set(this.form, 'content', this.handleSetLastEmailInfo());
          this.$set(this.form, 'stateLevelBox', stateLevelBox);
          this.$set(this.form, 'readReceiptBox', readReceiptBox);
          this.$set(this.form, 'sendToAll', '0');
          this.$set(this.form, 'timing', '0');
          this.$set(this.form, 'subject', '回复：' + subject);
          renderFile = true;

          if (this.type === 'reply') {
            this.$set(this.form, 'toName', senderName);
            this.$set(this.form, 'toId', senderId);
          }

          if (this.type === 'replyAll') {
            let result = this.processEmailData(
              senderId,
              senderName,
              toId,
              toName,
              ccId,
              ccName
            );

            this.$set(this.form, 'toName', result.name);
            this.$set(this.form, 'toId', result.id);
          }

          if (this.type === 'forward') {
            this.$set(this.form, 'subject', '转发：' + subject);
          }

          if (this.type === 'informationForward') {
            this.$set(this.form, 'subject', `文章推荐-《${subject}》`);
            this.$set(this.form, 'content', content);
            this.$set(
              this.form,
              'forwardType',
              `information-${forWordInformationId}`
            );

            this.$set(this.form, 'informationForward', forWordInformationId);
            renderFile = false;
          }
        }
      }
      this.$nextTick(() => {
        renderFile && this.handleRenderEmailFile();
        this.$refs.form?.clearValidate();
      });
    },

    processEmailData(senderId, senderName, toId, toName, ccId, ccName) {
      // 将所有数据直接拆分成数组
      let idList = [...toId.split(','), ...ccId.split(','), senderId];
      let nameList = [...toName.split(','), ...ccName.split(','), senderName];

      // 使用 Map 去重并保持顺序
      let uniqueIdNameMap = new Map(
        idList.map((id, index) => [id, nameList[index]])
      );

      return {
        id: Array.from(uniqueIdNameMap.keys()).join(','),
        name: Array.from(uniqueIdNameMap.values()).join(',')
      };
    },

    // 回复 回复全部 转发
    // 回显原始邮件
    handleSetLastEmailInfo() {
      let {
        senderName,
        postTime,
        toName,
        ccName,
        subject,
        content
      } = this.echoData;
      let contentStr = `
      <br><br><br>
      <div style="background-color:#f2f2f2;padding:15px;margin-bottom:10px;border-radius: 5px;">
        <p style="color:#999999;">---------------原始邮件---------------</p>
        <p style="color:#333333;"><span style="color:#999999;margin-right:5px;">发件人:</span>${senderName}</p>
        <p style="color:#333333;"><span style="color:#999999;margin-right:5px;">发送时间:</span>${postTime}</p>
        <p style="color:#333333;"><span style="color:#999999;margin-right:5px;">收件人:</span>${toName}</p>
        ${
          ccName
            ? `<p style="color:#333333;"><span style="color:#999999;margin-right:5px;">抄送人:</span>${ccName}</p>`
            : ''
        }
        <p style="color:#333333;"><span style="color:#999999;margin-right:5px;">主题:</span>${subject}</p>
        ${
          content
            ? `<p style="color:#333333;">
              ${content.replace('padding:15px;', '')}</p>`
            : ''
        }
      </div>
    `;

      return contentStr;
    },

    handleRenderEmailFile() {
      if (
        Array.isArray(this.form.attachmentList) &&
        this.form.attachmentList.length
      ) {
        let fileList = this.form.attachmentList.map(m => {
          return {
            name: m.originalName,
            size: m.fileSize,
            fileId: m.id,
            status: 1
          };
        });
        this.$refs.EmailUpload.handlePushFileData(fileList);
      }
    },

    handleInitForm() {
      const defaultForm = {
        sendToAll: '0',
        outEmailAddress: '',
        subject: '',
        uploadedFile: '',
        content: '',
        saveToOutbox: 'on',
        sendToWx: 'on',
        stateLevelBox: 'off',
        readReceiptBox: 'off',
        timing: '0',
        startTiming: ''
      };
      Object.keys(defaultForm).forEach(key => {
        this.$set(this.form, key, defaultForm[key]);
      });
      this.handleInitPersonSelectForm('render');
    },

    handleInitPersonSelectForm(type) {
      const defaultForm = {
        toName: '',
        toId: '',
        firstType: '1',
        ccName: '',
        ccId: '',
        bccName: '',
        bccId: ''
      };
      Object.keys(defaultForm).forEach(key => {
        this.$set(this.form, key, defaultForm[key]);
      });

      // change事件 是v-if 需要单独this.$nextTick 再init组件
      if (type === 'change' && !this.isSendToAll) {
        this.$nextTick(() => {
          this.handleInputSearchInit();
        });
      }
    },

    handleUploadSuccess(fileList) {
      if (Array.isArray(fileList) && fileList.length && !this.form.subject) {
        this.$set(this.form, 'subject', fileList[0].fileName);
      }
    },

    // 存草稿
    async handleDraft() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) return;

      let submitData = this.handleGetSubmitFormData();
      let API = null;
      if (submitData.id) {
        API = async () => this.ajax.optimizeEmailInternalUpdate(submitData);
      } else {
        submitData.isDraft = 1;
        API = async () =>
          this.ajax.optimizeEmailInternalSendEmailInternal(
            submitData,
            '?isDraft=1'
          );
      }

      const res = await API();
      if (!res.success) {
        this.$newMessage('error', res.message || '操作失败!');
        return;
      }
      this.$message.success('操作成功!');
      this.$parent.tabState = '3';
      this.$emit('refresh');
      this.close();
    },

    async handleSubmit() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) return;

      let submitData = this.handleGetSubmitFormData();
      submitData.isDraft = 0;
      if (!submitData.toName && !submitData.outEmailAddress) {
        this.$newMessage('warning', '收件人和外部邮箱必须有一项不能为空!');
        return;
      }

      const res = await this.ajax.optimizeEmailInternalSendEmailInternal(
        submitData
      );
      if (!res.success) {
        this.$newMessage('error', res.message || '操作失败!');
        return;
      }
      this.$message.success(res.object || '操作成功!');
      if (this.type !== 'informationForward') {
        this.$parent.tabState = submitData.timing == '1' ? '3' : '2';
      }
      this.$emit('refresh');
      this.close();
    },

    handleGetSubmitFormData() {
      const submitData = deepClone(this.form);
      if (submitData.sendToAll === '1') submitData.toName = '全院人员';
      submitData.stateLevel = submitData.stateLevelBox === 'on' ? '2' : '0';
      submitData.readReceipt = submitData.readReceiptBox === 'on' ? '1' : '0';

      return submitData;
    },

    handleClicksSelectAllUser(key) {
      const keyMapping = {
        toId: {
          echoData: { toId: this.form.toId },
          submitKeys: {
            dept: ['', ''],
            group: ['', ''],
            emp: ['toName', 'toId']
          }
        },
        ccId: {
          echoData: { ccId: this.form.ccId },
          submitKeys: {
            dept: ['', ''],
            group: ['', ''],
            emp: ['ccName', 'ccId']
          }
        },
        bccId: {
          echoData: { bccId: this.form.bccId },
          submitKeys: {
            dept: ['', ''],
            group: ['', ''],
            emp: ['bccName', 'bccId']
          }
        }
      };
      const { echoData, submitKeys } = keyMapping[key] || {};
      this.$refs.tsHomsSelectPerson.open(key, {
        showOrganizationCheck: false,
        showGroupCheck: false,
        isRadio: false,
        echoData,
        submitKeys
      });
    },

    handleSelectUserOk(result, key) {
      if (result[key]) {
        const { empList } = result[key];
        let userList = empList.map(m => {
          return {
            empCode: m.empCode,
            empName: m.empName
          };
        });

        this.$refs[this.keyMapping[key]].renderUser(userList);
      }
      this.$forceUpdate();
    },

    // 获取个性签名
    async handleGetEmpSignimg() {
      try {
        let res = await this.ajax.getEmployeeSelectEmpSignimg();
        if (!res.success) {
          this.$newMessage('error', res.message || '获取个性签名失败!');
          return '';
        }

        if (res.object != null && res.object != '') {
          this.emailSign = res.object.replace(/\n/g, '<br/>');
        } else {
          this.emailSign = '';
        }
      } catch (error) {
        this.emailSign = '';
        console.error(error);
      }
    },

    //常用联系人
    async handleGetEmailContact() {
      try {
        this.recentContacts = [];
        let res = await this.ajax.selectEmailContactByUsercode();
        if (!res.success) {
          this.$newMessage('error', res.message || '获取邮箱常用联系人失败!');
          return;
        }

        this.recentContacts = (res.object || []).map(item => {
          let showName = item.employeeName + '-' + item.deptName;
          return {
            ...item,
            showName
          };
        });
      } catch (error) {
        console.error(error);
      }
    },

    // 回执
    async handleChangeReadReceipt(val) {
      var toEmailselUserCodeArr = (this.form.toId || '').split(',');
      if (
        val === 'on' &&
        (toEmailselUserCodeArr.length > 10 || '1' == this.form.sendToAll)
      ) {
        try {
          await this.$confirm(
            `当前发送对象较多，您确定要接收所有收件人的邮件回执吗？`,
            '提示',
            {
              showClose: true,
              type: 'warning',
              dangerouslyUseHTMLString: true,
              customClass: 'new-el-message_box',
              cancelButtonClass: 'shallowButton'
            }
          );
          this.$set(this.form, 'readReceiptBox', 'on');
        } catch (e) {
          this.$set(this.form, 'readReceiptBox', 'off');
          console.error(e);
        }
      }
    },

    handleClickFrequentContacts(e) {
      if (e.showName.includes('-外部邮箱')) {
        const outEmailAddress = this.form.outEmailAddress || '';
        if (outEmailAddress) {
          const outEmailAddressArr = outEmailAddress.split(',');
          if (!outEmailAddressArr.includes(e.employeeName)) {
            this.form.outEmailAddress = `${outEmailAddress},${e.employeeName}`;
          } else {
            this.$newMessage('warning', '已添加至外部邮箱地址!');
          }
        } else {
          this.$set(this.form, 'outEmailAddress', e.employeeName);
        }
      } else {
        const keyObject = {
          toId: 'toName',
          ccId: 'ccName',
          bccId: 'bccName'
        };
        let userKeys = keyObject[this.selectInput];
        if (this.form[this.selectInput]) {
          const ids = this.form[this.selectInput].split(',');
          if (ids.includes(e.employeeNo)) {
            this.$newMessage('warning', '已添加该人员!');
            return;
          }

          let newKeyNos = `${this.form[this.selectInput]},${e.employeeNo}`;
          let newKeyNames = `${this.form[userKeys]},${e.employeeName}`;
          this.$set(this.form, this.selectInput, newKeyNos);
          this.$set(this.form, [userKeys], newKeyNames);
        } else {
          this.$set(this.form, this.selectInput, e.employeeNo);
          this.$set(this.form, [userKeys], e.employeeName);
        }
        this.$nextTick(() => {
          this.$refs[this.keyMapping[this.selectInput]].stringToListRender();
        });
      }
    },

    close() {
      this.handleInitForm();
      this.handleInputSearchInit();

      document.removeEventListener('keydown', this.handleKeyDown);
      document.removeEventListener('click', this.handleGlobalClick);
      this.$nextTick(() => {
        this.$refs.EmailUpload.clearFileList();
      });
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-add-email {
    .icon_users {
      cursor: pointer;
      margin-top: 5px;
      width: 16px;
      height: 16px;
    }

    .el-dialog__footer {
      width: 100% !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      background-color: #fff;
      border-top: 1px solid #eee !important;

      .footer-container {
        padding: 0 45px;
        .tips {
          margin-left: 10px;
          color: #000;
        }
      }
    }

    .el-dialog__body {
      width: 100% !important;
      height: calc(100vh - 88px) !important;
      max-height: calc(100vh - 88px) !important;
      padding: 0px !important;
      margin: 0px !important;
      background: #fff !important;

      .content {
        width: 100%;
        height: 100%;
        display: flex;
        padding: 8px;
        overflow: auto;

        .left {
          flex: 1;
          display: flex;
          flex-direction: column;
          margin-right: 16px;
          .time {
            .ant-calendar-time-picker-select {
              width: 50% !important;
              &:last-child {
                display: none !important;
              }
            }
          }

          .at-regular-time {
            margin-left: 8px;
          }

          .tynimce-content-container {
            height: 350px;
            background: #fff;
            .el-form-item__content {
              height: 100%;
            }
          }

          .out-email-address {
            .el-textarea__inner {
              min-height: 65px !important;
              max-height: 65px !important;
            }
          }

          .addressee-container {
            display: flex;
            flex-direction: column;

            .switch-all-person {
              margin-top: 8px;
            }
          }
        }
        .right {
          width: 280px;
          position: relative;
        }
      }
    }
  }
}
</style>
