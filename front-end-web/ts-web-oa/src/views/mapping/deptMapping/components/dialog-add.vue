<template>
  <vxe-modal
    className="dialog-add"
    width="80%"
    height="80%"
    :title="title"
    v-model="visible"
    showFooter
  >
    <template #default>
      <div class="trasen-container">
        <div class="platform-dept-container">
          <new-base-search-tree
            class="node-tree-plat"
            ref="searchPlatDeptTree"
            :apiFunction="ajax.getTreeWithMapping"
            placeholder="输入科室名称"
            title="平台科室"
            :params="{ syscode: syscode }"
            @beforeClick="clickPlatDeptItemTree"
          />
        </div>
        <div class="bus-dept-container">
          <new-base-search-tree
            class="node-tree-bus"
            ref="searchBusDeptTree"
            :apiFunction="ajax.getZTree"
            placeholder="输入科室名称"
            title="映射科室"
            :showTitle="false"
            :params="{ syscode: syscode, isMatched: isMatched }"
            showCheckbox
            @nodeCheck="checkBusDeptItemTree"
            @treeLoaded="setSearchCheckedNodes"
            @setSearchCheckedNodes="setSearchCheckedNodes"
          >
            <template #actionBar>
              <ts-form
                ref="form"
                labelWidth="65px"
                style="margin: 0px 8px;height: 30px;"
              >
                <ts-form-item
                  label="业务系统"
                  prop="syscode"
                  style="text-align: left;"
                >
                  <ts-select
                    style="width: 100%"
                    v-model="syscode"
                    @change="handleSyscodeChange"
                  >
                    <ts-option
                      v-for="(item, index) in syscodeList"
                      :key="index"
                      :label="item.itemName"
                      :value="item.itemCode"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-form>
              <ts-form
                ref="form"
                labelWidth="65px"
                style="margin: 0px 8px;height: 30px;padding-top: 3px;"
                v-if="type === 'add'"
              >
                <ts-form-item
                  label="映射科室"
                  prop="isMatched"
                  style="text-align: left;"
                >
                  <ts-select
                    style="width: 100%"
                    v-model="isMatched"
                    @change="handleIsMatchedChange"
                  >
                    <ts-option
                      v-for="(item, index) in isMatchedList"
                      :key="index"
                      :label="item.itemName"
                      :value="item.itemCode"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </ts-form>
            </template>
          </new-base-search-tree>
        </div>
        <div class="right-table-container">
          <TsVxeTemplateTable
            id="table_dept_mapping"
            ref="table"
            :columns="columns"
            disabled-row-field="isDeleted"
            disabled-row-value="Y"
            :hasPage="false"
            @refresh="handleRefreshTable"
          />
        </div>
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">提 交</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  name: 'DiaLogAdd',
  props: {},
  data() {
    return {
      syscodeList: this.getDicData(),
      isMatchedList: [
        { itemCode: '1', itemName: '已映射' },
        { itemCode: '0', itemName: '未映射' }
      ],
      visible: false,
      showBusDeptWrap: true,
      title: '',
      type: '',
      syscode: '',
      isMatched: '',
      platCurrentNode: {}, // 平台科室当前选中节点，用于编辑时回显
      form: {},
      data: {}, // 用于编辑时的数据
      rows: [],
      columns: [
        // {
        //   label: '序号',
        //   prop: 'index',
        //   align: 'center',
        //   width: 120
        // },
        {
          label: '平台科室名称',
          prop: 'oaDeptName',
          align: 'center',
          minWidth: 200
        },
        {
          label: '业务系统',
          prop: 'syscode',
          align: 'center',
          minWidth: 120
        },
        {
          label: '映射科室名称',
          prop: 'deptMappingChildList',
          align: 'center',
          minWidth: 400,
          render: (h, { row }) => {
            if (
              row.deptMappingChildList &&
              row.deptMappingChildList.length > 0
            ) {
              return h(
                'div',
                {},
                row.deptMappingChildList.map(item => item.hisDeptName).join(',')
              );
            }
          }
        }
      ],
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    open(opt = {}) {
      let { title, type, data } = opt;
      this.title = title;
      this.type = type;
      //清空之前的数据
      this.$set(this, 'form', {});
      this.rows = [];

      if (this.type === 'edit') {
        this.data = deepClone(data);
        this.rows.push({
          id: data.id,
          oaDeptId: data.oaDeptId,
          oaDeptName: data.oaDeptName,
          syscode: data.syscode,
          deptMappingChildList: data.deptMappingChildList || []
        });
        this.platCurrentNode = { id: data.oaDeptId, name: data.oaDeptName };
        //如果columns节点最后一个是操作列，则删除操作列
        if (this.columns[this.columns.length - 1].prop === 'actions') {
          this.columns.pop();
        }
      } else {
        if (this.columns[this.columns.length - 1].prop !== 'actions') {
          //新增时列表操作列显示
          this.columns.push({
            label: '操作',
            align: 'center',
            width: 120,
            headerSlots: 'action',
            prop: 'actions',
            fixed: 'right',
            render: (h, { row }) => {
              //编辑时不显示删除按钮
              if (this.type === 'edit') {
                return h('div');
              }
              let actionList = [
                {
                  label: '删除',
                  event: this.handleDelete
                }
              ];
              return h('BaseActionCell', {
                on: { 'action-select': event => event(row) },
                attrs: { actions: actionList }
              });
            }
          });
        }
      }
      this.syscode = data.syscode; // 初始化 syscode 为 data.syscode
      this.isMatched = data.isMatched; // 初始化 isMatched 为 data.isMatched

      this.getDicData();
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
        this.handleRefreshTable();
        if (
          this.$refs.searchBusDeptTree.treeClass ||
          this.$refs.searchPlatDeptTree.treeClass
        ) {
          this.$refs.searchBusDeptTree.treeClass.checkAllNodes(false);
          this.$refs.searchPlatDeptTree.treeClass.cancelSelectedNode();
          this.$refs.searchPlatDeptTree.getTreeData({ syscode: this.syscode });
          this.$refs.searchBusDeptTree.getTreeData({ syscode: this.syscode });
          //将搜索框清空
          this.$refs.searchBusDeptTree.searchVal = '';
          this.$refs.searchPlatDeptTree.searchVal = '';
        }
      });
    },

    setSearchCheckedNodes() {
      //获取左侧树的选中节点
      let platCurrentNodeList = this.$refs.searchPlatDeptTree.treeClass
        ? this.$refs.searchPlatDeptTree.treeClass.getSelectedNodes()
        : [];
      let platCurrentNode =
        this.type === 'edit'
          ? this.platCurrentNode
          : platCurrentNodeList.length > 0
          ? platCurrentNodeList[0]
          : {};
      //从rows中找到该节点，然后将该节点的deptMappingChildList的数据勾选到树节点上
      let index = this.rows.findIndex(
        item =>
          item.oaDeptId === platCurrentNode.id && item.syscode === this.syscode
      );
      if (index !== -1) {
        let deptMappingChildList = this.rows[index].deptMappingChildList;
        let deptCheckedNodes = [];
        if (deptMappingChildList.length > 0) {
          for (let i = 0; i < deptMappingChildList.length; i++) {
            deptCheckedNodes.push({
              id: deptMappingChildList[i].hisDeptId,
              name: deptMappingChildList[i].hisDeptName
            });
          }
        }
        setTimeout(() => {
          if (deptCheckedNodes.lenth === 0) {
            this.$refs.searchBusDeptTree.treeClass.checkAllNodes(false);
          } else {
            this.$refs.searchBusDeptTree.$refs.tsTree.setCheckedNodes(
              deptCheckedNodes,
              false
            );
          }
        }, 1000);
      }
    },

    async getDicData() {
      return this.ajax.getDataByDataLibrary('BUSINESS_SYSTEM').then(res => {
        if (res.success) {
          this.syscodeList = res.object || [];
        }
        this.showBusDeptWrap = true;
      });
    },

    //点击平台科室树节点事件
    async clickPlatDeptItemTree(node) {
      //如果是编辑则点击不做任何操作
      if (this.type === 'edit') {
        this.$refs.searchPlatDeptTree.treeClass.cancelSelectedNode();
        return;
      }
      //如果有则清空右侧映射科室树的数据
      this.$refs.searchBusDeptTree.treeClass.checkAllNodes(false);
      //先判断列表是否有该节点，如果有则不添加
      let index = this.rows.findIndex(
        item => item.oaDeptId === node.id && item.syscode === this.syscode
      );
      if (index === -1) {
        let oaDeptName = node.name;
        oaDeptName = oaDeptName.replace(' (已映射)', '');
        this.rows.push({
          oaDeptId: node.id,
          oaDeptName: oaDeptName,
          syscode: this.syscode,
          deptMappingChildList: []
        });
      } else {
        //右侧映射科室渲染已经勾选的数据
        let deptMappingChildList = this.rows[index].deptMappingChildList;
        if (deptMappingChildList.length > 0) {
          let deptCheckedNodes = [];
          for (let i = 0; i < deptMappingChildList.length; i++) {
            deptCheckedNodes.push({
              id: deptMappingChildList[i].hisDeptId,
              name: deptMappingChildList[i].hisDeptName
            });
          }
          this.$refs.searchBusDeptTree.$refs.tsTree.setCheckedNodes(
            deptCheckedNodes,
            false
          );
        }
      }
    },

    //点击映射科室树节点事件 treeNode-为点击的当前节点
    async checkBusDeptItemTree(node, treeNode) {
      //找到左边树的节点，如果没有则新加一个节点
      let platCurrentNodeList = this.$refs.searchPlatDeptTree.treeClass.getSelectedNodes();
      let platCurrentNode =
        this.type === 'edit'
          ? this.platCurrentNode
          : platCurrentNodeList.length > 0
          ? platCurrentNodeList[0]
          : {};
      //判断节点是否为空
      if (Object.keys(platCurrentNode).length === 0) {
        this.$newMessage('warning', '请先选择平台科室!');
        this.$refs.searchBusDeptTree.treeClass.checkAllNodes(false);
        return;
      }
      //遍历组装映射子类数组数据
      let deptMappingChildList = [];
      if (node.length > 0) {
        for (let i = 0; i < node.length; i++) {
          let nodeName = node[i].name;
          nodeName = nodeName.replace(/_.*/, '');
          deptMappingChildList.push({
            hisDeptId: node[i].id,
            hisDeptName: nodeName
          });
        }
      }
      if (Object.keys(platCurrentNode).length === 0) {
        this.rows.push({
          oaDeptId: null,
          oaDeptName: null,
          syscode: this.syscode,
          deptMappingChildList: deptMappingChildList
        });
      } else {
        //判断rows中是否存在oaDeptId并且syscode等于this.syscode的节点，如果存在则直接push，如果不存在则新增一个节点
        let index = this.rows.findIndex(
          item =>
            item.oaDeptId === platCurrentNode.id &&
            item.syscode === this.syscode
        );
        if (index === -1) {
          this.rows.push({
            oaDeptId: platCurrentNode.id,
            oaDeptName: platCurrentNode.name,
            syscode: this.syscode,
            deptMappingChildList: deptMappingChildList
          });
        } else {
          let deptMappingChildList_ = this.rows[index].deptMappingChildList;
          //判断deptMappingChildList_中是否存在点击的节点，如果存在则删除，如果不存在则添加
          //判断当前节点是否选中
          if (treeNode.checked) {
            let nodeTreeName = treeNode.name;
            nodeTreeName = nodeTreeName.replace(/_.*/, '');
            let index_ = deptMappingChildList_.findIndex(
              item => item.hisDeptId === treeNode.id
            );
            if (index_ === -1) {
              deptMappingChildList_.push({
                hisDeptId: treeNode.id,
                hisDeptName: nodeTreeName
              });
            }
          } else {
            //取消选择就在deptMappingChildList_中删除
            let index_ = deptMappingChildList_.findIndex(
              item => item.hisDeptId === treeNode.id
            );
            if (index_ !== -1) {
              deptMappingChildList_.splice(index_, 1);
            }
          }
          this.rows[index].deptMappingChildList = deptMappingChildList_;
        }
      }
    },

    handleIsMatchedChange(item) {
      this.isMatched = item;
      this.$refs.searchBusDeptTree.getTreeData({
        syscode: this.syscode,
        isMatched: this.isMatched
      });
    },

    handleSyscodeChange(item) {
      this.syscode = item;
      this.$refs.searchBusDeptTree.getTreeData({
        syscode: this.syscode,
        isMatched: this.isMatched
      });
    },

    handleDelete(data) {
      this.$confirm('确定要移除该科室映射吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          //根据ID删除rows中的数据
          let index = this.rows.findIndex(item => item.id === data.id);
          if (index !== -1) {
            this.rows.splice(index, 1);
            //取消映射科室树的勾选
            this.$refs.searchBusDeptTree.treeClass.checkAllNodes(false);
          }
        })
        .catch(() => {});
    },

    async handleRefreshTable() {
      this.$refs.table.refresh({
        rows: this.rows
      });
    },

    async submit() {
      //验证列表数据是否完整
      let rows = this.rows;
      if (rows.length === 0) {
        this.$newMessage('warning', '请添加映射科室关系!');
        return;
      }
      for (let i = 0; i < rows.length; i++) {
        if (
          rows[i].oaDeptId === null ||
          rows[i].oaDeptId === undefined ||
          rows[i].oaDeptId === '' ||
          rows[i].deptMappingChildList.length === 0 ||
          rows[i].deptMappingChildList === undefined ||
          rows[i].deptMappingChildList === null
        ) {
          this.$newMessage('warning', '请确保映射关系完整!');
          return;
        }
      }

      let API = null;
      let apiParams = null;
      if (this.type === 'add') {
        API = this.ajax.saveDeptMappingData;
        apiParams = this.rows;
      } else {
        API = this.ajax.updateDeptMappingData;
        apiParams = this.rows[0];
      }
      const res = await API(apiParams);

      if (!res.success) {
        this.$newMessage('error', res.message || '操作失败!');
        return;
      }
      this.$newMessage('success', '操作成功!');
      this.$emit('refresh');
      this.close();
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
  .trasen-container {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 8px 8px 0 8px;
    display: flex;
    overflow: hidden;
    .platform-dept-container,
    .bus-dept-container {
      width: 248px;
      margin-right: 8px;
      padding: 8px 0px 0px;
      background: #fff !important;
      overflow: hidden;
      height: 100%;
      position: relative;
      flex-direction: column;
      .search-tree-box {
        border: 1px solid rgb(235, 235, 240);
      }
    }
    .right-table-container {
      ::v-deep {
        .more-text-btn {
          width: 140px;
          > span {
            max-width: 140px !important;
          }
        }

        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        padding: 8px 8px 0 8px;
        background: #fff !important;
        .form-table {
          background: #fff !important;

          .primary-span {
            color: $primary-blue;
            cursor: pointer;
          }
          .red {
            color: red !important;
            color: $primary-blue;
          }
        }
      }
    }
  }
}
</style>
