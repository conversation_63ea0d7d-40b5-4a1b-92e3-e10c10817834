<template>
  <div class="fund-expenditure-manage">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-add
      :civilSubjectCodeOption="civilSubjectCodeOption"
      :civilEntryCodeOption="civilEntryCodeOption"
      ref="DialogAdd"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import table from './mixins/table';
import DialogAdd from './components/dialog-add.vue';
export default {
  mixins: [table],
  components: {
    DialogAdd
  },
  data() {
    return {
      civilSubjectCodeOption: [],
      civilEntryCodeOption: []
    };
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    refresh() {
      this.$nextTick(async () => {
        await this.ajax.getDictionaries(
          'civil_entry_code',
          this,
          'civilEntryCodeOption'
        );
        await this.ajax.getDictionaries(
          'civil_subject_code',
          this,
          'civilSubjectCodeOption'
        );

        this.searchList.find(
          f => f.value == 'entryCode'
        ).childNodeList = this.civilEntryCodeOption.map(m => {
          return {
            ...m,
            label: m.itemName,
            value: m.itemNameValue,
            element: 'ts-option'
          };
        });

        this.handleRefreshTable();
      });
    },

    handleEdit(row) {
      this.$refs.DialogAdd.open({
        type: 'edit',
        title: '编辑',
        row
      });
    },

    handleAdd() {
      this.$refs.DialogAdd.open({
        type: 'add',
        title: '新增'
      });
    },

    async handleDelete(row) {
      try {
        await this.$confirm('是否删除该条数据?', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.CivilAffairsFundingExpenditureDelete(
          row.id
        );
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { date = [] } = this.searchForm,
        [expendDateBegin = '', expendDateEnd = ''] = date,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          expendDateBegin,
          expendDateEnd,
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.date;

      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });

      let res = await this.ajax.CivilAffairsFundingExpenditureList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.fund-expenditure-manage {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      .operation-span {
        color: $primary-blue;
        margin-right: 8px;
        cursor: pointer;
      }
      .red {
        color: red !important;
      }
    }
  }
}
</style>
