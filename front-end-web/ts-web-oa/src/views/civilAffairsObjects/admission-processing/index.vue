<template>
  <div class="admission-processing-container">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="resetData"
    >
      <template slot="right">
        <ts-button type="primary" @click="this.handleReadIDCard">
          读卡
        </ts-button>
        <ts-button type="primary" @click="this.handleAdd">
          新增
        </ts-button>
      </template>
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-add-edit-active-form
      ref="DialogAddEditActiveForm"
      @refresh="handleRefreshTable"
    />

    <dialog-be-hospitalized-info
      ref="DialogBeHospitalizedInfo"
      @refresh="handleRefreshTable"
    />
  </div>
</template>

<script>
import table from './mixins/table';
import readCardMixins from '@/views/civilAffairsObjects/configMixins/readCardMixins.js';

import DialogBeHospitalizedInfo from './components/dialog-be-hospitalized-info.vue';
import DialogAddEditActiveForm from './components/dialog-add-edit-active-form.vue';
export default {
  mixins: [table, readCardMixins],
  components: {
    DialogBeHospitalizedInfo,
    DialogAddEditActiveForm
  },
  async created() {
    await this.ajax.getDictionaries(
      'civil_admission_ward',
      this,
      'civilAdmissionWardOptions'
    );

    this.searchList.find(
      f => f.value == 'ward'
    ).childNodeList = this.civilAdmissionWardOptions.map(m => {
      return {
        label: m.itemName,
        value: m.itemNameValue,
        element: 'ts-option'
      };
    });
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    handleAdd() {
      this.$refs.DialogAddEditActiveForm.open({
        activeTabsInfo: this.objectTabRefs.civil_affairs_personnel_info,
        type: 'add',
        title: '新增'
      });
    },
    handleEdit(row) {
      this.$refs.DialogBeHospitalizedInfo.open({
        type: 'edit',
        title: '编辑入院办理',
        row
      });
    },

    handleDetails(row) {
      this.$refs.DialogBeHospitalizedInfo.open({
        type: 'details',
        title: '详情',
        row
      });
    },

    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { date = [] } = this.searchForm,
        [inDateBeging = '', inDateEnd = ''] = date,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          inDateBeging,
          inDateEnd,
          sidx: 'create_date  desc,name',
          sord: 'desc'
        };
      delete searchForm.createDate;

      if (
        searchForm.objectTypeAttribute &&
        searchForm.objectTypeAttribute.length
      ) {
        searchForm.objectTypeAttribute = searchForm.objectTypeAttribute.join(
          ','
        );
      }
      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });
      let res = await this.ajax.CivilAffairsPersonnelInfoList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    async handleDelete(row) {
      let str = `是否删除<div>姓名为：${row.name}</div>身份证为：${row.identityNumber}的对象信息`;
      try {
        await this.$confirm(str, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        const res = await this.ajax.CivilAffairsPersonnelInfoDelete(
          row.personnelId
        );
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.admission-processing-container {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      .details-span {
        color: $primary-blue;
        margin-right: 8px;
        cursor: pointer;
      }

      .red {
        color: red !important;
      }
    }
  }
}
</style>
