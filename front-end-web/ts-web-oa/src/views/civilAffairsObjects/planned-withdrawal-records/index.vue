<template>
  <div class="fund-budget-manage">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
      <template slot="right">
        <ts-button type="primary" @click="this.handleReadIDCard">
          读卡
        </ts-button>
        <ts-button type="primary" @click="this.handleAdd">
          新增
        </ts-button>
      </template>
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      sidx="create_date"
      v-loading="loading"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-add ref="DialogAdd" @refresh="handleRefreshTable" />
  </div>
</template>

<script>
import table from './mixins/table';
import readCardMixins from '@/views/civilAffairsObjects/configMixins/readCardMixins.js';

import DialogAdd from './components/dialog-add.vue';
export default {
  mixins: [table, readCardMixins],
  components: {
    DialogAdd
  },
  data() {
    return {};
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    refresh() {
      this.$nextTick(async () => {
        this.handleRefreshTable();
      });
    },

    handleEdit(row) {
      this.$refs.DialogAdd.open({
        type: 'edit',
        title: '编辑',
        row
      });
    },

    handleAdd() {
      this.$refs.DialogAdd.open({
        type: 'add',
        title: '新增'
      });
    },

    async handleDelete(row) {
      try {
        await this.$confirm('是否删除该条数据?', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.civilAffairsWithdrawalPlanDelete(row.id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },
    /**@desc 还原驼峰命名 */
    camelToKebab(string) {
      if (string == 'a.create_time') {
        return 'create_date';
      }
      return string.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: this.camelToKebab(this.$refs.table.sidx),
          sord: this.$refs.table.sord
        };

      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });

      let res = await this.ajax.civilAffairsWithdrawalPlanList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.fund-budget-manage {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      .operation-span {
        color: $primary-blue;
        margin-right: 8px;
        cursor: pointer;
      }
      .red {
        color: red !important;
      }
    }
  }
}
</style>
