<template>
  <ts-dialog
    class="dialog-add-driver"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="800"
    @close="close"
  >
    <div class="content">
      <ts-form ref="ruleForm" :model="form" labelWidth="120px">
        <ts-form-item
          label="司机图片:"
          prop="driverPicture"
          :rules="rules.required"
        >
          <base-upload
            ref="driverPicture"
            v-model="form.driverPicture"
            :limit="1"
          />
        </ts-form-item>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="司机姓名:"
              prop="driverCode"
              :rules="rules.required"
            >
              <base-select
                style="width: 100%"
                v-model="form.driverCode"
                :inputText.sync="form.driverName"
                :loadMethod="handleGetPersonList"
                label="empName"
                value="empCode"
                searchInputName="empName"
                :clearable="false"
                @select="handlePersonSelect"
              ></base-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item label="身份证号码:">
              <ts-input
                v-model="form.driverIdcard"
                @change="handleChangeGetAge"
                placeholder="请输入"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item label="性别:">
              <ts-select
                style="width: 100%"
                v-model="form.driverSex"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of sexTypeList"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemNameValue"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item label="年龄:">
              <ts-input
                v-model="form.driverAge"
                :maxlength="10"
                placeholder="请输入"
              /> </ts-form-item
          ></ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item label="手机号码:">
              <ts-input
                v-model="form.driverPhone"
                :maxlength="11"
                placeholder="请输入"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item label="科室:">
              <ts-ztree-select
                ref="deptSelect"
                :data="options.deptTreeData"
                :inpText.sync="form.driverDeptName"
                :inpVal.sync="form.driverDept"
                @onCreated="handleExpand"
                placeholder="请选择所属科室"
              ></ts-ztree-select>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="准驾车型:"
              prop="driverQuasi"
              :rules="rules.required"
            >
              <ts-select
                style="width: 100%"
                v-model="form.driverQuasi"
                clearable
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of vehicleModelList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="初次领证日期:"
              prop="driverFirstDate"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                v-model="form.driverFirstDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="驾驶证有效期:"
              prop="driverEffective"
              :rules="rules.required"
            >
              <el-date-picker
                style="width: 100%"
                value-format="yyyy-MM-dd"
                v-model="form.driverEffective"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="实习期至:"
              prop="internshipDate"
              :rules="rules.required"
            >
              <ts-date-picker
                style="width: 100%"
                v-model="form.internshipDate"
                valueFormat="YYYY-MM-DD"
                placeholder="请选择"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="备注:">
          <ts-input
            v-model="form.driverRemark"
            type="textarea"
            class="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件:">
          <base-upload ref="driverFiles" v-model="form.driverFiles" />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import BaseSelect from '@/components/base-select/index.vue';
import personApi from '@/components/ts-user-dept-select/api.js';
import {
  vehicleDriverSave,
  vehicleDriverUpdate
} from '@/api/ajax/driver/index.js';
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  components: {
    BaseSelect
  },
  props: {
    show: {
      type: Boolean
    },
    vehicleModelList: {
      type: Array
    },
    eachData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '',

      options: {
        deptTreeData: []
      },

      rules: {
        required: { required: true, message: '必填' },
        iphone: [
          { required: true, message: '必填' },
          {
            trigger: ['blur', 'change'],
            message: '联系方式格式有误',
            validator: (prop, value, cb) => {
              let reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/;
              if (value && !reg.test(value)) {
                cb('false');
                return;
              }
              cb();
            }
          }
        ],
        driverIdcard: [
          { required: true, message: '必填' },
          {
            required: true,
            message: '请输入正确的身份证号码格式',
            trigger: ['blur', 'change'],
            validator: (prop, value, callback) => {
              let trimVal = String(value).trim();

              let reg = /((1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5]|8[1-3])\d{4})(\d{4})(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])(\d{3}([0-9xX]))/;

              if (!reg.test(trimVal)) {
                callback('请输入正确格式');
              } else {
                callback();
              }
            }
          }
        ]
      },

      form: {
        driverName: '',
        driverPicture: '',
        driverIdcard: '',
        driverSex: '',
        driverAge: '',
        driverPhone: '',
        driverDept: '',
        driverQuasi: '',
        driverFirstDate: '',
        driverEffective: [],
        internshipDate: '',
        driverRemark: '',
        driverFiles: ''
      },
      sexTypeList: [
        {
          itemName: '男',
          itemNameValue: '男'
        },
        {
          itemName: '女',
          itemNameValue: '女'
        },
        {
          itemName: '其他',
          itemNameValue: '其他'
        }
      ]
    };
  },
  watch: {
    show: {
      async handler(val) {
        this.form = {
          driverEffective: []
        };
        if (val) {
          this.handleGetDeptTreeData();

          if (JSON.stringify(this.eachData) !== '{}') {
            let date = [
              this.eachData.driverEffectiveStart,
              this.eachData.driverEffectiveEnd
            ];

            this.form = Object.assign({}, this.eachData);
            delete this.form.driverEffectiveStart;
            delete this.form.driverEffectiveEnd;

            this.title = '编辑';
            this.type = 'edit';
            this.$set(this.form, 'driverEffective', date);
          } else {
            this.title = '新增';
            this.type = 'add';
          }
        }

        this.$nextTick(() => {
          this.$refs.ruleForm?.clearValidate();
        });
        this.visible = val;
      }
    }
  },
  methods: {
    async handleGetPersonList(data) {
      let res = await personApi.getEmployeeList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    handlePersonSelect(e) {
      const {
        empAge = '',
        empPhone = '',
        empDeptId = '',
        empDeptName = '',
        empIdcard = '',
        empSex = ''
      } = e;

      if (!empAge) {
        this.$set(this.form, 'driverAge', getAgeFromIdCard(empIdcard));
        function getAgeFromIdCard(idCardNumber) {
          var birthYear = parseInt(idCardNumber.substr(6, 4));
          var birthMonth = parseInt(idCardNumber.substr(10, 2)) - 1; // 月份从0开始，需要减去1
          var birthDay = parseInt(idCardNumber.substr(12, 2));
          var today = new Date();
          var age = today.getFullYear() - birthYear;
          if (
            today.getMonth() < birthMonth ||
            (today.getMonth() === birthMonth && today.getDate() < birthDay)
          ) {
            age--;
          }
          return age;
        }
      } else {
        this.$set(this.form, 'driverAge', empAge);
      }
      this.$set(this.form, 'driverSex', empSex === 0 ? '男' : '女');
      this.$set(this.form, 'driverIdcard', empIdcard);
      this.$set(this.form, 'driverPhone', empPhone);
      this.$set(this.form, 'driverDeptName', empDeptName);
      this.$set(this.form, 'driverDept', empDeptId);
      this.$refs.deptSelect.input();
    },
    handleGetDeptTreeData() {
      this.ajax.getTree().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '机构数据获取失败');
          return;
        }
        this.$set(this.options, 'deptTreeData', res.object);
      });
    },
    /**@desc 展开树到指定层数 */
    handleExpand(treeObj) {
      if (this.$refs.deptSelect.searchVal != this.form.driverDeptName) {
        treeObj.expandAll(true);
      } else {
        let nodes = treeObj.getNodes();
        nodes &&
          nodes.map(node => {
            treeObj.expandNode(node, true);
          });
      }
    },

    handleChangeGetAge(idCard) {
      let age = getAgeFromIdCard(idCard);
      if (isNaN(age)) {
        return;
      } else {
        this.$set(this.form, 'driverAge', age);
      }

      function getAgeFromIdCard(idCard) {
        var birthday = idCard.substr(6, 8);
        var year = birthday.substr(0, 4);
        var month = birthday.substr(4, 2);
        var day = birthday.substr(6, 2);
        var age = new Date().getFullYear() - parseInt(year);

        // 如果当前月份小于出生月份，或者当前月份等于出生月份但是当前日期小于出生日期，则年龄减一
        if (
          new Date().getMonth() + 1 < parseInt(month) ||
          (new Date().getMonth() + 1 === parseInt(month) &&
            new Date().getDate() < parseInt(day))
        ) {
          age--;
        }

        return age;
      }
    },

    async submit() {
      let validate = await this.$refs.ruleForm.validate().catch(res => res);
      if (!validate) {
        return;
      }

      const data = Object.assign({}, this.form);
      data.driverEffectiveStart = data.driverEffective[0];
      data.driverEffectiveEnd = data.driverEffective[1];
      delete data.driverEffective;

      if (
        !this.$dayjs(data.driverFirstDate).isBefore(data.driverEffectiveStart)
      ) {
        this.$message.warning('驾驶证有效期开始时间 不能 大于初次领证日期!');
        return false;
      }

      if (!this.$dayjs(data.driverFirstDate).isBefore(data.internshipDate)) {
        this.$message.warning('实习期时间不能大于初次领证日期!');
        return false;
      }

      let API = null;
      if (this.type === 'add') {
        API = vehicleDriverSave;
      } else {
        API = vehicleDriverUpdate;
      }
      const submitres = await API(data);

      if (!submitres.success) {
        this.$message.error(submitres.message || '操作失败');
        return;
      }
      this.close();
      this.$message.success('操作成功!');
      this.$emit('refreshTable');
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss">
.dialog-add-driver {
  .ts-dialog {
    width: 800px;
  }
}
</style>

<style lang="scss" scoped>
.dialog-add-driver {
  .content {
    ::v-deep {
      .suffix-input {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .person-icon {
        margin-top: 3px;
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .textarea {
        .el-textarea__inner {
          min-height: 110px !important;
          max-height: 200px !important;
        }
      }
    }
  }
}
</style>
