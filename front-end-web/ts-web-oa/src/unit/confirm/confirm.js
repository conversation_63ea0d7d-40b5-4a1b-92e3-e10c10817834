class Confirm {
  constructor() {
    this.close = this.close.bind(this);
    this.verify = this.verify.bind(this);
    this.down = this.down.bind(this);
    this.up = this.up.bind(this);
    this.move = this.move.bind(this);
  }

  html(parameter) {
    var box = null;
    var modal = null;
    var title = null;
    var title_text = null;
    var title_btn = null;
    var title_close = null;
    var body = null;
    var body_icon = null;
    var body_icon_i = null;
    var body_content = null;
    var body_footer = null;
    var cancel = null;
    var cancel_span = null;
    var verify = null;
    var verify_span = null;
    var txt_span = null;
    var obj = {};

    if (typeof parameter === 'string') {
      obj.message = parameter;
    } else {
      obj = parameter;
    }

    title_text = document.createElement('div');
    title_text.className = 'flex';
    title_text.innerHTML = obj.title || '消息提示';
    title_text.setAttribute(
      'style',
      'padding-left: 10px !important; cursor: move;'
    );
    this.dragDom = title_text;
    title_text.addEventListener('mousedown', this.down, false);
    title_close = document.createElement('span');
    title_close.className = 'iconfont icon-close transition__all_3s font18';
    title_close.setAttribute('title', '关闭');
    // title_close.onclick = this.close
    title_close.addEventListener('click', this.close, false);
    title_btn = document.createElement('div');
    title_btn.className = 'btns';
    title_btn.appendChild(title_close);
    // 顶部区域
    title = document.createElement('div');
    title.className = 'm-dialay-right__title flexbox';
    title.setAttribute('style', 'padding-left: 0 !important;');
    title.appendChild(title_text);
    title.appendChild(title_btn);

    // 图标
    body_icon = document.createElement('div');
    body_icon.className = 'vxe-modal--status-wrapper';
    body_icon_i = document.createElement('i');
    body_icon_i.className = obj.icon || 'iconfont icon-jingshimian font16';
    body_icon.appendChild(body_icon_i);
    // 内容
    body_content = document.createElement('div');
    body_content.className = 'vxe-modal--content';
    body_content.style.maxHeight = '500px';
    body_content.innerHTML = obj.message || '';
    // 中间内容区域
    body = document.createElement('div');
    body.className = 'vxe-modal--body m-dialay-confirm__body';
    body.appendChild(body_icon);
    body.appendChild(body_content);

    // 确认按钮
    verify = document.createElement('button');
    verify.setAttribute('type', 'button');
    verify.className =
      'vxe-button type--button size--small theme--primary confirm-button';
    verify_span = document.createElement('span');
    verify_span.className = 'vxe-button--content';
    // verify_span.innerHTML = ' 确认'
    verify.appendChild(verify_span);
    body_icon_i = document.createElement('i');
    body_icon_i.className = 'iconfont icon-fenli';
    // verify.onclick = this.verify
    verify.addEventListener('click', this.verify, false);
    verify_span.appendChild(body_icon_i);
    txt_span = document.createElement('span');
    txt_span.innerHTML = ' 确认';
    verify_span.appendChild(txt_span);

    // 取消按钮
    cancel = document.createElement('button');
    cancel.setAttribute('type', 'button');
    cancel.className =
      'vxe-button type--button size--small theme--primary confirm-button';
    cancel_span = document.createElement('span');
    cancel_span.className = 'vxe-button--content';
    // cancel_span.innerHTML = ' 关闭'
    cancel.appendChild(cancel_span);
    body_icon_i = document.createElement('i');
    body_icon_i.className = 'iconfont icon-tuichu';
    // cancel.onclick = this.close
    cancel.addEventListener('click', this.close, false);
    cancel_span.appendChild(body_icon_i);
    txt_span = document.createElement('span');
    txt_span.innerHTML = ' 关闭';
    cancel_span.appendChild(txt_span);

    // 按钮区域
    body_footer = document.createElement('div');
    body_footer.className = 'm-dialay-right__button';
    body_footer.appendChild(verify);
    body_footer.appendChild(cancel);

    // 内容外框
    modal = document.createElement('div');
    modal.className = 'vxe-modal--box';
    modal.appendChild(title);
    modal.appendChild(body);
    modal.appendChild(body_footer);

    // 背景层 is--active
    box = document.createElement('div');
    box.className =
      'vxe-modal--wrapper type--confirm status--question is--animat lock--view is--mask is--visible is--active';
    box.appendChild(modal);

    box.addEventListener('mousemove', this.move, false);
    box.addEventListener('mouseup', this.up, false);

    return box;
  }

  confirm(parameter, resolve, reject) {
    let body = document.getElementsByClassName('qiankun-child');
    if (!body || body.length == 0) {
      body = document.querySelector('body');
    } else {
      body = body[0];
    }
    var winW = body.clientWidth;
    var winH = body.clientHeight;
    var w = 0;
    var h = 0;
    this.resolve = resolve;
    this.reject = reject;
    this.dom = this.html(parameter);
    body.appendChild(this.dom);
    w = this.dom.childNodes[0].offsetWidth;
    h = this.dom.childNodes[0].offsetHeight;
    this.dom.childNodes[0].style.left = (winW - w) / 2 + 'px';
    this.dom.childNodes[0].style.top = (winH - h) / 2 + 'px';
  }

  close(e) {
    e.stopPropagation();
    e.preventDefault();
    this.dom.remove();
    this.resolve('cancel');
  }

  verify() {
    this.dom.remove();
    this.resolve('confirm');
  }

  move(e) {
    if (this.moveType === true) {
      this.dom.childNodes[0].style.left = e.x - this.x - 14 + 'px';
      this.dom.childNodes[0].style.top = e.y - this.y + 'px';
    }
  }

  up() {
    this.moveType = false;
  }

  down(e) {
    this.moveType = true;
    this.x = e.offsetX;
    this.y = e.offsetY;
  }
}

export default new Confirm();
