<template>
  <div class="content flex-col-center flex-column">
    <div class="title flex-center">累计{{ totalHanding }}个工单正在处理</div>
    <div class="line"></div>
    <div class="table flex-grow">
      <div class="table-title table-row flex">
        <template v-for="(item, index) of columns">
          <div
            class="cell"
            :key="index"
            :style="{
              textAlign: item.align || 'center',
              width: item.width ? item.width + 'px' : 'auto'
            }"
          >
            {{ item.name }}
          </div>
        </template>
      </div>
      <div v-if="handingOrderList.length">
        <template v-for="(item, index) of handingOrderList">
          <div class="table-row flex table-data flex-col-center" :key="index">
            <template v-for="(prop, propIndex) of columns">
              <div
                class="cell"
                :key="propIndex"
                :style="{
                  textAlign: prop.align || 'center',
                  width: prop.width ? prop.width + 'px' : 'auto'
                }"
                :class="prop.class ? prop.class(item[prop.prop], item) : ''"
              >
                {{
                  prop.formatter
                    ? prop.formatter(item[prop.prop], item)
                    : item[prop.prop]
                }}
              </div>
            </template>
          </div>
        </template>
      </div>
      <div v-else class="empty-box flex-center flex-column">
        <img
          :src="require('@/assets/img/workSheet/largeScreen/empty-img.png')"
        />
        很优秀，工单都处理完了
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    totalHanding: {
      type: Number,
      default: () => 0
    },
    handingOrderList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      columns: [
        {
          name: '报修科室',
          prop: 'repairManDeptName',
          width: 168
        },
        {
          name: '问题描述',
          prop: 'faultDeion',
          align: 'left',
          width: 423
        },
        {
          name: '优先级',
          prop: 'faultEmergencyValue',
          width: 72,
          class: function(cell, row) {
            return {
              2: 'warning',
              1: 'error',
              3: ''
            }[row.faultEmergency];
          },
          formatter: function(cell, row) {
            return {
              2: '比较急',
              1: '紧急',
              3: '常规'
            }[row.faultEmergency];
          }
        },
        {
          name: '工单状态',
          prop: 'workStatusValue',
          width: 72,
          class: function(cell, row) {
            return row.workStatus < 3 ? 'error' : '';
          }
        },
        {
          name: '维修人员',
          prop: 'fkUserName',
          width: 96
        },
        {
          name: '报修时间',
          prop: 'createTime',
          align: 'right',
          width: 117
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 1020px;
  height: 774px;
  padding-top: 12px;
}
.line {
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );
  border-radius: 50%;
  margin-top: 4px;
  margin-bottom: 14px;
}
.title {
  font-weight: 600;
  color: #ffffff;
  line-height: 32px;
  font-size: 24px;
}
.table {
  width: 100%;
  .cell {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    flex-shrink: 0;
    &:nth-child(2) {
      margin-right: 4px;
    }
    &:not(:nth-child(2)):not(:last-child) {
      margin-right: 8px;
    }
  }
}
.table-row {
  padding-left: 17px;
  padding-right: 20px;
  &.table-data {
    height: 56px;
    &:nth-child(2n) {
      background-color: #162d59;
    }
    .cell {
      font-weight: 600;
      color: #ffffff;
      line-height: 33px;
      font-size: 24px;

      &.error {
        color: #ff6068;
      }
      &.warning {
        color: #d1851c;
      }
      &:last-child {
        font-size: 20px;
      }
    }
  }
}
.table-title {
  margin-bottom: 9px;
  div {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.5);
    line-height: 25px;
    font-size: 18px;
  }
}
.empty-box {
  img {
    width: 280px;
    height: 106px;
    margin-bottom: 16px;
  }
  font-size: 20px;
  color: #fff;
  line-height: 28px;
  height: calc(100% - 34px);
}
</style>
