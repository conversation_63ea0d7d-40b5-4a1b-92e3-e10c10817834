/**
 * charm补间动画库，pixijs适用
 */
export default class Charm {
  constructor() {
    // if (renderingEngine === undefined)
    // throw new Error('在使用charm.js之前，请在构造函数中指定一个渲染引擎');
    // 找出正在使用的渲染引擎（默认为Pixi）
    // this.renderer = '';
    // 如果渲染引擎是Pixi，请设置Pixi对象别名
    // if (renderingEngine.particles.ParticleContainer && renderingEngine.Sprite) {
    //   this.renderer = 'pixi';
    // }
    // 存储全局补间的数组
    this.globalTweens = [];
    // 存储所有缓动公式的对象
    // Linear：精灵从开始到停止保持匀速运动。
    // Smoothstep，smoothstepSquared，smoothstepCubed。加速精灵并以非常自然的方式减慢速度。
    // acceleration， accelerationCubed。逐渐加速精灵并突然停止。如果要更加平滑的加速效果，请使用 sine，sineSquared 或 sineCubed。
    // Deceleration：deceleration，decelerationCubed。突然加速精灵并逐渐减慢它。要获得更加平滑的减速效果，请使用inverseSine，inverseSineSquared或inverseSineCubed。
    // Bounce：bounce 10 -10 ，这将使精灵到达起点和终点时略微反弹，更改乘数10和 -10，可以改变效果。
    this.easingFormulas = {
      // Linear线性：匀速运动
      linear(x) {
        return x;
      },
      // Smoothstep 平顺的，光滑的
      smoothstep(x) {
        return x * x * (3 - 2 * x);
      },
      // 加速精灵并以非常自然的方式减慢速度
      smoothstepSquared(x) {
        return Math.pow(x * x * (3 - 2 * x), 2);
      },
      // 加速精灵并以非常自然的方式减慢速度
      smoothstepCubed(x) {
        return Math.pow(x * x * (3 - 2 * x), 3);
      },
      // Acceleration逐渐加速精灵并突然停止。
      acceleration(x) {
        return x * x;
      },
      // 逐渐加速精灵并突然停止。
      accelerationCubed(x) {
        return Math.pow(x * x, 3);
      },
      // Deceleration突然加速精灵并逐渐减慢它
      deceleration(x) {
        return 1 - Math.pow(1 - x, 2);
      },
      // 突然加速精灵并逐渐减慢它
      decelerationCubed(x) {
        return 1 - Math.pow(1 - x, 3);
      },
      // Sine
      sine(x) {
        return Math.sin((x * Math.PI) / 2);
      },
      sineSquared(x) {
        return Math.pow(Math.sin((x * Math.PI) / 2), 2);
      },
      sineCubed(x) {
        return Math.pow(Math.sin((x * Math.PI) / 2), 2);
      },
      inverseSine(x) {
        return 1 - Math.sin(((1 - x) * Math.PI) / 2);
      },
      inverseSineSquared(x) {
        return 1 - Math.pow(Math.sin(((1 - x) * Math.PI) / 2), 2);
      },
      inverseSineCubed(x) {
        return 1 - Math.pow(Math.sin(((1 - x) * Math.PI) / 2), 3);
      },
      // Spline
      spline(t, p0, p1, p2, p3) {
        return (
          0.5 *
          (2 * p1 +
            (-p0 + p2) * t +
            (2 * p0 - 5 * p1 + 4 * p2 - p3) * t * t +
            (-p0 + 3 * p1 - 3 * p2 + p3) * t * t * t)
        );
      },
      // Bezier curve
      cubicBezier(t, a, b, c, d) {
        const t2 = t * t;
        const t3 = t2 * t;
        return (
          a +
          (-a * 3 + t * (3 * a - a * t)) * t +
          (3 * b + t * (-6 * b + b * 3 * t)) * t +
          (c * 3 - c * 3 * t) * t2 +
          d * t3
        );
      }
    };
    // 添加scaleX和scaleY属性到Pixi精灵
    this._addScaleProperties = sprite => {
      // if (this.renderer === 'pixi') {
      if (!('scaleX' in sprite) && 'scale' in sprite && 'x' in sprite.scale) {
        Object.defineProperty(sprite, 'scaleX', {
          get() {
            return sprite.scale.x;
          },
          set(value) {
            sprite.scale.x = value;
          }
        });
      }
      if (!('scaleY' in sprite) && 'scale' in sprite && 'y' in sprite.scale) {
        Object.defineProperty(sprite, 'scaleY', {
          get() {
            return sprite.scale.y;
          },
          set(value) {
            sprite.scale.y = value;
          }
        });
      }
      // }
    };
  }

  // tweenProperty函数被用作高级补间方法的基础。
  /**
   * 底层的tweenProperty函数被用作高级补间方法的基础。
   * @param sprite 精灵对象
   * @param property 精灵的属性字符，例如：x,y,alpha
   * @param startValue 属性初始值
   * @param endValue 属性结束值
   * @param totalFrames 初始值到结束值所执行的帧数（动画时长）
   * @param type 缓动类型
   * @param yoyo 是否(初始值-结束值)来回循环
   * @param loop 是否从结束后一直循环 （例如旋转360°以后继续旋转360°）
   * @param delayBeforeRepeat yoyo 之前的延迟时间
   * @returns 补间对象
   */
  tweenProperty(
    sprite,
    property,
    startValue,
    endValue,
    totalFrames,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0
  ) {
    // 创建补间对象
    const o = {};
    // 如果补间是一个弹跳类型(样条)，设置开始和结束的幅度值
    const typeArray = type.split(' ');
    if (typeArray[0] === 'bounce') {
      o.startMagnitude = parseInt(typeArray[1]);
      o.endMagnitude = parseInt(typeArray[2]);
    }

    // 使用当前端点值创建一个新的补间
    o.start = (startValue, endValue) => {
      // 克隆开始和结束值，以便任何对精灵属性的可能引用都转换为普通数字
      o.startValue = JSON.parse(JSON.stringify(startValue));
      o.endValue = JSON.parse(JSON.stringify(endValue));
      o.playing = true;
      o.totalFrames = totalFrames;
      o.frameCounter = 0;
      // o.loopCounter = 0;

      // 将补间添加到全局补间数组中。补间数组会在每一帧上更新
      this.globalTweens.push(o);
    };

    // 调用start，并开始补间
    o.start(startValue, endValue);

    // update方法将被游戏循环在每一帧上调用。这就是使补间移动的原因
    o.update = () => {
      let curvedTime;
      if (o.playing) {
        // 如果经过的帧数少于总帧数，可以使用渐变公式移动精灵
        if (o.frameCounter < o.totalFrames) {
          // 当前帧数与设置的总帧数之比
          const normalizedTime = o.frameCounter / o.totalFrames;
          // 从easingFormulas对象的缓动函数库中选择正确的缓动函数
          // 如果不是弹跳类型，使用普通的缓动函数
          if (typeArray[0] !== 'bounce') {
            curvedTime = this.easingFormulas[type](normalizedTime);
          } else {
            // 如果它是一个弹跳类型，使用spline函数并应用两个额外的“type”数组值作为起始点和结束点
            curvedTime = this.easingFormulas.spline(
              normalizedTime,
              o.startMagnitude,
              0,
              1,
              o.endMagnitude
            );
          }
          // 根据缓动类型函数返回的值，来计算动画曲线值，来插入到精灵的具体属性，从而实现动画
          if (property == 'skewY') {
            sprite.skew.y =
              o.endValue * curvedTime + o.startValue * (1 - curvedTime);
          } else if (property == 'skewX') {
            sprite.skew.x =
              o.endValue * curvedTime + o.startValue * (1 - curvedTime);
          } else {
            sprite[property] =
              o.endValue * curvedTime + o.startValue * (1 - curvedTime);
          }
          // 当前帧数+1
          o.frameCounter += 1;
        } else {
          // 当补间结束时，运行结束任务
          if (property == 'skewY') {
            sprite.skew.y = o.endValue;
          } else if (property == 'skewX') {
            sprite.skew.x = o.endValue;
          } else {
            sprite[property] = o.endValue;
          }
          o.end();
        }
      }
    };
    // end方法将在补间完成时被调用
    o.end = () => {
      // 设置playing为false
      o.playing = false;
      // 调用补间的onComplete方法，如果它已经被分配
      if (o.onComplete) o.onComplete();

      // 从全局补间数组中移除该补间对象
      this.globalTweens.splice(this.globalTweens.indexOf(o), 1);

      // 如果补间的yoyo属性为true，创建一个新的补间
      // 使用相同的值，但是使用当前补间的startValue作为下一个补间的endValue
      if (yoyo) {
        this.wait(delayBeforeRepeat).then(() => {
          o.start(o.endValue, o.startValue);
        });
      }

      if (loop) {
        // o.loopCounter += 1;
        this.wait(delayBeforeRepeat).then(() => {
          o.start(o.startValue, o.endValue);
        });
      }
    };
    // 暂停和执行方法
    o.play = () => {
      o.playing = true;
    };
    o.pause = () => {
      o.playing = false;
    };
    // 返回补间对象
    return o;
  }

  /**
   * makeTween是一种用于制作复杂补间的方法。使用多个tweenProperty函数。
   * @param tweensToAdd 包含多个tweenProperty方法可以调用的数组: 二维数组: [[],[]...]
   * @returns 补间数组对象
   */
  makeTween(tweensToAdd) {
    // 创建一个对象来管理补间
    const o = {};
    // 创建一个tweens数组来存储新的tweens
    o.tweens = [];

    // 为每个数组创建一个新的补间
    tweensToAdd.forEach(tweenPropertyArguments => {
      // 使用传入进来的补间数组的每一项创建一个新的补间
      const newTween = this.tweenProperty(...tweenPropertyArguments);

      // 将新的补间放进补间数组中
      o.tweens.push(newTween);
    });

    // 添加一个计数器来跟踪完成动作的补间的数量
    let completionCounter = 0;

    // 每次其中一个补间完成将调用o.completed
    o.completed = () => {
      // 计数器+1
      completionCounter += 1;

      // 如果所有补间都完成了，调用用户定义的'onComplete'方法，重置completionCounter
      if (completionCounter === o.tweens.length) {
        if (o.onComplete) o.onComplete();
        completionCounter = 0;
      }
    };

    // 添加'onComplete'方法到所有补间
    o.tweens.forEach(tween => {
      tween.onComplete = () => o.completed();
    });

    // 添加暂停和播放方法来控制所有补间
    o.pause = () => {
      o.tweens.forEach(tween => {
        tween.playing = false;
      });
    };
    o.play = () => {
      o.tweens.forEach(tween => {
        tween.playing = true;
      });
    };

    // 返回补间集合
    return o;
  }

  /* 高级补间方法 */
  // ------------------------------------ 1. 简单类 ------------------------------------
  /**
   * 淡出动画
   * @param sprite 精灵
   * @param endAlpha 最终呈现的透明度的值
   * @param frames 执行帧数
   * @param type 缓动类型
   * @param yoyo 是否起始值与终止值来回变换
   * @param loop 动画结束后重新开始动画
   * @param delayBeforeRepeat 动画结束后等待时间再进行下一步动作
   * @param delayBefore 开始动画前延时
   * @returns 动画实例
   */
  fadeOut(
    sprite,
    endAlpha = 0,
    frames = 60,
    type = 'sine',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    let o = {};
    this.wait(delayBefore).then(() => {
      o = this.tweenProperty(
        sprite,
        'alpha',
        sprite.alpha,
        endAlpha,
        frames,
        type,
        yoyo,
        loop,
        delayBeforeRepeat
      );
    });
    return o;
  }
  /**
   * 淡入动画，支持可配置
   * @param sprite 精灵
   * @param endAlpha 最终呈现的透明度的值
   * @param frames 执行帧数
   * @param type 缓动类型
   * @param yoyo 是否起始值与终止值来回变换
   * @param loop 动画结束后重新开始动画
   * @param delayBeforeRepeat 动画结束后等待时间再进行下一步动作
   * @param delayBefore 开始动画前延时
   * @returns 动画实例
   */
  fadeIn(
    sprite,
    endAlpha = 1,
    frames = 60,
    type = 'sine',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    let o = {};
    this.wait(delayBefore).then(() => {
      o = this.tweenProperty(
        sprite,
        'alpha',
        sprite.alpha,
        endAlpha,
        frames,
        type,
        yoyo,
        loop,
        delayBeforeRepeat
      );
    });
    return o;
  }
  /**
   * 以稳定的速率淡入淡出精灵。设置' minAlpha '为大于0的东西，如果你不要让精灵完全消失
   * @param sprite 精灵
   * @param frames 执行帧数
   * @param minAlpha 最小透明度
   * @returns 动画实例
   */
  pulse(sprite, frames = 60, minAlpha = 0) {
    return this.tweenProperty(
      sprite,
      'alpha',
      sprite.alpha,
      minAlpha,
      frames,
      'smoothstep',
      true
    );
  }
  /**
   * 旋转动画
   * @param sprite 精灵
   * @param endRotation 结束时旋转了多少弧度
   * @param frames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param loop 是否从结束时一直循环旋转
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @param delayBefore 开始动画前延时
   */
  rotation(
    sprite,
    endRotation,
    frames = 60,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    let o = {};
    this.wait(delayBefore).then(() => {
      o = this.tweenProperty(
        sprite,
        'rotation',
        sprite.rotation,
        endRotation,
        frames,
        type,
        yoyo,
        loop,
        delayBeforeRepeat
      );
    });
    return o;
  }
  /**
   * x轴 翻转动画
   * @param sprite 精灵
   * @param endSkewX 结束时翻转了多少弧度
   * @param frames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param loop 是否从结束时一直循环旋转
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @param delayBefore 开始动画前延时
   */
  flipX(
    sprite,
    endSkewX,
    frames = 60,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    let o = {};
    this.wait(delayBefore).then(() => {
      o = this.tweenProperty(
        sprite,
        'skewX',
        sprite.skew.x,
        endSkewX,
        frames,
        type,
        yoyo,
        loop,
        delayBeforeRepeat
      );
    });
    return o;
  }
  /**
   * y轴 翻转动画
   * @param sprite 精灵
   * @param endSkewY 结束时翻转了多少弧度
   * @param frames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param loop 是否从结束时一直循环旋转
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @param delayBefore 开始动画前延时
   */
  flipY(
    sprite,
    endSkewY,
    frames = 60,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    let o = {};
    this.wait(delayBefore).then(() => {
      o = this.tweenProperty(
        sprite,
        'skewY',
        sprite.skew.y,
        endSkewY,
        frames,
        type,
        yoyo,
        loop,
        delayBeforeRepeat
      );
    });
    return o;
  }
  /**
   * 自定义动画，先翻转，在上下移动，一直循环
   * @param sprite 精灵
   * @param endSkewY 结束时翻转了多少弧度
   * @param flipYFrames 翻转动画时长，以帧为单位（一秒60帧）
   * @param flipYType 翻转动画缓动类型
   * @param originalPathArray 路径数组
   * @param slideFrames 移动动画时长，以帧为单位（一秒60帧）
   * @param slideType 移动动画缓动类型
   */
  flipYAndWalkPath(
    sprite,
    endSkewY,
    flipYFrames = 60,
    flipYType = 'smoothstep',
    originalPathArray,
    slideFrames = 60,
    slideType = 'smoothstep'
  ) {
    // 创建补间对象
    const flipYAnimation = {};
    let startValue = sprite.skew.y;
    let endValue = endSkewY;
    // 使用当前端点值创建一个新的补间
    flipYAnimation.start = (startValue, endValue) => {
      // 克隆开始和结束值，以便任何对精灵属性的可能引用都转换为普通数字
      flipYAnimation.startValue = JSON.parse(JSON.stringify(startValue));
      flipYAnimation.endValue = JSON.parse(JSON.stringify(endValue));
      flipYAnimation.playing = true;
      flipYAnimation.totalFrames = flipYFrames;
      flipYAnimation.frameCounter = 0;

      // 将补间添加到全局补间数组中。补间数组会在每一帧上更新
      this.globalTweens.push(flipYAnimation);
    };

    // 调用start，并开始补间
    flipYAnimation.start(startValue, endValue);

    // update方法将被游戏循环在每一帧上调用。这就是使补间移动的原因
    flipYAnimation.update = () => {
      let curvedTime;
      if (flipYAnimation.playing) {
        // 如果经过的帧数少于总帧数，可以使用渐变公式移动精灵
        if (flipYAnimation.frameCounter < flipYAnimation.totalFrames) {
          // 当前帧数与设置的总帧数之比
          const normalizedTime =
            flipYAnimation.frameCounter / flipYAnimation.totalFrames;
          curvedTime = this.easingFormulas[flipYType](normalizedTime);
          // 根据缓动类型函数返回的值，来计算动画曲线值，来插入到精灵的具体属性，从而实现动画
          sprite.skew.y =
            flipYAnimation.endValue * curvedTime +
            flipYAnimation.startValue * (1 - curvedTime);

          // 当前帧数+1
          flipYAnimation.frameCounter += 1;
        } else {
          // 当补间结束时，运行结束任务
          sprite.skew.y = flipYAnimation.endValue;
          flipYAnimation.end();
        }
      }
    };
    // end方法将在补间完成时被调用
    flipYAnimation.end = () => {
      // 设置playing为false
      flipYAnimation.playing = false;
      // 调用补间的onComplete方法，如果它已经被分配
      if (flipYAnimation.onComplete) flipYAnimation.onComplete();

      // 从全局补间数组中移除该补间对象
      this.globalTweens.splice(this.globalTweens.indexOf(flipYAnimation), 1);
    };
    // 暂停和执行方法
    flipYAnimation.play = () => {
      flipYAnimation.playing = true;
    };
    flipYAnimation.pause = () => {
      flipYAnimation.playing = false;
    };

    flipYAnimation.onComplete = () => {
      makePath(currentPoint);
    };

    // 移动动画
    // 克隆路径数组，以便将精灵属性的任何可能引用转换为普通数字
    // let originalPathArray = [
    //     [57, 44],
    //     [57, 34],
    //     [57, 44]
    // ];
    const pathArray = JSON.parse(JSON.stringify(originalPathArray));

    // 计算每个路径段的持续时间(以帧为单位)' totalFrames '除以' pathArray '长度
    // const frames = totalFrames / pathArray.length;

    // 设置当前点为0，这将是第一个路径点
    const currentPoint = 0;

    // makePath函数在两点之间创建一个补间，然后在它之后安排下一个路径
    const makePath = currentPoint => {
      // 使用makeTween函数创建精灵的x和y位置的补间动画
      let tween = this.makeTween([
        // 在当前点的第一个x值和下一个点的x值之间创建x轴补间
        [
          sprite,
          'x',
          pathArray[currentPoint][0],
          pathArray[currentPoint + 1][0],
          slideFrames,
          slideType
        ],
        // 用同样的方法创建y轴补间
        [
          sprite,
          'y',
          pathArray[currentPoint][1],
          pathArray[currentPoint + 1][1],
          slideFrames,
          slideType
        ]
      ]);
      // 补间完成后，将currentPoint向前推进1。在路径段之间添加一个可选的延迟，然后建立下一个连接路径
      tween.onComplete = () => {
        // 推进到下一点
        currentPoint += 1;
        // 如果精灵还没有到达路径的尽头，那么将精灵补间到下一个点
        if (currentPoint < pathArray.length - 1) {
          tween = makePath(currentPoint);
        } else {
          currentPoint = 0;
          flipYAnimation.start(
            flipYAnimation.startValue,
            flipYAnimation.endValue
          );
        }
      };
      return tween;
    };
  }
  // ------------------------------------ 2. 复杂类 ------------------------------------
  /**
   * 设置一个坐标点，从当前位置移动到坐标点
   * @param sprite 精灵
   * @param endX 终点x坐标
   * @param endY 终点y坐标
   * @param frames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param loop 是否循环
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @returns 动画实例
   */
  slide(
    sprite,
    endX,
    endY,
    frames = 60,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0
  ) {
    return this.makeTween([
      [
        sprite,
        'x',
        sprite.x,
        endX,
        frames,
        type,
        yoyo,
        loop,
        delayBeforeRepeat
      ],
      [sprite, 'y', sprite.y, endY, frames, type, yoyo, loop, delayBeforeRepeat]
    ]);
  }
  /**
   * 设置一个坐标点，从当前位置移动到坐标点，执行动画前延时
   * @param sprite 精灵
   * @param endX 终点x坐标
   * @param endY 终点y坐标
   * @param frames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param loop 是否循环
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @param delayBefore 动画执行前设置延时执行
   */
  slideDelayBefore(
    sprite,
    endX,
    endY,
    frames = 60,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    this.wait(delayBefore).then(() => {
      this.makeTween([
        [
          sprite,
          'x',
          sprite.x,
          endX,
          frames,
          type,
          yoyo,
          loop,
          delayBeforeRepeat
        ],
        [
          sprite,
          'y',
          sprite.y,
          endY,
          frames,
          type,
          yoyo,
          loop,
          delayBeforeRepeat
        ]
      ]);
    });
  }
  /**
   * 呼吸 缩放效果
   * @param sprite 精灵
   * @param endScaleX x轴最终缩放值
   * @param endScaleY y轴最终缩放值
   * @param frames 动画时长，以帧为单位
   * @param yoyo 是否来回循环
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @returns 动画实例
   */
  breathe(
    sprite,
    endScaleX = 0.8,
    endScaleY = 0.8,
    frames = 60,
    yoyo = true,
    delayBeforeRepeat = 0
  ) {
    // 添加scaleX和scaleY属性到Pixi精灵
    this._addScaleProperties(sprite);
    return this.makeTween([
      [
        sprite,
        'scaleX',
        sprite.scaleX,
        endScaleX,
        frames,
        'smoothstepSquared',
        yoyo,
        false,
        delayBeforeRepeat
      ],
      [
        sprite,
        'scaleY',
        sprite.scaleY,
        endScaleY,
        frames,
        'smoothstepSquared',
        yoyo,
        false,
        delayBeforeRepeat
      ]
    ]);
  }
  /**
   * 缩放动画
   * @param sprite 精灵实例
   * @param endScaleX x轴最终缩放值
   * @param endScaleY y轴最终缩放值
   * @param frames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param loop 是否从结束时一直循环旋转
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @param delayBefore 动画执行前设置延时执行
   * @returns 动画实例
   */
  scale(
    sprite,
    endScaleX = 0.5,
    endScaleY = 0.5,
    frames = 60,
    type = 'smoothstep',
    yoyo = false,
    loop = false,
    delayBeforeRepeat = 0,
    delayBefore = 0
  ) {
    // 添加scaleX和scaleY属性到Pixi精灵
    this._addScaleProperties(sprite);
    let o = {};
    this.wait(delayBefore).then(() => {
      o = this.makeTween([
        [
          sprite,
          'scaleX',
          sprite.scaleX,
          endScaleX,
          frames,
          type,
          yoyo,
          loop,
          delayBeforeRepeat
        ],
        [
          sprite,
          'scaleY',
          sprite.scaleY,
          endScaleY,
          frames,
          type,
          yoyo,
          loop,
          delayBeforeRepeat
        ]
      ]);
    });
    return o;
  }
  /**
   * 快速改变精灵比例，使精灵看起来像闪光灯一样闪烁。
   * @param sprite 精灵
   * @param scaleFactor 最终缩放值
   * @param startMagnitude 缓动类型初始值
   * @param endMagnitude 缓动类型结束值
   * @param frames 动画时长，以帧为单位
   * @param yoyo 是否来回循环
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @returns 动画实例
   */
  strobe(
    sprite,
    scaleFactor = 1.3,
    startMagnitude = 10,
    endMagnitude = 20,
    frames = 10,
    yoyo = true,
    delayBeforeRepeat = 0
  ) {
    const bounce = 'bounce ' + startMagnitude + ' ' + endMagnitude;
    // 添加scaleX和scaleY属性到Pixi精灵
    this._addScaleProperties(sprite);
    return this.makeTween([
      [
        sprite,
        'scaleX',
        sprite.scaleX,
        scaleFactor,
        frames,
        bounce,
        yoyo,
        false,
        delayBeforeRepeat
      ],
      [
        sprite,
        'scaleY',
        sprite.scaleY,
        scaleFactor,
        frames,
        bounce,
        yoyo,
        false,
        delayBeforeRepeat
      ]
    ]);
  }
  /**
   * 使精灵像果冻一样摆动
   * @param sprite 精灵
   * @param scaleFactorX x最终缩放值
   * @param scaleFactorY y最终缩放值
   * @param frames 动画时长，以帧为单位
   * @param xStartMagnitude x缓动类型初始值
   * @param xEndMagnitude x缓动类型结束值
   * @param yStartMagnitude y缓动类型初始值
   * @param yEndMagnitude y缓动类型结束值
   * @param friction 摩擦值
   * @param yoyo 是否来回循环
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @returns 动画实例
   */
  wobble(
    sprite,
    scaleFactorX = 1.2,
    scaleFactorY = 1.2,
    frames = 10,
    xStartMagnitude = 10,
    xEndMagnitude = 10,
    yStartMagnitude = -10,
    yEndMagnitude = -10,
    friction = 0.98,
    yoyo = true,
    delayBeforeRepeat = 0
  ) {
    const bounceX = 'bounce ' + xStartMagnitude + ' ' + xEndMagnitude;
    const bounceY = 'bounce ' + yStartMagnitude + ' ' + yEndMagnitude;
    // 添加scaleX和scaleY属性到Pixi精灵
    this._addScaleProperties(sprite);
    const o = this.makeTween([
      [
        sprite,
        'scaleX',
        sprite.scaleX,
        scaleFactorX,
        frames,
        bounceX,
        yoyo,
        false,
        delayBeforeRepeat
      ],
      [
        sprite,
        'scaleY',
        sprite.scaleY,
        scaleFactorY,
        frames,
        bounceY,
        yoyo,
        false,
        delayBeforeRepeat
      ]
    ]);
    // 在每个补间结束时给endValue添加一些摩擦
    o.tweens.forEach(tween => {
      tween.onComplete = () => {
        // 如果' endValue '大于1，则增加摩擦
        if (tween.endValue > 1) {
          tween.endValue *= friction;
          // 当效果完成时，设置endValue为1，并从全局' tweens '数组中移除渐变
          if (tween.endValue <= 1) {
            tween.endValue = 1;
            this.removeTween(tween);
          }
        }
      };
    });
    return o;
  }

  // ------------------------------------ 3. 运动路径补间 ------------------------------------
  /**
   * 沿贝塞尔曲线移动
   * @param sprite 精灵
   * @param pointsArray 坐标点数组
   * @param totalFrames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param yoyo 是否来回循环
   * @param delayBeforeRepeat 结束后开始动画的延时
   * @returns 动画实例
   */
  followCurve(
    sprite,
    pointsArray,
    totalFrames,
    type = 'smoothstep',
    yoyo = false,
    delayBeforeRepeat = 0
  ) {
    const o = {};

    // 如果补间是一个弹跳类型(样条)，设置开始和结束的幅度值
    const typeArray = type.split(' ');
    if (typeArray[0] === 'bounce') {
      o.startMagnitude = parseInt(typeArray[1]);
      o.endMagnitude = parseInt(typeArray[2]);
    }

    // 通过补间对象的start方法，使用当前的结束值去创建一个新的补间
    o.start = pointsArray => {
      o.playing = true;
      o.totalFrames = totalFrames;
      o.frameCounter = 0;

      // 克隆设置像素点的数组
      o.pointsArray = JSON.parse(JSON.stringify(pointsArray));

      // 添加补间到globalTweens数组。globalTweens数组在每一帧上更新
      this.globalTweens.push(o);
    };

    // 调用start，开始第一个补间动画
    o.start(pointsArray);

    // update方法将被游戏循环在每一帧上调用。这就是补间动画产生的原因
    o.update = () => {
      let normalizedTime;
      let curvedTime;
      const p = o.pointsArray;

      if (o.playing) {
        // 如果经过的帧数少于总帧数，可以使用渐变公式移动精灵
        if (o.frameCounter < o.totalFrames) {
          // 得出比值
          normalizedTime = o.frameCounter / o.totalFrames;

          // 选择正确的缓动类型
          if (typeArray[0] !== 'bounce') {
            curvedTime = this.easingFormulas[type](normalizedTime);
          } else {
            curvedTime = this.easingFormulas.spline(
              normalizedTime,
              o.startMagnitude,
              0,
              1,
              o.endMagnitude
            );
          }

          // 将贝塞尔曲线应用于精灵的位置
          sprite.x = this.easingFormulas.cubicBezier(
            curvedTime,
            p[0][0],
            p[1][0],
            p[2][0],
            p[3][0]
          );
          sprite.y = this.easingFormulas.cubicBezier(
            curvedTime,
            p[0][1],
            p[1][1],
            p[2][1],
            p[3][1]
          );

          o.frameCounter += 1;
        } else {
          // 当补间结束时，运行结束任务
          // sprite[property] = o.endValue;
          o.end();
        }
      }
    };

    // ' end '方法将在补间完成时被调用
    o.end = () => {
      o.playing = false;
      if (o.onComplete) o.onComplete();

      this.globalTweens.splice(this.globalTweens.indexOf(o), 1);

      if (yoyo) {
        this.wait(delayBeforeRepeat).then(() => {
          o.pointsArray = o.pointsArray.reverse();
          o.start(o.pointsArray);
        });
      }
    };

    o.pause = () => {
      o.playing = false;
    };
    o.play = () => {
      o.playing = true;
    };

    return o;
  }
  /**
   * 沿指定点的路径移动
   * @param sprite 精灵
   * @param originalPathArray 路径数组
   * @param totalFrames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param loop 是否循环
   * @param yoyo 是否来回循环
   * @param delayBetweenSections 一个以毫秒为单位的数字，用于确定精灵在移动到路径的下一部分之前应该等待的时间。
   * @param delayBeforeLoop 循环之前的等待时间
   * @returns 动画实例
   */
  walkPath(
    sprite,
    originalPathArray,
    totalFrames = 300,
    type = 'smoothstep',
    loop = false,
    yoyo = false,
    delayBetweenSections = 0,
    delayBeforeLoop = 0
  ) {
    // 克隆路径数组，以便将精灵属性的任何可能引用转换为普通数字
    const pathArray = JSON.parse(JSON.stringify(originalPathArray));

    // 计算每个路径段的持续时间(以帧为单位)' totalFrames '除以' pathArray '长度
    // const frames = totalFrames / pathArray.length;

    // 设置当前点为0，这将是第一个路径点
    const currentPoint = 0;

    // makePath函数在两点之间创建一个补间，然后在它之后安排下一个路径
    const makePath = currentPoint => {
      // 使用makeTween函数创建精灵的x和y位置的补间动画
      let tween = this.makeTween([
        // 在当前点的第一个x值和下一个点的x值之间创建x轴补间
        [
          sprite,
          'x',
          pathArray[currentPoint][0],
          pathArray[currentPoint + 1][0],
          totalFrames,
          type
        ],
        // 用同样的方法创建y轴补间
        [
          sprite,
          'y',
          pathArray[currentPoint][1],
          pathArray[currentPoint + 1][1],
          totalFrames,
          type
        ]
      ]);
      // 补间完成后，将currentPoint向前推进1。在路径段之间添加一个可选的延迟，然后建立下一个连接路径
      tween.onComplete = () => {
        // 推进到下一点
        currentPoint += 1;
        // 如果精灵还没有到达路径的尽头，那么将精灵补间到下一个点
        if (currentPoint < pathArray.length - 1) {
          this.wait(delayBetweenSections).then(() => {
            tween = makePath(currentPoint);
          });
        } else {
          // 如果我们到达了路径的终点，可以选择循环/起点-终点来回循环
          if (loop) {
            if (yoyo) pathArray.reverse();

            this.wait(delayBeforeLoop).then(() => {
              currentPoint = 0;
              sprite.x = pathArray[0][0];
              sprite.y = pathArray[0][1];

              tween = makePath(currentPoint);
            });
          }
        }
      };
      return tween;
    };

    // 使用内部的' makePath '函数创建第一个路径
    const tween = makePath(currentPoint);

    // 将补间传递回主程序
    return tween;
  }
  /**
   * 遵循一系列连接的贝塞尔曲线移动；以下是参数
   * @param sprite 精灵
   * @param pathArray 路径数组
   * @param totalFrames 动画时长，以帧为单位
   * @param type 缓动类型
   * @param loop 是否循环
   * @param yoyo 是否来回循环
   * @param delayBeforeContinue 一个以毫秒为单位的数字，用于确定精灵yoyo之前的延迟时间。
   * @returns 动画实例
   */
  walkCurve(
    sprite,
    pathArray,
    totalFrames = 300,
    type = 'smoothstep',
    loop = false,
    yoyo = false,
    delayBeforeContinue = 0
  ) {
    // 计算每个路径段的持续时间(以帧为单位)' totalFrames '除以' pathArray '长度
    const frames = totalFrames / pathArray.length;

    // 设置当前点为0，这将是第一个路径点
    const currentCurve = 0;

    // makePath函数在两点之间创建一个补间，然后在它之后安排下一个路径
    const makePath = currentCurve => {
      // 使用自定义的followCurve函数让精灵遵循曲线
      let tween = this.followCurve(
        sprite,
        pathArray[currentCurve],
        frames,
        type
      );

      // 补间完成后，将currentCurve向前推进1。在路径段之间添加一个可选的延迟，然后创建下一个路径
      tween.onComplete = () => {
        currentCurve += 1;
        if (currentCurve < pathArray.length) {
          this.wait(delayBeforeContinue).then(() => {
            tween = makePath(currentCurve);
          });
        } else {
          if (loop) {
            if (yoyo) {
              // 反转路径
              pathArray.reverse();

              // 反转路径点
              pathArray.forEach(curveArray => curveArray.reverse());
            }

            // 在一个可选的延迟之后，将精灵重置为路径的开始，并创建下一个新路径
            this.wait(delayBeforeContinue).then(() => {
              currentCurve = 0;
              sprite.x = pathArray[0][0];
              sprite.y = pathArray[0][1];
              tween = makePath(currentCurve);
            });
          }
        }
      };

      return tween;
    };

    const tween = makePath(currentCurve);

    return tween;
  }
  // ------------------------------------ 5. 组合动画序列 -----------------------------------
  testWaitQueue() {
    this.wait(0)
      .then(() => {
        setTimeout(() => {
          console.log('One');
        }, 1000);
      })
      .then(() => this.wait(0))
      .then(() => {
        setTimeout(() => {
          console.log('Two');
        }, 1000);
      })
      .then(() => this.wait(0))
      .then(() => {
        setTimeout(() => {
          console.log('Three');
        }, 1000);
      });
  }

  // ------------------------------------ 4. 工具方法 ------------------------------------
  /**
   * wait方法允许你设置一个定时的事件序列
   * wait(1000).then(() => console.log("One"))
   *      .then(() => wait(1000))
   *      .then(() => console.log("Two"))
   *      .then(() => wait(1000))
   *      .then(() => console.log("Three"))
   * @param duration 等待时长
   * @returns promise对象
   */
  wait(duration = 0) {
    return new Promise((resolve, reject) => {
      if (duration !== 0) {
        setTimeout(resolve, duration);
      } else {
        resolve();
      }
    });
  }

  /**
   * 通过全局的补间对象数组将具体的补间对象从游戏循环中移除
   * @param tweenObject  具体的补间对象
   */
  removeTween(tweenObject) {
    // 如果' tweenObject '没有任何嵌套的补间对象，移除补间
    if (!tweenObject.tweens) {
      tweenObject.pause();

      // array.splice(-1,1)总是会删除数组的最后一个元素，所以这个额外的检查可以防止出错
      if (this.globalTweens.indexOf(tweenObject) !== -1) {
        this.globalTweens.splice(this.globalTweens.indexOf(tweenObject), 1);
      }
    } else {
      // 否则，移除嵌套的补间对象
      tweenObject.pause();
      tweenObject.tweens.forEach(element => {
        this.globalTweens.splice(this.globalTweens.indexOf(element), 1);
      });
    }
  }
  /**
   * 通过游戏循环调用，执行全局补间数组中所有补间动画对象的update方法，从而实现动画的形成
   */
  update() {
    // 更新' globalTweens '数组中的所有补间对象
    if (this.globalTweens.length > 0) {
      for (let i = this.globalTweens.length - 1; i >= 0; i--) {
        const tween = this.globalTweens[i];
        if (tween) tween.update();
      }
    }
  }
}
