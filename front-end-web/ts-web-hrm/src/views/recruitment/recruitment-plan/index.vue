<template>
  <div class="recruitment-plan-box">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{
        planStatus: '0'
      }"
    >
      <template v-slot:signUpMonth>
        <ts-date-picker
          v-model="searchForm.signUpMonth"
          mode="month"
          format="YYYY-MM"
          :open="searchMonthIsOpen"
          @openChange="val => (searchMonthIsOpen = val)"
          @panelChange="handlePanelChange"
        ></ts-date-picker>
      </template>
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-recruit-plan
      :eachData="eachData"
      :settingList="settingList"
      v-model="dialogRecruitPlan"
      @refresh="handleRefreshTable"
    />

    <dialog-recruit-details
      v-model="dialogRecruitDetails"
      :eachData="eachData"
    />
  </div>
</template>

<script>
import table from './mixins/table';
import DialogRecruitPlan from './components/dialog-recruit-plan.vue';
import DialogRecruitDetails from './components/dialog-recruit-details';

export default {
  mixins: [table],
  components: {
    DialogRecruitPlan,
    DialogRecruitDetails
  },
  data() {
    return {
      dialogRecruitPlan: false,
      dialogRecruitDetails: false,
      searchMonthIsOpen: false,
      settingList: [],
      eachData: {}
    };
  },
  async created() {
    this.$nextTick(() => {
      this.handleRefreshTable();
    });
  },
  methods: {
    async handleGetPlanSetting() {
      const res = await this.ajax.zpapiSettingList({
        pageNo: 1,
        pageSize: 999
      });
      this.settingList = res.rows.map(item => {
        return {
          label: item.porName,
          value: item.id
        };
      });
    },
    handleDetails(row) {
      this.handleGetPlanSetting();
      this.eachData = row;
      this.dialogRecruitDetails = true;
    },
    // 新增
    handleAddRecruitPlan() {
      this.handleGetPlanSetting();
      this.eachData = {};
      this.dialogRecruitPlan = true;
    },
    // 编辑
    handleRecruitEdit(e) {
      this.eachData = e;
      this.dialogRecruitPlan = true;
    },
    // 发布
    async handleRelease(row) {
      try {
        await this.$confirm('发布后该招聘计划对外公开，确定发布吗？', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.recruitRelease(row.id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message);
        }
      } catch (e) {
        console.error(e);
      }
    },
    // 取消发布
    async handleUnpublish(row) {
      try {
        await this.$confirm(
          '取消发布后该招聘计划不对外公开，确定取消吗？',
          '提示',
          {
            type: 'warning'
          }
        );
        const res = await this.ajax.cancelRelease(row.id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message);
        }
      } catch (e) {
        console.error(e);
      }
    },
    // 结束招聘
    async handleEndRecruit(row) {
      try {
        await this.$confirm('该操作无法撤销，确定要结束本次招聘吗？', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.recruitPlanEnd(row.id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message);
        }
      } catch (e) {
        console.error(e);
      }
    },
    // 删除
    async handleRecruitDel(row) {
      try {
        await this.$confirm('确定删除该数据吗？', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.recruitPlanDel({
          id: row.id
        });
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message);
        }
      } catch (e) {
        console.error(e);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.recruitment-plan-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
</style>
