export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '意见',
          value: 'opinion',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'index',
          align: 'center'
        },
        {
          label: '意见',
          prop: 'opinion',
          align: 'left',
          formatter: row => {
            return (
              <span
                class="details-span"
                onClick={() => {
                  this.handleDetails(row);
                }}>
                {row.opinion}
              </span>
            );
          }
        },
        {
          label: '是否处理',
          prop: 'status',
          width: 100,
          align: 'center',
          formatter: row => {
            return row.status == '1' ? '未处理' : '已处理';
          }
        },
        {
          label: '提出时间',
          prop: 'createDate',
          width: 200,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          fixed: 'right',
          headerSlots: 'action',
          formatter: row => {
            let arr = [
              {
                label: '处理',
                event: this.handleEdit
              },
              {
                label: '删除',
                event: this.handleDel
              }
            ];
            return (
              <BaseActionCell
                actions={arr}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ]
    };
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          boxType: 0,
          sidx: 'create_date',
          sord: 'desc'
        };
      let res = await this.ajax.suggestionBoxList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
