<template>
  <vxe-modal
    className="dialog-add-setting"
    v-model="visible"
    title="人力规划导入"
    width="500"
    height="260"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <ts-form ref="form" :model="form" labelWidth="120px">
          <ts-form-item label="导入方式" prop="type" :rules="rules.required">
            <ts-select v-model="form.type" style="width: 100%">
              <ts-option
                v-for="item of selectType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </ts-form-item>

          <ts-form-item label="">
            <div class="tips-text" v-if="form.type == '1'">
              增量导入将在已有的数据基础上新增
            </div>
            <div class="tips-text" v-if="form.type == '2'">
              全量导入将已有的数据清除后重新导入
            </div>
          </ts-form-item>

          <ts-form-item
            label="选择文件"
            prop="fileName"
            :rules="rules.required"
          >
            <div class="file-box">
              <ts-input v-model="form.fileName" disabled></ts-input>
              <ts-upload
                class="upload-demo"
                action=""
                :show-file-list="false"
                accept=".xlsx,.xls"
                :http-request="httpRequest"
              >
                <ts-button class="shallowButton" type="primary">浏览</ts-button>
              </ts-upload>
            </div>
          </ts-form-item>

          <ts-form-item label="">
            <div class="down-template" @click="handleDownTemplate">
              下载导入模版
            </div>
          </ts-form-item>
        </ts-form>
      </div>

      <vxe-modal
        v-model="importErrorLoading"
        title="处理结果"
        width="500"
        height="260"
        showFooter
      >
        <template #default>
          <div class="content" v-html="importErrorResult"></div>
        </template>

        <template #footer>
          <ts-button @click="importErrorLoading = false">关 闭</ts-button>
        </template>
      </vxe-modal>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" :loading="submitLoading" @click="submit">
          提 交
        </ts-button>
        <ts-button @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      importErrorLoading: false,
      importErrorResult: '',

      selectType: [
        { label: '增量导入', value: '1' },
        { label: '全量导入', value: '2' }
      ],

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    handleDownTemplate() {
      let a = document.createElement('a');
      a.href =
        '/ts-basics-bottom/organizationAllocation/download/template/importOrganizationAllocation';
      a.click();
    },

    open() {
      this.$set(this, 'form', {
        type: '1',
        fileName: '',
        file: ''
      });

      this.importErrorResult = '';
      this.importErrorLoading = false;

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    httpRequest({ file }) {
      this.form.file = file;
      this.form.fileName = file.name;
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = new FormData();
        formData.append('file', this.form.file);
        formData.append('type', this.form.type);
        this.submitLoading = true;
        const res = await this.ajax.organizationPlanOrganizationAllocationImport(
          formData
        );
        this.submitLoading = false;
        if (!res.success) {
          this.importErrorLoading = true;
          this.importErrorResult = res.message || '操作失败!';
          return;
        }

        this.$newMessage('success', '操作成功!');
        this.close();
        this.$emit('refresh');
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.form = {};
      this.importErrorResult = '';
      this.importErrorLoading = false;
      this.$refs.form?.clearValidate();

      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-setting {
  .tips-text {
    color: #f8a745;
    font-size: 14px;
  }

  .file-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .down-template {
    color: $primary-blue;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
