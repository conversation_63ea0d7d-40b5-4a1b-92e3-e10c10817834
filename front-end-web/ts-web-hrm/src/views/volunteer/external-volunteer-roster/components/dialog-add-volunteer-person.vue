<template>
  <ts-dialog
    custom-class="dialog-add-volunteer-person"
    :visible.sync="visible"
    :title="isEdit ? '编辑' : '新增'"
    type="large"
  >
    <ts-form ref="form" :model="editData">
      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            prop="externalName"
            :rules="rules.required"
            label="姓名"
          >
            <ts-input placeholder="请输入" v-model="editData.externalName" />
          </ts-form-item>
        </ts-col>

        <ts-col :span="12">
          <ts-form-item
            label="身份证号码"
            prop="externalIdcard"
            :rules="rules.identificationNumber"
          >
            <ts-input
              v-model="editData.externalIdcard"
              @blur="handleBlurIdCard"
              placeholder="请输入"
            />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            prop="externalBirthday"
            :rules="rules.required"
            label="出生年月"
          >
            <ts-date-picker
              style="width:100%"
              v-model="editData.externalBirthday"
              valueFormat="YYYY-MM-DD"
              placeholder="请选择"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="12">
          <ts-form-item
            label="性别"
            prop="externalGender"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="editData.externalGender"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of sexList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            label="联系方式"
            prop="externalMobile"
            :rules="rules.iphone"
          >
            <ts-input v-model="editData.externalMobile" placeholder="请输入" />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-form-item
        label="爱心类型"
        prop="externalLoveType"
        :rules="rules.required"
      >
        <ts-select
          style="width: 100%"
          v-model="editData.externalLoveType"
          clearable
          multiple
          placeholder="请选择"
        >
          <ts-option
            v-for="item of loveList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></ts-option>
        </ts-select>
      </ts-form-item>

      <ts-row>
        <ts-col :span="12">
          <ts-form-item
            label="服务类型"
            prop="externalServiceType"
            :rules="rules.required"
          >
            <ts-select
              style="width: 100%"
              v-model="editData.externalServiceType"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of serviseList"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>

        <ts-col :span="12">
          <ts-form-item
            label="社工证"
            prop="externalSwc"
            :rules="rules.required"
          >
            <ts-radio-group v-model="editData.externalSwc">
              <ts-radio label="0">没有社工证</ts-radio>
              <ts-radio label="1">有社工证</ts-radio>
            </ts-radio-group>
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-form-item
        prop="externalSwcFile"
        :rules="rules.externalSwcFile"
        label="社工证上传"
      >
        <base-upload v-model="editData.externalSwcFile" />
      </ts-form-item>

      <ts-form-item label="可服务时间" style="position: relative;">
        <span
          style="position: absolute;left: -87px;top: 9px;color: rgb(245, 108, 108);"
        >
          *
        </span>
        <ts-select
          style="width: 300px"
          v-model="editData.serviseTime"
          clearable
          multiple
          placeholder="请选择"
        >
          <ts-option
            v-for="item of serviseTimeList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          ></ts-option>
        </ts-select>

        <ts-radio-group style="margin: 0 8px" v-model="editData.dayType">
          <ts-radio label="上午">上午</ts-radio>
          <ts-radio label="下午">下午</ts-radio>
          <ts-radio label="全天">全天</ts-radio>
        </ts-radio-group>

        <ts-button type="primary" @click="handleSubmitServiseTime">
          保存
        </ts-button>
      </ts-form-item>

      <ts-form-item label="" prop="serviceJson" :rules="rules.required">
        <ul class="servise-time-container">
          <li v-for="(item, index) in editData.serviceJson" :key="index">
            <ts-tag @close="handleDeleteServise(item)" closable>
              {{ item.label }}
            </ts-tag>
          </li>
        </ul>
      </ts-form-item>

      <ts-row>
        <ts-col :span="12">
          <ts-form-item label="服务区域">
            <ts-select
              style="width: 100%"
              v-model="editData.externalServiceArea"
              clearable
              placeholder="请选择"
            >
              <ts-option
                v-for="item of areaList"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-col>

        <ts-col :span="12">
          <ts-form-item label="职业">
            <ts-input placeholder="请输入" v-model="editData.externalCareer" />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-form-item label="单位">
        <ts-input placeholder="请输入" v-model="editData.externalUnit" />
      </ts-form-item>

      <ts-row>
        <ts-col :span="12">
          <ts-form-item label="专业特长">
            <ts-input
              placeholder="请输入"
              v-model="editData.externalSpeciality"
            />
          </ts-form-item>
        </ts-col>

        <ts-col :span="12">
          <ts-form-item label="电子邮箱">
            <ts-input placeholder="请输入" v-model="editData.externalEmail" />
          </ts-form-item>
        </ts-col>
      </ts-row>

      <ts-form-item label="居住地址">
        <ts-input placeholder="请输入" v-model="editData.externalAddress" />
      </ts-form-item>
    </ts-form>

    <template slot="footer">
      <ts-button type="primary" @click="submit">确定</ts-button>
      <ts-button @click="close">取消</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
import moment from 'moment';
import { deepClone } from '@/unit/commonHandle.js';
import { commonUtils } from '@/util/common.js';
export default {
  data() {
    return {
      visible: false,
      isEdit: false,
      editData: {},
      sexList: [
        { label: '男', value: '0' },
        { label: '女', value: '1' }
      ],
      loveList: [
        { label: '日常陪伴', value: '日常陪伴' },
        { label: '康复陪伴', value: '康复陪伴' },
        { label: '出游陪伴', value: '出游陪伴' },
        { label: '农场劳动', value: '农场劳动' },
        { label: '捐赠活动', value: '捐赠活动' },
        { label: '表演节目', value: '表演节目' },
        { label: '专业服务', value: '专业服务' },
        { label: '心理辅导', value: '心理辅导' },
        { label: '其他服务', value: '其他服务' }
      ],
      serviseList: [
        { label: '失智失能人员', value: '失智失能人员' },
        { label: '失能老人', value: '失能老人' },
        { label: '精神病人', value: '精神病人' },
        { label: '其他', value: '其他' }
      ],
      serviseTimeList: [
        { label: '周一', value: '周一' },
        { label: '周二', value: '周二' },
        { label: '周三', value: '周三' },
        { label: '周四', value: '周四' },
        { label: '周五', value: '周五' },
        { label: '周六', value: '周六' },
        { label: '周日', value: '周日' }
      ],
      areaList: [
        { label: '雨花区', value: '雨花区' },
        { label: '开福区', value: '开福区' },
        { label: '望城区', value: '望城区' },
        { label: '天心区', value: '天心区' },
        { label: '芙蓉区', value: '芙蓉区' },
        { label: '岳麓区', value: '岳麓区' },
        { label: '长沙县', value: '长沙县' },
        { label: '浏阳市', value: '浏阳市' },
        { label: '宁乡县', value: '宁乡县' },
        { label: '其他', value: '其他' }
      ],
      disabledDate: current => {
        return current && current <= moment().subtract(1, 'days');
      },
      rules: {
        required: { required: true, message: '必填' },
        externalSwcFile: { required: true, message: '必填' },

        identificationNumber: [
          { required: true, message: '必填' },
          {
            required: true,
            message: '请输入正确的身份证号码格式',
            trigger: ['blur', 'change'],
            validator: (prop, value, callback) => {
              let trimVal = String(value).trim();

              let reg = /((1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5]|8[1-3])\d{4})(\d{4})(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])(\d{3}([0-9xX]))/;

              if (!reg.test(trimVal)) {
                callback('请输入正确格式');
              } else {
                callback();
              }
            }
          }
        ],

        iphone: [
          { required: true, message: '必填' },
          {
            trigger: ['blur', 'change'],
            message: '联系方式格式有误',
            validator: (prop, value, cb) => {
              let reg = /^1[3,4,5,6,7,8,9][0-9]{9}$/;
              if (value && !reg.test(value)) {
                cb('false');
                return;
              }
              cb();
            }
          }
        ]
      }
    };
  },
  watch: {
    'editData.externalSwc': {
      handler(val) {
        this.$set(this.rules.externalSwcFile, 'required', val === '1');
      }
    }
  },
  methods: {
    open({ data, isEdit }) {
      this.isEdit = isEdit;

      if (this.isEdit) {
        let echoData = deepClone(data);
        echoData.serviceJson = JSON.parse(echoData.serviceJson);
        echoData.externalLoveType = echoData.externalLoveType.split(',');
        delete echoData.serviceTimeList;
        this.editData = echoData;
        this.editData.dayType = '全天';
      } else {
        this.editData = {
          externalName: '',
          externalIdcard: '',
          externalBirthday: '',
          externalGender: '',
          externalMobile: '',
          externalLoveType: [],
          externalServiceType: '',
          externalSwc: '1',
          externalSwcFile: '',

          serviseTime: null,
          dayType: '全天',
          serviceJson: [],

          externalServiceArea: '',
          externalCareer: '',
          externalUnit: '',
          externalSpeciality: '',
          externalEmail: '',
          externalAddress: ''
        };
      }

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },
    handleBlurIdCard() {
      let reg = /((1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5]|8[1-3])\d{4})(\d{4})(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])(\d{3}([0-9xX]))/;

      let val = this.editData.externalIdcard;
      if (reg.test(val)) {
        this.$set(
          this.editData,
          'externalBirthday',
          commonUtils.getBrithDayFromIdCard(val)
        );
        this.$set(
          this.editData,
          'externalGender',
          commonUtils.getSexFromIdCard(val) === '女' ? '1' : '0'
        );
      }
    },
    async handleSubmitServiseTime() {
      if (this.editData.serviseTime && this.editData.serviseTime.length > 0) {
        this.editData.serviceJson.push({
          label:
            this.editData.serviseTime.join(',') + ' ' + this.editData.dayType
        });

        this.$set(this.editData, 'serviseTime', []);
        this.$set(this.editData, 'dayType', '全天');

        this.$nextTick(() => {
          this.$refs.form?.clearValidate();
        });
      } else {
        this.$message.warning('请选择服务日期(周)');
        return false;
      }
    },
    handleDeleteServise(item) {
      let index = this.editData.serviceJson.indexOf(item);
      if (index !== -1) {
        this.editData.serviceJson.splice(index, 1);
      }
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.editData);
        formData.serviceTimeList = [];
        formData.serviceJson.forEach(({ label }) => {
          let [week = '', serviceTime = ''] = label.split(' '),
            weekArr = week.split(',');

          weekArr.forEach(serviceWeek => {
            formData.serviceTimeList.push({
              serviceWeek,
              serviceTime
            });
          });
        });
        formData.serviceJson = JSON.stringify(formData.serviceJson);
        formData.age = this.getAgeFromIdCard(formData.externalIdcard);

        formData.externalLoveType = formData.externalLoveType.join(',');

        delete formData.serviseTime;
        delete formData.dayType;

        let API = null;
        if (this.isEdit) {
          API = this.ajax.voluntariesExternalUpdate;
        } else {
          API = this.ajax.voluntariesExternalSave;
        }

        const res = await API(formData);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    getAgeFromIdCard(idCard) {
      var birthday = idCard.substr(6, 8);
      var year = birthday.substr(0, 4);
      var month = birthday.substr(4, 2);
      var day = birthday.substr(6, 2);
      var age = new Date().getFullYear() - parseInt(year);

      // 如果当前月份小于出生月份，或者当前月份等于出生月份但是当前日期小于出生日期，则年龄减一
      if (
        new Date().getMonth() + 1 < parseInt(month) ||
        (new Date().getMonth() + 1 === parseInt(month) &&
          new Date().getDate() < parseInt(day))
      ) {
        age--;
      }

      return age;
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .dialog-add-volunteer-person {
  .el-dialog__body {
    height: calc(100vh - 200px);
    overflow: auto;
  }
  .ant-time-picker-panel-select {
    &:last-child {
      display: none;
    }
  }
  .ts-time-picker {
    height: 30px !important;
    padding: 0 8px !important;
    margin-left: 8px;
  }
  .servise-time-container {
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #eee;
    height: 60px;
    overflow: auto;
    padding: 8px;
    li {
      margin-right: 8px;
    }
  }
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
