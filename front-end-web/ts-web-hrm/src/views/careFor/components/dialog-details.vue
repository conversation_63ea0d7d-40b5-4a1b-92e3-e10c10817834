<template>
  <ts-dialog
    class="dialog-edit-form"
    title="详情"
    width="800px"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <table ref="tableDataBox" class="table-data" style="table-layout: fixed">
        <tr>
          <td colspan="2">
            关爱需求:
          </td>
          <td colspan="5">
            {{ form.opinion }}
          </td>
        </tr>
        <tr>
          <td colspan="2">
            关爱结果:
          </td>
          <td colspan="5">
            {{ form.result }}
          </td>
        </tr>
      </table>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: <PERSON><PERSON><PERSON>
    },
    eachData: {
      type: Object
    }
  },
  data() {
    return {
      visible: false,

      form: {}
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.initData();
          this.$nextTick(() => {
            this.$refs.form?.clearValidate();
          });

          if (JSON.stringify(this.eachData) !== '{}') {
            this.form = deepClone(this.eachData);
          }
        }
        this.visible = val;
      }
    }
  },
  methods: {
    initData() {
      this.form.opinion = '';
      this.form.result = '';
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-edit-form {
  ::v-deep {
    .el-dialog__body {
      height: 200px;
      overflow: auto;
    }
  }
  .table-data {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;

    h1 {
      font-size: 1.6em;
      text-align: center;
      margin: 8px 0;
    }

    tr {
      width: 100%;

      > td {
        height: 40px;
        border: 1px solid #cecece;

        &:nth-child(2n-1) {
          width: 105px;
          color: #000;
          padding-right: 12px;
          text-align: center;
          line-height: 1;
        }

        &:nth-child(2n) {
          padding: 8px;
        }
      }

      &.title-tr {
        > td {
          color: #000;
          text-align: center;
        }
      }

      &.value-tr {
        > td {
          color: rgb(96, 98, 102);
          text-align: center;
        }
      }

      .title {
        font-weight: 700;
        letter-spacing: 3px;
        font-size: 16px;
      }
    }
  }

  .table-data tr > td {
    line-height: 16px !important;
  }
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
