<template>
  <div class="template-two-zone">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :showCheckbox="true"
        @nodeCheck="clickItemTree"
      >
      </new-base-search-tree>
    </div>
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
      >
      </new-ts-search-bar>
      <div class="analysis flex-column">
        <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
          <div class="flex-column">
            <colmun-head title="结果分析" background="#fff" />
            <div class="analysis-top">
              <div
                class="top-item flex-column"
                v-for="(item, index) in topList"
                :key="index"
              >
                <span class="value">{{ item.value }}</span>
                <span class="name">{{ item.name }}</span>
              </div>
            </div>
            <colmun-head title="上报科室分布" background="#fff" />
            <div class="analysis-echarts">
              <div class="echarts" ref="echarts1"></div>
              <div class="echarts" ref="echarts2"></div>
            </div>
            <colmun-head title="趋势分析" background="#fff" />
            <div class="analysis-lines" ref="echarts3"></div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
<script>
import table from './mixins/table';
export default {
  mixins: [table],
  data() {
    return {
      treeCode: '',
      apiFunction: this.ajax.getDeptTreeList
    };
  },
  methods: {
    async refresh() {
      await this.search();
    },
    clickItemTree(node) {
      this.treeCode = node.map(e => e.code).join(',');
      this.search();
    },
    reset() {
      this.$refs.searchTree.treeClass.checkAllNodes(false);
      return {};
    },
    search() {
      this.$nextTick(() => {
        this.renderEcharts1();
        this.renderEcharts2();
        this.renderEcharts3();
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/assets/css/template.scss';
/deep/ {
  .analysis {
    flex: 1;
    height: 200px;
    .analysis-top {
      padding: 8px;
      background: #e8ebfa;
      display: flex;
      margin-bottom: 8px;
      border-radius: 8px;
      .top-item {
        flex: 1;
        justify-content: center;
        align-items: center;
        .name {
          color: #000;
        }
        .value {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
    .analysis-echarts,
    .analysis-lines {
      flex: 1;
    }
    .analysis-lines {
      min-height: 300px;
    }
    .analysis-echarts {
      display: flex;
      .echarts {
        flex: 1;
        min-height: 200px;
      }
    }
  }
}
</style>
