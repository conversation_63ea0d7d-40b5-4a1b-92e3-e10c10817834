import Decimal from 'decimal.js';
import moment from 'moment';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      tableData: [],
      role: {
        // 扣分人员
        deduct: {
          approveColumns: 'deductColumns',
          rectificationColumns: ''
        },
        // 秘书审核人员
        examine: {
          approveColumns: 'examineColumns',
          rectificationColumns: ''
        },
        // 科主任，护士长
        correction: {
          approveColumns: 'correctionColumns',
          rectificationColumns: ''
        },
        // 复核的
        ordinary: {
          approveColumns: 'correctionColumnsEdit',
          rectificationColumns: ''
        },
        // 最大权限-编辑s
        masterRole: {
          approveColumns: 'masterColumns',
          rectificationColumns: ''
        },
        // 查看的
        details: {
          approveColumns: 'ordinaryColumns',
          rectificationColumns: ''
        },
        // 补录
        admission: {
          approveColumns: 'ordinaryColumns',
          rectificationColumns: ''
        }
      },
      pickerOptions: {
        disabledDate: this.disabledDate
      },
      selectData: [],
      groupList: [],
      sumScore: 0,
      sumPoint: 0
    };
  },
  computed: {
    userInfo() {
      return this.$getParentStoreInfo('userInfo');
    },
    canSubmit() {
      if (this.type == 'details') return false;
      if (this.type == 'examine') {
        let obj = this.form.roundsTaskGroupList?.find(e => e.status == 0);
        if (obj) {
          return false;
        }
      }
      return true;
    },
    canSubmit1() {
      if (this.type != 'examine') return false;
      let obj = this.form.roundsTaskGroupList?.find(e => e.status == 0);
      if (obj) {
        return false;
      }
      return true;
    },
    canSubmit2() {
      if (this.type != 'correction') return false;
      return true;
    }
  },
  methods: {
    async getGroupList() {
      this.form.roundsTaskGroupList.forEach(async item => {
        let res = await this.ajax.getRoundsGroupDetails(item.groupId);
        let group = res.object.roundsGroupMemberList.find(
          e => e.hospArea == this.form.hospArea
        );
        let nameList = group.groupMemberName.split(',');
        let options = group.groupMemberCode.split(',').map((e, index) => {
          return {
            code: `${e}-${item.groupId}`,
            name: nameList[index]
          };
        });
        this.groupList.push({
          groupId: item.groupId,
          groupName: res.object.groupName,
          options
        });
      });
    },
    refreshTable() {
      this.$emit('refresh');
    },
    // 补录的数据处理
    formatAdmission(data) {
      let datas = deepClone(data);
      let schedulingGroupUserCode = this.selectData
        .map(e => e.split('-')[0])
        .join(',');
      let schedulingGroupUserName = [];
      let taskGroupList = {};
      this.selectData.forEach(i => {
        let groupId = i.split('-')[1];
        let code = i.split('-')[0];
        let name = this.groupList
          .find(g => g.groupId == groupId)
          .options.find(j => j.code == i).name;
        schedulingGroupUserName.push(name);
        if (!taskGroupList[groupId]) {
          taskGroupList[groupId] = {
            code: [code],
            name: [name]
          };
        } else {
          taskGroupList[groupId].code.push(code);
          taskGroupList[groupId].name.push(name);
        }
      });
      let names = [];
      datas.roundsTaskGroupList.forEach(i => {
        if (taskGroupList[i.groupId]) {
          i.groupUserCode = taskGroupList[i.groupId].code.join(',');
          i.groupUserName = taskGroupList[i.groupId].name.join(',');
        } else {
          names.push(`【${i.groupName}】`);
        }
      });
      return {
        id: datas.id,
        schedulingGroupUserCode,
        schedulingGroupUserName: schedulingGroupUserName.join(','),
        roundsTaskGroupList: datas.roundsTaskGroupList,
        names
      };
    },
    formateData() {
      // 计算总分，得分
      if (this.form.status != 1 && this.form.status != 0) {
        let scrol = this.form.roundsTaskGroupList?.filter(
          e => e.groupName != '护理组'
        );
        let sumScrol = scrol.reduce(
          (acc, item) =>
            new Decimal(acc).plus(new Decimal(item.hjScore || 0)).toNumber(),
          0
        );
        let sumScrol1 = scrol.reduce(
          (acc, item) =>
            new Decimal(acc)
              .plus(new Decimal(item.hjDeductPoints || 0))
              .toNumber(),
          0
        );
        this.sumPoint = sumScrol;
        this.sumScore = new Decimal(sumScrol)
          .plus(new Decimal(sumScrol1 || 0))
          .toNumber();
      }
      let obj = this.form.roundsTaskGroupList?.filter(
        e => e.groupUserCode.indexOf(this.userInfo.employeeNo) > -1
      );
      let data = [];
      if (this.type == 'deduct') {
        // 扣分
        let backList = [];
        let backIndex = obj?.findIndex(e => e.status == 0);
        obj?.forEach(e => {
          e.roundsTaskGroupRulesList.forEach(i => {
            i.groupName = e.groupName;
            i.existingProblems = i.existingProblems || '无';
            i.checked = false;
          });
          if (backIndex >= 0 && e.status == 0) {
            backList.push({
              groupName: e.groupName,
              returnReason: e.returnReason
            });
            e.status = 2;
            data = [...data, ...e.roundsTaskGroupRulesList];
          }
          if (backIndex == -1) {
            e.status = 2;
            data = [...data, ...e.roundsTaskGroupRulesList];
          }
        });
        this.backList = backList;
        this.tableData = data;
      } else if (this.type == 'correction') {
        // 整改
        let groupList = this.form.roundsTaskGroupList.filter(e => {
          if (e.replyPerson == '1') {
            return this.form.directorUserCode == this.userInfo.employeeNo;
          } else if (e.replyPerson == '2') {
            return this.form.nurseUserCode == this.userInfo.employeeNo;
          } else if (e.replyPerson == '3') {
            return true;
          }
        });
        let ruleList = [];
        groupList.forEach(e => {
          e.status = 3;
          let groupRulesList = e.roundsTaskGroupRulesList.filter(
            i => i.deductPoints > 0
          );
          if (groupRulesList.length == 0) {
            groupRulesList = [
              {
                groupName: e.groupName,
                hjScore: e.hjScore,
                hjDeductPoints: e.hjDeductPoints,
                status: 3,
                roundsContent: '无扣项',
                existingProblems: '无',
                deductPoints: 0,
                causeAnalysis: '无',
                rectificationMeasures: '无',
                rectificationFiles: ''
              }
            ];
          } else {
            groupRulesList.forEach(i => {
              i.groupName = e.groupName;
              i.hjScore = e.hjScore;
              i.hjDeductPoints = e.hjDeductPoints;
              i.status = 3;
              // --start 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
              if (e.causeAnalysis) {
                // 整改数据
                i.causeAnalysis = e.causeAnalysis;
                i.rectificationMeasures = e.rectificationMeasures;
                i.rectificationFiles = e.rectificationFiles;
              }
              // -end 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
            });
            // --start 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
            if (e.causeAnalysis) {
              e.causeAnalysis = '';
            }
            // -end 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
          }
          ruleList = [...ruleList, ...groupRulesList];
        });
        this.tableData = ruleList;
      } else if (this.type == 'ordinary') {
        // 复检
        let ruleList = [];
        this.form.roundsTaskGroupList.forEach(e => {
          // 过滤自己的组
          if (e.groupUserCode.indexOf(this.userInfo.employeeNo) > -1) {
            let groupRulesList = e.roundsTaskGroupRulesList.filter(
              i => i.deductPoints > 0
            );
            e.status = 4;
            if (groupRulesList.length == 0) {
              groupRulesList = [
                {
                  groupName: e.groupName,
                  hjScore: e.hjScore,
                  hjDeductPoints: e.hjDeductPoints,
                  status: 4,
                  roundsContent: '无扣项',
                  existingProblems: '无',
                  deductPoints: 0,
                  causeAnalysis: '无',
                  rectificationMeasures: '无',
                  rectificationFiles: '',
                  rectificationResults: '无扣分项，无需整改',
                  rectificationScore: 0,
                  remarks: '',
                  evaluationFiles: ''
                }
              ];
            } else {
              groupRulesList.forEach(i => {
                i.groupName = e.groupName;
                i.hjScore = e.hjScore;
                i.hjDeductPoints = e.hjDeductPoints;
                i.evaluationTime = moment().format('YYYY-MM-DD HH:mm:ss');
                i.evaluatorCode = this.userInfo.employeeNo;
                i.evaluator = this.userInfo.employeeName;
                i.status = 4;
                // --start 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
                if (e.causeAnalysis) {
                  // 整改数据
                  i.causeAnalysis = e.causeAnalysis;
                  i.rectificationMeasures = e.rectificationMeasures;
                  i.rectificationFiles = e.rectificationFiles;
                }
                if (e.rectificationResults) {
                  // 复检数据
                  if (e.rectificationScore == 100) {
                    i.rectificationResults = '已整改';
                  } else if (e.rectificationScore == 0) {
                    i.rectificationResults = '未整改';
                  } else {
                    i.rectificationResults = '部分整改';
                  }
                  i.remarks = e.rectificationResults;
                  i.evaluationFiles = e.evaluationFiles;
                  i.evaluator = e.evaluator;
                  i.evaluationTime = e.evaluationTime;
                }
                // -end 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
              });
              // --start 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
              if (e.causeAnalysis) {
                e.causeAnalysis = '';
              }
              if (e.rectificationResults) {
                e.rectificationResults = '';
              }
              // -end 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
            }
            ruleList = [...ruleList, ...groupRulesList];
          }
        });
        this.tableData = ruleList;
        // this.tableData = list;
      } else if (this.type != 'examine') {
        // 编辑 详情
        let ruleList = [];
        this.form.roundsTaskGroupList.forEach(e => {
          let groupRulesList = e.roundsTaskGroupRulesList.filter(
            i => i.deductPoints > 0
          );
          if (groupRulesList.length == 0) {
            let label = '无扣项';
            if (
              this.type == 'details' &&
              this.form.status == '1' &&
              e.status == '1'
            ) {
              label = '待提交扣分及内容';
            }
            groupRulesList = [
              {
                groupName: e.groupName,
                hjScore: e.hjScore || 0,
                hjDeductPoints: e.hjDeductPoints || 0,
                roundsContent: `${label}`,
                existingProblems: '无',
                deductPoints: 0,
                causeAnalysis: '无',
                rectificationMeasures: '无',
                rectificationFiles: '',
                rectificationResults: `${label}`,
                rectificationScore: 0,
                remarks: '',
                evaluationFiles: ''
              }
            ];
          } else {
            groupRulesList.forEach(i => {
              i.groupName = e.groupName;
              i.hjScore = e.hjScore;
              i.hjDeductPoints = e.hjDeductPoints;
              // --start 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
              if (e.causeAnalysis) {
                // 整改数据
                i.causeAnalysis = e.causeAnalysis;
                i.rectificationMeasures = e.rectificationMeasures;
                i.rectificationFiles = e.rectificationFiles;
              }
              if (e.rectificationResults) {
                // 复检数据
                if (e.rectificationScore == 100) {
                  i.rectificationResults = '已整改';
                } else if (e.rectificationScore == 0) {
                  i.rectificationResults = '未整改';
                } else {
                  i.rectificationResults = '部分整改';
                }
                i.remarks = e.rectificationResults;
                i.evaluationFiles = e.evaluationFiles;
                i.evaluator = e.evaluator;
                i.evaluationTime = e.evaluationTime;
              }
              // -end 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
            });
            // --start 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
            if (e.causeAnalysis) {
              e.causeAnalysis = '';
            }
            if (e.rectificationResults) {
              e.rectificationResults = '';
            }
            // -end 同步历史数据问题(因为需求变化-信息颗粒度从组调整到细则)
          }
          ruleList = [...ruleList, ...groupRulesList];
        });
        this.tableData = ruleList;
      } else {
        // 秘书长审核
        this.tableData = this.form.roundsTaskGroupList;
      }
    },
    disabledDate(time) {
      if (moment(time).isBefore(moment(this.form.roundsTime))) {
        return true;
      } else {
        return false;
      }
    },
    cellClassName({ columnIndex }) {
      if (this.columnsApprove[columnIndex]?.label == '附件') {
        return '';
      }
      if (!this.columnsApprove[columnIndex]?.editabled) {
        return 'disabled_cell';
      }
      return '';
    },
    cellClassName1({ columnIndex }) {
      if (this.columnsRectification[columnIndex]?.label == '附件') {
        return '';
      }
      if (!this.columnsRectification[columnIndex]?.editabled) {
        return 'disabled_cell';
      }
      return '';
    },
    // 点击评分细则单元格 自动回填评分细则内容到存在问题里面
    cellClick({ row, columnIndex }) {
      let obj = this.columnsApprove.find(e => e.prop == 'existingProblems');
      if (
        this.columnsApprove[columnIndex].prop == 'scoringRubric' &&
        obj &&
        obj.editabled
      ) {
        row.checked = !row.checked;
        if (
          row.checked &&
          row.existingProblems.indexOf(row.scoringRubric) == -1
        ) {
          // 如果被点击且 存在问题没有评分细则内容
          if (row.existingProblems == '' || row.existingProblems == '无') {
            row.existingProblems = row.scoringRubric + '\n';
          } else {
            row.existingProblems += row.scoringRubric + '\n';
          }
        } else if (!row.checked) {
          // 被点击了取消 去除评分细则内容
          row.existingProblems = row.existingProblems.replace(
            `${row.scoringRubric}\n`,
            ''
          );
          if (row.existingProblems == '') {
            row.existingProblems = '无';
          }
        }
        // 计算分数
        const value = row[this.defaultSpanKey];
        let scrol = this.tableData.filter(
          e =>
            e[this.defaultSpanKey] === value &&
            row.groupId == e.groupId &&
            e.checked
        );
        let sumScrol = scrol.reduce(
          (acc, item) =>
            new Decimal(acc)
              .plus(new Decimal(item.deductPointsLimit || 0))
              .toNumber(),
          0
        );
        let resNum = Number(
          new Decimal(row.scoreValue).minus(new Decimal(sumScrol)).toString()
        );
        if (resNum < 0) {
          resNum = 0;
          sumScrol = row.scoreValue;
        }
        row.deductPoints = sumScrol;
        row.score = resNum;
        this.asyncOtherData(row, 'deductPoints');
        this.asyncOtherData(row, 'score');
        this.asyncOtherData(row, 'existingProblems');
      }
    },
    // 点击单元格编辑自动聚焦
    handleEditActivated({ columnIndex }) {
      if (
        this.columnsApprove[columnIndex].editabled &&
        this.columnsApprove[columnIndex].slotName != 'files'
      ) {
        this.$nextTick(() => {
          this.$refs[this.columnsApprove[columnIndex].slotName].focus();
        });
      }
    },
    handlePersonSelect(item) {
      this.$set(this.form, 'nurseUserCode', item.employeeNo);
      this.$set(this.form, 'nurseUserName', item.employeeName);
    },
    handlePersonSelect1(item) {
      this.$set(this.form, 'directorUserCode', item.employeeNo);
      this.$set(this.form, 'directorUserName', item.employeeName);
    },
    async handleGetPersonList(data) {
      let res = await this.ajax.getBasicMyEmployeeList({
        employeeStatusList: [],
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    calculateScore(row, maxKey, inputKey, resKey, column) {
      row[resKey] = Number(
        new Decimal(row[maxKey]).minus(new Decimal(row[inputKey])).toString()
      );
      this.asyncOtherData(row, resKey);
      this.asyncOtherData(row, inputKey);
    },
    inputKeys(row, key) {
      this.$set(row, `${key}`, row[key]);
      this.asyncOtherData(row, key);
      this.$forceUpdate();
    },
    asyncOtherData(row, key) {
      // if (this.type != 'deduct') return;
      const value = row[this.defaultSpanKey];
      this.tableData.forEach(item => {
        if (
          item[this.defaultSpanKey] === value &&
          row.groupId == item.groupId
        ) {
          item[key] = row[key];
        }
      });
      this.$forceUpdate();
    },
    formaterrData(data) {
      let tableData = deepClone(this.tableData);
      let groupIds = tableData.map(e => {
        return e.groupId;
      });
      groupIds = groupIds.filter(function(item, index) {
        return groupIds.indexOf(item, 0) == index;
      });
      let submitTableData = [];
      groupIds.forEach(item => {
        let rules = data.roundsTaskGroupList.find(e => e.groupId == item);
        let score = 0;
        let existingProblems = '';
        let ids = rules.roundsTaskGroupRulesList.map(e => {
          return e.roundsContent;
        });
        ids = ids.filter(function(item, index) {
          return ids.indexOf(item, 0) == index;
        });
        ids.forEach(e => {
          let ruleRes = rules.roundsTaskGroupRulesList.find(
            r => r.roundsContent == e
          );
          score += ruleRes.score;
          if (
            ruleRes.existingProblems != '' &&
            ruleRes.existingProblems != '无'
          ) {
            const newlineRegex = /\r?\n|\r$/;
            if (newlineRegex.test(ruleRes.existingProblems)) {
              existingProblems += ruleRes.existingProblems;
            } else {
              existingProblems += ruleRes.existingProblems + '\n';
            }
          }
        });
        rules.score = score;
        if (existingProblems == '') {
          existingProblems = '无';
        }
        rules.existingProblems = existingProblems;
        submitTableData.push({ ...rules });
      });
      data.roundsTaskGroupList = submitTableData;
    },
    mergeRowMethod({ row, _rowIndex, column, visibleData }) {
      if (this.type == 'examine') return { rowspan: 1, colspan: 1 };
      const fields = this.spanFields;
      const List = this.copyFields;
      let cellValue = row[column.field];
      if (List.indexOf(column.field) > -1) cellValue = row[this.defaultSpanKey];
      if (cellValue && fields.includes(column.field)) {
        let keyField = column.field;
        if (List.indexOf(column.field) > -1) keyField = this.defaultSpanKey;
        const prevRow = visibleData[_rowIndex - 1];
        let nextRow = visibleData[_rowIndex + 1];
        if (
          prevRow &&
          prevRow[keyField] === cellValue &&
          prevRow.groupName === row.groupName
        ) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (
            nextRow &&
            nextRow[keyField] === cellValue &&
            nextRow.groupName === row.groupName
          ) {
            nextRow = visibleData[++countRowspan + _rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    refreshRowData(data) {
      let rules = this.form.roundsTaskGroupList.find(
        e => e.groupId == data[0].groupId
      );
      let score = 0;
      let ids = data.map(e => {
        return e.roundsContent;
      });
      ids = ids.filter(function(item, index) {
        return ids.indexOf(item, 0) == index;
      });
      ids.forEach(e => {
        let ruleRes = data.find(r => r.roundsContent == e);
        score += ruleRes.score;
      });
      rules.score = score;
    }
  }
};
