<template>
  <el-drawer
    custom-class="ts-custom-default-drawer drawer-add-custom"
    direction="rtl"
    size="60%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="ruleForm" :model="form" labelWidth="100px">
            <div class="form-card-box">
              <colmun-head title="事件信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="12">
                  <ts-form-item
                    label="事件日期"
                    prop="eventsDate"
                    :rules="rules.required"
                  >
                    <ts-date-picker
                      class="date-picker-show-h-m"
                      style="width: 100%;"
                      v-model="form.eventsDate"
                      placeholder="请选择事件日期"
                      clearable
                      showTime
                      :disabled="isDetail"
                      format="YYYY-MM-DD HH:mm"
                      valueFormat="YYYY-MM-DD HH:mm"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="事件类型"
                    prop="eventsType"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.eventsType"
                      style="width: 100%;"
                      :disabled="isDetail"
                    >
                      <ts-option
                        v-for="(item, index) in eventsTypeList"
                        :label="item.itemName"
                        :value="item.itemCode"
                        :key="index"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item
                    label="事件名称"
                    prop="eventsName"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.eventsName"
                      placeholder="请输入事件名称"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="事件描述">
                    <ts-input
                      v-model="form.eventsDscr"
                      placeholder="请输入事件描述"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="事件影响">
                    <ts-input
                      v-model="form.eventsInfl"
                      placeholder="请输入事件影响"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="处理措施">
                    <ts-input
                      v-model="form.dspoMes"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="调查与分析">
                    <ts-input
                      v-model="form.srvyAna"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="改进与建议">
                    <ts-input
                      v-model="form.imprAdv"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="200"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="附件上传" prop="fileKey">
                    <base-upload
                      class="upload_Button"
                      ref="files"
                      v-model="form.fileKey"
                      :onlyRead="isDetail"
                      moduleName="healthcare"
                    >
                      <ts-button type="primary">
                        <i class="el-icon-upload"></i>
                        上传附件
                      </ts-button>
                    </base-upload>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div class="form-card-box">
              <colmun-head title="相关患者信息">
                <template slot="right">
                  <ts-button
                    v-show="!isDetail"
                    type="primary"
                    @click="() => handleImport('1')"
                  >
                    导入
                  </ts-button>
                </template>
              </colmun-head>
              <form-table
                ref="FormTable"
                class="mrgT8 pdR8"
                :formData="form"
                operateDataKey="patns"
                :disabled="true"
                :loaclColumns="[]"
                :columns="patientColumns"
              >
                <template slot="action-bottom">
                  <ts-button
                    type="primary"
                    @click="handleAddTable('FormTable')"
                    v-if="!isDetail"
                  >
                    添加行
                  </ts-button>
                </template>
                <!-- 住院号 -->
                <template v-slot:inpNo="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`patns.${index}.${column.property}`"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </template>
                <!-- 床号 -->
                <template v-slot:bedno="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`patns.${index}.${column.property}`"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </template>
                <!-- 患者姓名 -->
                <template v-slot:patnName="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`patns.${index}.${column.property}`"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </template>
                <!-- 患者性别 -->
                <template v-slot:patnGend="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`patns.${index}.${column.property}`"
                  >
                    <ts-input
                      style="width: 80px !important; min-width: 80px !important;"
                      v-model="row[column.property]"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </template>
                <!-- 所属科室 -->
                <template v-slot:deptName="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`patns.${index}.${column.property}`"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </template>
                <!-- 操作 -->
                <template
                  v-slot:actions="{ row, column, index }"
                  v-if="!isDetail"
                >
                  <ts-form-item class="flex-item" label="" label-width="0">
                    <div @click="delRow('patns', index)" class="action del">
                      删除
                    </div>
                  </ts-form-item>
                </template>
              </form-table>
            </div>
            <div class="form-card-box">
              <colmun-head title="相关参与人员">
                <template slot="right">
                  <ts-button
                    v-if="!isDetail"
                    type="primary"
                    @click="() => handleImport('2')"
                  >
                    导入
                  </ts-button>
                </template>
              </colmun-head>
              <form-table
                ref="FormTable1"
                class="mrgT8 pdR8"
                :formData="form"
                operateDataKey="emps"
                :disabled="true"
                :loaclColumns="[]"
                :columns="joinColumns"
              >
                <template slot="action-bottom">
                  <ts-button
                    v-if="!isDetail"
                    type="primary"
                    @click="handleAddTable('FormTable1')"
                  >
                    添加行
                  </ts-button>
                </template>
                <!-- 工号 -->
                <template v-slot:employeeNo="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`emps.${index}.${column.property}`"
                    :rules="rules.required"
                  >
                    <base-select
                      v-model="row[column.property]"
                      :inputText.sync="row.employeeNo"
                      :loadMethod="handleGetPersonList"
                      label="empCode"
                      value="empCode"
                      searchInputName="seachKey"
                      :clearable="false"
                      :disabled="isDetail"
                      placeholder="请选择相关参与人"
                      @select="item => handlePersonSelect(item, row)"
                    >
                      <template slot-scope="options">
                        <span>
                          {{ options.data['empCode'] }}--
                          {{ options.data['empName'] }}
                        </span>
                      </template>
                    </base-select>
                  </ts-form-item>
                </template>
                <!-- 姓名 -->
                <template v-slot:employeeName="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`emps.${index}.${column.property}`"
                  >
                    <ts-input v-model="row[column.property]" disabled />
                  </ts-form-item>
                </template>
                <!-- 性别 -->
                <template v-slot:gender="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`emps.${index}.${column.property}`"
                  >
                    <ts-input
                      style="width: 80px !important; min-width: 80px !important;"
                      v-model="row[column.property]"
                      disabled
                    />
                  </ts-form-item>
                </template>
                <!-- 岗位 -->
                <template v-slot:personalIdentity="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`emps.${index}.${column.property}`"
                  >
                    <ts-input v-model="row[column.property]" disabled />
                  </ts-form-item>
                </template>
                <!-- 组织机构 -->
                <template v-slot:deptName="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`emps.${index}.${column.property}`"
                  >
                    <ts-input v-model="row[column.property]" disabled />
                  </ts-form-item>
                </template>
                <!-- 操作 -->
                <template
                  v-slot:actions="{ row, column, index }"
                  v-if="!isDetail"
                >
                  <ts-form-item class="flex-item" label="" label-width="0">
                    <div @click="delRow('emps', index)" class="action del">
                      删除
                    </div>
                  </ts-form-item>
                </template>
              </form-table>
            </div>
            <div v-if="type != 'add'" class="form-card-box">
              <colmun-head title="记录信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="12">
                  <ts-form-item label="工号">
                    <ts-input v-model="form.createUser" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="姓名">
                    <ts-input v-model="form.createUserName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="记录日期">
                    <ts-input v-model="form.createDate" disabled />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div v-if="form.approvalUser" class="form-card-box">
              <colmun-head title="审核信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="12">
                  <ts-form-item label="工号">
                    <ts-input v-model="form.approvalUser" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="姓名">
                    <ts-input v-model="form.approvalUserName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="审核日期">
                    <ts-input v-model="form.approvalTime" disabled />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button type="primary" @click="submit" v-if="!isDetail"
          >提 交</ts-button
        >
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </div>
    </div>

    <!-- 公用导入 -->
    <base-import
      ref="baseImport1"
      :ImportConfiguration="patientImportConfiguration"
      @refresh="handleImportPatientSuccess"
    />

    <!-- 公用导入 -->
    <base-import
      ref="baseImport2"
      :ImportConfiguration="joinImportConfiguration"
      @refresh="handleImportJoinSuccess"
    />
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import FormTable from '@/components/form-table/form-table.vue';
export default {
  components: { FormTable },
  props: {
    eventsTypeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,

      title: '',
      type: '',
      form: {
        patns: [],
        emps: []
      },
      isDetail: false,
      rules: {
        required: { required: true, message: '必填' }
      },
      patientColumns: [
        {
          prop: 'inpNo',
          label: '住院号',
          required: true
        },
        {
          prop: 'bedno',
          label: '床号'
        },
        {
          prop: 'patnName',
          label: '患者姓名'
        },
        {
          prop: 'patnGend',
          label: '性别',
          width: '100px'
        },
        {
          prop: 'deptName',
          label: '所属科室'
        },
        {
          prop: 'actions',
          label: '操作',
          width: '100px'
        }
      ],
      patientImportConfiguration: {
        importTempalteApi: '/ts-hrms/api/majorEventsPatn/downloadTemplate',
        importTempalteName: '医务大事记相关患者信息导入模板',
        importApi: 'importMajorEventsPatn'
      },

      joinColumns: [
        {
          prop: 'employeeNo',
          label: '工号',
          required: true
        },
        {
          prop: 'employeeName',
          label: '姓名'
        },
        {
          prop: 'gender',
          label: '性别',
          width: '100px'
        },
        {
          prop: 'personalIdentity',
          label: '岗位名称'
        },
        {
          prop: 'deptName',
          label: '所属科室'
        },
        {
          prop: 'actions',
          label: '操作',
          width: '100px'
        }
      ],
      joinImportConfiguration: {
        importTempalteApi: '/ts-hrms/api/majorEventsEmp/downloadTemplate',
        importTempalteName: '医务大事记相关参与人员导入模板',
        importApi: 'importMajorEventsEmp'
      }
    };
  },
  methods: {
    async open({ data = {}, title, type }) {
      if (Object.keys(data).length != 0) {
        let res = await this.ajax.majorEventsDetail(data.id);
        if (res.success) {
          this.form = res.object || {};
        }
      } else {
        this.$set(this, 'form', {
          patns: [],
          emps: []
        });
      }
      if (type == 'details') {
        this.isDetail = true;
      }
      this.title = title;
      this.type = type;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
      });
    },

    inputBlur(event, formName, setKey) {
      let value = parseFloat(event.target.value);
      this.$set(this[formName], setKey, isNaN(value) ? '' : value);
    },

    handleAddTable(ref) {
      this.$refs[ref].handleFormTableAddRow();
    },

    delRow(key, index) {
      this.form[key].splice(index, 1);
    },

    async handleGetPersonList(data) {
      let res = await this.ajax.oaGetEmployeeList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$newMessage('error', res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },

    handleImport(type) {
      this.$refs[`baseImport${type}`].open({
        title: '导入',
        increment: true,
        quantity: false
      });
    },

    handleImportPatientSuccess(importRes) {
      this.form.patns = [...this.form.patns, ...importRes];
    },

    handlePersonSelect(item, row) {
      let existUserCode = this.form.emps.filter(
        f => f.employeeNo === item.empCode
      );
      if (existUserCode.length > 1) {
        let tips = item.empName || `工号为 ${item.empCode}`;
        this.$newMessage('warning', `相关参与人员 已存在 ${tips}`);

        let findIndex = this.form.emps.findIndex(item => item === row);
        if (findIndex != -1) this.form.emps.splice(findIndex, 1);
        return;
      }

      this.$set(row, 'employeeName', item.empName);
      this.$set(row, 'deptName', item.empDeptName);
      this.$set(row, 'personalIdentity', item.empDutyName);
      this.$set(
        row,
        'gender',
        row.empSex == '1' ? '女' : row.empSex == '0' ? '男' : '未知'
      );
    },

    handleImportJoinSuccess(importRes) {
      let existNoSet = new Set(this.form.emps.map(m => m.employeeNo));

      let exist = importRes.filter(({ employeeNo }) =>
        existNoSet.has(employeeNo)
      );
      let userCodes = exist.map(m => m.employeeNo).join(',');
      setTimeout(() => {
        userCodes &&
          this.$newMessage(
            'warning',
            `获奖名单已存在工号 ${userCodes}, 已去重`
          );
      }, 400);

      this.form.emps = [
        ...this.form.emps,
        ...importRes.filter(({ employeeNo }) => !existNoSet.has(employeeNo))
      ];
    },

    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);
        if (data.patns.length == 0) {
          this.$newMessage('warning', '请【记录】相关患者信息');
          return;
        }
        if (data.emps.length == 0) {
          this.$newMessage('warning', '请【记录】相关参与人员信息');
          return;
        }
        let Api = this.ajax.majorEventsSave;
        if (this.type == 'edit') {
          Api = this.ajax.majorEventsUpdate;
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },

    close() {
      this.type = undefined;
      this.form = {};
      this.visible = false;
      this.isDetail = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.date-picker-show-h-m {
  width: 100% !important;
  ::v-deep .ant-calendar-time-picker-select {
    width: 50% !important;
  }
  ::v-deep .ant-calendar-time-picker-select:last-child {
    display: none;
  }
}
::v-deep {
  .drawer-add-custom {
    .el-input {
      min-width: 100%;
    }
    .action {
      width: 100%;
      text-align: center;
      color: rgb(82, 96, 255);
      cursor: pointer;
      &.del {
        color: red;
      }
    }
  }
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }
}
</style>
