<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-handover-matters"
    direction="rtl"
    size="90%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <ts-form ref="ruleForm" :model="form" labelWidth="110px">
          <div class="form-card-box">
            <colmun-head title="交班信息" />
            <ts-row class="mrgT8 pdR8">
              <ts-col :span="6">
                <ts-form-item
                  label="交接班时间"
                  prop="shiftDate"
                  :rules="rules.required"
                >
                  <ts-date-picker
                    v-model="form.shiftDate"
                    placeholder="请选择会诊开始时间"
                    clearable
                    :showTime="{ format: 'HH:mm' }"
                    :disabled="isDetail"
                    format="YYYY-MM-DD HH:mm"
                    valueFormat="YYYY-MM-DD HH:mm"
                  />
                </ts-form-item>
              </ts-col>
              <ts-col :span="6">
                <ts-form-item label="交接工号" prop="shiftUserCode">
                  <ts-input v-model="form.shiftUserCode" disabled />
                </ts-form-item>
              </ts-col>
              <ts-col :span="6">
                <ts-form-item
                  label="交班姓名"
                  prop="shiftUserName"
                  :rules="rules.required"
                >
                  <ts-input v-model="form.shiftUserName" disabled />
                </ts-form-item>
              </ts-col>
              <ts-col :span="6">
                <ts-form-item label="交班职称" prop="shiftJobtitle">
                  <ts-input v-model="form.shiftJobtitle" disabled />
                </ts-form-item>
              </ts-col>
            </ts-row>
          </div>
          <div class="form-card-box">
            <colmun-head
              :title="form.status == '2' ? '已接班信息' : '待接班信息'"
            />
            <ts-row class="mrgT8 pdR8">
              <ts-col :span="6">
                <ts-form-item
                  label="接班姓名"
                  prop="receiveUserName"
                  :rules="rules.required"
                >
                  <base-select
                    v-model="form.receiveUserCode"
                    :inputText.sync="form.receiveUserName"
                    :loadMethod="handleGetPersonList"
                    label="empCode"
                    value="empCode"
                    searchInputName="seachKey"
                    :clearable="false"
                    placeholder="请选择责任人"
                    style="width: 100%;"
                    :disabled="isDetail"
                    @select="handlePersonSelect"
                  >
                    <template slot-scope="options">
                      <p
                        style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                        :title="
                          `${options.data['empCode']}--${options.data['empDeptName']}--${options.data['empName']}`
                        "
                      >
                        {{ options.data['empCode'] }}--{{
                          options.data['empDeptName']
                        }}--{{ options.data['empName'] }}
                      </p>
                    </template>
                  </base-select>
                </ts-form-item>
              </ts-col>
              <ts-col :span="6">
                <ts-form-item label="指定接班工号" prop="receiveUserCode">
                  <ts-input v-model="form.receiveUserCode" disabled />
                </ts-form-item>
              </ts-col>
              <ts-col :span="6" v-if="form.status == '2'">
                <ts-form-item label="确认接班时间">
                  <ts-input v-model="form.updateDate" disabled />
                </ts-form-item>
              </ts-col>
            </ts-row>
          </div>
          <div class="form-card-box">
            <colmun-head title="患者信息">
              <template slot="right">
                <ts-button type="primary" v-if="!isDetail" @click="handleImport"
                  >导入</ts-button
                >
              </template>
            </colmun-head>
            <form-table
              ref="FormTable"
              class="mrgT8 pdR8"
              :formData="form"
              operateDataKey="patientList"
              :disabled="true"
              :loaclColumns="[]"
              :columns="cols"
            >
              <template slot="action-bottom">
                <ts-button
                  type="primary"
                  @click="handleAddTable('FormTable')"
                  v-if="!isDetail"
                >
                  添加行
                </ts-button>
              </template>
              <!-- 住院号 -->
              <template v-slot:inpNo="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input v-model="row[column.property]" v-if="!isDetail" />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 床号 -->
              <template v-slot:bedno="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input v-model="row[column.property]" v-if="!isDetail" />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 患者姓名 -->
              <template v-slot:patnName="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input v-model="row[column.property]" v-if="!isDetail" />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 患者性别 -->
              <template v-slot:patnGender="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input v-model="row[column.property]" v-if="!isDetail" />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 入院时间 -->
              <template v-slot:inHospital="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input v-model="row[column.property]" v-if="!isDetail" />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 病情程度 -->
              <template v-slot:degreeStatus="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input v-model="row[column.property]" v-if="!isDetail" />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 当前病情 -->
              <template v-slot:currentDegree="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input
                    v-if="!isDetail"
                    v-model="row[column.property]"
                    placeholder="请输入当前病情"
                    type="textarea"
                    class="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 治疗计划 -->
              <template v-slot:treatment="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input
                    v-if="!isDetail"
                    v-model="row[column.property]"
                    placeholder="请输入治疗计划"
                    type="textarea"
                    class="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 重要事件 -->
              <template v-slot:important="{ row, column, index }">
                <ts-form-item
                  class="flex-item"
                  label=""
                  label-width="0"
                  :prop="`patientList.${index}.${column.property}`"
                >
                  <ts-input
                    v-if="!isDetail"
                    v-model="row[column.property]"
                    placeholder="请输入重要事件"
                    type="textarea"
                    class="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                  <div class="table-cell" :title="row[column.property]" v-else>
                    {{ row[column.property] }}
                  </div>
                </ts-form-item>
              </template>
              <!-- 操作 -->
              <template
                v-slot:actions="{ row, column, index }"
                v-if="!isDetail"
              >
                <ts-form-item class="flex-item" label="" label-width="0">
                  <div @click="delRow('patientList', index)" class="action del">
                    删除
                  </div>
                </ts-form-item>
              </template>
            </form-table>
          </div>
          <div class="form-card-box">
            <colmun-head title="其他信息" />
            <ts-row class="mrgT8 pdR8">
              <ts-col :span="24">
                <ts-form-item label="沟通记录" style="margin-top: 8px;">
                  <ts-input
                    v-model="form.communicate"
                    placeholder="请输入沟通要点"
                    type="textarea"
                    class="textarea"
                    maxlength="200"
                    :disabled="isDetail"
                    show-word-limit
                  />
                </ts-form-item>
              </ts-col>
              <ts-col :span="24">
                <ts-form-item label="反馈与问题">
                  <ts-input
                    v-model="form.remark"
                    placeholder="请输入遇到的问题及建议改进措施"
                    type="textarea"
                    class="textarea"
                    maxlength="200"
                    :disabled="isDetail"
                    show-word-limit
                  />
                </ts-form-item>
              </ts-col>
              <ts-col :span="24" v-if="!isDetail">
                <ts-col :span="24">
                  <ts-form-item label="附件上传" prop="files">
                    <table-module-upload
                      ref="tableUpload"
                      v-model="form.files"
                      moduleName="qualification"
                    >
                      <div style="display: flex;">
                        <ts-button type="primary" icon="el-icon-upload"
                          >上传附件</ts-button
                        >
                        <p class="tooptip">
                          （附件大小建议不要超过{{ allowFileSize }}MB），
                          可允许上传文件后缀为{{ allowFileExtension }}
                        </p>
                      </div>
                    </table-module-upload>
                  </ts-form-item>
                </ts-col>
              </ts-col>
              <ts-col :span="24" v-else>
                <ts-col :span="24">
                  <ts-form-item prop="files" label="相关附件">
                    <file-list-batch ref="fileListBatch" :fileList="fileList" />
                  </ts-form-item>
                </ts-col>
              </ts-col>
            </ts-row>
          </div>
        </ts-form>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button
          type="primary"
          @click="submit(1)"
          v-if="!isDetail || type == 'succession'"
        >
          {{ type == 'succession' ? '接 班' : '提 交' }}
        </ts-button>
        <ts-button @click="submit(0)" v-if="!isDetail" class="wait"
          >暂 存</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
    <!-- 公用导入 -->
    <base-import
      ref="baseImport"
      :ImportConfiguration="ImportConfiguration"
      @refresh="handleImportSuccess"
    />
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import FormTable from '@/components/form-table/form-table.vue';
import TableModuleUpload from '@/components/table-module-upload';
import fileListBatch from './file-list-batch.vue';
import darwerHandoverMatters from '../mixins/darwer-handover-matters';
export default {
  mixins: [darwerHandoverMatters],
  components: { FormTable, TableModuleUpload, fileListBatch },
  computed: {
    allowFileSize() {
      return this.$getParentStoreInfo().globalSetting.allowFileSize || 100;
    },
    allowFileExtension() {
      return this.$getParentStoreInfo().globalSetting.allowFileExtension;
    },
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '',
      form: {
        patientList: []
      },
      isDetail: false,
      rules: {
        required: { required: true, message: '必填' }
      },
      Columns: [
        {
          prop: 'inpNo',
          label: '住院号',
          required: true,
          width: 100
        },
        {
          prop: 'bedno',
          label: '床号',
          required: true,
          width: 100
        },
        {
          prop: 'patnName',
          label: '患者姓名',
          width: 100
        },
        {
          prop: 'patnGender',
          label: '性别',
          width: 60
        },
        {
          prop: 'inHospital',
          label: '入院时间',
          width: 100
        },
        {
          prop: 'degreeStatus',
          label: '病情程度',
          width: 100
        },
        {
          prop: 'currentDegree',
          label: '当前病情'
        },
        {
          prop: 'treatment',
          label: '治疗计划'
        },
        {
          prop: 'important',
          label: '重要事件'
        },
        {
          prop: 'actions',
          label: '操作',
          width: 80
        }
      ],
      cols: [],
      ImportConfiguration: {
        importTempalteApi: '/ts-hrms/api/shiftPatient/downloadTemplate',
        importTempalteName: '患者信息导入模板',
        importApi: 'importShiftPatient'
      }
    };
  },
  methods: {
    async open({ data = null, title, type }) {
      if (type == 'details' || type == 'succession') {
        this.isDetail = true;
        this.cols = this.Columns.slice(0, this.Columns.length - 1);
      } else {
        this.cols = this.Columns;
      }
      if (!data) {
        this.form.shiftUserCode = this.userInfo.employeeNo;
        this.form.shiftUserName = this.userInfo.employeeName;
        this.form.shiftJobtitle = this.userInfo.jobtitleName;
      } else {
        await this.getData(data);
        await this.getFileList();
      }
      this.title = title;
      this.type = type;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
        if (type != 'details')
          this.$refs.tableUpload.handlePushFileData(this.tableFileList);
      });
    },
    async getData(data) {
      let res = await this.ajax.shiftWorkDetail(data.id);
      this.form = res.object || {};
    },
    handleAddTable(ref) {
      this.$refs[ref].handleFormTableAddRow();
    },
    handleImport() {
      this.$refs.baseImport.open({
        title: '导入患者信息',
        increment: true,
        quantity: false
      });
    },
    handleImportSuccess(importRes) {
      let existNoSet = new Set(this.form.patientList.map(m => m.inpNo));

      let exist = importRes.filter(({ inpNo }) => existNoSet.has(inpNo));
      let inpNo = exist.map(m => m.inpNo).join(',');
      setTimeout(() => {
        inpNo &&
          this.$newMessage('warning', `患者里面已有住院号 ${inpNo}, 已去重`);
      }, 400);

      this.form.patientList = [
        ...this.form.patientList,
        ...importRes.filter(({ inpNo }) => !existNoSet.has(inpNo))
      ];
    },
    delRow(key, index) {
      this.form[key].splice(index, 1);
    },
    async submit(status) {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);
        if (data.patientList.length == 0) {
          this.$newMessage('warning', '请【记录】相关患者信息');
          return;
        }
        let Api = this.ajax.shiftWorkSave;
        data.status = status;
        if (this.type == 'edit') {
          Api = this.ajax.shiftWorkUpdate;
        }
        if (this.type == 'succession') {
          Api = this.ajax.shiftWorkReceive;
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.type = undefined;
      this.form = {
        patientList: []
      };
      this.tableFileList = [];
      this.fileList = [];
      this.visible = false;
      this.isDetail = false;
      this.initData();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .darwer-handover-matters {
    .el-input {
      min-width: 100%;
    }
    .action {
      width: 100%;
      text-align: center;
      color: rgb(82, 96, 255);
      cursor: pointer;
      &.del {
        color: red;
      }
    }
    .upload-content {
      max-height: 400px;
      overflow: auto;
    }
    .el-upload--text {
      width: 100%;
      text-align: left;
    }
    .tooptip {
      width: 100%;
      margin: 0;
      color: #f59a23;
      word-wrap: break-word;
      word-break: break-all;
      text-align: left;
      line-height: 30px;
    }
  }
  .textarea {
    .el-textarea__inner {
      min-height: 60px !important;
      max-height: 60px !important;
    }
  }
  .wait {
    background: #18aa69;
  }
  .table-cell {
    min-height: 30px;
    line-height: 30px;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
