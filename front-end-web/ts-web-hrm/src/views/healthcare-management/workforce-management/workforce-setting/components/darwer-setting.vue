<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-setting"
    direction="rtl"
    size="60%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="ruleForm" :model="form" labelWidth="110px">
            <ts-row class="pdR8">
              <ts-col :span="24">
                <ts-form-item
                  label="排班管理员"
                  prop="manageName"
                  :rules="rules.required"
                >
                  <div class="select-item-content">
                    <div
                      class="selected-item"
                      v-for="(item, index) in form.manageNameList"
                      :key="index"
                    >
                      {{ item.empName }}
                      <i
                        class="el-icon-close"
                        @click="handleDeleteUser1(item, index)"
                      ></i>
                    </div>
                    <vxe-icon
                      name="square-plus"
                      status="primary"
                      style="font-size: 20px;"
                      @click="handleOpenSelPerson('manageName')"
                    ></vxe-icon>
                  </div>
                </ts-form-item>
              </ts-col>
              <ts-col :span="24">
                <ts-form-item
                  label="考勤组范围"
                  prop="scheduleName"
                  :rules="rules.required"
                >
                  <div class="select-item-content">
                    <div
                      class="selected-item"
                      v-for="(item, index) in form.scheduleNameList"
                      :key="index"
                    >
                      {{ item?.empName || item.name }}
                      <i
                        class="el-icon-close"
                        @click="handleDeleteUser(item)"
                      ></i>
                    </div>
                    <vxe-icon
                      name="square-plus"
                      status="primary"
                      style="font-size: 20px;"
                      @click="handleOpenSelPerson('scheduleName')"
                    ></vxe-icon>
                  </div>
                </ts-form-item>
              </ts-col>
              <ts-col :span="24">
                <ts-form-item label="备注" prop="authorityRemark">
                  <ts-input
                    v-model="form.authorityRemark"
                    placeholder="请输入备注"
                    type="textarea"
                    class="textarea"
                    maxlength="200"
                    show-word-limit
                  />
                </ts-form-item>
              </ts-col>
            </ts-row>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button type="primary" v-if="!isDetail" @click="submit"
          >提交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk1" />
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      isDetail: false,
      title: '',
      type: '',
      form: {
        scheduleNameList: [],
        manageNameList: []
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async getDetail(data) {
      this.form = deepClone(data) || {};
      let nameList = this.form.scheduleName?.split(',') || [];
      let deptList = [],
        empList = [];
      if (this.form.scheduleOrg) {
        deptList = this.form.scheduleOrg.split(',').map((e, index) => {
          return {
            id: e,
            type: 'dept',
            name: nameList[index]
          };
        });
      }
      if (this.form.scheduleUser) {
        let length = deptList.length || 0;
        empList = this.form.scheduleUser.split(',').map((e, index) => {
          return {
            empCode: e,
            type: 'emp',
            empName: nameList[index + length]
          };
        });
      }
      this.form.scheduleNameList = [...deptList, ...empList];
      if (this.form.manageUserCode) {
        this.form.manageNameList = this.form.manageUserCode
          .split(',')
          .map((e, index) => {
            return {
              empCode: e,
              empName: this.form.manageName.split(',')[index]
            };
          });
      } else {
        this.form.manageNameList = [];
      }
    },
    async open({ data = null, title, type }) {
      if (data) {
        await this.getDetail(data);
      }
      this.title = title;
      this.type = type;
      if (type == 'detail') {
        this.isDetail = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);
        let Api = this.ajax.scheduleAuthoritySave;
        if (data.id) {
          Api = this.ajax.scheduleAuthorityUpdate;
        }
        delete data.manageNameList;
        delete data.scheduleNameList;
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    handleOpenSelPerson(key) {
      let emp = ['', ''],
        dept = ['', ''],
        echoData = {};
      if (key != 'scheduleName') {
        emp = ['manageName', 'manageUserCode'];
        echoData = {
          dept: '',
          group: '',
          manageUserCode: this.form.manageUserCode
        };
      } else {
        echoData = {
          scheduleOrg: this.form.scheduleOrg,
          group: '',
          scheduleUser: this.form.scheduleUser
        };
        dept = ['scheduleOrgName', 'scheduleOrg'];
        emp = ['scheduleUserName', 'scheduleUser'];
      }
      this.$refs.TsHomsSelectPerson.open(key, {
        showOrganizationCheck: key != 'scheduleName' ? false : true,
        showGroupCheck: false,
        isRadio: false,
        echoData,
        submitKeys: {
          dept,
          group: ['', ''],
          emp
        }
      });
    },
    handleDeleteUser1(item) {
      let list = this.form.manageUserCode.split(',') || [];
      let listName = this.form.manageName.split(',') || [];
      let index = list.findIndex(e => e == item.empCode);
      list.splice(index, 1);
      listName.splice(index, 1);
      this.form.manageNameList.splice(index, 1);
      this.form.manageUserCode = list.join(',');
      this.form.manageName = listName.join(',');
      this.$forceUpdate();
    },
    // 删除选中某人
    handleDeleteUser({ type, id = '', empCode = '' }) {
      let list = [];
      if (type == 'dept') {
        list = this.form.scheduleOrg.split(',') || [];
        let index = list.findIndex(e => e == id);
        list.splice(index, 1);
        this.form.scheduleOrg = list.join(',');
      } else {
        list = this.form.scheduleUser.split(',') || [];
        let index = list.findIndex(e => e == empCode);
        list.splice(index, 1);
        this.form.scheduleUser = list.join(',');
      }
      let index = this.form.scheduleNameList.findIndex(f => {
        if (type == 'dept') {
          return f.id == id;
        } else {
          return f.empCode == empCode;
        }
      });
      this.form.scheduleNameList.splice(index, 1);
      let allNames = this.form.scheduleNameList
        .map(e => {
          let name = e?.name ? e.name : e.empName;
          return name;
        })
        .join(',');
      this.form.scheduleName = allNames;
      this.$forceUpdate();
    },
    handleSelectUserOk1(result, key) {
      switch (key) {
        case 'manageName':
          let { manageUserCode, manageName } = result[key];
          let empLists = result[key].empList;
          this.$set(this.form, 'manageUserCode', manageUserCode);
          this.$set(this.form, 'manageName', manageName);
          this.$set(this.form, 'manageNameList', [...empLists]);
          this.$forceUpdate();
          break;
        case 'scheduleName':
          const { scheduleOrg, scheduleUser, deptList, empList } = result[key];
          this.$set(this.form, 'scheduleOrg', scheduleOrg);
          this.$set(this.form, 'scheduleUser', scheduleUser);
          deptList?.forEach(e => {
            e.type = 'dept';
          });
          empList?.forEach(e => {
            e.type = 'emp';
          });
          let list = [...deptList, ...empList];
          let allNames = list
            .map(e => {
              let name = e?.name ? e.name : e.empName;
              return name;
            })
            .join(',');
          this.$set(this.form, 'scheduleNameList', list);
          this.$set(this.form, 'scheduleName', allNames);
          this.$forceUpdate();
          break;
      }
    },
    close() {
      this.visible = false;
      this.type = undefined;
      this.form = {
        scheduleNameList: [],
        manageNameList: []
      };
      this.isDetail = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .darwer-setting {
    .textarea {
      .el-textarea__inner {
        min-height: 120px !important;
        max-height: 120px !important;
      }
    }
    .select-item-content {
      min-height: 40px;
      width: 100%;
      flex-grow: 1;
      padding: 8px 7px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(51, 51, 51, 0.7);
      line-height: 17px;
      overflow-y: auto;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-top: 8px;
      .selected-item {
        display: inline-block;
        padding: 2px 8px;
        padding-right: 4px;
        margin-right: 12px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
        border-radius: 8px;
        border: 1px solid $primary-blue;
        i {
          color: $error-color;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
