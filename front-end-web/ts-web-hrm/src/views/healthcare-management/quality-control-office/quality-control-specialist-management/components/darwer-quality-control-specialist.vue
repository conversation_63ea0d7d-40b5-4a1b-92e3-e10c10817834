<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-physician-rotation-add"
    direction="rtl"
    size="60%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 45px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="ruleForm" :model="form" labelWidth="110px">
            <!-- 基本信息 -->
            <div class="form-card-box">
              <colmun-head title="基本信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="8">
                  <ts-form-item
                    label="姓名"
                    prop="applyName"
                    :rules="rules.required"
                  >
                    <base-select
                      v-if="!isDetail"
                      v-model="form.applyCode"
                      :inputText.sync="form.applyName"
                      :loadMethod="handleGetPersonList"
                      label="employeeName"
                      value="employeeNo"
                      searchInputName="employeeName"
                      :clearable="false"
                      placeholder="请选择"
                      style="width: 100%;"
                      @select="handlePersonSelect"
                    >
                      <template slot-scope="options">
                        <p
                          style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                          :title="
                            `${options.data['employeeNo']}--${options.data['employeeName']}`
                          "
                        >
                          {{ options.data['employeeNo'] }}--
                          {{ options.data['employeeName'] }}
                        </p>
                      </template>
                    </base-select>
                    <ts-input v-else v-model="form.applyName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="工号" prop="applyCode">
                    <ts-input
                      v-model="form.applyCode"
                      placeholder="选择医师姓名自动填充工号"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="身份证号" prop="applyIdcard">
                    <ts-input
                      v-model="form.applyIdcard"
                      placeholder="选择医师姓名自动填充身份证号"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="申请时间" prop="applyDate">
                    <ts-input
                      v-model="form.applyDate"
                      placeholder="选择医师姓名自动填充身份证号"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="归属院区" prop="applyAreaText">
                    <ts-input
                      v-model="form.applyAreaText"
                      placeholder="选择医师姓名自动填充归属院区"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="归属科室" prop="orgName">
                    <ts-input
                      v-model="form.orgName"
                      placeholder="选择医师姓名自动填充归属科室"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="技术职称" prop="applyTechnical">
                    <ts-input
                      v-model="form.applyTechnical"
                      placeholder="选择医师姓名自动填充技术职称"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="联系方式" prop="applyPhone">
                    <ts-input
                      v-model="form.applyPhone"
                      placeholder="选择医师姓名自动填充联系方式"
                      disabled
                    />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 任职信息 -->
            <div class="form-card-box">
              <colmun-head title="任期信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="8">
                  <ts-form-item
                    label="担任院区"
                    prop="holdArea"
                    :rules="rules.required"
                  >
                    <ts-select
                      v-model="form.holdArea"
                      placeholder="请选择担任院区"
                      style="width: 100%;"
                      :disabled="isDetail"
                    >
                      <ts-option
                        v-for="(item, index) in hopsAreaList"
                        :key="index"
                        :label="item.itemName"
                        :value="item.itemCode"
                      ></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    label="担任科室"
                    prop="holdOrgId"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.holdOrgId"
                      placeholder="请输入担任科室"
                      maxlength="100"
                      :disabled="isDetail"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="">
                    <div style="height: 36px;"></div>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    label="任期开始时间"
                    prop="applyStartDate"
                    :rules="rules.required"
                  >
                    <!-- :disabled="datePickerDisabled" -->
                    <el-date-picker
                      style="width: 100%;"
                      v-model="form.applyStartDate"
                      placeholder="请选择任期开始时间"
                      type="date"
                      clearable
                      :disabled="isDetail"
                      :picker-options="pickerOptions"
                      format="yyyy-MM-dd"
                      valueFormat="yyyy-MM-dd"
                    />
                    <!-- @change="calcMonths" -->
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item
                    label="任期结束时间"
                    prop="applyEndDate"
                    :rules="rules.required"
                  >
                    <!-- :disabled="datePickerDisabled" -->
                    <el-date-picker
                      style="width: 100%;"
                      v-model="form.applyEndDate"
                      placeholder="请选择任期结束时间"
                      clearable
                      :disabled="isDetail"
                      :picker-options="pickerOptions1"
                      format="yyyy-MM-dd"
                      valueFormat="yyyy-MM-dd"
                    />
                    <!-- @change="calcMonths" -->
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="应任期时长">
                    <ts-input
                      v-model="form.applyTermTime"
                      :disabled="isDetail"
                      placeholder="请输入应任期时长"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="实际结束时间">
                    <!-- <ts-input v-model="form.applyRealDate" disabled /> -->
                    <el-date-picker
                      style="width: 100%;"
                      v-model="form.applyRealDate"
                      placeholder="请选择实际结束时间"
                      :picker-options="pickerOptions1"
                      :disabled="isDetail"
                      clearable
                      format="yyyy-MM-dd"
                      valueFormat="yyyy-MM-dd"
                    />
                    <!-- @change="calcMonths1" -->
                  </ts-form-item>
                </ts-col>
                <ts-col :span="8">
                  <ts-form-item label="实际任期时长">
                    <ts-input
                      v-model="form.applyRealTime"
                      :disabled="isDetail"
                      placeholder="请输入实际任期时长"
                    />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 考核信息 -->
            <div class="form-card-box">
              <colmun-head title="处罚信息" />
              <form-table
                class="mrgT8 pdlr8"
                ref="FormTable"
                :formData="form"
                operateDataKey="qualityPunishList"
                :loaclColumns="[]"
                :disabled="true"
                :columns="patientColumns.filter(e => e.show)"
              >
                <template slot="action-bottom">
                  <ts-button
                    type="primary"
                    v-if="!isDetail"
                    @click="handleAddTable('FormTable')"
                  >
                    添加行
                  </ts-button>
                </template>
                <template v-slot:punishEndDate="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`qualityPunishList.${index}.${column.property}`"
                    :rules="rules.required"
                  >
                    <el-date-picker
                      v-if="!isDetail"
                      style="width: 100%;"
                      v-model="row[column.property]"
                      placeholder="请选择处罚结束日期"
                      type="date"
                      format="yyyy-MM-dd"
                      valueFormat="yyyy-MM-dd"
                    />
                    <!-- :default-value="defaultValue" -->
                    <!-- :picker-options="pickerOptions2" -->
                    <!-- @change="checkMaxDate(row, column.property)" -->
                    <p v-else>{{ row[column.property] }}</p>
                  </ts-form-item>
                </template>
                <template v-slot:punishResult="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`qualityPunishList.${index}.${column.property}`"
                  >
                    <ts-input
                      v-if="!isDetail"
                      v-model="row[column.property]"
                      :maxlength="20"
                      placeholder="请输入处罚结果"
                    />
                    <p v-else>{{ row[column.property] }}</p>
                    <!-- <p>{{ row[column.property] }}</p> -->
                  </ts-form-item>
                </template>
                <template v-slot:punishDate="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`qualityPunishList.${index}.${column.property}`"
                  >
                    <p>{{ row[column.property] }}</p>
                  </ts-form-item>
                </template>
                <template v-slot:punishRemark="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`qualityPunishList.${index}.${column.property}`"
                  >
                    <ts-input
                      v-if="!isDetail"
                      v-model="row[column.property]"
                      :maxlength="20"
                      placeholder="请输入处罚原因"
                    />
                    <p v-else>{{ row[column.property] }}</p>
                  </ts-form-item>
                </template>
                <template v-slot:actions="{ row, column, index }">
                  <ts-form-item class="flex-item" label="" label-width="0">
                    <div
                      v-show="!isDetail"
                      @click="delRow('qualityPunishList', index)"
                      class="action del"
                    >
                      删除
                    </div>
                  </ts-form-item>
                </template>
              </form-table>
              <ts-row class="pdR8" v-if="!isDetail">
                <ts-col :span="24">
                  <ts-form-item label="附件上传" prop="applyFiles">
                    <table-module-upload
                      style="margin-top: 4px;"
                      ref="tableUpload"
                      v-model="form.applyFiles"
                      moduleName="qualification"
                    >
                      <div style="display: flex;">
                        <ts-button type="primary" icon="el-icon-upload"
                          >上传附件</ts-button
                        >
                        <p class="tooptip">
                          （附件大小建议不要超过{{ allowFileSize }}MB），
                          可允许上传文件后缀为{{ allowFileExtension }}
                        </p>
                      </div>
                    </table-module-upload>
                  </ts-form-item>
                </ts-col>
              </ts-row>
              <ts-row class="pdR8" v-if="isDetail && fileList.length">
                <ts-col :span="24">
                  <ts-form-item label="相关附件" prop="applyFiles">
                    <base-file-list ref="fileListBatch" :fileList="fileList" />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <!-- 考核情况 -->
            <div class="form-card-box">
              <colmun-head title="考核情况" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="8">
                  <ts-form-item label="考核结果">
                    <ts-select v-model="form.applyResult" :disabled="isDetail">
                      <ts-option label="考核中" value="0"></ts-option>
                      <ts-option label="合格" value="1"></ts-option>
                      <ts-option label="不合格" value="2"></ts-option>
                    </ts-select>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button type="primary" v-if="type != 'detail'" @click="submit"
          >提交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import TableModuleUpload from '@/components/table-module-upload';
import darwerQualityControlSpecialist from '../mixins/darwer-quality-control-specialist';
import FormTable from '@/components/form-table/form-table.vue';
import moment from 'moment';
export default {
  mixins: [darwerQualityControlSpecialist],
  components: { TableModuleUpload, FormTable },
  props: {
    hopsAreaList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    allowFileSize() {
      return this.$getParentStoreInfo().globalSetting.allowFileSize || 100;
    },
    allowFileExtension() {
      return this.$getParentStoreInfo().globalSetting.allowFileExtension;
    },
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  data() {
    return {
      visible: false,
      isDetail: false,
      title: '',
      type: '',
      form: {
        qualityPunishList: []
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async getDetail(data) {
      let res = await this.ajax.getQualityApplyDetail(data.id);
      this.form = res.object || {};
    },
    async open({ data = null, title, type }) {
      if (data) {
        await this.getDetail(data);
        await this.getFileList();
      } else {
        this.form.applyDate = moment().format('YYYY-MM-DD');
      }
      this.title = title;
      this.type = type;
      if (type == 'detail') {
        this.isDetail = true;
        this.patientColumns[4].show = false;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
        if (type != 'details') {
          this.$refs.tableUpload?.handlePushFileData(this.tableFileList);
        }
      });
    },
    async submit() {
      try {
        await this.$refs.ruleForm.validate();
        const data = deepClone(this.form);
        let Api = this.ajax.qualityApplySave;
        if (data.id) {
          Api = this.ajax.qualityApplyUpdate;
        } else {
          data.qualityPunishList.forEach(item => {
            delete item.id;
            delete item.actions;
          });
        }
        const res = await Api(data);

        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh');
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.type = undefined;
      this.form = {
        qualityPunishList: []
      };
      this.tableFileList = [];
      this.fileList = [];
      this.isDetail = false;
      this.patientColumns[4].show = true;
      this.initData();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .darwer-physician-rotation-add {
    .el-input {
      min-width: 100%;
    }
    .tooptip {
      width: 100%;
      margin: 0;
      color: #f59a23;
      word-wrap: break-word;
      word-break: break-all;
      text-align: left;
      line-height: 30px;
    }
    .el-form-item__content {
      p {
        margin: 0;
        line-height: 30px;
      }
    }
    .action {
      width: 100%;
      text-align: center;
      cursor: pointer;
      &.del {
        color: red;
      }
    }
  }
  .textarea {
    .el-textarea__inner {
      min-height: 120px !important;
      max-height: 120px !important;
    }
  }
}
</style>
