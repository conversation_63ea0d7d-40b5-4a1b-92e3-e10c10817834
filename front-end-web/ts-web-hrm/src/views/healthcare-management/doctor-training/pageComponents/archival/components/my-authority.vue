<template>
  <div class="my-authority flex-column">
    <colmun-head title="我的权限" :background="background" />
    <div class="radioAll">
      <item-head :title="`权限一览(已有授权：${groupData.length})`" />
      <vxe-checkbox-group v-model="groupData" disabled>
        <vxe-checkbox
          v-for="item in groupList"
          :key="item.value"
          :label="item.value"
          :content="item.name"
          class="checkItem"
        ></vxe-checkbox>
      </vxe-checkbox-group>
    </div>
    <div class="tableData">
      <item-head title="已有手术权限明细" class="borderTop">
        <template slot="right">
          <div class="search">
            <ts-input
              v-model="searhVal"
              placeholder="搜索编码/名称"
              @change="handleRefreshTable"
            />
            <span class="label">院内级别</span>
            <ts-select v-model="searhType" @change="handleRefreshTable">
              <ts-option value="1" label="一级"></ts-option>
              <ts-option value="2" label="二级"></ts-option>
              <ts-option value="3" label="三级"></ts-option>
              <ts-option value="4" label="四级"></ts-option>
            </ts-select>
          </div>
        </template>
      </item-head>
      <TsVxeTemplateTable
        id="table_my-authority"
        class="form-table"
        ref="table"
        :hasPage="false"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
  </div>
</template>

<script>
import staticData from '../../../mixins/staticData';
import itemHead from '../../../components/item-head.vue';
export default {
  mixins: [staticData],
  components: { itemHead },
  data() {
    return {
      searhType: '',
      searhVal: '',
      columns: [
        {
          label: '手术类型',
          prop: 'key1',
          align: 'center',
          width: 80
        },
        {
          label: '手术编码',
          prop: 'key2',
          align: 'center',
          width: 100
        },
        {
          label: '手术名称',
          prop: 'key3',
          align: 'center',
          minWidth: 200
        },
        {
          label: '院内级别',
          prop: 'key4',
          align: 'center',
          width: 80
        },
        {
          label: '国家级别',
          prop: 'key5',
          align: 'center',
          width: 80
        },
        {
          label: '是否新技术项目',
          prop: 'key7',
          align: 'center',
          width: 120
        },
        {
          label: '工作量',
          prop: 'key6',
          align: 'center',
          minWidth: 120,
          render: (h, { row }) => {
            return h(
              'div',
              { class: 'parcentCell', style: `width: ${row.key6}%` },
              row.key6
            );
          }
        },
        {
          label: '排名',
          prop: 'key8',
          align: 'center',
          minWidth: 200,
          render: (h, { row }) => {
            return h('p', { class: 'topCell' }, [
              h('span', {}, [
                h('span', { class: 'cellDot' }, '院'),
                h('span', { class: 'cellValue' }, row.key8)
              ]),
              h('span', {}, [
                h('span', { class: 'cellDot' }, '科'),
                h('span', { class: 'cellValue' }, row.key9)
              ])
            ]);
          }
        }
      ]
    };
  },
  methods: {
    async refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    async handleRefreshTable() {
      // let searchForm = {
      //   startDate: this.date[0],
      //   endDate: this.date[1]
      // };
      // let res = await this.ajax.getScheduleClassesList(searchForm);
      this.$refs.table.refresh({ rows: this.myAuthorityData });
    }
  }
};
</script>

<style lang="scss" scoped>
.my-authority {
  margin-top: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  padding-bottom: 8px;
  ::v-deep {
    .border-top {
      border: 1px solid #eee;
      border-radius: 4px;
    }
    .radioAll {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 4px 8px;
      margin: 4px 4px;
      border-radius: 4px;
      border: 1px solid #eee;
      .checkItem {
        width: 20%;
        margin: 0;
        line-height: 30px;
        .vxe-icon-checkbox-checked-fill {
          color: $primary-blue !important;
        }
        .vxe-icon-checkbox-unchecked {
          background: #eee !important;
        }
        .vxe-checkbox--label {
          color: #333 !important;
        }
      }
    }
    .tableData {
      height: 300px;
      margin: 4px 4px;
      display: flex;
      flex-direction: column;
      border: 1px solid #eee;
      border-radius: 4px;
      .form-table {
        margin-top: 4px;
      }
    }
    .parcentCell {
      background: #f8e5b0;
      color: #eec985;
    }
    .topCell {
      display: flex;
      justify-content: space-around;
      margin: 0;
      .cellDot {
        padding: 4px 6px;
        font-size: 12px;
        line-height: 24px;
        background: $primary-blue;
        color: #fff;
        border-radius: 12px;
      }
      .cellValue {
        margin-left: 4px;
      }
    }
    .search {
      display: flex;
      .el-input {
        min-width: 100px;
        width: 100px;
      }
      .label {
        line-height: 30px;
        margin: 0 8px;
      }
    }
  }
}
</style>
