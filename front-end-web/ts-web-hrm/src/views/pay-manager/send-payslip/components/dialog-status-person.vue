<template>
  <ts-dialog
    custom-class="dialog-status-person"
    :title="title"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <select-month
        class="select-month-component"
        v-model="payrollDate"
        title="人员工资条"
      />
      <ts-tabs v-model="activeTab">
        <ts-tab-pane
          v-for="(item, index) in headerList"
          :key="index"
          :name="index + ''"
          :label="item.name + ' (' + headerData[item.value] + ')人'"
        ></ts-tab-pane>
      </ts-tabs>
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
      >
        <template slot="deptTree">
          <ts-ztree-select
            placeholder="请选择科室"
            ref="treeSelect"
            :inpText.sync="searchForm.name"
            :inpVal.sync="searchForm.code"
            :data="treeData"
            defaultExpandAll
            @change="handleChangeTree"
            @before-change="handleTreeBeforeChange"
          />
        </template>
        <template slot="personStatus">
          <ts-select v-model="searchForm.employeeStatus" clearable>
            <ts-option
              v-for="(item, index) in statusList"
              :key="index"
              :value="item.itemCode"
              :label="item.itemName"
            ></ts-option>
          </ts-select>
        </template>
        <template slot="right">
          <template>
            <ts-button
              :loading="sendLoading"
              type="primary"
              @click="handleAllsend"
            >
              全量发送
            </ts-button>
            <ts-button
              :loading="sendLoading"
              type="primary"
              @click="handleBatchsend"
            >
              批量发送
            </ts-button>
            <ts-button
              :loading="sendLoading"
              type="primary"
              @click="handleAllRevoke"
            >
              批量撤回
            </ts-button>
            <ts-button
              :loading="sendLoading"
              type="primary"
              @click="handleExport"
            >
              导出
            </ts-button>
          </template>
        </template>
      </ts-search-bar>

      <!-- <base-table
        ref="table"
        class="form-table"
        border
        stripe
        :columns="columns"
        @refresh="handleRefreshTable"
        @selection-change="handleSelectionChange"
      /> -->
      <TsVxeTemplateTable
        id="table_status_person"
        class="form-table"
        ref="table"
        :columns="columns"
        @selection-change="handleSelectionChange"
        @refresh="handleRefreshTable"
      />
      <div class="footer_total">
        <salary-total-columns :totalData="totalData" />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <drawer-salary-slip ref="DrawerSalarySlip" />
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import SelectMonth from '@/views/pay-manager/components/select-month.vue';
import SalaryTotalColumns from '@/views/pay-manager/components/salary-total-columns.vue';
import dialogStatusPerson from './dialog-status-person.js';
import DrawerSalarySlip from './drawer-salary-slip.vue';
export default {
  mixins: [dialogStatusPerson],
  components: {
    SelectMonth,
    SalaryTotalColumns,
    DrawerSalarySlip
  },
  data() {
    return {
      sendLoading: false,
      visible: false,
      details: {},
      payrollDate: '',
      title: '',
      statusList: [],
      treeData: [],
      totalData: [],
      columns: [],
      form: '',
      selectList: [],
      headerData: {},
      activeTab: undefined
    };
  },
  watch: {
    activeTab: {
      handler(val) {
        if (typeof val !== 'undefined') {
          this.$nextTick(() => {
            this.search();
          });
        }
      }
    },
    payrollDate: {
      handler() {
        this.$nextTick(() => {
          if (this.payrollDate != '') {
            this.search();
            this.calculateWagesTitle();
            this.sendSalarySummaryData();
            this.sendSalaryDeatilsSummaryData();
          }
        });
      }
    }
  },
  methods: {
    open({ data, title }, form = 'sendPaySlip') {
      this.title = title;
      this.payrollDate = data.payrollDate;
      this.details = deepClone(data);
      this.form = form;
      this.visible = true;
      this.$nextTick(() => {
        this.getDeptTreeList();
        this.getdictItemList();
      });
    },
    sendSalarySummaryData() {
      let param = {
        optionId: this.details.optionId,
        startDate: this.payrollDate,
        endDate: this.payrollDate
      };
      this.ajax.sendSalarySummaryData(param).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取汇总信息失败');
          return;
        }
        this.totalData = res.object;
      });
    },
    sendSalaryDeatilsSummaryData() {
      let param = {
        optionId: this.details.optionId,
        payrollDate: this.payrollDate
      };
      this.ajax.sendSalaryDeatilsSummaryData(param).then(res => {
        if (!res.success) {
          this.$message.error('获取失败');
          return;
        }
        this.headerData = res.object || {};
      });
    },
    calculateWagesTitle() {
      let param = {
        optionId: this.details.optionId,
        computeDate: this.payrollDate,
        salaryIndex: 1
      };
      this.ajax.calculateWagesTitle(param).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取表头信息失败');
          return;
        }
        let obj = {
          type: 'checkbox',
          width: 40,
          align: 'center',
          fixed: 'left'
        };
        this.columns = [obj, ...res.object, ...this.columns1];
      });
    },
    search() {
      this.$nextTick(() => {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
      });
    },
    async getdictItemList() {
      let res = await this.ajax.getdictItemList();
      this.statusList = res.rows || [];
    },
    getDeptTreeList() {
      this.ajax.getDeptTreeList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '获取科室信息失败');
          return;
        }
        this.treeData = res.object;
      });
    },
    handleChangeTree(e) {
      const paths = e.getPath();
      const names = paths.map(item => item.name) || [];
      let allPath = names[names.length - 1];
      const { id, name } = e;
      this.searchForm.code = id;
      this.searchForm.codetext = name;
      this.searchForm.name = allPath;
      this.searchForm.orgName = name;
    },
    handleTreeBeforeChange(e, treeObj, fn) {
      return true;
    },
    handleExport() {
      let aDom = document.createElement('a');
      aDom.href = `/ts-hrms/api/newSalaryOptionPayroll/calculateWagesDataExport?optionId=${
        this.details.optionId
      }&computeDate=${this.payrollDate || ''}&employeeName=${this.searchForm
        .employeeName || ''}&orgName=${this.searchForm.orgName ||
        ''}&employeeStatus=${this.searchForm.employeeStatus || ''}`;
      aDom.click();
    },
    handleSelectionChange(e) {
      this.selectList = e;
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        sendStatus =
          this.activeTab == '0'
            ? ''
            : this.activeTab == '1'
            ? '1'
            : this.activeTab == '2'
            ? '0'
            : this.activeTab == '3'
            ? '2'
            : this.activeTab == '4'
            ? '3'
            : '',
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          optionId: this.details.optionId,
          computeDate: this.payrollDate,
          sendStatus
        };
      let res = await this.ajax.calculateWagesData(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    // 批量发送
    async handleBatchsend() {
      if (this.selectList.length === 0) {
        this.$message.warning('请至少选择一条数据操作!');
        return;
      }
      try {
        await this.$confirm(
          `<p style="font-weight: 600;font-size:16px">确认发送工资条吗？</p><p>已发放工资条，再次操作会<span style="color:red;">覆盖原工资条数据</span></p>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.sendLoading = true;
        let ids = this.selectList.map(m => m.id).join(',');
        let param = {
          computeDate: this.payrollDate,
          optionIds: this.details.optionId,
          ids
        };
        this.ajax.batchSend(param).then(res => {
          this.sendLoading = false;
          if (!res.success) {
            this.$message.error('发送失败');
            return;
          }
          this.$message.success('批量发送成功');
          this.sendSalaryDeatilsSummaryData();
          this.search();
        });
      } catch (e) {
        this.sendLoading = false;
        console.error(e);
      }
    },
    // 全量发送
    async handleAllsend() {
      try {
        await this.$confirm(
          `<p style="font-weight: 600;font-size:16px">确认发送工资条吗？</p><p>已发放工资条，再次操作会<span style="color:red;">覆盖原工资条数据</span></p>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.sendLoading = true;
        let param = {
          computeDate: this.payrollDate,
          optionIds: this.details.optionId
        };
        this.ajax.batchSend(param).then(res => {
          this.sendLoading = false;
          if (!res.success) {
            this.$message.error('全量发送失败');
            return;
          }
          this.$message.success('全量发送成功');
          this.search();
        });
      } catch (e) {
        this.sendLoading = false;
        console.error(e);
      }
    },
    // 单个发送
    async handleSend(row) {
      try {
        await this.$confirm(
          `<p style="font-size:16px;font-weight:600;">是否重新发送</p><p>已发放工资条，再次操作会<span style="color:red">覆盖原工资条数据</span>！</p>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.sendLoading = true;
        // let param = {
        //   computeDate: this.payrollDate,
        //   optionIds: this.details.optionId,
        //   ids: row.id
        // };
        // this.ajax.batchSend(param).then(res => {
        //   this.sendLoading = false;
        //   if (!res.success) {
        //     this.$message.error('发送失败');
        //     return;
        //   }
        //   this.$message.success('发送成功');
        //   this.sendSalaryDeatilsSummaryData();
        //   this.search();
        // });

        const res = await this.ajax.salaryPayrollSingleSend({ id: row.id });
        this.sendLoading = false;
        if (!res.success) {
          this.$message.error(res.message || '发送失败!');
          return;
        }
        this.$message.success('发送成功!');
        this.sendSalaryDeatilsSummaryData();
        this.search();
      } catch (e) {
        this.sendLoading = false;
        console.error(e);
      }
    },
    // 批量撤回
    async handleAllRevoke() {
      if (this.selectList.length === 0) {
        this.$message.warning('请至少选择一条数据操作!');
        return;
      }
      let ids = this.selectList.map(m => m.id).join(',');
      let data = {
        optionId: this.details.optionId,
        payrollDate: this.payrollDate,
        ids
      };
      try {
        await this.$confirm(
          `<p style="font-weight: 600;font-size:16px">是否撤回工资条</p><p>撤回工资条后，<span style="color:red;">员工将看不到本次工资条</span>，是否撤回？</p>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        this.sendLoading = true;
        this.ajax.salaryPayrollBatchRevocation(data).then(res => {
          this.sendLoading = false;
          if (!res.success) {
            this.$message.error('全部撤回失败');
            return;
          }
          this.$message.success('全部撤回成功');
          this.sendSalaryDeatilsSummaryData();
          this.search();
        });
      } catch (e) {
        this.sendLoading = false;
        console.error(e);
      }
    },
    // 单个撤回
    async handleRevoke(row) {
      try {
        await this.$confirm(
          `<p style="font-weight: 600;font-size:16px">是否撤回工资条</p><p>撤回工资条后，<span style="color:red;">员工将看不到本次工资条</span>，是否撤回？</p>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        // let data = {
        //   optionId: this.details.optionId,
        //   payrollDate: this.payrollDate,
        //   ids: row.id
        // };
        // this.ajax.salaryPayrollBatchRevocation(data).then(res => {
        //   if (!res.success) {
        //     this.$message.error('撤回失败');
        //     return;
        //   }
        //   this.$message.success('撤回成功');
        //   this.sendSalaryDeatilsSummaryData();
        //   this.search();
        // });

        const res = await this.ajax.salaryPayrollSingleRevocation({
          id: row.id
        });
        if (!res.success) {
          this.$message.error(res.message || '撤回失败!');
          return;
        }
        this.$message.success('撤回成功!');
        this.sendSalaryDeatilsSummaryData();
        this.search();
      } catch (e) {
        console.error(e);
      }
    },
    handleView(row) {
      this.$refs.DrawerSalarySlip.open({
        data: {
          payrollDate: this.payrollDate,
          optionName: row.option_name,

          optionId: this.details.optionId,
          employeeId: row.employee_id,
          employeeName: row.employee_name
        },
        title: ''
      });
    },
    close() {
      this.visible = false;
      this.headerData = {};
      this.title = '';
      this.statusList = [];
      this.treeData = [];
      this.totalData = {};
      this.columns = [];
      this.form = '';
      this.payrollDate = '';
      this.activeTab = undefined;
      this.$emit('refresh');
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-status-person {
    .el-dialog__footer {
      width: 1344px !important;
    }
    .el-dialog__body {
      height: calc(100% - 80px);
      width: 1344px !important;
      overflow: auto;
      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        .footer_total {
          position: relative;
          left: 0;
          bottom: 0;
          z-index: 9999;
        }
        .ts-button {
          &.is-disabled {
            color: rgb(204, 204, 204) !important;
            border-color: rgb(231, 235, 240) !important;
            background-color: rgb(250, 250, 250) !important;
            &:hover {
              cursor: not-allowed;
            }
          }
        }
        .form-table {
          flex: 1;
          overflow: hidden;
          transform: scale(1);
        }
      }
    }
  }
}
</style>
