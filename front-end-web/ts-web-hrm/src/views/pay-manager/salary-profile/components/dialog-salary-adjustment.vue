<template>
  <ts-dialog
    custom-class="dialog-salary-adjustment"
    width="80%"
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="box-item-container">
        <div class="head flex-space">
          <span class="head-title">{{ infoTitle }}</span>

          <div class="operate">
            <ts-button @click="handleSwitchHeight" type="text">收起</ts-button>
          </div>
        </div>

        <ul class="item-content flex flex-wrap">
          <li v-for="(val, key, index) in basicInfoDetail" :key="index">
            {{ key }}：{{ val }}
          </li>
        </ul>
      </div>

      <ts-form
        class="ts-form-container"
        ref="form"
        :model="form"
        labelWidth="155px"
      >
        <div class="box-item-container">
          <div class="head flex-space">
            <span class="head-title">
              {{ type === 'change' ? '调薪' : '定薪' }}说明
            </span>

            <!-- <div class="operate">
              <ts-button @click="handleSwitchHeight" type="text">
                收起
              </ts-button>
            </div> -->
          </div>

          <div class="item-content">
            <ts-row>
              <el-col :span="8">
                <ts-form-item
                  label="生效日期"
                  prop="effectiveDate"
                  :rules="rules.required"
                >
                  <ts-month-picker
                    style="width: 100%"
                    value-format="yyyy-MM"
                    placeholder="请选择月份"
                    v-model="form.effectiveDate"
                  />
                </ts-form-item>
              </el-col>

              <el-col :span="8">
                <ts-form-item
                  v-if="type === 'change'"
                  label="任职时间"
                  prop="jobDeionTypeTime"
                  :rules="rules.required"
                >
                  <ts-month-picker
                    style="width: 100%"
                    value-format="yyyy-MM"
                    placeholder="请选择月份"
                    v-model="form.jobDeionTypeTime"
                  />
                </ts-form-item>
              </el-col>

              <el-col :span="8">
                <ts-form-item
                  v-if="type === 'change'"
                  label="调薪原因"
                  prop="reason"
                  :rules="rules.required"
                >
                  <ts-input
                    style="width: 100%"
                    v-model="form.reason"
                    placeholder="请输入"
                  />
                </ts-form-item>
              </el-col>
            </ts-row>

            <ts-form-item label="备注">
              <ts-input
                v-model="form.remark"
                class="textarea"
                type="textarea"
                resize="none"
                maxlength="300"
                show-word-limit
              />
            </ts-form-item>
          </div>
        </div>

        <div class="box-item-container require-form">
          <div class="head">
            <span class="head-title">
              {{ type === 'change' ? '调薪' : '定薪' }}项目
            </span>
          </div>

          <div class="item-content">
            <div class="flex-col-center title-head-table">
              <div class="title">薪酬项目</div>
              <div class="left">
                {{ type === 'change' ? '调整前工资' : '工资' }}
              </div>
              <div v-if="type === 'change'" class="right">调整后工资</div>
            </div>

            <el-scrollbar
              class="flex-column"
              style="flex: 1;"
              ref="scrollbar"
              wrap-style="overflow-x: hidden;"
            >
              <ts-form-item
                v-for="(item, index) in formItem"
                :key="index"
                class="histroy-form-item"
                :class="{ 'radio-item': item.basicItemType === '3' }"
                :label="item.basicItemName"
                :prop="item.prop"
                :rules="formItemRules(item)"
              >
                <template v-slot:error>
                  <div
                    :class="[type === 'change' ? 'change-error' : 'error-tips']"
                  >
                    必填
                  </div>
                </template>

                <ts-col
                  v-if="type == 'change' && item.showLastTimeData"
                  class="last-time-container"
                  :span="12"
                >
                  <div v-if="item.basicItemType === '1'">
                    <ts-input
                      style="width: 155px"
                      v-model="lastTimeForm[item.prop]"
                      disabled
                    />
                  </div>

                  <div v-if="item.basicItemType === '2'" class="flex-space">
                    <ts-input
                      class="right-aligned-input"
                      style="width: 155px"
                      v-model="lastTimeForm[item.prop].salaryAmount"
                      disabled
                    />

                    <span class="row-styles-input">百分比:</span>
                    <ts-input
                      class="percentage-input"
                      v-model="lastTimeForm[item.prop].empFieldValue"
                      disabled
                    >
                      <template slot="append">%</template>
                    </ts-input>
                  </div>

                  <ts-switch
                    v-if="['3'].includes(item.basicItemType)"
                    style="width: 100%"
                    v-model="lastTimeForm[item.prop]"
                    active-value="1"
                    inactive-value="2"
                    active-text=""
                    inactive-text=""
                    disabled
                  />
                </ts-col>

                <ts-col :span="12" class="now-time-container">
                  <!-- 政策标准 -->
                  <template
                    v-if="
                      item.basicItemType == '1' &&
                        item.empField === 'policy_standard_id'
                    "
                  >
                    <ts-select
                      style="width: 155px"
                      @change="handlePolicyStandardData"
                      v-model="form[item.prop]"
                      clearable
                    >
                      <ts-option
                        v-for="item in policyStandardData"
                        :key="item.policyStandardId"
                        :label="item.policyStandardName"
                        :value="item.policyStandardId"
                      />
                    </ts-select>
                  </template>
                  <!-- 岗位类别 -->
                  <template
                    v-else-if="
                      item.basicItemType == '1' && item.empField === 'plgw'
                    "
                  >
                    <ts-select
                      style="width: 155px"
                      @change="handleChangePostCategory"
                      v-model="form[item.prop]"
                      clearable
                    >
                      <ts-option
                        v-for="item in postCategoryList"
                        :key="item.postCategoryId"
                        :label="item.postCategoryName"
                        :value="item.postCategoryId"
                      />
                    </ts-select>
                  </template>

                  <!-- 岗位等级 -->
                  <template
                    v-else-if="
                      item.basicItemType == '1' && item.empField === 'gwdj'
                    "
                  >
                    <ts-select
                      style="width: 155px"
                      v-model="form[item.prop]"
                      @change="handleChangePostLevel"
                      clearable
                    >
                      <ts-option
                        v-for="item of postLevelList"
                        :key="item.postId"
                        :label="item.label"
                        :value="item.postId"
                      />
                    </ts-select>
                  </template>

                  <!-- 薪级类别 -->
                  <template
                    v-else-if="
                      item.basicItemType == '1' &&
                        item.empField === 'salary_level_type'
                    "
                  >
                    <ts-select
                      style="width: 155px"
                      @change="handleChangeSalaryCategory"
                      v-model="form[item.prop]"
                      clearable
                    >
                      <ts-option
                        v-for="item in salaryCategoryList"
                        :key="item.dictValue"
                        :label="item.dictName"
                        :value="item.dictValue"
                      />
                    </ts-select>
                  </template>

                  <!-- 薪级等级 -->
                  <template
                    v-else-if="
                      item.basicItemType == '1' &&
                        item.empField === 'salary_level_id'
                    "
                  >
                    <ts-select
                      style="width: 155px"
                      v-model="form[item.prop]"
                      @change="handleChangeSalaryLevel"
                      clearable
                    >
                      <ts-option
                        v-for="item of salaryLevelList"
                        :key="item.salaryLevelId"
                        :label="item.salaryLevelName"
                        :value="item.salaryLevelId"
                      />
                    </ts-select>
                  </template>

                  <template v-else-if="item.basicItemType == '2'">
                    <div class="flex-col-center">
                      <ts-input
                        class="right-aligned-input"
                        style="width: 155px"
                        v-model="form[item.prop].salaryAmount"
                        placeholder="请输入"
                        @input="
                          value => {
                            validateTowDecimalPlaces(
                              value,
                              form[item.prop],
                              'salaryAmount'
                            );
                            handleComputedPrice(item.prop);
                          }
                        "
                      />
                      <span class="row-styles-input">百分比:</span>
                      <ts-input
                        class="percentage-input"
                        v-model="form[item.prop].empFieldValue"
                        placeholder="请输入"
                        @input="handleInputPercentage($event, item.prop)"
                      >
                        <template slot="append">%</template>
                      </ts-input>
                      <span class="row-styles-input">应发:</span>
                      <ts-input
                        class="percentage-input"
                        v-model="form[item.prop].computed"
                        disabled
                      >
                      </ts-input>
                    </div>
                  </template>

                  <template v-else-if="item.basicItemType == '3'">
                    <ts-switch
                      v-model="form[item.prop]"
                      active-value="1"
                      inactive-value="2"
                      active-text=""
                      inactive-text=""
                    />
                  </template>
                </ts-col>
              </ts-form-item>
            </el-scrollbar>
          </div>
        </div>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone, inputTowDecimalPlaces } from '@/unit/commonHandle.js';
function validatorFunction(rule, data, callback) {
  let empFieldValue = parseFloat(data.empFieldValue);
  let salaryAmount = parseFloat(data.salaryAmount);

  if (isNaN(empFieldValue) || isNaN(salaryAmount)) {
    callback(new Error('必填'));
  } else {
    callback();
  }
}

export default {
  components: {},
  data() {
    return {
      submitLoading: false,
      visible: false,
      title: '',
      infoTitle: '',
      type: '',

      rules: {
        required: { required: true, message: '必填' }
      },
      postSaralyType: [
        'plgw',
        'gwdj',
        'salary_level_type',
        'salary_level_id',
        'policy_standard_id'
      ],
      policyStandardData: [],
      salaryCategoryList: [],
      postCategoryList: [],
      postLevelList: [],
      salaryLevelList: [],

      basicInfoDetail: {},
      adjustmentSalaryItem: [],
      formItem: [],
      detailsData: {},

      form: {},
      lastTimeForm: {}
    };
  },
  computed: {
    formItemRules() {
      return item => {
        if (item.basicItemType == '2') {
          return {
            required: true,
            trigger: 'blur',
            validator: validatorFunction
          };
        }

        if (item.basicItemType == '3') {
          return { required: false, message: '必填' };
        }

        return { required: true, message: '必填' };
      };
    }
  },
  methods: {
    async open({ data, title, type }) {
      this.title = title;
      this.type = type;
      this.detailsData = deepClone(data);
      this.$set(this, 'form', {});

      this.handleGetPostCategoryList();
      this.handleGetSalaryCategoryList();
      this.handleGetSalaryPolicyStandardData();
      await this.handleGetFormItem();
      switch (this.type) {
        case 'change':
          this.$set(
            this.form,
            'jobDeionTypeTime',
            this.$dayjs().format('YYYY-MM')
          );
          // 调薪回显
          await this.handleChangeEchoInfo();
          break;
        case 'edit':
          // 编辑回显
          await this.handleEditEchoInfo();
          break;
      }

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    handleSwitchHeight(e) {
      let target = e.target;
      let parentDom = target.closest('.box-item-container');
      if (!parentDom) return false;

      parentDom.classList.toggle('close');

      let flag = parentDom.classList.contains('close');
      target.textContent = flag ? '展开' : '收起';
      this.$refs.scrollbar.update();
    },

    // 调薪
    async handleChangeEchoInfo() {
      let res = await this.ajax.getSalaryBasicItemEmpInfo(
        this.detailsData.employee_id
      );

      if (res.success == false) {
        this.$message.error(res.message || '表单数据获取失败');
        return;
      }

      if (res.object.length) {
        let formItem = res.object.filter(
          f =>
            f.basicItemType != '1' || this.postSaralyType.includes(f.empField)
        );
        // 回显调薪前的数据
        formItem.forEach(f => {
          let val = this.handleGetSetKey(f, 'getLabel');
          this.$set(this.lastTimeForm, [f.basicItemId], val);
        });

        this.handleEchoFormData(formItem);
      }
    },

    // 编辑
    async handleEditEchoInfo() {
      let res = await this.ajax.salaryBasicItemEmpHistoryGetEmployeeIdAndEffectiveDate(
        {
          employeeId: this.detailsData.employee_id,
          effectiveDate: this.detailsData.effective_date
        }
      );

      if (res.success == false) {
        this.$message.error(res.message || '表单数据获取失败');
        return;
      }
      if (res.object.length) {
        this.handleEchoFormData(res.object);
      }
    },

    handleEchoFormData(data = []) {
      this.$set(this.form, 'effectiveDate', data[0].effectiveDate);
      this.$set(this.form, 'remark', data[0].remark);

      // 回显右侧调薪
      data.forEach(f => {
        let val = this.handleGetSetKey(f);
        // 编辑定薪 通过岗位类型 获取岗位等级下拉选项进行回显
        if (f.empField == 'plgw' && val) {
          this.handleChangePostCategory(val);
        }

        // 编辑定薪 通过薪酬类型 获取薪酬等级下拉选项进行回显
        if (f.empField == 'salary_level_type' && val) {
          this.handleChangeSalaryCategory(val);
        }

        this.$set(this.form, [f.basicItemId], val);

        if (f.basicItemType == '2') {
          this.handleComputedPrice(f.basicItemId);
        }
      });
    },

    // 获取薪酬档案字段
    async handleGetFormItem() {
      let res = await this.ajax.salaryBasicItemEmpGetAllData({
        employeeId: this.detailsData.employee_id
      });
      if (res.success == false) {
        this.$message.error(res.message || '表单项数据获取失败');
        return;
      }
      if (res.object.length) {
        this.adjustmentSalaryItem = deepClone(res.object);

        // 过滤出基础信息项目 处理展示数据
        let object = deepClone(res.object);
        object.forEach(f => {
          f.isBasicInfoItem =
            f.basicItemType == '1' && !this.postSaralyType.includes(f.empField);
        });
        this.handleRenderPersonBasic(object);
        this.handleInitFormItem(object);
      }
    },

    handleRenderPersonBasic(data = []) {
      this.basicInfoDetail = {};
      this.infoTitle = '';
      let arr = [];
      const field = ['employee_no', 'org_name', 'employee_name'];

      let basicItem = data.filter(f => f.isBasicInfoItem) || [];
      basicItem.forEach(({ basicItemName, basicItemValue, empField }) => {
        if (field.includes(empField)) {
          arr.push(basicItemValue);
        } else {
          this.basicInfoDetail[basicItemName] = basicItemValue;
        }
      });

      this.infoTitle = arr.join('-');
    },

    handleInitFormItem(data = []) {
      this.formItem = [];

      let formItemList = data.filter(f => !f.isBasicInfoItem) || [];
      this.formItem = formItemList.map(m => {
        // 初始化表单form
        if (m.basicItemType == '2') {
          this.$set(this.form, [m.id], {
            salaryAmount: m.salaryAmount,
            empFieldValue: 100,
            computed: 0
          });
          this.$set(this.lastTimeForm, [m.id], {
            salaryAmount: m.salaryAmount,
            empFieldValue: 100,
            computed: 0
          });
        }

        return {
          ...m,
          basicItemId: m.id,
          prop: m.id,
          employeeId: this.detailsData.employee_id,
          showLastTimeData: true
        };
      });
    },

    handleGetSetKey(item, type) {
      let val = null;
      switch (item.basicItemType) {
        case '1':
          if (this.postSaralyType.includes(item.empField)) {
            val = item.empFieldValue;
          }
          break;
        case '2':
          val = {
            salaryAmount: item.salaryAmount,
            empFieldValue: item.empFieldValue || 100
          };
          break;
        case '3':
          val = item.empFieldValue;
          break;
      }

      // 调薪 需要展示之前定薪的记录。其中 类别 等级的下拉储存的id 要展示label
      if (
        item.basicItemType == '1' &&
        type == 'getLabel' &&
        this.postSaralyType.includes(item.empField)
      ) {
        val = item.empFieldValueText;
      }
      return val;
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let dataForm = deepClone(this.form);
        let effectiveDate =
          this.$dayjs(dataForm.effectiveDate).format('YYYY-MM-DD') || '';
        let reason = dataForm.reason || '';
        let remark = dataForm.remark || '';

        delete dataForm.effectiveDate;
        delete dataForm.reason;
        delete dataForm.remark;

        let jobDeionTypeTime = '';
        if (this.type === 'change') {
          jobDeionTypeTime =
            this.$dayjs(dataForm.jobDeionTypeTime).format('YYYY-MM') || '';
          delete dataForm.jobDeionTypeTime;
        }

        let submitData = deepClone(this.adjustmentSalaryItem);
        submitData.forEach(f => {
          f.basicItemId = f.id;
          f.employeeId = this.detailsData.employee_id;

          f.effectiveDate = effectiveDate;
          f.jobDeionTypeTime = jobDeionTypeTime;
          f.reason = reason;
          f.remark = remark;
        });

        Object.keys(dataForm).forEach(fid => {
          submitData.forEach(f => {
            if (fid == f.basicItemId) {
              switch (f.basicItemType) {
                case '1':
                case '3':
                  f['empFieldValue'] = dataForm[fid];
                  break;
                case '2':
                  f['empFieldValue'] = dataForm[fid].empFieldValue;
                  f['salaryAmount'] = dataForm[fid].salaryAmount;
                  break;
              }
            }
          });
        });

        let API = null;
        switch (this.type) {
          case 'add':
            API = this.ajax.salaryBasicItemEmpSave;
            break;
          case 'change':
            API = this.ajax.salaryBasicItemEmpAdjustSave;
            break;
          case 'edit':
            API = this.ajax.salaryBasicItemEmpHistoryUpdateDate;
            break;
        }

        this.submitLoading = true;
        const res = await API(submitData);
        this.submitLoading = false;
        if (!res.success) {
          this.$message.error(res.message || '操作失败！');
          return;
        }
        this.$message.success('操作成功!');
        this.close();
        this.$emit('refresh');
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    //获取政策标准列表
    async handleGetSalaryPolicyStandardData() {
      let res = await this.ajax.selectHrmsSalaryPolicyStandardData();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      if (res.object.length) {
        this.policyStandardData = res.object.filter(f => f.isEnable == '1');
      }
    },
    // 变更政策标准
    handlePolicyStandardData() {
      // 清除 岗位等级
      let gwdj = this.formItem.find(f => f.empField === 'gwdj')?.prop || '';
      gwdj && this.$set(this.form, [gwdj], '');
      //重新加载岗位等级
      let plgw = this.formItem.find(f => f.empField === 'plgw')?.prop || '';
      // 获取对应薪级类别的 薪级等级下拉
      this.handleGetPostLevel(this.form[plgw]);
      // 清除 岗位工资
      this.handleChangePostLevel();

      // 清除 薪级等级
      let salaryLevelProp =
        this.formItem.find(f => f.empField === 'salary_level_id')?.prop || '';
      salaryLevelProp && this.$set(this.form, [salaryLevelProp], '');
      //重新加载薪级等级
      let salaryLevelType =
        this.formItem.find(f => f.empField === 'salary_level_type')?.prop || '';
      // 获取对应薪级类别的 薪级等级下拉
      this.handleGetSalaryLevel(this.form[salaryLevelType]);
      // 清除 薪级工资
      this.handleChangeSalaryLevel();
    },

    // 获取岗位类别
    async handleGetPostCategoryList() {
      let res = await this.ajax.postCategoryGetList();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      if (res.object.length) {
        this.postCategoryList = res.object.filter(f => f.isEnable == '1');
      }
    },

    // 获取薪级类别
    async handleGetSalaryCategoryList() {
      let res = await this.ajax.comboboxSalaryLevelCategory();
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      if (res.object.length) {
        this.salaryCategoryList = res.object || [];
      }
    },

    // 变更岗位类别 获取岗位等级
    handleChangePostCategory(val) {
      // 清除 岗位等级
      let gwdjProp = this.formItem.find(f => f.empField === 'gwdj')?.prop || '';
      gwdjProp && this.$set(this.form, [gwdjProp], '');

      // 获取对应岗位类别的 岗位等级下拉
      this.handleGetPostLevel(val);

      // 清除 岗位工资
      this.handleChangePostLevel();
    },

    // 变更岗位等级 获取岗位工资
    async handleChangePostLevel(val) {
      let gwgzProp =
        this.formItem.find(f => f.customRule === 'A01')?.prop || '';
      if (gwgzProp) {
        this.$set(this.form[gwgzProp], 'salaryAmount', '');
        this.handleComputedPrice(gwgzProp);
      }

      let jxgzProp =
        this.formItem.find(f => f.customRule === 'A02')?.prop || '';
      if (jxgzProp) {
        this.$set(this.form[jxgzProp], 'salaryAmount', '');
        this.handleComputedPrice(jxgzProp);
      }

      let jlxjxgzProp =
        this.formItem.find(f => f.customRule === 'A04')?.prop || '';
      if (jlxjxgzProp) {
        this.$set(this.form[jlxjxgzProp], 'salaryAmount', '');
        this.handleComputedPrice(jlxjxgzProp);
      }

      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });

      if (!val) return;
      //添加政策标准的查询条件
      let itemId =
        this.formItem.find(f => f.empField === 'policy_standard_id')?.prop ||
        '';
      let policyStandardId = this.form[itemId];
      const res = await this.ajax.getDataByPostWageId({
        postId: val,
        policyStandardId
      });
      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }

      if (res.object) {
        let postWage = res.object?.postWage || '';

        if (gwgzProp) {
          this.$set(this.form[gwgzProp], 'salaryAmount', postWage);
          this.handleComputedPrice(gwgzProp);
        }

        let performanceWage = res.object?.performanceWage || '';
        if (jxgzProp) {
          this.$set(this.form[jxgzProp], 'salaryAmount', performanceWage);
          this.handleComputedPrice(jxgzProp);
        }

        let awardWage = res.object?.awardWage || '';
        if (jlxjxgzProp) {
          this.$set(this.form[jlxjxgzProp], 'salaryAmount', awardWage);
          this.handleComputedPrice(jlxjxgzProp);
        }
      }
    },

    // 变更薪级类别
    handleChangeSalaryCategory(val) {
      // 清除 薪级等级
      let salaryLevelProp =
        this.formItem.find(f => f.empField === 'salary_level_id')?.prop || '';
      salaryLevelProp && this.$set(this.form, [salaryLevelProp], '');

      // 获取对应薪级类别的 薪级等级下拉
      this.handleGetSalaryLevel(val);

      // 清除 薪级工资
      this.handleChangeSalaryLevel();
    },

    // 变更薪级等级 获取薪级工资
    async handleChangeSalaryLevel(val) {
      let xjgzProp =
        this.formItem.find(f => f.customRule === 'A03')?.prop || '';
      if (xjgzProp) {
        this.$set(this.form[xjgzProp], 'salaryAmount', '');
        this.handleComputedPrice(xjgzProp);
      }

      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
      if (!val) return;
      //添加政策标准的查询条件
      let itemId =
        this.formItem.find(f => f.empField === 'policy_standard_id')?.prop ||
        '';
      let policyStandardId = this.form[itemId];
      const res = await this.ajax.salaryLevelWageGetDataByLevelId({
        salaryLevelId: val,
        policyStandardId
      });
      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }

      if (res.object) {
        let salaryLevelWage = res.object?.salaryLevelWage || '';

        if (xjgzProp) {
          this.$set(this.form[xjgzProp], 'salaryAmount', salaryLevelWage);
          this.handleComputedPrice(xjgzProp);
        }
      }
    },

    // 获取岗位等级
    async handleGetPostLevel(postCategory) {
      const res = await this.ajax.payPostGetList({
        postCategory
      });

      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }

      if (res.object.length) {
        this.postLevelList = res.object.map(m => {
          return {
            postId: m.postId,
            label: m.postName
          };
        });
      }
    },

    // 获取薪级等级
    async handleGetSalaryLevel(salaryLevelCategory) {
      //添加政策标准的查询条件
      let itemId =
        this.formItem.find(f => f.empField === 'policy_standard_id')?.prop ||
        '';
      let policyStandardId = this.form[itemId];
      const res = await this.ajax.getSalaryLeveCombobox({
        salaryLevelCategory,
        policyStandardId
      });

      if (!res.success) {
        this.$message.error(res.message || '获取数据失败!');
        return;
      }

      if (res.object.length) {
        this.salaryLevelList = res.object || [];
      }
    },

    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = inputTowDecimalPlaces(value);
      obj[attr] = newVal;
    },

    handleInputPercentage(value, prop) {
      let newValue = inputTowDecimalPlaces(value);
      if (parseFloat(newValue) > 100) {
        newValue = '100';
      } else if (parseFloat(newValue) < 0) {
        newValue = '0';
      }
      this.$set(this.form[prop], 'empFieldValue', newValue);
      this.handleComputedPrice(prop);
    },

    handleComputedPrice(prop) {
      let { empFieldValue, salaryAmount } = this.form[prop];
      let val1 = parseFloat(salaryAmount);
      let val2 = parseFloat(empFieldValue);
      if (isNaN(val1) || isNaN(val2)) return;

      let result = (val1 * val2) / 100;
      this.$set(this.form[prop], 'computed', Math.round(result));
    },

    close() {
      this.salaryCategoryList = [];
      this.postCategoryList = [];
      this.postLevelList = [];
      this.salaryLevelList = [];

      this.basicInfoDetail = {};
      this.adjustmentSalaryItem = [];
      this.formItem = [];
      this.detailsData = {};

      this.form = {};
      this.lastTimeForm = {};

      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-salary-adjustment {
    height: calc(100% - 48px);

    .el-dialog__body {
      height: calc(100% - 88px);
      padding: 8px !important;
      padding-bottom: 0px !important;
      overflow: auto;

      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .textarea {
          .el-textarea__inner {
            min-height: 35px !important;
            max-height: 35px !important;
          }
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
        }
        input[type='number'] {
          -moz-appearance: textfield;
        }

        .box-item-container {
          width: 100%;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          margin-bottom: 8px;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          .head {
            height: 32px;
            line-height: 32px;
            padding: 0 8px;
            border-bottom: 1px solid #ebeef5;

            .head-title {
              font-weight: 800;
              &::before {
                content: '';
                display: inline-block;
                width: 4px;
                height: 16px;
                background: $primary-blue;
                border-radius: 2px;
                vertical-align: text-top;
                margin-right: 4px;
              }
            }
          }

          &.close {
            .item-content {
              height: 0;
              padding: 0;
              opacity: 0;
            }
          }

          &.require-form {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            .item-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              overflow: hidden;
              padding: 0;
            }
          }

          .item-content {
            padding: 8px;
            padding-bottom: 0px;
            margin: 0px;
            transition: all 0.4s;

            li {
              margin-bottom: 8px;
              width: 25%;
            }
          }
        }

        .title-head-table {
          width: 100%;
          height: 30px;
          line-height: 30px;
          background-color: #7980f8;
          border-radius: 4px;
          color: #fff;
          .title {
            width: 155px;
            text-align: center;
            line-height: 30px;
            border-right: 1px solid #fff;
          }
          .left {
            flex: 1;
            text-align: center;
            line-height: 30px;
            border-right: 1px solid #fff;
          }
          .right {
            flex: 1;
            text-align: center;
            line-height: 30px;
          }
        }

        .ts-form-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .row-styles-input {
            margin: 0 4px 0 8px;
          }

          .percentage-input {
            width: 86px;
            min-width: 86px !important;

            .el-input {
              min-width: 86px !important;
            }
          }

          .histroy-form-item {
            border-bottom: 1px solid #ebeef5;
            margin-bottom: 0px !important;
            .el-form-item__label {
              transform: translateY(4px);
            }

            &.radio-item {
              .el-form-item__label {
                transform: translateY(0px);
              }
            }

            .el-form-item__content {
              display: flex;
              align-items: center;
              position: relative;

              .last-time-container {
                display: flex;
                align-items: center;
                overflow: hidden;
                border-left: 1px solid #ebeef5;
                padding: 6px 8px;

                .parent-last-container {
                  width: 100%;
                }
              }

              .now-time-container {
                display: flex;
                align-items: center;
                overflow: hidden;
                border-left: 1px solid #ebeef5;
                padding-left: 12px;
                padding: 6px 8px;
              }
            }
          }

          .change-error {
            position: absolute;
            z-index: 10;
            padding: 2px 8px;
            left: 50%;
            top: calc(100% - 4px);
            color: rgb(245, 108, 108);
            background-color: rgb(251, 227, 227);
            border-radius: 4px;
            display: flex;
            align-items: center;
            height: 24px;
            width: 45px;
            transform: translateX(8px);
          }

          .error-tips {
            position: absolute;
            z-index: 10;
            padding: 2px 8px;
            top: calc(100% - 4px);
            left: 8px;
            color: rgb(245, 108, 108);
            background-color: rgb(251, 227, 227);
            border-radius: 4px;
            width: 45px;
            height: 24px;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>
