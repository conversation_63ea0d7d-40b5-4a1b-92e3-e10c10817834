<template>
  <div class="departmentalEducationDistribution">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="apiFunction"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :params="deptParams"
        :showCheckbox="true"
        @nodeCheck="checkItemTree"
      />
    </div>
    <div class="right">
      <colmun-head
        title="学历分布报表"
        style="background: none;border: 1px solid #eee;"
      >
      </colmun-head>
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :actions="[]"
        @search="handleRefreshTable"
        :resetData="resetData"
        @reset="handleReset"
      >
        <template slot="right">
          <ts-button type="primary" @click="handleChart" class="shallowButton">
            图表
          </ts-button>
          <ts-button type="primary" @click="handleExport" class="shallowButton">
            导出
          </ts-button>
        </template>
      </ts-search-bar>
      <TsVxeTemplateTable
        id="table_personnelReport_departmentalEducationDistribution"
        class="form-table"
        ref="table"
        :hasPage="false"
        :columns="columns"
        show-footer
        @refresh="handleRefreshTable"
        :footer-data="footerData"
        :footer-cell-class-name="footerCellClassName2"
      />
    </div>
    <dialog-chart ref="dialogChart" />
  </div>
</template>

<script>
import dialogChart from '../components/dialog-chart.vue';
export default {
  components: { dialogChart },
  data() {
    return {
      apiFunction: this.ajax.getOrganizationGetTree2,
      columns: [],
      initEmployeeStatuses: [],
      searchForm: {},
      resetData: {},
      footerData: [],
      fileds: [],
      idList: [],
      searchList: [
        {
          label: '员工状态',
          value: 'employeeStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true
          },
          childNodeList: []
        }
      ]
    };
  },
  created() {
    //菜单参数获取
    let type = this.$route.params.type;
    if (type) {
      this.archivesType = type;
    } else {
      this.archivesType = '';
    }
    this.handleDictItemEmployeeStatus();
    // 初始化页面查询条件
    const hospitalCode = this.$store.state.common.hospitalCode;
    this.initEmployeeStatuses =
      hospitalCode === 'pjxdyrmyy'
        ? ['1', '99', '9']
        : ['1', '6', '12', '99', '9'];
    this.$set(this.searchForm, 'employeeStatus', this.initEmployeeStatuses);
    this.$set(this.resetData, 'employeeStatus', this.initEmployeeStatuses);
  },
  watch: {
    $route: {
      handler: function(to, from) {
        if (
          to.path.indexOf(
            '/personnel-report/departmentalEducationDistribution'
          ) > -1
        ) {
          //菜单参数获取
          let type = this.$route.params.type;
          if (type) {
            this.archivesType = type;
          } else {
            this.archivesType = '';
          }
          // 每次路由变化时会调用这个方法
          this.$nextTick(() => {
            this.refresh();
          });
        }
      }
    }
  },
  computed: {
    isNormalInstance() {
      return (
        this.$route.path !==
        '/personnel-report/departmentalEducationDistribution'
      );
    },
    deptParams() {
      let params = {};
      // if (!this.isNormalInstance) {
      //   params.employeeStatus = '89';
      // }
      params.archivesType = this.archivesType;
      return params;
    }
  },
  methods: {
    async refresh() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },
    footerCellClassName2({ $rowIndex, column, columnIndex }) {
      return 'font-bold';
    },
    checkItemTree(node) {
      this.idList = node.map(e => {
        return e.id;
      });
      this.handleRefreshTable();
    },
    handleExport() {
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.id = 'down-file-iframe';

      const form = document.createElement('form');
      form.method = 'get';
      form.target = iframe.id;
      form.action = '/ts-hrms/hrmsRpt/exportEducationDistr';

      let formData = {
        orgIds: this.idList.join(','),
        employeeStatus: this.searchForm.employeeStatus.join(','),
        archivesType: this.archivesType
      };
      Object.keys(formData).map(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = formData[key];
        form.appendChild(input);
      });

      iframe.appendChild(form);
      document.body.appendChild(iframe);

      form.submit();
      document.body.removeChild(iframe);

      this.exportLoading = true;
      setTimeout(() => {
        this.exportLoading = false;
      }, 1500);
    },
    handleChart() {
      this.$refs.dialogChart.open('学历分布', this.footerData[0], this.fileds);
    },
    async handleRefreshTable() {
      let param = {};
      if (this.idList.length) {
        param = { orgIds: this.idList.join(',') };
      }
      if (this.searchForm.employeeStatus.length) {
        param.employeeStatus = this.searchForm.employeeStatus.join(',');
      }
      param.archivesType = this.archivesType;
      let res = await this.ajax.getEducationDistr(param);
      let { fileds = [], dataList = [] } = res.object || {};
      let col = [];
      col = fileds.map(e => {
        return {
          label: e.item_name,
          prop: e.item_code,
          align: 'left',
          width: 100
        };
      });
      col.unshift({
        label: '科室名称',
        prop: 'org_name',
        align: 'left',
        width: 100
      });
      this.columns = col;
      this.fileds = fileds;
      let footerData = dataList.splice(dataList.length - 1, 1);
      this.footerData = footerData;
      this.$refs.table.refresh({
        rows: dataList
      });
    },
    handleReset() {
      this.$refs.searchTree.treeClass.checkAllNodes(false);
      return {};
    },
    // 获取人员状态
    async handleDictItemEmployeeStatus() {
      let res = await this.ajax.dictItemEmployeeStatus();
      if (res.success == false) {
        this.$message.error(res.message || '人员状态数据获取失败');
        return;
      }

      this.searchList.find(f => f.value == 'employeeStatus').childNodeList = (
        res.rows || []
      ).map(m => {
        return {
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        };
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.departmentalEducationDistribution {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  padding: 4px;
  ::v-deep {
    .left {
      width: 216px;
      height: 100%;
      padding-right: 8px;
      background: #fff;
    }
    .right {
      flex: 1;
      background: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      position: relative;
      border: 1px solid $primary-blue;
      border-radius: 4px;
      .trasen-search-content {
        padding: 12px;
      }
      & > .form-table {
        padding: 0 12px 12px 12px;
        margin-top: 2px;
        flex: 1;
        overflow: hidden;
        transform: scale(1);
        .red {
          color: $error-color;
        }
        .font-bold {
          background-color: #eceef3;
          .vxe-cell--item {
            font-weight: bold;
            color: $primary-blue;
          }
        }
      }
    }
  }
}
</style>
