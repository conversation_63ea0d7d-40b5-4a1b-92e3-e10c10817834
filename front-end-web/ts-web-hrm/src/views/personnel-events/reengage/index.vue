<template>
  <div class="reengage">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
      <template slot="right">
        <ts-button type="primary" @click="handleAdd">
          新增
        </ts-button>
        <ts-button type="primary" @click="handleExport" class="shallowButton">
          导出
        </ts-button>
        <ts-button
          @click="handleBatchApproval"
          v-show="hasOperateBtn.includes('approval')"
          class="shallowButton"
        >
          批量审批
        </ts-button>
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_reengage"
      class="form-table"
      ref="table"
      :defaultSort="{
        sidx: 'create_date',
        sord: 'desc'
      }"
      :columns="columns"
      @selection-change="handleSelectChange"
      @refresh="handleRefreshTable"
    />
    <dialog-add-or-edit ref="dialogAddOrEdit" @ok="handleRefreshTable" />
    <dialog-stop ref="dialogStop" @ok="handleRefreshTable" />
  </div>
</template>
<script>
import table from './minixs/table';
import dialogAddOrEdit from './components/dialog-addOrEdit.vue';
import dialogStop from './components/dialog-stop.vue';
export default {
  components: { dialogAddOrEdit, dialogStop },
  mixins: [table],
  data() {
    return {
      loading: false,
      selectList: []
    };
  },
  watch: {
    $route: {
      handler: function(to, from) {
        if (to.path.indexOf('personnel-events/reengage') > -1) {
          let { approvalStatus, employeeNo } = this.$route.query;
          if (approvalStatus || employeeNo) {
            this.$set(this.searchForm, 'employeeNo', employeeNo);
            this.$set(this.searchForm, 'approvalStatus', approvalStatus);
            // 每次路由变化时会调用这个方法
            this.search();
          }
        }
      },
      immediate: true // 如果需要在组件创建时立即触发，设置为true
    }
  },
  methods: {
    async refresh() {
      this.search();
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    // 新增
    handleAdd() {
      this.$refs.dialogAddOrEdit.open();
    },
    // 编辑
    handleEdit(row) {
      this.$refs.dialogAddOrEdit.open(row);
    },
    // 查看
    handleDetail(row) {
      this.$refs.dialogAddOrEdit.open(row, 'detail');
    },
    handleStop(row) {
      this.$refs.dialogStop.open(row);
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    handleExport() {
      let aDom = document.createElement('a'),
        queryList = [],
        { searchForm } = this.getQueryParam();
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        } else {
          queryList.push(key + '=' + searchForm[key]);
        }
      });
      aDom.href = '/ts-hrms/personnelIncident/export?' + queryList.join('&');
      aDom.click();
    },
    async handleDelete(row) {
      try {
        await this.$confirm(`您确认删除该条数据吗？`, '提示', {
          type: 'warning'
        });
        this.ajax.personnelIncidentDel(row.personnelIncidentId).then(res => {
          if (!res.success) return this.$newMessage('error', '【删除】失败');
          this.$newMessage('success', '【删除】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleApproval(row) {
      try {
        await this.$confirm(`您确认审批选中的数据吗？`, '提示', {
          type: 'warning'
        });
        this.ajax.incidentAudit(row.personnelIncidentId).then(res => {
          if (!res.success) return this.$newMessage('error', '【审批】失败');
          this.$newMessage('success', '【审批】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleBatchApproval() {
      if (this.selectList.length == 0) {
        this.$newMessage('warning', '请选择需要【审批】的数据');
        return false;
      }
      try {
        await this.$confirm(`您确认审批选中的数据吗？`, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        let ids = [];
        this.selectList.forEach(item => {
          if (item.approvalStatus != 4) {
            ids.push(item.personnelIncidentId);
          }
        });
        if (ids.length != this.selectList.length) {
          this.$newMessage('warning', '已审批数据无法重复【审批】');
        }
        if (ids.length == 0) return false;
        this.ajax.incidentAudit(ids.join(',')).then(res => {
          if (!res.success) return this.$newMessage('error', '【审批】失败');
          this.$newMessage('success', '【审批】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    getQueryParam() {
      let {
          completeDateList = [],
          doDateList = [],
          politicalStatusType = []
        } = this.searchForm,
        [startDate = '', endDate = ''] = completeDateList,
        [createStartDate = '', createEndDate = ''] = doDateList,
        pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          startDate,
          endDate,
          createStartDate,
          createEndDate,
          incidentCategory: 5,
          politicalStatusTypes: politicalStatusType.join(','),
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      delete searchForm.completeDateList;
      delete searchForm.doDateList;
      delete searchForm.politicalStatusType;
      return { searchForm, pageSize, pageNo };
    },
    async handleRefreshTable() {
      this.loading = true;
      let { searchForm, pageSize, pageNo } = this.getQueryParam();
      this.ajax.personnelIncidentList(searchForm).then(res => {
        this.loading = false;
        if (res.success == false) {
          this.$newMessage('error', res.message || '列表数据获取失败');
          return;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.reengage {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 4px;
  display: flex;
  flex-direction: column;
  /deep/ {
    .primary-span {
      color: $primary-blue;
      cursor: pointer;
    }
  }
}
</style>
