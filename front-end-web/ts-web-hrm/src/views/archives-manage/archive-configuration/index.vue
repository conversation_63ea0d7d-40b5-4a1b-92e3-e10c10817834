<template>
  <div class="container">
    <div class="left">
      <component-select-group
        ref="ComponentSelectGroup"
        :archivesType="archivesType"
        v-model="selectGroup"
        @change="handleChangeGroup"
      />
    </div>

    <div class="right">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :showLength="4"
        :resetData="resetData"
        :actions="actions"
        @search="search"
      >
        <template v-slot:showName>
          <ts-input
            placeholder="搜索字段名称"
            v-model="searchForm.showName"
            clearable
            @keyup.enter.native="search"
          />
        </template>
      </ts-search-bar>

      <TsVxeTemplateTable
        class="group-field-table"
        ref="table"
        id="cust_emp_config_list"
        :columns="columns"
        :hasPage="false"
        @refresh="handleRefreshTable"
      />
    </div>

    <drawer-add-field ref="DrawerAddField" @refresh="handleRefreshTable" />

    <drawer-set-group-auth ref="DrawerSetGroupAuth" />
    <drawer-set-manage-auth ref="DrawerSetManageAuth" />
  </div>
</template>

<script>
import Sortable from 'sortablejs';
import { deepClone } from '@/unit/commonHandle.js';
import tableMixins from './mixins/tableMixins.js';
import ComponentSelectGroup from './components/component-select-group.vue';
import DrawerAddField from './components/drawer-add-field.vue';
import DrawerSetGroupAuth from './components/drawer-set-group-auth.vue';
import DrawerSetManageAuth from './components/drawer-set-manage-auth.vue';
export default {
  mixins: [tableMixins],
  components: {
    ComponentSelectGroup,
    DrawerAddField,
    DrawerSetGroupAuth,
    DrawerSetManageAuth
  },
  data() {
    return {
      selectGroup: '',
      sortableInstance: null,
      archivesType: null
    };
  },
  created() {
    let type = this.$route.params.type;
    if (type) {
      this.archivesType = type;
    } else {
      this.archivesType = null;
    }
  },
  watch: {
    $route: {
      handler: function(to, from) {
        if (to.path.indexOf('archives-manage/archive-configuration') > -1) {
          let type = this.$route.params.type;
          if (type) {
            this.archivesType = type;
          } else {
            this.archivesType = null;
          }
          this.$nextTick(() => {
            this.refresh();
          });
        }
      }
    }
  },
  methods: {
    async refresh() {
      await this.$refs.ComponentSelectGroup.init();
      this.handleRefreshTable();
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleGroupAdd() {
      this.$refs.DialogAddGroup.open({
        data: {},
        archivesType: this.archivesType,
        title: '添加分组',
        type: 'add'
      });
    },

    handleChangeGroup() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    handleAddField() {
      this.$refs.DrawerAddField.open({
        data: {},
        archivesType: this.archivesType,
        selectGroup: this.selectGroup,
        type: 'add',
        title: '新增字段'
      });
    },

    async handleEditField() {
      let selectField = this.$refs.table.handleGetCurrentRecord();
      if (!selectField) {
        this.$message.warning('请选择一条记录进行操作！');
        return false;
      }

      let data = await this.handleGetCustomEmployeeGroupDetails(selectField.id);
      if (!data) {
        return;
      }
      this.$refs.DrawerAddField.open({
        data,
        selectGroup: this.selectGroup,
        type: 'edit',
        title: '编辑字段'
      });
    },

    async handleDeleteField() {
      let selectField = this.$refs.table.handleGetCurrentRecord();
      if (!selectField) {
        this.$message.warning('请选择一条记录进行操作！');
        return false;
      }

      if (['1', '2'].includes(selectField.fieldClass)) {
        this.$message.warning('系统级与标准级字段不能进行删除!');
        return false;
      }

      try {
        await this.$confirm('确认删除该条数据?', '提示', {
          type: 'warning'
        });
        let res = await this.ajax.customEmployeeFieldDelete(selectField.id);
        if (!res.success) {
          this.$message.error(res.message || '删除字段失败!');
          return false;
        }
        this.$message.success('操作成功!');
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    // 获取字段详情
    async handleGetCustomEmployeeGroupDetails(id) {
      try {
        let res = await this.ajax.customEmployeeFieldDetails(id);
        if (!res.success) {
          this.$message.error(res.message || '获取字段详情接口失败!');
          return false;
        }
        return res.object || {};
      } catch (error) {
        console.error(error);
        return false;
      }
    },

    handleSetGroupAuth() {
      this.$refs.DrawerSetGroupAuth.open({
        data: {},
        archivesType: this.archivesType,
        title: '设置分组权限'
      });
    },

    handleSetManageAuth() {
      this.$refs.DrawerSetManageAuth.open({
        data: {},
        title: '管理权限'
      });
    },

    async handleRefreshTable() {
      let list = await this.ajax.customEmployeeFieldList({
        archivesType: this.archivesType,
        isSyncView: '0',
        groupId: this.selectGroup,
        ...this.searchForm
      });
      let rows = list.map((item, i) => {
        return {
          index: i + 1,
          ...item
        };
      });
      this.$refs.table.refresh(rows);

      if (this.sortableInstance) {
        this.sortableInstance.destroy();
        this.sortableInstance = null;
      }

      if (this.selectGroup) {
        const _this = this;
        const groupContainer = document.querySelector(
          '.group-field-table tbody'
        );

        _this.sortableInstance = Sortable.create(groupContainer, {
          async onEnd({ newIndex, oldIndex }) {
            if (newIndex == oldIndex) return;
            let sortTable = deepClone(rows);
            const currRow = sortTable.splice(oldIndex, 1)[0];
            sortTable.splice(newIndex, 0, currRow);
            sortTable.forEach((f, i) => (f.seq = i + 1));

            const res = await _this.ajax.customEmployeeFieldUpdateGroupSeq(
              sortTable
            );
            if (!res.success) {
              _this.$message.error(res.message || '排序失败!');
              return;
            }
            _this.$message.success('排序成功!');
            _this.$refs.table.refresh([]);
            _this.handleRefreshTable();
          }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  background: #eee !important;
  padding: 0px !important;
  display: flex;
  .left {
    width: 206px;
    margin-right: 8px;
    padding: 8px 8px 0 8px;
    background: #fff !important;
    overflow: hidden;
    position: relative;
    height: 100%;
    ::v-deep .ts-button.is-disabled:hover:not(.el-button--text) {
      background: rgb(82, 96, 255);
      color: #fff;
    }
  }
  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 8px 8px 0 8px;
    background: #fff !important;
    ::v-deep {
      .group-img {
        margin-right: 8px;
        width: 16px;
        height: 16px;
      }
    }
  }
}
</style>
