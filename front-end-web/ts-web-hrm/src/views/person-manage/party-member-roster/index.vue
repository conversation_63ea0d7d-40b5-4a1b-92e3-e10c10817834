<template>
  <div class="party-member-roster">
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
      <template #right>
        <ts-button type="primary" @click="handleAdd">新增</ts-button>
        <ts-button class="shallowButton" @click="handleExport">导出</ts-button>
        <ts-button class="shallowButton" @click="handleSynchronization"
          >同步档案信息</ts-button
        >
      </template>
      <template slot="partyDate">
        <div style="display: flex; line-height: 30px;">
          <el-date-picker
            style="flex: 1;"
            class="date-picker"
            v-model="searchForm.partyDateStartTime"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="开始日期"
          >
          </el-date-picker>
          &nbsp;至&nbsp;
          <el-date-picker
            style="flex: 1;"
            class="date-picker"
            v-model="searchForm.partyDateEndTime"
            value-format="yyyy-MM-dd"
            placeholder="结束日期"
            :picker-options="pickerOptions1"
          >
          </el-date-picker>
        </div>
      </template>
    </ts-search-bar>
    <TsVxeTemplateTable
      id="table_party_member_roster"
      class="form-table"
      ref="table"
      :columns="columns"
      :defaultSort="{
        sidx: 'party_status,create_date',
        sord: 'desc'
      }"
      @selection-change="handleSelectChange"
      @refresh="handleRefreshTable"
    />
    <dialog-add ref="dialogAdd" @refresh="handleRefreshTable" />
  </div>
</template>
<script>
import table from './minixs/table';
import dialogAdd from './components/dialog-add.vue';
import moment from 'moment';
export default {
  components: { dialogAdd },
  mixins: [table],
  data() {
    return {
      loading: false,
      selectList: [],
      pickerOptions: {
        disabledDate: this.disabledDate
      },
      pickerOptions1: {
        disabledDate: this.disabledDate1
      }
    };
  },
  watch: {
    $route: {
      handler: function(to, from) {
        let { approvalStatus, employeeNo } = this.$route.query;
        this.$set(this.searchForm, 'nameOrCode', employeeNo);
        this.$set(this.searchForm, 'partyStatus', approvalStatus);
        // 每次路由变化时会调用这个方法
        this.search();
      },
      immediate: true // 如果需要在组件创建时立即触发，设置为true
    }
  },
  methods: {
    async refresh() {
      this.$nextTick(() => {
        this.search();
      });
    },
    disabledDate(time) {
      if (this.searchForm.partyDateEndTime) {
        if (moment(time).isAfter(moment(this.searchForm.partyDateEndTime))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    disabledDate1(time) {
      if (this.searchForm.partyDateStartTime) {
        if (moment(time).isBefore(moment(this.searchForm.partyDateStartTime))) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },
    search() {
      let num = 0;
      this.searchForm.partyDateStartTime && num++;
      this.searchForm.partyDateEndTime && num++;
      if (num == 1) {
        this.$newMessage('warning', '请选择完整的入党时间区间');
        return;
      }
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    getQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          status: this.activeTab,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageSize, pageNo };
    },
    handleAdd() {
      this.$refs.dialogAdd.open({ title: '新增', type: 'add' });
    },
    handleEdit(data) {
      this.$refs.dialogAdd.open({ title: '编辑', data, type: 'edit' });
    },
    handleDetail(data) {
      this.$refs.dialogAdd.open({ title: '详情', data, type: 'details' });
    },
    handleExport() {
      let aDom = document.createElement('a');
      let { searchForm } = this.getQueryParam();
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/partyMembers/export?${conditionList.join('&')}`;
      aDom.click();
    },
    async handleDelete(row) {
      try {
        await this.$confirm(
          `【<span style="color: red">删除</span>】该条数据？`,
          '提示',
          {
            showClose: true,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            customClass: 'new-el-message_box',
            cancelButtonClass: 'shallowButton'
          }
        );
        this.ajax.partyMembersDelete(row.id).then(res => {
          if (!res.success) {
            this.$newMessage('error', res.message || '【删除】失败');
            return;
          }
          this.$newMessage('success', '【删除】成功');
          this.search();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleApproval(row) {
      try {
        await this.$newConfirm(
          `【<span style="color: #295cf9">审批</span>】选中的数据？`
        );
        let data = {
          id: row.id,
          partyStatus: '1',
          empId: row.empId,
          politicalStatus: row.politicalStatus,
          partyDate: row.partyDate
        };
        this.ajax.partyMembersUpdate(data).then(res => {
          if (!res.success) {
            this.$newMessage('error', res.message);
            return;
          }
          this.$newMessage('success', res.message);
          this.handleRefreshTable();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleSynchronization() {
      try {
        await this.$newConfirm(
          `【<span style="color: #5260ff">同步</span>】是否同步档案信息？`
        );
        this.ajax.syncEmployeeData().then(res => {
          if (!res.success) {
            this.$newMessage('error', res.message);
            return;
          }
          this.$message.success(res.message);
          // this.$newMessage('success', res.message);
          this.handleRefreshTable();
        });
      } catch (e) {
        console.error(e);
      }
    },
    async handleRefreshTable() {
      this.loading = true;
      let { searchForm, pageSize, pageNo } = this.getQueryParam();
      this.ajax.getPartyMembersList(searchForm).then(res => {
        this.loading = false;
        if (res.success == false) {
          this.$message.error(res.message || '列表数据获取失败');
          return;
        }
        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.party-member-roster {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  /deep/ .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
    .primary-span {
      color: #295cf9;
      cursor: pointer;
      text-align: left;
    }
  }
}
</style>
