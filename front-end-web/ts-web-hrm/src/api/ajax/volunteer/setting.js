import { request } from '@/api/ajax';

// 新增活动
const voluntariesActivityMasterSave = function(params) {
  return request({
    url: '/ts-hrms/api/voluntariesActivityMaster/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: params
  });
};

// 新增活动的 时间记录
const voluntariesActivitySave = function(params) {
  return request({
    url: '/ts-hrms/api/voluntariesActivity/save',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: params
  });
};

// 编辑活动
const voluntariesActivityMasterUpdate = function(params) {
  return request({
    url: '/ts-hrms/api/voluntariesActivityMaster/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: params
  });
};

// 编辑活动的 时间记录
const voluntariesActivityUpdate = function(params) {
  return request({
    url: '/ts-hrms/api/voluntariesActivity/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: params
  });
};

// 删除活动
const voluntariesActivityMasterDelete = function(id) {
  return request({
    url: `/ts-hrms/api/voluntariesActivityMaster/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

// 删除活动 的时间段记录
const voluntariesActivityDelete = function(id) {
  return request({
    url: `/ts-hrms/api/voluntariesActivity/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

// 查询活动
const voluntariesActivityMasterList = function(params) {
  return request({
    url: '/ts-hrms/api/voluntariesActivityMaster/list',
    method: 'get',
    params
  });
};

// 查询活动的 时间段列表
const voluntariesActivityList = function(params) {
  return request({
    url: '/ts-hrms/api/voluntariesActivity/list',
    method: 'get',
    params
  });
};

//查询月打卡概况
const getWXClockMonthData = function(params) {
  return request({
    url: '/ts-oa/api/wxclockMonthdata/list',
    method: 'get',
    params
  });
};

export default {
  voluntariesActivityMasterSave,
  voluntariesActivitySave,

  voluntariesActivityMasterUpdate,
  voluntariesActivityUpdate,

  voluntariesActivityMasterList,
  voluntariesActivityList,

  voluntariesActivityMasterDelete,
  voluntariesActivityDelete,
  getWXClockMonthData
};
