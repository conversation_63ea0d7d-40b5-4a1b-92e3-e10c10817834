<template>
  <div class="base-select-content">
    <el-popover
      ref="popover"
      popper-class="base-select-popover"
      placement="bottom-start"
      trigger="click"
      :disabled="disabled"
      :width="popoverWidth"
      @show="handlePopoverShow"
      @hide="handlePopoverHide"
    >
      <template slot="reference">
        <ts-input
          v-model="insideText"
          :disabled="disabled || !searchable"
          :placeholder="isFocus ? inputText : placeholder"
          class="search-input"
          ref="insideText"
          :class="{
            'is-focus': isFocus,
            'can-not-search': !searchable
          }"
          @input="handleInput"
          @focus="hanldeFocus"
        >
          <span slot="suffix" class="action-suffix-content">
            <i
              class="el-input__icon el-icon-arrow-down"
              :class="{
                'has-clearable': clearable && modelValue
              }"
            ></i>
            <i
              class="el-input__icon el-icon-circle-close"
              @click.stop="clearInput"
              v-if="!disabled && clearable && modelValue"
            ></i>
          </span>
        </ts-input>
      </template>
      <div ref="templateContent" class="options-content">
        <el-scrollbar
          ref="scroll"
          :style="{
            height: '200px'
          }"
          v-infinity-scroll="{
            loadMethod: handleLoadData,
            selector: '.options-scrollbar',
            hasFinished: true,
            hasLoading: true
          }"
          wrap-class="options-scrollbar"
          wrap-style="overflow-x: hidden;"
        >
          <ul ref="templateList" style="margin: 0;padding: 0;">
            <li
              v-for="(item, index) of options"
              :key="index"
              class="option-item flex-row-between flex-col-center"
              :class="{
                'active-option-item': modelValue == item[value]
              }"
              @click="handleSelect(item)"
            >
              <slot :data="item">
                {{ item[label] }}
              </slot>
              <i class="el-icon-check"></i>
            </li>
          </ul>
        </el-scrollbar>
      </div>
    </el-popover>
  </div>
</template>

<script>
/**
 * @param {String} placeholder 当选择框值为空时 input 的 placeholder
 * @param {Function} loadMethod 加载数据的方法
 * @param {String} inputText 选中的值的文本显示
 * @param [Number, String] delay 输入防抖时间
 * @param String searchInputName 搜索时的input
 * @param String label 选项展示的内容名称
 * @param String value 选项选中的值名称
 * @param Boolean canQuit 再次点击是否可以取消选择
 * @param Boolean searchable 是否可以搜索
 */
import infinityScroll from './infinityScroll';

export default {
  mixins: [infinityScroll],
  model: {
    prop: 'modelValue',
    event: 'input'
  },
  props: {
    modelValue: {},
    placeholder: {
      type: String,
      default: () => '请选择'
    },
    loadMethod: Function,
    inputText: String,
    delay: {
      type: [Number, String],
      default: () => 500
    },
    searchInputName: String,
    label: {
      type: String,
      default: () => 'name'
    },
    value: {
      type: String,
      default: () => 'id'
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    canQuit: Boolean,
    searchable: {
      type: Boolean,
      default: () => true
    },
    data: Array
  },
  data() {
    return {
      wrapDom: null,
      insideText: '',
      insideValue: '',
      insidePlaceHolder: '',
      pageNo: 1,
      options: [],

      inputTimer: null,
      isFocus: false,
      popoverWidth: null
    };
  },
  watch: {
    inputText: {
      handler(val) {
        this.insideText = val;
        this.insidePlaceHolder = val;
      },
      immediate: true
    },
    modelValue: {
      handler(val) {
        this.insideValue = val;
      },
      immediate: true
    }
  },
  async mounted() {
    await this.$nextTick();
    this.wrapDom = this.$refs.scroll.$el.querySelector('.options-scrollbar');
    this.popoverWidth = this.$refs.insideText.$el.clientWidth;
  },
  methods: {
    async handleLoadData(cb) {
      if (this.loadMethod) {
        let data = { pageNo: this.pageNo };
        data[this.searchInputName || 'name'] = this.insideText;

        let res = await this.loadMethod(data);
        cb(!(res && res.length));
        if (this.pageNo == 1) this.options = [];

        if (res && res.length) {
          this.options.push(...res);
        }
        this.pageNo++;
      } else {
        this.options = (this.data || []).filter(item => {
          return item[this.label].includes(this.insideText);
        });
        cb(true);
      }
    },
    handlePopoverShow() {
      this.pageNo = 1;
      this.options = [];
      this.isFocus = true;
      this.insideText = '';
      this.wrapDom.resetInfinityScrolling();
    },
    handlePopoverHide() {
      this.isFocus = false;
      this.insideText = this.inputText;
    },
    /**@desc 输入防抖 */
    handleInput() {
      this.inputTimer && clearTimeout(this.inputTimer);
      this.inputTimer = setTimeout(() => {
        this.pageNo = 1;
        this.options = [];
        this.wrapDom.resetInfinityScrolling();
      }, Number(this.delay));
    },
    /**@desc focus 事件 */
    hanldeFocus() {
      this.insideText = '';
      this.popoverWidth = this.$refs.insideText.$el.clientWidth;
    },
    handleSelect(item) {
      let value = item[this.value],
        selectedVal = '';
      if (value == this.modelValue) {
        if (this.canQuit) {
          this.$emit('input', null);
          this.$emit('update:inputText', null);
        }
      } else {
        selectedVal = value;
        this.$emit('input', value);
        this.$emit('update:inputText', item[this.label]);
      }
      this.$emit('select', item, this.modelValue, selectedVal);
      this.$refs.popover.doClose();
    },
    clearInput() {
      this.$emit('input', null);
      this.$emit('update:inputText', null);
      this.$refs.popover.doClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.base-select-content {
  display: inline-block;
}
.option-item {
  line-height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  padding: 0 $primary-spacing;
  &:hover {
    background-color: $list-hover-color;
  }
  .el-icon-check {
    font-weight: inherit;
    display: none;
    flex-shrink: 0;
  }

  &.active-option-item {
    color: $primary-blue;
    font-weight: 600;
    .el-icon-check {
      display: inline-block;
    }
  }
}
.search-input {
  &.can-not-search.is-focus .el-icon-arrow-down,
  &.is-focus .el-icon-arrow-down {
    transform: rotate(180deg);
  }
  .action-suffix-content .el-icon-circle-close {
    display: none;
    cursor: pointer;
  }
  &:hover {
    .action-suffix-content .el-icon-circle-close {
      display: inline-block;
    }
    .el-icon-arrow-down.has-clearable {
      display: none;
    }
  }
  /deep/ &.can-not-search {
    &.is-focus .el-input__inner {
      border-color: $primary-blue !important;
    }
    .el-input__inner {
      cursor: pointer !important;
      background-color: #fff !important;
    }
  }
}
</style>

<style lang="scss">
.base-select-popover {
  padding: $primary-spacing 0;
}
</style>
