/**@desc 路由组件混入获取菜单权限 */
function routerResolve(resolve) {
  let _this = this;
  return function(module) {
    let component = module.default;
    !component.mixins && (component.mixins = []);
    component.mixins.push({
      data() {
        return {
          menuLimits: [] // 路由按钮权限按钮
        };
      },
      mounted() {
        this.getMenuLimitList();
      },
      methods: {
        async getMenuLimitList() {
          let alink =
            this.$route.path.indexOf('ts-web-') > -1
              ? this.$route.path
              : `/ts-web-hrm${this.$route.path}`;
          let menu = this.$getParentStoreInfo('menuLineList').find(
            e => e.alink === alink
          );
          if (menu) {
            let res = await this.ajax.getUserMenuSourceData(menu.id);
            this.menuLimits = res.length ? res : [];
          }
        }
      }
    });
    return resolve.call(_this, module);
  };
}
export default [
  {
    path: '/pay-manager/new-pay-programme',
    component: resolve =>
      require([`@/views/pay-manager/new-pay-programme/index.vue`], resolve),
    styleName: '',
    name: '薪酬方案(新)'
  },
  {
    path: '/pay-manager/basis-setting',
    component: resolve =>
      require([`@/views/pay-manager/basis-setting/index.vue`], resolve),
    styleName: '',
    name: '基础配置'
  },
  {
    path: '/pay-manage/new-salary-accounting',
    component: resolve =>
      require([`@/views/pay-manager/new-salary-accounting/index.vue`], resolve),
    styleName: '',
    name: '薪酬核算(新)'
  },
  {
    path: '/pay-manage/salary-profile',
    component: resolve =>
      require([`@/views/pay-manager/salary-profile/index.vue`], resolve),
    styleName: '',
    name: '薪酬档案'
  },
  {
    path: '/pay-manage/send-payslip',
    component: resolve =>
      require([`@/views/pay-manager/send-payslip/index.vue`], resolve),
    styleName: '',
    name: '发送工资条'
  },
  {
    path: '/pay-manage/salary-report',
    component: resolve =>
      require([`@/views/pay-manager/salary-report/index.vue`], resolve),
    styleName: '',
    name: '薪酬报表'
  },
  {
    path: '/pay-manage/salary-allocation',
    component: resolve =>
      require([`@/views/pay-manager/salary-allocation/index.vue`], resolve),
    styleName: '',
    name: '工龄工资配置'
  },
  {
    path: '/pay-manage/yongyou',
    component: resolve =>
      require([`@/views/pay-manager/yongyou/index.vue`], resolve),
    styleName: '',
    name: '用友财务接口'
  },
  {
    path: '/pay-manage/salary-setting',
    component: resolve =>
      require([`@/views/pay-manager/salary-setting/index.vue`], routerResolve(
        resolve
      )),
    styleName: '',
    name: '薪酬配置'
  },
  {
    path: '/pay-manage/person-salary-edger',
    component: resolve =>
      require([`@/views/pay-manager/person-salary-edger/index.vue`], resolve),
    styleName: '',
    name: '员工工资台账'
  },
  {
    path: '/pay-manage/report-configuration',
    component: resolve =>
      require([`@/views/pay-manager/report-configuration/index.vue`], resolve),
    styleName: '',
    name: '薪酬报表配置'
  },
  {
    path: '/pay-manage/report-relationship',
    component: resolve =>
      require([`@/views/pay-manager/report-relationship/index.vue`], resolve),
    styleName: '',
    name: '上报关系'
  },
  {
    path: '/pay-manage/dept-report',
    component: resolve =>
      require([`@/views/pay-manager/dept-report/index.vue`], resolve),
    styleName: '',
    name: '科室上报'
  },
  {
    path: '/pay-manage/report-approval',
    component: resolve =>
      require([`@/views/pay-manager/report-approval/index.vue`], resolve),
    styleName: '',
    name: '上报审批'
  },
  {
    path: '/pay-manage/person-promotion',
    component: resolve =>
      require([`@/views/pay-manager/person-promotion/index.vue`], resolve),
    styleName: '',
    name: '人员晋升'
  },
  {
    path: '/pay-manage/salary-change-order',
    component: resolve =>
      require([`@/views/pay-manager/salary-change-order/index.vue`], resolve),
    styleName: '',
    name: '薪酬异动单'
  },
  {
    path: '/pay-manager/newsalary-temporary-adjust',
    component: resolve =>
      require([
        `@/views/pay-manager/newsalary-temporary-adjust/index.vue`
      ], resolve),
    styleName: '',
    name: '薪酬调整'
  },
  {
    path: '/pay-manager/newsalary-remind',
    component: resolve =>
      require([`@/views/pay-manager/newsalary-remind/index.vue`], resolve),
    styleName: '',
    name: '薪酬提醒'
  }
  // ,
  // {
  //   path: '/pay-manager/newsalary-performance/performance-manage',
  //   component: resolve =>
  //     require([
  //       `@/views/pay-manager/newsalary-performance/performance-manage/index.vue`
  //     ], resolve),
  //   styleName: '',
  //   name: '薪酬绩效'
  // },
  // {
  //   path: '/pay-manager/newsalary-performance/performance-report',
  //   component: resolve =>
  //     require([
  //       `@/views/pay-manager/newsalary-performance/performance-report/index.vue`
  //     ], resolve),
  //   styleName: '',
  //   name: '绩效上报'
  // }
];
