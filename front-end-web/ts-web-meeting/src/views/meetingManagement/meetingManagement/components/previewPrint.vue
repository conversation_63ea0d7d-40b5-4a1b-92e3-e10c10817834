<template>
  <el-dialog
    custom-class="dialog-preview-print"
    :title="title"
    width="820px"
    :close-on-click-modal="false"
    :visible.sync="visible"
    @close="handleCancel"
    top="5vh"
  >
    <div
      ref="pdfContent"
      class="meeting-management-pdf-content"
      v-if="printData"
    >
      <table class="noborder">
        <tr class="noborder">
          <td class="noborder nopadding">
            <h2 class="table-name">
              {{ printData.webTitle }}
            </h2>
            <h4 class="table-name fs18">
              会议室申请审批表
            </h4>
          </td>
        </tr>

        <tr class="noborder">
          <td class="noborder nopadding">
            <template>
              <table style="border-top: 1px solid #333 !important;">
                <tr>
                  <td style="width: 12%;">申请人</td>
                  <td style="width: 15%;">{{ printData.employeeName }}</td>
                  <td style="width: 9%;">申请科室</td>
                  <td style="width: 16%;">{{ printData.orgName }}</td>
                  <td style="width: 12%;">申请时间</td>
                  <td style="width: 20%;">
                    {{ printData.destineDate | formatDateTime }}
                  </td>
                </tr>

                <tr>
                  <td>会议类型</td>
                  <td colspan="2">{{ printData.appTypeName }}</td>
                  <td>参会人数</td>
                  <td colspan="2">{{ printData.controlNumber }}</td>
                </tr>

                <tr style="height: 85px;;">
                  <td>会议主题</td>
                  <td colspan="6">
                    {{ printData.motif }}
                  </td>
                </tr>

                <tr style="height: 50px;">
                  <td>会议地址</td>
                  <td colspan="6">
                    {{ printData.location }}
                  </td>
                </tr>

                <tr style="height: 35px;">
                  <td>拟使用设备</td>
                  <td colspan="6">
                    {{ printData.device }}
                  </td>
                </tr>

                <tr style="height: 65px;">
                  <td>参会人员</td>
                  <td colspan="6">
                    {{ printData.names }}
                  </td>
                </tr>

                <tr>
                  <td>会议开始时间</td>
                  <td colspan="2">
                    {{ printData.startTime | formatDateTime }}
                  </td>
                  <td>会议结束时间</td>
                  <td colspan="2">{{ printData.endTime | formatDateTime }}</td>
                </tr>

                <tr style="height: 60px;">
                  <td>备注</td>
                  <td colspan="6">
                    {{ printData.remark }}
                  </td>
                </tr>

                <tr style="height: 85px;;" v-show="hasProcess">
                  <td>{{ printData.wfStepName }}</td>
                  <td colspan="6">
                    <div class="btm-process-status">
                      {{ printData.processStatusLabel }}
                      {{ printData.remark }}
                    </div>
                    <div class="btm-time-status">
                      签名： {{ printData.actAssigneeName }}
                    </div>
                    <div class="btm-time-status">
                      {{ printData.finishedDate | formatDateTime }}
                    </div>
                  </td>
                </tr>
              </table>
            </template>
          </td>
        </tr>
      </table>
    </div>
    <div slot="footer" class="dialog-footer">
      <span class="ts-button primary" @click="handlePrint"> 打 印 </span>
      <span class="ts-button" @click="handleCancel"> 取 消 </span>
    </div>
  </el-dialog>
</template>

<script>
import print from '@/unit/print.js';
export default {
  data() {
    return {
      visible: false,
      printData: null,
      hasProcess: false,
      title: '预览打印'
    };
  },

  filters: {
    formatDateTime(value) {
      if (!value) return '';
      const date = new Date(value);
      if (isNaN(date.getTime())) return value;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
    }
  },

  methods: {
    async open({ data }) {
      const {
        motif,
        location,
        startTime,
        endTime,
        wfInstId,
        applyId,
        appTypeId,
        controlNumber = '',
        applyEmployee = {},
        currentEmpPower,
        meetingStatus
      } = data;
      const formattedControlNumber = controlNumber ? `${controlNumber}人` : '';

      this.printData = {
        motif,
        location,
        controlNumber: formattedControlNumber,
        employeeName: applyEmployee.employeeName || '',
        orgName: applyEmployee.orgName || '',
        startTime,
        endTime,
        webTitle: this.$getParentStoreInfo('globalSetting')?.webTitle,
        processStatusLabel:
          currentEmpPower.indexOf('2') !== -1 && meetingStatus === 4
            ? '撤销'
            : '同意'
      };

      const [meetingRoomTypesRes, bookingDetailsRes] = await Promise.all([
        this.ajax.getDictDatas({ typeCode: 'MEETING_TYPE' }),
        this.ajax.getBoardRoomApply({ id: applyId })
      ]);
      const meetingRoomTypes = meetingRoomTypesRes.object || [];
      const appType =
        meetingRoomTypes.find(item => item.itemNameValue === appTypeId) || {};
      this.printData.appTypeName = appType.itemName || '';
      if (bookingDetailsRes.success) {
        const {
          device = '',
          destineDate = '',
          remark = '',
          attendEmployeeList = []
        } = bookingDetailsRes.object || {};

        Object.assign(this.printData, {
          device,
          destineDate,
          remark,
          names: attendEmployeeList.map(m => m.username).join(',')
        });
      }

      await this.handleGetProcessLastInfo(wfInstId);
      this.visible = true;
    },

    async handleGetProcessLastInfo(wfInstId) {
      if (!wfInstId) {
        return;
      }
      const res = await this.ajax.findTaskHisCommentList({
        wfInstId
      });

      if (!res.success) {
        this.$message({
          showClose: true,
          message: res.message || '操作失败, 请联系管理员!',
          type: 'error'
        });
        return;
      }

      if (!Array.isArray(res.object) || !res.object?.[0]) {
        return;
      }

      let lastInfo = res.object[0] || {};
      let { wfStepName, actAssigneeName, remark, finishedDate } = lastInfo;
      this.printData.wfStepName = wfStepName;
      this.printData.actAssigneeName = actAssigneeName;
      this.printData.remark = remark && remark !== 'null' ? `(${remark})` : '';
      this.printData.finishedDate = finishedDate;
      this.hasProcess = true;
    },

    async handlePrint() {
      try {
        print(this.$refs.pdfContent);
      } catch (error) {}
    },

    handleCancel() {
      this.printData = null;
      this.hasProcess = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss">
.meeting-management-pdf-content {
  width: 100%;
  height: 618px;
  margin: auto;
  font-family: Times New Roman !important;

  .noborder {
    border: 0px !important;
  }

  .table-name {
    font-size: 22px;
    margin-bottom: 4px;
    color: rgba(0, 0, 0, 0.75);
  }

  .fs18 {
    font-size: 18px;
  }

  table {
    width: 100% !important;
    border: 1px solid #333;
    border-top: 0px !important;

    font-weight: 400 !important;
    font-family: Times New Roman !important;
    page-break-inside: avoid;

    img {
      width: 100%;
      height: 100%;
    }

    tr {
      height: 28px;
      border-bottom: 1px solid #333;
      p {
        margin: 4px 0;
      }
      .row-align {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    td {
      text-align: center;
      font-family: Times New Roman !important;
      border-right: 1px solid #333;
      padding: 8px 2px;

      &.nopadding {
        padding: 0;
      }
    }

    .btm-process-status {
      padding-left: 12px;
      text-align: left;
    }

    .btm-time-status {
      text-align: right;
      padding-right: 12px;
    }
  }
}

.dialog-preview-print {
  .dialog-footer {
    .primary {
      margin-right: 8px;
    }
  }
}

@media print {
  thead {
    display: table-header-group !important;
  }
}
</style>
