<template>
  <div class="date-view-table" ref="calendarContainer">
    <div class="header-search">
      <div class="resource_box">
        <div
          :class="[
            'resource_box_item',
            queryParam.subscribeStatus == '2' ? 'check' : ''
          ]"
          @click="toggleSubscription('2')"
        >
          我的订阅
        </div>
        <div class="resource_box_line"></div>
        <div
          :class="[
            'resource_box_item',
            queryParam.subscribeStatus !== '2' ? 'check' : ''
          ]"
          @click="toggleSubscription('')"
        >
          全部资源
        </div>
      </div>
      <div class="header-container">
        <i
          class="el-icon-arrow-left date-check"
          @click="dateBack(queryParam.time)"
        ></i>
        <el-date-picker
          type="date"
          :clearable="false"
          v-model="queryParam.time"
          @change="handleChange"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        >
        </el-date-picker>
        <i
          class="el-icon-arrow-right date-check"
          @click="dateNext(queryParam.time)"
        ></i>
        <div class="content">
          <div>
            <span
              @click="toggleDate(today)"
              :class="['date-button', today === queryParam.time ? 'check' : '']"
            >
              今天
            </span>
            <span
              @click="toggleDate(tomorrow)"
              :class="[
                'date-button',
                tomorrow === queryParam.time ? 'check' : ''
              ]"
            >
              明天
            </span>
            <span class="searchMore" @click="showSeachMore">{{
              showSearch ? '更多搜索' : '收起搜索'
            }}</span>
          </div>
          <div class="appointment-tips">
            请在对应的时间下方空白处单击 预约会议
          </div>
          <div>
            <div class="desc-item">
              <span style="background: #e4e4e4"></span>
              禁用/未开放
            </div>
            <div class="desc-item">
              <span style="background: #D5D9FF"></span>
              我的预约
            </div>
            <div class="desc-item">
              <span style="background: #FFDEDE"></span>
              他人预约
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 表头 -->
    <el-table class="calendar_table_header" style="width: 100%" row-key="id">
      <el-table-column :resizable="false" :width="titleWidth - 25">
        <!-- 资源 -->
        <!-- <template #header>
        </template> -->
        <!-- 统计 -->
        <template #header>
          <div class="subscribe_box">
            <div class="subscribe_box_item">共{{ tableData.length }}个资源</div>
            <div
              v-if="queryParam.subscribeStatus == '2'"
              class="subscribe_box_item"
              @click="subscribeManagement"
            >
              订阅管理
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column :resizable="false">
        <!-- 搜索条件 -->
        <template #header>
          <el-table-column width="80" :resizable="false">
            <template v-slot:header>
              <span class="narrow">
                00:00-06:00
              </span>
            </template>
          </el-table-column>
          <el-table-column
            :resizable="false"
            v-for="(timeItem, idx) in timelineHeader"
            :key="idx"
            :label="timeItem.label"
            :width="calendarContainerWidth"
          >
          </el-table-column>
          <el-table-column :resizable="false" width="80">
            <template v-slot:header>
              <span class="narrow">
                22:00-23:59
              </span>
            </template>
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>
    <!-- 主体 -->
    <div class="calendar_table_body">
      <el-scrollbar style="height: 100%; width: 100%">
        <div class="calendar_table_body_relative">
          <el-table
            style="width: 100%"
            :data="tableData"
            row-key="id"
            border
            :show-header="false"
            @cell-click="handleCellClick"
          >
            <el-table-column
              :width="titleWidth"
              :resizable="false"
              label="header"
            >
              <template slot-scope="scope">
                <el-tooltip
                  class="item"
                  :disabled="!scope.row.remark"
                  :content="scope.row.remark"
                  placement="right"
                >
                  <div class="title-card">
                    <div class="title">
                      <div class="label">
                        {{ scope.row.name }}
                      </div>
                      <div class="value">
                        <i class="el-icon-user-solid"></i>
                        {{ scope.row.capacitance }}人
                      </div>
                    </div>
                    <div class="address">
                      <i class="el-icon-location-outline"></i>
                      {{
                        `${scope.row.location || ''} ${
                          scope.row.floor ? '-' + scope.row.floor + '层' : ''
                        }`
                      }}
                    </div>
                    <div class="device">
                      <span>
                        <i class="el-icon-printer"></i>
                        {{ getDeviceName(scope.row.deviceList || []) }}
                      </span>
                    </div>
                  </div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="LEFT_OTHERS"
              label="00:00-06:00"
              :resizable="false"
              width="80"
            >
              <template slot-scope="scope">
                <div
                  class="other-time"
                  @click="handleOtherTime('left', scope.row)"
                >
                  {{
                    scope.row.boardroomApplyList &&
                    scope.row.boardroomApplyList.length > 0
                      ? getOtherTime(scope.row.boardroomApplyList || [])
                      : ''
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              v-for="(timeItem, idx) in timelineHeader"
              :key="idx"
              :label="timeItem.label"
              :width="calendarContainerWidth"
            >
              <template slot-scope="scope">
                <div
                  class="cell-add"
                  @mouseenter="showAdd(timeItem.label, scope.row)"
                  @mouseleave="hideAdd(timeItem.label, scope.row)"
                >
                  <div
                    class="cell-show"
                    v-show="
                      showRowId === scope.row.id &&
                        showClumnsLabel === timeItem.label
                    "
                  >
                    <span>+新增预约</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              :resizable="false"
              prop="RIGHT_OTHERS"
              label="22:00-23:59"
              width="80"
            >
              <template slot-scope="scope">
                <div
                  class="other-time"
                  @click="handleOtherTime('right', scope.row)"
                >
                  {{
                    scope.row.boardroomApplyList &&
                    scope.row.boardroomApplyList.length > 0
                      ? getOtherEndTime(scope.row.boardroomApplyList || [])
                      : ''
                  }}
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 未开放时段 -->
          <div
            :sss="calendarContainerWidth"
            :titleWidth="titleWidth"
            v-for="(unopenItem, unopenIndex) in unopenList"
            :style="getUnopenStyle(unopenItem)"
            :key="`unopenIndex:${unopenIndex}`"
            class="disable_card"
          >
            <el-tooltip
              :open-delay="200"
              content="未开放时段"
              placement="top-start"
            >
              <div class="box"></div>
            </el-tooltip>
          </div>
          <!-- 禁用时段 -->
          <div
            :sss="calendarContainerWidth"
            :titleWidth="titleWidth"
            v-for="(disableItem, disableIndex) in disableList"
            :style="getBookingStyle(disableItem, { type: 'disable' })"
            :key="`disableIndex:${disableIndex}`"
            class="disable_card"
          >
            <el-tooltip
              :open-delay="200"
              :disabled="!disableItem.remark"
              placement="top-start"
            >
              <template v-slot:content>
                <div class="disable_style">
                  <div>禁用时段</div>
                  <div>原因：{{ disableItem.remark }}</div>
                  <div>联系人：{{ contactsName(disableItem.contacts) }}</div>
                </div>
              </template>
              <div class="box"></div>
            </el-tooltip>
          </div>
          <!-- 预约模块 -->
          <div
            :sss="calendarContainerWidth"
            :titleWidth="titleWidth"
            v-for="(bookingItem, bookingIdx) in bookingList"
            :style="getBookingStyle(bookingItem, { type: 'booking' })"
            :key="`bookingIdx:${bookingIdx}`"
            class="booking_card"
            @click="hanleOpenInfo(bookingItem)"
          >
            <el-popover
              placement="top"
              width="260"
              trigger="hover"
              popper-class="bookingCardPop"
            >
              <div class="bookingCardTip">
                <div class="title">
                  <i class="el-icon-reading"></i>
                  <div :title="bookingItem.motif">
                    主题：{{ bookingItem.motif || '' }}
                  </div>
                </div>
                <div>
                  <i class="el-icon-time"></i>
                  预约时段：{{ bookingItem.startTime | getTime }}~{{
                    bookingItem.endTime | getTime
                  }}
                </div>
                <div>
                  <i class="el-icon-user-solid"></i>
                  预约人：{{ bookingItem.applyEmployee.employeeName || '' }}
                </div>
                <div>
                  <i class="el-icon-user"></i>
                  参与人：{{ bookingItem.controlNumber || 0 }}人
                </div>
                <div>
                  <i class="el-icon-s-check"></i>
                  {{ bookingItem.statusLable || '' }}
                </div>
              </div>
              <div slot="reference" class="booking_card_card">
                <div>{{ bookingItem.applyEmployee.orgName || '' }}</div>
                <div>{{ bookingItem.applyEmployee.employeeName || '' }}</div>
                <div>
                  {{ isHidden(bookingItem) }}
                </div>
                <div>
                  {{ bookingItem.statusLable || '' }}
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <details-modal ref="detailsModal" @ok="loadData"></details-modal>
    <booking-info-modal ref="bookingInfoModal"></booking-info-modal>
    <booking-info-list-modal
      @ok="loadData"
      ref="bookingInfoListModal"
    ></booking-info-list-modal>
    <subscribe-management
      ref="subscribeManagement"
      @change="loadData"
    ></subscribe-management>
    <add-meeting-room-modal
      ref="addMeetingRoomModal"
      @ok="loadData"
    ></add-meeting-room-modal>
  </div>
</template>

<script>
import SubscribeManagement from './subscribeManagement.vue';
import DetailsModal from '../details/index';
import AddMeetingRoomModal from '@/views/meetingManagement/meetingRoomManagement/addMeetingRoomModal';
import BookingInfoModal from './bookingInfoModal.vue';
import BookingInfoListModal from './bookingInfoListModal.vue';
import { commonUtils } from '@/utils/index.js';
import moment from 'moment';
// 时间轴
let timelineHeader = [];
for (let i = 7; i < 22; i++) {
  timelineHeader.push({ id: i, label: i < 10 ? `0${i}:00` : `${i}:00` });
}

export default {
  components: {
    DetailsModal,
    BookingInfoModal,
    BookingInfoListModal,
    SubscribeManagement,
    AddMeetingRoomModal
  },
  data() {
    return {
      usercode: '',
      calendarContainerWidth: '', // 元素宽度
      timelineHeader, // 时间轴
      timelineData: [],
      tableData: [],
      // 具体数据
      // 查询条件
      queryParam: {
        time: moment().format('YYYY-MM-DD'),
        startTime: '',
        endTime: '',
        subscribeStatus: '3'
      },
      titleWidth: 170, // 表头宽度
      time: '',
      today: moment().format('YYYY-MM-DD'),
      tomorrow: moment()
        .day(moment().day() + 1)
        .format('YYYY-MM-DD'),
      unopenList: [], //未开放模块
      bookingList: [],
      disableList: [],
      dataSource: {},
      showRowId: '',
      showClumnsLabel: '',
      showSearch: true
    };
  },
  created() {
    this.getListBoardRoomSubscribe();
    this.timelineData = this.createTimeUnitListByTimeRange(
      '2021-11-16 07:30:00',
      '2021-11-16 22:00:00',
      60000 * 30
    );
    this.usercode = this.$getCookiesInfo('usercode');
  },
  filters: {
    getTime(val) {
      return (
        val.split(' ')[1].split(':')[0] + ':' + val.split(' ')[1].split(':')[1]
      );
    }
  },
  mounted() {
    const vm = this;
    // 窗口监听 防抖
    this.__resizeHanlder = commonUtils.debounce(() => {
      let clientWidth =
        vm.$refs.calendarContainer.clientWidth - 160 - vm.titleWidth;
      this.titleWidth = 175;
      let clientWidthList = clientWidth.toString().split('');
      // 位数相加
      const num = clientWidthList.reduce((total, num) => {
        return parseInt(total) + parseInt(num);
      });
      if (!(num % 3 === 0 && [0, 5].indexOf(clientWidth % 10) !== -1)) {
        let num15 = clientWidth % 15;

        if (num15 > 8) {
          vm.titleWidth = vm.titleWidth - (15 - num15);
          clientWidth = clientWidth + (15 - num15);
        } else {
          vm.titleWidth = vm.titleWidth + num15;
          clientWidth = clientWidth - num15;
        }
      } else {
      }
      vm.calendarContainerWidth = clientWidth / this.timelineHeader.length;

      if (vm.calendarContainerWidth % 2 !== 0) {
        vm.calendarContainerWidth -= 1;
      }
      vm.titleWidth =
        vm.$refs.calendarContainer.clientWidth -
        162 -
        vm.calendarContainerWidth * this.timelineHeader.length;
    }, 500);
    this.__resizeHanlder();
    window.addEventListener('resize', this.__resizeHanlder);
    // 监听主应用
    window.addEventListener(
      'collapseChangeBack',
      res => {
        this.__resizeHanlder();
      },
      false
    );
  },
  beforeDestroy() {
    // 销毁监听
    window.removeEventListener('resize', this.__resizeHanlder);
    window.removeEventListener('collapseChangeBack', res => {}, false);
  },
  methods: {
    /*
     *@params startDate 开始时间
     *@params endDate 结束时间
     *@params timeUnit 时间间隔,注意是毫秒数
     **/
    createTimeUnitListByTimeRange(startDate, endDate, timeUnit) {
      let startSeconds = new Date(startDate).getTime();
      let endSeconds = new Date(endDate).getTime();

      // 必须用计算机初始时间的时间戳来作为基准点，否则时区会影响初始时间戳毫秒数
      let base = new Date('1970-01-01 00:00:00').getTime();

      let rangeTimeUnitList = [];
      let firstDegree;

      // 第一个刻度，可能刚好在你需要的整点刻度上，如果不在整点上，减去多余的部分，往前推一个刻度。
      // 此处就是减掉基准时间戳再执行整除操作，否则如果以天为刻度，整除86400000，得到的第一个刻度会是包含时区的时间，如北京时间：2020-09-10 08：00：00
      firstDegree = startSeconds - ((startSeconds - base) % timeUnit);

      rangeTimeUnitList.push(moment(firstDegree).format('HH:mm'));

      // 当最后一个刻度大于截止时间，停止创建刻度数据
      while (firstDegree < endSeconds) {
        firstDegree += timeUnit;
        rangeTimeUnitList.push(moment(firstDegree).format('HH:mm'));
      }

      return rangeTimeUnitList;
    },
    showSeachMore() {
      this.showSearch = !this.showSearch;
      this.$emit('showSearch');
    },
    showAdd(idx, row) {
      this.showClumnsLabel = idx;
      this.showRowId = row.id;
    },
    hideAdd() {
      this.showClumnsLabel = '';
      this.showRowId = '';
    },
    // 列表数据
    async loadData() {
      if (
        this.queryParam.subscribeStatus != '' &&
        this.queryParam.subscribeStatus != '2'
      )
        return;
      try {
        let params = JSON.parse(JSON.stringify(this.queryParam));
        params.startTime = params.time + ' 00:00:00';
        params.endTime = params.time + ' 23:59:59';
        // params.isVideo = this.$parent.situationActive;
        const parentQueryParam = this.$parent.queryParam;
        params = Object.assign(params, parentQueryParam);
        if (params.freeStartTime) {
          params.freeStartTime =
            params.time + ' ' + params.freeStartTime + ':00';
          params.freeEndTime = params.time + ' ' + params.freeEndTime + ':00';
        }
        const res = await this.ajax.getBoardRoomApplyTimeDetailList(params);
        this.tableData = res.object || [];
        this.unopenList = [];
        this.bookingList = [];
        this.disableList = [];
        this.tableData.forEach((e, i) => {
          this.unopenList.push({
            index: i,
            start: '00:00',
            end: e.bookingTimeBegin
          });
          this.unopenList.push({
            index: i,
            start: e.bookingTimeEnd,
            end: '23:59'
          });
          if (e.boardRoomDisable && e.boardRoomDisable.begintime) {
            this.disableList.push({
              ...e.boardRoomDisable,
              index: i
            });
          }

          // 其他人预约
          e.boardroomApplyList = e.boardroomApplyList || [];
          e.boardroomApplyList.forEach(item => {
            this.bookingList.push({
              ...item,
              index: i,
              parentData: e
            });
          });
        });
      } catch (error) {}
    },
    async getListBoardRoomSubscribe() {
      try {
        let res = await this.ajax.getListBoardRoomSubscribe({
          isVideo: ''
        });
        res = res.object || [];
        let subscribed = res.filter(e => {
          return (
            e.subscribeStatus === 2 && e.disableType != 2 && e.roomIsEnable == 1
          );
        });
        if (subscribed.length > 0) {
          this.queryParam.subscribeStatus = '2';
        } else {
          this.queryParam.subscribeStatus = '';
        }
        this.loadData();
      } catch (error) {}
    },
    // 订阅管理
    subscribeManagement() {
      this.$refs.subscribeManagement.open();
    },
    // 切换订阅
    toggleSubscription(value) {
      this.queryParam.subscribeStatus = value;
      this.loadData();
    },
    handleChange(val) {
      this.loadData();
    },
    toggleDate(date) {
      this.queryParam.time = date;
      this.loadData();
    },
    dateBack(date) {
      this.queryParam.time = moment(date)
        .subtract(1, 'days')
        .format('YYYY-MM-DD');
      this.loadData();
    },
    dateNext(date) {
      this.queryParam.time = moment(date)
        .add(1, 'days')
        .format('YYYY-MM-DD');
      this.loadData();
    },
    getDeviceName(list) {
      return list
        .map(e => {
          return e.name;
        })
        .join(', ');
    },
    // 未开放的样式
    getUnopenStyle(item) {
      let style = {
        position: 'absolute',
        background: '#f4f4f4',
        height: '79px',
        display: 'block'
      };
      let left = 0;
      let width = 0;
      const startHours = parseInt(item.start.split(':')[0]);
      const endHours = parseInt(item.end.split(':')[0]);
      const startM = startHours * 60 + parseInt(item.start.split(':')[1]);
      const endM = endHours * 60 + parseInt(item.end.split(':')[1]);
      const time7 = 7 * 60;

      if (item.start == item.end) {
        style.display = 'none';
      } else if (startHours < 7) {
        left = this.titleWidth + (80 / (60 * 7)) * startM;
        if (endHours < 7) {
          width = (80 / (60 * 7)) * (endM - startM);
        } else if (endHours >= 22) {
          width =
            (80 / (60 * 7)) * (time7 - startM) +
            this.calendarContainerWidth * 15 +
            (80 / (60 * 2 - 1)) * (endM - 22 * 60);
        } else {
          width =
            (80 / (60 * 7)) * (time7 - startM) +
            (this.calendarContainerWidth / 60) * (endM - time7);
        }
      } else if (startHours < 22) {
        left =
          this.titleWidth +
          80 +
          (this.calendarContainerWidth / 60) * (startM - time7);
        if (endHours >= 22) {
          width =
            (this.calendarContainerWidth / 60) * (22 * 60 - startM) +
            (80 / (60 * 2 - 1)) * (endM - 22 * 60);
        } else {
          width = (this.calendarContainerWidth / 60) * (endM - startM);
        }
      } else {
        left =
          this.titleWidth +
          80 +
          this.calendarContainerWidth * 15 +
          (80 / (2 * 60 - 1)) * (startM - 22 * 60);
        width = (80 / (2 * 60 - 1)) * (endM - startM);
      }
      style = Object.assign(style, {
        top: `${item.index * 81 + 1}px`,
        left: `${left}px`,
        width: `${width}px`
      });

      return style;
    },
    getBookingStyle(item, config = {}) {
      let style = {
        position: 'absolute',
        zIndex: 1,
        height: '80px'
      };
      let display = 'block';
      let left = 0;
      let width = 0;
      let start, end;
      if (config.type === 'booking') {
        start = moment(item.startTime).format('HH:mm');
        end = moment(item.meetingEndTime || item.endTime).format('HH:mm');
      } else {
        start = item.begintime;
        end = item.endtime;
      }
      const startHours = parseInt(start.split(':')[0]);
      const endHours = parseInt(end.split(':')[0]);
      const startM = startHours * 60 + parseInt(start.split(':')[1]);
      const endM = endHours * 60 + parseInt(end.split(':')[1]);
      const time7 = 7 * 60;

      if (startHours < 7) {
        left = this.titleWidth + (80 / (60 * 7)) * startM;
        if (endHours < 7) {
          width = (80 / (60 * 7)) * (endM - startM);
        } else if (endHours >= 22) {
          width =
            (80 / (60 * 7)) * (time7 - startM) +
            this.calendarContainerWidth * 15 +
            (80 / (60 * 2 - 1)) * (endM - 22 * 60);
        } else {
          width =
            (80 / (60 * 7)) * (time7 - startM) +
            (this.calendarContainerWidth / 60) * (endM - time7);
        }
      } else if (startHours < 22) {
        left =
          this.titleWidth +
          80 +
          (this.calendarContainerWidth / 60) * (startM - time7);
        if (endHours >= 22) {
          width =
            (this.calendarContainerWidth / 60) * (22 * 60 - startM) +
            (80 / (60 * 2 - 1)) * (endM - 22 * 60);
        } else {
          width = (this.calendarContainerWidth / 60) * (endM - startM);
        }
      } else {
        left =
          this.titleWidth +
          80 +
          this.calendarContainerWidth * 15 +
          (80 / (2 * 60 - 1)) * (startM - 22 * 60);
        width = (80 / (2 * 60 - 1)) * (endM - startM);
      }
      style = Object.assign(style, {
        background:
          config.type === 'booking' && item.applyEmployee
            ? item.applyEmployee.employeeNo === this.usercode
              ? '#D5D9FF'
              : '#FFDEDE'
            : '#e8ecf2',

        top: `${item.index * 81 + 1}px`,
        left: `${left}px`,
        width: `${width}px`,
        display: display
      });
      return style;
    },
    //单元格点击事件
    async handleCellClick(row, column, cell, event) {
      if (column.label === 'header') {
        return;
        // try {
        //   const meetingRoomDetails = await this.ajax.getBoardRoom({
        //     id: row.id
        //   });
        //   this.$refs.addMeetingRoomModal.edit(meetingRoomDetails.object);
        //   return;
        // } catch (error) {}
      }
      const now = moment().format('x');
      let times = [];
      let columnTime;
      if (
        column.label &&
        ['00:00-06:00', '22:00-23:59'].indexOf(column.label) === -1
      ) {
        let columnDate = this.queryParam.time + ' ' + column.label;
        columnTime = parseInt(
          moment(columnDate)
            .add(1, 'hours')
            .format('x')
        );
        let nowHour = moment(parseInt(now)).format('YYYY-MM-DD HH');
        let columnHour = moment(columnDate).format('YYYY-MM-DD HH');
        let timesNum1,
          addNum = 1;
        if (nowHour == columnHour) {
          timesNum1 = commonUtils.roundTime(parseInt(now));
          addNum = 60 - moment(timesNum1).format(`mm`) > 10 ? 1 : 2;
        } else {
          timesNum1 = columnDate;
        }
        times = [
          moment(timesNum1).format('YYYY-MM-DD HH:mm:ss'),
          moment(columnDate)
            .add(addNum, 'hours')
            .format('YYYY-MM-DD HH:mm:ss')
        ];
        if (parseInt(columnTime) < now) {
          this.$message({
            message: '选中时间段必须在当前时间之后！',
            type: 'warning'
          });
          return;
        }
        // 打开详情
        this.$refs.detailsModal.open(row, times);
      }
    },
    getOtherTime(list) {
      if (list.length === 0) return '';
      let num = 0;
      list.forEach(e => {
        if (
          moment(e.startTime)
            .format()
            .valueOf() <
          moment(this.queryParam.time + ' ' + '07:00:00')
            .format()
            .valueOf()
        ) {
          num++;
        }
      });
      return num > 0 ? `${num}个预约` : '';
    },

    getOtherEndTime(list) {
      let num = 0;
      list.forEach(e => {
        if (
          moment(moment(e.endTime).format('YYYY-MM-DD HH:mm:ss')).valueOf() >
          moment(
            moment(this.queryParam.time + ' ' + '22:00:00').format(
              'YYYY-MM-DD HH:mm:ss'
            )
          ).valueOf()
        ) {
          num++;
        }
      });
      return num > 0 ? `${num}个预约` : '';
    },
    // 其他时间段点击事件
    async handleOtherTime(type, row) {
      let data = { boardroomId: row.id };
      const now = moment().format('x');
      let columnTime;
      let times = [];
      if (type === 'left') {
        columnTime = moment(this.queryParam.time + ' ' + '07:00:00').format(
          'x'
        );
        times = [
          moment(this.queryParam.time + ' ' + '00:00:00').format(
            'YYYY-MM-DD HH:mm:ss'
          ),
          moment(this.queryParam.time + ' ' + '07:00:00').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ];
        data.lteStartTime = times[1];
        data.startTime = times[0];
      } else {
        columnTime = moment(this.queryParam.time + ' ' + '22:00:00').format(
          'x'
        );
        times = [
          moment(this.queryParam.time + ' ' + '22:00:00').format(
            'YYYY-MM-DD HH:mm:ss'
          ),
          moment(this.queryParam.time + ' ' + '23:59:59').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ];
        //  data.startTime = times[0];
        data.startTime = times[0];
        data.endTime = times[1];
      }

      // 打开详情
      const res = await this.ajax.getBoardRoomApplyTimeDetailList(data);
      const resList = res.object[0].boardroomApplyList || [];
      if (resList.length === 0) {
        if (parseInt(columnTime) < now) {
          this.$message({
            message: '选中时间段必须在当前时间之后！',
            type: 'warning'
          });
          return;
        }
        this.$refs.detailsModal.open(row, times);
      } else {
        this.$refs.bookingInfoListModal.open(row, resList, type === 'left');
      }
    },
    chilrenOpenAdd(row, times) {
      this.$refs.detailsModal.open(row, times);
    },
    hanleOpenInfo(boardroomApply) {
      this.dataSource = boardroomApply.parentData;

      // this.$nextTick(() => {
      //   this.$refs.bookingInfoModal.open(boardroomApply);
      // });
      if (
        this.$getCookiesInfo('usercode') !=
        boardroomApply.applyEmployee.employeeNo
      )
        return;
      let params = JSON.parse(JSON.stringify(boardroomApply));
      params.startTime = moment(params.startTime).format('YYYY-MM-DD HH:mm:ss');
      params.endTime = moment(params.endTime).format('YYYY-MM-DD HH:mm:ss');
      this.chilrenOpenDetails(params);
    },
    chilrenOpenDetails(row) {
      this.$refs.detailsModal.edit(this.dataSource, row, {
        readOnly: !(row.applyEmployee.employeeNo === this.usercode)
      });
    },
    isHidden(row) {
      const isVisble =
        row.applyEmployee.employeeNo === this.usercode ||
        row.attendEmployeeList.some(e => {
          return e.usercode == this.usercode;
        });
      return row.motifType == '1' || isVisble
        ? row.motif
        : row.motif
            .split('')
            .map(e => {
              return '*';
            })
            .join('');
    },
    contactsName(contacts) {
      if (contacts) {
        return `${contacts.employeeName}${
          contacts.phoneNumber ? '(' + contacts.phoneNumber + ')' : ''
        }`;
      } else {
        return '';
      }
    }
  }
};
</script>
<style lang="scss">
.el-popover.bookingCardPop {
  border-radius: 10px !important;
  padding: 0 0 12px 0 !important;
  .popper__arrow {
    display: none !important;
  }
  .bookingCardTip {
    div {
      padding: 0 12px;
    }
    .title {
      border-radius: 10px 10px 0 0;
      font-weight: 600;
      height: 30px;
      display: flex;
      i {
        font-size: 16px;
        line-height: 30px;
      }
      div {
        width: 230px;
        font-weight: 600 !important;
        font-size: 16px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 30px;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.date-view-table {
  width: 100%;
  height: 100%;
  // 搜索头
  .header-search {
    display: flex;
    line-height: 23px;
    height: 30px;
    text-align: center;
    margin-bottom: 5px;
    .resource_box {
      min-width: 157px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #1684fc;
      border-radius: 4px;
      .resource_box_item {
        flex-grow: 1;
        font-size: 14px;
        font-weight: 400;
        color: #1684fc;
        cursor: pointer;
      }
      .resource_box_line {
        width: 1px;
        height: 16px;
        background-color: #e8ecf2;
      }
      .resource_box_item.check {
        padding: 5px 0;
        background: #5260ff;
        font-size: 14px;
        font-weight: bold;
        color: #ffffff;
      }
    }
    .header-container {
      display: flex;
      align-items: center;
      margin: 5px;
      width: 83%;
      .date-check {
        font-size: 18px;
      }
      .content {
        margin-left: 16px;
        flex-grow: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .appointment-tips {
          color: rgb(245, 154, 37);
          font-size: 14px;
        }
        & > div:first-child {
          .date-button {
            display: inline-block;
            width: 40px;
            height: 20px;
            line-height: 20px;
            background: #999999;
            border-radius: 10px;
            text-align: center;
            margin-right: 8px;
            font-size: 12px;
            font-weight: 400;
            color: #ffffff;
            cursor: pointer;
          }
          .date-button.check {
            background: #5260ff;
          }
          .searchMore {
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 1);
            color: rgba(22, 132, 252, 1);
            font-size: 12px;
            text-align: center;
            border: 1px solid rgba(22, 132, 252, 1);
            padding: 4px 10px;
            cursor: pointer;
          }
        }
        & > div:last-child {
          display: flex;
          align-items: center;

          .desc-item {
            display: flex;
            align-items: center;
            margin-left: 12px;
            & > span {
              display: inline-block;
              width: 18px;
              height: 18px;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
  // 表头
  /deep/.calendar_table_header {
    .el-table__empty-block {
      display: none;
    }
    // 去除表头背景颜色
    .el-table__header-wrapper {
      th {
        background-color: white;
      }
    }
    .cell {
      padding: 0 !important;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
    .el-table--border {
      td:first-child {
        .cell {
          padding: 0 !important;
        }
      }
    }
    .resource_box {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #1684fc;
      border-radius: 4px;
      .resource_box_item {
        flex-grow: 1;
        font-size: 14px;
        font-weight: 400;
        color: #1684fc;
        cursor: pointer;
      }
      .resource_box_line {
        width: 1px;
        height: 16px;
        background-color: #e8ecf2;
      }
      .resource_box_item.check {
        padding: 4px 0;
        background: #5260ff;
        font-size: 14px;
        font-weight: bold;
        color: #ffffff;
      }
    }
    .subscribe_box {
      display: flex;
      justify-content: center;
      align-items: center;
      .subscribe_box_item {
        flex-grow: 1;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
      }
      .subscribe_box_item + .subscribe_box_item {
        color: #5260ff;
        cursor: pointer;
      }
    }
    .header-container {
      display: flex;
      align-items: center;
      margin: 5px;
      .date-check {
        font-size: 18px;
      }
      .content {
        margin-left: 16px;
        flex-grow: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .appointment-tips {
          color: rgb(245, 154, 37);
          font-size: 14px;
        }
        & > div:first-child {
          .date-button {
            display: inline-block;
            width: 40px;
            height: 20px;
            line-height: 20px;
            background: #999999;
            border-radius: 10px;
            text-align: center;
            margin-right: 8px;
            font-size: 12px;
            font-weight: 400;
            color: #ffffff;
            cursor: pointer;
          }
          .date-button.check {
            background: #5260ff;
          }
        }
        & > div:last-child {
          display: flex;
          align-items: center;

          .desc-item {
            display: flex;
            align-items: center;
            margin-left: 12px;
            & > span {
              display: inline-block;
              width: 18px;
              height: 18px;
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
  .calendar_table_body {
    height: calc(100% - 72px);
    margin-top: -1px;
    /deep/ .el-table__row {
      td {
        border-right: 1px solid #e1e1e1 !important;
        border-bottom: 1px solid #e1e1e1 !important;
      }
    }
    .calendar_table_body_relative {
      position: relative;
    }
    /deep/.cell {
      padding: 0 !important;
    }
    /deep/.el-table__row {
      height: 80px;
      max-height: 80px;
      min-height: 80px;
    }
    .cell-add {
      cursor: pointer;
      height: 80px;
      width: 100%;
    }
    .cell-show {
      height: 80px;
      line-height: 80px;
      font-size: 12px;
      color: #1684fc;
      background-color: rgba(239, 239, 239, 0.77);
      span {
        word-break: normal;
        width: auto;
        display: inline-flex;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }
    }
    .time_box {
      // 2021/11/16 08:00:00 - 2121/11/16 12:00:00
      position: absolute;
      left: 0;
      right: 0;
      background: #d5d9ff;
      left: 255px;
      top: 0;
      width: 80.93333333333334px;
      height: 32px;
    }
    .title-card {
      padding: 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2px;
        .label {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-weight: bold;
          color: #5260ff;
          margin-right: 16px;
        }
        .value {
          font-size: 12px;
          font-weight: 400;
          color: #333333;
          white-space: nowrap;
        }
      }
      .address {
        margin-bottom: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        margin-right: 16px;
      }
      .device {
        width: 100%;
        position: relative;
        text-align: left;
        height: 12px;
        span {
          // display: block;
          position: absolute;
          left: -14px;
          right: 0;
          transform: scale(0.83);
          font-size: 10px;
          font-weight: 400;
          color: rgba(51, 51, 51, 0.5);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .booking_card {
      cursor: pointer;
      overflow: hidden;
      padding: 4px;
      .booking_card_card div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
      }
    }
    .booking_card:hover {
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
      z-index: 11 !important;
    }
    .disable_card {
      .box {
        width: 100%;
        height: 100%;
      }
    }
    .other-time {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      cursor: pointer;
      font-size: 10px;
      font-weight: 400;
      color: #d6670e;
    }
  }
  .narrow {
    font-size: 14px;
    transform: scale(0.8);
  }
}
.disable_style {
  & > div:first-child {
    margin-bottom: 4px;
  }
  div {
    font-size: 12px;
  }
}
</style>
