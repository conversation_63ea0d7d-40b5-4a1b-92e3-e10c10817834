前端框架

项目使用主要技术

Vue全家桶,elementui,axios,qiankun,项目私有包

项目框架选型背景

Vue 公司前端技术统一采用vue,方便人员的流动,沟通交流,开发,减少学习成本

Axios 请求后台接口数据,通用

Elementui 公司pc端采用的ui框架统一采用elementui ui框架统一,减少学习成本

Qiankun 有利于整合多个项目,更好的页面体验效果

Elementui框架改造

因为elementui框架一绡组件是直接挂载在body下面的,而项目是嵌在基座面里的,元素作用域的问题,样式无法定制,事件没有提供卸载的方法

项目基座选型(Qiankun )
Iframe
优势 提供浏览器原生隔离方案,样式和js问题都能完美的解决

缺点 应用上下文无法被共享,随之带来的开发体验,产品体验的问题

1,url 不同步。浏览器刷新 iframe url 状态丢失、后退前进按钮无法使用。

2,UI 不同步，DOM 结构不共享。想象一下屏幕右下角 1/4 的 iframe 里 来一个带遮罩层的弹框，同时我们要求这个弹框要浏览器居中显示，		还要浏览器 resize 时自动居中..

3,全局上下文完全隔离，内存变量不共享。iframe 内外系统的通信、数据		同步等需求，主应用的 cookie 要透传到根域名都不同的子应用中实		现免登效果。

4,慢。每次子应用进入都是一次浏览器上下文重建、资源重新加载的过程。

Qiankun  (https://www.yuque.com/kuitos/gky7yw/rhduwc)
优势 能够兼容(vue,react,angular)的项目框架 核心价值“技术栈无关”,应用之间的			切换无感的体验效果

缺点 监听事件要注意卸载,元素状态,css作用域,需要子应用跨域或者在同域下

Webpack.js配置统一修改

应用日志监控
Webpack打包支持map文件,服务端采用node解析map定位错误代码的具体文件里面的具体行数

Api封装(标准的resful)

1种循环遍历生成,优点 请求批量生成,代码量精简,缺点上手难度高,开发习惯要好

2种页面写对应的api,优点,方法清晰,缺点,代码量多

Vuex 状态管理器

管理器统一采用多模块的方式

组件继承
因为有些特定的业务场景,我们的组件库有些特定的属性无法满足需求,如果改动不大,就继承原有组件,在添加属性和方法,满足业务需求

工具公用类

定义常用的(正则列表,分页方法,...)

字体

字体库一般在基座上面加载完成

项目架构图
![img.png](img.png)


改造框架 项目可以在新老项目上兼容

1沟通部门搭建整体框架,布局 demo

2,(菜单标签页,websocket消息)采用自定义h5事件发射器进行事件分发

3研究ifame路由跟主路由的联动关系(突破)

4,组件缓存和qiankun.js缓存问题 路由缓存

5,弹框改造(90%)
