<template>
  <div :style="{ height: `${headerHeight}px` }">
    <div class="home">
      <header class="header flex-start">
        <div
          class="flex-start"
          :style="{
            width: logoUrl ? isCollapseWidth + 44 + 'px' : '44px'
          }"
        >
          <!-- logo -->
          <div class="logo flex-center" v-if="logoUrl">
            <img :src="logoUrl" />
          </div>
          <!-- 门户 -->
          <el-popover
            ref="indexDoorPopover"
            placement="bottom-start"
            popper-class="home-set-popper"
            :visible-arrow="false"
            :close-delay="0"
            v-if="indexDoorMenueList.length && !isExternalPersonnl"
          >
            <div class="index-door" slot="reference">
              <i class="oaicon oa-icon-door"></i>
            </div>

            <div
              v-for="(item, index) of indexDoorMenueList"
              :key="index"
              class="index-door-menue"
              :class="{ 'active-door-menue': activeIndexDoor == index }"
              @click="handleCheckeDoorMenue(item, index)"
            >
              <div class="door-content">
                <p :title="item.title">{{ item.title }}</p>
                <el-tooltip
                  content="设为默认"
                  placement="top-start"
                  popper-class="home-set-tooltip"
                  :enterable="false"
                >
                  <el-switch
                    class="switch"
                    :class="item.defaultId ? 'is-checked' : ''"
                    @click.native="handleSetHomePage($event, item, index)"
                    active-color="$theme-color"
                  ></el-switch>
                </el-tooltip>
              </div>
            </div>
          </el-popover>
          <div v-else class="index-door">
            <i class="oaicon oa-icon-door"></i>
          </div>
        </div>

        <div
          ref="centerTabContainer"
          class="flex-space flex-grow"
          style="overflow:hidden;"
        >
          <!-- 中间tab栏 -->
          <ul class="flex-start flex-grow" style="overflow:hidden;">
            <li
              v-for="(item, index) in showTabList"
              :key="index"
              class="flex-center header-list"
              :class="[index == activeIndex ? 'header-list-acitve' : '']"
              @click="goto(item, index)"
            >
              {{ item.menuname }}
            </li>
            <el-popover
              ref="hiddenTabPopover"
              placement="bottom"
              trigger="hover"
              :visible-arrow="false"
            >
              <li
                class="flex-center header-list"
                :class="{ 'header-list-acitve': -1 == activeIndex }"
                @click="handleMoreTabClick"
                v-show="hiddenTabList.length"
                slot="reference"
              >
                <i
                  class="oaicon oa-icon-18gengduo"
                  :style="{ fontWeight: -1 == activeIndex ? '600' : '100' }"
                ></i>
              </li>

              <div
                v-for="(item, index) of hiddenTabList"
                :key="index"
                class="flex-center header-list hidden-list"
                :class="[
                  index + showTabList.length == activeIndex
                    ? 'header-list-acitve'
                    : ''
                ]"
                @click="goto(item, index + showTabList.length)"
              >
                {{ item.menuname }}
              </div>
            </el-popover>
          </ul>
          <!-- v-if="hiddenTabList.length" -->
          <!-- 右侧信息栏 -->
          <div class="right-user-info flex">
            <!-- 切换机构 -->
            <el-popover
              ref="orgPopover"
              width="100"
              placement="bottom-end"
              trigger="hover"
              v-model="orgPopoverVisible"
              popper-class="select-dept-popover"
              v-if="showInstitutionSelect"
            >
              <div
                class="flex inline-people-count"
                slot="reference"
                style="margin-top: 8px;"
              >
                {{ orgName }}
              </div>
              <ul class="popover-content dept-list">
                <li
                  class="dept-item"
                  v-for="item in institutionList"
                  :key="item.orgCode"
                  :class="{
                    'current-dept': item.orgCode == orgCode
                  }"
                  @click="changeOrg(item)"
                >
                  <div
                    class="select-dept-icon"
                    v-if="userInfo.orgCode == item.orgCode"
                  >
                    *
                  </div>
                  {{ item.orgName }}
                  <span
                    v-if="item.orgCode == orgCode"
                    class="fa fa-check"
                  ></span>
                </li>
              </ul>
            </el-popover>
            <!-- <div class="flex inline-people-count" @click="handleSkin">
              <i
                class="oaicon oa-icon-renyuan-kaoqin"
                style="font-size: 22px; line-height: 0; vertical-align: middle"
              ></i>
              &ensp;换肤&ensp;
            </div> -->
            <div
              class="flex inline-people-count"
              @click="handleShowInlinePeopleCount"
              v-show="systemLoginInfoShow && !isExternalPersonnl"
            >
              <i
                class="oaicon oa-icon-renyuan-kaoqin"
                style="font-size: 22px; line-height: 0; vertical-align: middle"
              ></i>
              &ensp;在线人数&ensp;
              <span class="count">{{ inlinePeopleCount }}</span>
            </div>

            <div
              v-show="showAI"
              class="flex inline-people-count"
              @click="handleShowAI"
            >
              <img
                src="@/assets/img/home/<USER>"
                style="height: 22px; width: 22px;"
                title="DeepSeek助手"
              />
              &ensp;DeepSeek助手&ensp;
            </div>

            <div class="message-content" @click="handleMessageClick">
              <el-badge :value="messageCount" :hidden="!(messageCount > 0)">
                <img
                  src="@/assets/img/home/<USER>"
                  style="height: 20px; width: 20px;"
                  title="消息"
                />
              </el-badge>
            </div>

            <div
              v-show="!isExternalPersonnl && showEmail"
              class="email-content real-email"
              @click="handleEmailIconClick"
            >
              <el-badge :value="emailCount" :hidden="!(emailCount > 0)">
                <i
                  class="fa fa-envelope-o"
                  style="font-size: 20px;"
                  title="我的邮箱"
                ></i>
              </el-badge>
            </div>

            <div
              v-if="hasIMTimelyCommunication"
              class="email-content communication"
              @click="handleOpenIMTimelyCommunication"
            >
              <el-badge :value="imInformCount" :hidden="!(imInformCount > 0)">
                <i
                  class="fa fa-address-book-o"
                  style="font-size: 20px; margin-top: 1px;"
                  title="即时通讯"
                ></i>
              </el-badge>
            </div>
            <!-- <div
              class="work-order-QR-content"
              @click="handleWorkOrderQRIconClick"
            >
              <i
                class="fa fa-qrcode"
                style="font-size: 20px;"
                title="扫码报单"
              ></i>
            </div> -->
            <el-popover
              ref="popover"
              width="260"
              placement="bottom-end"
              :visible-arrow="false"
              trigger="click"
              v-model="popoverVisible"
              popper-class="home-person-info-popper"
            >
              <div
                class="el-dropdown-link name-dept-container flex-center"
                slot="reference"
              >
                <img
                  name="userImg"
                  class="circle"
                  :src="loginUser.empHeadImg || require('@/assets/img/boy.png')"
                />
                <div class="name-dept">
                  <span class="label-span emp-name">
                    {{ loginUser.empName }}
                  </span>
                  <span
                    class="label-span org-name"
                    :title="currentDept.orgName"
                  >
                    {{ currentDept.orgName }}
                  </span>
                </div>
              </div>
              <div class="popover-content">
                <div class="user-info-top-contain flex">
                  <img
                    :src="
                      loginUser.empHeadImg || require('@/assets/img/boy.png')
                    "
                    name="userImg"
                    class="circle"
                  />
                  <div class="user-name-dept">
                    <div>{{ loginUser.empName }}</div>
                    <div style="z-index: 999999;">
                      <el-popover
                        ref="deptPopover"
                        width="100"
                        placement="bottom-end"
                        trigger="hover"
                        v-model="deptPopoverVisible"
                        popper-class="select-dept-popover"
                      >
                        <div
                          class="el-dropdown-link flex-center"
                          slot="reference"
                        >
                          <!-- 当前科室：<span>{{ currentDept.orgName }}</span> -->
                          <span>{{ currentDept.orgName }}</span>
                          <img
                            v-if="
                              JSON.stringify(userInfo) !== '{}' &&
                                userInfo.organizationParttimeList &&
                                userInfo.organizationParttimeList.length > 1
                            "
                            src="@/assets/img/home/<USER>"
                            style="height: 16px; width: 16px;margin-left: 4px;"
                          />
                        </div>
                        <ul class="popover-content dept-list">
                          <li
                            class="dept-item"
                            v-for="item in userInfo.organizationParttimeList"
                            :key="item.orgId"
                            :class="{
                              'current-dept': item.orgId == currentDept.orgId
                            }"
                            @click="changeDept(item)"
                          >
                            <div
                              class="select-dept-icon"
                              v-if="userInfo.orgId == item.orgId"
                            >
                              *
                            </div>
                            {{ item.orgName }}
                            <span
                              v-if="item.orgId == currentDept.orgId"
                              class="fa fa-check"
                            ></span>
                          </li>
                        </ul>
                      </el-popover>
                    </div>
                  </div>
                </div>
                <div class="user-info">
                  <div>
                    <span>工号</span>
                    <span>{{ loginUser.empCode }}</span>
                  </div>
                  <div>
                    <span>职务</span>
                    <span>{{ loginUser.empDutyName }}</span>
                  </div>
                  <div>
                    <span>登录IP</span>
                    <span>{{ loginUser.loginip }}</span>
                  </div>
                  <div>
                    <span>登录时间</span>
                    <span>{{ loginUser.logintime }}</span>
                  </div>
                </div>
                <div class="user-info-footer">
                  <span
                    @click="
                      showEditPassWord = true;
                      editPasswordForm = {};
                    "
                    >修改密码</span
                  >
                  <span @click="getPath">退出登录</span>
                </div>
              </div>
              <div
                class="popover-modal"
                @mouseenter="popoverVisible = false"
                :style="{ top: `${headerHeight - 1}px` }"
              ></div>
            </el-popover>
          </div>
        </div>
      </header>
    </div>

    <el-dialog
      title="修改密码"
      :visible.sync="showEditPassWord"
      width="420px"
      :show-close="showEditPassWordClose"
      :close-on-click-modal="false"
      :close-on-press-escape="showEditPassWordClose"
    >
      <el-form
        ref="editPasswordForm"
        :model="editPasswordForm"
        :rules="editRules"
        label-width="100px"
      >
        <div class="resetPassword" v-if="mustUpdatePassword">
          您的密码已过期，请重置您的密码
        </div>
        <ts-form-item
          label="旧密码"
          prop="oldpassword"
          style="margin-top: 10px;"
        >
          <el-input
            v-model="editPasswordForm.oldpassword"
            type="password"
            autocomplete="off"
            show-password
          >
          </el-input>
        </ts-form-item>
        <ts-form-item label="新密码" prop="newpassword">
          <el-input
            v-model="editPasswordForm.newpassword"
            type="password"
            autocomplete="off"
            show-password
          ></el-input>
        </ts-form-item>
        <ts-form-item label="重复密码" prop="repeatPassword">
          <el-input
            v-model="editPasswordForm.repeatPassword"
            type="password"
            autocomplete="off"
            show-password
          ></el-input>
        </ts-form-item>
      </el-form>

      <div slot="footer">
        <el-button
          class="password-save-btn"
          @click="handleSaveEdit"
          :loading="isSaving"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

    <system-use-info ref="SystemUseInfo" />
    <!-- <dialog-deepseek ref="dialogDeepseek" /> -->
    <!-- <work-order-QR-info ref="workOrderQRInfo" /> -->
  </div>
</template>

<script>
import SystemUseInfo from './components/system-use-info.vue';
// import dialogDeepseek from './components/dialog-deepseek.vue';
// import WorkOrderQRInfo from './components/work-order-QR-info.vue';
import { $api } from '@/api/ajax.js';
import common from '@/unit/common.js';
import { deepClone } from '@/utils/deepClone.js';
import { Encrypt } from '@/utils/encrypt.js';

import IMCommunication from './js/IMCommunication';
import moment from 'moment';
import JSEncrypt from 'jsencrypt';
export default {
  name: 'index',
  mixins: [IMCommunication],
  components: {
    SystemUseInfo
    // dialogDeepseek
    // WorkOrderQRInfo
  },
  data() {
    return {
      showTabList: [], //显示的tab
      hiddenTabList: [], //隐藏的tab
      tabComputTimer: null, //页面大小改变计时器

      routeracitve: '', //显示哪个为激活应用
      loginUser: {}, //当前登录人员信息

      inlinePeopleCount: 0, //当前登录人数
      emailCount: 0, //当前未读 email 数量

      deptPopoverVisible: false,
      systemLoginInfoShow: true,

      popoverVisible: false,

      showEditPassWord: false, //是否展示修改密码
      showEditPassWordClose: true, //是否显示关闭修改密码的icon
      editPasswordForm: {}, //编辑密码对象
      isSaving: false, //是否正在保存
      //编辑密码规则
      editRules: {
        oldpassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
          // { min: 6, message: '密码不能小于6位数', trigger: 'blur' }
        ],
        newpassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          {
            message: '',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value == this.editPasswordForm.oldpassword) {
                callback('新密码不能与旧密码一致');
              } else if (
                this.editPasswordForm.repeatPassword &&
                value != this.editPasswordForm.repeatPassword
              ) {
                callback('两次密码不一致');
              } else {
                if (
                  this.editPasswordForm.repeatPassword &&
                  this.editPasswordForm.repeatPassword.length >= 6
                ) {
                  this.$refs.editPasswordForm.clearValidate('repeatPassword');
                }
                callback();
              }
            }
          },
          {
            trigger: ['blur', 'change'],
            validator: this.handleValidator
          }
        ],
        repeatPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          {
            message: '两次密码不一致',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (
                this.editPasswordForm.newpassword &&
                value &&
                value != this.editPasswordForm.newpassword
              ) {
                callback('两次密码不一致');
              } else {
                if (
                  this.editPasswordForm.newpassword &&
                  this.editPasswordForm.newpassword.length >= 6
                ) {
                  this.$refs.editPasswordForm.clearValidate('newpassword');
                }
                callback();
              }
            }
          },
          {
            trigger: ['blur', 'change'],
            validator: this.handleValidator
          }
        ]
      },

      activeIndexDoor: 0, //选中的首页门户
      indexDoorMenueList: [], //indexdoor 的下拉选项
      institutionList: [],
      orgPopoverVisible: false,
      orgName: '',
      mustUpdatePassword: false,
      orgCode: ''
    };
  },
  created() {
    if (localStorage.getItem('values')) {
      this.orgName = JSON.parse(localStorage.getItem('values')).orgName;
      this.orgCode = JSON.parse(localStorage.getItem('values')).orgCode;
    }
    this.getOrgList();
    this.$root.$on('inlinePeopleCountChange', this.handleInlinePeopleChange);
    this.$root.$on('headRightMessageChange', e => {
      // 老版本 邮箱未读书
      if (e.type == 'emailNum') {
        this.emailCount = e.data;
      } else {
        // this.messageCount = e.data;
      }

      // 新版本 邮箱未读书
      if (e.customEmailCount) {
        this.emailCount = e.noread;
      }
    });
    if (
      this.$cookies.get('isLowerPassWord') == '1' ||
      this.$cookies.get('isModifyDefaultPwd') == '1'
    ) {
      this.$nextTick(() => {
        this.showEditPassWordClose = false;
        this.editPasswordForm = {};
        this.showEditPassWord = true;
      });
    }
    this.getLoginUserInfo();
    this.getIndexDoorMenueList();
    this.getMessageCount();
  },
  mounted() {
    setTimeout(() => {
      let popoverContent = this.$refs.popover.$refs.popper;
      popoverContent.style.padding = 0;
      popoverContent.style.border = 'none';
      popoverContent.style.borderRadius = '4px';

      // let indexDoorPopover = this.$refs.indexDoorPopover.$refs.popper;
      // indexDoorPopover.style.marginTop = '8px';
      // indexDoorPopover.style.padding = '4px 0px';

      let hiddenTabPopover = this.$refs.hiddenTabPopover.$refs.popper;
      hiddenTabPopover.style.marginTop = '8px';
      hiddenTabPopover.style.padding = '0';
      hiddenTabPopover.style.minWidth = '96px';
    });
    this.checkUpdatePassword();
    this.recomputedTopTab(this.$store.state.common.menuList);
    window.addEventListener('resize', this.windowResize);
  },
  props: {
    headerHeight: {
      //头部高度
      type: [String, Number],
      default: 44
    },
    isCollapseWidth: {
      //左边logo宽度
      type: [String, Number],
      default: 160
    }
  },
  methods: {
    checkUpdatePassword() {
      if (this.$store.state.common.globalSetting.passwordExpire != 1) return;
      let checkData = this.$store.state.common.userInfo.passwordExpireDate.split(
        ' '
      )[0];
      let days = moment(moment().format('YYYY-MM-DD')).diff(
        moment(checkData),
        'day'
      );
      if (days >= 0) {
        this.mustUpdatePassword = true;
        this.showEditPassWord = true;
        this.editPasswordForm = {};
        this.showEditPassWordClose = false;
        return;
      }
    },
    windowResize() {
      this.tabComputTimer && clearTimeout(this.tabComputTimer);
      this.tabComputTimer = setTimeout(() => {
        this.recomputedTopTab(this.$store.state.common.menuList);
      }, 500);
    },

    //计算展示的菜单
    recomputedTopTab(tabs) {
      let showTab = [],
        hiddenTab = [];
      if (!this.$refs.centerTabContainer) {
        showTab = tabs;
      } else {
        let tabWidth = 0,
          container = this.$refs.centerTabContainer,
          containerWidth = container.clientWidth,
          rightContentWidth = container.querySelector('.right-user-info')
            .clientWidth,
          centerWidth = containerWidth - rightContentWidth - 5;
        let spliceIndex = tabs.findIndex((item, index) => {
          tabWidth +=
            item.menuname.length > 3 ? item.menuname.length * 14 + 44 : 96;
          if (index < tabs.length - 1) {
            return tabWidth + 96 > centerWidth;
          } else {
            return tabs.length;
          }
        });

        if (spliceIndex < tabs.length - 1) {
          showTab = tabs.slice(0, spliceIndex);
          hiddenTab = tabs.slice(spliceIndex);
        } else {
          showTab = tabs;
          hiddenTab = [];
        }
      }
      this.showTabList = showTab;
      // this.showTabList = showTab;
      this.hiddenTabList = hiddenTab;
    },
    /**@desc 页面调转详情
     * @param {Object} item
     * @param {Number} index
     * @param {Boolean} isTabClick
     * **/
    goto(item, index, isTabClick = true) {
      // 老版院长查询跳转
      if (item.alink === '/TrasenDSS/login.htm') {
        let account = this.userInfo.employeeNo;
        let url =
          location.origin + item.alink + `?account=${account}&resource=OA`;
        window.open(url, '_target');
        return false;
      }
      // 党建系统跳转
      if (item.alink === '/djkhbg_hnnkyy') {
        let params = {
          uid: this.routeChangeEncryptedData(this.userInfo.employeeNo)
        };
        let conditionList = Object.keys(params).map(key => {
          let val = params[key];
          return `${key}=${val}`;
        });

        let pathDic = {
          'https://oa.hnnkyy.com:9088': 'https://djkhbg.hnnkyy.com',
          'http://192.168.188.171:9088': 'http://192.168.7.222:1023'
        };

        if (!pathDic[location.origin]) return false;
        let url = `${pathDic[location.origin]}/user/login?` + conditionList;
        window.open(url, '_target');
        return false;
      }
      // 新版院长查询跳转
      let newSearchIcon = 'oa_change_path';
      let ddkhNewSearchIcon = 'oa_change_path_ddkh';
      if (item.alink.includes(ddkhNewSearchIcon)) {
        let url = item.alink.replace(ddkhNewSearchIcon, location.origin);
        window.open(url, '_target');
        return false;
      } else if (item.alink.includes(newSearchIcon)) {
        let url = item.alink.replace(newSearchIcon, location.hostname);
        window.open('http://' + url, '_target');
        return false;
      } else if (
        item.alink.startsWith('http:') ||
        item.alink.startsWith('https:')
      ) {
        window.open(
          `${item.alink}?userCode=${this.$store.state.common.userInfo.employeeNo}`,
          '_blank'
        );
        return false;
      }
      this.$root.$emit('updateFirstMenu', item);
      let menus = this.$store.state.common.menuList[index];
      this.$set(this.$store.state.common, 'childMenuList', []);
      if (menus.menus.length == 0) {
        this.$router.push(item.alink);
        this.$set(this.$store.state.common, 'sideBarWidth', 0);
      } else {
        if (isTabClick) {
          let computedMenus = function(menu) {
              let menus = menu.menus || [],
                alink = '';
              if (menu.alink == '#' && menus.length) {
                menus.findIndex(item => {
                  alink = computedMenus(item);
                  if (alink) {
                    return true;
                  }
                  return false;
                });
              } else {
                menu.alink != '#' && (alink = menu.alink);
              }
              return alink;
            },
            alink = computedMenus(menus);
          if (alink.indexOf('TSDesign') < 0) {
            alink && this.$router.push(alink);
          }
        }
        if (this.$store.state.common.isCollapse) {
          this.$set(this.$store.state.common, 'sideBarWidth', 50);
        } else {
          if (this.$store.state.common.sideBarWidth == 0) {
            this.$set(this.$store.state.common, 'sideBarWidth', 160);
          }
        }
      }
      setTimeout(() => {
        this.$set(this.$store.state.common, 'childMenuList', menus.menus);
        this.$set(this.$store.state.common, 'activeMenuListParent', index);
      });
    },

    // 党建系统跳转 加密方法
    routeChangeEncryptedData(data) {
      const PUBLIC_KEY =
        'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmnt1bD4bfbo/IGRqDguuv2GzEalSBL7QIPKbeCFR3OlxlCzFch3pUhpYzbcQjcc3aTsEC71EWitUd1jml0gMosjxHpKwOebQPZfU1GLgKQBEScnXyrr7pHfY8LWowSIomWUg+e0PMfjN8QaI5RPnVPd1Q9LohbRtAMsxmUVFaCwIDAQAB';
      let encryptor = new JSEncrypt({ default_key_size: 1024 });
      encryptor.setPublicKey(PUBLIC_KEY);
      return encodeURIComponent(encryptor.encrypt(data));
    },

    getPath() {
      this.$root.$emit('loginOut');
    },
    //处理在线人数改变
    handleInlinePeopleChange(data) {
      this.inlinePeopleCount = data;
    },
    handleShowInlinePeopleCount() {
      this.$refs.SystemUseInfo.open();
    },
    isIP(hostname) {
      let hostnames = hostname.replace('http://').replace('https://');
      let doname = /^([\w-]+\.)+((com)|(net)|(org)|(gov\.cn)|(info)|(cc)|(com\.cn)|(net\.cn)|(org\.cn)|(name)|(biz)|(tv)|(cn)|(mobi)|(name)|(sh)|(ac)|   (io)|(tw)|(com\.tw)|(hk)|(com\.hk)|(ws)|(travel)|(us)|(tm)|(la)|(me\.uk)|(org\.uk)|(ltd\.uk)|(plc\.uk)|(in)|(eu)|(it)|(jp))$/;
      let flag_domain = doname.test(hostnames);
      if (!flag_domain) {
        return false;
      } else {
        return true;
      }
    },
    handleShowAI() {
      let aiLink = this.$store.state.common.globalSetting.aiExternalLink;
      if (!aiLink) {
        this.$emit('intelligentAssistantShow');
      } else {
        let ipList = this.$store.state.common.globalSetting.aiExternalLink.split(
          ';'
        );
        let url = window.location.hostname;
        let path = '';
        if (this.isIP(url)) {
          ipList.forEach(i => {
            if (this.isIP(i)) {
              path = i;
            }
          });
        } else {
          ipList.forEach(i => {
            if (!this.isIP(i)) {
              path = i;
            }
          });
        }
        path && window.open(path);
      }
      // if (this.$store.state.common.globalSetting.orgCode == 'yysdsrmyy') {
      //   window.open('http://*************:8080');
      // } else {
      //   this.$refs.dialogDeepseek.open(this.loginUser);
      // }
    },
    handleSkin() {
      let theme = window.document.documentElement.getAttribute('theme');
      if (theme == 'green') {
        window.document.documentElement.setAttribute('theme', 'basic');
      } else {
        window.document.documentElement.setAttribute('theme', 'green');
      }
    },
    handleEmailIconClick() {
      // this.$router.push('/email/emailManagement');
      this.$router.push('/ts-web-oa/email/emailManagement');
    },
    // handleWorkOrderQRIconClick() {
    //   this.$refs.workOrderQRInfo.open();
    // },
    async getLoginUserInfo() {
      try {
        let res = await $api({
          url: '/ts-oa/employee/selectByPrimaryKey',
          method: 'post'
        });
        if (res.success == false) {
          this.$message.error(res.message || '数据请求错误');
          return;
        }

        this.loginUser = res.object || {};
        let userInfoRes = await $api({
          url: '/ts-system/user/info?' + Math.random(10000),
          method: 'get'
        });
        if (userInfoRes.success == false) {
          this.$message.error(userInfoRes.message || '数据请求错误');
          return;
        }
        this.loginUser.loginip = userInfoRes.object.loginip;
        this.loginUser.logintime = userInfoRes.object.logintime;
        this.loginUser.logintime = userInfoRes.object.logintime;
        this.$set(
          this.$store.state.common,
          'sysRoleCode',
          userInfoRes.object.sysRoleCode
        );
        global.$userInfo = userInfoRes.object;
        // 二幅需要添加角色 查看系统登录运行信息
        if (this.$store.state.common.globalSetting.orgCode === 'cssdeshfly') {
          this.systemLoginInfoShow = userInfoRes.object.sysRoleCode.includes(
            'XTYXSJGLY'
          );
        }
      } catch (e) {
        console.log(e);
        return;
      }
    },
    async getIndexDoorMenueList() {
      let res = await $api({
        url: '/ts-basics-bottom/portalTheme/selectPortalThemeList',
        method: 'post',
        data: JSON.stringify({
          isFindChild: 'N',
          authorization: 'Y',
          isDefaultFlag: 'Y'
        }),
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      });
      if (res.success == false) {
        this.$message.error(res.message || '数据加载错误，请刷新重试');
        console.log('error in refresh indexDoor');
        return;
      }
      let indexDoorId = localStorage.getItem('indexDoor');
      if (indexDoorId) {
        this.activeIndexDoor =
          res.object.findIndex(item => item.id == indexDoorId) || 0;
      } else {
        this.activeIndexDoor =
          res.object.findIndex(item => item.defaultId) || 0;
        localStorage.setItem('indexDoor', res.object[this.activeIndexDoor].id);
      }

      this.indexDoorMenueList = res.object || [];
    },
    handleCheckeDoorMenue(item, index, isSetDefaultDoor = false) {
      this.$refs.indexDoorPopover.doClose();
      if (this.$route.fullPath != '/index') {
        this.$router.push('/index');
      }
      // this.$root.$emit('indexDoorChange', item); //此为老版本门户
      localStorage.setItem('indexDoor', item.id);
      this.$root.$emit('indexNewDoorChange', isSetDefaultDoor);
      this.activeIndexDoor = index;
    },
    //保存密码修改
    handleSaveEdit() {
      this.$refs.editPasswordForm.validate(res => {
        if (!res) {
          return;
        }
        this.isSaving = true;
        let data = deepClone(this.editPasswordForm);
        data.usercode = (
          this.$cookies.get('sso_domain_user_code') ||
          this.$cookies.get('sso_user_code')
        ).toLowerCase();
        data.newpassword = Encrypt(data.newpassword.trim()).toString();
        data.oldpassword = Encrypt(data.oldpassword.trim()).toString();
        data.repeatPassword = Encrypt(data.repeatPassword.trim()).toString();
        $api({
          url: '/ts-basics-bottom/user/chgpwd',
          method: 'post',
          data: JSON.stringify(data),
          headers: {
            'Content-Type': 'application/json'
          }
        }).then(res => {
          this.isSaving = false;
          if (typeof res == 'string') {
            res = JSON.parse(res);
          }
          if (res.success == false) {
            this.$message.error(res.message || '修改密码失败');
            return;
          }
          this.$message.success(res.message);
          this.showEditPassWord = false;
          this.showEditPassWordClose = true;

          this.$cookies.set('isLowerPassWord', '0');
          this.$cookies.set('isModifyDefaultPwd', '0');
          let remindPassword = this.$store.state.common.globalSetting
            .remindPassword;
          if (remindPassword == 1) {
            this.$cookies.set('rememberPassword', '');
          }

          this.$refs.editPasswordForm.resetFields();
          setTimeout(() => {
            this.$root.$emit('loginOut');
          }, 500);
        });
      });
    },
    //处理消息点击
    handleMessageClick() {
      this.$emit('OpenNewMessage');
      //主动添加页签
      // this.$root.$emit('addTabNav', {
      //   alink: '/message',
      //   fullPath: '/message',
      //   id: '/message',
      //   index: '0',
      //   menuname: '消息',
      //   open: 'true',
      //   parentIndex: undefined,
      //   parentIndex_: undefined,
      //   pid: '/message'
      // });
      // this.$router.push('/message');
    },
    //获取未读消息数
    async getMessageCount() {
      this.$store.dispatch('common/getMessageCount');
    },
    //处理显更多菜单的点击事件
    handleMoreTabClick() {
      this.$set(this.$store.state.common, 'activeMenuListParent', -1);
      this.$set(this.$store.state.common, 'sideBarWidth', 0);
      this.$set(this.$store.state.common, 'childMenuList', []);
      this.$root.$emit('menuMessage', { type: 'hiddenLeft' });
    },
    //处理设置默认首页
    handleSetHomePage(e, item, index) {
      e.cancelBubbul = true;
      e.stopPropagation();

      this.$api({
        url: '/ts-basics-bottom/portalTheme/setDefaultTheme?themeId=' + item.id,
        method: 'post'
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '设置失败');
          return;
        }
        let switchNodeList = this.$refs.indexDoorPopover.$refs.popper.querySelectorAll(
          '.switch'
        );
        for (let i = 0; i < switchNodeList.length; i++) {
          let item = switchNodeList[i];
          item.classList.remove('is-checked');
        }
        e.target.parentNode.classList.add('is-checked');

        this.$message.success(res.message || '设置成功');

        this.handleCheckeDoorMenue(item, index, true);
      });
    },
    /**@desc 处理 密码校验 */
    handleValidator(rule, value, callback) {
      let globalSetting = this.$store.state.common.globalSetting,
        level = common.pwdCheckStrong(value, globalSetting.passwordLength),
        rules = [],
        check = true;
      if (
        String(value).length <
        (this.$store.state.common.globalSetting.passwordLength || 6)
      ) {
        check = false;
      }

      if (globalSetting?.passwordRule) {
        rules = globalSetting.passwordRule.split(',');
      }

      for (var i = 0; i < rules.length; i++) {
        if (!level.checkType[rules[i]]) {
          check = false;
          break;
        }
      }

      if (level.level < rules.length || level.level == -1) {
        check = false;
      }

      if (!check) {
        let tost = [];
        rules.forEach(item => {
          tost.push(
            {
              1: '大写字母',
              2: '小写字母',
              3: '数字',
              4: '特殊字符'
            }[Number(item)]
          );
        });
        callback(
          `至少${this.$store.state.common.globalSetting.passwordLength ||
            6}位数` + (tost.length ? '，且必须包含' + tost.join('、') : '')
        );
        return;
      }
      callback();
    },
    //切换科室
    async changeDept(dept) {
      let userData = deepClone(this.$store.state.common.userInfo);
      for (let i = 0; i < userData.organizationParttimeList.length; i++) {
        if (dept.orgId == userData.organizationParttimeList[i].orgId) {
          userData.organizationParttimeList[i].isDefault = '1';
        } else {
          userData.organizationParttimeList[i].isDefault = '0';
        }
      }
      this.$store.commit('common/setData', {
        label: 'userInfo',
        value: userData
      });
      const loading = await this.$loading();
      await this.$api({
        url:
          '/ts-basics-bottom/api/organizationParttime/chooseOrganizationParttime',
        method: 'post',
        data: JSON.stringify({
          orgId: dept.orgId,
          orgName: dept.orgName,
          id: dept.id,
          employeeId: this.userInfo.employeeId,
          employeeNo: this.userInfo.employeeNo
        }),
        headers: {
          'Content-Type': 'application/json;charset=UTF-8'
        }
      }).then(async res => {
        if (res.success == false) {
          this.$message.error(res.message || '切换失败');
          return;
        }
        this.$root.$emit('sendMessageToOldFrame', {
          detail: {
            type: 'closeAllPage',
            data: {}
          }
        });
        this.$root.$emit('sendMessageToOldFrame', {
          detail: {
            type: 'changeCurrentDept',
            data: {
              currentDept: dept
            }
          }
        });
        loading.close();
      });
    },

    async getOrgList() {
      let usercode = this.$cookies.get('rememberUsercode');
      let res = await this.ajax.getMultipleOrg(usercode);
      if (res.success == false) {
        this.$message.error(res.message || '获取机构失败!');
        return;
      }
      if (res.object && res.object.length) {
        this.institutionList = res.object;
      }
    },
    changeOrg(item) {
      let loginData = {
        fromType: 'OA',
        orgCode: item.orgCode,
        orgName: item.orgName,
        password: localStorage.getItem('password'),
        remarkid: location.origin + '#/index?encrypt=0',
        usercode: this.userInfo.employeeNo,
        sessionid: 'myself'
      };
      this.handleAccountLogin(loginData, item);
    },
    //账号登录
    handleAccountLogin(loginData, item) {
      this.$api({
        url: '/ts-basics-bottom/user/login',
        method: 'post',
        data: JSON.stringify(loginData),
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(async res => {
        if (res.success == false) {
          this.$message.error(res.message);
          return;
        }
        this.setLoginCookie(res.object);
        this.$store.state.common.token = res.object.token;
        localStorage.setItem(
          'values',
          JSON.stringify({
            orgCode: item.orgCode,
            orgName: item.orgName
          })
        );
        this.recordLoginLog({
          userCode: res.object.usercode,
          userName: res.object.username,
          source: 'PC'
        });
        await this.getHomeDatas(res.object.token);
      });
    },
    /**@desc 日志接口，检查是否为弱密码 */
    recordLoginLog(sysAccessLog) {
      this.$api({
        url: '/ts-information/api/sysAccessLog/save',
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(sysAccessLog),
        method: 'POST'
      });
    },
    /**@desc 获取首页信息 */
    async getHomeDatas(token) {
      await this.$api({
        url: '/ts-basics-bottom/rolePage/getRolePage',
        method: 'POST'
      }).then(homeRes => {
        let homeUrl =
          homeRes.success == false
            ? '/index'
            : (homeRes.object && homeRes.object.pageUrl) || '/index';

        sessionStorage.setItem('homeUrl', homeUrl);
        //由于调了这个接口后THPMSCookie被修改，重新设置一下
        this.$cookies.set('THPMSCookie', token);
        this.$router.push(homeUrl);
        this.$root.$emit('login');
        window.location.reload();
      });
    },
    setLoginCookie(loginData) {
      this.$cookies.set('THPMSCookie', loginData.token);
      this.$cookies.set('token', loginData.token);
      this.$cookies.set('noticeType', '1');
      this.$cookies.set('sso_user_code', loginData.id);
      this.$cookies.set('sso_domain_user_code', loginData.usercode);
      this.$cookies.set('sso_sysRoleCode', loginData.sysRoleCode);
    }
  },
  computed: {
    showInstitutionSelect() {
      //机构数大于1，且系统配置为多机构平台时显示切换
      const isMultiInstitution = this.institutionList?.length ?? 0 > 1,
        isMultiPlatform =
          this.$store.state.common.globalSetting.platformLoginType == 2;
      return isMultiInstitution && isMultiPlatform;
    },
    showAI() {
      const systemCustomCode = this.$store.state.common.systemCustomCode;
      return systemCustomCode['DEEPSEEK'] ? false : true;
    },
    isExternalPersonnl() {
      let value = this.$cookies.get('sso_sysRoleCode');
      if (!value) return false;
      return value.includes('EXTERNAL_PERSONNEL');
    },
    userInfo() {
      return this.$store.state.common.userInfo;
    },
    currentDept() {
      let list =
        this.$store.state.common.userInfo.organizationParttimeList || [];
      let deptArr = list.filter(item => item.isDefault == 1);
      return deptArr.length
        ? {
            orgId: deptArr[0].orgId,
            orgName: deptArr[0].orgName,
            id: deptArr[0].id
          }
        : {
            orgId: this.$store.state.common.userInfo.orgId,
            orgName: this.$store.state.common.userInfo.orgName
          };
    },
    messageCount() {
      return this.$store.state.common.messageCount;
    },
    microApps() {
      return this.$store.state.common.menuList;
    },
    /**@desc 设置当前路由的激活样式**/
    activeIndex() {
      return this.$store.state.common.activeMenuListParent;
    },
    menuList() {
      return this.$store.state.common.menuList;
    },
    globalSetting() {
      return this.$store.state.common.globalSetting;
    },
    showEmail() {
      return this.globalSetting.orgCode !== '浏阳市妇幼保健院';
    },
    logoUrl() {
      return this.globalSetting.topLogo
        ? `/ts-basics-bottom/fileAttachment/downloadFile/${this.globalSetting.topLogo}`
        : '';
    }
  },
  watch: {
    //监听头部菜单的改变，计算展示的头部标签
    menuList: {
      handler(newVal) {
        this.recomputedTopTab(newVal);
      },
      immediate: true,
      deep: true
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.windowResize);
  }
};
</script>

<style scoped lang="scss">
.home {
  height: 100%;
  width: 100%;
  background: $theme-color;
}

.name-dept-container {
  display: flex;
  .name-dept {
    display: flex;
    flex-direction: column;
    .label-span {
      margin-left: 5px;
      color: #fff;
      &.org-name {
        max-width: 100px;
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}

.el-dropdown-link {
  cursor: pointer;
}
.header {
  height: 100%;

  .logo {
    flex: 1;
    height: 100%;
    padding: 5px 16px 5px 12px;
    box-sizing: border-box;
    img {
      width: 100%;
      height: 100%;
    }
    > span {
      flex-shrink: 0;
    }
  }
  .title {
    margin-left: 20px;
    font-size: 17px;
    font-weight: 400;
  }
}

.header-list {
  padding: 0 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  // color: #caced3;
  color: #fff;
  height: 44px;
  min-width: 56px;
  white-space: nowrap;
}
.header-list-acitve {
  font-size: 14px;
  font-weight: 600;
  background: #fff;
  color: $theme-color;
}
.hidden-list {
  white-space: nowrap;
  color: #333;
  padding: 0 20px;
  &.header-list-acitve {
    background: $theme-color;
    color: #fff;
  }
}
.right-user-info {
  // padding: 0 20px;
  padding: 0 8px;
  // margin-right: 5px;
  height: 100%;
  .username {
    margin-left: 20px;
  }
}
.flex {
  display: flex;
}
.inline-people-count {
  align-items: center;
  margin: 0 5px;
  color: #fff;
  cursor: pointer;
  white-space: nowrap;
  .count {
    padding: 0 10px 0 9px;
    background-color: #d3d7ff;
    border-radius: 10px;
    color: $theme-color;
    margin-left: 3px;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      border-right: 5px solid #d3d7ff;
      border-top: 5px solid transparent;
      border-bottom: 5px solid transparent;
      top: 50%;
      margin-top: -5px;
      left: -3px;
    }
  }
}
.email-content {
  display: flex;
  align-items: center;
  color: #d8d8d8;
  padding: 0 20px;
  cursor: pointer;
  &.real-email {
    padding-left: 8px;
    padding-right: 26px;
    margin-top: 3px;
  }
  &.communication {
    padding-left: 2px;
    padding-right: 28px;
    margin-top: 3px;
  }
  &:hover {
    color: #fff;
  }
}
.popover-content {
  z-index: 2058;
  position: relative;
  background: #fff;
}
.popover-content.dept-list .dept-item {
  padding: 4px 0;
  border-bottom: 1px solid #efefef;
  cursor: pointer;
  position: relative;
  &:first-child {
    padding-top: 0;
  }
  &:last-child {
    padding-bottom: 0;
    border: 0;
  }
}

.select-dept-icon {
  position: absolute;
  top: 1px;
  left: -10px;
  font-size: 18px;
}

.popover-content.dept-list .dept-item.current-dept {
  color: $theme-color;
  position: relative;
  span {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-4 - 50%);
  }
}
.popover-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #333;
  z-index: 999;
}
[name='userImg'] {
  border-radius: 50%;
  overflow: hidden;
  height: 32px;
  width: 32px;
}
.user-info-top-contain {
  height: 50px;
  width: 220px;
  padding: 20px;
  background: #eeefff;
  position: relative;
  .circle {
    border-radius: 50%;
    overflow: hidden;
    height: 50px;
    width: 50px;
  }
  &::after {
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-image: url(./../../assets/img/home/<USER>
    background-repeat: no-repeat;
    background-position: left bottom;
    background-size: auto 80%;
    transform: rotateY(180deg);
  }
  .user-name-dept {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    margin-left: 10px;
    > div:first-child {
      color: #333;
    }
    > div:last-child {
      color: #33333380;
    }
  }
}
.user-info {
  padding: 10px 20px;
  > div {
    line-height: 44px;
    white-space: nowrap;
    > span:first-child {
      display: inline-block;
      width: 60px;
      color: #33333380;
    }
    > span:last-child {
      color: #333;
      // white-space: nowrap;
    }
  }
}
.user-info-footer {
  line-height: 44px;
  width: 220px;
  padding: 10px 20px;
  border-top: 1px solid #eee;
  text-align: center;
  span {
    display: inline-block;
    margin: 0 8px;
    width: 70px;
    padding-left: 20px;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
    color: #333;
    &:hover {
      color: $theme-color;
    }
  }
  > span:first-child {
    background-image: url(./../../assets/img/home/<USER>
    background-repeat: no-repeat;
    background-size: 16px 16px;
    background-position: 5px center;
    &:hover {
      background-image: url(./../../assets/img/home/<USER>
    }
  }
  > span:last-child {
    background-image: url(./../../assets/img/home/<USER>
    background-repeat: no-repeat;
    background-size: 16px 16px;
    background-position: 5px center;
    &:hover {
      background-image: url(./../../assets/img/home/<USER>
    }
  }
}

.password-save-btn {
  background-color: $theme-color;
  border-color: $theme-color;
  color: #fff;
  line-height: 28px;
  min-width: 60px;
  padding: 0 8px;
  height: 30px;
  font-size: 14px;
  border: 1px $theme-color solid;
  border-radius: 2px;

  &:hover,
  &:active,
  &:focus {
    background-color: $theme-color;
    border-color: $theme-color;
    color: #fff;
    opacity: 0.8;
  }
}
.index-door {
  background: $theme-color-highlight;
  width: 44px;
  height: 44px;
  text-align: center;
  i {
    font-size: 20px;
    line-height: 44px;
    color: #fff;
  }
}
.index-door-menue {
  white-space: nowrap;
  word-break: unset;
  line-height: 28px;
  width: 150px;
  text-align: left;
  padding: 0 8px;
  cursor: pointer;
  &:hover {
    background-color: rgba($theme-color, 0.1);
  }
  .door-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 28px;
    p {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .check-door-icon {
    float: right;
    height: 16px;
    width: 16px;
    border: 1px solid #ccc;
    border-radius: 50%;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      width: 12px;
      height: 12px;
      top: 2px;
      left: 2px;
      background-color: #ccc;
      border-radius: 50%;
    }
  }
  &.active-door-menue {
    background-color: var(--theme-color-8);
    .check-door-icon {
      border-color: $theme-color;
      &::after {
        background-color: $theme-color;
      }
    }
  }
}
.message-content,
.work-order-QR-content {
  cursor: pointer;
  margin-left: 8px;
  margin-right: 20px;
  height: 100%;
  padding-top: 10px;
  color: #d8d8d8;
}
/deep/ .ts-form-item {
  .el-form-item__error {
    z-index: 10;
    padding: 2px 8px;
    top: calc(100% + 4px);
    background-color: #fbe3e3;
    border-radius: 4px;
    height: 24px;
    display: flex;
    align-items: center;
  }
}
.resetPassword {
  color: #f56c6c;
  text-align: center;
}
</style>

<style lang="scss">
.home-set-tooltip.is-light.el-tooltip__popper {
  border-radius: 0;
  padding: 8px;
  &,
  .popper__arrow {
    // border-color: transparent;
    border: none;
    box-shadow: 2px 4px 3px 0 rgba($color: black, $alpha: 0.2);
  }
}
.home-set-popper.el-popover[x-placement^='bottom'] {
  padding: 4px 0;
  margin-top: 8px;
}
.home-set-popper .switch.is-checked .el-switch__core {
  background-color: $theme-color;
  border-color: $theme-color;
}

.select-dept-popover {
  padding-left: 22px !important;
}

.home-person-info-popper.el-popper[x-placement^='bottom'] {
  margin: 2px;
}
</style>
