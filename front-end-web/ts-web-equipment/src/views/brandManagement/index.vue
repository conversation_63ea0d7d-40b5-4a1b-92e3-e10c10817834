<template>
  <div class="brand-management-box">
    <ts-search-bar-new
      v-model="searchForm"
      :actions="searchActions"
      :formList="searchList"
      @search="refresh"
    >
      <template slot="right">
        <ts-button @click="handleAdd" class="shallowButton" type="primary">
          新增
        </ts-button>
        <ts-button @click="handleImport" class="shallowButton" type="primary">
          导入
        </ts-button>
        <ts-button @click="handleExport" class="shallowButton" type="primary">
          导出
        </ts-button>
      </template>
    </ts-search-bar-new>

    <ts-vxe-base-table
      id="table_brand_management"
      ref="table"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <DialogBrandDetail ref="dialogBrandDetail" @submit="refresh" />
    <DialogImportBrand ref="dialogImportBrand" @submit="refresh" />
    <base-import
      ref="baseImport"
      @refresh="refresh"
      :ImportConfiguration="ImportConfiguration"
    />
  </div>
</template>

<script>
import DialogBrandDetail from './components/dialog-brand-detail.vue';
import DialogImportBrand from './components/dialog-import-brand.vue';
export default {
  components: { DialogBrandDetail, DialogImportBrand },
  data() {
    return {
      loading: false,
      searchForm: {},
      searchActions: [],
      searchList: [
        {
          label: '品牌名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入品牌名称'
          },
          event: {
            change: () => {
              this.refresh();
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '品牌名称',
          align: 'letf',
          prop: 'name'
        },
        {
          label: '创建时间',
          align: 'center',
          width: 140,
          prop: 'createDate',
          render: (h, { row }) => {
            return (
              <span>
                {this.$dayjs(row.createDate).format('YYYY-MM-DD HH:mm')}
              </span>
            );
          }
        },
        {
          label: '创建人',
          align: 'center',
          width: 100,
          prop: 'createUserName'
        },
        {
          label: '更新时间',
          align: 'center',
          width: 140,
          prop: 'updateDate',
          render: (h, { row }) => {
            return (
              <span>
                {this.$dayjs(row.updateDate).format('YYYY-MM-DD HH:mm')}
              </span>
            );
          }
        },
        {
          label: '更新人',
          align: 'center',
          width: 100,
          prop: 'updateUserName'
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          headerSlots: 'actions',
          prop: 'actions',
          render: (h, { row }) => {
            let actions = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                className: 'actionDel',
                event: this.handleDelete
              }
            ];
            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions: actions
                }
              },
              this.$slots.default
            );
          }
        }
      ],
      ImportConfiguration: {
        importTempalteApi: '/ts-ams/api/device/brand/tpl',
        importTempalteName: '品牌导入模板',
        importApi: 'importBrand'
      }
    };
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },

    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      this.ajax.getBrandList(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },

    handleAdd() {
      this.$refs.dialogBrandDetail.show({
        type: 'add'
      });
    },

    handleEdit(row) {
      this.$refs.dialogBrandDetail.show({
        type: 'edit',
        data: row
      });
    },

    handleDelete(row) {
      this.$newConfirm(
        `【<span style="color: red">删除</span>】选中的数据？`,
        '提示',
        {
          type: 'warning'
        }
      ).then(() => {
        this.ajax.deleteBrand(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.$newMessage('success', '操作成功!');
            this.refresh();
          } else {
            this.$newMessage('error', res.message || '操作失败!');
          }
        });
      });
    },

    handleImport() {
      // this.$refs.dialogImportBrand.show();
      this.$refs.baseImport.open({
        title: '导入品牌',
        increment: true,
        quantity: false
      });
    },

    handleExport() {
      let aDom = document.createElement('a'),
        conditionList = Object.keys(this.searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      aDom.href = '/ts-ams/api/device/brand/export?' + conditionList.join('&');
      aDom.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.brand-management-box {
  height: 100%;
  width: 100%;
  padding: 8px;
  background: #fff;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.form-table {
  flex: 1;
  overflow: hidden;
}
</style>
