<template>
  <div class="complete-table-container">
    <ts-search-bar-new
      ref="TsSearchBar"
      v-model="searchForm"
      :formList="searchList"
      :resetData="resetData"
      @search="search"
    />

    <ts-vxe-base-table
      class="form-table"
      id="complete-table"
      ref="table"
      minHeight="100%"
      :defaultSort="defaultSort"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <dialog-add-install-acceptance
      ref="DialogAddInstallAcceptance"
      @submit="handleRefreshTable"
      :ymdList="ymdList"
    />

    <dialog-install-acceptance-details ref="DialogInstallAcceptanceDetails" />
  </div>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import DialogAddInstallAcceptance from './dialog-add-install-acceptance.vue';
import DialogInstallAcceptanceDetails from './dialog-install-acceptance-details.vue';
export default {
  name: 'CompleteStatus',
  props: {
    ymdList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    DialogAddInstallAcceptance,
    DialogInstallAcceptanceDetails
  },
  data() {
    return {
      defaultSort: {
        sord: 'desc',
        sidx: 'SURE_DATE'
      },
      searchForm: {},
      resetData: {},
      searchList: [
        {
          label: '验收日期',
          value: 'date',
          element: 'base-date-range-picker'
        },
        {
          label: '采购项目名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入采购项目名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '设备名称',
          value: 'skuNameSet',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入设备名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '状态',
          value: 'status',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择状态',
            filterable: true,
            clearable: true
          },
          childNodeList: [
            {
              label: '全部',
              value: '',
              element: 'ts-option'
            },
            {
              label: '待确认',
              value: '0',
              element: 'ts-option'
            },
            {
              label: '已确认',
              value: '1',
              element: 'ts-option'
            }
          ]
        }
      ],

      columns: [
        {
          label: '序号',
          prop: 'pageIndex',
          fixed: 'left',
          width: 60,
          align: 'center'
        },
        {
          label: '安装验收单据号',
          align: 'center',
          fixed: 'left',
          prop: 'flowNo',
          width: 160,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => this.handleDetails(row)
                }
              },
              row.flowNo
            );
          }
        },
        {
          label: '验收日期',
          align: 'center',
          width: 90,
          prop: 'sureDate'
        },
        {
          label: '验收人员',
          align: 'center',
          minWidth: 150,
          prop: 'sureUserName'
        },
        {
          label: '验收设备',
          align: 'center',
          minWidth: 150,
          prop: 'skuNameSet'
        },
        {
          label: '状态',
          align: 'center',
          width: 80,
          prop: 'statusShow'
        },
        {
          label: '采购项目名称',
          align: 'center',
          minWidth: 140,
          prop: 'name'
        },
        {
          label: '供应商',
          align: 'center',
          prop: 'supplierName',
          minWidth: 120
        },
        {
          label: '供应商联系人',
          align: 'center',
          prop: 'supplierContactor',
          width: 110
        },
        {
          label: '供应商联系电话',
          align: 'center',
          prop: 'supplierMobile',
          width: 120
        },
        {
          label: '登记人',
          align: 'center',
          width: 90,
          prop: 'createUserName'
        },
        {
          label: '登记时间',
          align: 'center',
          width: 140,
          prop: 'createDate'
        },
        {
          label: '操作',
          align: 'center',
          width: 210,
          headerSlots: 'actions',
          prop: 'actions',
          fixed: 'right',
          render: (h, { row }) => {
            const buildActions = () => {
              let actions = [
                {
                  label: '详情',
                  event: this.handleDetails
                }
              ];
              if (row.status === '0') {
                actions.push(
                  {
                    label: '确认验收',
                    event: this.handleConfirm
                  },
                  {
                    label: '编辑',
                    event: this.handleEdit
                  },
                  {
                    label: '撤销',
                    className: 'actionDel',
                    event: this.handleCancel
                  }
                );
              }
              return actions;
            };

            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions: buildActions()
                }
              },
              this.$slots.default
            );
          }
        }
      ]
    };
  },
  created() {
    this.handleInitResetData();
  },
  methods: {
    refresh() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    handleInitResetData() {
      let date = [
        this.$dayjs()
          .subtract(6, 'month')
          .format('YYYY-MM-DD'),
        this.$dayjs().format('YYYY-MM-DD')
      ];
      this.searchForm = {
        date,
        status: ''
      };
      this.resetData = {
        date,
        status: ''
      };
    },

    checkData([start, end]) {
      let num = 0;
      start && num++;
      end && num++;
      return num === 1;
    },

    checkQueryParam() {
      const { date = [] } = this.searchForm;
      if (this.checkData(date)) {
        this.$newMessage('warning', '请选择验收日期完整的时间区间查询');
        return false;
      }
      return true;
    },

    async handleRefreshTable() {
      if (!this.checkQueryParam()) {
        return;
      }

      let { date = [] } = this.searchForm,
        pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        formData = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sord: this.defaultSort.sord,
          sidx: this.defaultSort.sidx
        };

      const [startD, endD] = date;
      formData.sureDateQuery =
        startD && endD ? `${startD} 00:00:00,${endD} 23:59:59` : '';
      !formData.sureDateQuery && delete formData.sureDateQuery;
      delete formData.date;

      let res = await this.ajax.signoffedList(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }

      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });

      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    // 详情
    async handleDetails(row) {
      let res = await this.ajax.signoffDetails(row.id);
      if (!res.success) {
        this.$newMessage('error', res.message || '安装验收详情获取失败!');
        return;
      }

      this.$refs.DialogInstallAcceptanceDetails.open({
        data: res.object
      });
    },

    // 确认验收
    handleConfirm(row) {
      let title = `确定对当前选中的安装验收单据<span style="color: ${primaryBlue}">【确认验收】</span>吗？`;
      this.$newConfirm(title, '提示', {
        type: 'tips'
      }).then(() => {
        this.ajax.signoffSure(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.$newMessage('success', '操作成功!');
            this.handleRefreshTable();
          } else {
            this.$newMessage('error', res.message || '操作失败!');
          }
        });
      });
    },

    // 编辑
    async handleEdit(row) {
      let res = await this.ajax.signoffDetails(row.id);
      if (!res.success) {
        this.$newMessage('error', res.message || '安装验收详情获取失败!');
        return;
      }

      // selectType 1 采入购库未登记 2 其他入库未登记
      this.$refs.DialogAddInstallAcceptance.open({
        type: 'edit',
        selectType: row.purchaseOrderId ? '1' : '2',
        data: res.object
      });
    },

    // 撤销
    handleCancel(row) {
      let title = `确定<span style="color: red">【撤消】</span>当前选中的安装验收单据吗？`;
      this.$newConfirm(title, '提示', {
        type: 'warning'
      }).then(() => {
        this.ajax.signoffDelete(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.$newMessage('success', '操作成功!');
            this.handleRefreshTable();
          } else {
            this.$newMessage('error', res.message || '操作失败!');
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.complete-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid $primary-blue;
  padding: 8px;
  border-radius: 4px;
  ::v-deep {
    .base-date-range-picker {
      .el-date-editor {
        width: 135px !important;
        min-width: 135px !important;
      }
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);
  }
}
</style>
