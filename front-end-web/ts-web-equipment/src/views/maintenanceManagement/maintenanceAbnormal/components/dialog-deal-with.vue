<template>
  <ts-dialog title="处理" :visible.sync="visible" @close="handleCancel">
    <ts-form ref="form" :model="form">
      <ts-form-item
        label="处理方式"
        prop="dealWithMethod"
        :rules="rules.required"
      >
        <ts-select
          v-model="form.dealWithMethod"
          clearable
          class="full-content-picker"
        >
          <ts-option
            v-for="item of calibrationDealWithMethod"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></ts-option>
        </ts-select>
      </ts-form-item>
      <ts-form-item label="备注">
        <ts-input
          v-model="form.remark"
          type="textarea"
          class="textarea"
          maxlength="200"
          show-word-limit
          placeholder="请输入,200字以内"
        />
      </ts-form-item>
    </ts-form>
    <template slot="footer">
      <ts-button type="primary" :loading="submitLoading" @click="handleSubmit">
        确 定
      </ts-button>
      <ts-button :disabled="submitLoading" @click="handleCancel">
        取 消
      </ts-button>
    </template>
  </ts-dialog>
</template>
<script>
import { calibrationDealWithMethod } from '@/assets/js/constants.js';
import { deepClone } from '@/unit/commonHandle.js';
export default {
  name: 'DialogClassificationDetail',
  data() {
    return {
      visible: false,
      submitLoading: false,
      calibrationDealWithMethod: calibrationDealWithMethod,

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    async show({ data = {} }) {
      this.form = deepClone(data);
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },
    async handleSubmit() {
      try {
        this.submitLoading = true;
        await this.$refs.form.validate();
        let data = deepClone(this.form);
        return;
        this.ajax.dealMaintenanceAbnormal(data).then(res => {
          this.submitLoading = false;
          if (res.success && res.statusCode === 200) {
            this.$message.success(res.message || '操作成功');
            this.handleCancel();
            this.$emit('submit');
          } else {
            this.$message.error(res.message || '操作失败');
          }
        });
      } catch (error) {
        this.submitLoading = false;
        console.error(error);
      }
    },
    handleCancel() {
      this.submitLoading = false;
      this.addModalVisible = false;
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped></style>
