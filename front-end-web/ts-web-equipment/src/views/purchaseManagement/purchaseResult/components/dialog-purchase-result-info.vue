<template>
  <ts-dialog
    custom-class="dialog-purchase-result-info-box"
    :fullscreen="true"
    :visible.sync="visible"
    :append-to-body="true"
    @close="handleCancel"
  >
    <template slot="title">
      <div class="dialog-title">
        {{ title }}
        <div v-if="editData.status == 3" class="aproval-fail-tips">
          <span class="tips-icon el-icon-warning-outline"></span>
          资料审核不通过！
          <span style="color: #333;">原因：{{ editData.checkRemark }}</span>
        </div>
        <div v-else-if="editData.status == 2" class="aproval-pass-tips">
          <span class="tips-icon el-icon-circle-check"></span>
          资料审核通过！
        </div>
      </div>
    </template>
    <div class="dialog-purchase-result-content">
      <el-scrollbar
        ref="scroll"
        style="flex: 1;"
        wrap-style="height: calc(100% + 17px);"
      >
        <purchase-base-info
          ref="basicDetail"
          :detail="editData"
          :actionType="actionType"
          :isFirst="!purchaseList.length"
          @changeType="handleChangeType"
        />
        <div class="info-title" v-if="purchaseList.length">
          采购结果
          <span
            class="add-btn"
            v-if="actionType == 'edit'"
            @click="refreshForm"
          >
            +
          </span>
        </div>
        <base-table
          v-if="purchaseList.length"
          ref="table"
          border
          stripe
          :columns="columns"
          :span-method="hanldeMergeTable"
          :hasPage="false"
        />
        <component
          v-if="showForm"
          ref="component"
          :is="formType + '-purchase-form'"
          :editData="purchaseGroup"
          :actionType="actionType"
        />
      </el-scrollbar>
    </div>
    <template slot="footer">
      <template v-if="actionType == 'edit'">
        <ts-button
          v-if="showForm"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit('save')"
        >
          保 存
        </ts-button>
        <ts-button
          v-if="showForm"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit('saveAndSubmit')"
        >
          保存并提交
        </ts-button>
        <ts-button
          v-if="!showForm"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit('submit')"
        >
          提 交
        </ts-button>
      </template>
      <template v-else-if="actionType == 'approval'">
        <ts-button
          type="success"
          :loading="submitLoading"
          @click="handleApproval('approve')"
        >
          审核通过
        </ts-button>
        <ts-button
          type="danger"
          :disabled="submitLoading"
          @click="handleApproval('disapprove')"
        >
          审核不通过
        </ts-button>
      </template>
      <ts-button :disabled="submitLoading" @click="handleCancel">
        {{ actionType == 'edit' && showForm ? '取 消' : '关 闭' }}
      </ts-button>
    </template>
    <DialogDisapprove ref="dialogDisapprove" @submit="handleDisapprove" />
  </ts-dialog>
</template>

<script>
import PurchaseBaseInfo from './purchase-base-info.vue';
import EquipmentPurchaseForm from './equipment-purchase-form.vue';
import GeneralEquipmentPurchaseForm from './general-equipment-purchase-form.vue';
import servicePurchaseForm from './service-purchase-form.vue';
import constructionPurchaseForm from './construction-purchase-form.vue';
import DialogDisapprove from './dialog-disapprove.vue';

import { deepClone } from '@/util/index.js';
import dialogColumnMap from '../mixins/dialog-column-map';

export default {
  name: 'DialogPurchaseResultInfo',
  mixins: [dialogColumnMap],
  components: {
    PurchaseBaseInfo,
    EquipmentPurchaseForm,
    GeneralEquipmentPurchaseForm,
    servicePurchaseForm,
    constructionPurchaseForm,
    DialogDisapprove
  },
  data() {
    return {
      title: '',
      actionType: 'check',
      visible: false,
      showForm: false,
      formType: 'equipment',
      editData: {},
      purchaseList: [],
      purchaseGroupList: [],
      purchaseGroup: {},
      submitLoading: false
    };
  },
  computed: {
    columns() {
      let formType =
        this.formType == 'general-equipment' ? 'equipment' : this.formType;
      return this.projectTypeData[formType]['columnsList'];
    }
  },
  created() {
    Object.keys(this.projectTypeData).map(typeItem => {
      let hideList = this.projectTypeData[typeItem].hideColumnKeyList;
      let columnsMapList = this.columnsMap(typeItem);
      columnsMapList.forEach((value, key, item) => {
        !hideList.includes(key) &&
          this.projectTypeData[typeItem].columnsList.push(value);
      });
    });
  },
  methods: {
    async show(data) {
      this.actionType = data.actionType;
      switch (data.actionType) {
        case 'edit':
          this.title = '维护采购结果';
          break;
        case 'approval':
          this.title = '资料审核';
          break;
        default:
          this.title = '查看采购结果';
          break;
      }
      this.editData = deepClone(data.editData);
      this.formType = this.wfDefId[data.editData.wfDefinitionId]['type'];
      this.visible = true;
      await this.getPurchaseResult(data.editData.id);
      await this.$nextTick(async () => {
        await this.$refs.table?.refresh({
          rows: this.purchaseList
        });
        // if (this.purchaseList.length)
        //   this.handleShowDetail(this.purchaseList[0]);
      });
    },
    async getPurchaseResult(id) {
      await this.ajax.getPurchaseResult(id).then(async res => {
        if (res.success == false) {
          this.$message.error(res.message || '数据获取失败');
          return;
        }
        let groupList = res.object.purchaseGroupList,
          logList = res.object.purchaseLogList;
        if (groupList.length) {
          let list = [];
          let groupIdList = await Promise.all(
            logList.map(item => item.purchaseGroupId)
          );
          groupIdList = Array.from(new Set(groupIdList));
          await Promise.all(
            groupIdList.map((groupId, index) => {
              let filterList = logList.filter(
                item => item.purchaseGroupId == groupId
              );
              filterList[0].mergeLength = filterList.length;
              filterList[0].pageIndex = index + 1;
              list.push(...filterList);
              let filterGroupList = groupList.filter(
                groupItem => groupItem.id == groupId
              );
              this.purchaseGroupList.push({
                ...filterGroupList[0],
                purchaseLogList: filterList
              });
            })
          );
          this.purchaseList = list;
        } else {
          this.purchaseList = [];
          this.purchaseGroup = {};
          this.refreshForm();
        }
      });
    },
    refreshForm() {
      this.showForm = true;
      let formType =
        this.formType == 'general-equipment' ? 'equipment' : this.formType;
      let purchaseLogField = deepClone(this.projectTypeData[formType]['field']);
      this.purchaseGroup = {
        purchaseLogList: []
      };
      this.purchaseGroup.purchaseLogList.push(purchaseLogField);
      this.$nextTick(() => {
        this.$refs.component.$refs.form?.clearValidate();
      });
    },
    hanldeMergeTable({ row, column, rowIndex, columnIndex }) {
      if (['pageIndex', 'header'].includes(column.property)) {
        return row.mergeLength ? [row.mergeLength, 1] : [0, 0];
      }
      return [1, 1];
    },
    handleShowDetail(data) {
      this.showForm = true;
      let editGroupList = this.purchaseGroupList.filter(
        groupItem => groupItem.id == data.purchaseGroupId
      );
      let editGroup = editGroupList[0];
      //特殊处理服务起止时间
      if (this.formType == 'service') {
        editGroup.purchaseLogList.forEach(logItem => {
          if (logItem.purchaseSpec) {
            logItem.purchaseSpecArr = logItem.purchaseSpec
              .split('-')
              .map(time => {
                if (this.$dayjs(time).isValid())
                  return this.$dayjs(time).format('YYYY-MM-DD');
                else return '';
              });
          }
        });
      }
      this.purchaseGroup = editGroup;

      this.$nextTick(() => {
        this.$refs.component.$refs.form?.clearValidate();
      });
    },
    async handleSubmit(type) {
      try {
        let data = {};
        data.isPurchase = 1;
        data.id = this.editData.id;
        data.type = this.$refs.basicDetail.form.type || this.editData.type;
        if (type == 'submit') {
          data.purchaseGroupReqList = deepClone(this.purchaseGroupList);
        } else {
          let formData = await this.$refs.component.submit();
          if (!formData) {
            throw '表单必填项未填写!';
          }
          //特殊处理服务起止时间
          if (this.formType == 'service') {
            formData.purchaseLogList.forEach(logItem => {
              if (logItem.purchaseSpecArr && logItem.purchaseSpecArr.length) {
                let purchaseSpec = logItem.purchaseSpecArr
                  .map(time => {
                    return this.$dayjs(time).format('YYYY.MM.DD');
                  })
                  .join('-');
                logItem.purchaseSpec = purchaseSpec;
              }
            });
          }
          let purchaseGroupList = deepClone(this.purchaseGroupList);
          if (formData.id) {
            let groupIndex = purchaseGroupList.findIndex(
              groupItem => groupItem.id == formData.id
            );
            if (groupIndex != -1) {
              purchaseGroupList[groupIndex] = deepClone(formData);
            }
          } else {
            purchaseGroupList.push(formData);
          }
          data.purchaseGroupReqList = purchaseGroupList;
        }
        this.submitLoading = true;
        const res = await this.ajax.updatePurchaseResult(data);

        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.$emit('submit');
          if (type != 'save') {
            this.handleCancel();
          } else {
            this.showForm = false;
            this.purchaseGroup = {};
            this.purchaseGroupList = [];
            await this.getPurchaseResult(data.id);
            this.$refs.table.refresh({
              rows: this.purchaseList
            });
          }
        } else {
          this.$message.error(res.message || '操作失败!');
        }
        this.submitLoading = false;
      } catch (error) {
        console.error(error);
        this.submitLoading = false;
      }
    },
    async handleApproval(type) {
      if (type == 'approve') {
        await this.$confirm(
          `<span>确定实际已采购设备资料已维护完整？</span>`,
          '提示',
          {
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        );
        await this.handleApprovalSubmit(this.editData.id, {
          status: 2,
          remark: ''
        });
      } else {
        this.$refs.dialogDisapprove.show();
      }
    },
    async handleDisapprove(approvalForm, callback) {
      let data = {
        status: 3,
        ...approvalForm
      };
      await this.handleApprovalSubmit(this.editData.id, data, callback);
    },
    async handleApprovalSubmit(id, data, callback) {
      try {
        this.submitLoading = true;
        const res = await this.ajax.reviewPurchaseResult(id, data);
        this.submitLoading = false;
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          callback && (await callback(res.success && res.statusCode === 200));
          this.handleCancel();
          this.$emit('submit');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        this.submitLoading = false;
        console.error(e);
      }
    },
    handleCancel() {
      this.submitLoading = false;
      this.editData = {};
      this.purchaseList = [];
      this.purchaseGroupList = [];
      this.purchaseGroup = {};
      this.showForm = false;
      this.visible = false;
      this.$emit('change', false);
    },
    handleChangeType(value) {
      this.formType = value;
      this.refreshForm();
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-title {
  color: #333333;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}
.aproval-fail-tips,
.aproval-pass-tips {
  font-size: 14px;
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  .tips-icon {
    font-size: 16px;
  }
}
.aproval-fail-tips {
  color: $warning-color;
  background-color: rgb(250, 238, 230);
}
.aproval-pass-tips {
  color: $success-color;
  background-color: rgb(219, 241, 209);
}
.dialog-purchase-result-content {
  height: calc(100vh - 143px);
  overflow: hidden;
  display: flex;
}
.action-cell {
  cursor: pointer;
  color: rgb(82, 96, 255);
}
.info-title {
  border-left: 3px solid $primary-blue;
  font-weight: bold;
  font-size: 16px;
  padding-left: 8px;
  line-height: 1;
  margin: 16px 0;
  position: relative;
  .add-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 22px;
    font-weight: bold;
    border: 2px solid #666;
    height: 28px;
    width: 28px;
    text-align: center;
    line-height: 1;
    border-radius: 4px;
    color: #666;
    cursor: pointer;
  }
}
/deep/ {
  .dialog-purchase-result-info-box .el-dialog__body {
    margin: 24px 24px 0 !important;
    width: auto !important;
  }
  .dialog-purchase-result-info-box .el-dialog__footer {
    margin: 0 24px !important;
    width: auto !important;
  }
}
</style>
