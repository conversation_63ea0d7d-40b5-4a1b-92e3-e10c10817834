export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      searchList: [
        {
          label: '任务名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入任务名称'
          },
          event: {
            change: () => {
              this.refresh();
            }
          }
        },
        {
          label: '盘点状态',
          value: 'status',
          element: 'ts-select',
          childNodeList: [
            {
              element: 'ts-option',
              label: '全部',
              value: ''
            },
            {
              element: 'ts-option',
              label: '未开始',
              value: '0'
            },
            {
              element: 'ts-option',
              label: '进行中',
              value: '1'
            },
            {
              element: 'ts-option',
              label: '已结束',
              value: '2'
            }
          ],
          elementProp: {
            placeholder: '请选择出库状态',
            clearable: true
          }
        },
        {
          label: '资产类别',
          value: 'skuType',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择资产类别',
            filterable: true,
            clearable: true
          },
          childNodeList: []
        }
      ],

      columns: [
        { label: '序号', prop: 'index', width: 60, align: 'center' },
        {
          label: '任务名称',
          align: 'center',
          prop: 'name',
          minWidth: 160,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: 'action-cell',
                on: {
                  click: () => {
                    this.handleInventoryDetails(row);
                  }
                }
              },
              row.name
            );
          }
        },
        {
          label: '计划执行人',
          align: 'center',
          minWidth: 140,
          prop: 'engineerNameSet'
        },
        { label: '盘点开始时间', align: 'center', width: 105, prop: 'startAt' },
        { label: '盘点结束时间', align: 'center', width: 105, prop: 'endAt' },
        {
          label: '资产类别',
          align: 'center',
          width: 90,
          prop: 'skuTypeShow'
        },
        {
          label: '盘点科室数',
          align: 'center',
          width: 90,
          prop: 'orgs'
        },
        {
          label: '盘点资产数',
          align: 'center',
          width: 90,
          prop: 'devices'
        },
        {
          label: '盘到数量',
          align: 'center',
          width: 70,
          prop: 'pds',
          render: (h, { row }) => {
            return h('div', { class: 'green' }, row.pds);
          }
        },
        {
          label: '遗失数量',
          align: 'center',
          width: 70,
          prop: 'pks',
          render: (h, { row }) => {
            return h('div', { class: 'red' }, row.pks);
          }
        },
        {
          label: '正常数量',
          align: 'center',
          width: 70,
          prop: 'zczs',
          render: (h, { row }) => {
            return h('div', { class: 'green' }, row.zczs);
          }
        },
        {
          label: '异常数量',
          align: 'center',
          width: 70,
          prop: 'ycs',
          render: (h, { row }) => {
            return h('div', { class: 'red' }, row.ycs);
          }
        },
        { label: '状态', align: 'center', width: 75, prop: 'statusShow' },
        {
          label: '操作',
          align: 'center',
          width: 130,
          headerSlots: 'actions',
          prop: 'actions',
          fixed: 'right',
          render: (h, { row }) => {
            let actions = [
              {
                label: row.status === '2' ? '详情' : '盘点',
                event: this.handleInventoryDetails
              }
            ];

            if (row.status !== '2' && this.showOperateBtn) {
              actions.unshift({
                label: '删除',
                className: 'actionDel',
                event: this.handleDelete
              });
            }

            if (row.status === '0' && this.showOperateBtn) {
              actions.unshift({
                label: '编辑',
                event: this.handleEdit
              });
            }
            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions: actions
                }
              },
              this.$slots.default
            );
          }
        }
      ]
    };
  }
};
