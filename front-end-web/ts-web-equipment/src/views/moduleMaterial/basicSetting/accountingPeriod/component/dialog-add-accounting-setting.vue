<template>
  <vxe-modal
    className="dialog-add-accounting-setting"
    width="380"
    v-model="visible"
    title="会计期间设置"
    showFooter
  >
    <template #default>
      <ts-form ref="form" :model="form" labelWidth="130px">
        <ts-form-item
          label="是否按自然月"
          prop="isNatureMonth"
          :rules="rules.required"
        >
          <ts-radio-group v-model="form.isNatureMonth" placeholder="请选择">
            <ts-radio
              v-for="item in isYesNo"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </ts-radio>
          </ts-radio-group>
        </ts-form-item>

        <ts-form-item
          v-if="isNatureMonth"
          label="每月开始日期"
          prop="startDay"
          :rules="isNatureMonth ? rules.number : []"
        >
          <ts-input
            type="number"
            v-model="form.startDay"
            placeholder="请输入保质期"
            :max="28"
            :min="1"
            @input="handleInputstartDay"
          />
        </ts-form-item>

        <ts-form-item
          label="是否系统自动生成"
          prop="isAutoGen"
          :rules="rules.required"
        >
          <ts-radio-group v-model="form.isAutoGen" placeholder="请选择">
            <ts-radio
              v-for="item in isYesNo"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </ts-radio>
          </ts-radio-group>
        </ts-form-item>
      </ts-form>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">确 定</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import { isYesNo } from '@/assets/js/constants';
export default {
  data() {
    return {
      visible: false,
      numberReg: /^(?:[1-9]|1\d|2[0-8])$/,
      isYesNo,

      form: {},
      rules: {
        required: { required: true, message: '必填' },
        number: [
          {
            required: true,
            message: '必填'
          },
          {
            validator: (rule, value, callback) => {
              if (!this.numberReg.test(value)) {
                callback(new Error('请输入1-28之间的整数'));
              }
              callback();
            }
          }
        ]
      }
    };
  },
  computed: {
    isNatureMonth() {
      return this.form.isNatureMonth === '0';
    }
  },
  methods: {
    async open({ data }) {
      if (JSON.stringify(data) !== '{}') {
        this.$set(this, 'form', data);
      } else {
        this.$set(this, 'form', {
          isNatureMonth: '1',
          isAutoGen: '1'
        });
      }

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    handleInputstartDay(value) {
      this.form.startDay = (value.match(this.numberReg) || [''])[0];
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = cloneDeep(this.form);
        this.submitLoading = true;
        const res = await this.ajax.saveMaterialAccountingPeriodConfig(
          formData
        );
        if (!res.success) {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `操作失败!`);
          return;
        }

        this.submitLoading = false;
        this.$newMessage('success', `操作成功`);
        this.$emit('refresh');
        this.close();
      } catch (error) {
        console.log(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    }
  }
};
</script>
