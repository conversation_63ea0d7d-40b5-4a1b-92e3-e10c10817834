<template>
  <vxe-modal
    className="dialog-device-details"
    width="88%"
    height="85%"
    :title="isOutbound ? '出库明细' : '入库明细'"
    v-model="visible"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <div
          class="basic-info"
          v-if="visible && JSON.stringify(details) !== '{}'"
        >
          <template v-if="isPutIn || isBack">
            <div class="row">
              <div class="item">
                <span class="label">入库日期：</span>
                <span class="value">
                  {{ details.doDate }}
                </span>
              </div>
              <div class="item">
                <span class="label">入库单号：</span>
                <span class="value">
                  {{ details.batchNo }}
                </span>
              </div>
              <div class="item">
                <span class="label">入库人员：</span>
                <span class="value">
                  {{ details.doerName }}
                </span>
              </div>
              <div class="item">
                <span class="label">入库类型：</span>
                <span class="value">
                  {{ details.typeShow }}
                </span>
              </div>
            </div>
            <div class="row">
              <div class="item">
                <span class="label">入库状态：</span>
                <span class="value">
                  {{ details.statusShow }}
                </span>
              </div>
              <div class="item">
                <span class="label">供应商：</span>
                <span class="value">
                  {{ details.supplyName }}
                </span>
              </div>
              <div class="item">
                <span class="label">总金额：</span>
                <span class="value">
                  {{
                    !isNaN(parseFloat(details.money))
                      ? parseFloat(details.money).toLocaleString('zh-CN')
                      : details.money || ''
                  }}元
                </span>
              </div>
              <!--<div class="item">-->
              <!--  <span class="value other"><img src="" alt="" />CT采购机</span>-->
              <!--</div>-->
            </div>
          </template>
          <template v-if="isOutbound">
            <div class="row">
              <div class="item">
                <span class="label">出库日期：</span>
                <span class="value">
                  {{ details.doDate }}
                </span>
              </div>
              <div class="item">
                <span class="label">出库单号：</span>
                <span class="value">
                  {{ details.batchNo }}
                </span>
              </div>
              <div class="item">
                <span class="label">出库类型：</span>
                <span class="value">
                  {{ details.typeShow }}
                </span>
              </div>
              <div class="item">
                <span class="label">出库科室：</span>
                <span class="value">
                  {{ details.outOrgName }}
                </span>
              </div>
            </div>
            <div class="row">
              <div class="item">
                <span class="label">出库状态：</span>
                <span class="value">
                  {{ details.statusShow }}
                </span>
              </div>
              <div class="item">
                <span class="label">总金额：</span>
                <span class="value">
                  {{
                    !isNaN(parseFloat(details.money))
                      ? parseFloat(details.money).toLocaleString('zh-CN')
                      : details.money || ''
                  }}元
                </span>
              </div>
              <!--<div class="item">-->
              <!--  <span class="value other"><img src="" alt="" />CT采购机</span>-->
              <!--</div>-->
            </div>
          </template>

          <div class="row">
            <div class="item" style="width: 100%;">
              <span class="label">备注：</span>
              <span class="value">
                {{ details.note }}
              </span>
            </div>
          </div>
        </div>

        <ts-vxe-base-table
          v-if="visible"
          class="form-table"
          id="table_inventory_details"
          ref="table"
          minHeight="100%"
          :columns="columns"
          :hasPage="false"
          show-footer
          :footer-data="footerData"
          footer-cell-class-name="footer-cell"
          @refresh="handleRefreshTable"
        />
      </div>

      <dialog-device-print
        ref="DialogDevicePrint"
        :details="details"
        :detailsList="detailsList"
        :renderType="renderType"
      />
    </template>

    <template #footer>
      <ts-button class="shallowButton" @click="handlePrint">
        打印{{ isOutbound ? '出库单' : '入库单' }}
      </ts-button>
      <ts-button class="shallowButton" @click="close">
        关 闭
      </ts-button>
    </template>
  </vxe-modal>
</template>
<script>
import { Decimal } from 'decimal.js';
import { deepClone } from '@/unit/commonHandle.js';

import DialogDevicePrint from '@/views/inventoryManagement/components/dialog-device-print.vue';
export default {
  components: {
    DialogDevicePrint
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      details: {},
      detailsList: [],

      renderType: '',
      columns: [],
      footerData: []
    };
  },
  computed: {
    isPutIn() {
      return this.renderType === '1'; // 资产入库
    },
    isBack() {
      return this.renderType === '2'; // 资产退回入库
    },
    isOutbound() {
      return this.renderType === '3'; // 资产出库
    }
  },
  methods: {
    async show({ data, renderType, triggerPrint }) {
      this.details = data.details;
      let arr = deepClone(data?.detailsList || []);
      // 格式化数据
      this.detailsList = this.handleFormatterData(arr);
      this.renderType = renderType;
      this.handleInitSetting();
      this.visible = true;

      this.$nextTick(() => {
        this.handleRefreshTable(this.detailsList);

        setTimeout(() => {
          if (triggerPrint) this.handlePrint();
        }, 500);
      });
    },

    handlePrint() {
      this.$refs.DialogDevicePrint.show();
    },

    handleInitSetting() {
      let columns = [
        {
          label: '序号',
          prop: 'index',
          width: 60,
          align: 'center'
        },
        { prop: 'name', label: '资产名称', align: 'center', minWidth: 140 },
        { slot: 'slot1' },
        {
          prop: 'category22Name',
          label: '医疗器械分类',
          align: 'center',
          minWidth: 140
        },
        {
          prop: 'num',
          label: '数量',
          align: 'center',
          width: 65,
          render: (h, { row }) => {
            return h('span', null, this.formatNumber(row.num));
          }
        },
        {
          prop: 'price',
          label: '单价(元)',
          align: 'right',
          width: 105,
          render: (h, { row }) => {
            return h('span', null, this.formatNumber(row.price));
          }
        },
        {
          prop: 'amount',
          label: '金额(元)',
          align: 'right',
          width: 105,
          render: (h, { row }) => {
            return h('span', null, this.formatNumber(row.amount));
          }
        },
        { prop: 'model', label: '规格型号', align: 'center', width: 110 },
        {
          prop: 'brandName',
          label: '资产品牌',
          align: 'center',
          minWidth: 140
        },
        {
          prop: 'manufacturerName',
          label: '资产厂家',
          align: 'center',
          minWidth: 140
        },
        { slot: 'slot2' },
        { slot: 'slot3' }
      ];

      // 入库， 退回入库
      if (this.isPutIn || this.isBack) {
        let slot1Index = columns.findIndex(f => f.slot === 'slot1');
        if (slot1Index !== -1) columns.splice(slot1Index, 1);

        let slot2Index = columns.findIndex(f => f.slot === 'slot2');
        columns[slot2Index] = {
          prop: 'loc',
          label: '入库位置',
          align: 'center',
          minWidth: 140
        };
      }
      if (this.isOutbound) {
        let slot1Index = columns.findIndex(f => f.slot === 'slot1');
        columns[slot1Index] = {
          prop: 'assetCode',
          label: '资产编码',
          align: 'center',
          minWidth: 95
        };

        let slot2Index = columns.findIndex(f => f.slot === 'slot2');
        columns[slot2Index] = {
          prop: 'supplierName',
          label: '供应商',
          align: 'center',
          minWidth: 140
        };

        let slot3Index = columns.findIndex(f => f.slot === 'slot3');
        columns[slot3Index] = {
          prop: 'tloc',
          label: '位置',
          align: 'center',
          minWidth: 140
        };
      }

      this.columns = columns;
    },

    handleFormatterData(list) {
      return list.map((item, i) => {
        let amount;
        const isNumValid = !isNaN(item.num);
        const isPriceValid = !isNaN(item.price);
        if (isNumValid && isPriceValid) {
          amount = Decimal.mul(item.price, item.num).toNumber();
        }

        return {
          ...item,
          index: i + 1,
          amount
        };
      });
    },

    handleRefreshTable(rows) {
      this.footerData = [];
      this.$refs.table.refresh(rows);
      this.footerData = this.handleGetFooterData(rows);
    },

    handleGetFooterData(rows) {
      const calculateTotal = key => {
        return rows
          .map(row => row[key])
          .reduce((prev, curr) => {
            return !isNaN(curr) ? prev.plus(curr) : prev;
          }, new Decimal(0))
          .toNumber()
          .toLocaleString('zh-CN');
      };

      return [
        {
          index: '合计',
          num: calculateTotal('num'),
          amount: calculateTotal('amount')
        }
      ];
    },

    formatNumber(value) {
      return isNaN(Number(value))
        ? value
        : Number(value).toLocaleString('zh-CN');
    },

    close() {
      this.details = {};
      this.detailsList = [];

      this.submitLoading = false;
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-device-details {
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .basic-info {
      .row {
        display: flex;
        width: 100%;
        .item {
          width: 25%;
          display: flex;
          .label {
            width: 90px;
            text-align: right;
            display: inline-flex;
            align-items: center;
            justify-content: flex-end;
          }
          .value {
            flex: 1;
            display: inline-flex;
            align-items: center;
            &.other {
              margin-left: 32px;
              font-size: 14px;
              color: $primary-blue;
            }
          }
        }
      }
    }

    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
      ::v-deep {
        .footer-cell {
          .vxe-cell--item {
            font-weight: bold;
            color: $warning-color;
          }
        }
      }
    }
  }
}
</style>
