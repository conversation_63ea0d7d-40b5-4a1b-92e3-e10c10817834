<template>
  <div class="trasen-container flex-column">
    <ts-tabs v-model="activeTable" @tab-click="handleTabClick">
      <ts-tab-pane
        v-for="(item, index) in tabList"
        :key="index"
        :name="item.name"
      >
        <span slot="label">{{
          `${item.label}${item.count != undefined ? `(${item.count})` : ''}`
        }}</span>
      </ts-tab-pane>
    </ts-tabs>
    <all-table
      ref="allTable"
      :tableData.sync="tableDatas[activeTable]"
      @event="handleEvent"
      @refresh="handleGetTableData"
    >
    </all-table>

    <ts-dialog :title="title" :visible.sync="showEditModal" :fullscreen="true">
      <div class="dispute-action-content flex-column">
        <ts-tabs
          v-model="editActiveTab"
          :type="null"
          :class="{
            'register-add-borad':
              editType == 'registerAdd' || editType == 'registerEdit',
            'action-tab flex-column ts-tabs tabs-content': true
          }"
        >
          <ts-tab-pane label="基本信息" name="basic">
            <el-scrollbar
              ref="basicScroll"
              style="height: 100%;"
              wrap-style="height: calc(100% + 17px);"
            >
              <dispute-base-inform-form
                ref="baseForm"
                :data="baseInfo"
                :renderType="editType"
              ></dispute-base-inform-form>
              <ts-form
                ref="form"
                class="operation-form"
                v-if="editType != 'registerEdit'"
                :model="editData"
                label-position="right"
                label-width="96px"
              >
                <p class="content-title">{{ title }}</p>

                <!-- 处理 -->
                <template v-if="editType == 'deal'">
                  <ts-form-item
                    label="是否处理完成"
                    prop="isSolved"
                    :rules="editRules.requiredRow"
                    label-width="108px"
                  >
                    <ts-radio-group v-model="editData.isSolved">
                      <ts-radio label="2">是</ts-radio>
                      <ts-radio label="1">否</ts-radio>
                    </ts-radio-group>
                  </ts-form-item>

                  <ts-row>
                    <ts-col :span="6">
                      <ts-form-item
                        label="处理人"
                        prop="handledUserCode"
                        :rules="editRules.requiredRow"
                      >
                        <base-select
                          v-model="editData.handledUserCode"
                          :inputText.sync="editData.handledUserName"
                          :loadMethod="getPeopleListData"
                          label="employeeName"
                          value="employeeNo"
                          searchInputName="employeeName"
                        ></base-select>
                      </ts-form-item>
                    </ts-col>

                    <ts-col :span="6">
                      <ts-form-item
                        label="处理科室"
                        prop="handledUserOrgCode"
                        :rules="editRules.requiredRow"
                      >
                        <ts-ztree-select
                          style="display: inline-block"
                          :data="deptTreeData"
                          defaultExpandAll
                          :inpText.sync="editData.handledUserOrgName"
                          :inpVal.sync="editData.handledUserOrgCode"
                        ></ts-ztree-select>
                      </ts-form-item>
                    </ts-col>

                    <ts-col :span="6">
                      <ts-form-item
                        label="处理时间"
                        prop="handledTime"
                        :rules="editRules.requiredRow"
                      >
                        <ts-date-picker
                          v-model="editData.handledTime"
                          format="YYYY-MM-DD"
                        ></ts-date-picker>
                      </ts-form-item>
                    </ts-col>

                    <ts-col :span="6">
                      <ts-form-item
                        label="处理方式"
                        prop="handledType"
                        :rules="editRules.requiredRow"
                      >
                        <ts-select v-model="editData.handledType" clearable>
                          <ts-option
                            v-for="(item, index) of resolventList"
                            :key="index"
                            v-bind="item"
                          ></ts-option>
                        </ts-select>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>

                  <ts-row>
                    <ts-col :span="6">
                      <ts-form-item label="赔偿金额" prop="money">
                        <ts-input
                          v-model="editData.claimAmount"
                          placeholder="请输入赔偿金额"
                        ></ts-input>
                      </ts-form-item>
                    </ts-col>
                  </ts-row>
                </template>

                <template v-if="hasVerticalReason">
                  <ts-form-item
                    prop="templateRemarkText"
                    :rules="editRules.templateRemarkText"
                    class="vertical-form-item"
                  >
                    <span slot="label">
                      {{ templateRemarkTitle }}
                      <el-popover placement="bottom-start" trigger="click">
                        <span
                          slot="reference"
                          class="choose-template"
                          @click="handleTriggerLoadTemplate"
                        >
                          选择模板
                        </span>
                        <div ref="templateContent" class="template-content">
                          <el-scrollbar
                            ref="scroll"
                            :style="{
                              height: templateContentHeight
                            }"
                            v-infinity-scroll="{
                              loadMethod: handleGetTemplate,
                              selector: '.template-scrollbar'
                            }"
                            wrap-class="template-scrollbar"
                            wrap-style="overflow-x: hidden;"
                          >
                            <ul ref="templateList" style="margin: 0;">
                              <li
                                v-for="(item, index) of templateList"
                                :key="index"
                                class="template-item"
                                @click="handleFillWithTemplate(item)"
                              >
                                {{ item }}
                              </li>
                            </ul>
                          </el-scrollbar>
                        </div>
                      </el-popover>
                    </span>
                    <ts-input
                      v-model="editData.templateRemarkText"
                      placeholder="请输入内容"
                      type="textarea"
                    ></ts-input>
                  </ts-form-item>
                </template>
              </ts-form>
            </el-scrollbar>
          </ts-tab-pane>
          <ts-tab-pane label="附件" name="files">
            <upload-detail :businessId="editData.businessId"></upload-detail>
          </ts-tab-pane>
          <ts-tab-pane label="操作日志" name="logs">
            <log-detail></log-detail>
          </ts-tab-pane>
        </ts-tabs>
      </div>
      <div slot="footer">
        <ts-button type="primary" @click="handleSubmit">{{ title }}</ts-button>
        <ts-button @click="handleCancelModal">取消</ts-button>
      </div>
    </ts-dialog>
  </div>
</template>

<script>
import allTable from './components/all-table.vue';
import disputeBaseInformForm from '@/components/dispute-base-inform-form/index.vue';
import indexJs from './index.js';
import infinityScroll from '@/unit/infinityScroll';
import baseSelect from '@/components/base-select/index.vue';
import uploadDetail from './components/upload-detail.vue';
import logDetail from './components/log-detial.vue';
import { resolventList } from '@/assets/js/constants';

export default {
  mixins: [indexJs, infinityScroll],
  components: {
    allTable,
    disputeBaseInformForm,
    baseSelect,
    uploadDetail,
    logDetail
  },
  data() {
    return {
      resolventList,
      activeTable: 'allTable',
      tabList: [
        {
          label: '全部',
          name: 'allTable'
        },
        {
          label: '待处理',
          name: 'unHandledTable',
          count: 0
        },
        {
          label: '已处理',
          name: 'handledTable',
          count: 0
        }
      ],
      tableDatas: {},
      deptTreeData: []
    };
  },
  watch: {
    'editData.isSolved': function() {
      this.$nextTick(() => {
        this.$refs.basicScroll.update();
      });
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.handleTabClick();
    });
  },
  methods: {
    async handleTabClick() {
      this.$refs.allTable.refresh();
    },
    /**@desc 刷新编辑所需要用到的数据 */
    refreshEditDatas() {
      this.getDeptTreeData();
    },
    getDeptTreeData() {
      this.ajax.getDeptTree().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '科室数据获取失败');
          return;
        }
        this.deptTreeData = res.object;
      });
    },
    async getPeopleListData(data) {
      let res = await this.ajax.getEmployeeByPage({ ...data, pageSize: 15 });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs-content {
  /deep/ .el-tabs__content {
    flex: 1;
    overflow: hidden;
  }
  /deep/ .el-tab-pane {
    height: 100%;
    overflow: hidden;
  }
}
/deep/ .table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
/deep/ .form-table {
  flex: 1;
}
/deep/ .pagination-content {
  margin-top: $primary-spacing;
  text-align: right;
}
.content-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  display: flex;
  align-items: center;
  &::before {
    content: '';
    height: 18px;
    width: 4px;
    border-radius: 6px;
    background-color: $primary-blue;
    margin-right: 8px;
  }
}
/deep/ .vertical-form-item {
  display: flex;
  flex-direction: column;
  .el-form-item__label {
    text-align: left;
    width: 100% !important;
  }
  .el-form-item__content {
    margin-left: 0 !important;
  }
}
.choose-template {
  color: $primary-blue;
  cursor: pointer;
  &:active {
    color: $primary-blue-active;
  }
  &:hover {
    opacity: 0.8;
  }
}
.template-content {
  max-height: 200px;
  overflow: hidden;
}
.template-item {
  line-height: 30px;
  cursor: pointer;
  &:hover {
    background-color: $list-hover-color;
  }
}

/deep/.dispute-action-content {
  height: calc(100vh - 143px);
  overflow: hidden;
  .el-input {
    min-width: unset;
  }
}
.action-tab {
  flex: 1;
  overflow: hidden;
}
/deep/ .register-add-borad .el-tabs__header {
  height: 0;
  overflow: hidden;
}
.operation-form {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid $theme-border-color;
}
</style>
