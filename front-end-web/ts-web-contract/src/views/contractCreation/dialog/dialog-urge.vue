<template>
  <ts-dialog
    class="dialog-urge"
    title="流程退回"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <ts-form ref="form" :model="form" labelWidth="120px">
        <ts-form-item label="催办标题">
          <ts-input v-model="form.title" />
        </ts-form-item>
        <ts-form-item label="催办说明">
          <ts-input v-model="form.content" type="textarea" class="textarea" />
        </ts-form-item>
        <ts-form-item label="未办理人">
          <ts-input v-model="form.userNames" disabled />
        </ts-form-item>
        <ts-form-item label="短信提醒">
          <ts-switch v-model="form.isSMS" active-value="1" inactive-value="0" />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" @click="submit">提 交</ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import api from '@/api/ajax/api-creation.js';
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean
    },
    typeContractData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,

      form: {
        title: '',
        content: '',
        userCodes: '',
        userNames: '',
        isSMS: '0'
      }
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.$nextTick(this.$refs.form?.clearValidate());
          this.initFieldValue();
          this.init();
        }
        this.visible = val;
      }
    }
  },
  methods: {
    async init() {
      const definitionName = this.typeContractData.definitionName;
      const workflowName = this.typeContractData.workflowName;
      this.form.title = `催办: ${definitionName} ${workflowName} 等待您的办理!`;

      let params = {
        wfInstId: this.typeContractData.wfInstanceId,
        sidx: 'finished_date',
        sord: 'desc',
        pageNo: 1,
        pageSize: 100
      };

      const res = await api.workflowTaskHisList(params);
      let obj = (res.rows && res.rows[0]) || {};
      this.form.userCodes = obj.assigneeNo;
      this.form.userNames = obj.assigneeName;
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        let data = deepClone(this.form);

        data.wfInstId = this.typeContractData.wfInstanceId;
        data.wfDefId = this.typeContractData.wfDefinitionId;

        const res = await api.workflowWfInstPress(data);
        if (res.success && res.statusCode === 200) {
          this.$message.success('催办成功');
          this.close('success');
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    initFieldValue() {
      this.form.title = '';
      this.form.content = '';
      this.form.userCodes = '';
      this.form.userNames = '';
      this.form.isSMS = '';
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-urge {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
