/******错误处理****/
window.addEventListener(
  'error',
  (error) => {
    console.log('捕获到异常：', error);
  },
  true
);
window.onerror = handleError;

function handleError(message, source, lineno, colno, error) {
  $.closeloadings();
  console.log('捕获到异常：', {
    message,
    source,
    lineno,
    colno,
    error,
  });
  return true;
}
// 禁用刷新
document.onkeydown = function (e) {
  e = window.event || e;
  var k = e.keyCode;
  //屏蔽ctrl+R，F5键，ctrl+F5键  F3键！验证
  if (
    (e.ctrlKey == true && k == 82) ||
    k == 116 ||
    (e.ctrlKey == true && k == 116) ||
    k == 114
  ) {
    e.keyCode = 0;
    e.returnValue = false;
    e.cancelBubble = true;
    return false;
  }
  // if (k == 8) {
  //     alert('不能返回或后退！');
  //     e.keyCode = 0;
  //     e.returnValue = false;
  //     return false;
  // }
  //屏蔽 Ctrl+n   验证可以实现效果
  if (e.ctrlKey && k == 78) {
    e.keyCode = 0;
    e.returnValue = false;
    e.cancelBubble = true;
    return false;
  }
  //屏蔽F11   验证可以实现效果
  if (k == 122) {
    e.keyCode = 0;
    e.returnValue = false;
    e.cancelBubble = true;
    return false;
  }
  //屏蔽 shift+F10  验证可以实现效果
  if ((e.shiftKey && k == 121) || (e.ctrlKey && k == 121)) {
    e.keyCode = 0;
    e.returnValue = false;
    e.cancelBubble = true;
    return false;
  }

  //屏蔽Alt+F4
  if (e.altKey && k == 115) {
    window.showModelessDialog(
      'about:blank',
      '',
      'dialogWidth:1px; dialogheight:1px'
    );
    e.keyCode = 0;
    e.returnValue = false;
    e.cancelBubble = true;
    return false;
  }
  //屏蔽 Alt+ 方向键 ← ;屏蔽 Alt+ 方向键 → ！验证
  if (e.altKey && (k == 37 || k == 39)) {
    alert('不准你使用ALT+方向键前进或后退网页！');
    e.keyCode = 0;
    e.returnValue = false;
    e.cancelBubble = true;
    return false;
  }
};
//地址栏参数
function getParmas() {
  var url = window.location.href.split('?')[1];
  var arr = url ? url.split('&') : '';
  var data = {};
  for (var i = 0; i < arr.length; i++) {
    data[arr[i].split('=')[0]] = arr[i].split('=')[1];
  }
  return data;
}
//
window.openWiner = 1;
$(window).bind('beforeunload', function (e) {
  return;
});

function initNumCount(valLength, maxLength) {
  return $(
    `<div class="num_count">
      <span id="nowLength">${valLength}</span>/<span id="count">${maxLength}</span>
     </div>`
  );
}
var oldFileList = [];
var childFormCountList = [];
//屏蔽右键菜单，！验证
// document.oncontextmenu = function (event) {
//     if (window.event) {
//         event = window.event;
//     }
//     return false;
//     // try {
//     //     var the = event.srcElement;
//     //     if (!((the.tagName == 'INPUT' && the.type.toLowerCase() == 'text') || the.tagName == 'TEXTAREA')) {
//     //         return false;
//     //     }
//     //     return true;
//     // } catch (e) {
//     //     return false;
//     // }
// };
// 标签页关闭
// window.onbeforeunload = function (e) {
//     if (e.target.activeElement.attributes.id.value == 'cancleWin' || e.target.activeElement.nodeName == 'BODY') {
//         window.openWiner = undefined
//     }
//     return
// }
//下拉框
/**
 * @param
 * condition
 * pageNo
 * pageSize
 * @return
 * pageSize
 * pageNo
 * rows []
 *  {name:''}
 * total
 **/

function initSelect(option) {
  this.options = $.extend({}, this.baseCfg, option);
  this.$el = $(option.el);
  this.$el.prop('readonly', true);
  this.bind();
}
initSelect.prototype.baseCfg = {
  $el: '',
  url: '',
  type: '',
  param: {},
  prop: {
    value: 'name',
    key: 'id',
  },
  pageNo: 1,
  pageSize: 10,
  allData: [],
};
initSelect.prototype.bind = function () {
  this.$el.off('click').on(
    'click',
    function (e) {
      e.stopPropagation();
      this.options.pageNo = 1;
      this.allData = [];
      this.initBox();
    }.bind(this)
  );
};
initSelect.prototype.initBox = function () {
  $('#domSelectBox').remove();
  var _this = this;
  var _left = this.$el.offset().left;
  var _top = this.$el.offset().top;
  var allH = $(window).height();
  var height = this.$el.outerHeight();
  if (_top < allH - 200) {
    tb = `top: ${_top + height}px;`;
  } else {
    var _w = allH - _top;
    tb = `bottom: ${_w}px; top:auto;`;
  }
  var dataBox = `<div 
      id="domSelectBox" 
      style="left:${_left}px; ${tb}"
    >
      <input id="domSelect_condition" class="layui-input" />
      <ul id="domSelectList"></ul>
    </div>`;
  $('body').append(dataBox);
  this.ajax();
  $('#domSelect_condition').focus();
  $('#domSelectBox').on('click', function (e) {
    e.stopPropagation();
    return false;
  });
  $('#domSelect_condition')
    .off('input')
    .on(
      'input',
      debounce(function (e) {
        e.stopPropagation();
        _this.options.pageNo = 1;
        _this.allData = [];
        $('#domSelectList').html('');
        _this.ajax();
      }, 500)
    );
  $('#domSelectList').scroll(function () {
    var yScroll = $(this).scrollTop();
    var h = $(this)[0].scrollHeight;
    if (h - 165 <= yScroll) {
      _this.options.pageNo++;
      _this.ajax();
    }
  });
};
initSelect.prototype.ajax = function () {
  var _this = this;
  var data = {
    condition: $('#domSelect_condition').val(),
    pageNo: this.options.pageNo,
    pageSize: this.options.pageSize,
  };
  $.ajax({
    url: this.options.url,
    method: this.options.type,
    data: data,
    success: function (res) {
      var data = res.rows;
      _this.allData = _this.allData.concat(data);
      for (var i = 0; i < data.length; i++) {
        $('#domSelectList').append(
          bindData(_this, data[i], function (data) {
            _this.callback && _this.callback.call(_this.$el, data);
          })
        );
      }
    },
  });

  function bindData(domselect, data, callback) {
    var li = document.createElement('li');
    var label = data[domselect.options.prop.value];
    li.innerText = label;
    li.addEventListener(
      'click',
      function (e) {
        domselect.$el.val(label);
        callback(data);
        $('#domSelectBox').remove();
      }.bind(data)
    );
    return li;
  }
};

/**@desc 生成唯一标识符 */
function createUUID() {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  );
}
/***
 * role 角色 （self 本人） （deal 办理）（consult 办理查阅）
 * type 类型 （restart 重新发起） （confirm  办理） (see 查看) (cancle 强制结束) (countersign 加签)
 *** */
var userChooseData = {};
var deptChoose = {};
var processChooseData = {};
var taskFileTable = null;
var hasTask = false;
var itemMap = {};
/**@desc 子表单拖动列表 */
var childDragDom = {};
// TODO 事件处理
/*
    @ data
    {
        keyId:{
            $el,
            events:{
                change:[],
                blur:[]
            }
        }
    }
*/
var formItemEvent = {};

/**
 * @desc 计算公式事件处理 将方法追加到统一事件处理方法中
 * @param {object} $el jq Dom对象
 * @param {formulaSetting} json 计算公式配置
 */
function formItemCalculationRole($el, json) {
  var fs = bindBlurCal($el, json);
  upFormItemEvent(json.keyId, 'blur', fs.fun);
  if (fs.triggerField.length) {
    for (var i = 0; i < fs.triggerField.length; i++) {
      upFormItemEvent(fs.triggerField[i].keyId, 'input', fs.fun);
    }
  }
}

//添加节点事件
function upFormItemEvent(keyId, eventType, fn) {
  formItemEvent[keyId].events[eventType].push(fn);
}
//绑定表单事件  事件绑定触发
//  闭包可调用参数keyId对应的json
function formItemEventBind() {
  for (var key in formItemEvent) {
    if (formItemEvent[key].$el) {
      for (var event in formItemEvent[key].events) {
        var fns = formItemEvent[key].events[event];
        var json = itemMap[key];
        if (fns.length > 0) {
          (function ($el, json, fns) {
            $el.on(event, function () {
              for (var i = 0; i < fns.length; i++) {
                fns[i].call(this);
              }
            });
          })(formItemEvent[key].$el, json, fns);
        }
      }
    }
  }
}
//props回调触发事件处理
/* 
* events:[{
    type:'',//事件类型 change 
    trigger:'',//触发判断 等于 不等于  大于 小于
    target:'',//触发值
    touch:[{
        target:'',//触发后修改目标
        prop:''//修改属性  只读/必填/可编辑/不必填
    }]
}]
*/
function formItemCallBackEvent() {
  for (var key in itemMap) {
    if (itemMap[key].events && itemMap[key].events.length) {
      var es = itemMap[key].events;
      for (var e = 0; e < es.length; e++) {
        var funs = formItemCallBackFun(itemMap[key], es[e]);
        upFormItemEvent(key, es[e].type, funs);
      }
    }
  }
}

function formItemCallBackFun(json, es) {
  //记录原始props
  var backProps = {};
  for (var i = 0; i < es.touch.length; i++) {
    var touch = es.touch[i];
    // backProps[touch.target] ?? (backProps[touch.target] = {});
    backProps[touch.target] = backProps[touch.target] || {};

    backProps[touch.target].readonly = itemMap[touch.target].isReadonly;
    backProps[touch.target].must = itemMap[touch.target].isMust;
  }

  function backProp() {
    //触发条件前还原所有props
    for (var i = 0; i < es.touch.length; i++) {
      var touch = es.touch[i];
      itemMap[touch.target].isReadonly = backProps[touch.target].readonly;
      itemMap[touch.target].isMust = backProps[touch.target].must;
      formItemEvent[touch.target].$el &&
        formItemPropsSet(
          formItemEvent[touch.target].$el.closest('.formItem'),
          itemMap[touch.target]
        );
    }
  }

  function changeProp(touch) {
    if (touch.prop == 'required') {
      itemMap[touch.target].isMust = true;
    } else if (touch.prop == 'noRequired') {
      itemMap[touch.target].isMust = false;
    } else if (touch.prop == 'readonly') {
      itemMap[touch.target].isReadonly = true;
    } else if (touch.prop == 'edit') {
      itemMap[touch.target].isReadonly = false;
    }
    formItemEvent[touch.target].$el &&
      formItemPropsSet(
        formItemEvent[touch.target].$el.closest('.formItem'),
        itemMap[touch.target]
      );
  }
  return function () {
    switch (es.trigger) {
      case '=':
        if ($(this).val() == es.target) {
          for (var i = 0; i < es.touch.length; i++) {
            var touch = es.touch[i];
            changeProp(touch);
          }
        } else {
          backProp();
        }
        break;
      case '!=':
        if ($(this).val() != es.target) {
          for (var i = 0; i < es.touch.length; i++) {
            var touch = es.touch[i];
            changeProp(touch);
          }
        } else {
          backProp();
        }
        break;
      case '>':
        if ($(this).val() > es.target) {
          for (var i = 0; i < es.touch.length; i++) {
            var touch = es.touch[i];
            changeProp(touch);
          }
        } else {
          backProp();
        }
        break;
      case '<':
        if ($(this).val() < es.target) {
          for (var i = 0; i < es.touch.length; i++) {
            var touch = es.touch[i];
            changeProp(touch);
          }
        } else {
          backProp();
        }
        break;
    }
  };
}

$('#editConfirm').on('click', function () {
  if (isSubmit) {
    return false;
  }
  isSubmit = true;
  var data = dealCustomFormData('submit');
  if (!data) {
    isSubmit = false;
    return false;
  }
  var allData = {
    wfDefinitionId: params.baseData.wfDefinitionId,
    workflowNo: params.baseData.workflowNo,
    dataMap: data.formData,
    tableName: params.formData.tableName,
  };
  let textArea = $('.taskHis');
  let commentList = [];
  for (var i = 0; i < textArea.length; i++) {
    let obj = {
      taskHisId: $(textArea[i]).attr('taskHisId'),
      remark: $(textArea[i]).val(),
    };
    commentList.push(obj);
  }
  allData.commentList = commentList;
  let newFileList = [];
  if (textArea.length) {
    for (var i = 0; i < textArea.length; i++) {
      let taskHisId = $(textArea[i]).attr('taskHisId');
      var files =
        fileuploadCopy.file[taskHisId] &&
        fileuploadCopy.file[taskHisId].fileList;
      for (var j = 0; j < files.length; j++) {
        let obj = {
          fileUrl: files[j].filePath,
          fileName: files[j].fileName,
          fileSize: files[j].fileSize,
          originalName: files[j].originalName,
          fileId: files[j].id,
          taskHisId: taskHisId,
          isDelete: 1,
        };
        newFileList.push(obj);
      }
    }
  }
  allData.commentFileList = [...oldFileList, ...newFileList];
  data.childTableData && (allData.childTableData = data.childTableData);
  $.ajax({
    method: 'post',
    contentType: 'application/json; charset=utf-8',
    url: '/ts-form/form/api/update',
    data: JSON.stringify(allData),
    success: function (res) {
      isSubmit = false;
      if (res.success) {
        layer.msg('修改成功');
        window.location.reload();
      } else {
        layer.msg(res.message || '保存失败');
      }
    },
    error: function (e) {
      isSubmit = false;
    },
  });
});

//关闭窗口
$('#cancleWin').on('click', function () {
  window.openWiner = undefined;
  closeWindow();
});

function closeWindow(params) {
  if (
    common.getBrowserInfo.kernel == 'chrome' &&
    parseFloat(common.getBrowserInfo.version) <= '54'
  ) {
    trasenBrowser.ClosePage();
  } else {
    window.open('about:blank', '_self').close();
  }
}
var hasTask = false;
//切换
$('.nav-item')
  .off('click')
  .on('click', function () {
    $(this).addClass('active').siblings().removeClass('active');
    var index = $(this).index();
    $('#content #inner_content').addClass('none').eq(index).removeClass('none');

    if (index == 2) {
      getTaskFileList();
    }
    if (index == 1) {
      if (hasTask) {
        return;
      }
      hasTask = true;
      //历史流程信息
      taskShow();
      //工作流显示
      wfShow();
    }
  });
//抄送人
$('body')
  .off('click', '.copyUser')
  .on('click', '.copyUser', function () {
    var data = {
      isCheckDept: 'N',
      user_str: 'wfCopyUser',
      user_id: 'workFlowCopyUserCode',
      user_code: 'workFlowCopyUserCode',
      user_datas: copyUsers,
    };
    $.quoteFun('/common/userSel', {
      trasen: trasenTable,
      title: '抄送人选择',
      data: data,
      callback: function (
        names,
        idsArr,
        codesArr,
        deptNamesArr,
        deptUserNamesArr,
        userNamePhonesArr,
        userDatasArr
      ) {
        copyUsers = userDatasArr;
      },
    });
  });

function getTextLength(arr) {
  var textArr = [];
  for (var i = 0; i < arr.length; i++) {
    textArr.push(arr[i].username);
  }
  var text = textArr.join('、');
  return text;
}
//人员选择
$('body').on('click', '.userChoose', function () {
  var keyId = $(this).attr('keyid');
  var deptCheck = $(this).attr('deptCheck');
  var arr = userChooseData[keyId] || [];
  var userList = [];
  var deptList = [];
  for (var i = 0; i < arr.length; i++) {
    if (!arr[i].type || arr[i].type == 2) {
      userList.push(arr[i]);
    } else if (arr[i].type == 1) {
      deptList.push({
        id: arr[i].code,
        name: arr[i].name
      });
    }
  }
  var that = this;
  var data = {
    user: true,
    userList: userList,
    dept: true,
    deptCheck: !!deptCheck,
    deptList: deptList,
  };
  $.quoteFun('/commonPage/userDeptGroup/index', {
    data: data,
    callBack: function (selDept, selUser, selGroup) {
      var arr = [];
      var nameArr = [];
      var codeArr = [];
      for (var i = 0; i < selDept.length; i++) {
        var item = selDept[i];
        item.type = '1';
        arr.push(item);
        nameArr.push(selDept[i].name);
        codeArr.push(selDept[i].code);
      }
      for (var i = 0; i < selUser.length; i++) {
        var item = selUser[i];
        item.type = '2';
        arr.push(item);
        nameArr.push(selUser[i].name);
        codeArr.push(selUser[i].code);
      }
      userChooseData[keyId] = arr;
      $(that).val(nameArr.join(','));
      $(that).trigger('change.changeCallback', userChooseData);
    },
  });
});

//流程选择
$('body').on('click', '.process-choose .process-choose-btn', function () {
  var keyId = $(this).closest('.process-choose').attr('keyid');
  var json = itemMap[keyId];
  var processArr = processChooseData[keyId] || [];
  var listBox = $(this).closest('.process-choose').find('.process-list-box');
  $.quoteFun('../view-new/processView/modules/process/index', {
    data: {
      processList: processArr,
      wfDefinitionIds: json.relationWorkflowId,
    },
    callBack: function (selArr) {
      var processElem = null;
      if (selArr.length) {
        processElem = $('<ul class="process-list"></ul>');
        for (var i = 0; i < selArr.length; i++) {
          var liElem = $(
            `<li 
                class="process-item" 
                row-id="${selArr[i].wfInstanceId}"
              >
                <span>
                  ${selArr[i].workflowName} - ${selArr[i].createDate}
                </span>
              </li>`
          );
          processElem.append(liElem);
        }
      } else {
        processElem = $(
          '<input class="layui-input process-choose-btn" readonly autocomplete="off" type="text">'
        );
      }
      $(listBox).html(processElem);
      processChooseData[keyId] = selArr;
    },
  });
});

$('body').on('click', '.process-choose .process-item span', function () {
  var rowId = $(this).closest('.process-item').attr('row-id');
  var keyId = $(this).closest('.process-choose').attr('keyid');
  var processArr = processChooseData[keyId] || [];
  var rowData = processArr.find((i) => {
    return i.wfInstanceId == rowId;
  });
  checkDetail(rowData);
});

function checkDetail(rowData) {
  $.ajax({
    method: 'get',
    url: '/ts-workflow/workflow/definition/code/' + rowData.workflowNo,
    success: function (res) {
      if (res.success) {
        if (res.object && res.object.isNormal == 'N') {
          var isHideContent =
            res.object.isHideContent == 1 &&
            rowData.createUser == common.userInfo.usercode
              ? 1
              : 0;
          var role =
            rowData.createUser == common.userInfo.usercode ? 'self' : 'deal';
          var son = common.processDeal.onlySee(rowData, isHideContent, role);
          common.openedWindow.push(son);
        } else if (res.object && res.object.isNormal == 'Y') {
          if (res.object.examinePageUrl.startsWith('/ts-web')) {
            handleNewFrameJump(2, rowData, res);
          } else {
            var opt = {
              data: {
                details: 1,
                id: rowData.businessId,
                taskId: rowData.taskId,
                workId: rowData.wfInstanceId,
                workflowNumber: rowData.workflowNumber,
                currentStepName: rowData.currentStepName,
                currentStepNo: rowData.currentStepNo,
                wfInstanceId: rowData.wfInstanceId,
                status: rowData.status,
                stepNo: rowData.currentStepNo,
              },
              rowData: rowData,
              trasen: trasenTable,
              title: '查看详情',
              ref: initTable,
            };
            $.quoteFun(res.object.examinePageUrl, opt);
          }
        } else {
          layer.msg(res.message || '获取流程信息失败');
        }
      } else {
        layer.msg(res.message || '失败');
      }
    },
  });
}

function handleNewFrameJump(type, data, res) {
  window.dispatchEvent(
    new CustomEvent('sendToNewFrameMessage', {
      detail: {
        type: 'broadcastInformation',
        data: {
          event: 'messageToastEvent',
          data: {
            path: res.object.examinePageUrl,
            businessId: data.businessId,
            data,
            type,
            res,
          },
        },
      },
    })
  );
}

//数组对象查找
function findIndex(arr, value, key) {
  var index = -1;
  for (var i = 0; i < arr.length; i++) {
    if (arr[i][key] == value) {
      index = i;
    }
  }
  return index;
}
/*****************表单组件部分限制********************/
//数字输入框   整数   负数  浮点数
$('body').on('keyup', 'input[inp-type="number"]', function (e) {
  var list = /(-)?(\d+)?(\.)?(\d+)?/.exec(this.value);
  if (list) {
    this.value = list[0];
  }
});

//文件上传
//删除
$('body').on('click', 'span.fileDel', function () {
  var fileId = $($(this).parents('li')[0]).attr('file-id');
  var keyId = $($(this).parents('li')[0]).attr('keyId');
  var list = fileupload.file[keyId].fileList;
  var index = -1;
  for (var i = 0; i < list.length; i++) {
    if (list[i].id == fileId) {
      index = i;
    }
  }
  if (index != -1) {
    fileupload.file[keyId].fileList.splice(index, 1);
  }
  fileupload.setFileData(keyId);
  fileupload.setFileStr(keyId);
});

$('body').on('click', 'span.fileDelNew', function () {
  var fileId = $($(this).parents('li')[0]).attr('file-id');
  var keyId = $($(this).parents('li')[0]).attr('keyid');
  var list = fileuploadCopy.file[keyId].fileList;
  var index = -1;
  for (var i = 0; i < list.length; i++) {
    if (list[i].id == fileId) {
      index = i;
    }
  }
  if (index != -1) {
    fileuploadCopy.file[keyId].fileList.splice(index, 1);
  }
  fileuploadCopy.setFileData(keyId);
  fileuploadCopy.setFileStr(keyId);
});
//  下载
/*$('body').on('click', 'a.fileDown', function () {
    var fileId = $($(this).parents('li')[0]).attr('file-id')
    var keyId = $($(this).parents('li')[0]).attr('keyId')
   // window.open('/ts-document/attachment/downloadFile/' + fileId,'_blank')
    window.location.href = '/ts-document/attachment/downloadFile/' + fileId;
})*/
$('body').on('click', '.AllFileDown', function () {
  var fileList = $(this).siblings('.layui-upload').find('a');
  let ids = [];

  $(fileList).each((index, item) => {
    let href = $(item).attr('href');
    let paramsArr = href.split('/');

    ids.push(paramsArr[paramsArr.length - 1]);
  });
  let url = `${
    common.url
  }/ts-document/attachment/batchDownloadByIds?ids=${ids.join(',')}`;
  var alink = document.createElement('a');
  alink.href = url;
  alink.click();
  // for (var i = 0; i < fileList.length; i++) {
  //     (function (el, n) {
  //         setTimeout(function () {
  //             el.click();
  //         }, n * 500);
  //     })(fileList[i], i);
  // }
  return;
});
var fileuploadCopy = {
  file: {},
  init: function (dom, module, fileStr) {
    var keyId = dom.attr('taskhisid');
    this.file[keyId] = {
      FileList: dom.siblings('.layui-upload').find('.FileList'),
      fileList: [],
      fileStr: fileStr || '',
      inp: dom.siblings(`input[tasHisId="${keyId}"]`),
      number: dom.attr('file-number') || 999,
      isReadOnly: dom.attr('isReadonly') == 1,
    };
    if (dom.attr('isReadonly') == 1) {
      dom.css('display', 'none');
    }
    if (fileStr) {
      this.initFile(keyId);
    }
    var that = this;
    var uploadListIns = layUpload.render({
      elem: dom,
      url: '/ts-document/attachment/fileUpload?module=' + (module || 'form'),
      accept: 'file',
      exts: dom.attr('file-exts') || '',
      multiple: !dom.attr('file-number') || dom.attr('file-number') > 1,
      auto: true,
      choose: function (obj) {
        this.files = null;
        this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
      },
      before: function (obj) {
        if (
          Object.keys(this.files).length + that.file[keyId].fileList.length >
          that.file[keyId].number
        ) {
          uploadListIns.config.elem.next()[0].value = '';
          layer.msg('文件上传数量超出限制');
          // layer.stopPropagation()
        }
      },
      done: function (res, index, upload) {
        uploadListIns.config.elem.next()[0].value = '';
        if (res.success == true) {
          //上传成功
          res.object[0].index = index;
          if (that.file[keyId].fileList.length < that.file[keyId].number) {
            that.file[keyId].fileList.push({
              id: res.object[0].fileId,
              originalName: res.object[0].fileName,
              fileName: res.object[0].fileRealName,
              filePath: res.object[0].filePath,
              fileSize: res.object[0].fileSize,
            });
            that.setFileStr(keyId);
            that.setFileData(keyId);
          }
          return delete this.files[index]; //删除文件队列已经上传成功的文件
        } else {
          layer.msg(res.message || '上传失败');
        }
        this.error(index, upload);
      },
      error: function (index, upload) {
        delete this.files[index]
      },
    });
  },
  setFileStr: function (keyId) {
    var list = this.file[keyId].fileList;
    var fileStrArr = [];
    for (var i = 0; i < list.length; i++) {
      fileStrArr.push(list[i].id);
    }
    this.file[keyId].fileStr = fileStrArr.join(',');
    this.file[keyId].inp.val(fileStrArr.join(','));
  },
  setFileData: function (keyId) {
    var list = this.file[keyId].fileList;
    var html = '';
    for (var i = 0; i < list.length; i++) {
      html += `<li 
          class="file-item" 
          file-id="${list[i].id}" 
          keyId="${keyId}"
        >
          <a 
            class="fileDown"
            href="/ts-document/attachment/downloadFile/${list[i].id}"
          >
            ${list[i].originalName}
          </a>`;
      if (common.isImg(list[i].originalName)) {
        html += `<span 
            class="viewerImg" 
            style="margin:0 5px; cursor: pointer;" 
            fileurl="${list[i].id}" 
            filename="${list[i].fileName}"
          >预览</span>`;
      }
      if (common.isDoc(list[i].originalName)) {
        html += `<span 
            class="viewerDoc2" 
            filename="${list[i].fileName}" 
            style="margin:0 5px; cursor: pointer;" 
            fileid="${list[i].id}"
          >预览</span>`;
      }
      if (this.file[keyId].isReadOnly) {
        html += '<span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span>';
        html += '</li>';
      } else {
        html += '<span class="fileDelNew">删除</span><span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span></li>';
      }
    }
    if (list.length <= 1) {
      this.file[keyId].FileList.closest('.layui-upload')
        .siblings('.AllFileDown')
        .addClass('none');
    } else {
      this.file[keyId].FileList.closest('.layui-upload')
        .siblings('.AllFileDown')
        .removeClass('none');
    }
    this.file[keyId].FileList.html(html);
  },
  initFile: function (keyId) {
    var fileStr = this.file[keyId].fileStr;
    var that = this;
    var res = getFileByIds(fileStr);
    if (res && res.success) {
      that.file[keyId].fileList = res.object;
      that.setFileData(keyId);
    } else {
      layer.msg(res.message || '上传失败');
    }
  },
};
var fileupload = {
  file: {},
  init: function (dom, module, fileStr) {
    var keyId = dom.attr('keyId');
    this.file[keyId] = {
      FileList: dom.siblings('.layui-upload').find('.FileList'),
      fileList: [],
      fileStr: fileStr || '',
      inp: dom.siblings(`input[keyId="${keyId}"]`),
      number: dom.attr('file-number') || 999,
      isReadOnly: dom.attr('isReadonly') == 1,
    };

    if (dom.attr('isReadonly') == 1) {
      dom.css('display', 'none');
    }
    if (fileStr) {
      this.initFile(keyId);
    }
    var that = this;
    var uploadListIns = layUpload.render({
      elem: dom,
      url: '/ts-document/attachment/fileUpload?module=' + (module || 'form'),
      accept: 'file',
      exts: dom.attr('file-exts') || '',
      multiple: !dom.attr('file-number') || dom.attr('file-number') > 1,
      auto: true,
      choose: function (obj) {
        this.files = null;
        this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
      },
      before: function (obj) {
        if (
          Object.keys(this.files).length + that.file[keyId].fileList.length >
          that.file[keyId].number
        ) {
          uploadListIns.config.elem.next()[0].value = '';
          layer.msg('文件上传数量超出限制');
          // layer.stopPropagation()
        }
      },
      done: function (res, index, upload) {
        uploadListIns.config.elem.next()[0].value = '';
        if (res.success == true) {
          //上传成功
          res.object[0].index = index;
          if (that.file[keyId].fileList.length < that.file[keyId].number) {
            that.file[keyId].fileList.push({
              id: res.object[0].fileId,
              originalName: res.object[0].fileName,
              fileName: res.object[0].fileRealName,
              filePath: res.object[0].filePath,
              fileSize: res.object[0].fileSize,
            });
            that.setFileStr(keyId);
            that.setFileData(keyId);
          }
          return delete this.files[index]; //删除文件队列已经上传成功的文件
        } else {
          layer.msg(res.message || '上传失败');
        }
        this.error(index, upload);
      },
      error: function (index, upload) {
        delete this.files[index]
      },
    });
  },
  setFileStr: function (keyId) {
    var list = this.file[keyId].fileList;
    var fileStrArr = [];
    for (var i = 0; i < list.length; i++) {
      fileStrArr.push(list[i].id);
    }
    this.file[keyId].fileStr = fileStrArr.join(',');
    this.file[keyId].inp.val(fileStrArr.join(','));
  },
  setFileData: function (keyId) {
    var list = this.file[keyId].fileList;
    var html = '';
    for (var i = 0; i < list.length; i++) {
      html += `<li 
          class="file-item" 
          file-id="${list[i].id}" 
          keyId="${keyId}"
        >
          <a 
            class="fileDown"
            href="/ts-document/attachment/downloadFile/${list[i].id}"
          >
            ${list[i].originalName}
          </a>`;
      if (common.isImg(list[i].originalName)) {
        html += `<span 
            class="viewerImg" 
            style="margin:0 5px; cursor: pointer;" 
            fileurl="${list[i].id}" 
            filename="${list[i].fileName}"
          >预览</span>`;
      }
      if (common.isDoc(list[i].originalName)) {
        html += `<span 
            class="viewerDoc2" 
            filename="${list[i].fileName}" 
            style="margin:0 5px; cursor: pointer;" 
            fileid="${list[i].id}"
          >预览</span>`;
      }
      if (this.file[keyId].isReadOnly) {
        html += '<span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span>';
        html += '</li>';
      } else {
        html += '<span class="fileDel">删除</span><span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span></li>';
      }
    }
    if (list.length <= 1) {
      this.file[keyId].FileList.closest('.layui-upload')
        .siblings('.AllFileDown')
        .addClass('none');
    } else {
      this.file[keyId].FileList.closest('.layui-upload')
        .siblings('.AllFileDown')
        .removeClass('none');
    }
    this.file[keyId].FileList.html(html);
  },
  initFile: function (keyId) {
    var fileStr = this.file[keyId].fileStr;
    var that = this;
    var res = getFileByIds(fileStr);
    if (res && res.success) {
      that.file[keyId].fileList = res.object;
      that.setFileData(keyId);
    } else {
      layer.msg(res.message || '上传失败');
    }
  },
};

/**
 * @typedef file
 * @property {string} id 唯一标识
 * @property {string} fileName 文件名
 * @property {string} fileUrl 文件下载路径
 * @property {string} originalName 文件原来的名称
 */
/**
 * @typedef fileUploadSetting
 * @property {boolean} [readonly] 新增按钮插槽
 * @property {string | JQueryDom | function} [addBtnSlot] 新增按钮插槽
 * @property {function(newVal, oldVal)} [onChange] businessId 改变回调
 * @property {function} [onDelete] 删除回调
 * @property {string | string[]} [actions] 操作按钮列表
 * @property {function} [initUpload] 自定义初始化上传事件
 * @property {object} [uploadSetting] 上传参数
 * @property {number} [maxNumber] 最大上传数量
 */
/**
 * @typedef newFileUpload
 * @property {string} _uuid 唯一标识，用来获取相关的文件列表和上传按钮
 * @property {JQueryDom} $el 挂载元素，businessId 将会赋值给该元素
 * @property {file[]} fileList 已上传文件列表
 * @property {fileUploadSetting} setting 初始化设置
 */
/**
 * @typedef newFileUpload
 * @class 使用 businessId 作为唯一标识的上传方式
 * @param {*} el
 * @param {fileUploadSetting} setting
 * @returns
 */
function newFileUpload(el, setting = {}, autoInit = true) {
  // this.$el 赋值
  const elType = Object.prototype.toString.call(el);
  if (elType == '[object String]') {
    this.$el = $(el);
  } else if (elType == '[object Object]') {
    this.$el = el;
  } else {
    console.error('$el is a String or JQuery Dom not a ' + elType.slice(8, -1));
    return;
  }
  let uploadKey = createUUID(),
    businessId = this.$el.val() || uploadKey,
    { readonly, addBtnSlot, onChange } = setting;

  this._uuid = uploadKey; // 该上传组件的唯一标识id
  this.$el[0]._uuid = uploadKey;
  /**
   * @desc 已上传附件列表
   * @type file[]
   */
  this.fileList = [];
  this.setting = Object.assign(
    {
      actions: ['preview', 'delete', 'connection'],
      // actions: ['preview', 'delete'],
    },
    setting
  );
  if (this.setting.readonly && this.setting.actions.includes('delete')) {
    this.setting.actions = this.setting.actions.filter(
      (action) => action != 'delete'
    );
  }

  // 监听 businessId 的修改，刷新页面
  Object.defineProperty(this, 'businessId', {
    enumerable: true,
    configurable: true,
    get() {
      return businessId;
    },
    set(newVal) {
      if (newVal == businessId) {
        return;
      }
      let oldVal = businessId;
      businessId = newVal;
      this.$el.val(newVal);
      // 子表单多文件上传赋值有问题 未知影响其他业务
      // this.getUploadedFiles();
      onChange &&
        onChange instanceof Function &&
        onChange.call(this, newVal, oldVal);
    },
  });

  // 初始化附件
  this.businessId && this.getUploadedFiles();
  if (readonly) {
    return;
  }
  try {
    let addBtnNode = '';
    if (addBtnSlot) {
      let addSlotType = typeof addBtnSlot;
      switch (addSlotType) {
        case 'object':
        case 'string':
          addBtnNode = addBtnSlot;
          break;
        case 'function':
          addBtnNode = addBtnSlot();
          break;
      }
    } else {
      addBtnNode = $(
        `<button 
          class="layui-btn layui-btn-normal" 
          upload-key="${this._uuid}"
        >上传附件</button>`
      );
    }
    this.$el.after(addBtnNode);
    autoInit && this.initUpload();
  } catch (error) {
    console.error(error);
  }
}
/**@desc 绑定上传事件 */
newFileUpload.prototype.initUpload = function () {
  if (this.setting.initUpload) {
    try {
      this.setting.initUpload.call(this);
    } catch (e) {
      console.error(e);
    }
    return;
  }
  let dom = this.$el.siblings(`button[upload-key="${this._uuid}"]`),
    businessId = this.businessId || createUUID(),
    _this = this,
    uploadSetting = Object.assign(
      {
        elem: dom,
        url: `/ts-basics-bottom/fileAttachment/upload?businessId=${businessId}&moduleName=form`,
        accept: 'file',
        exts: '',
        multiple: true,
        auto: true,
        choose: function (obj) {
          this.files = null;
          this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
        },
        before: function (obj) {
          let maxNum = this.number || _this.setting.maxNumber;

          if (
            maxNum &&
            Object.keys(this.files).length + _this.fileList.length > maxNum
          ) {
            uploadListIns.config.elem.next()[0].value = '';
            let deleteIndex = maxNum - _this.fileList.length;
            // 删除多余的文件
            Object.keys(this.files)
              .slice(deleteIndex)
              .map((key) => {
                delete this.files[key];
              });
            layer.msg('文件上传数量超出限制');
          }
        },
        done: function (res, index, upload) {
          uploadListIns.config.elem.next()[0].value = '';
          if (res.success == true) {
            let maxNum = this.number || _this.setting.maxNumber;
            //上传成功
            res.object[0].index = index;
            if (!this.businessId) {
              _this.businessId = businessId;
            }
            if (!maxNum || _this.fileList.length < maxNum) {
              _this.fileList.push({
                id: res.object[0].fileId,
                originalName: res.object[0].fileRealName,
                fileName: res.object[0].fileRealName,
                fileRealName: res.object[0].fileName,
                fileUrl: res.object[0].filePath,
                fileSize: res.object[0].fileSize,
              });
              _this.renderFileList();
            }
            return delete this.files[index]; //删除文件队列已经上传成功的文件
          } else {
            layer.msg(res.message || '上传失败');
          }
          this.error(index, upload);
        },
        error: function (index, upload) {
          delete this.files[index];
        },
      },
      this.uploadSetting
    );

  var uploadListIns = layUpload.render(uploadSetting);
};
/**@desc 获取历史文件 */
newFileUpload.prototype.getUploadedFiles = function () {
  this.fileList = [];
  if (!this.businessId) {
    this.renderFileList();
    return;
  }
  $.ajax({
    url:
      '/ts-basics-bottom/fileAttachment/getFileAttachmentByBusinessId?businessId=' +
      this.businessId,
    type: 'get',
    success: (res) => {
      if (res.success == false) {
        layer.msg(res.message || '附件获取失败');
        return;
      }
      this.fileList = res.object.map((file) => {
        let { id, originalName, fileName, realPath, fileSize } = file;
        return {
          id,
          originalName,
          fileName,
          fileUrl: realPath,
          fileSize,
        };
      });
      if (!this.fileList.length) {
        this.businessId = null;
      }
      this.renderFileList();
    },
  });
};
/**
 * @desc 渲染文件列表
 * @todo 尝试减少重排重绘
 */
newFileUpload.prototype.renderFileList = function () {
  let fileListContainer = this.$el.siblings(`ul[upload-key="${this._uuid}"]`);
  fileListContainer.empty();
  if (!fileListContainer.length) {
    fileListContainer = $(
      `<ul upload-key="${this._uuid}" class="FileList"></ul>`
    );
    let insertDom = this.setting.readonly
      ? this.$el
      : this.$el.siblings(`button[upload-key="${this._uuid}"]`);
    insertDom.after(fileListContainer);
  }
  this.fileList.map((file, index) => {
    let fileDom = $(
      `<li class="file-item">
        <a class="fileDown" href="${file.fileUrl}">
          ${file.originalName}
        </a>
      </li>`
    );
    if (this.setting.actions.includes('preview')) {
      let previewDom = $(
        '<span class="operation-btn" style="margin:0 5px; cursor: pointer;">预览</span>'
      );
      previewDom.on('click', () => {
        if (common.isDoc(file.fileName)) {
          common.viewerDocBase(file.fileUrl, file.fileName);
        } else {
          common.viewerImgBase(this.fileList, file.fileUrl);
        }
      });
      fileDom.append(previewDom);
    }
    if (this.setting.actions.includes('delete')) {
      let deleteDom = $(
        '<span class="operation-btn" style="color: red; cursor: pointer;">删除</span>'
      );
      deleteDom.on('click', () => {
        layer.confirm('是否确认删除？', (layerIndex) => {
          layer.close(layerIndex);
          if (this.setting.onDelete) {
            this.setting.onDelete.call(this, file, fileDom);
            if (this.fileList.length == 0) {
              this.businessId = null;
            }
            return;
          }
          $.ajax({
            url: `/ts-basics-bottom/fileAttachment/deleteFileId?fileid=${file.id}`,
            type: 'get',
            success: (res) => {
              if (res.success) {
                fileDom.remove();
                layer.msg('删除成功');
                if (this.fileList.length == 1) {
                  this.fileList = [];
                } else {
                  this.fileList.splice(index, 1);
                }
                if (this.fileList.length == 0) {
                  this.businessId = null;
                }
              }
            },
          });
        });
      });
      fileDom.append(deleteDom);
    }
    if (this.setting.actions.includes('connection')) {
      let connectionDom = $(
        '<span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;">收藏</span>'
      );
      connectionDom.on('click', () => {
        $.ajax({
          url: '/ts-oa/attachment/saveCollect',
          method: 'post',
          contentType: 'application/json',
          data: JSON.stringify({
            collectId: file.id,
          }),
          success: function (res) {
            if (res.success) {
             layer.msg("收藏成功,已收藏到个人文档");
            } else {
              layer.msg(res.message);
            }
          },
        });
      });
      fileDom.append(connectionDom);
    }
    fileListContainer.append(fileDom);
  });
};

function getFileByIds(fileIds) {
  var res;
  $.ajax({
    type: 'get',
    url: '/ts-document/attachment/selectByIds',
    data: {
      idsStr: fileIds,
    },
    async: false,
    success: function (e) {
      res = e;
    },
  });
  return res;
}

//流水号
var lshObj = [];

//TODO公共操作
$('body').on('click', '.comment_box .officaldiction ul li', function (e) {
  $('.comment_box .officaldiction ul').show();
  e.stopPropagation();
});
$('body').on('click', function () {
  $('.comment_box .officaldiction ul').hide();
});
$('body').on('click', '.viewerDoc', function (e) {
  e.stopPropagation();
  var id = $(this).attr('fileid');
  common.viewerDoc(id);
  return false;
});
$('body').on('click', '.viewerDoc2', function (e) {
  e.stopPropagation();
  var id = $(this).attr('fileid');
  var filename = $(this).attr('filename');
  common.viewerDoc2(id, filename);
  return false;
});
$('body').on('click', '.viewerImg', function (e) {
  e.stopPropagation();
  let liDom = $(this).closest('li.file-item'),
    fileKey = liDom.attr('keyId'),
    fileList = (fileupload.file[fileKey] || {}).fileList || [],
    imgList = fileList
      .filter((item) => {
        return common.isImg(item.originalName);
      })
      .map((item) => {
        return {
          fileUrl: item.id,
          fileName: item.fileName,
        };
      });
  if (!imgList.length) {
    imgList = [
      {
        fileUrl: $(this).attr('fileurl'),
        fileName: $(this).attr('filename'),
      },
    ];
  }
  common.viewerImg(imgList, $(this).attr('fileurl'));
  return false;
});
$('body').on('click', '.comment_box .officaldiction span', function (e) {
  e.stopPropagation();
  $('.comment_box .officaldiction ul').hide();
  $(this).next().show();
});
$('body').on('click', '.comment_box .officaldiction ul li', function () {
  var text = $(this).text();
  var commentText = $(this).closest('.comment_box').find('textarea').val();
  $(this)
    .closest('.comment_box')
    .find('textarea')
    .val(commentText + (commentText ? '，' + text : text));
  // for (var i = 0; i < officaldictionValue.length; i++) {
  //   if (officaldictionValue[i] == this.innerHTML) {
  //     officaldictionValue.splice(i, 1);
  //     $(this)
  //       .closest(".comment_box")
  //       .find("textarea")
  //       .val(officaldictionValue.join(","));
  //     return;
  //   }
  // }
  // officaldictionValue.push(text);
  // $(this)
  //   .closest(".comment_box")
  //   .find("textarea")
  //   .val(officaldictionValue.join(","));
});

/*****************表单部分********************/
//互通组件数据
var interworkComData = {};
var interworkComMap = {
  his: {
    callStack: [
      {
        url: '/ts-external/emrApi/getInpatientInfoById',
        method: 'post',
        multiselect: false,
      },
      {
        url: '/ts-external/emrApi/getMedicalRecord',
        method: 'post',
        multiselect: true,
        queryFun: function (arr) {
          var ids = [];
          for (var i = 0; i < arr.length; i++) {
            ids.push(arr[i].id);
          }
          return {
            id: ids.join(','),
          };
        },
      },
    ],
    index: 0,
  },
};

function interworkCallBack() {
  for (var key in interworkComData) {
    var arr = interworkComData[key];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
      html += `<p>${arr[i].name}</p>`;
    }
    $(`.interworkBox[key="${key}"]`).html(html);
  }
}
//病人信息
var sexObj = {
  1: '男',
  2: '女',
  9: '未知',
};
var interworkSick = {};
var workorderSetting = {};

function interworkSickCallBack() {
  for (var key in interworkSick) {
    var item = interworkSick[key];
    if (item && item.after) {
      var html = '';
      item.after.name &&
        (html += `<p>
            【姓名】 修改前：${item.before.name || ''}；修改后：${
          item.after.name
        }
          </p>`);
      item.after.sex &&
        (html += `<p>
            【性别】 修改前：${
              sexObj[item.before.sex] || item.before.sex
            }；修改后：${sexObj[item.after.sex]}
          </p>
          `);
      item.after.phone &&
        (html += `<p>
            【联系电话】 修改前：${item.before.phone || ''}； 修改后：${
          item.after.phone
        }
          </p>`);
      item.after.idcard &&
        (html += `<p>
            【身份证号码】 修改前：${item.before.idcard || ''}； 修改后：${
          item.after.idcard
        }
          </p>`);
      $(`.interworkSickBox[key="${key}"]`).html(html);
    }
  }
}
//取消结算
var interworkSettle = {};

function interworkSettleCallBack() {
  for (var key in interworkSettle) {
    var item = interworkSettle[key];
    if (!item) {
      continue;
    }
    var html = `${item.zy}；${new Date(item.finishDate).format(
      'yyyy年MM月dd日'
    )}结算，应退${item.recedeFee}元`;
    $(`.interworkSettleBox[key="${key}"]`).html(html);
  }
}
//退预交金
var interworkPay = {};

function interworkPayCallBack() {
  for (var key in interworkPay) {
    var items = interworkPay[key];
    if (!items) {
      continue;
    }
    var html = '';
    for (var i = 0; i < items.length; i++) {
      var item = items[i];
      html += `<p>
          ${item.name}；${
        new Date(item.arriveDate).format('yyyy年MM月dd日') +
        item.payModeName +
        item.payValues
      }元
        </p>`;
    }
    $(`.interworkPayBox[key="${key}"]`).html(html);
  }
}
//取消住院项目
var interworkHosPro = {};

function interworkHosProCallBack() {
  for (var key in interworkHosPro) {
    var list = interworkHosPro[key];
    var html = `<table class="nortable">
        <thead>
          <th style="width:20px"></th>
          <th>医嘱日期</th>
          <th>内容</th>
          <th style="width:50px">数量</th>
          <th style="width:50px">单价</th>
          <th style="width:60px">金额</th>
        </thead>`;
    if (list) {
      html += '<tbody>';
      for (var i = 0; i < list.length; i++) {
        html += `<tr>
            <td>${i + 1}</td>
            <td>${list[i].BOOK_DATE}</td>
            <td>${list[i].SHOW_ORDER_NAME}</td>
            <td>${list[i].SHOW_QTY}</td>
            <td>${list[i].RETAIL_PRICE}</td>
            <td>${list[i].RETAIL_VALUE}</td>
          </tr>`;
      }
      html += '</tbody>';
    }
    html += '</table>';
    $(`.interworkHosProBox[key="${key}"]`).html(html);
  }
}
//医嘱项目
var interworkTest = {};

function interworkTestCallBack() {
  for (var key in interworkTest) {
    var list = interworkTest[key];
    var html = `<table class="oa-table no-zebra">
        <thead>
          <th style="width:20px"></th>
          <th>名称</th>
          <th>标本</th>
          <th>单价</th>
        </thead>`;
    if (list) {
      html += '<tbody>';
      for (var i = 0; i < list.length; i++) {
        html += `<tr>
            <td>${i + 1}</td>
            <td>${list[i].name}</td>
            <td>${list[i].sampleName}</td>
            <td>${list[i].price}</td>
          </tr>`;
      }
      html += '</tbody>';
    }
    html += '</table>';
    $(`.interworkTestBox[key="${key}"]`).html(html);
  }
}

//医嘱耗材
var inPatientOrder = {};
function inPatientOrderCallBack() {
  for (var key in inPatientOrder) {
    var list = inPatientOrder[key];
    var html = `<table class="oa-table no-zebra">
        <thead>
          <th style="width:20px"></th>
          <th>医嘱内容</th>
          <th>规格</th>
          <th>单位</th>
        </thead>`;
    if (list) {
      html += '<tbody>';
      for (var i = 0; i < list.length; i++) {
        html += `<tr>
            <td>${i + 1}</td>
            <td>${list[i].orderName}</td>
            <td>${list[i].spec}</td>
            <td>${list[i].dosageUnitName}</td>
          </tr>`;
      }
      html += '</tbody>';
    }
    html += '</table>';
    $(`.inPatientOrderBox[key="${key}"]`).html(html);
  }
}

/**
 * @desc 解析计算公式内容
 * @param {string} calculationRole 公式 HTML 内容
 * @param {function} computedValueKey 计算公式涉及传参的参数名称
 * @returns [{string}, {string[]}] {funStr, relatedAttrs} 公式内容
 */
function computedFormula(calculationRole, computedValueKey) {
  /**@desc 函数内容 */
  let funStr = '',
    funcReg = /^func_name_/,
    filedReg = /^form_item_keyid_/,
    /**@desc 相关参数唯一标识 */
    relatedAttrs = [],
    isFiled = false;
  common.parsehtml(calculationRole, {
    start: function (tag, attrs, unary) {
      for (var i = 0; i < attrs.length; i++) {
        // 匹配算法名字
        if (attrs[i].name == 'class' && funcReg.test(attrs[i].value)) {
          funStr += `customFun.${attrs[i].value.replace(/func_name_/, '')}`;
          isFiled = true;
          break;
        }
        // 匹配字段名称
        if (attrs[i].name == 'class' && filedReg.test(attrs[i].value)) {
          /**@type {string} 所需操作数据在表单的唯一标识 */
          let formKey = attrs[i].value.replace(/form_item_keyid_/, ''),
            valueName = '';
          if (computedValueKey) {
            valueName = computedValueKey(formKey);
          } else {
            valueName = formKey;
          }
          if (!valueName) {
            console.error('字段缺失');
            break;
          }
          relatedAttrs.push(formKey);
          funStr += `data.${valueName}`;
          isFiled = true;
          break;
        }
      }
    },
    chars: function (text) {
      if (!isFiled) {
        funStr += text;
      }
    },
    end: function (tag) {
      isFiled = false;
    },
    comment: function (text) {},
  });
  return { funStr, relatedAttrs };
}
/**
 * @desc 使用公式计算单元格内容
 * @param {string} funStr 计算公式字符串
 * @param {object} relatedNode 相关dom的 key：jqNode 对象
 * @returns {{value?: any, error?: Error}} {value, error} 计算结果
 */
function handleRelatedCompute(funStr, data) {
  let result = {},
    func = Function('data', `"use strict";return (${funStr})`);
  try {
    result.value = func(data);
  } catch (e) {
    result.error = e;
  }
  return result;
}
/**
 * @namespace childForm
 * @typedef childFormItem
 * @type {object}
 * @property {string} fieldName 子表单数据库的 key 名称
 * @property {string} remark 子表单 key 名称的注释，即表头名字
 * @property {'VARCHAR' | 'TEXTAREA' | 'SELECT' | 'MULTIPLESELECT' | 'DATEPICKER' | 'NUMBER' | 'FILE' | 'EXPRESSION'} fieldType 子表单编辑类型
 * @property {number|string} [fieldLength] 字段长度
 * @property {string} [optionValue] 当编辑类型为 SELECT 时，选择框的选项
 * @property {string} [defaultValue] 当前参数的默认值，渲染表子表单时，自动填充
 * @property {boolean} isNull 是否必填
 * @property {boolean} pcShow 是否启用 0禁用 1启用
 * @property {boolean} isHide 发起时是否隐藏 0显示 1隐藏
 * @property {string | number} seq 字段排序
 * @property {string | number} [fieldWidth] 列表宽度
 * @property {string | number} isReadOnly 是否只读
 */
/**
 * @desc 计算子表单表格内容
 * @property {string | number} showType 子表单展现形式 1表格 2卡片
 * @param {childFormItem[]} columns 子表单各列配置参数 list
 * @param {object} rowData 当前行的数据
 * @param {boolean} isRead 是否只读
 * @param {childFormItem[]} errorList 默认值等有问题的列，用来做提示
 * @param {boolean} editLine 是否允许审批时新增/删除行
 * @returns {string} trDom 当前行的 HTML 字符串
 */
function computeNewChildFormItem(
  showType,
  columns = [],
  rowData = {},
  isRead,
  errorList,
  editLine = '1'
) {
  let tableRowList = [],
    computedList = [];
  /**
   * @desc
   * @param {childFormItem} item 子表单的某一列
   */
  columns.map((item, index) => {
    var templateRes = getChildFormTemplate(
      showType,
      item,
      index,
      rowData,
      isRead,
      errorList
    );
    if (showType == 2) {
      let required =
        item.isNull == 1 ? '<span style="color: #ff0000">*</span>' : '';
      let colClass = ['TEXTAREA', 'FILE'].includes(item.fieldType)
        ? 'layui-col-xs12'
        : 'layui-col-xs4';
      let colDiv = $(`<div class="${colClass}">
          <label class="shell-layui-form-label">
            ${required}
            ${item.remark}
          </label>
        </div>`);
      colDiv.append(templateRes.colTemplate);
      tableRowList.push(colDiv);
    } else {
      tableRowList.push(templateRes.colTemplate);
    }
    if (templateRes.colComputed) {
      computedList.push(templateRes.colComputed);
    }
  });
  // 操作列
  let actionTd =
    !isRead && editLine == '1'
      ? `
      <${showType == 2 ? 'div' : 'td'} 
        class="child-form-table-action no-print"
      >
        ${showType == 2 ? '<span class="sequence-num"></span>' : ''}
        <div 
          name="childFormDeleteBtn"
          class="oaicon oa-icon-cuowu1 child-form-inline-btn"
        ></div>
        <div 
          name="childFormAddBtn"
          class="oaicon oa-icon-icon_chuangjianricheng child-form-inline-btn"
        ></div>
      </${showType == 2 ? 'div' : 'td'}>`
      : '';
  let boxDom =
    showType == 2 ? $(`<div class="cell-content-box"></div>`) : $(`<tr></tr>`);
  tableRowList.map((dom) => {
    boxDom.append(dom);
  });
  let trDom;
  if (showType == 2) {
    let cardDom = $(`<div class="child-form-table-cell"></div>`);
    trDom = cardDom.append(boxDom);
    trDom.prepend(actionTd);
  } else {
    trDom = boxDom;
    trDom.append(actionTd);
  }
  //可编辑且为table表格形式时实现可拖拽功能
  if (!isRead && showType == 1) {
    trDom.attr('draggable', true);
    rowData.ID && trDom.attr('id', rowData.ID);
    trDom[0].ondragstart = function (event) {
      let dom =
          showType == 2
            ? $(event.target).closest('.child-form-table-cell')
            : $(event.target).closest('tr'),
        table = $(dom).closest('.child-form-table'),
        tableKey = table.attr('data-child-form-key');
      tableKey && (childDragDom[tableKey] = dom);
    };
    trDom[0].ondragenter = function (event) {
      let dom =
          showType == 2
            ? $(event.target).closest('.child-form-table-cell')
            : $(event.target).closest('tr'),
        table = $(dom).closest('.child-form-table'),
        tableKey = table.attr('data-child-form-key');
      tableKey && dom.before(childDragDom[tableKey]);
    };
  }

  // 删除
  $('[name="childFormDeleteBtn"]', trDom).on('click', function () {
    let table = $(this).closest('.child-form-table'),
      t_showType = table.attr('data-child-form-type');
    layer.confirm('是否确认删除？', function (index) {
      layer.close(index);
      trDom.remove();
      let trList = $(
        t_showType == 2 ? '.child-form-table-cell' : 'tbody tr',
        table
      );
      if (trList.length == 1) {
        $('[name="childFormDeleteBtn"]', trList[0]).addClass('none');
      }
      table.find('.sequence-num').map((index, item) => {
        $(item).text(index + 1);
      });
      childFormCountFun();
    });
  });

  // 新增一行
  $('[name="childFormAddBtn"]', trDom).on('click', function (e) {
    const table = $(this).closest('.child-form-table'),
      key = table.attr('data-child-form-key'),
      t_showType = table.attr('data-child-form-type'),
      options = itemMap[key].childFormDetail || {},
      columns = options.fields || [];
    let newTr = computeNewChildFormItem(t_showType, columns);
    $(this)
      .closest(t_showType == 2 ? '.child-form-table-cell' : 'tr')
      .after(newTr);
    let trList = $(
      t_showType == 2 ? '.child-form-table-cell' : 'tbody tr',
      table
    );
    if (trList.length > 1) {
      $('[name="childFormDeleteBtn"]', trList[0]).removeClass('none');
    }
    table.find('.sequence-num').map((index, item) => {
      $(item).text(index + 1);
    });
    layForm.render();
    childFormBindFun();
  });

  computedList.map((setting) => {
    let {
        funStr,
        relatedAttrs,
        resultKey,
        data: rowData,
        pointLength,
      } = setting,
      resultNode = $(`[data-key-name=${resultKey}]`, trDom),
      relatedNode = {};

    // 绑定事件
    relatedAttrs.map((key) => {
      let dom = $(`[data-key-name=${key}]`, trDom);
      if (!dom.length) {
        return;
      }
      if (rowData[key] != dom.val()) {
        rowData[key] = dom.val();
      }
      relatedNode[key] = dom;
      let nodeName = dom[0].nodeName,
        /**@type childFormItem */
        relatedSetting = columns.find((item) => item.fieldName == key) || {},
        eventHandle = function () {
          let data = {};
          Object.keys(relatedNode).map((key) => {
            data[key] = relatedNode[key].val();
          });
          let { value, error } = handleRelatedCompute(funStr, data);
          if (error) {
            layer.msg(
              `<span class="required">${relatedSetting.remark}：</span>${error}`
            );
            return;
          }
          let isMathFunc =
            funStr.includes('MULTIPLY') || funStr.includes('SUM');
          if (isMathFunc) {
            if (value == 0 || (!pointLength && pointLength != 0)) {
              value = parseFloat(value);
            } else if (value) {
              value = value.toFixed(Number(pointLength));
            }
          }
          resultNode.val(value);
          resultNode.trigger('input');
        };
      if (nodeName == 'INPUT') {
        dom.on('blur', eventHandle);
      } else if (nodeName == 'SELECT') {
        dom.on('change', eventHandle);
      }
    });
    // 计算初始值
    if (
      !rowData[resultKey] &&
      relatedAttrs.every(
        (key) => rowData[key] !== undefined && rowData[key] !== null
      )
    ) {
      let { value = '' } = handleRelatedCompute(funStr, rowData);
      resultNode.val(value);
    } else if (rowData[resultKey]) {
      resultNode.val(rowData[resultKey]);
    }
  });
  return trDom;
}

function getChildFormTemplate(
  showType,
  item,
  index,
  rowData,
  isRead,
  errorList
) {
  let {
      remark: label = index,
      promptText = '',
      fieldName: value = index,
      fieldType = 'VARCHAR',
      fieldLength = null,
      defaultValue = '',
      optionValue = '',
      styleStr = '',
      pointLength = null,
      isReadOnly,
    } = item,
    /**@property {object} content table行 的JQ对象 */
    content =
      showType == 2
        ? $(
            `<div class="shell-layer-input-box" data-child-form-item-name="${value}"></div>`
          )
        : $(`<td data-child-form-item-name="${value}"></td>`),
    maxlength = fieldLength != null ? `maxlength=${fieldLength}` : '',
    inpVal = $.isEmptyObject(rowData) ? defaultValue : rowData[value] || '';
  if ((isRead || isReadOnly) && !['FILE', 'EXPRESSION'].includes(fieldType)) {
    let spanDom = $(
      `<span 
        style="color: #545454;display:inline-block;width:90%;word-wrap:break-word;white-space:normal;word-break:break-all;" 
        data-key-name="${value}" 
        ${styleStr ? `style="${styleStr}"` : ''}
      >${inpVal}</span>`
    );
    if (showType == 2) {
      content.css({
        display: 'flex',
        alignItems: 'center',
      });
    }
    spanDom[0].value = inpVal;
    content.append(spanDom);
    return {
      colTemplate: content,
    };
  }
  let colComputed;
  switch (fieldType) {
    // 文本框 -input
    case 'VARCHAR':
      if (fieldLength != null && inpVal.length > fieldLength) {
        inpVal = inpVal.slice(0, fieldLength);
      }
      content.append(
        `<input
        class="layui-input"
        placeholder="${promptText || ''}"
        autocomplete="off"
        data-key-name="${value}"
        ${maxlength}
        value="${inpVal}"
      >`
      );
      break;
    // 多行文本 -textarea
    case 'TEXTAREA':
      if (fieldLength != null && inpVal.length > fieldLength) {
        inpVal = inpVal.slice(0, fieldLength);
      }
      var inp = $(
        `<textarea
        type="textarea"
        class="layui-textarea"
        placeholder="${promptText || ''}"
        autocomplete="off"
        data-key-name="${value}"
        ${maxlength}
      ></textarea>`
      );
      inp.val(inpVal);
      content.append(inp);
      break;
    // 选择框 -select
    case 'SELECT':
      optionValue = optionValue.replace(/[,，;；]/g, ',');
      var optionList = [],
        options = optionValue.split(',').filter((item) => item);
      options.map((item) => {
        let selected = item == inpVal ? 'selected' : '';
        optionList.push(`<option value="${item}" ${selected}>${item}</option>`);
      });
      if (inpVal && !options.includes(inpVal)) {
        errorList && errorList.push(rowData);
      }
      content.append(
        `<select data-key-name="${value}" 
          placeholder="${promptText || ''}"
        >
          <option value=""></option>
          ${optionList.join('')}
        </select>`
      );
      break;
    //下拉多选 - multiple select
    case 'MULTIPLESELECT':
      var inp = $(
        `<input 
        type="text" 
        data-key-name="${value}"
        class="layui-input" 
        placeholder="${promptText || ''}" />`
      );
      content.append(inp);
      inp.val(inpVal);
      optionValue = optionValue.replace(/[,，;；]/g, ',');
      var optionList = [],
        options = optionValue.split(',').filter((item) => item);
      optionList = options.map((item) => {
        return {
          label: item,
          value: item,
        };
      });
      inpVal = inpVal.replace(/[,，;；]/g, ',');
      var defaultList = [],
        defaults = inpVal.split(',').filter((item) => item);
      defaultList = defaults.map((item) => {
        return {
          label: item,
          value: item,
        };
      });
      new $.checkSelect(inp, {
        datatype: 'local',
        data: optionList,
        label: 'label',
        value: 'value',
        default: defaultList,
        callback: function (res) {},
      });
      break;
    // 时间选择 -input
    case 'DATEPICKER':
      var dateReg =
        /^((\d{3}[1-9]|\d{2}[1-9]\d|\d[1-9]\d{2}|[1-9]\d{3})\-(((0[13578]|1[02])\-(0[1-9]|[12]\d|3[01]))|((0[469]|11)\-(0[1-9]|[12]\d|30))|(02\-(0[1-9]|[1]\d|2[0-8])))$)|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))\-02\-29)$/;
      if (!dateReg.test(inpVal)) {
        inpVal = defaultValue
          ? new Date(defaultValue).format('yyyy-MM-dd')
          : '';
      }
      var inp = $(
        `<input
          class="layui-input layDate"
          placeholder="${promptText || ''}"
          autocomplete="off" 
          type="text"
          readonly="readonly"
          data-key-name="${value}"
          value="${inpVal}"
        />`
      );
      layDate.render({
        elem: inp[0],
        trigger: 'click',
        format: 'yyyy-MM-dd',
        type: 'date',
        classes: 'time_Hm',
        done: function () {
          $(inp[0]).trigger('input');
          $(inp[0]).trigger('blur');
        },
      });
      content.append(inp);
      break;
    // 数值 -input
    case 'NUMBER':
      if (pointLength) {
        var str = `^(([1-9]{1}\\d*)|(0{1}))((\\.\\d{1,${pointLength}}|\\.))?$`,
          numberReg = new RegExp(str);
        if (!numberReg.test(inpVal)) {
          inpVal = defaultValue ? defaultValue : '';
        }
      }
      var inp = $(
        `<input 
          class="layui-input" 
          placeholder="${promptText || ''}"
          ${maxlength} 
          autocomplete="off" 
          data-key-name="${value}" 
          type="text"
        />`
      );
      inp.val(inpVal);
      inp.off('input').on('input', function () {
        let value = $(this).val(),
          isNegative = value.indexOf('-') == 0;
        let newVal =
          (isNegative ? '-' : '') +
          value
            .replace(/[^\d\.]/g, '')
            .replace('.', '$#$')
            .replace(/\./g, '')
            .replace('$#$', '.');
        if (pointLength) {
          let matchList = newVal.match(/\d+/g) || [];
          if (matchList.length > 1 && matchList[1].length >= pointLength) {
            matchList[1] = matchList[1].slice(0, pointLength);
            newVal = matchList.join('.');
            if (isNegative) {
              newVal = '-' + newVal;
            }
          }
        } else if (pointLength == 0) {
          let matchList = newVal.match(/\d+/g) || [];
          newVal = matchList[0] || '';
          if (isNegative) {
            newVal = '-' + newVal;
          }
        }
        $(this).val(newVal);
      });
      inp.on('blur', function () {
        let value = $(this).val();
        let newVal = parseFloat(value);
        if (isNaN(newVal)) {
          $(this).val('');
        } else if (value) {
          if (newVal == 0 || !pointLength) {
            $(this).val(parseFloat(value));
          } else {
            $(this).val(newVal.toFixed(Number(pointLength)));
          }
        }
      });
      content.append(inp);
      break;
    // 附件 -button
    case 'FILE':
      var inp = $(
        `<input
        style="display: none;"
        type="text"
        disabled
        data-key-name="${value}"
        value="${inpVal}"
      />`
      );
      content.append(inp);
      new newFileUpload(inp, { readonly: isRead || isReadOnly });
      break;
    // 公式计算 -input
    case 'EXPRESSION':
      var inp = $(
          `<input class="layui-input noBorder" disabled ${maxlength} data-key-name="${value}">`
        ),
        { funStr, relatedAttrs = [] } = computedFormula(defaultValue);
      colComputed = {
        funStr,
        relatedAttrs,
        resultKey: value,
        data: rowData,
        pointLength,
      };
      content.append(inp);
      break;
    default:
      break;
  }
  return {
    colTemplate: content,
    colComputed,
  };
}

// layui验证规则设置
function layuiVerify() {
  layForm.verify({
    otherReq: function (value, item) {
      var $ = layui.$;
      var verifyName = $(item).attr('name'),
        verifyType = $(item).attr('type'),
        formElem = $(item).parents('.layui-form'), //获取当前所在的form元素，如果存在的话
        verifyElem = formElem.find(`input[name="${verifyName}"]`), //获取需要校验的元素
        isTrue = verifyElem.is(':checked'), //是否命中校验
        focusElem = verifyElem.next().find('i.layui-icon'); //焦点元素
      if (!isTrue || !value) {
        //定位焦点
        focusElem.css(
          verifyType == 'radio'
            ? {
                color: '#FF5722',
              }
            : {
                'border-color': '#FF5722',
              }
        );
        //对非输入框设置焦点
        focusElem
          .first()
          .attr('tabIndex', '1')
          .css('outline', '0')
          .blur(function () {
            focusElem.css(
              verifyType == 'radio'
                ? {
                    color: '',
                  }
                : {
                    'border-color': '',
                  }
            );
          })
          .focus();
        return '必填项不能为空';
      }
    },
    checkReq: function (value, item) {
      var $ = layui.$;
      var verifyName = $(item).attr('name'),
        verifyType = $(item).attr('type'),
        formElem = $(item).parents('.layui-form'), //获取当前所在的form元素，如果存在的话
        verifyElem = formElem.find(`input[name="${verifyName}"]`), //获取需要校验的元素
        isTrue = verifyElem.is(':checked'), //是否命中校验
        focusElem = verifyElem.next().find('i.layui-icon'); //焦点元素
      if (!isTrue || !value) {
        //定位焦点
        focusElem.css(
          verifyType == 'checkBox'
            ? {
                color: '#FF5722',
              }
            : {
                'border-color': '#FF5722',
              }
        );
        //对非输入框设置焦点
        focusElem
          .first()
          .attr('tabIndex', '1')
          .css('outline', '0')
          .blur(function () {
            focusElem.css(
              verifyType == 'checkBox'
                ? {
                    color: '',
                  }
                : {
                    'border-color': '',
                  }
            );
          })
          .focus();
        return '必填项不能为空';
      }
    },
    file_number: function (value, item) {
      var $ = layui.$;
      var verifyName = $(item).attr('name'),
        formElem = $(item).parents('.layui-form'); //获取当前所在的form元素，如果存在的话
      verifyElem = formElem.find(`input[name="${verifyName}"]`); //获取需要校验的元素

      if (!value) {
        $(item).focus();
        return '请先对发文进行编号';
      }
    },
    phoneNumer: function (value, item) {
      function isTelCode(str) {
        var reg = /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/;
        return reg.test(str);
      }
      if (!isTelCode(value)) {
        $(item).focus();
        return '请输入正确的手机号码';
      }
    },
    idCard: function (value, item) {
      var checkCode = function (val) {
        var p =
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
        var code = val.substring(17);
        if (p.test(val)) {
          var sum = 0;
          for (var i = 0; i < 17; i++) {
            sum += val[i] * factor[i];
          }
          if (parity[sum % 11] == code.toUpperCase()) {
            return true;
          }
        }
        return false;
      };
      var checkDate = function (val) {
        var pattern =
          /^(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)$/;
        if (pattern.test(val)) {
          var year = val.substring(0, 4);
          var month = val.substring(4, 6);
          var date = val.substring(6, 8);
          var date2 = new Date(year + '-' + month + '-' + date);
          if (date2 && date2.getMonth() == parseInt(month) - 1) {
            return true;
          }
        }
        return false;
      };
      var checkProv = function (val) {
        var pattern = /^[1-9][0-9]/;
        var provs = {
          11: '北京',
          12: '天津',
          13: '河北',
          14: '山西',
          15: '内蒙古',
          21: '辽宁',
          22: '吉林',
          23: '黑龙江 ',
          31: '上海',
          32: '江苏',
          33: '浙江',
          34: '安徽',
          35: '福建',
          36: '江西',
          37: '山东',
          41: '河南',
          42: '湖北 ',
          43: '湖南',
          44: '广东',
          45: '广西',
          46: '海南',
          50: '重庆',
          51: '四川',
          52: '贵州',
          53: '云南',
          54: '西藏 ',
          61: '陕西',
          62: '甘肃',
          63: '青海',
          64: '宁夏',
          65: '新疆',
          71: '台湾',
          81: '香港',
          82: '澳门',
        };
        if (pattern.test(val)) {
          if (provs[val]) {
            return true;
          }
        }
        return false;
      };
      var checkID = function (val) {
        if (checkCode(val)) {
          var date = val.substring(6, 14);
          if (checkDate(date)) {
            if (checkProv(val.substring(0, 2))) {
              return true;
            }
          }
        }
        return false;
      };
      if (!checkID(value)) {
        $(item).focus();
        return '请输入正确的身份证号码';
      }
    },
    operationFile: function(value, dom) {
      let uploadKey = $(dom).find('button').attr('upload-key');
      let operationName = $(dom).attr('key-name');
      let ulDom = $(dom).find(`ul[upload-key=${uploadKey}]`);
      if (ulDom.length) {
        let liDom = ulDom.find('li');
        if (!liDom.length) {
          return `请上传【手术名称：${operationName}】的附件`;
        } 
      } else {
        return `请上传【手术名称：${operationName}】的附件`;
      }
    },
    /**@desc 添加子表单自定义校验 */
    childForm: function (value, dom) {
      let key = dom.getAttribute('data-child-form-key'),
        showType = dom.getAttribute('data-child-form-type'),
        options = itemMap[key],
        childFormColumns = options.childFormDetail.fields,
        tableData = [],
        requiredColumnKeys = childFormColumns
          .filter((item) => item.isNull == 1 && !item.isReadOnly)
          .map((item) => item.fieldName),
        failDom = null;
      $.each(
        $(showType == 2 ? '.child-form-table-cell' : 'tbody tr', $(dom)),
        function (index, trDom) {
          let rowData = {};
          let trID = $(this).attr('id');
          if (trID) rowData.ID = trID;

          $.each(
            $('[data-key-name]:not(.layui-upload-file)', $(trDom)),
            function (i, inputDom) {
              let key = $(inputDom)
                  .closest(showType == 2 ? '.shell-layer-input-box' : 'td')
                  .attr('data-child-form-item-name'),
                value = inputDom.value;
              $(inputDom).get(0).style.setProperty('border-color', '');
              requiredColumnKeys.includes(key) &&
                !value &&
                !failDom &&
                (failDom = inputDom);
              value && (rowData[key] = value);
            }
          );
          Object.keys(rowData).length && tableData.push(rowData);
        }
      );
      if (failDom) {
        $(failDom)
          .get(0)
          .style.setProperty('border-color', '#FF5722', 'important');
        failDom.focus();
        return '子表单必填项不能为空';
      }
    },
    /**@desc 基本药品字典 自定义校验 */
    essentialDrugDic: function (value, dom) {
      let keyId = $(dom).attr('data-key-id'),
        setting = itemMap[keyId] || {};
      if (
        setting.isMust &&
        (!formData[setting.fieldName] || !formData[setting.fieldName].length)
      ) {
        return '基本药品字典必填，请至少选择一条数据';
      }

      let numInputList = Array.from(
          $('tbody tr input[data-key-name="num"]', dom)
        ),
        errorDom = numInputList.find((dom) => !$(dom).val());
      if (errorDom) {
        $(errorDom).focus();
        return '药品数量不能为空';
      }

      let selectInputList = Array.from(
          $('tbody tr select[data-key-name="unitName"]', dom)
            .closest('.cell')
            .find('input')
        ),
        selectErrorDom = selectInputList.find((dom) => !$(dom).val());
      if (selectErrorDom) {
        $(selectErrorDom).focus();
        return '药品单位不能为空';
      }
    },
    medicalSupplieDic: function (value, dom) {
      let keyId = $(dom).attr('data-key-id'),
        setting = itemMap[keyId] || {};
      if (
        setting.isMust &&
        (!formData[setting.fieldName] || !formData[setting.fieldName].length)
      ) {
        return '物品信息必填，请至少选择一条数据';
      }
      let requiredField = [
        {
          prop: 'chemicalName',
          lable: '品名',
        },
        {
          prop: 'spec',
          lable: '规格',
        },
        {
          prop: 'unitName',
          lable: '单位',
        },
        {
          prop: 'factName',
          lable: '生产厂家',
        },
        {
          prop: 'supplierName',
          lable: '供货单位',
        },
        {
          prop: 'num',
          lable: '采购数量',
        },
        {
          prop: 'buyPrice',
          lable: '预算单价',
        },
        {
          prop: 'total',
          lable: '预算金额',
        },
        {
          prop: 'purchaseType',
          lable: '采购方式',
        },
      ];
      for (let i = 0; i < requiredField.length; i++) {
        let numInputList = Array.from(
            $(`tbody tr input[data-key-name="${requiredField[i].prop}"]`, dom)
          ),
          errorDom = numInputList.find((dom) => !$(dom).val());
        if (errorDom) {
          $(errorDom).focus();
          return `${requiredField[i].lable}不能为空`;
        }
      }
    },
  });
}
//处理表单数据
function dealCustomFormData(type) {
  //提交必填判断
  var isSubmit = type ? true : false;
  var fieldList = params.formData.toaFieldSetList;
  var formDatas = {};
  var childTableData = null;
  var childTableId = null;
  var commentData = {
    remark: '',
    approvalFiled: '',
    taskFileList: [],
  };
  if (params.baseData.templateType == 1) {
    formDatas.page_office_id = $('[name="page_office_id"]').val();
  }
  var form = $(params.formData.printTemplate || params.formData.content).clone();
  var formitems = form.find('.wfFormItem');
  let fieldListCopy = [];
  for (var i = 0; i < formitems.length; i++) {
    let keyid = $(formitems[i]).attr('id');
    let obh = fieldList.find(e => e.keyId === keyid);
    fieldListCopy.push(obh);
  }
  fieldList = fieldListCopy;
  for (var i = 0; i < fieldList.length; i++) {
    var field = fieldList[i];
    switch (field.fieldType) {
      case 'input':
        if (field.fieldName == 'doc_word') {
          formDatas['doc_word'] = $(`#doc_word${field.keyId}`)
            .find('[name="doc_word"]')
            .val();
          formDatas['doc_word_id'] = $(`#doc_word${field.keyId}`)
            .find('[name="doc_word_id"]')
            .val();
        } else if (field.fieldName == 'to_user_name') {
          formDatas['to_user_name'] = $(`#copy_user_name_${field.keyId}`).val();
          formDatas['to_user'] =
            $(`#copy_user_${field.keyId}`).val() +
            ';' +
            $(`#userDeptCodecopy_user_name_${field.keyId}`).val();
        } else if (field.fieldName == 'copy_user_name') {
          formDatas['copy_user_name'] = $(
            `#copy_user_name_${field.keyId}`
          ).val();
          formDatas['copy_user'] =
            $(`#copy_user_${field.keyId}`).val() +
            ';' +
            $(`#userDeptCodecopy_user_name_${field.keyId}`).val();
        } else {
          formDatas[field.fieldName] = $(
            `input[name="${field.fieldName}"]`
          ).val();
        }
        break;
      case 'textarea':
        formDatas[field.fieldName] = $(
          `textarea[name="${field.fieldName}"]`
        ).val();
        break;
      case 'number':
        formDatas[field.fieldName] = $(
          `input[name="${field.fieldName}"]`
        ).val();
        break;
      case 'date':
        formDatas[field.fieldName] = $(
          `input[name="${field.fieldName}"]`
        ).val();
        break;
      case 'radio':
        formDatas[field.fieldName] = $(
          `input[name="${field.fieldName}"]:checked`
        ).val();
        break;
      case 'checkbox':
        var items = $(`input[name="${field.fieldName}"]:checked`);
        var arr = [];
        for (var j = 0; j < items.length; j++) {
          arr.push($(items[j]).val());
        }
        formDatas[field.fieldName] = arr.join(',');
        break;
      case 'select':
        formDatas[field.fieldName] = $(
          '[name="' + field.fieldName + '"]'
        ).val();
        break;
      case 'file':
        var files = fileupload.file[field.keyId]
          ? fileupload.file[field.keyId].fileStr
          : '';
        formDatas[field.fieldName] = files;
        // if (itemMap[field.keyId].isMust && isSubmit) {
        //   if (!files) {
        //     layer.msg(field.showName + '必填');
        //     return false;
        //   }
        // }
        break;
      case 'comment':
        if ($(`textarea[name="${field.fieldName}"]`).length) {
          var files =
            fileupload.file[field.keyId] &&
            fileupload.file[field.keyId].fileList;
          commentData.remark +=
            ($(`textarea[name="${field.fieldName}"]`).val() || '') + '\n';
          commentData.approvalFiled = field.fieldName;
          for (var j = 0; j < files.length; j++) {
            commentData.taskFileList.push({
              fileUrl: files[j].filePath,
              fileName: files[j].originalName,
              fileSize: files[j].fileSize,
            });
          }
        }
        if ($('input[name="sealFile"]').val()) {
          formDatas.sealFile = $('input[name="sealFile"]').val();
        }
        break;
      case 'signature':
        formDatas[field.fieldName] = $(
          `.signatureDiv [name="${field.fieldName}"]`
        ).attr('path');
        break;
      case 'serialNumber':
        formDatas[field.fieldName] = $(
          `input[name="${field.fieldName}"]`
        ).val();
        break;
      case 'personChose':
        if ('N' == field.isDeleted) {
          var list = userChooseData[field.keyId];
          var names = [];
          var codes = [];
          for (var j = 0; j < list.length; j++) {
            names.push(list[j].name);
            codes.push(list[j].code);
          }
          formDatas[field.fieldName] = `${names.join(',')}--${codes.join(',')}`;
        }
        break;
      case 'deptChose':
        if ('N' == field.isDeleted) {
          formDatas[field.fieldName] = `${deptChoose[field.fieldName].name}--${
            deptChoose[field.fieldName].id
          }`;
        }
        break;
      case 'processChoose':
        var processList = processChooseData[field.keyId];
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (processList && processList.length == 0) {
            layer.msg(field.showName + '必选');
            return false;
          }
        }
        formDatas[field.fieldName] = (processList && processList.length)
          ? JSON.stringify(processList)
          : '';
        break;
      case 'hrpHyperlink':
        formDatas[field.fieldName] = $(
          `input[name="${field.fieldName}"]`
        ).val();
        break;
      case 'interworkCom':
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (!interworkComData[field.fieldName].length) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          interworkComData[field.fieldName]
        );
        break;
      case 'interworkSick':
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (!interworkSick[field.fieldName]) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          interworkSick[field.fieldName]
        );
        break;
      case 'workorderSetting':
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (!workorderSetting[field.fieldName]) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = workorderSetting[field.fieldName];
        break;
      case 'interworkSettle':
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (!interworkSettle[field.fieldName]) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          interworkSettle[field.fieldName]
        );
        break;
      case 'interworkPay':
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (!interworkPay[field.fieldName]) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          interworkPay[field.fieldName]
        );
        break;
      case 'interworkHosPro':
        if (itemMap[field.keyId].isMust) {
          if (
            !interworkHosPro[field.fieldName] ||
            !interworkHosPro[field.fieldName].length
          ) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          interworkHosPro[field.fieldName]
        );
        break;
      case 'interworkTest':
        if (itemMap[field.keyId].isMust && isSubmit) {
          if (!interworkTest[field.fieldName]) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          interworkTest[field.fieldName]
        );
        break;
      case 'inPatientOrder':
        if (itemMap[field.keyId].isMust) {
          if (!inPatientOrder[field.fieldName]) {
            layer.msg(field.showName + '必填');
            return false;
          }
        }
        formDatas[field.fieldName] = JSON.stringify(
          inPatientOrder[field.fieldName]
        );
        break;
      case 'childForm':
        if (!field.isReadonly && 'N' == field.isDeleted) {
          let childFormData = [],
            childFormKey = field.keyId,
            childFormShowType = field.childFormDetail.showType,
            childFormDom = $(`[data-child-form-key="${childFormKey}"]`);
          $.each(
            $(
              childFormShowType == 2 ? '.child-form-table-cell' : 'tbody tr',
              $(childFormDom)
            ),
            function (index, trDom) {
              let rowData = {};
              let trID = $(this).attr('id');
              if (trID) rowData.ID = trID;
              $.each($('[data-key-name]', $(trDom)), function (i, inputDom) {
                let key = $(inputDom)
                    .closest(
                      childFormShowType == 2 ? '.shell-layer-input-box' : 'td'
                    )
                    .attr('data-child-form-item-name'),
                  value = inputDom.value;
                value && (rowData[key] = value);
              });
              Object.keys(rowData) && childFormData.push(rowData);
            }
          );
          if (!childTableData) {
            childTableData = [];
          }
          childTableData.push(
            JSON.stringify({
              tableId: field.childFormDetail.id,
              fieldName: field.fieldName,
              childTableList: childFormData,
            })
          );
          childFormShowType == 2 && (childTableId = field.childFormDetail.id);
        }
        break;
      case 'childFormCount':
        let sumField = $(`[data-form-key-id="${field.keyId}"]`).text();
        formData[field.fieldName] = sumField;
        break;
      case 'essentialDrugDic':
        var drugDicTable = $(`[data-key-id="${field.keyId}"]`),
          drugData = formData[field.fieldName] || [];
        $.each(drugDicTable.find('tbody tr'), function (i, tr) {
          let num = $('[data-key-name="num"]', tr).val(),
            unitName = $('[data-key-name="unitName"]', tr).val(),
            memo = $('[data-key-name="memo"]', tr).val().replace(/\s/g, '');
          drugData[i] && (drugData[i].num = num);
          drugData[i] && (drugData[i].memo = memo);

          drugData[i] && (drugData[i].unitName = unitName);
          drugData[i] &&
            (drugData[i].unitId = drugData[i].drugList.filter(
              (fl) => fl.unitName === unitName
            )[0].unitId);
        });
        formDatas[field.fieldName] = drugData.length
          ? JSON.stringify(drugData)
          : null;
        break;
      case 'medicalSupplieDic':
        var drugDicTable = $(`[data-key-id="${field.keyId}"]`),
          drugData = formData[field.fieldName] || [];

        $.each(drugDicTable.find('tbody tr'), function (i, tr) {
          [
            'chemicalName',
            'spec',
            'unitName',
            'factName',
            'supplierName',
            'num',
            'buyPrice',
            'total',
            'purchaseType',
            'memo',
          ].map((prop) => {
            let propVal = $(`[data-key-name="${prop}"]`, tr)
              .val()
              .replace(/\s/g, '');
            drugData[i] && (drugData[i][prop] = propVal);
          });
        });
        formDatas[field.fieldName] = drugData.length
          ? JSON.stringify(drugData)
          : null;
        break;
      case 'operationItem':
        // 处理授权项目数据传输问题
        if ($('.operationTable').length != 0) {
          let tableData = $(`.operationTable[data-key-id="${field.keyId}"] tbody`);
          let list = JSON.parse(formData[field.fieldName]);
          tableData.find('tr').each(function(rowIndex) {
            let obj = {
              auditFlag: '1',
              auditScr: '',
            }
            $(this).find("td").each(function(colIndex) {
              if (colIndex == 1){
                obj.auditFlag = $(this).find('input[data-key="auditFlag"]:checked').val();
              }
              if (colIndex == 2){
                obj.auditScr = $(this).find('input[data-key="auditScr"]').val();
              }
            })
            list[rowIndex] = Object.assign({}, list[rowIndex], obj);
          })
          formData[field.fieldName] = JSON.stringify(list);
        } else {
          formDatas[field.fieldName] = $(
            `input[name="${field.fieldName}"]`
          ).val();
        }
        break;
      case 'deliveryInspection':
        formDatas[field.fieldName] = $(
          `input[name="${field.fieldName}"]`
        ).val();
        break;
      default:
        break;
    }
  }
  if (formData) {
    formDatas = $.extend({}, formData, formDatas);
  }
  // for (var key in formDatas) {
  //     formDatas[key] = formDatas[key] || '';
  // }

  return {
    formData: formDatas,
    commentData: commentData,
    childTableData,
    childTableId,
  };
}
var workflowType;

function setFormPageHeader() {
  $('#wfName').html(params.baseData.workflowName);
  if (workflowType == 'start' || workflowType == 'startAgain') {
    $('#userInfo').html(
      `<span>
        发起人：${userInfo.organizationParttimeName || userInfo.empDeptName}-${
        userInfo.empName
      }
      </span>`
    );
  } else {
    $('#userInfo').html(
      `<span>
        发起人：${wfData.launchDeptName}-${wfData.createUserName}
      </span>
      <span style="margin-left:10px">
        当前节点：${decodeURI(uriParams.currentStepName || '开始')}
      </span>`
    );
    $('#workflowNumber').html(`编号${uriParams.workflowNumber}`);
  }
}

var isEquipmentOfPurchase = false;
function getEquipmentOfBudgetData(pramaData) {
  $.ajax({
    method: 'post',
    url: '/ts-device/api/deviceBudgetWorkflow/getBudgetQuota',
    data: pramaData,
    success: function (res) {
      if (res.success && res.statusCode == 200) {
        let budgetData = res.object;
        if (
          budgetData.procureConfigInDict != null &&
          budgetData.departmentBudget != null
        ) {
          isEquipmentOfPurchase = true;

          if (budgetData.procureConfigInDict.type == 'service') {
            setEquipmentOfBudgetText(
              budgetData.departmentBudget.serviceQuota,
              budgetData.departmentBudget.serviceQuotaUse,
              budgetData.departmentBudget.serviceQuotaLeft
            );
          } else {
            setEquipmentOfBudgetText(
              budgetData.departmentBudget.projectQuota,
              budgetData.departmentBudget.projectQuotaUse,
              budgetData.departmentBudget.projectQuotaLeft
            );
          }
          let equipmentFormType = budgetData.procureConfigInDict.typeSmaller;
          setTimeout(() => {
            $.quoteFun(
              '../view-new/processView/modules/equipmentHistoryModal/index',
              {
                node: $('.wf-form-content-box #content .content-box'),
                type: equipmentFormType,
                wfDefinId: pramaData.definitionId,
              }
            );
          });
        }
      } else {
        layer.msg(res.message || '获取业务数据失败');
      }
    },
  });
}

function setEquipmentOfBudgetText(total, expense, surplus) {
  const budgetText = `预算总额: <span style="color: #5260ff">${total}</span>万元，已使用: <span style="color: #ff8c00">${expense}</span>万元，剩余: <span style="color: #00ab44">${surplus}</span>万元`;
  $('#equipmentBudget').html(budgetText);
}

//设置表单
function setForm(type) {
  workflowType = type;
  layuiVerify();

  if ((uriParams.role == 'self' || !uriParams.role) && !uriParams.type) {
    //流程发起人
    getFormTemplate();
  } else {
    //节点权限字段
    $.ajax({
      method: 'post',
      url: '/ts-workflow/workflow/form/filedPermissions',
      contentType: 'application/json; charset=utf-8',
      async: false,
      data: JSON.stringify({
        wfDefinitionId: params.baseData.wfDefinitionId,
        wfStepId: uriParams.approverCurrentStepNo,
      }),
      success: function (res) {
        if (res.success && res.object) {
          var arr = [];
          for (var i = 0; i < res.object.length; i++) {
            if (
              res.object[i].isShow == 1 ||
              res.object[i].isRequired == 1 ||
              res.object[i].isEditor == 1 ||
              res.object[i].isHide == 1
            ) {
              arr.push(res.object[i]);
            }
          }
          if (arr.length > 0) {
            getFormTemplate(arr);
          } else {
            getFormTemplate();
          }
        }
      },
    });
  }
  getEquipmentOfBudgetData({
    definitionId: params.baseData.wfDefinitionId,
    instanceId: uriParams.wfInstanceId || '',
  });
}
var canEditRes = false;
//获取表单设计模板
function getFormTemplate(fields) {
  $.ajax({
    method: 'post',
    url:
      '/ts-form/dpFormTemplate/findById/' +
      params.baseData.formId +
      '?wfInstanceId=' +
      uriParams.wfInstanceId,
    async: false,
    success: function (res) {
      if (res.success && res.object) {
        params.formData = res.object;
        wfParams = res.object.variables || [];
        var formField = params.formData.toaFieldSetList;
        //判断是否有办理查阅编辑权限，角色为办理查阅（role = 'consult'）且有编辑权限时显示修改提交按钮
        var consultEditPermission = params.baseData.wfPermissionsList.find(
          (e) => {
            return e.permissionsType == 2 && $.cookie('usercode') == e.userId;
          }
        );
        canEditRes =
          consultEditPermission != undefined && uriParams.role == 'consult';
        if (consultEditPermission != undefined && uriParams.role == 'consult') {
          $('#editConfirm').removeClass('none');
        }

        var commonFields = [
          'nowDate',
          'positiveTime',
          'loginName',
          'loginName',
          'loginCode',
          'loginOrg',
          'loginDept',
          'loginPhone',
          'loginEmail',
          'loginIDCard',
          'loginBirth',
          'loginAge',
          'loginSex',
          'loginPost',
          'loginPostType',
          'loginDuty',
          'loginPosttitle',
          'loginEntryDate',
          'loginSignature',
          'loginCarno',
          'loginOrgAttributes',
          'loginJob',
          'loginWorkDate',
          'loginHospArea',
          'loginTechnical',
          'annualLeave',
          'annualLeave_l',
        ];
        for (var key in formField) {
          formField[key].isMust = formField[key].isMust == 'Y' ? true : false;
          formField[key].isReadonly =
            formField[key].isReadonly == 'Y' ? true : false;
            // 处理表单字段隐藏的逻辑
            if (fields) {
              for (var i = 0; i < fields.length; i++) {
                if (fields[i].fieldName == formField[key].fieldName) {
                  if (fields[i].isHide == 1) {
                    formField[key].isHide = true;
                  } else {
                    formField[key].isHide = false;
                  }
                 
                }
              }
            }
          //普通审批 会签
          if (
            (uriParams.type == 'confirm' || uriParams.type == 'countersign') &&
            uriParams.role == 'deal'
          ) {
            if (fields) {
              formField[key].isMust = false;
              formField[key].isReadonly = true;
              for (var i = 0; i < fields.length; i++) {
                if (fields[i].fieldName == formField[key].fieldName) {
                  if (fields[i].isShow == 2) {
                    formField[key].isShow = false;
                  } else {
                    formField[key].isShow = true;
                  }
                  if (fields[i].isRequired == 1) {
                    formField[key].isMust = true;
                  } else {
                    formField[key].isMust = false;
                  }
                  if (fields[i].isHide == 1) {
                    formField[key].isHide = true;
                  } else {
                    formField[key].isHide = false;
                  }
                  if (fields[i].isEditor == 2) {
                    formField[key].isReadonly = true;
                  } else {
                    formField[key].isReadonly = false;
                  }
                }
              }
            } else {
              formField[key].isMust = false;
              formField[key].isReadonly = true;
            }
          }
          //重新发起
          if (
            uriParams.type == 'restart' &&
            (uriParams.role == 'self' || !uriParams.role)
          ) {
          }
          //撤销  催办
          if (
            uriParams.type == 'confirm' &&
            (uriParams.role == 'self' || !uriParams.role)
          ) {
            formField[key].isReadonly = true;
            formField[key].isMust = false;
          }
          //强制结束
          if (uriParams.type == 'cancle' && uriParams.role == 'consult') {
            //常用字段和审批意见字段不可编辑
            if (
              formField[key].fieldType != 'comment' &&
              !commonFields.includes(formField[key].sourceField) &&
              consultEditPermission != undefined
            ) {
              formField[key].isReadonly = false;
              formField[key].isMust = false;
            } else {
              formField[key].isReadonly = true;
              formField[key].isMust = false;
            }
          }
          //查看
          if (uriParams.type == 'see') {
            if (
              uriParams.role == 'consult' &&
              formField[key].fieldType != 'comment' &&
              !commonFields.includes(formField[key].sourceField) &&
              consultEditPermission != undefined
            ) {
              formField[key].isReadonly = false;
              formField[key].isMust = false;
            } else {
              formField[key].isReadonly = true;
              formField[key].isMust = false;
            }
          }
        }
        initForm();
      }
    },
  });
}
//表单解析
function initForm() {
  setStepName();
  var form = $(params.formData.content);
  var formitems = form.find('.wfFormItem');
  itemMap = {};

  for (var i = 0; i < params.formData.toaFieldSetList.length; i++) {
    itemMap[params.formData.toaFieldSetList[i].keyId] =
      params.formData.toaFieldSetList[i];
    formItemEvent[params.formData.toaFieldSetList[i].keyId] = {
      $el: null,
      events: {
        change: [],
        input: [],
        blur: [],
        focus: [],
      },
    };
  }
  for (var i = 0; i < formitems.length; i++) {
    var keyid = $(formitems[i]).attr('id');
    $(formitems[i]).replaceWith(formItemInit(formitems[i], itemMap[keyid]));
  }
  $('#form_box').append(form);
  layForm.render('radio');
  layForm.render('select');

  formItemFun();
  //props回调处理
  formItemCallBackEvent();
  //表单元素绑定事件
  formItemEventBind();
  // 子表单统计Input绑定
  childFormBindFun();
  // 子表单统计字段计算
  childFormCountFun();
  if (params.baseData.templateType == 1) {
    $('#form_box').append(
      `<input 
        name="page_office_id" 
        class="none" 
        value="${(formData && formData.page_office_id) || ''}" 
      />`
    );
  }

  $('#form_box [disabled]').addClass('noBorder');
  $('#faultTypeBoxName').removeClass('noBorder');
  setFormPageHeader();
}
// 子表单统计input绑定change事件
function childFormCountFun() {
  if (childFormCountList.length == 0) return;
  for (var e = 0; e < childFormCountList.length; e++) {
    let item = childFormCountList[e];
    let inputList = `#form_content [data-child-form-table-id="${item.tableId}"] [data-key-name="${item.sumField}"]`;
    let sum = 0;
    $(inputList).each(function () {
      let value = 0;
      if ($(this).prop('nodeName') === 'SPAN') {
        value = Number($(this).text());
      } else if ($(this).prop('nodeName') === 'INPUT') {
        value = Number($(this).val());
      }
      sum += value * 10000;
    });
    sum /= 10000;

    $(`#form_content div[data-form-key-id="${item.keyId}"]`).text(sum);
  }
}
function childFormBindFun() {
  if (childFormCountList.length == 0) return;
  for (var e = 0; e < childFormCountList.length; e++) {
    let item = childFormCountList[e];
    let inputList = `#form_content [data-child-form-table-id="${item.tableId}"] [data-key-name="${item.sumField}"`;
    $(inputList)
      .off('change input')
      .on('input', function () {
        childFormCountFun();
      });
  }
}
// 文本域输入高度自适应
function makeExpandingArea(el) {
  var timer = null;
  //由于ie8有溢出堆栈问题，故调整了这里
  var setStyle = function (el, auto) {
    setTimeout(() => {
      let nowLength = $(el).closest('.formItem').find('#nowLength');
      if (nowLength) {
        $(nowLength).text($(el).val().length);
      }

      if (auto) {
        el.style.height = 'auto';
      }
      el.style.height = el.scrollHeight + 'px';
    }, 100);
  };
  var delayedResize = function (el) {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
    timer = setTimeout(function () {
      setStyle(el);
    }, 200);
  };
  if (el.addEventListener) {
    el.addEventListener(
      'input',
      function () {
        setStyle(el, 1);
      },
      false
    );
    setStyle(el);
  } else if (el.attachEvent) {
    el.attachEvent('onpropertychange', function () {
      setStyle(el);
    });
    setStyle(el);
  }
  if (window.VBArray && window.addEventListener) {
    //IE9
    el.attachEvent('onkeydown', function () {
      var key = window.event.keyCode;
      if (key == 8 || key == 46) delayedResize(el);
    });
    el.attachEvent('oncut', function () {
      delayedResize(el);
    }); //处理粘贴
  }
}

// 文本域获取焦点添加剩余长度提示
function addLengthTips(el, parent, jsonLength) {
  el.addEventListener(
    'focus',
    function () {
      $(el).closest('.formItem').css('padding-bottom', '12px');
      $(parent).append(initNumCount($(el).val().length, jsonLength));
    },
    false
  );

  el.addEventListener(
    'blur',
    function () {
      $(el).closest('.formItem').css('padding-bottom', '0px');
      let numCountDom = $(parent).find('.num_count');
      if (numCountDom.length) numCountDom.remove();
    },
    false
  );
}

//模板解析
function formItemInit(temp, json) {
  var item = $('<div class="formItem"></div>');
  if (!json || json.isHide) {
    console.warn('模板解析', temp, '的字段');
    return item;
  }
  var readonly = json.isReadonly ? true : false;
  var required = json.isMust ? true : false;
  var childFormLimitColumns = null; // 子表单权限列表，减少多次调用接口的情况

  // hrpHyperlink不为地址 则渲染附件组件
  if (json.fieldType === 'hrpHyperlink') {
    let hrpHyperlinkValue = formData[json.fieldName];
    if (hrpHyperlinkValue && hrpHyperlinkValue.indexOf('http:') === -1) {
      json.fieldType = 'file';
    }
  }
  switch (json.fieldType) {
    case 'input':
      if (json.fieldName == 'doc_word') {
        var div = $(
          `<div 
            class="doc_word" 
            keyid="${json.keyId}" 
            id="doc_word${json.keyId}"
          ></div>`
        );
        if (formData[json.fieldName]) {
          div.attr('doc_word', formData['doc_word']);
          div.attr('doc_word_id', formData['doc_word_id']);
        }
        // 机关代字
        // else {
        //   json.isMust = true;
        //   item.append('<span class="required">*</span>');
        // }
        item.append(div);
      } else if (json.fieldName == 'to_user_name') {
        var btn = $(
          '<button class="layui-btn layui-btn-normal fr userSelect">选择</button>'
        );
        var box = $(
          '<div style="overflow:hidden; position:relative; margin-right:3px"></div>'
        );
        var show = $('<p class="userShow" style="opacity:0;"></p>');
        btn.attr('keyid', json.keyId);
        var area = $(
          `<textarea 
            class="userInp"
            style="opacity:1; z-index:10" 
            name="to_user_name" 
            maxlength="${json.length}"
            id="copy_user_name_${json.keyId}"
          ></textarea>`
        );
        area.prop('disabled', readonly);
        area.attr('keyid', json.keyId);
        var name = $(
          `<input id="userNameIdcopy_user_name_${json.keyId}" class="none" />`
        );
        var deptCode = $(
          `<input id="userDeptCodecopy_user_name_${json.keyId}" class="none" />`
        );
        var code = $(
          `<input id="copy_user_${json.keyId}" name="to_user" class="none" />`
        );
        area.val(json.defaultVal);
        show.text(json.defaultVal);
        code.val(json.defaultVal);
        if (formData && formData.to_user_name) {
          show.text(formData.to_user_name);
          area.val(formData.to_user_name);
          if (null != formData.to_user && '' != formData.to_user) {
            code.val(formData.to_user.split(';')[0]);
            deptCode.val(formData.to_user.split(';')[1]);
          }
        }
        box.append(area);
        box.append(show);
        box.append(name);
        box.append(deptCode);
        box.append(code);
        item.append(btn);
        item.append(box);
        readonly && btn.remove();
      } else if (json.fieldName == 'copy_user_name') {
        var btn = $(
          '<button class="layui-btn layui-btn-normal fr userSelect">选择</button>'
        );
        var box = $('<div style="overflow:hidden;position:relative;"></div>');
        var show = $('<p class="userShow userSelect" style="opacity:0;"></p>');
        btn.attr('keyid', json.keyId);
        var area = $(
          `<textarea 
            class="userInp" 
            style="opacity:1;z-index:10;" 
            name="copy_user_name" 
            maxlength="${json.length}"
            id="copy_user_name_${json.keyId}"
          ></textarea>`
        );
        area.prop('disabled', readonly);
        area.attr('keyid', json.keyId);
        var name = $(
          `<input id="userNameIdcopy_user_name_${json.keyId}" class="none"/>`
        );
        var deptCode = $(
          `<input id="userDeptCodecopy_user_name_${json.keyId}" class="none" />`
        );
        var code = $(
          `<input id="copy_user_${json.keyId}" name="copy_user" class="none" />`
        );
        area.val(json.defaultVal);
        show.text(json.defaultVal);
        code.val(json.defaultVal);
        if (formData && formData.copy_user_name) {
          show.text(formData.copy_user_name);
          area.val(formData.copy_user_name);
          if (null != formData.copy_user && '' != formData.copy_user) {
            code.val(formData.copy_user.split(';')[0]);
            deptCode.val(formData.copy_user.split(';')[1]);
          }
        }
        box.append(area);
        box.append(show);
        box.append(name);
        box.append(deptCode);
        box.append(code);
        item.append(btn);
        item.append(box);
        readonly && btn.remove();
      } else if (json.fieldName == 'receive_number') {
        var inp = $(
          `<input 
            name="receive_number" 
            keyid="${json.keyId}" 
            class="layui-input" 
            maxlength="${json.length}"
            readonly 
          />`
        );
        inp.val((formData && formData.receive_number) || json.defaultVal);
        if (formData.receive_number) {
        } else {
          inp.addClass('receive_number');
        }
        item.append(inp);
      } else {
        var inp = $(
          `<input 
            class="layui-input" 
            autocomplete="off" 
            name="${json.fieldName}"
            maxlength="${json.length}"
          />`
        );
        inp.attr('placeholder', json.promptText);
        formItemEvent[json.keyId].$el = inp;
        if (formData[json.fieldName]) {
          inp.val(formData[json.fieldName]);
          inp.attr('title', formData[json.fieldName]);
        } else {
          if (json.fieldName == 'file_number') {
            inp.prop('disabled', true);
          } else {
            if (json.dataSource == 1) {
              inp.val(json.defaultValue);
            } else if (
              json.dataSource == 2 &&
              JSON.stringify(formData) == '{}'
            ) {
              inp.attr('data-from', json.sourceField);
            }
          }
        }
        if (json.sourceField == 'nowDate' && uriParams.type == 'startAgain') {
          inp.attr('data-from', json.sourceField);
        }
        if (json.dataSource == 3 && json.fieldName != 'file_number') {
          formItemCalculationRole(inp, json);
        }
        item.append(inp);
        formItemPropsSet(item, json);
      }
      break;
    case 'textarea':
      var inp = $(
        `<textarea 
          autoHeight="true" 
          class="layui-textarea" 
          name="${json.fieldName}"
          maxlength="${json.length}"
        ></textarea>`
      );

      inp.attr('placeholder', json.promptText);
      formItemEvent[json.keyId].$el = inp;
      if (formData[json.fieldName]) {
        inp.val(formData[json.fieldName]);
      } else {
        if (json.dataSource == 1) {
          inp.val(json.defaultValue);
        } else if (json.dataSource == 2 && JSON.stringify(formData) == '{}') {
          inp.attr('data-from', json.sourceField);
        }
      }
      if (json.dataSource == 3) {
        formItemCalculationRole(inp, json);
      }

      // 未设置值 最小高度为30px （准备发起流程时）

      inp.css({
        height: '30px',
        minHeight: '30px',
      });
      item.append(inp);
      formItemPropsSet(item, json);
      break;
    case 'number':
      var inp = $(
        `<input 
          class="layui-input" 
          autocomplete="off" 
          type="text" 
          inp-type="number" 
          maxlength="${json.length}"
          name="${json.fieldName}"
        />`
      );
      inp.attr('placeholder', json.promptText);
      inp.attr('treatmentMethod', json.treatmentMethod);
      inp.attr('isThousandth', json.isThousandth);
      inp.attr('isMakeBigger', json.isMakeBigger);
      inp.attr('decimalDigit', json.decimalDigit);
      inp.attr('min', json.min);
      inp.attr('max', json.max);
      formItemEvent[json.keyId].$el = inp;
      if (formData[json.fieldName]) {
        inp.val(formData[json.fieldName]);
      } else {
        if (json.dataSource == 1) {
          inp.val(json.defaultValue);
        }
      }
      //#start 2021-11-3 suli 解决退回流程表单自动计算未绑定
      if (json.dataSource == 3) {
        formItemCalculationRole(inp, json);
      }
      if (readonly) {
        upFormItemEvent(json.keyId, 'input', bindNumberFormat(inp, json));
      }
      upFormItemEvent(json.keyId, 'blur', bindNumberFormat(inp, json));
      if (json.isMakeBigger) {
        upFormItemEvent(json.keyId, 'focus', function () {
          var val = inp.attr('title');
          if (val != '') {
            inp.val(val);
          }
        });
      }
      //#end 2021-11-3
      item.append(inp);
      formItemPropsSet(item, json);
      break;
    case 'date':
      var inp = $(
        `<input 
          class="layui-input layDate" 
          autocomplete="off" 
          type="text" 
          name="${json.fieldName}" 
          readonly="readonly"
        />`
      );
      inp.attr('placeholder', json.promptText);
      inp.attr('dataFormat', json.dataFormat);
      formItemEvent[json.keyId].$el = inp;
      if (formData[json.fieldName]) {
        inp.val(formData[json.fieldName]);
      } else {
        if (json.dataSource == 1) {
          inp.val(json.defaultValue);
        } else if (json.dataSource == 2 && JSON.stringify(formData) == '{}') {
          format = json.dataFormat.replace(/Y/g, 'y').replace(/D/g, 'd');
          inp.val(new Date().format(format));
        }
      }
      item.append(inp);
      formItemPropsSet(item, json);
      break;
    case 'radio':
      var list = json.optionValue.split(',');
      if (formData[json.fieldName]) {
        if (list.indexOf(formData[json.fieldName]) == -1) {
          list.push(formData[json.fieldName]);
        }
      }
      for (var i = 0; i < list.length; i++) {
        var inp = $(`<input type="radio" name="${json.fieldName}" lay-filter="${json.fieldName}">`);
        inp.val(list[i]);
        inp.attr('title', list[i]);
        if (formData[json.fieldName] == list[i]) {
          inp.prop('checked', true);
        } else {
          if (json.dataSource == 1) {
            if (!formData[json.fieldName] && json.defaultValue) {
              if (json.defaultValue == list[i]) {
                inp.prop('checked', true);
              }
            }
          }
        }
        item.append(inp);
      }

      if (json.wrap === 'Y') {
        setTimeout(() => {
          item.find('.layui-form-radio').css('display', 'flex');
        }, 100);
      }
      formItemEvent[json.keyId].$el = item.find('input');
      formItemPropsSet(item, json);
      break;
    case 'checkbox':
      var list = json.optionValue.split(',');
      var defList;
      if (json.defaultValue) {
        defList = json.defaultValue.split(',');
      }
      var valList = formData[json.fieldName]
        ? formData[json.fieldName].split(',')
        : [];
      if (formData[json.fieldName]) {
        for (var i = 0; i < valList.length; i++) {
          if (list.indexOf(valList[i]) == -1) {
            list.push(valList[i]);
          }
        }
      }
      for (var i = 0; i < list.length; i++) {
        var label = $('<label class="labelCheckbox"></label>');
        if (json.wrap === 'Y') label.css('display', 'block');
        var inp = $(
          `<input type="checkbox" class="self-checkbox" name="${json.fieldName}">`
        );
        var icon = $('<i class="self-checkbox-icon"></i>');
        inp.val(list[i]);
        if (valList.indexOf(list[i]) != -1) {
          inp.prop('checked', true);
        } else {
          if (json.dataSource == 1) {
            if (!readonly && !formData[json.fieldName] && json.defaultValue) {
              if (defList && defList.indexOf(list[i]) != -1) {
                inp.prop('checked', true);
              }
            }
          }
        }
        label.append(inp);
        label.append(icon);
        label.append(list[i]);
        item.append(label);
      }
      formItemEvent[json.keyId].$el = item.find('input');
      formItemPropsSet(item, json);
      break;
    case 'select':
      if (json.fieldName == 'receive_unit') {
        var select = $(
          `<select name="${json.fieldName}" class="receive_unit"></select>`
        );
        if (formData[json.fieldName]) {
          select.attr('data-value', formData[json.fieldName]);
        }
        item.append(select);
      } else {
        if (json.dataSource == 1) {
          var list = json.optionValue.split(',');
          var select = $(`<select name="${json.fieldName}"></select>`);
          if (formData[json.fieldName]) {
            if (list.indexOf(formData[json.fieldName]) == -1) {
              list.push(formData[json.fieldName]);
            }
          }
          var emptyOpt = $('<option value="">请选择</option>');
          select.append(emptyOpt);
          for (var i = 0; i < list.length; i++) {
            var opt = $(`<option value="${list[i]}">${list[i]}</option>`);
            if (!formData[json.fieldName]) {
              if (json.defaultValue == list[i]) {
                select.val(list[i]);
              }
            }
            select.append(opt);
          }
          if (formData[json.fieldName]) {
            select.val(formData[json.fieldName]);
          }
          item.append(select);
        }
        if (json.dataSource == 4) {
          var sel;
          sel = $(
            `<select name="${json.fieldName}" class="dictSel" keyid="${json.keyId}"></select>`
          );
          if (formData[json.fieldName]) {
            sel.val(formData[json.fieldName]);
          }
          item.append(sel);
        }
        if (json.dataSource == 6) {
          var sel;
          sel = $(
            `<select 
              name="${json.fieldName}" 
              class="relationWorkflowSel" 
              keyid="${json.keyId}"
            ></select>`
          );
          if (formData[json.fieldName]) {
            sel.val(formData[json.fieldName]);
          }
          item.append(sel);
        }
        if (json.dataSource == 7) {
          var sel;
          sel = $(
            `<select 
              name="${json.fieldName}" 
              class="interfaceServicesSel" 
              keyid="${json.keyId}"
            ></select>`
          );

          if (formData[json.fieldName]) {
            sel.val(formData[json.fieldName]);
          }
          item.append(sel);
        }
        formItemEvent[json.keyId].$el = item.find('select');
        formItemPropsSet(item, json);
      }
      break;
    case 'file':
      var file = $(
        `<input 
          type="hidden" 
          keyId="${json.keyId}" 
          name="${json.fieldName}"
        />`
      );
      required &&
        (item.append('<span class="required">*</span>'),
        file.attr('lay-verify', 'required'),
        file.attr('ts-required', 'required'));
      if (formData[json.fieldName]) {
        file.val(formData[json.fieldName]);
      }
      let file_number = json.fieldName == 'main_file' ? 1 : 999;
      var html = $(
        `<span 
          class="FileUploadBtn layui-btn layui-btn-normal" 
          keyId="${json.keyId}" 
          isReadonly="${readonly * 1}"
          file-number="${file_number}"
        >选择附件</span>
        <span class="AllFileDown layui-btn layui-btn-normal none">
          批量下载
        </span>
        <div class="layui-upload">
          <ul class="FileList"></ul>
        </div>`
      );
      item.append(file);
      item.append(html);
      break;
    case 'comment':
      var box = $(
        `<div class="comment_box" field-name="${json.fieldName}" maxlength="${json.length}" key-id="${json.keyId}"></div>`
      );
      if (json.hideApprovalTime) {
        box.attr('hideApprovalTime', 'Y');
      }
      var area = $('<div class="comment_area"></div>');
      let hasSeal = false;
      var hasSealHtml = null;
      var divSealHtml = '<div class="sealImg"></div>';
      var valueSealHtml = null;
      if (!readonly) {
        $.ajax({
          url: '/ts-basics-bottom/api/commSealManage/getCommSealManageList',
          type: 'get',
          async: false,
          success: function (res) {
            if (res.object && res.object.length > 0) {
              hasSeal = true;
            }
          },
        });
        if (hasSeal) {
          hasSealHtml = `
            <span 
              class="sealBtn layui-btn layui-btn-normal" 
              keyId="${json.keyId}"
              style="background: #009688 !important; border: 1px solid #009688"
            >
              插入印章
            </span>
          `;
          valueSealHtml = `<input class="layui-input" type="hidden" autocomplete="off" name="sealFile" />`
        }
        var textarea = $(
          `<textarea 
            class="layui-textarea" 
            name="${json.fieldName}" 
            maxlength="${json.length}"  
            autoHeight="true" 
            style="width:calc(100% - 64px) !important"
          ></textarea>`
        );
        textarea.attr(
          'placeholder',
          json.promptText || '请输入' + json.showName.replace(/\t/g, '')
        );
        var opera = $('<div class="officaldiction"><span>常用语</span></div>');
        area.append(textarea);
        area.append(opera);
        if (required) {
          textarea.attr({
            'lay-verify': 'required',
            'ts-required': 'required',
          });
        }
      }
      if (divSealHtml) {
        area.append(divSealHtml);
      }
      if (valueSealHtml) {
        area.append(valueSealHtml);
      }
      var html = $(
        `<div class="uploadDiv">
          <input 
            type="hidden" 
            name="taskFile${json.fieldName}" 
            keyId="${json.keyId}"
            maxlength="${json.length}" 
            ${
              !readonly && json.isMustCommentFile
                ? 'lay-verify="required" ts-required="required"'
                : ''
            }
           />
          <span 
            class="FileUploadBtn layui-btn layui-btn-normal" 
            keyId="${json.keyId}" 
            isReadonly="${readonly || json.isHideCommentFile ? 1 : 0}"
          >
            选择附件${!readonly && json.isMustCommentFile ? '（必传）' : ''}
          </span>
          <span class="AllFileDown layui-btn layui-btn-normal none">批量下载</span>
          <div class="layui-upload">
            <ul class="FileList"></ul>
          </div>`
      );
      if (hasSealHtml) {
        console.log($(html.find('.FileUploadBtn')));
        $(html.find('.FileUploadBtn')).after(hasSealHtml);
      }
      if (required) {
        item.append('<span class="required">*</span>');
      }
      if (readonly) {
        area.addClass('none');
      }
      area.append(html);
      box.append(area);
      item.append(box);
      break;
    case 'signature':
      var div = $(
        `<div 
          class="signatureDiv" 
          keyid="${json.keyId}" 
          id="signature_${json.keyId}"
        ></div>`
      );
      if (formData[json.fieldName]) {
        if (
          formData[json.fieldName].includes(
            '/ts-basics-bottom/fileAttachment/downloadFile'
          )
        ) {
          div.append(
            `<img class="signImg" name="${json.fieldName}" src="${
              formData[json.fieldName]
            }" path="${formData[json.fieldName]}">`
          );
        } else {
          var inp = $(
            `<input 
              class="layui-input signInput" 
              autocomplete="off" 
              name="${json.fieldName}"
              value="${formData[json.fieldName]}"
              path="${formData[json.fieldName]}"
              disabled
            />`
          );
          div.append(inp);
        }
      } else {
        var img = $(
          `<img class="signImg" name="${json.fieldName}" data-from="${json.sourceField}">`
        );
        var inp = $(
          `<input 
            class="layui-input signInput" 
            autocomplete="off" 
            name="${json.fieldName}"
            disabled
          />`
        );
        div.append(img);
        div.append(inp);
        json.isMust = true;
        item.append('<span class="required">*</span>');
      }
      item.append(div);
      break;
    case 'serialNumber':
      var inp = $(
        `<input class="layui-input" autocomplete="off" name="${json.fieldName}" />`
      );
      inp.attr('placeholder', json.promptText);
      if (formData[json.fieldName] && uriParams.type != 'startAgain') {
        inp.val(formData[json.fieldName]);
      } else {
        inp.addClass('serialNumber');
      }
      item.append(inp);
      formItemPropsSet(item, json);
      break;
    case 'personChose':
      var textarea = $(
        `<textarea class="layui-textarea" keyid="${json.keyId}"></textarea>`
      );
      userChooseData[json.keyId] = [];
    if (formData[json.fieldName] && !['--','无'].includes(formData[json.fieldName])) {
        var list = formData[json.fieldName].split('--');
        var names = list[0] == null ? "" : list[0].split(',');
        var codes = list[1] == null ? "" : list[1].split(',');
        var arr = [];
        for (var i = 0; i < names.length; i++) {
          if (names[i] && codes[i]) {
            arr.push({
              name: names[i],
              code: codes[i],
            });
          }
        }
        textarea.val(names);
        userChooseData[json.keyId] = arr;
      }
      textarea.prop('readonly', true);
      if (!readonly) {
        textarea.addClass('userChoose');
      }
      required &&
        (item.append('<span class="required">*</span>'),
        textarea.attr({ 'lay-verify': 'required', 'ts-required': 'required' }));
      item.append(textarea);
      break;
    case 'deptChose':
      var inp = $(
        `<input 
          class="layui-input" 
          autocomplete="off" 
          type="text" 
          name="${json.fieldName}" 
        />`
      );
      if (formData[json.fieldName]) {
        deptChoose[json.fieldName] = {
          name: formData[json.fieldName].split('--')[0],
          id: formData[json.fieldName].split('--')[1],
        };
        inp.val(formData[json.fieldName].split('--')[0]);
      } else {
        deptChoose[json.fieldName] = {
          name: '',
          id: '',
        };
      }
      if (!readonly) {
        inp.addClass('deptChoose');
        inp.attr('id', `deptChoose_${json.keyId}`);
      } else {
        inp.prop('readonly', true);
      }
      required &&
        (item.append('<span class="required">*</span>'),
        inp.attr({ 'lay-verify': 'required', 'ts-required': 'required' }));
      item.append(inp);
      break;
    case 'processChoose':
      var con = $(
        `<div class="process-choose" field-name="${json.fieldName}" keyid="${json.keyId}"></div>`
      );
      var box = $('<div class="process-list-box"></div>');
      processChooseData[json.keyId] = [];
      if (!readonly) {
        var inp = $(
          '<input class="layui-input process-choose-btn" placeholder="请选择" readonly autocomplete="off" type="text">'
        );
        box.append(inp);
        var btn = $(
          `<span 
            class="fa fa-search process-choose-btn" 
            style="line-height: 30px; color: #c2c2c2;" 
            keyid="${json.keyId}"
          ></span>`
        );
        con.append(btn);
      }
      if (formData[json.fieldName]) {
        var list = JSON.parse(formData[json.fieldName]);
        var ulElem = $('<ul class="process-list"></ul>');
        for (var i = 0; i < list.length; i++) {
          var liElem = $(
            `<li 
                class="process-item" 
                row-id="${list[i].wfInstanceId}"
              >
                <span>
                  ${list[i].workflowName}
                  - ${list[i].createDate}
                </span>
              </li>`
          );
          ulElem.append(liElem);
        }
        box.html(ulElem);
        processChooseData[json.keyId] = list;
      }
      con.prepend(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'hrpHyperlink':
      if (readonly && formData[json.fieldName]) {
        var linkArr = formData[json.fieldName].split(',');
        for (var i = 0; i < linkArr.length; i++) {
          var link = $(
            `<p class="hrp-hyperlink-item">
              <a 
                class="hrpHyperlink" 
                name="${json.fieldName}" 
                href="${linkArr[i]}"
              >${linkArr[i]}</a>
            </p>`
          );
          item.append(link);
        }
      } else {
        var inp = $(
          `<input 
            class="layui-input" 
            autocomplete="off" 
            name="${json.fieldName}"
          />`
        );
        inp.attr('placeholder', json.promptText);
        formItemEvent[json.keyId].$el = inp;
        item.append(inp);
      }
      formItemPropsSet(item, json);
      break;
    case 'fileTemplate':
      var con = $('<div class="fileTemplate"></div>');
      // var title = $(`<span class="AllFileDown layui-btn layui-btn-normal">批量下载</span>`);
      var title = '';
      var box = $(`<div class="layui-upload"><ul class="FileList"></ul></div>`);
      var res = getFileByIds(json.fileTemplate);
      if (res && res.success) {
        var fileList = res.object;
        var html = '';
        for (var i = 0; i < fileList.length; i++) {
          html = `<li class="file-litem" file-id="${fileList[i].id}">
              <a 
                class="fileDown" 
                href="/ts-document/attachment/downloadFile/${fileList[i].id}"
              >${fileList[i].originalName}</a>`;
          var imgClass = common.isImg(fileList[i].originalName)
            ? 'viewerImg'
            : 'viewerDoc2';
          html += `<span 
              class="${imgClass}" 
              style="margin:0 5px; cursor: pointer;" 
              fileurl="${fileList[i].id}" 
              fileid="${fileList[i].id}" 
              filename="${fileList[i].fileName}"
            >
              预览
            </span>`;
          html += '<span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span>';
          html += '</li>';
          box.append(html);
        }
      }
      con.append(title).append(box);
      item.append(con);
      break;
    case 'remark':
      var div = $(
        `<div class="remark">
        ${json.remark ? json.remark.replace(/(\r\n)|(\n)/g, '<br>') : ''}
        </div>`
      );
      if (json.isRed) {
        div.css('color', 'red');
      }
      if (json.isBold) {
        div.css('fontWeight', 'bold');
      }
      item.append(div);
      break;
    case 'interworkCom':
      var con = $('<div class="interworkCom"></div>');
      var box = $(`<div class="interworkBox" key="${json.fieldName}"></div>`);
      interworkComData[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : [];
      if (!readonly) {
        var search = $('<div class="interworkSearch"></div>');
        var inp = $(
          '<input class="layui-input search" placeholder="请输入住院号">'
        );
        var btn = $(
          `<button 
            class="layui-btn layui-btn-normal interworkBtn" 
            keyid="${json.keyId}"
          >查询</button>`
        );
        search.append(inp);
        search.append(btn);
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'interworkSick':
      var con = $(`<div class="interworkSick" key="${json.fieldName}"></div>`);
      var box = $(
        `<div class="interworkSickBox" key="${json.fieldName}"></div>`
      );
      interworkSick[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : null;
      if (!readonly) {
        var search = $('<div class="interworkSickSearch"></div>');
        var sel = $('<div class="interworkSickSel"></div>');
        var $sel = $('<select class="interworkSickSelFilter"></select>');
        $sel.append('<option value="zyh">住院号</option>');
        $sel.append('<option value="sfzh">身份证号</option>');
        $sel.append('<option value="zlkh">诊疗卡号</option>');
        sel.append($sel);
        var inp = $(
          '<input class="layui-input search" placeholder="请输入住院号">'
        );
        var btn = $(
          `<button 
            class="layui-btn layui-btn-normal interworkSickBtn" 
            keyid="${json.keyId}"
          >查询</button>`
        );
        search.append(sel);
        search.append(inp);
        search.append(btn);
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'interworkSettle':
      var con = $('<div class="interworkCom"></div>');
      var box = $(
        `<div class="interworkSettleBox" key="${json.fieldName}"></div>`
      );
      interworkSettle[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : null;
      if (!readonly) {
        var search = $('<div class="interworkSearch"></div>');
        var inp = $(
          '<input class="layui-input search" placeholder="请输入住院号">'
        );
        var btn = $(
          `<button 
            class="layui-btn layui-btn-normal interworkSettleBtn" 
            keyid="${json.keyId}"
          >查询</button>`
        );
        search.append(inp);
        search.append(btn);
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'interworkPay':
      var con = $('<div class="interworkCom"></div>');
      var box = $(
        `<div class="interworkPayBox" key="${json.fieldName}"></div>`
      );
      interworkPay[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : null;
      if (!readonly) {
        var search = $('<div class="interworkSearch"></div>');
        var inp = $(
          '<input class="layui-input search" placeholder="请输入住院号">'
        );
        var btn = $(
          `<button 
            class="layui-btn layui-btn-normal interworkPayBtn" 
            keyid="${json.keyId}"
          >查询</button>`
        );
        search.append(inp);
        search.append(btn);
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'interworkHosPro':
      var con = $('<div class="interworkCom"></div>');
      var box = $(
        `<div class="interworkHosProBox" key="${json.fieldName}"></div>`
      );
      interworkHosPro[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : null;
      if (!readonly) {
        var search = $('<div class="interworkSearch"></div>');
        var inp = $(
          '<input class="layui-input search" placeholder="请输入住院号">'
        );
        var btn = $(
          `<button 
            class="layui-btn layui-btn-normal interworkHosProBtn" 
            keyid="${json.keyId}"
          >查询</button>`
        );
        search.append(inp);
        search.append(btn);
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'interworkTest':
      var con = $('<div class="interworkTestCom"></div>');
      var box = $(
        '<div class="interworkTestBox" key="' + json.fieldName + '"></div>'
      );
      interworkTest[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : null;
      if (!readonly) {
        var search = $('<div class="interworkTestSearch"></div>');
        /* var inp = $('<input class="layui-input search" placeholder="请输入医嘱名称">');*/
        var btn = $(
          '<button class="layui-btn layui-btn-normal interworkTestBtn" keyid="' +
            json.keyId +
            '">查询</button>'
        );
        search.append(btn);
        /* search.append(inp);*/
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'inPatientOrder':
      var con = $('<div class="inPatientOrderCom"></div>');
      var box = $(
        '<div class="inPatientOrderBox" key="' + json.fieldName + '"></div>'
      );
      inPatientOrder[json.fieldName] = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : null;
      if (!readonly) {
        var search = $('<div class="inPatientOrderSearch"></div>');
        var inp = $(
          '<input class="layui-input search" placeholder="请输入住院号" style="width:80%;float:left">'
        );
        var btn = $(
          '<button style="float:right" class="layui-btn layui-btn-normal inPatientOrderBtn" keyid="' +
            json.keyId +
            '">查询</button>'
        );
        search.append(inp);
        search.append(btn);
        con.append(search);
      }
      con.append(box);
      item.append(con);
      required && item.append('<span class="required">*</span>');
      break;
    case 'workorderSetting':
      // 处理科室
      var sel;
      workorderSetting[json.fieldName] = formData[json.fieldName]
        ? formData[json.fieldName]
        : null;
      if (json.dataSource == 8) {
        sel = $(
          `<select 
            name="${json.fieldName}" 
            class="workorderSettingDeptSel" 
            keyid="${json.keyId}"
          ></select>`
        );
        if (formData[json.fieldName]) {
          $('#cs').text(formData[json.fieldName]);
          sel.val(formData[json.fieldName]);
        }
        item.append(sel);
        formItemPropsSet(item, json);
      }
      if (json.dataSource == 9) {
        sel = $(
          `<div 
              class="order-required-info-box faultTypeBox" 
              style="position: relative"
            >
              <input 
                type="text" 
                disabled="true" 
                name="fkFaultTypeName" 
                class="layui-input" 
                id="faultTypeBoxName" 
                autocomplete="off" 
                maxlength="30" 
              />
              <input 
                type="hidden" 
                name="${json.fieldName}" 
                class="layui-input" 
                keyid="${json.keyId}"
              />
            </div>`
        );

        if (formData[json.fieldName]) {
          let _data = [];
          $.ajax({
            url: `/ts-worksheet/faultType/selectOne/${
              formData[json.fieldName]
            }`,
            method: 'get',
            async: false,
            success: function (res) {
              _data = res.object || [];
            },
            error: function (res) {},
          });
          lxWorkorderSetting[json.fieldName] = _data.categoryName;
          $(sel).find('#faultTypeBoxName').val(_data.categoryName);
        }
        item.append(sel);
        required && item.append('<span class="required">*</span>');
        formItemPropsSet(item, json);
      }
      break;
    case 'childFormCount':
      var con = $(`<div data-form-key-id="${json.keyId}">0</div>`);
      childFormCountList.push({
        keyId: json.keyId,
        tableId: json.tableId,
        sumField: json.sumField,
      });
      item.append(con);
      break;
    case 'childForm':
      if (!json.tableId) {
        break;
      }
      let childFormRes = {};
      $.ajax({
        url: '/ts-form/dpTable/findById/' + json.tableId,
        type: 'post',
        async: false,
        success: function (res) {
          childFormRes = res;
        },
      });
      if (!childFormRes.success) {
        break;
      }
      let childFormDetail = childFormRes.object,
        {
          fields: cFormColumns = [],
          tableComment,
          editLine,
          showType,
        } = childFormDetail;
      // 获取流程节点 子表单权限控制
      if (
        (uriParams.type == 'confirm' || uriParams.type == 'countersign') &&
        uriParams.role == 'deal'
      ) {
        !childFormLimitColumns &&
          $.ajax({
            url: '/ts-workflow/workflow/form/selectChildFormFieldList',
            type: 'post',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
              wfDefinitionId: params.baseData.wfDefinitionId,
              wfStepId: uriParams.approverCurrentStepNo,
              formId: params.baseData.formId,
            }),
            async: false,
            success: function (res) {
              if (res.success && res.object) {
                childFormLimitColumns = res.object;
              }
            },
          });
        childFormLimitColumns
          .filter((colItem) => colItem.tableId == json.tableId)
          .map((limitSetting) => {
            let columnIndex = cFormColumns.findIndex(
              (colItem) => colItem.fieldName == limitSetting.fieldName
            );
            if (columnIndex >= 0) {
              cFormColumns[columnIndex].isNull = limitSetting.isRequired == 1; //isRequired是否必填1-必填;2-不必填
              cFormColumns[columnIndex].isReadOnly = limitSetting.isEditor == 2; //isEditor是否编辑1-可编辑;2-只读
              cFormColumns[columnIndex].isHide = limitSetting.isShow == 2; // isShow是否查看 1-是;2-否
            }
          });
      }
      let initUrlParams = getParmas();
      let exportBtn = `
        <button
          name="childFormImportBtn" 
          class="layui-btn no-print"
        >导入</button>
      `;
      if (initUrlParams.currentStepNo != 'start' && editLine == '0') {
        exportBtn = '';
      }
      let tableAction = `
      ${exportBtn}
        <button 
          name="childFormExportBtn" 
          class="layui-btn normal-btn no-print"
        >下载模板</button>`,
        cFormTitle = `<div class="child-form-top-content">
            <span class="child-form-title"></span>
            <div>
              ${!json.isReadonly ? tableAction : ''}
            </div>
          </div>`,
        cFormTableHead = [];
      cFormColumns.sort((pre, next) => {
        let preIndex = pre.seq,
          nextIndex = next.seq;
        return preIndex < nextIndex ? -1 : preIndex == nextIndex ? 0 : 1;
      });
      childFormDetail.fields = cFormColumns = cFormColumns.filter(
        (colItem) =>
          colItem.pcShow == 1 &&
          (!colItem.isHide ||
            initUrlParams.type == 'see' ||
            (uriParams.role == 'self' && uriParams.type == 'confirm'))
      );
      cFormColumns.map((colItem = {}, index) => {
        let required = colItem.isNull == 1 ? 'required' : '',
          { fieldWidth = '', fieldType } = colItem;
        // 判断子表单展现形式 1-表格 2-卡片
        if (showType != 2) {
          fieldType == 'DATEPICKER' &&
            fieldWidth < 125 &&
            (fieldWidth = json.isReadonly ? 105 : 125);
          let minWidth = fieldWidth ? `style="width: ${fieldWidth}px"` : '';
          cFormTableHead.push(
            `<th ${minWidth}>
              <span class="cell ${required}">${colItem.remark}</span>
            </th>`
          );
        }
      });
      item.append(cFormTitle);
      if (showType == 2) {
        item.append(
          `<div 
            data-child-form-key="${json.keyId}"
            data-child-form-table-id="${json.tableId}"
            class="card-table child-form-table"  
            data-child-form-type="${showType}"
            ts-required="childForm"
            lay-verify="childForm"
          ></div>`
        );
      } else {
        item.append(
          `<table
            data-child-form-key="${json.keyId}"
            data-child-form-type="${showType}"
            data-child-form-table-id="${json.tableId}"
            class="child-form-table"
            ts-required="childForm"
            lay-verify="childForm"
          >
            <thead>
              <tr>
                ${cFormTableHead.join('')}
                ${
                  !json.isReadonly
                    ? '<th class="child-form-table-action no-print"></th>'
                    : ''
                }
              </tr>
            </thead>
            <tbody></tbody>
          </table>`
        );
      }
      if (
        !json.isReadonly &&
        !initUrlParams.type &&
        initUrlParams.currentStepNo == 'start' &&
        !initUrlParams.businessId
      ) {
        let newTr = computeNewChildFormItem(showType, cFormColumns);
        let sequenceLength = $('.child-form-table', item).find(
          '.sequence-num'
        ).length;
        $('.sequence-num', newTr).text(sequenceLength + 1);
        $('.child-form-table', item).append(newTr);
      }
      if (
        initUrlParams.currentStepNo != 'start' ||
        (initUrlParams.currentStepNo == 'start' && initUrlParams.businessId)
      ) {
        $.ajax({
          url: '/ts-form/dpTable/getChildDataList',
          method: 'get',
          async: false,
          data: {
            tableId: childFormDetail.id,
            fieldName: json.fieldName,
            businessId: uriParams.businessId,
          },
          success(res) {
            if (res.success) {
              let errorList = [];
              res.object.map((row) => {
                let newTr = computeNewChildFormItem(
                  showType,
                  cFormColumns,
                  row,
                  json.isReadonly,
                  errorList,
                  editLine
                );
                let sequenceLength = $('.child-form-table', item).find(
                  '.sequence-num'
                ).length;
                $('.sequence-num', newTr).text(sequenceLength + 1);
                $('.child-form-table', item).append(newTr);
              });
              if (!json.isReadOnly && errorList.length) {
                layer.msg(
                  '存在系统无法匹配的数据，已重置，如有需要，请自行更改'
                );
              }
            }
          },
        });
      }
      let trList = $(
        showType == 2
          ? '.child-form-table .child-form-table-cell'
          : '.child-form-table tbody tr',
        item
      );
      $('[name="childFormDeleteBtn"]', trList[0])[
        trList.length == 1 ? 'addClass' : 'remove'
      ]('none');
      itemMap[json.keyId] &&
        (itemMap[json.keyId].childFormDetail = childFormDetail);
      break;
    case 'essentialDrugDic':
      if (!readonly) {
        var essDrugAddBtn = $(
          `<button 
            name="essDrugDicAddBtn" 
            data-key-id="${json.keyId}" 
            class="layui-btn no-print" 
            style="margin-bottom: 8px;"
          >添加药品</button>`
        );
        item.append(essDrugAddBtn);
      }
      var essDrugColumns = [
          '药品名称',
          '规格',
          '药品生产企业名称',
          '单位',
          '数量',
          '备注',
        ].map(
          (label) =>
            `<th><span class="cell ${
              ['单位', '数量'].includes(label) ? 'required' : ''
            }">${label}</span></th>`
        ),
        actionTd = !readonly ? '<td class="action-col no-print"></td>' : '';
      var tableContent = $(
        `<table
          name="essDrugDicSelectedTable"
          class="inside-table"
          data-key-id="${json.keyId}"
          style="border-collapse: collapse;"
          border="1"
          lay-verify="essentialDrugDic"
        >
          <thead>
            <tr>
              ${essDrugColumns.join('')}
              ${actionTd}
            </tr>
          </thead>
          <tbody>
          </tbody>
        </table>`
      );
      item.append(tableContent);
      var essDrugData = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : [];
      formData[json.fieldName] = essDrugData;
      renderEssDrugDicSeletedTable(essDrugData, json, tableContent);
      break;
    case 'medicalSupplieDic':
      if (!readonly) {
        var medicalSupplieDicAddBtn = $(
          `<button 
                name="medicalSupplieDicAddBtn" 
                data-key-id="${json.keyId}" 
                class="layui-btn no-print" 
                style="margin-bottom: 8px;"
              >添加物品</button>`
        );
        item.append(medicalSupplieDicAddBtn);
      }
      var drugProcurementColumns = [
          '品名',
          '规格',
          '单位',
          '生产厂家',
          '供货单位',
          '采购数量',
          '预算单价',
          '预算金额',
          '采购方式',
        ].map(
          (label) => `<th><span class="cell required">${label}</span></th>`
        ),
        actionTd = !readonly ? '<td class="action-col no-print"></td>' : '';
      var tableContent = $(
        `<table
              name="medicalSupplieDicSelectedTable"
              class="inside-table"
              data-key-id="${json.keyId}"
              style="border-collapse: collapse;"
              border="1"
              lay-verify="medicalSupplieDic"
            >
              <thead>
                <tr>
                  ${drugProcurementColumns.join('')}
                  <th><span class="cell">编码及备注</span></th>
                  ${actionTd}
                </tr>
              </thead>
              <tbody>
              </tbody>
            </table>`
      );
      item.append(tableContent);
      var medicalSupplieDicData = formData[json.fieldName]
        ? JSON.parse(formData[json.fieldName])
        : [];
      formData[json.fieldName] = medicalSupplieDicData;
      renderMedicalSupplieDicSeletedTable(
        medicalSupplieDicData,
        json,
        tableContent
      );
      break;
    case 'leaveStatistics':
      item.append(renderleaveStatistics(json));
      break;
    case 'operationItem':
      item.append(renderOperationItem(json));
      let initUrl = getParmas();
      // 职称字段特殊验证
      let technical = formData.zyjszc7;
      let Check = ['主治医师', '经治医师', '主治(主管)医师'];
      if (initUrl.currentStepNo != 'start') {
        let dataList = JSON.parse(formData[json.fieldName]);
        for(let i=0;i<dataList.length;i++) {
          if (initUrl.type == 'confirm' && Check.indexOf(technical) > -1) {
            if (dataList[i].authLvHosp == 4) {
              let inps = $(item).find(`tr[data-key-id=${dataList[i].id}]`);
              if (inps) {
                let tdList = $(inps).find(`td`);
                for(var j=3;j<=9;j++) {
                  $(tdList[j]).attr('style','color: red');
                }
              }
            }
          }
        }
      }
      break;
    case 'deliveryInspection':
      item.append(renderDeliveryInspection(json));
      break;
    default:
      break;
  }
  const itemChildren = $(item).find('textarea');
  if (itemChildren.is('textarea') && itemChildren.attr('autoHeight')) {
    makeExpandingArea(itemChildren[0]);

    if (json.length) {
      addLengthTips(itemChildren[0], item, json.length);
    }
  }

  return item;
}
function renderleaveStatistics(json) {
  let htmls = '';
  let tableHeadList = [];
  let tableData = [];
  let year = new Date().getFullYear();
  let employeeCode = '';
  let initUrl = getParmas();
  if (initUrl.currentStepNo == 'start') {
    employeeCode = userInfo.empCode;
  } else {
    employeeCode = wfData.createUser;
  }
  $.ajax({
    url: '/ts-hrms/api/leaveStatistics/getleaveStatisticsTableHeadCols',
    type: 'post',
    async: false,
    data: {
      isWorkflow: 'Y',
    },
    success: function (res) {
      if (res.success && res.object) {
        res.object.push({
          label: '合计',
          name: 'total',
          hidden: false,
        });
        tableHeadList = res.object;
      }
    },
  });
  $.ajax({
    url: '/ts-hrms/api/leaveStatistics/getleaveStatisticsDataList',
    type: 'post',
    async: false,
    data: {
      isWorkflow: 'Y',
      startLeaveMonth: year + '-01',
      endLeaveMonth: year + '-12',
      employeeCode,
    },
    success: function (res) {
      tableData = res.rows || [];
      for (var i = 0; i < tableData.length; i++) {
        let total = 0;
        for (let key in tableData[i]) {
          if (typeof tableData[i][key] == 'number') {
            total += tableData[i][key];
          }
        }
        tableData[i].total = total;
      }
    },
  });
  let noData = false;
  // 处理数据 假期类型对应的数据全是0的隐藏列
  for (var i = 0; i < tableHeadList.length; i++) {
    if (!tableHeadList[i].hidden) {
      let canShow = false;
      for (var j = 0; j < tableData.length; j++) {
        let vPd = tableData[j][tableHeadList[i].name];

        // 添加可休年假表头
        if (
          (typeof vPd == 'number' && vPd > 0) ||
          (vPd instanceof Array && vPd.length && tableHeadList[i].name === 'kxnj')
        ) {
          canShow = true;
          noData = true;
        }
      }
      if (!canShow) {
        tableHeadList[i].hidden = true;
      }
    }
  }
  htmls += `<table class="child-form-table" data-key-id="${json.keyId}">`;
  // 表头
  htmls += '<thead><tr>';
  for (var i = 0; i < tableHeadList.length; i++) {
    if (!tableHeadList[i].hidden) {
      htmls += `<th><span class="cell">${tableHeadList[i].label}</span></th>`;
    }
  }
  htmls += '</tr></thead>';
  // 表数据
  htmls += '<tbody>';
  for (var i = 0; i < tableData.length; i++) {
    htmls += '<tr>';
    for (var j = 0; j < tableHeadList.length; j++) {
      if (!tableHeadList[j].hidden) {
        let templateStr = `<td><span class="cell">${tableData[i][tableHeadList[j].name]}</span></td>`;

        // 添加可休年假数据展示
        if (tableHeadList[j].name === 'kxnj') {
          const kxnjStr = (tableData[i].kxnj || [])
              .map(({ year, remainingDays }) => `<div style="line-height: 18px">${year}年<span class="remainingDays">${remainingDays}</span>天</div>`).join('')

          templateStr = `<td><span class="cell">${kxnjStr}</span></td>`;
        }

        htmls += templateStr;
      }
    }
    htmls += '</tr>';
  }
  htmls += '</tbody>';
  htmls += '</table>';
  if (!noData) {
    htmls += '<span>暂无请假数据</span>';
  }
  return htmls;
}
function renderOperationItem(json) {
  // 如果不是发起就需要渲染了
  let initUrl = getParmas();
  var htmls = '';
  if (initUrl.currentStepNo == 'start' || initUrl.currentStepNo == 'startAgain') {
    htmls += '<span class="required">*</span>';
    htmls += `<span class="operationItemButton layui-btn layui-btn-normal" keyId="${json.keyId}" style="margin-top:4px;">选择手术</span>`;
    htmls += `<span class="operationTableExcl layui-btn layui-btn-normal" keyId="${json.keyId}" name="${json.fieldName}" style="margin-top:4px;float: right;">导出Excel</span>`;
    htmls += `<input class="layui-input" type="hidden" value='${formData[json.fieldName] || ""}' autocomplete="off" name="${json.fieldName}" data-key-id="${json.keyId}" lay-verify="required" ts-required="required" />`;
    htmls += `<table class="child-form-table" data-key-id="${json.keyId}" style="margin-top:4px;">
        <thead><tr>
        <th><span class="cell">序号</span></th>
        <th><span class="cell">手术类型</span></th>
        <th><span class="cell">手术编码</span></th>
        <th><span class="cell">申请授权名称</span></th>
        <th><span class="cell">院内手术级别</span></th>
        <th><span class="cell">国家手术级别</span></th>
        <th><span class="cell">是否新技术项目</span></th>
        <th><span class="cell">是否限制性项目</span></th>
        </tr></thead>`;
    htmls += '<tbody>';
    if (formData[json.fieldName]) {
      let dataList = JSON.parse(formData[json.fieldName]);
      for(let i = 0; i< dataList.length; i++) {
        let tr = `<tr data-key-id=${dataList[i].id}>
        <td><span class="cell">${i + 1}</span></td>
        <td><span class="cell">${dataList[i].quaAuthTypeName}</span></td>
        <td><span class="cell">${dataList[i].itemCode}</span></td>
        <td><span class="cell">${dataList[i].itemName}</span></td>
        <td><span class="cell">${dataList[i].authLvHospName}</span></td>
        <td><span class="cell">${dataList[i].authLvNatName}</span></td>
        <td><span class="cell">${dataList[i].isNewTech}</span></td>
        <td><span class="cell">${dataList[i].isRstdTech}</span></td>
        </tr>
        `;
        htmls += tr;
      }
    }
    htmls += '</tbody></table>';
    return htmls;
  } else {
    htmls += `<span class="operationTableExcl layui-btn layui-btn-normal" keyId="${json.keyId}" name="${json.fieldName}" style="margin-top:4px;float: right;">导出Excel</span>`;
    htmls += `<table class="child-form-table operationTable" data-key-id="${json.keyId}" data-field-name="${json.fieldName}">`;
    htmls += renderOperationEditTable(JSON.parse(formData[json.fieldName]), json);
    htmls += '</table>';
    htmls += `<input class="layui-input" type="hidden" autocomplete="off" name="${json.fieldName}" data-key-id="${json.keyId}" />`;
    return htmls;
  }
}
function renderDeliveryInspection(json) {
  // 如果不是发起就需要渲染了
  let initUrl = getParmas();
  var htmls = '';
  if (initUrl.currentStepNo == 'start' || initUrl.currentStepNo == 'startAgain') {
    htmls += '<span class="required">*</span>';
    htmls += `<span class="DeliveryButton layui-btn layui-btn-normal" keyId="${json.keyId}" style="margin:4px 0;float: right">选择项目</span>`;
    let dataValue = '';
    if (formData[json.fieldName]) {
      dataValue = `value='${formData[json.fieldName]}'`;
    }
    htmls += `<input class="layui-input" type="hidden" ${dataValue} autocomplete="off" name="${json.fieldName}" data-key-id="${json.keyId}" lay-verify="required" ts-required="required" />`;
  } else {
    htmls += `<input class="layui-input" type="hidden" value='${formData[json.fieldName]}' autocomplete="off" name="${json.fieldName}" data-key-id="${json.keyId}" />`;
  }
  htmls += `<table class="child-form-table" data-key-id="${json.keyId}" style="margin-top:4px;">
      <thead><tr>
      <th><span class="cell">序号</span></th>
      <th><span class="cell">检验项目</span></th>
      <th><span class="cell">样本类型</span></th>
      <th><span class="cell">样本量</span></th>
      <th><span class="cell">外送检验机构</span></th>
      <th><span class="cell">费用</span></th>
      </tr></thead>`;
  htmls += '<tbody>';
  if (formData[json.fieldName]) {
    let dataList = JSON.parse(formData[json.fieldName]);
    for(let i = 0; i< dataList.length; i++) {
      let tr = `<tr data-key-id=${dataList[i].id}>
      <td><span class="cell">${i + 1}</span></td>
      <td><span class="cell">${dataList[i].projectName}</span></td>
      <td><span class="cell">${dataList[i].sampleType}</span></td>
      <td><span class="cell">${dataList[i].sampleDose}</span></td>
      <td><span class="cell">${dataList[i].testOrg}</span></td>
      <td><span class="cell">${dataList[i].cost}</span></td>
      </tr>
      `;
      htmls += tr;
    }
  }
  htmls += '</tbody></table>';
  return htmls;
}
function renderOperationEditTable(data, json) {
  data.forEach(e => {
    if (e.auditFlag === undefined) {
      e.auditFlag = '1';
    }
    if (e.auditScr === undefined) {
      e.auditScr = '';
    }
  })
  $(`input[data-key-id=${json.keyId}]`).val(JSON.stringify(data));
  formData[json.fieldName] = JSON.stringify(data);
  let disabled = 'disabled';
  if (uriParams.type == 'confirm' && uriParams.role == 'deal') {
    disabled = '';
  }
  var htmls = '';
  // 表头
  htmls += `<thead><tr>`;
  htmls += `<th><span class="cell">序号</span></th>`;
  htmls += `<th><span class="cell">授权结果</span></th>`;
  htmls += `<th><span class="cell">原因</span></th>`;
  htmls += `<th><span class="cell">手术类型</span></th>`;
  htmls += `<th><span class="cell">手术编码</span></th>`;
  htmls += `<th><span class="cell">申请授权名称</span></th>`;
  htmls += `<th><span class="cell">院内手术级别</span></th>`;
  htmls += `<th><span class="cell">国家手术级别</span></th>`;
  htmls += `<th><span class="cell">是否新技术项目</span></th>`;
  htmls += `<th><span class="cell">是否限制性项目</span></th>`;
  htmls += '</tr></thead>';
  // 表数据
  htmls += '<tbody>';
  for (var i = 0; i < data.length; i++) {
    let required = '';
    let span = '';
    let style = '';
    let color = '';
    if (data[i].auditFlag == '0' && disabled == '') {
      required = 'lay-verify="required" ts-required="required"'
      span = '<span class="required">*</span>';
      style = 'style="display: flex;position: relative;"';
    }
    if (data[i].auditFlag == '1' && disabled == '') {
      required = 'lay-verify ts-required'
    }
    if (data[i].auditFlag == '0') {
      color = 'style="color: red"';
    }
    htmls += `<tr data-key-id=${data[i].id}><td><span class="cell">${i + 1}</span></td>`;
    htmls +=
    `<td>
      <span class="cell auditFlag">
        <input data-key="auditFlag" name="${data[i].id}" type="radio" title="批准授权" value="1" ${data[i].auditFlag == '1' ? 'checked' : ''} ${disabled}>
        <input data-key="auditFlag" name="${data[i].id}" type="radio" title="不批准授权" value="0" ${data[i].auditFlag == '0' ? 'checked' : ''} ${disabled}>
      </span>
    </td>`;
    htmls += `<td><span class="cell auditScr" ${style}><input type="text" data-key="auditScr" nameId="${data[i].id}" value="${data[i].auditScr}" ${disabled} ${required} />${span}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].quaAuthTypeName}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].itemCode}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].itemName}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].authLvHospName}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].authLvNatName}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].isNewTech}</span></td>`;
    htmls += `<td><span class="cell" ${color}>${data[i].isRstdTech}</span></td>`;
    htmls += '</tr>';
  }
  htmls += '</tbody>';
  return htmls;
}
/**@desc 渲染 基本药品数据字典 表格 */
function renderEssDrugDicSeletedTable(dataList = [], setting, table) {
  let keyId = setting.keyId,
    readonly = setting.isReadonly ? true : false,
    dataDomList = dataList.map((data) => {
      if (typeof data.drugList === 'string') {
        data.drugList = JSON.parse(data.drugList);
      }
      let {
          drugId,
          chemicalName,
          spec,
          factName,
          drugList,
          unitName = '',
          num = '',
          memo = '',
        } = data,
        list = [chemicalName, spec, factName]
          .map((label) => `<td><div class="cell">${label}</div></td>`)
          .join(''),
        actionTd = !readonly
          ? `<td class="action-col no-print">
                <div 
                data-key-id="${drugId}" 
                class="oaicon oa-icon-cuowu1 action-btn" 
                style="color: red;"
                ></div>
            </td>`
          : '',
        trDom = $(`<tr>${list}</tr>`),
        unitOption = drugList
          .map((mp) => {
            let selected = mp.unitName === unitName ? 'selected' : '';
            return `<option ${selected} id="${mp.unitId}" value="${mp.unitName}">${mp.unitName}</option>`;
          })
          .join('');
      //备注信息去掉空格
      memo = memo.replace(/\s/g, '');
      // 单位下拉
      selectInput = $(
        setting.isReadonly
          ? `<td><div class="cell" data-key-name="unitName">${unitName}</div></td>`
          : `<td>
              <div class="cell">
                <select data-key-name="unitName" autocomplete="off" placeholder="请选择">
                    <option value="">请选择</option>
                    ${unitOption}
                </select>
              </div>
            </td>`
      );
      selectInput.find('[data-key-name="unitName"]').val(unitName);
      trDom.append(selectInput);
      // 数量输入框
      numberInput = $(
        setting.isReadonly
          ? `<td><div class="cell" data-key-name="num">${num}</div></td>`
          : `<td>
              <div class="cell">
                <input
                  class="layui-input"
                  autocomplete="off"
                  data-key-name="num"
                  type="text"
                  placeholder="请输入数量"
                >
              </div>
            </td>`
      );
      numberInput.find('[data-key-name="num"]').val(num);
      numberInput
        .find('input')
        .off('input')
        .on('input', function () {
          let value = $(this).val(),
            matchList =
              value.match(/^(([1-9]{1}\d*)|(0{1}))((\.\d{1,2}|\.))?/) || [];
          $(this).val(matchList[0] || '');
        });
      numberInput.find('input').on('blur', function () {
        let value = $(this).val();
        value && $(this).val(parseFloat(value));
      });
      trDom.append(numberInput);
      // 备注输入框
      let remarkDom = $(
        setting.isReadonly
          ? `<td><div class="cell" data-key-name="memo">${memo}</div></td>`
          : `<td>
              <div class="cell">
                <textarea
                  type="textarea"
                  class="layui-textarea"
                  autocomplete="off"
                  data-key-name="memo"
                  placeholder="请输入备注"
                >
              </div>
            </td>`
      );
      remarkDom.find('[data-key-name="memo"]').val(memo);
      remarkDom.find('textarea').val(memo);
      trDom.append(remarkDom);

      trDom.append(actionTd);
      return trDom;
    });
  tableBody = table
    ? $('tbody', table)
    : $(`table[name="essDrugDicSelectedTable"][data-key-id="${keyId}"] tbody`);
  tableBody.empty();
  tableBody.append(dataDomList);

  layForm.render('select');
  $('.action-btn', tableBody).funs('click', function (e) {
    e.stopPropagation();
    layer.confirm('是否确认删除？', (layerIndex) => {
      layer.close(layerIndex);

      let keyId = $(this).attr('data-key-id'),
        keyIndex = formData[setting.fieldName].findIndex(
          (item) => item.drugId == keyId
        );
      keyIndex >= 0 && formData[setting.fieldName].splice(keyIndex, 1);
      $(this).closest('tr').remove();
    });
  });
}
/**@desc 渲染 基本药品、耗材、试剂数据字典 表格 */
function renderMedicalSupplieDicSeletedTable(dataList = [], setting, table) {
  let keyId = setting.keyId,
    readonly = setting.isReadonly ? true : false,
    dataDomList = dataList.map((data) => {
      let {
          drugId,
          chemicalName,
          spec,
          factName,
          supplierName,
          unitName,
          num = '',
          buyPrice,
          total = '',
          purchaseType = '',
          memo = '',
        } = data,
        listLable = [
          {
            prop: 'chemicalName',
            lable: '品名',
          },
          {
            prop: 'spec',
            lable: '规格',
          },
          {
            prop: 'unitName',
            lable: '单位',
          },
          {
            prop: 'factName',
            lable: '生产厂家',
          },
          {
            prop: 'supplierName',
            lable: '供货单位',
          },
        ],
        list = [chemicalName, spec, unitName, factName, supplierName]
          .map((label, index) =>
            readonly
              ? `<td><div class="cell" data-key-name="${listLable[index].prop}">${label}</div></td>`
              : `<td>
          <div class="cell">
            <input
              class="layui-input"
              autocomplete="off"
              data-key-name="${listLable[index].prop}"
              type="text"
              value="${label}"
              placeholder="请输入${listLable[index].lable}"
            >
          </div>
        </td>`
          )
          .join('');
      trDom = $(`<tr>${list}</tr>`);
      // 数量输入框
      numberDom = $(
        readonly
          ? `<td><div class="cell" data-key-name="num">${num}</div></td>`
          : `<td>
              <div class="cell">
                <input
                  class="layui-input"
                  autocomplete="off"
                  data-key-name="num"
                  type="number"
                  placeholder="请输入数量"
                >
              </div>
            </td>`
      );
      numberDom.find('[data-key-name="num"]').val(num);
      numberDom
        .find('input')
        .off('input')
        .on('input', function () {
          let value = $(this).val(),
            matchList =
              value.match(/^(([1-9]{1}\d*)|(0{1}))((\.\d{1,2}|\.))?/) || [];
          $(this).val(matchList[0] || '');
        });

      trDom.append(numberDom);
      //单价输入框
      let priceDom = $(
        readonly
          ? `<td><div class="cell" data-key-name="buyPrice">${buyPrice}</div></td>`
          : `<td>
            <div class="cell">
              <input
                class="layui-input"
                autocomplete="off"
                data-key-name="buyPrice"
                type="number"
                value="${buyPrice}"
                placeholder="请输入预算单价"
              >
            </div>
          </td>`
      );
      trDom.append(priceDom);
      // 金额输入框
      let totalDom = $(
        readonly
          ? `<td><div class="cell" data-key-name="total">${total}</div></td>`
          : `<td>
            <div class="cell">
              <input
                class="layui-input"
                autocomplete="off"
                data-key-name="total"
                type="text"
                value="${total}"
                placeholder="请输入预算金额"
              >
            </div>
          </td>`
      );

      numberDom.find('input').on('blur', function () {
        let numVal = $(this).val(),
          priceVal = priceDom.find('input').val(),
          totalVal =
            numVal && priceVal
              ? $.addMultiplyPrecision(Number(numVal), Number(priceVal))
              : '';
        totalDom.find('input').val(totalVal);
      });
      priceDom.find('input').on('blur', function () {
        let priceVal = $(this).val(),
          numVal = numberDom.find('input').val();
        totalVal =
          numVal && priceVal
            ? $.addMultiplyPrecision(Number(numVal), Number(priceVal))
            : '';
        totalDom.find('input').val(totalVal);
      });
      trDom.append(totalDom);
      // 采购方式输入框
      let typeDom = readonly
        ? `<td><div class="cell" data-key-name="purchaseType">${purchaseType}</div></td>`
        : `<td>
            <div class="cell">
              <input
                class="layui-input"
                autocomplete="off"
                data-key-name="purchaseType"
                type="text"
                value="${purchaseType}"
                placeholder="请输入采购方式"
              >
            </div>
          </td>`;
      trDom.append(typeDom);
      //备注信息去掉空格
      memo = memo.replace(/\s/g, '');
      // 备注输入框
      let remarkDom = $(
        readonly
          ? `<td><div class="cell" data-key-name="memo">${memo}</div></td>`
          : `<td>
              <div class="cell">
                <input
                  type="text"
                  class="layui-input"
                  autocomplete="off"
                  data-key-name="memo"
                  placeholder="请输入备注"
                >
              </div>
            </td>`
      );
      remarkDom.find('[data-key-name="memo"]').val(memo);
      remarkDom.find('textarea').val(memo);
      trDom.append(remarkDom);
      // 操作
      let actionDom = !readonly
        ? `<td class="action-col no-print">
                <div 
                data-key-id="${drugId}" 
                class="oaicon oa-icon-cuowu1 action-btn" 
                style="color: red;"
                ></div>
            </td>`
        : '';
      trDom.append(actionDom);
      return trDom;
    });
  tableBody = table
    ? $('tbody', table)
    : $(
        `table[name="medicalSupplieDicSelectedTable"][data-key-id="${keyId}"] tbody`
      );
  tableBody.empty();
  tableBody.append(dataDomList);

  layForm.render('select');
  $('.action-btn', tableBody).funs('click', function (e) {
    e.stopPropagation();
    layer.confirm('是否确认删除？', (layerIndex) => {
      layer.close(layerIndex);

      let keyId = $(this).attr('data-key-id'),
        keyIndex = formData[setting.fieldName].findIndex(
          (item) => item.drugId == keyId
        );
      keyIndex >= 0 && formData[setting.fieldName].splice(keyIndex, 1);
      $(this).closest('tr').remove();
    });
  });
}
//字段只读、必填属性提取
// 目前只支持基础组件，其他组件暂不处理-
function formItemPropsSet($box, json) {
  var readonly = json.isReadonly ? true : false;
  var required = json.isMust ? true : false;
  switch (json.fieldType) {
    case 'input':
      $box.find('input').prop('disabled', readonly);
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('input')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('input').attr('lay-verify', '');
      }
      break;
    case 'textarea':
      $box.find('textarea').prop('disabled', readonly);
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('textarea')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('textarea').attr('lay-verify', '');
      }
      break;
    case 'number':
      $box.find('input').prop('disabled', readonly);
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('input')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('input').attr('lay-verify', '');
      }
      break;
    case 'date':
      $box.find('input').prop('disabled', readonly);
      if (readonly) {
        $box.find('input').addClass('noBorder');
      } else {
        $box.find('input').removeClass('noBorder');
      }
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('input')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('input').attr('lay-verify', '');
      }
      break;
    case 'radio':
      if (readonly) {
        $box.find('input:not(:checked)').prop('disabled', true);
      }
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('input')
          .attr({ 'lay-verify': 'otherReq', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('input').attr('lay-verify', '');
      }
      layForm.render('radio');
      break;
    case 'checkbox':
      if (readonly) {
        $box.find('input:not(:checked)').prop('disabled', true);
        $box.find('input:checked').click(function () {
          return false;
        });
      }
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('input')
          .attr({ 'lay-verify': 'otherReq', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('input').attr('lay-verify', '');
      }
      break;
    case 'select':
      $box.find('select').prop('disabled', readonly);
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('select')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('select').attr('lay-verify', '');
      }
      layForm.render('select');
      break;
    case 'file':
      break;
    case 'comment':
      break;
    case 'serialNumber':
      $box.find('input').prop('disabled', readonly);
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('input')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('input').attr('lay-verify', '');
      }
      break;
    case 'personChose':
      break;
    case 'deptChose':
      break;
    case 'hrpHyperlink':
      if (!readonly) {
        $box.find('input').prop('disabled', readonly);
        if (required) {
          $box.append('<span class="required">*</span>');
          $box
            .find('input')
            .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
        } else {
          $box.find('.required').remove();
          $box.find('input').attr('lay-verify', '');
        }
      }
      break;
    case 'fileTemplate':
      break;
    case 'remark':
      break;
    case 'interworkCom':
      break;
    case 'interworkSick':
      break;
    case 'interworkSettle':
      break;
    case 'interworkPay':
      break;
    case 'interworkHosPro':
      break;
    case 'interworkTest':
      break;
    case 'inPatientOrder':
      break;
    case 'workorderSetting':
      $box.find('select').prop('disabled', readonly);
      if (required) {
        $box.append('<span class="required">*</span>');
        $box
          .find('select')
          .attr({ 'lay-verify': 'required', 'ts-required': 'required' });
      } else {
        $box.find('.required').remove();
        $box.find('select').attr('lay-verify', '');
      }
      layForm.render('select');
      break;
  }
}
//初始化字段方法
var lshObj = [];
var fieldSerialNumberRoleId = '';

function formItemFun() {
  //来源字段设置
  var ignoreFiled = ['draft_user', 'draft_unit'];
  $.each($('[data-from]'), function (i, n) {
    var el = this;
    // if (ignoreFiled.indexOf(this.name) == -1) {
    //     $(el).prop('disabled', true);
    // }
    (function (el) {
      setSourceFieldVal(el);
    })(el);
  });

  //病人信息修改查询方式切换
  $('.interworkSickSelFilter').on('change', function () {
    var selectedOption = $(this).find('option:selected').text();
    var inp = $(this).closest('.interworkSickSel').next();
    $(inp).prop('placeholder', `请输入${selectedOption}`);
  });

  //日期选择
  $.each($('.layDate'), function (i, n) {
    var el = this;
    var format = $(this).attr('dataFormat') || 'yyyy-MM-dd';
    var type = {
      yyyy: 'year',
      'yyyy-MM': 'month',
      'yyyy-MM-dd': 'date',
      'yyyy-MM-dd HH:mm': 'datetime',
      'yyyy-MM-dd HH:mm:ss': 'datetime',
    };
    var classes = {
      'yyyy-MM-dd HH:mm': 'time_Hm',
    };
    layDate.render({
      elem: this,
      trigger: 'click',
      format: format,
      type: type[format],
      classes: classes[format],
      done: function () {
        $(el).trigger('change');
        $(el).trigger('input');
      },
    });
  });
  // 附件上传
  $.each($('.FileUploadBtn'), function (i, n) {
    var fieldName = $(this).siblings('input').attr('name');
    fileupload.init($(this), 'form', formData && formData[fieldName]);
  });
  //审批意见
  getCommentList();

  //部门选择
  $.each($('.deptChoose'), function (i, n) {
    var key = $(this).attr('name');
    zTreeSearch.init('#' + $(this).attr('id'), {
      url: common.url + '/ts-oa/thpsSysetm/getDeptList',
      type: 'get',
      checkbox: true,
      choice: true,
      condition: 'name',
      choiceType: 's',
      allCheckVal: 'Y',
      searchCheckedAll: false,
      ztreeInitFun: function (treeObj) {
        let ids = deptChoose[key].id;
        if (!ids) return;
        ids
          .split(',')
          .filter((id) => id)
          .map((id) => {
            let node = treeObj.getNodesByParam('id', id)[0];
            if (!node) return;
            treeObj.checkNode(node, true, true);
            node.children && treeObj.expandNode(node, true);
            let parentNode = node.getParentNode();
            while (parentNode) {
              treeObj.expandNode(parentNode, true);
              parentNode = parentNode.getParentNode();
            }
          });
      },
      zTreeChoice: function (nodes) {
        var ids = [],
          names = [];
        if (nodes) {
          for (var i = 0; i < nodes.length; i++) {
            ids.push(nodes[i].id);
            names.push(nodes[i].name);
          }
        }
        deptChoose[key].name = names.join(',');
        deptChoose[key].id = ids.join(',');
      },
    });
  });
  //机关代字
  $.each($('.doc_word'), function (i, n) {
    var id = $(this).attr('id');
    var json = itemMap[$(this).attr('keyid')];
    (function (id, json) {
      new $.selectPlug('#' + id, {
        url: '/ts-document/govSendDocword/getSendDocword',
        datatype: 'get',
        data: {
          processId: params.baseData.wfDefinitionId,
        },
        searchType: 'json', // 静态数据
        textName: 'wordName',
        valName: 'id',
        inpValName: 'doc_word_id',
        inpTextName: 'doc_word',
        choice: false, // 是否多选
        disabled: json.isReadonly,
        required: json.isMust ? 'required' : '',
        defaultVal: $('#' + id).attr('doc_word_id'),
        defaultText: $('#' + id).attr('doc_word'),
        callback: function (res) {},
      });
    })(id, json);
  });
  //主送人，抄送人
  $.each($('.userSelect'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    var that = this;
    (function (json, el) {
      $(el).on('click', function (e) {
        var data = {
          isCheckDept: 'Y',
          user_str: 'copy_user_name_' + json.keyId,
          user_id: 'userNameIdcopy_user_name_' + json.keyId,
          user_code: 'copy_user_' + json.keyId,
          dept_code: 'userDeptCodecopy_user_name_' + json.keyId,
        };
        $.quoteFun('/common/userSel', {
          title: '人员选择',
          data: data,
          callback: function (names, ids, codes) {
            $(`.userInp[keyid="${json.keyId}"]`).text(names);
            $(`.userInp[keyid="${json.keyId}"]`).next().text(names);
            $(`.userInp[keyid="${json.keyId}"]`).val(names);
          },
        });
      });
    })(json, that);
  });
  //文号
  $.each($('.receive_number'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    (function (json, el) {
      if (formData && formData['receive_number']) {
        return false;
      }
      $.ajax({
        url: '/ts-document/govReceivefileseq/getReceiveFileNumber',
        method: 'get',
        data: {
          processId: params.baseData.wfDefinitionId,
        },
        success: function (res) {
          if (res.success) {
            lshObj.push({
              keyId: json.keyId,
              seqId: res.object.seqId,
              number: res.object.number,
            });
            var str = '';
            str += res.object.seqName || ' ';
            str += res.object.year ? ' [' + res.object.year + '] ' : ' ';
            str += res.object.number || '';
            $(el).val(str);
          } else {
            layer.msg(res.message || '');
          }
        },
      });
    })(json, this);
  });
  //来文单位
  $.each($('.receive_unit'), function (i, n) {
    (function (el) {
      $.ajax({
        url: '/ts-document/govDocunit/getDocunitList',
        type: 'get',
        success: function (res) {
          if (res.success) {
            var opts = '<option value="">请选择</option>';
            var list = res.object || [];
            for (var i = 0; i < list.length; i++) {
              opts += `<option value="${list[i].unitWholeName}">
                  ${list[i].unitWholeName}
                </option>`;
            }
            $(el).html(opts);
            $(el).val($(el).attr('data-value'));
            layForm.render('select');
          } else {
            layer.msg(res.message || '');
          }
        },
      });
    })(this);
  });
  //流水号
  getSerialNumber();
  //数据字典
  $.each($('.dictSel'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    (function (el, json) {
      $.ajax({
        url: '/ts-basics-bottom/dictItem/getDictItemByTypeCode',
        method: 'get',
        data: {
          typeCode: json.dictSource,
        },
        success: function (res) {
          if (res.success) {
            var html = '<option value="">请选择</option>';
            var list = res.object || [];
            for (var i = 0; i < list.length; i++) {
              html += `<option value="${list[i].itemName}">
                  ${list[i].itemName}
                </option>`;
            }
            $(el).html(html);
            if (formData[json.fieldName]) {
              $(el).val(formData[json.fieldName]);
            }
            layForm.render('select');
          }
        },
      });
    })(this, json);
  });
  //接口服务
  $.each($('.interfaceServicesSel'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    (function (el, json) {
      $.ajax({
        url: json.interfaceServices,
        method: 'get',
        success: function (res) {
          if (res.success) {
            var html = '<option value="">请选择</option>';
            var list = res.object || [];
            for (var i = 0; i < list.length; i++) {
              html += `<option value="${list[i].itemValue}">
                  ${list[i].itemName}
                </option>`;
            }
            $(el).html(html);
            if (formData[json.fieldName]) {
              $(el).val(formData[json.fieldName]);
            }
            layForm.render('select');
          }
        },
      });
    })(this, json);
  });
  //处理科室
  $.each($('.workorderSettingDeptSel'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    let name = $(this).attr('name');
    (function (el, json) {
      $.ajax({
        url: '/ts-worksheet/workSheet/itemMeauList',
        method: 'get',
        success: function (res) {
          if (res.success) {
            var html = '<option value="">请选择</option>';
            var list = res.object || [];
            ksWorkorderSetting[name] = list;
            for (var i = 0; i < list.length; i++) {
              html += `<option value="${list[i].itemValue}">
                  ${list[i].itemName}
                </option>`;
            }
            $(el).html(html);
            if (formData[json.fieldName]) {
              $(el).val(formData[json.fieldName]);
            }
            layForm.render('select');
          }
        },
      });
    })(this, json);
  });
  // 手术项目
  $.each($('.operationItemButton'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    var that = this;
    (function (json, el) {
      $(el).on('click', function (e) {
        let initUrl = getParmas();
        let strList = $(`.layui-input[name=${json.fieldName}]`).val() || '';
        if (initUrl.currentStepNo == 'startAgain' && strList == '') {
          $(`.layui-input[name=${json.fieldName}]`).val(formData[json.fieldName]);
          strList = formData[json.fieldName];
        }
        var data = strList == '' ? [] : JSON.parse(strList);
        $.quoteFun('/common/operationSel', {
          title: '手术项目选择',
          data: data,
          callback: function (dataList, ids) {
            // 去重验证 start
            let tableStr = $(`.layui-input[name=${json.fieldName}]`).val();
            if (tableStr != '') {
              let tableData = JSON.parse(tableStr);
              for(let i = 0; i < tableData.length; i++) {
                let obj = dataList.find(e => e.id == tableData[i].id);
                let objIndex = dataList.findIndex(e => e.id == tableData[i].id);
                if (obj) {
                  dataList[objIndex] = obj;
                  dataList[objIndex].isOld = true;
                } else {
                  $(`.child-form-table[data-key-id=${json.keyId}] tr[data-key-id=${tableData[i].id}]`).remove();
                  if (objIndex > -1) {
                    dataList[objIndex].isOld = false;
                  }
                }
              }
            }
            // 去重验证 end
            for(let i = 0; i< dataList.length; i++) {
              if (!dataList[i].isOld) {
                let tr = `<tr data-key-id=${dataList[i].id}>
                <td><span class="cell">${i + 1}</span></td>
                <td><span class="cell">${dataList[i].quaAuthTypeName}</span></td>
                <td><span class="cell">${dataList[i].itemCode}</span></td>
                <td><span class="cell">${dataList[i].itemName}</span></td>
                <td><span class="cell">${dataList[i].authLvHospName}</span></td>
                <td><span class="cell">${dataList[i].authLvNatName}</span></td>
                <td><span class="cell">${dataList[i].isNewTech}</span></td>
                <td><span class="cell">${dataList[i].isRstdTech}</span></td>
                </tr>
                `;
                $(`.child-form-table[data-key-id=${json.keyId}] tbody`).append(tr);
                delete dataList[i].isOld;
              }
            }
            dataList.sort((a, b) => b.authLvHosp - a.authLvHosp);
            $(`.layui-input[name=${json.fieldName}]`).val(JSON.stringify(dataList));
          },
        });
      });
    })(json, that);
  });
  // 手术项目导出Excl
  $.each($('.operationTableExcl'), function (i, n) {
    var keyid = $(this).attr('keyid');
    var that = this;
    (function (el) {
      $(el).on('click', function (e) {
        var Name = $('input[name="xm0"]').val();
        var Dept = $('input[name="szks44"]').val();
        var Technical = $('input[name="zyjszc7"]').val();
        let tables = $(`.child-form-table[data-key-id=${keyid}]`).clone();
        $(tables.find('thead').find('tr')).children().eq(0).after($(`<th><span class="cell">职称</span></th>`));
        $(tables.find('thead').find('tr')).children().eq(0).after($(`<th><span class="cell">科室</span></th>`));
        $(tables.find('thead').find('tr')).children().eq(0).after($(`<th><span class="cell">姓名</span></th>`));
        $.each(tables.find('tbody').find('tr'), function() {
          $(this).children().eq(0).after($(`<td><span class="cell">${Technical}</span></td>`));
          $(this).children().eq(0).after($(`<td><span class="cell">${Dept}</span></td>`));
          $(this).children().eq(0).after($(`<td><span class="cell">${Name}</span></td>`));
          let label = $($(this).find('td span.auditFlag')).find('div.layui-form-radioed div').html();
          if (label == '批准授权' || label == '不批准授权') {
            $($(this).find('td span.auditFlag')).html('');
            $($(this).find('td span.auditFlag')).html(`${label}`)
            let value = $($(this).find('td span.auditScr input')).val()
            $($(this).find('td span.auditScr')).html('');
            $($(this).find('td span.auditScr')).html(`${value}`)
          }
        });
        const workbook = XLSX.utils.table_to_book(tables[0], { raw: true });
        const excelBuffer = XLSX.write(workbook, {
          bookType: 'xlsx',
          type: 'array',
        });
        const blob = new Blob([excelBuffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${Dept}-${Name}-手术项目导出.xlsx`;
        a.click();
        URL.revokeObjectURL(url);
       });
    })(that);
  });
  // 手术项目不批准授权对应的原因必填
  $.each($('input[data-key="auditFlag"]'), function (i, n) {
    var that = this;
    (function (el) {
      $(el).on('change', function() {
        let name = $(el).attr('name');
        let td = $(`input[nameid=${name}]`).parent().parent().parent().find('span.cell');
        if ($(el).attr('value') == 0) {
          $(`input[nameid="${name}"]`).attr('lay-verify','required');
          $(`input[nameid=${name}]`).attr('ts-required','required');
          $(`input[nameid=${name}]`).parent().append(`<span class="required">*</span>`);
          $(`input[nameid=${name}]`).parent().css('display', 'flex');
          $(`input[nameid=${name}]`).parent().css('position', 'relative');
          $(td[3]).css('color','red');
          $(td[4]).css('color','red');
          $(td[5]).css('color','red');
          $(td[6]).css('color','red');
          $(td[7]).css('color','red');
          $(td[8]).css('color','red');
          $(td[9]).css('color','red');
        } else {
          $(`input[nameid="${name}"]`).attr('lay-verify','');
          $(`input[nameid="${name}"]`).attr('ts-required','');
          $(`input[nameid=${name}]`).parent().find('span').remove();
          $(td[3]).css('color','');
          $(td[4]).css('color','');
          $(td[5]).css('color','');
          $(td[6]).css('color','');
          $(td[7]).css('color','');
          $(td[8]).css('color','');
          $(td[9]).css('color','');
        }
      })
    })(that);
  });
  // 外送检验机构
  $.each($('.DeliveryButton'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    var that = this;
    (function (json, el) {
      $(el).on('click', function (e) {
        let initUrl = getParmas();
        let strList = $(`.layui-input[name=${json.fieldName}]`).val() || '';
        if (initUrl.currentStepNo == 'startAgain' && strList == '') {
          $(`.layui-input[name=${json.fieldName}]`).val(formData[json.fieldName]);
          strList = formData[json.fieldName];
        }
        var data = strList == '' ? [] : JSON.parse(strList);
        $.quoteFun('/commonPage/deliveryInspection/index', {
          title: '外送检验项目选择',
          data: data,
          callback: function (dataList, ids) {
            // 去重验证 start
            let tableStr = $(`.layui-input[name=${json.fieldName}]`).val();
            if (tableStr != '') {
              let tableData = JSON.parse(tableStr);
              for(let i = 0; i < tableData.length; i++) {
                let obj = dataList.find(e => e.id == tableData[i].id);
                let objIndex = dataList.findIndex(e => e.id == tableData[i].id);
                if (obj) {
                  dataList[objIndex] = obj;
                  dataList[objIndex].isOld = true;
                } else {
                  $(`.child-form-table[data-key-id=${json.keyId}] tr[data-key-id=${tableData[i].id}]`).remove();
                  dataList[objIndex].isOld = false;
                }
              }
            }
            // 去重验证 end
            for(let i = 0; i< dataList.length; i++) {
              if (!dataList[i].isOld) {
                let tr = `<tr data-key-id=${dataList[i].id}>
                <td><span class="cell">${i + 1}</span></td>
                <td><span class="cell">${dataList[i].projectName}</span></td>
                <td><span class="cell">${dataList[i].sampleType}</span></td>
                <td><span class="cell">${dataList[i].sampleDose}</span></td>
                <td><span class="cell">${dataList[i].testOrg}</span></td>
                <td><span class="cell">${dataList[i].cost}</span></td>
                </tr>
                `;
                $(`.child-form-table[data-key-id=${json.keyId}] tbody`).append(tr);
                delete dataList[i].isOld;
              }
            }
            $(`.layui-input[name=${json.fieldName}]`).val(JSON.stringify(dataList));
          },
        });
      });
    })(json, that);
  });
  // 印章管理
  $.each($('.sealBtn'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    var keyid = $(this).attr('keyid');
    var that = this;
    (function (json, el) {
      $(el).on('click', function (e) {
        let value = $('input[name="sealFile"]').val();
        var data = value ? value.split(',') : [];
        $.quoteFun('/common/sealSel', {
          title: '印章选择',
          data: data,
          callback: function (dataList) {
            $(`div[key-id="${keyid}"] div.sealImg`).empty();
            let ids = [];
            for(let i = 0; i < dataList.length; i++) {
              ids.push(dataList[i].sealImage);
              $(`div[key-id="${keyid}"] div.sealImg`).append(`<img class="signImg sealImgItem" src="/ts-basics-bottom/fileAttachment/downloadFile/${dataList[i].sealImage}">`)
            }
            $('input[name="sealFile"]').val(ids.join(','));
          },
        });
      });
    })(json, that);
  });
  // 输入框文字过长增加监听，悬停展示文字
  $(`.layui-input`).on('change', function () {
    let val = $(this).val();
    if (val) {
      $(this).attr('title', val);
    } else {
      $(this).removeAttr('title');
    }
  });
  $(`[name='${$('#form_box .workorderSettingDeptSel').attr('name')}']`).on(
    'change',
    function () {
      let val = $(this).val();
      let el = $('#faultTypeBoxName');
      $(el).val('');
      workorderSetting[$(this).attr('name')] = val;
      lxWorkorderSetting[$(this).attr('name')] = val;
      if (val) {
        $(el).attr('disabled', false);
        let elData = $('.faultTypeBox').find('input')[1];
        treeSelect(val, $(elData).attr('name'));
      } else {
        $(el).attr('disabled', true);
      }
    }
  );
  // //故障类型
  $.each($('#faultTypeBoxName'), function (i, n) {
    let val = $('.workorderSettingDeptSel').val();
    let el = $(this).closest('.faultTypeBox').find('input')[1];

    if (!!val) {
      $(this).attr('disabled', false);
      treeSelect(val, $(el).attr('name'));
    } else {
      $(this).attr('disabled', true);
    }
  });
  // 故障类型下拉选择树
  function treeSelect(deptId, name) {
    layui.use(['zTreeSearch', 'trasen'], function () {
      var trasen = layui.trasen,
        zTreeSearch = layui.zTreeSearch;
      zTreeSearch.init('#faultTypeBoxName', {
        url: `${common.url}/ts-worksheet/faultType/getFaultTypeAllList/1/${deptId}`,
        type: 'get',
        checkbox: false,
        condition: 'name',
        zTreeOnClick: function (treeId, treeNode) {
          if (treeNode) {
            // $('#fkFaultTypeId').val(treeNode.id);
            workorderSetting[name] = treeNode.id;
            lxWorkorderSetting[name] = treeNode.name;
            $('#faultTypeBoxName').val(treeNode.fullPath);
            var arr = treeNode.getPath(),
              path = '';
            for (var i = 0; i < arr.length; i++) {
              var item = arr[i];
              path += item.name;
              if (i != arr.length - 1) {
                path += '>';
              }
            }
          }
        },
        callback: function () {
          //清空id值
          workorderSetting[name] = '';
          lxWorkorderSetting[name] = '';
        },
        ztreeInitFun(treeObj) {
          nodes = treeObj.getNodes();
          nodes.forEach((item) => {
            treeObj.expandNode(item, true, false);
          });
        },
      });
    });
  }

  // 关联流程
  $.each($('.relationWorkflowSel'), function (i, n) {
    var json = itemMap[$(this).attr('keyid')];
    (function (el, json) {
      let dataList = [];
      let dpFieldRelation = [];

      $.ajax({
        url:
          `/ts-form/form/api/getMyselfDataListByWorkflowId/${json.relationWorkflowId}?wfInstanceId=` +
          uriParams.wfInstanceId,
        method: 'post',
        async: false,
        success: function (res) {
          dataList = res.object || [];
        },
        error: function (res) {},
      });
      $.ajax({
        url: `/ts-form/dpFieldRelation/findByFieldId/${json.keyId}`,
        async: false,
        method: 'post',
        success: function (res) {
          let relation = res.object.relation;
          dpFieldRelation = relation ? JSON.parse(relation) : [];
        },
      });
      // 给撤销流程打印使用的数据
      if (json.relationWorkflowId) {
        cancelLeave[json.relationWorkflowId] = dataList;
        clDpFieldRelation[json.relationWorkflowId] = dpFieldRelation;
      }
      let html = '<option value="">请选择</option>';
      $(el).html(html);
      let list = dataList;
      for (let i = 0; i < list.length; i++) {
        let dataSource = JSON.stringify(list[i]);
        // 组装option列表
        let option = document.createElement('option');
        $(option).attr('datasource', dataSource);
        option.value = list[i].ID;
        // 获取label文案
        let innerText = '';
        dpFieldRelation.forEach((e, idx) => {
          innerText += `${list[i][e.valueFieldname]}`;
          if (idx < dpFieldRelation.length - 1) {
            innerText += '——';
          }
        });
        option.innerText = innerText;
        $(el).append(option);
      }
      $(el).attr(
        'datasource',
        JSON.stringify(dpFieldRelation ? dpFieldRelation : {})
      );
      if (formData[json.fieldName]) {
        $(el).val(formData[json.fieldName]);
      }
      layForm.render('select');
    })(this, json);
  });

  $(`[name='${$('#form_box .relationWorkflowSel').attr('name')}']`).on(
    'change',
    function () {
      const dpFieldRelation = JSON.parse($(this).attr('datasource'));
      let selectedOpt = {};
      const id = $(this).val();
      const options = $(this).find('option');
      $.each(options, function (i, n) {
        let datasource = $(this).attr('datasource');
        datasource = datasource ? JSON.parse(datasource) : {};
        if (datasource.ID == id) {
          selectedOpt = datasource;
        }
      });

      dpFieldRelation.forEach((e) => {
        $('#form_box')
          .find(`[name="${e.keyFieldname}"]`)
          .val(selectedOpt[e.valueFieldname]);
      });
      layForm.render();
    }
  );

  //病历文书
  interworkCallBack();
  $.each($('.interworkBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var $inp = el.prev();
      var json = json;
      var type = json.interworkFace;
      el.on('click', function () {
        var number = $inp.val();
        if (!number) {
          layer.msg('请输入住院号');
          return false;
        }
        $.ajax({
          url: '/ts-external/emrApi/getInpatientInfoById',
          method: 'post',
          data: {
            name: number,
          },
          success: function (res) {
            if (res.success && res.object) {
              if (res.object.length == 0) {
                layer.msg('未查到住院信息');
                return false;
              }
              $.quoteFun('../view-new/processView/modules/interWork/EMR', {
                data: res.object,
                number: number,
                callBack: function (data) {
                  interworkComData[json.fieldName] = data;
                  interworkCallBack();
                },
              });
            } else {
              layer.msg('获取信息失败');
            }
          },
        });
        // $.quoteFun('../view-new/processView/modules/interWork/normal', {
        //     name: $inp.val(),
        //     type: type,
        //     itemJson: json,
        //     callBack: interworkCallBack,
        // });
      });
    })($this, json);
  });

  //病人信息
  interworkSickCallBack();
  $.each($('.interworkSickBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var $inp = el.prev();
      var $type = el.siblings('.interworkSickSel').find('select');
      var json = json;
      el.on('click', function () {
        var type = $type.val();
        var number = $inp.val();
        if (!number) {
          layer.msg('请输入查询条件');
          return false;
        }
        $.ajax({
          url: '/ts-external/hisApi/getPatientInfo',
          method: 'post',
          data: {
            type: type,
            number: number,
          },
          success: function (res) {
            if (res.success) {
              $.quoteFun('../view-new/processView/modules/interWork/sick', {
                data: res.object,
                callBack: function (data) {
                  interworkSick[json.fieldName] = data;
                  interworkSickCallBack();
                },
              });
            } else {
              layer.msg('获取病人信息失败');
            }
          },
        });
      });
    })($this, json);
  });
  //取消结算
  interworkSettleCallBack();
  $.each($('.interworkSettleBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var $inp = el.prev();
      var json = json;
      el.on('click', function () {
        var number = $inp.val();
        if (!number) {
          layer.msg('请输入住院号');
          return false;
        }
        $.ajax({
          url: '/ts-external/emrApi/getInpatientInfoById',
          method: 'post',
          data: {
            name: number,
          },
          success: function (res) {
            if (res.success) {
              $.quoteFun('../view-new/processView/modules/interWork/settle', {
                data: res.object,
                number: number,
                callBack: function (data, text) {
                  interworkSettle[json.fieldName] = data;
                  interworkSettle[json.fieldName].zy = text;
                  interworkSettleCallBack();
                },
              });
            } else {
              layer.msg('获取信息失败');
            }
          },
        });
      });
    })($this, json);
  });
  //取消预交金
  interworkPayCallBack();
  $.each($('.interworkPayBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var $inp = el.prev();
      var json = json;
      el.on('click', function () {
        var number = $inp.val();
        if (!number) {
          layer.msg('请输入住院号');
          return false;
        }
        $.ajax({
          url: '/ts-external/emrApi/getInpatientInfoById',
          method: 'post',
          data: {
            name: number,
          },
          success: function (res) {
            if (res.success) {
              $.quoteFun('../view-new/processView/modules/interWork/pay', {
                data: res.object,
                number: number,
                callBack: function (data) {
                  interworkPay[json.fieldName] = data;
                  interworkPayCallBack();
                },
              });
            } else {
              layer.msg('获取信息失败');
            }
          },
        });
      });
    })($this, json);
  });
  //取消住院项目
  interworkHosProCallBack();
  $.each($('.interworkHosProBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var $inp = el.prev();
      var json = json;
      el.on('click', function () {
        var number = $inp.val();
        // $.quoteFun('../view-new/processView/modules/interWork/hospro', {});
        if (!number) {
          layer.msg('请输入住院号');
          return false;
        }
        $.ajax({
          url: '/ts-external/emrApi/getInpatientInfoById',
          method: 'post',
          data: {
            name: number,
          },
          success: function (res) {
            if (res.success) {
              $.quoteFun('../view-new/processView/modules/interWork/hospro', {
                data: res.object,
                number: number,
                callBack: function (data) {
                  interworkHosPro[json.fieldName] = data;
                  interworkHosProCallBack();
                },
              });
            } else {
              layer.msg('获取信息失败');
            }
          },
        });
      });
    })($this, json);
  });
  //检验医嘱申请
  interworkTestCallBack();
  $.each($('.interworkTestBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var json = json;
      el.on('click', function () {
        $.quoteFun('../view-new/processView/modules/interWork/test', {
          callBack: function (data) {
            interworkTest[json.fieldName] = data;
            interworkTestCallBack();
          },
        });
      });
    })($this, json);
  });

  //医嘱耗材
  inPatientOrderCallBack();
  $.each($('.inPatientOrderBtn'), function (i, n) {
    var $this = $(this);
    var json = itemMap[$this.attr('keyid')];
    (function (el, json) {
      var $inp = el.prev();
      var json = json;
      el.on('click', function () {
        var number = $inp.val();
        if (!number) {
          layer.msg('请输入住院号');
          return false;
        }
        $.quoteFun('../view-new/processView/modules/interWork/inPatientOrder', {
          number: number,
          callBack: function (data) {
            inPatientOrder[json.fieldName] = data;
            inPatientOrderCallBack();
          },
        });
      });
    })($this, json);
  });

  /**@desc 子表单 模板导出 */
  $('[name="childFormExportBtn"]')
    .off('click')
    .on('click', function (e) {
      let table = $(e.target).closest('.formItem').find('.child-form-table'),
        key = table.attr('data-child-form-key'),
        tableDetail = itemMap[key].childFormDetail,
        aDom = document.createElement('a');
      aDom.href =
        '/ts-form/dpTable/downloadDpTableTemplate?tableId=' + tableDetail.id;
      aDom.click();
    });
  /**@desc 子表单 导入 */
  layui.use('upload', function () {
    let upload = layui.upload;
    upload.render({
      elem: '[name="childFormImportBtn"]',
      url: common.url + '/ts-form/dpTable/importDpTableTemplate',
      size: 50000,
      accept: 'file',
      auto: true,
      method: 'post',
      before: function () {
        let table = this.item.closest('.formItem').find('.child-form-table'),
          key = table.attr('data-child-form-key'),
          tableDetail = itemMap[key].childFormDetail;
        this.data = { tableId: tableDetail.id };
      },
      done: function (res) {
        if (!res.success) {
          layer.msg(res.message || '文件导入失败');
          return;
        }
        let table = this.item.closest('.formItem').find('.child-form-table'),
          key = table.attr('data-child-form-key'),
          showType = table.attr('data-child-form-type'),
          tableDetail = itemMap[key].childFormDetail || {},
          columns = tableDetail.fields || [],
          errorList = [];
        table.find('tbody').empty();
        res.object.map((row) => {
          let newTr = computeNewChildFormItem(
            showType,
            columns,
            row,
            false,
            errorList
          );
          let sequenceLength = table.find('.sequence-num').length;
          $('.sequence-num', newTr).text(sequenceLength + 1);
          table.append(newTr);
        });
        layForm.render();
        childFormBindFun();
        childFormCountFun();
        if (errorList.length) {
          layer.msg('存在系统无法匹配的数据，已重置，如有需要，请自行更改');
        } else {
          layer.msg('导入成功');
        }
      },
    });
  });

  /**@desc 基本药品字典 打开选择弹窗 */
  $('[name="essDrugDicAddBtn"]')
    .off('click')
    .on('click', function () {
      let keyId = $(this).attr('data-key-id'),
        setting = itemMap[keyId] || {},
        selectedData = formData[setting.fieldName];

      $.quoteFun(
        '../view-new/processView/modules/essDrugDicSelectModal/index',
        {
          data: selectedData,
          callBack: function (dataList = []) {
            formData[setting.fieldName] = dataList;
            renderEssDrugDicSeletedTable(dataList, setting);
          },
        }
      );
    });

  /**@desc 药品采购 打开选择弹窗 */
  $('[name="medicalSupplieDicAddBtn"]')
    .off('click')
    .on('click', function () {
      let keyId = $(this).attr('data-key-id'),
        setting = itemMap[keyId] || {},
        selectedData = formData[setting.fieldName];

      $.quoteFun(
        '../view-new/processView/modules/medicalSupplieSelectModal/index',
        {
          data: selectedData,
          callBack: function (dataList = []) {
            formData[setting.fieldName] = dataList;
            renderMedicalSupplieDicSeletedTable(dataList, setting);
          },
        }
      );
    });
}
//设置来源字段值
function setSourceFieldVal(el) {
  var type = $(el).attr('data-from');
  var verify = $(el).attr('lay-verify')
    ? $(el).attr('lay-verify').split('|')
    : [];
  var disabled = $(el).disabled;
  switch (type) {
    case 'nowDate':
      if (!disabled) {
        $(el).prop('readonly', true);
        $(el).addClass('layDate');
      }
      $(el).val(new Date().format('yyyy-MM-dd'));
      break;
    case 'loginName':
      $(el).val(userInfo.empName);
      break;
    case 'loginCode':
      $(el).val(userInfo.empCode);
      break;
    case 'loginOrg':
      $(el).val(userInfo.ssoOrgName);
      break;
    case 'loginDept':
      $(el).val(userInfo.organizationParttimeName || userInfo.empDeptName);
      break;
    case 'loginPhone':
      if (!disabled) {
        $(el).attr('type', 'number');
        verify.push('noRequirePhone');
        $(el).attr('lay-verify', verify.join('|'));
      }
      $(el).val(userInfo.empPhone);
      break;
    case 'loginEmail':
      if (!disabled) {
        verify.push('noRequireEmail');
        $(el).attr('lay-verify', verify.join('|'));
      }
      $(el).val(userInfo.empEmail);
      break;
    case 'loginSignature':
      if (userInfo.signatureImgName) {
        $(el)
          .attr({
            path: userInfo.signatureImgName,
            src: userInfo.signatureImgName,
          })
          .next('.signInput')
          .remove();
      } else {
        $(el)
          .next('.signInput')
          .val(userInfo.empName)
          .attr('path', userInfo.empName);
        $(el).remove();
      }
      break;
    case 'loginDuty':
      $(el).val(userInfo.empDutyName);
      break;
    case 'loginBirth':
      if (!disabled) {
        $(el).prop('readonly', true);
        $(el).addClass('layDate');
      }
      $(el).val(userInfo.empBirth);
      break;
    case 'loginAge':
      if (!disabled) {
        $(el).attr('type', 'number');
        verify.push('noRequirePositiveInt');
        $(el).attr('lay-verify', verify.join('|'));
      }
      $(el).val(userInfo.empAge);
      break;
    case 'loginSex':
      $(el).val(userInfo.empSex == 0 ? '男' : '女');
      break;
    case 'loginIDCard':
      if (!disabled) {
        verify.push('noRequireIdentity');
        $(el).attr('lay-verify', verify.join('|'));
      }
      $(el).val(userInfo.empIdcard);
      break;
    case 'loginPost':
      $(el).val(userInfo.postName);
      break;
    case 'loginPostType':
      $(el).val(userInfo.postType);
      break;
    case 'loginCarno':
      $(el).val(userInfo.carNo);
      break;
    case 'loginJob':
      $(el).val(userInfo.jobAttributes);
      break;
    case 'loginWorkDate':
      $(el).val(userInfo.workStartDate);
      break;
    case 'loginHospArea':
      $(el).val(userInfo.hospName);
      break;
    case 'loginTechnical':
      $(el).val(userInfo.technical);
      break;
    case 'loginOrgAttributes':
      $(el).val(userInfo.orgAttributes);
      break;
    case 'loginEntryDate':
      if (!disabled) {
        $(el).prop('readonly', true);
        $(el).addClass('layDate');
      }
      $(el).val(userInfo.entryDate);
      break;
    case 'loginPosttitle':
      $(el).val(userInfo.empPosttitle);
      break;
    case 'annualLeave':
      if (!disabled) {
        $(el).attr('type', 'number');
        verify.push('noRequireNonnegativeNumber');
        $(el).attr('lay-verify', verify.join('|'));
      }
      $(el).val(userInfo.yearDays || 0);
      break;
    case 'annualLeave_h':
      if (!disabled) {
        $(el).attr('type', 'number');
        verify.push('noRequireNonnegativeNumber');
        $(el).attr('lay-verify', verify.join('|'));
      }
      $(el).val(userInfo.yearNumber || 0);
      break;
    case 'positiveTime':
      if (!disabled) {
        $(el).prop('readonly', true);
        $(el).addClass('layDate');
      }
      $(el).val(userInfo.positiveTime || 0);
      break;
    case 'annualLeave_l':
      break;
    default:
      break;
  }
}
// 常用语列表
var officaldictionValue = [];

function getCommentList() {
  $.ajax({
    url: '/ts-oa/employee/officaldiction/getMyOfficaldiction',
    type: 'get',
    success: function (res) {
      if (res.success) {
        var list = res.object || [];
        var str = '<ul>';
        var defaultValue = '';
        for (var i = 0; i < list.length; i++) {
          if (null != list[i].isDefaultValue && list[i].isDefaultValue == '1') {
            defaultValue = list[i].offName;
            officaldictionValue.push(defaultValue);
          }
          str += `<li title="${list[i].offName}">${list[i].offName}</li>`;
        }
        str += '</ul>';
        $('.comment_box .officaldiction').append(str);
        if (!canEditRes) {
          $('.comment_box').find('textarea').val(defaultValue);
        }
      }
    },
  });
  if (!uriParams.wfInstanceId) {
    return false;
  }
  var commonFileDatas = [];
  $.ajax({
    url: '/ts-workflow/task/file/list',
    type: 'post',
    async: false,
    data: {
      pageNo: 1,
      pageSize: 999,
      wfInstanceId: uriParams.wfInstanceId,
      sord: 'desc',
      sidx: 'create_date',
    },
    success: function (res) {
      commonFileDatas = res.rows || [];
      oldFileList = res.rows || [];
      for (var i = 0; i < oldFileList.length; i++) {
        oldFileList[i].isDelete = 0;
      }
    },
  });
  $.ajax({
    url: '/ts-workflow/workflow/taskHis/selectFormApproval',
    method: 'post',
    contentType: 'application/json; charset=utf-8',
    async: false,
    data: JSON.stringify({
      wfInstanceId: uriParams.wfInstanceId,
    }),
    success: function (res) {
      if (res.success) {
        var data = res.object || {};
        for (var key in data) {
          var list = res.object[key] || [];
          var str = '<div class="hisComment">';
          var hideApprovalTime = $(`.comment_box[field-name="${key}"]`).attr(
            'hideApprovalTime'
          );
          let maxlengthHisComment = $(`.comment_box[field-name="${key}"]`).attr(
            'maxlength'
          );
          let keyId = $(`.comment_box[field-name="${key}"]`).attr('key-id');
          for (var i = 0; i < list.length; i++) {
            str += `<div class="hisCommentItem" taskHisId="${list[i].taskHisId}">`;
            if (!canEditRes) {
              str += `<pre><p class="approvalFiledRemark">${
                list[i].remark || ''
              }</p></pre>`;
            } else {
              str += `<textarea 
                class="layui-textarea taskHis" 
                name="${key}" 
                maxlength="${maxlengthHisComment}"  
                autoHeight="true" 
                style="width:100% !important"
                value="${list[i].remark}"
                taskHisId="${list[i].taskHisId}"
                ></textarea>`;
            }
            let fileDatas = commonFileDatas.filter((item) => {
              return list[i].taskId == item.taskId;
            });
            str += setCommonFileStr(
              fileDatas,
              key,
              keyId,
              maxlengthHisComment,
              list[i].taskHisId
            );
            // 印章
            if (list[i].sealFile) {
              let seal = list[i].sealFile.split(',');
              str += '<div class="sealImg">'
              for(let i = 0; i< seal.length; i++) {
                str += `<img class="signImg sealImgItem"  src="/ts-basics-bottom/fileAttachment/downloadFile/${seal[i]}?isdel=no" />`;
              }
              str += '</div>';
            }
            //电子签章
            str += '<p class="approvalFiledDetail">';
            if (list[i].signatureImg) {
              str += `<img class="signImg" src="${list[i].signatureImg}" />`;
            } else if (list[i].signatureImgName) {
              str += `<img class="signImg" src="${list[i].signatureImgName}" />`;
            } else {
              str += list[i].actAssigneeName;
            }
            if (!hideApprovalTime) {
              let paramsData =
                params.formData.toaFieldSetList.find(
                  (item) => item.fieldName == key
                ) || {};
              let format =
                paramsData.approvalTimeFormat || 'yyyy-MM-dd HH:mm:ss';
              format = format
                .replace(/Y/g, 'y')
                .replace(/D/g, 'd')
                .replace(/H/g, 'h');
              str += `<span style="font-size: 14px; color: #666;">
                  （${new Date(list[i].finishedDate).format(format)}）
                </span>`;
            }
            str += '</p>';
            str += '</div>';
          }
          str += '</div>';
          $(`.comment_box[field-name="${key}"]`).append(str);
        }
        if (canEditRes) {
          for (var key in data) {
            var list = res.object[key] || [];
            for (var i = 0; i < list.length; i++) {
              $(`textarea[taskHisId="${list[i].taskHisId}"]`).val(
                list[i].remark
              );
            }
          }
        }
        // 附件上传
        $.each($('.FileUploadBtnCopy'), function (i, n) {
          var fieldName = $(this).siblings('input').attr('name');
          fileuploadCopy.init($(this), 'form', formData && formData[fieldName]);
        });
      }
    },
  });
}

function setCommonFileStr(commonFileList, key, keyId, length, taskHisId) {
  if (!canEditRes) {
    var html = '<ul class="FileList">';
  } else {
    var html = `<div class="uploadDiv">
        <input 
          type="hidden" 
          name="taskFile${key}" 
          keyId="${keyId}"
          maxlength="${length}"
          taskHisId="${taskHisId}" 
         />
        <span 
          class="FileUploadBtn FileUploadBtnCopy layui-btn layui-btn-normal" 
          keyId="${keyId}" 
          taskHisId="${taskHisId}"
        >
          选择附件
        </span>
        <span class="AllFileDown layui-btn layui-btn-normal none">批量下载</span>
        <div class="layui-upload">`;
    html += '<ul class="FileList"></ul>';
    html += '<ul class="FileLists">';
  }
  for (var i = 0; i < commonFileList.length; i++) {
    var fileUrl = commonFileList[i].fileUrl.replace(/\\/g, '/');
    commonFileList[i].id = fileUrl.split('/')[0];
    html += `<li class="file-item" file-id="${commonFileList[i].id}">
        <a 
          class="fileDown" 
          href="/ts-document/attachment/downloadFile/${commonFileList[i].id}"
        >
          ${commonFileList[i].fileName}
        </a>`;
    var imgClass = common.isImg(commonFileList[i].fileName)
      ? 'viewerImg'
      : 'viewerDoc2';
    html += `<span 
        class="${imgClass}" 
        style="margin:0 5px; cursor: pointer;" 
        fileurl="${commonFileList[i].id}" 
        fileid="${commonFileList[i].id}" 
        filename="${commonFileList[i].fileName}"
      >预览</span>`;
    if (canEditRes) {
      html += '<span class="fileDelOld">删除</span>';
    }
    html += '<span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span>';
    html += '</li>';
  }
  html += '</ul>';
  if (canEditRes) {
    html += '</div></div>';
  }
  return html;
}
$('body').on('click', 'span.fileDelOld', function () {
  var fileId = $($(this).parents('li')[0]).attr('file-id');
  for (var i = 0; i < oldFileList.length; i++) {
    if (oldFileList[i].id == fileId) {
      oldFileList[i].isDelete = -1;
    }
  }
  $($(this).parents('li')[0]).addClass('none');
});
// 收藏附件
$('body').on('click', 'span.fileConnection', function () {
  var fileId = $($(this).parents('li')[0]).attr('file-id');
  $.ajax({
    url: '/ts-oa/attachment/saveCollect',
    method: 'post',
    contentType: 'application/json',
    data: JSON.stringify({
      collectId: fileId,
    }),
    success: function (res) {
      if (res.success) {
       layer.msg("收藏成功,已收藏到个人文档");
      } else {
        layer.msg(res.message);
      }
    },
  });
});
//获取流水号
function getSerialNumber() {
  if ($('.serialNumber').length) {
    $.ajax({
      url: '/ts-form/field/serialNumber/calculationSerialNo',
      method: 'post',
      contentType: 'application/json',
      data: JSON.stringify({
        templateTd: params.formData.id,
      }),
      success: function (res) {
        if (res.success && res.object) {
          for (var key in res.object) {
            $(`[name="${key}"]`).val(res.object[key]);
          }
          fieldSerialNumberRoleId = res.object['fieldSerialNumberRoleId'];
        }
      },
    });
  }
}
/**********************收发文************** */
// #region
//正文id获取
function getPageOfficeId(id) {
  $('[name="page_office_id"]').val(id);
}
/* TODO  按钮部分  */
var fwData = {
  docnumId: '',
  number: '',
};

//发文按钮
function fwBtnFun() {
  //  起草正文
  $('body').on('click', 'button[code="1"]', function () {
    var pageOfficeId = $('[name="page_office_id"]').val();
    var url =
      '/ts-document/govSendfile/openSendFileForm?pageOfficeId=' +
      pageOfficeId +
      '&editType=QCZW&userCode=' +
      userInfo.empCode +
      '&userName=' +
      encodeURIComponent(userInfo.empName);
    javascript: POBrowser.openWindowModeless(
      url,
      'width=1370px;height=1000px;fullscreen=yes;frame=yes'
    );
  });
  //批阅正文
  $('body').on('click', 'button[code="2"]', function () {
    var pageOfficeId = $('[name="page_office_id"]').val();
    if (!pageOfficeId) {
      layer.msg('请先起草正文');
      return false;
    }
    var url =
      '/ts-document/govSendfile/openSendFileForm?pageOfficeId=' +
      pageOfficeId +
      '&editType=PYZW&userCode=' +
      userInfo.empCode +
      '&userName=' +
      encodeURIComponent(userInfo.empName);
    javascript: POBrowser.openWindowModeless(
      url,
      'width=1370px;height=1000px;fullscreen=yes;frame=yes'
    );
  });
  //查看正文
  $('body').on('click', 'button[code="7"]', function () {
    var pageOfficeId = $('[name="page_office_id"]').val();
    if (!pageOfficeId) {
      layer.msg('请先起草正文');
      return false;
    }
    var url =
      '/ts-document/govSendfile/openSendFileForm?pageOfficeId=' +
      pageOfficeId +
      '&editType=CKZW&userCode=' +
      userInfo.empCode +
      '&userName=' +
      encodeURIComponent(userInfo.empName);
    javascript: POBrowser.openWindowModeless(
      url,
      'width=1370px;height=1000px;fullscreen=yes;frame=yes'
    );
  });
  //编号
  $('body').on('click', 'button[code="3"]', function () {
    var pageOfficeId = $('[name="page_office_id"]').val();
    if (!pageOfficeId) {
      layer.msg('请先起草正文');
      return false;
    }
    $.ajax({
      url: '/ts-document/govSendfile/getIsSetRed',
      type: 'get',
      data: {
        pageOfficeId: pageOfficeId,
        workflowNo: params.baseData.workflowNo,
      },
      success: function (res) {
        if (res.success && res.object == 'true') {
          layer.msg('此文件已套红，不能重复编号');
          return false;
        } else {
          $.ajax({
            method: 'get',
            url: '/ts-document/govSendfile/getSendfileNumber',
            data: {
              docwordId: $('[name="doc_word_id"]').val(),
            },
            success: function (res) {
              if (res.success) {
                fwData.docnumId = res.object.docnumId;
                $.quoteFun('/commonPage/docword/index', {
                  data: res.object || {},
                  callBack: function (str, number, closeFun) {
                    $.ajax({
                      method: 'get',
                      url: '/ts-document/govSendfile/validateFileNumber',
                      data: {
                        number: number,
                        docnumId: fwData.docnumId,
                      },
                      success: function (res) {
                        if (res.success && !res.object) {
                          closeFun();
                          $('[name="file_number"]').val(str);
                          fwData.number = number;
                        } else if (res.success && res.object) {
                          layer.msg('此编号已被使用，请切换编号');
                        } else {
                          layer.msg(res.message || '系统错误');
                        }
                      },
                    });
                  },
                });
              } else {
                layer.msg(res.message || '失败');
              }
            },
          });
        }
      },
    });
  });
  //套红
  $('body').on('click', 'button[code="4"]', function () {
    var pageOfficeId = $('[name="page_office_id"]').val();
    if (!pageOfficeId) {
      layer.msg('请先起草正文');
      return false;
    }
    // 没有编号按钮，直接套红
    if ($('button[code="3"]').length == 1 && !$('[name="file_number"]').val()) {
      layer.msg('请先对发文进行编号!');
      return false;
    }
    var url =
      '/ts-document/govSendfile/openSendFileForm?pageOfficeId=' +
      pageOfficeId +
      '&editType=TH' +
      '&docwordId=' +
      $('[name="doc_word_id"]').val() +
      '&fileNumber=' +
      escape($('[name="file_number"]').val()) +
      '&docnumId=' +
      fwData.docnumId +
      '&userCode=' +
      userInfo.empCode +
      '&userName=' +
      encodeURIComponent(userInfo.empName);
    javascript: POBrowser.openWindowModeless(
      url,
      'width=1370px;height=1000px;fullscreen=yes;frame=yes'
    );
  });

  //编辑正文
  $('body').on('click', 'button[code="41"]', function () {
    var pageOfficeId = $('[name="page_office_id"]').val();
    if (!pageOfficeId) {
      layer.msg('请先起草正文');
      return false;
    }
    var url =
      '/ts-document/govSendfile/openSendFileForm?pageOfficeId=' +
      pageOfficeId +
      '&editType=TH' +
      '&convertDoc=Y' +
      '&docwordId=' +
      $('[name="doc_word_id"]').val() +
      '&fileNumber=' +
      escape($('[name="file_number"]').val()) +
      '&docnumId=' +
      fwData.docnumId +
      '&userCode=' +
      userInfo.empCode +
      '&userName=' +
      encodeURIComponent(userInfo.empName);
    javascript: POBrowser.openWindowModeless(
      url,
      'width=1370px;height=1000px;fullscreen=yes;frame=yes'
    );
  });

  //发文分发
  $('body').on('click', 'button[code="5"]', function () {
    if ($('button[code="4"]').length == 0) {
      //没套红 走简易发文
      var pageOfficeId = $('.file-item').attr('file-id');
      if (pageOfficeId == '') {
        layer.msg('请先上传发文附件');
        return false;
      }
    } else {
      var pageOfficeId = $('[name="page_office_id"]').val();
      if (!pageOfficeId) {
        layer.msg('请先起草正文');
        return false;
      }
    }
    function openFFWin(data) {
      var data = {
        isCheckDept: 'N',
        userNameStrs: data.userNameList.join(','),
        userIdStrs: data.userCodeList.join(','),
        userCodeStrs: data.userCodeList.join(','),
      };
      $.quoteFun('/common/userSel', {
        trasen: trasenTable,
        title: '人员选择',
        data: data,
        rowNum: 2000,
        callback: function (names, ids, codes) {
          $.ajax({
            url: '/ts-document/govSendfile/distributionSendFile',
            method: 'post',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
              userCodeStr: codes.join(','),
              userNameStr: names,
              sendfileId: formData.ID,
              type: 1,
            }),
            success: function (res) {
              if (res.success) {
                layer.msg('分发成功');
              } else {
                layer.msg(res.message);
              }
            },
          });
        },
      });
    }

    function openCheck() {
      $.ajax({
        url: '/ts-document/govSendfile/getSendFileList',
        type: 'get',
        data: {
          sendfileId: formData.ID,
        },
        success: function (res) {
          var userCodeList = [];
          var userNameList = [];
          if (res.success) {
            for (var i = 0; i < res.object.length; i++) {
              /*userlist.push({
                                code: res.object[i].userCode,
                                name: res.object[i].userName
                            })*/
              userCodeList.push(res.object[i].userCode);
              userNameList.push(res.object[i].userName);
            }
          }
          openFFWin({
            user: true,
            userCodeList: userCodeList,
            userNameList: userNameList,
            dept: true,
            deptCheck: false,
          });
        },
      });
    }
    if ($('button[code="4"]').length == 0) {
      openCheck();
      return false;
    }
    $.ajax({
      url: '/ts-document/govSendfile/getIsSetRed',
      type: 'get',
      data: {
        pageOfficeId: pageOfficeId,
        workflowNo: params.baseData.workflowNo,
      },
      success: function (res) {
        if (res.success && res.object == 'true') {
          openCheck();
        } else {
          layer.open({
            title: '提示',
            content: '此文件未套红，不能分发',
          });
        }
      },
    });
  });

  //发文分发下级机构
  $('body').on('click', 'button[code="51"]', function () {
    if ($('button[code="4"]').length == 0) {
      //没套红 走简易发文
      var pageOfficeId = $('.file-item').attr('file-id');
      if (pageOfficeId == '') {
        layer.msg('请先上传发文附件');
        return false;
      }
    } else {
      var pageOfficeId = $('[name="page_office_id"]').val();
      if (!pageOfficeId) {
        layer.msg('请先起草正文');
        return false;
      }
    }
    // var pageOfficeId = $('[name="page_office_id"]').val();
    // if (!pageOfficeId) {
    //   layer.msg('请先起草正文');
    //   return false;
    // }

    function openFFWin(data) {
      var data = {
        isCheckDept: 'N',
        userNameStrs: data.userNameList.join(','),
        userIdStrs: data.userCodeList.join(','),
        userCodeStrs: data.userCodeList.join(','),
        isChildOrg: 'Y',
      };
      $.quoteFun('/common/userSel', {
        trasen: trasenTable,
        title: '人员选择',
        data: data,
        noRenderTree: true,
        rowNum: 2000,
        callback: function (names, ids, codes) {
          const element = document.getElementById('form_content');
          const options = {
            dpi: 192, //dpi属性的值为192，表示图像的分辨率
            scale: 2, //scale属性的值为2，表示图像的缩放比例。
            backgroundColor: '#F1F6FE', //backgroundColor属性的值为"#F1F6FE"，表示图像的背景颜色。
          };
          var formFileId = '';
          // 将元素转换为canvas对象
          html2canvas(element, options).then((canvas) => {
            var contentWidth = canvas.width; //获取Canvas(上面元素id 'layout-wrapper')对象的宽度
            var contentHeight = canvas.height; //获取Canvas(上面元素id 'layout-wrapper')对象的高度
            // 创建jsPDF对象
            // jsPDF = jspdf.jsPDF;  //导入jsPDF库，用于创建PDF文件
            const pdf = new jsPDF('1', 'pt', [contentWidth, contentHeight]); //创建一个新的PDF对象，参数包括页面格式（'1'表示A4纸张）、单位（'pt'）和页面尺寸（[contentWidth, contentHeight]）
            var pageData = canvas.toDataURL('image/jpeg', 1.0); //将Canvas对象转换为JPEG格式的数据，并将其存储在pageData变量中。1.0表示图片质量
            var imgX = (contentWidth / 2) * 0.75;
            var imgY = (contentHeight / 2) * 0.75;
            pdf.addImage(pageData, 'JPEG', 0, 0, imgX, imgY); //将JPEG格式的图片添加到PDF文件中，图片的左上角坐标为(0, 0)，宽度为contentWidth，高度为contentHeight
            // pdf.save($('#wfName').html() + ".pdf");
            let base64 = pdf.output('datauristring');
            let file = convertBase64ToFile(base64, $('#wfName').html());
            // 上传到服务器
            let fileData = new FormData();
            fileData.append('file', file);
            $.ajax({
              url:
                common.url + '/ts-document/attachment/fileUpload?module=form',
              type: 'post',
              async: false,
              data: fileData,
              processData: false,
              contentType: false,
              success: function (res) {
                formFileId = res.object[0].fileId;
                $.ajax({
                  url: '/ts-document/govSendfile/distributionSendFile',
                  method: 'post',
                  contentType: 'application/json; charset=utf-8',
                  data: JSON.stringify({
                    userCodeStr: codes.join(','),
                    userNameStr: names,
                    sendfileId: formData.ID,
                    type: 1,
                    isChildOrg: 'Y',
                    formFileId: formFileId,
                  }),
                  success: function (res) {
                    if (res.success) {
                      layer.msg('分发成功');
                    } else {
                      layer.msg(res.message);
                    }
                  },
                });
              },
            });
          });
        },
      });
    }

    function openCheck() {
      // 查询已分发人员
      $.ajax({
        url: '/ts-document/govSendfile/getSendFileList',
        type: 'get',
        data: {
          sendfileId: formData.ID,
        },
        success: function (res) {
          var userCodeList = [];
          var userNameList = [];
          if (res.success) {
            for (var i = 0; i < res.object.length; i++) {
              userCodeList.push(res.object[i].userCode);
              userNameList.push(res.object[i].userName);
            }
          }
          openFFWin({
            user: true,
            userCodeList: userCodeList,
            userNameList: userNameList,
            dept: true,
            deptCheck: false,
          });
        },
      });
    }
    //判断是否套红
    if ($('button[code="4"]').length == 0) {
      openCheck();
      return false;
    }
    $.ajax({
      url: '/ts-document/govSendfile/getIsSetRed',
      type: 'get',
      data: {
        pageOfficeId: pageOfficeId,
        workflowNo: params.baseData.workflowNo,
      },
      success: function (res) {
        if (res.success && res.object == 'true') {
          openCheck();
        } else {
          layer.open({
            title: '提示',
            content: '此文件未套红，不能分发',
          });
        }
      },
    });
  });

  // 发文分发全员
  $('body').on('click', 'button[code="52"]', function () {
    layer.confirm(
      '确定要进行全院分发吗?',
      {
        btn: ['确定', '取消'],
        title: '提示',
        closeBtn: 0,
      },
      function (index) {
        $.ajax({
          url: '/ts-document/govSendfile/distributionSendFile',
          method: 'post',
          contentType: 'application/json; charset=utf-8',
          data: JSON.stringify({
            sendfileId: formData.ID,
            type: 1,
            isAll: 'Y',
          }),
          success: function (res) {
            if (res.success) {
              layer.msg('分发成功');
            } else {
              layer.msg(res.message);
            }
          },
        });
      }
    );
  });

  //发文取消分发
  $('body').on('click', 'button[code="6"]', function () {
    if ($('button[code="4"]').length == 0) {
      //没套红 走简易发文
      var pageOfficeId = $('.file-item').attr('file-id');
      if (pageOfficeId == '') {
        layer.msg('请先上传发文附件');
        return false;
      }
    } else {
      var pageOfficeId = $('[name="page_office_id"]').val();
      if (!pageOfficeId) {
        layer.msg('请先起草正文');
        return false;
      }
    }
    // var pageOfficeId = $('[name="page_office_id"]').val();
    // if (!pageOfficeId) {
    //   layer.msg('请先起草正文');
    //   return false;
    // }
    $.ajax({
      url: '/ts-document/govSendfile/getIsSetRed',
      type: 'get',
      data: {
        pageOfficeId: pageOfficeId,
        workflowNo: params.baseData.workflowNo,
      },
      success: function (res) {
        if (res.success && res.object == 'true') {
          //获取分发人员信息
          $.ajax({
            url: '/ts-document/govSendfile/getSendFileList',
            type: 'get',
            data: {
              sendfileId: formData.ID,
            },
            success: function (res) {
              if (res.success && res.object.length == 0) {
                layer.msg('暂无分发人员');
              } else if (res.success && res.object.length) {
                var userList = res.object.filter((item) => {
                  return !item.ssoOrgName;
                });
                var childOrgUserList = res.object.filter((item) => {
                  return item.ssoOrgName;
                });
                let orgNameObj = {};
                for (var i = 0; i < childOrgUserList.length; i++) {
                  if (!orgNameObj[childOrgUserList[i].ssoOrgName]) {
                    var arr = [];
                    arr.push(childOrgUserList[i]);
                    orgNameObj[childOrgUserList[i].ssoOrgName] = arr;
                  } else {
                    orgNameObj[childOrgUserList[i].ssoOrgName].push(
                      childOrgUserList[i]
                    );
                  }
                }
                let childOrgList = Object.keys(orgNameObj).map((key) => {
                  return {
                    childOrgName: key,
                    userList: orgNameObj[key],
                  };
                });
                $.quoteFun('/commonPage/cancelDistribution/index', {
                  data: {
                    userList,
                    childOrgList,
                  },
                });
              }
            },
          });
        } else {
          layer.open({
            title: '提示',
            content: '此文件未套红，不能取消分发',
          });
        }
      },
    });
  });
}

function initFWBtn(type) {
  fwBtnFun();
  $('#form_content').addClass('redBor');
  if (type == 'start') {
    getDocumentBtns();
  } else if (type == 'audit') {
    if (uriParams.role != 'deal' || uriParams.type != 'confirm') {
      var str = '<button class="layui-btn fl" code="7">查看正文</button>';
      var btn = $(str);
      $('.fwbtn').append(btn);
    }

    if (uriParams.type == 'restart') {
      getDocumentBtns();
    } else {
      if (uriParams.type != 'confirm') {
        if (uriParams.type == 'see' && uriParams.gov == '1') {
          var str = "";
            str += `<button 
              class="layui-btn fl" 
              code="41"
            >编辑正文</button>`;
            str += `<button 
              class="layui-btn fl" 
              code="5"
            >分发</button>`;
            str += `<button 
              class="layui-btn fl" 
              code="52"
            >全院分发</button>`;
            str += `<button 
              class="layui-btn fl" 
              code="6"
            >取消分发</button>`;
            var btn = $(str);
            $('.fwbtn').append(btn);
        } else {
          return false;
        }
      }
      if (uriParams.role == 'self' && uriParams.type == 'confirm') {
        return false;
      }
      getDocumentBtns();
    }
  }

  function getDocumentBtns() {
    $.ajax({
      url: '/ts-workflow/workflow/step/button/permissionsList',
      type: 'post',
      contentType: 'application/json; charset=utf-8',
      data: JSON.stringify({
        wfDefinitionId: params.baseData.wfDefinitionId,
        wfStepId: uriParams.approverCurrentStepNo,
        buttonType: 2,
        status: 1,
      }),
      success: function (res) {
        if (res.success) {
          var list = res.object;
          for (var i = 0; i < list.length; i++) {
            if (list[i].status == 1 && list[i].buttonType != 9) {
              if ($('[code=7]').length && list[i].wfBaseButtonId == 7) {
                continue;
              }
              var str = `<button 
                  class="layui-btn fl" 
                  code="${list[i].wfBaseButtonId}"
                >${list[i].buttonName}</button>`;
              if (list[i].wfBaseButtonId == 5) {
                str += `<button 
                  class="layui-btn fl" 
                  code="51"
                >分发下级机构</button>`;
                str += `<button 
                  class="layui-btn fl" 
                  code="52"
                >全院分发</button>`;
              }
              var btn = $(str);
              $('.fwbtn').append(btn);
              //  btn.on('click', function () {
              //      //批阅正文
              //      //修改机关代字
              //      //分发
              //      //编号
              //      //套红
              //      //起草正文
              //  })
            }
          }
        }
      },
    });
  }
}

function SWBtnFun() {
  function openFFWin(data) {
    var data = {
      isCheckDept: 'N',
      userNameStrs: data.userNameList.join(','),
      userIdStrs: data.userCodeList.join(','),
      userCodeStrs: data.userCodeList.join(','),
    };
    $.quoteFun('/common/userSel', {
      trasen: trasenTable,
      title: '人员选择',
      data: data,
      callback: function (names, ids, codes) {
        $.ajax({
          url: '/ts-document/govSendfile/distributionSendFile',
          method: 'post',
          contentType: 'application/json; charset=utf-8',
          data: JSON.stringify({
            userCodeStr: codes.join(','),
            userNameStr: names,
            sendfileId: formData.ID,
            type: 2,
          }),
          success: function (res) {
            if (res.success) {
              layer.msg('分发成功');
            } else {
              layer.msg(res.message);
            }
          },
        });
      },
    });
  }
  //收文分发
  $('body').on('click', '[code="8"]', function () {
    $.ajax({
      url: '/ts-document/govSendfile/getSendFileList',
      type: 'get',
      data: {
        sendfileId: formData.ID,
      },
      success: function (res) {
        var userCodeList = [];
        var userNameList = [];
        if (res.success) {
          for (var i = 0; i < res.object.length; i++) {
            userCodeList.push(res.object[i].userCode);
            userNameList.push(res.object[i].userName);
          }
        }
        openFFWin({
          user: true,
          userCodeList: userCodeList,
          userNameList: userNameList,
          dept: true,
          deptCheck: false,
        });
      },
    });
  });

  //收文分发下级机构
  $('body').on('click', '[code="81"]', function () {
    function openFFWins(data) {
      var data = {
        isCheckDept: 'N',
        userNameStrs: data.userNameList.join(','),
        userIdStrs: data.userCodeList.join(','),
        userCodeStrs: data.userCodeList.join(','),
        isChildOrg: 'Y',
      };
      $.quoteFun('/common/userSel', {
        trasen: trasenTable,
        title: '人员选择',
        data: data,
        noRenderTree: true,
        rowNum: 2000,
        callback: function (names, ids, codes) {
          const element = document.getElementById('form_content');
          const options = {
            dpi: 192, //dpi属性的值为192，表示图像的分辨率
            scale: 2, //scale属性的值为2，表示图像的缩放比例。
            backgroundColor: '#F1F6FE', //backgroundColor属性的值为"#F1F6FE"，表示图像的背景颜色。
          };
          var formFileId = '';
          // 将元素转换为canvas对象
          html2canvas(element, options).then((canvas) => {
            var contentWidth = canvas.width; //获取Canvas(上面元素id 'layout-wrapper')对象的宽度
            var contentHeight = canvas.height; //获取Canvas(上面元素id 'layout-wrapper')对象的高度
            // 创建jsPDF对象
            // jsPDF = jspdf.jsPDF;  //导入jsPDF库，用于创建PDF文件
            const pdf = new jsPDF('1', 'pt', [contentWidth, contentHeight]); //创建一个新的PDF对象，参数包括页面格式（'1'表示A4纸张）、单位（'pt'）和页面尺寸（[contentWidth, contentHeight]）
            var pageData = canvas.toDataURL('image/jpeg', 1.0); //将Canvas对象转换为JPEG格式的数据，并将其存储在pageData变量中。1.0表示图片质量
            var imgX = (contentWidth / 2) * 0.75;
            var imgY = (contentHeight / 2) * 0.75;
            pdf.addImage(pageData, 'JPEG', 0, 0, imgX, imgY); //将JPEG格式的图片添加到PDF文件中，图片的左上角坐标为(0, 0)，宽度为contentWidth，高度为contentHeight
            // pdf.save($('#wfName').html() + ".pdf");
            let base64 = pdf.output('datauristring');
            let file = convertBase64ToFile(base64, $('#wfName').html());
            // 上传到服务器
            let fileData = new FormData();
            fileData.append('file', file);
            $.ajax({
              url:
                common.url + '/ts-document/attachment/fileUpload?module=form',
              type: 'post',
              async: false,
              data: fileData,
              processData: false,
              contentType: false,
              success: function (res) {
                formFileId = res.object[0].fileId;
                $.ajax({
                  url: '/ts-document/govSendfile/distributionSendFile',
                  method: 'post',
                  contentType: 'application/json; charset=utf-8',
                  data: JSON.stringify({
                    userCodeStr: codes.join(','),
                    userNameStr: names,
                    sendfileId: formData.ID,
                    type: 2,
                    isChildOrg: 'Y',
                    formFileId: formFileId,
                  }),
                  success: function (res) {
                    if (res.success) {
                      layer.msg('分发成功');
                    } else {
                      layer.msg(res.message);
                    }
                  },
                });
              },
            });
          });
        },
      });
    }
    $.ajax({
      url: '/ts-document/govSendfile/getSendFileList',
      type: 'get',
      data: {
        sendfileId: formData.ID,
      },
      success: function (res) {
        var userCodeList = [];
        var userNameList = [];
        if (res.success) {
          for (var i = 0; i < res.object.length; i++) {
            userCodeList.push(res.object[i].userCode);
            userNameList.push(res.object[i].userName);
          }
        }
        openFFWins({
          user: true,
          userCodeList: userCodeList,
          userNameList: userNameList,
          dept: true,
          deptCheck: false,
        });
      },
    });
  });

  // 收文分发全院
  $('body').on('click', '[code="82"]', function () {
    layer.confirm(
      '确定要进行全院分发吗?',
      {
        btn: ['确定', '取消'],
        title: '提示',
        closeBtn: 0,
      },
      function (index) {
        $.ajax({
          url: '/ts-document/govSendfile/distributionSendFile',
          method: 'post',
          contentType: 'application/json; charset=utf-8',
          data: JSON.stringify({
            sendfileId: formData.ID,
            type: 2,
            isAll: 'Y',
          }),
          success: function (res) {
            if (res.success) {
              layer.msg('分发成功');
            } else {
              layer.msg(res.message);
            }
          },
        });
      }
    );
  });
  //收文取消分发
  $('body').on('click', '[code="9"]', function () {
    $.ajax({
      url: '/ts-document/govSendfile/getSendFileList',
      type: 'get',
      data: {
        sendfileId: formData.ID,
      },
      success: function (res) {
        if (res.success && res.object.length == 0) {
          layer.msg('暂无分发人员');
        } else if (res.success && res.object.length) {
          var userlist = res.object;
          // for (var i = 0; i < res.object.length; i++) {
          //   userlist.push({
          //     code: res.object[i].id,
          //     name: res.object[i].userName,
          //   });
          // }
          $.quoteFun('/commonPage/cancelDistribution/index', {
            data: {
              userList: userlist,
            },
          });
        }
      },
    });
  });
}

function convertBase64ToFile(urlData, filename) {
  var arr = urlData.split('base64,');
  var type = arr[0].match(/:(.*?);/)[1];
  var fileExt = type.split('/')[1];
  var bstr = atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename + '.' + fileExt, {
    type: type,
  });
}

function initSWBtn(type) {
  if (type == 'start') {
    // $('#fwQc').removeClass('none')
  } else if (type == 'audit') {
    if (
      (uriParams.role != 'deal' && uriParams.role != 'consult') ||
      uriParams.type != 'confirm'
    ) {
      if (uriParams.type == 'see' && uriParams.gov == '1') {
        var str = "";
          str += `<button 
            class="layui-btn fl" 
            code="8"
          >分发</button>`;
          str += `<button 
            class="layui-btn fl" 
            code="82"
          >全院分发</button>`;
          str += `<button 
            class="layui-btn fl" 
            code="9"
          >取消分发</button>`;
          var btn = $(str);
          $('.fwbtn').append(btn);
      } else {
        return false;
      }
    }
    $.ajax({
      url: '/ts-workflow/workflow/step/button/permissionsList',
      type: 'post',
      contentType: 'application/json; charset=utf-8',
      data: JSON.stringify({
        wfDefinitionId: params.baseData.wfDefinitionId,
        wfStepId: uriParams.approverCurrentStepNo,
        buttonType: 3,
        status: 1,
      }),
      success: function (res) {
        if (res.success) {
          var list = res.object;
          for (var i = 0; i < list.length; i++) {
            if (list[i].status == 1 && list[i].buttonType != 9) {
              var str = `<button 
                  class="layui-btn fl" 
                  code="${list[i].wfBaseButtonId}"
                >
                  ${list[i].buttonName}
                </button>`;
              if (list[i].wfBaseButtonId == 8) {
                str += `<button 
                    class="layui-btn fl" 
                    code="81"
                  >分发下级机构</button>`;
                str += `<button 
                    class="layui-btn fl" 
                    code="82"
                  >全院分发</button>`;
              }
              var btn = $(str);
              $('.fwbtn').append(btn);
              //  btn.on('click', function () {
              //      //批阅正文
              //      //修改机关代字
              //      //分发
              //      //编号
              //      //套红
              //      //起草正文
              //  })
            }
          }
          SWBtnFun();
        }
      },
    });
  }
}

/**@desc 渲染流程按钮 */
function initFlowBtn() {
  let { type, role } = uriParams;
  if (type != 'confirm' && type != 'countersign') {
    return false;
  }
  if (type == 'countersign' && role != 'deal') {
    return;
  }

  $.ajax({
    url: '/ts-workflow/workflow/step/button/permissionsList',
    type: 'post',
    contentType: 'application/json; charset=utf-8',
    data: JSON.stringify({
      wfDefinitionId: params.baseData.wfDefinitionId,
      wfStepId: uriParams.approverCurrentStepNo,
      buttonType: 9,
      status: 1,
    }),
    success: function (res) {
      if (res.success) {
        var list = res.object;
        // 抄送等按钮权限控制，status 1: 显示 2：隐藏  null 未设置
        // 如果全部为null 我就认为是老数据，则全部显示
        if (!list.filter((item) => item.status).length) {
          return;
        }

        let hiddenList = list.filter(
          (item) => item.status == 2 || !item.status
        );
        hiddenList.map((item) => {
          $(
            `#foot .content-box .layui-btn[data-btn-code="${item.wfBaseButtonId}"]`
          ).remove();
          // .css('display', 'none');
        });
      }
    },
  });
}
// #endregion
/*******************************流程部分************************************/
// #region
function taskShow() {
  $.quoteFun('../view-new/processView/modules/taskList/index', {
    node: $('#taskBox'),
    wfInstanceId: uriParams.wfInstanceId,
  });
}

function wfShow() {
  $.quoteFun('/commonPage/wfPic/index', {
    node: $('#wfBox'),
    wfDefinitionId: params.baseData.wfDefinitionId,
    workflowName: params.baseData.workflowName,
    workflowNo: params.baseData.workflowNo,
    currentStepNo: uriParams.currentStepNo,
  });
}
//流程节点信息
function setStepName() {
  if (uriParams.businessId && !uriParams.wfInstanceId) {
    $('#wfTypeInfo').text('草稿');
    return;
  }
  if (uriParams.type == 'see' && uriParams.currentStepNo == 'end') {
    $('#wfTypeInfo').html('流程</br>办结');
  } else if (uriParams.type == 'see' && uriParams.currentStepNo != 'end') {
    $('#wfTypeInfo').html('流程</br>在办');
  }
  if (!uriParams.wfInstanceId) {
    return;
  }
  $.ajax({
    url: '/ts-workflow/workflow/wfInst/info/' + uriParams.wfInstanceId,
    method: 'get',
    async: false,
    success: function (res) {
      if (res.success) {
        wfData = res.object;
        var str = '';
        if (res.object && res.object.currentStepName == '重新提交') {
          str = res.object.handleMarkedWords || '';
        } else if (
          res.object &&
          uriParams.type == 'confirm' &&
          res.object.handleMarkedWords
        ) {
          str = res.object.handleMarkedWords || '';
        }
        str && $('.handReminder').html(`<span>办理提示：${str}</span>`);
      }
    },
  });
}
// #endregion
/***************************************打印**********************************************/
// #region
$('body')
  .off('click', '#print')
  .on('click', '#print', function () {
    $.ajax({
      url: `/ts-workflow/workflow/instance/updatePrint`,
      method: 'post',
      contentType: 'application/json;charset=UTF-8',
      data: JSON.stringify({
        wfInstanceId: uriParams.wfInstanceId,
      }),
      async: false,
    });

    var data = dealCustomFormData();
    // if (!params.formData.printTemplate) {
    //   layer.confirm(
    //     '此流程未设置打印模板，确定要进行打印？',
    //     {
    //       btn: ['确定', '取消'],
    //       title: '提示',
    //       closeBtn: 0,
    //     },
    //     function (index) {
    //       printTemplateInit(data.formData);
    //       setTimeout(function () {
    //         layer.close(index);
    //       }, 1000);
    //       return false;
    //     }
    //   );
    // } else {
    // }
    printTemplateInit(data.formData);
  });
// #endregion
/***************************************导出**********************************************/
//#region
$('body')
  .off('click', '#export')
  .on('click', '#export', function () {
    const element = document.getElementById('form_content');

    // 处理textarea 文字被截断的情况
    $('#pdfElementClone').remove();
    const pdfElementClone = element.cloneNode(true);
    $(pdfElementClone)
      .find('textarea')
      .each((index, f) => {
        var $div = $('<div>', {
          id: 'textareaReplaceDiv',
          text: $(f).val(),
        });
        $(f).replaceWith($div);
      });

    $(pdfElementClone)
        .find('input[type="checkbox"]')
        .each((index, checkbox) => {
          const $checkbox = $(checkbox);
          $checkbox.closest('.labelCheckbox').css('margin', '0 6px');

          const isChecked = $checkbox.prop('checked');
          const fakeCheckbox = $('<span>', {
            class: `checkbox-fake${isChecked ? ' checked' : ''}`,
            text: '✔'
          });

          $checkbox.after(fakeCheckbox).remove();
      });

    pdfElementClone.id = 'pdfElementClone';
    $('#form_content').append(pdfElementClone);

    const options = {
      dpi: 192, //dpi属性的值为192，表示图像的分辨率
      scale: 2, //scale属性的值为2，表示图像的缩放比例。
      backgroundColor: '#F1F6FE', //backgroundColor属性的值为"#F1F6FE"，表示图像的背景颜色。
    };
    // 将元素转换为canvas对象
    html2canvas(pdfElementClone, options).then((canvas) => {
      var contentWidth = canvas.width; //获取Canvas(上面元素id 'layout-wrapper')对象的宽度
      var contentHeight = canvas.height; //获取Canvas(上面元素id 'layout-wrapper')对象的高度
      var scale = 800 / contentWidth;
      // 创建jsPDF对象
      // jsPDF = jspdf.jsPDF;  //导入jsPDF库，用于创建PDF文件
      const pdf = new jsPDF('1', 'pt', [contentWidth * scale, contentHeight / 2]); //创建一个新的PDF对象，参数包括页面格式（'1'表示A4纸张）、单位（'pt'）和页面尺寸（[contentWidth, contentHeight]）
      var pageData = canvas.toDataURL('image/jpeg', 1.0); //将Canvas对象转换为JPEG格式的数据，并将其存储在pageData变量中。1.0表示图片质量
      // var imgX = (contentWidth) / 2 * 0.75;
      // var imgY = (contentHeight / 2 * 0.75);
      pdf.addImage(pageData, 'JPEG', 0, 0, contentWidth * scale, contentHeight / 2); //将JPEG格式的图片添加到PDF文件中，图片的左上角坐标为(0, 0)，宽度为contentWidth，高度为contentHeight
      pdf.save($('#wfName').html() + '.pdf');

      $('#pdfElementClone').remove();
    });
  });
  
$('body')
  .off('click', '#exportWord')
  .on('click', '#exportWord', function () {
     var data = dealCustomFormData();
     var print = $(
      params.formData.printTemplate || params.formData.content
     ).clone();
     var temp1 = print.find('.wfFormItem');
     var temp2 = print.find('.printFormItem');
     var items = [].concat(temp1.splice(0), temp2.splice(0));
     for (var i = 0; i < items.length; i++) {
       var keyid = $(items[i]).attr('id');

       $(items[i]).replaceWith(
         printItemInit(items[i], itemMap[keyid], data.formData)
       );
     }
     $('#print_box').empty().append(print);
     layForm.render('radio');

     setTimeout(function () {
       var cl = $('#print_box');
       cl.wordExport($('#wfName').html());
     }, 1000);
  });

function printTemplate() {
  setTimeout(function () {
    $('#print_box').jqprint();
  }, 1000);
}

function printTemplateInit(data) {
  var print = $(
    params.formData.printTemplate || params.formData.content
  ).clone();
  var temp1 = print.find('.wfFormItem');
  var temp2 = print.find('.printFormItem');
  var items = [].concat(temp1.splice(0), temp2.splice(0));
  for (var i = 0; i < items.length; i++) {
    var keyid = $(items[i]).attr('id');
    $(items[i]).replaceWith(printItemInit(items[i], itemMap[keyid], data));
  }
  $('#print_box').empty().append(print);
  layForm.render('radio');
  var printText = '';
  $.ajax({
    url: '/ts-basics-bottom/globalSetting/getAllGlobalSetting',
    method: 'get',
    async: false,
    success: function (res) {
      printText = `${res.object.orgCode}-${new Date().format(
        'yyyy-MM-dd hh:mm'
      )}`;
    },
    error: function (res) {},
  });
  if (params.baseData.showPrintWatermark == 1) {
    common.watermark.canvasImg({
      el: $('#print_box'),
      text: printText,
      width: 220,
      height: 200,
      color: '#999',
    });
  }

  let printFormTable = $('#print_box').children('table');
  if (printFormTable && printFormTable.length) {
    printFormTable.css('width', '96%');
  }
  printTemplate();
}

//导出字段赋值
function printItemInit(items, json, data) {
  var item = $('<div></div>');
  if (!json || json.isHide) {
    console.warn(items, '的字段已被删除');
    return item;
  }
  switch (json.fieldType) {
    case 'input':
      var p = $('<p></p>');
      p.text(data[json.fieldName] || '');
      p.attr('style', 'display:inline-block;width:90%;word-wrap:break-word;white-space:normal;word-break:break-all;');
      item.append(p);
      break;
    case 'textarea':
      var p = $('<p></p>');
      if (data[json.fieldName]) {
        p.html(data[json.fieldName].replace(/(\r\n)|(\n)/g, '<br>') || '');
      }
      item.append(p);
      break;
    case 'number':
      var p = $('<p></p>');
      p.text(data[json.fieldName] || '');
      item.append(p);
      break;
    case 'date':
      var p = $('<p></p>');
      p.text(data[json.fieldName] || '');
      item.append(p);
      break;
    case 'radio':
      var list = json.optionValue.split(',');
      for (var i = 0; i < list.length; i++) {
        if (data[json.fieldName] == list[i]) {
          item.append(list[i]);
        }
      }
      break;
    case 'checkbox':
      var list = json.optionValue.split(',');
      var valList = data[json.fieldName] ? data[json.fieldName].split(',') : [];
      for (var i = 0; i < list.length; i++) {
        var label = $('<label class="labelCheckbox"></label>');
        if (json.wrap === 'Y') label.css('display', 'block');
        var inp = $(
          `<input 
            type="checkbox" 
            class="self-checkbox" 
            name="print_${json.fieldName}"
           />`
        );
        var icon = $('<i class="self-checkbox-icon"></i>');
        inp.val(list[i]);
        if (valList.indexOf(list[i]) != -1) {
          inp.attr('checked', 'checked');
        }
        label.append(inp);
        label.append(icon);
        label.append(list[i]);
        item.append(label);
      }
      break;
    case 'select':
      var p = $('<p></p>');
      if (json.dataSource == 6) {
        let _option = cancelLeave[json.relationWorkflowId].find((e) => {
          return e.ID === data[json.fieldName];
        });
        let innerText = '';
        if (_option) {
          clDpFieldRelation[json.relationWorkflowId].forEach((e, idx) => {
            innerText += `${_option[e.valueFieldname]}`;
            if (idx < clDpFieldRelation[json.relationWorkflowId].length - 1) {
              innerText += '——';
            }
          });
        }
        p.text(innerText || '');
      } else if (json.dataSource == 7) {
        let optionList = $(`[keyid=${$(items).attr('id')}]`).find('option');
        $.each(optionList, function (i, n) {
          if (data[json.fieldName] == $(this).val()) {
            p.text(n.innerText || '');
          }
        });
      } else {
        p.text(data[json.fieldName] || '');
      }
      item.append(p);
      break;
    case 'file':
      var ul = $('<ul></ul>');
      var list = fileupload.file[json.keyId].fileList;
      for (var i = 0; i < list.length; i++) {
        ul.append(`<li>${list[i].originalName}</li>`);
      }
      item.append(ul);
      break;
    case 'comment':
      var box = $(`.comment_box[field-name="${json.fieldName}"]`)
        .find('.hisComment')
        .clone();
      box.find('.approvalFiledDetail span').css('display', 'block');

      if (json.hideApprovalTime) {
        box.attr('hideApprovalTime', 'Y');
      }
      if (canEditRes) {
        let list = box.find('.hisCommentItem');
        for (var i = 0; i < list.length; i++) {
          let remark = $(list[i]).find('textarea').val();
          let pre =
            '<pre><p class="approvalFiledRemark">' + remark + '</p></pre>';
          $(list[i]).find('textarea').addClass('none');
          $(list[i]).prepend(pre);
        }
        box.find('.uploadDiv span').addClass('none');
      } else {
        box.find('.FileList span').addClass('none');
      }
      item.append(box);
      
      // 打印隐藏空意见 
      if (params.baseData.exploitConfiguration.includes('B') && box.length === 0) {
        if ($(items).closest('tr').children().length === 2) {
          $(items).closest('tr').remove()
        } else {
          $(items).parent('td').prev('td').remove();
          $(items).parent('td').remove();
        }
      }
      break;
    case 'signature':
      if (data[json.fieldName].indexOf('downloadFile') > -1){
        var div = `<div 
          class="signatureDiv" 
          keyid="${json.keyId}" 
          id="signature_${json.keyId}"
        >
          <img class="signImg" src="${data[json.fieldName]}"
        ></div>`;
      } else {
        var div = `<div 
        class="signatureDiv" 
        keyid="${json.keyId}" 
        id="signature_${json.keyId}"
      >
        <p>${data[json.fieldName]}</p>
      </div>`;
      }
      item.append(div);
      break;
    case 'serialNumber':
      var p = $('<p></p>');
      p.text(data[json.fieldName] || '');
      item.append(p);
      break;
    case 'personChose':
      var p = $('<p></p>');
      var list = userChooseData[json.keyId] || [];
      var names = [];
      for (var i = 0; i < list.length; i++) {
        names.push(list[i].name);
      }
      p.text(names.join(','));
      item.append(p);
      break;
    case 'deptChose':
      var p = $('<p></p>');
      p.text(data[json.fieldName].split('--')[0] || '');
      item.append(p);
      break;
    case 'processChoose':
      var ul = $('<ul></ul>');
      var list = processChooseData[json.keyId] || [];
      for (var i = 0; i < list.length; i++) {
        ul.append('<li>' + list[i].workflowName + '</li>');
      }
      item.append(ul);
      break;
    case 'hrpHyperlink':
      var ul = $('<ul></ul>');
      var list = data[json.fieldName].split(',');
      for (var i = 0; i < list.length; i++) {
        ul.append(`<li>${list[i]}</li>`);
      }
      item.append(ul);
      break;
    case 'fileTemplate':
      var con = $('<div class="fileTemplate"></div>');
      var box = $('<div class="layui-upload"><ul class="FileList"></ul></div>');
      var res = getFileByIds(json.fileTemplate);
      if (res && res.success) {
        var fileList = res.object;
        var html = '';
        for (var i = 0; i < fileList.length; i++) {
          html = `<li class="file-item" file-id="${fileList[i].id}">
              <a 
                class="fileDown" 
                href="/ts-document/attachment/downloadFile/${fileList[i].id}"
              >${fileList[i].originalName}</a>
            </li>`;
          html += '<span class="fileConnection" style="margin:0 0 0 5px; cursor: pointer;" >收藏</span></li>';
          box.append(html);
        }
      }
      con.append(box);
      item.append(con);
      break;
    case 'remark':
      var div = $(
        `<div>${
          json.remark ? json.remark.replace(/(\r\n)|(\n)/g, '<br>') : ''
        }</div>`
      );
      if (json.isRed) {
        div.css('color', 'red');
      }
      if (json.isBold) {
        div.css('fontWeight', 'bold');
      }
      item.append(div);
      break;
    case 'interworkCom':
      var copy = $(`#form_box .interworkBox[key="${json.fieldName}"]`).clone();
      item.append(copy);
      break;
    case 'interworkSick':
      var copy = $(
        `#form_box .interworkSickBox[key="${json.fieldName}"]`
      ).clone();
      item.append(copy);
      break;
    case 'interworkSettle':
      var copy = $(
        `#form_box .interworkSettleBox[key="${json.fieldName}"]`
      ).clone();
      item.append(copy);
      break;
    case 'interworkPay':
      var copy = $(
        `#form_box .interworkPayBox[key="${json.fieldName}"]`
      ).clone();
      item.append(copy);
      break;
    case 'interworkHosPro':
      var copy = $(
        `#form_box .interworkHosProBox[key="${json.fieldName}"]`
      ).clone();
      item.append(copy);
      break;
    case 'interworkTest':
      var copy = $(
        `#form_box .interworkTestBox[key="${json.fieldName}"]`
      ).clone();
      item.append(copy);
      break;
    case 'workorderSetting':
      var p = $('<p></p>');
      let innerText = '';
      if (json.dataSource == 8) {
        let _deptInfo = ksWorkorderSetting[json.fieldName].find((e) => {
          return e.deptId == data[json.fieldName];
        });
        innerText = _deptInfo ? _deptInfo.deptName : '';
      } else {
        innerText = lxWorkorderSetting[json.fieldName];
      }
      p.text(innerText || '');
      item.append(p);
      break;
    case 'childForm':
      let showType = json.childFormDetail.showType;
      let styleStrDir = {};

      if (json.childFormDetail.fields && json.childFormDetail.fields.length) {
        json.childFormDetail.fields.forEach((f) => {
          if (f.styleStr) styleStrDir[f.fieldName] = f.styleStr;
        });
      }

      var dom = $(`.child-form-table[data-child-form-key="${json.keyId}"]`)
        .closest('.formItem')
        .clone();
      dom.find('.no-print').remove();
      $.each(dom.find('.layui-form-select'), function (i, select) {
        let value = $('input', $(select)).val(),
          tdDom =
            showType == 2
              ? $(select).closest('.shell-layer-input-box')
              : $(select).closest('td');
        tdDom.empty();
        tdDom.append(`<span>${value}</span>`);
      });
      $.each(dom.find('.checkSelectWrap'), function (i, checkSelect) {
        let value = $('input', $(checkSelect)).val(),
          tdDom =
            showType == 2
              ? $(checkSelect).closest('.shell-layer-input-box')
              : $(checkSelect).closest('td');
        tdDom.empty();
        tdDom.append(`<span>${value}</span>`);
      });
      $.each(dom.find('input'), function (i, input) {
        let inputDom = $(input);
        if (inputDom.css('display') == 'none') {
          inputDom.remove();
          return true;
        }
        inputDom.replaceWith(`<span>${inputDom.val()}</span>`);
      });
      $.each(dom.find('textarea'), function (i, input) {
        let inputDom = $(input);
        inputDom.replaceWith(`<span>${inputDom.val()}</span>`);
      });
      $.each(dom.find('ul'), function (i, ul) {
        $(ul).replaceWith(
          `<div style="text-align: left;">${$(ul).html()}</div>`
        );
      });
      $.each(dom.find('li'), function (i, li) {
        $(li).replaceWith(`<p>${$(li).html()}</p>`);
      });
      if (showType == 2) {
        let cellContentDom = dom.find('.cell-content-box'),
          t_head = [],
          t_body = [];
        $(cellContentDom.eq(0))
          .find('.shell-layui-form-label')
          .each((i, labelDom) => {
            $(labelDom).find('span').remove();
            t_head.push(`<th>
            <span>${$(labelDom).text()}</span>
          </th>`);
          }),
          cellContentDom.each((i, cellDom) => {
            let t_td = [];
            $(cellDom)
              .find('.shell-layer-input-box')
              .each((i, colDom) => {
                t_td.push(`<td>${$(colDom).html()}</td>`);
              });
            t_body.push(`<tr>${t_td.join('')}</tr>`);
          });
        let t_table = `<table width="100%">
            <thead>
                <tr>
                  ${t_head.join('')}
                </tr>
            </thead>
            <tbody>
              ${t_body.join('')}
            </tbody>
          </table>`;
        dom.html(t_table);
      }
      $('table', dom).css({
        tableLayout: 'unset',
      });
      $.each(dom.find('th'), function (i, th) {
        let thDom = $(th),
          innerText = $('span', thDom).text(),
          width = thDom.css('width');
        thDom.replaceWith(
          `<th style="border: 1px solid; line-height: 30px; ${
            width ? 'width:' + width + ';' : ''
          }">
            <span>${innerText}</span>
          </th>`
        );
      });
      $.each(dom.find('td'), function (i, td) {
        $(td).css('border', '1px solid');
      });

      // 将页面元素 装入子表单打印盒子
      $.each(dom.children(), function (i, d) {
        let tagName = $(d).prop('tagName');
        // 子表单字段dom与对应配置样式进行匹配设置
        if (tagName === 'TABLE') {
          $(d)
            .find('td')
            .each((iTd, domTd) => {
              let tdFName = $(domTd).attr('data-child-form-item-name');
              if (styleStrDir[tdFName])
                $(domTd).find('span').attr('style', styleStrDir[tdFName]);
            });
        }
        item.append(d);
      });
      item.find('.no-print').remove();
      item.find('.operation-btn').remove();
      item.find('button').remove();
      break;
    case 'childFormCount':
      var p = $('<p></p>');
      p.text(data[json.fieldName] || '');
      item.append(p);
      break;
    case 'leaveStatistics':
      var dom = $(`table[data-key-id="${json.keyId}"]`)
        .closest('.formItem')
        .clone();
      $('table', dom).css({
        tableLayout: 'unset',
      });
      $.each(dom.find('th'), function (i, th) {
        let thDom = $(th),
          innerText = $('span', thDom).text(),
          width = thDom.css('width');
        thDom.replaceWith(
          `<th style="border: 1px solid; line-height: 30px; ${
            width ? 'width:' + width + ';' : ''
          }">
            <span>${innerText}</span>
          </th>`
        );
      });
      $.each(dom.find('td'), function (i, td) {
        $(td).css('border', '1px solid');
      });
      // 将页面元素 装入子表单打印盒子
      $.each(dom.children(), function (i, d) {
        item.append(d);
      });
      break;
    case 'operationItem':
      var dom = $(`table[data-key-id="${json.keyId}"]`)
        .closest('.formItem')
        .clone();
      $.each(dom.find('th'), function (i, th) {
        let thDom = $(th),
          innerText = $('span', thDom).text(),
          width = thDom.css('width');
        thDom.replaceWith(
          `<th style="border: 1px solid; line-height: 30px; ${
            width ? 'width:' + width + ';' : ''
          }">
            <span>${innerText}</span>
          </th>`
        );
      });
      $.each(dom.find('td'), function (i, td) {
        let tdDom = $(td);
        if (tdDom.find('input').length == 1) {
          let text = tdDom.find('input[data-key="auditScr"]').val() || '';
          tdDom.replaceWith(`<td><span>${text}</span></td>`)
        }
        if (tdDom.find('input').length == 2) {
          let auditFlag = tdDom.find('input[data-key="auditFlag"]:checked').val();
          let label = auditFlag == '0' ? '不批准授权' : '批准授权'
          tdDom.replaceWith(`<td><span>${label}</span></td>`)
        }
        tdDom.css('border', '1px solid');
      });
      // 将页面元素 装入子表单打印盒子
      $.each(dom.children(), function (i, d) {
        item.append(d);
      });
      break;
    case 'deliveryInspection':
      var dom = $(`table[data-key-id="${json.keyId}"]`)
        .closest('.formItem')
        .clone();
      $.each(dom.find('th'), function (i, th) {
        let thDom = $(th),
          innerText = $('span', thDom).text(),
          width = thDom.css('width');
        thDom.replaceWith(
          `<th style="border: 1px solid; line-height: 30px; ${
            width ? 'width:' + width + ';' : ''
          }">
            <span>${innerText}</span>
          </th>`
        );
      });
      $.each(dom.find('td'), function (i, td) {
        let tdDom = $(td);
        tdDom.css('border', '1px solid');
      });
      // 将页面元素 装入子表单打印盒子
      $.each(dom.children(), function (i, d) {
        item.append(d);
      });
      break;
    case 'essentialDrugDic':
      var dom = $(`table[data-key-id="${json.keyId}"]`)
        .closest('.formItem')
        .clone();
      dom.find('.no-print').remove();
      $('table', dom).css({
        tableLayout: 'unset',
      });
      $.each(dom.find('input'), function (i, input) {
        let inputDom = $(input);
        inputDom.replaceWith(`<span>${inputDom.val()}</span>`);
      });
      $.each(dom.find('textarea'), function (i, input) {
        let inputDom = $(input);
        inputDom.replaceWith(`<span>${inputDom.val()}</span>`);
      });
      dom.find('th .required').removeClass('required');
      $.each(dom.children(), function (i, d) {
        item.append(d);
      });
      item.find('.no-print').remove();
      break;
    default:
      break;
  }
  return item;
}
//#endregion
// TODO 获取审批上传附件
function getTaskFileList() {
  if (taskFileTable) {
    return;
  }
  $.ajax({
    url: '/ts-workflow/task/file/list',
    type: 'post',
    data: {
      pageNo: 1,
      pageSize: 999,
      wfInstanceId: uriParams.wfInstanceId,
      sord: 'desc',
      sidx: 'create_date',
    },
    success: function (res) {
      var data = res.rows || [];
      initTaskFileTable(data);
    },
  });
}

function initTaskFileTable(data) {
  taskFileTable = new $.trasenTable('taskFileTable', {
    datatype: 'local',
    pager: '',
    data: data,
    shrinkToFit: true,
    rowNum: 999,
    colModel: [
      {
        label: '文件名',
        name: 'fileName',
        sortable: false,
        align: 'center',
      },
      {
        label: '上传人',
        name: 'createUserName',
        sortable: false,
        align: 'center',
      },
      {
        label: '上传时间',
        name: 'createDate',
        sortable: false,
        align: 'center',
      },
      {
        label: '上传节点',
        name: 'wfStepName',
        sortable: false,
        align: 'center',
      },
      {
        label: '操作',
        name: '',
        sortable: false,
        width: 60,
        align: 'center',
        formatter: function (cell, opt, row) {
          var isImg = common.isImg(row.fileName);
          var isDoc = common.isDoc(row.fileName);
          var fileUrl = row.fileUrl.replace(/\\/g, '/');
          var fileId = fileUrl.split('/')[0];
          var prevStr = '';
          if (isDoc) {
            prevStr = `<span 
                style="margin:0 5px; 
                cursor: pointer;" 
                class="viewerDoc2" 
                filename="${row.fileName}" 
                fileid="${fileId}"
              >预览</span>`;
          }
          if (isImg) {
            prevStr = `<span 
                style="margin:0 5px; cursor: pointer;" 
                class="viewerImg" 
                fileurl="${fileId}" 
                filename="${row.fileName}"
              >预览</span>`;
          }
          return `${prevStr}<a href="/ts-document/attachment/downloadFile/${fileId}">下载</a>`;
        },
      },
    ],
  });
}
