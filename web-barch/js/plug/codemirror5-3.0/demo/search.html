<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <title>CodeMirror: Search/Replace Demo</title>
    <link rel="stylesheet" href="../lib/codemirror.css">
    <script src="../lib/codemirror.js"></script>
    <script src="../mode/xml/xml.js"></script>
    <script src="../lib/util/dialog.js"></script>
    <link rel="stylesheet" href="../lib/util/dialog.css">
    <script src="../lib/util/searchcursor.js"></script>
    <script src="../lib/util/search.js"></script>
    <link rel="stylesheet" href="../doc/docs.css">

    <style type="text/css">
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      dt {font-family: monospace; color: #666;}
    </style>
  </head>
  <body>
    <h1>CodeMirror: Search/Replace Demo</h1>

    <form><textarea id="code" name="code">
  <dt id="option_indentWithTabs"><code>indentWithTabs (boolean)</code></dt>
  <dd>Whether, when indenting, the first N*8 spaces should be
  replaced by N tabs. Default is false.</dd>

  <dt id="option_tabMode"><code>tabMode (string)</code></dt>
  <dd>Determines what happens when the user presses the tab key.
  Must be one of the following:
    <dl>
      <dt><code>"classic" (the default)</code></dt>
      <dd>When nothing is selected, insert a tab. Otherwise,
      behave like the <code>"shift"</code> mode. (When shift is
      held, this behaves like the <code>"indent"</code> mode.)</dd>
      <dt><code>"shift"</code></dt>
      <dd>Indent all selected lines by
      one <a href="#option_indentUnit"><code>indentUnit</code></a>.
      If shift was held while pressing tab, un-indent all selected
      lines one unit.</dd>
      <dt><code>"indent"</code></dt>
      <dd>Indent the line the 'correctly', based on its syntactic
      context. Only works if the
      mode <a href="#indent">supports</a> it.</dd>
      <dt><code>"default"</code></dt>
      <dd>Do not capture tab presses, let the browser apply its
      default behaviour (which usually means it skips to the next
      control).</dd>
    </dl></dd>

  <dt id="option_enterMode"><code>enterMode (string)</code></dt>
  <dd>Determines whether and how new lines are indented when the
  enter key is pressed. The following modes are supported:
    <dl>
      <dt><code>"indent" (the default)</code></dt>
      <dd>Use the mode's indentation rules to give the new line
      the correct indentation.</dd>
      <dt><code>"keep"</code></dt>
      <dd>Indent the line the same as the previous line.</dd>
      <dt><code>"flat"</code></dt>
      <dd>Do not indent the new line.</dd>
    </dl></dd>
</textarea></form>

    <script>
var editor = CodeMirror.fromTextArea(document.getElementById("code"), {mode: "text/html", lineNumbers: true});
</script>

    <p>Demonstration of primitive search/replace functionality. The
    keybindings (which can be overridden by custom keymaps) are:</p>
    <dl>
      <dt>Ctrl-F / Cmd-F</dt><dd>Start searching</dd>
      <dt>Ctrl-G / Cmd-G</dt><dd>Find next</dd>
      <dt>Shift-Ctrl-G / Shift-Cmd-G</dt><dd>Find previous</dd>
      <dt>Shift-Ctrl-F / Cmd-Option-F</dt><dd>Replace</dd>
      <dt>Shift-Ctrl-R / Shift-Cmd-Option-F</dt><dd>Replace all</dd>
    </dl>
    <p>Searching is enabled by
    including <a href="../lib/util/search.js">lib/util/search.js</a>
    and <a href="../lib/util/searchcursor.js">lib/util/searchcursor.js</a>.
    For good-looking input dialogs, you also want to include
    <a href="../lib/util/dialog.js">lib/util/dialog.js</a>
    and <a href="../lib/util/dialog.css">lib/util/dialog.css</a>.</p>
  </body>
</html>
