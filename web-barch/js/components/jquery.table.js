/**
 * @param
 * colModel 新增自定义表头class处理  headClasses
 * **/
'use strict';
(function ($, window, document, undefined) {
  var lastSel;

  var defaults = {
    // url: urls,                 //请求地址
    datatype: 'json', // 访问方式  json：请求服务器数据    local：本地数据
    // data: tdDataInfo,   //本地数据
    // colNames: formTit,     //表头名字
    // colModel: formField,   //表字段
    rowNum: 100,
    rowList: [30, 50, 100, 200],
    pager: 'grid-pager', //分页id
    sortname: 'create_date',
    mtype: 'post', //  post   get   请求
    viewrecords: true,
    postData: {},
    sortorder: 'desc',
    sortable: true,
    autowidth: true,
    altRows: true,
    footerrow: false,
    userDataOnFooter: false, // 显示底部
    repeatitems: true,
    jsonReader: {
      root: 'rows', // json中代表实际模型数据的入口
      page: 'pageNo', // json中代表当前页码的数据
      total: 'pageCount', // json中代表页码总数的数据
      records: 'totalCount', // json中代表数据行总数的数据
      userdata: 'rows',
      repeatitems: false,
    },
    //单元格编辑
    cellEdit: false, // 单元格编辑   true:可编辑    false:不可编辑
    cellsubmit: 'clientArray',
    cellurl: '',
    editurl: null,
    prmNames: {
      page: 'pageNo',
      rows: 'pageSize',
    },
    rowKey: undefined,
    scroll: undefined,
    cellLayout: 1,
    scrollOffset: 0,
    rownumbers: true, //如果为ture则会在表格左边新增一列，显示行顺序号，从1开始递增。此列名为'rn'
    shrinkToFit: false, //表格宽度  true 初始化列宽度  false 列宽度使用colModel指定的宽度
    multiselect: false, //关闭全选
    multiboxonly: false, //为ture时只有选择checkbox才会起作用
    //表格还在完成后执行

    // 选择数据
    onSelectAll: null,
    // 单元格选择
    // onCellSelect: null,
    // onSelectRow: null,

    // 子表
    subGrid: false,
    subGridRowExpanded: null,
    beforeSubmitCell: null,
    afterSubmitCell: null,
    onCellSelect: function (rowid) {},
    loadComplete: function (xhr, o) {
      var pdom = $(this).closest('.ui-jqgrid-view');

      //admin  放开权限
      if (common.mainId != 'admin') {
        if (common.showButton) {
          $.each(common.showButton, function (i, v) {
            var dom = '[id=' + v.resourceId + ']';
            $(dom).show();
          });
        }
      }
      //合并表格出现的bug处理
      var thvDom = pdom.find('.ui-jqgrid-hdiv thead .jqg-first-row-header');
      var ln = pdom.find('.ui-jqgrid-hdiv thead .ui-jqgrid-labels').length;
      if (thvDom.length > 0) {
        var tp = ln * 31.3333;
        thvDom.closest('.ui-jqgrid-hbox').addClass('tableMergeHead');
        $(this)
          .closest('.ui-jqgrid-view')
          .find('.ui-jqgrid-bdiv')
          .css({
            top: tp + 'px',
            'padding-top': '0',
          });
      }
    },
    gridComplete: function () {},
    localReader: {
      root: 'data',
      page: 'page',
      total: 'total',
      records: 'records',
    },
    queryFormId: null,
    buidQueryParams: function () {},
    //双击
    ondblClickRow: function () {},
    //表格加载失败
    loadError: function () {},
    //选中行后
    onSelectRow: function (rowId) {},
    afterEditCell: function () {},
    afterSaveCell: function () {},
    beforeSaveCell: function () {},
    //当插入每行时触发
    afterInsertRow: function () {},
    //当用户点击行在未选择此行时触发
    beforeSelectRow: function () {},
    onPaging: function () {},
  };

  var trasenTable = function (tableId, options) {
    this.tableId = tableId;
    this.options = $.extend({}, defaults, options);
    this.oTable = null;
    this.init();
  };

  //隐藏列
  trasenTable.prototype.hideCol = function (colname) {
    var self = this;
    return self.oTable.setGridParam().hideCol(colname).trigger('reloadGrid');
  };

  //显示列
  trasenTable.prototype.showCol = function (colname) {
    var self = this;
    return self.oTable.setGridParam().showCol(colname).trigger('reloadGrid');
  };

  //获取colmodle
  trasenTable.prototype.colmodle = function () {
    var self = this;
    return self.oTable.jqGrid('getGridParam', 'colModel');
  };

  //获取colmodle
  trasenTable.prototype.colname = function () {
    var self = this;
    return self.oTable.jqGrid('getGridParam', 'colNames');
  };

  //选中行
  trasenTable.prototype.setSelection = function (rowid) {
    var self = this;
    return self.oTable.jqGrid('setSelection', rowid);
  };

  //选中行id
  trasenTable.prototype.getSelectRowId = function () {
    var self = this;
    return self.oTable.getGridParam('selrow');
  };

  //获取所有选中行id
  trasenTable.prototype.getSelectAllRowIDs = function () {
    var self = this;
    return self.oTable.getGridParam('selarrrow');
  };

  //取表格所有行id
  trasenTable.prototype.getDataIDs = function () {
    var self = this;
    return self.oTable.jqGrid('getDataIDs');
  };

  // 更新一行数据
  trasenTable.prototype.setRowData = function (rowid, data) {
    var self = this;
    return self.oTable.jqGrid('setRowData', rowid, data);
  };

  // 清空表格
  trasenTable.prototype.clearGridData = function () {
    var self = this;
    return self.oTable.jqGrid('clearGridData');
  };

  //选中行数据
  trasenTable.prototype.getSelectRowData = function () {
    var self = this;
    var id = self.oTable.getGridParam('selrow');
    return self.getRowData(id);
  };

  //选中行原始数据
  trasenTable.prototype.getSourceRowData = function (rowid) {
    var self = this;
    var row = self.oTable.jqGrid('getGridParam', 'userData');
    var id = rowid || self.oTable.getGridParam('selrow');
    if (id === null) {
      return undefined;
    }
    var data = null;
    for (var i in row) {
      if (self.options.rowKey) {
        if (row[i][self.options.rowKey] == id) {
          data = JSON.parse(JSON.stringify(row[i]));
        }
      } else {
        if (isNaN(id)) {
          if (row[i].id == id) {
            data = JSON.parse(JSON.stringify(row[i]));
          }
        } else {
          if (row[i].id == id) {
            data = JSON.parse(JSON.stringify(row[i]));
          } else if (parseInt(id) - 1 == i) {
            data = JSON.parse(JSON.stringify(row[i]));
          }
        }
      }

      if (data) {
        return data;
      }
    }
    return data;
  };

  //表格原始数据
  trasenTable.prototype.getSourceAllData = function () {
    var self = this;
    return self.oTable.jqGrid('getGridParam', 'userData');
  };

  //所有选中行数据
  trasenTable.prototype.getSelectAllRowData = function () {
    var self = this;
    var ids = self.oTable.getGridParam('selarrrow');
    var datas = [];
    for (var i in ids) {
      datas.push(self.getRowData(ids[i]));
    }
    return datas;
  };
  //所有选中行原始数据
  trasenTable.prototype.getSourceAllRowData = function () {
    var self = this;
    var ids = self.oTable.getGridParam('selarrrow');
    var datas = [];
    for (var i in ids) {
      datas.push(self.getSourceData(ids[i]));
    }
    return datas;
  };

  //获取行数据  传行id
  trasenTable.prototype.getRowData = function (id) {
    var self = this;
    return self.oTable.jqGrid('getRowData', id);
  };

  //获取行原始数据  传行id
  trasenTable.prototype.getSourceData = function (id) {
    var self = this;
    var row = self.oTable.jqGrid('getGridParam', 'userData');
    var data = null;
    for (var i in row) {
      if (row[i].id === id) {
        data = row[i];
      } else if (parseInt(id) - 1 === i) {
        data = row[i];
      }
    }
    return data;
  };

  // 获取所有本地数据
  trasenTable.prototype.getLocalAllData = function () {
    var self = this;
    //获取显示配置记录数量
    var rowNum = self.oTable.jqGrid('getGridParam', 'rowNum');
    //获取查询得到的总记录数量
    var total = self.oTable.jqGrid('getGridParam', 'records');
    //设置rowNum为总记录数量并且刷新jqGrid，使所有记录现出来调用getRowData方法才能获取到所有数据
    self.oTable
      .jqGrid('setGridParam', {
        rowNum: total,
      })
      .trigger('reloadGrid');
    var rowIds = self.oTable.jqGrid('getDataIDs');
    var rows = [];
    for (var i in rowIds) {
      var data = self.oTable.jqGrid('getRowData', rowIds[i]);
      if (!data.rowid) {
        data.rowid = rowIds[i];
      }
      rows.push(data);
    }
    //还原原来显示的记录数量
    self.oTable
      .jqGrid('setGridParam', {
        rowNum: rowNum,
      })
      .trigger('reloadGrid');
    return rows;
  };

  //所有数据
  trasenTable.prototype.getAllData = function () {
    var self = this;
    var rowIds = self.oTable.jqGrid('getDataIDs');
    var datas = [];
    for (var i in rowIds) {
      var data = self.oTable.jqGrid('getRowData', rowIds[i]);
      if (!data.rowid) {
        data.rowid = rowIds[i];
      }
      datas.push(data);
    }
    return datas;
  };

  //返回请求的参数信息
  trasenTable.prototype.getGridParamByKey = function (key) {
    var self = this;
    return self.oTable.getGridParam(key);
  };

  //设置选中行
  trasenTable.prototype.setSelection = function (rowid) {
    var self = this;
    return self.oTable.jqGrid('setSelection', rowid);
  };

  //添加一行
  trasenTable.prototype.addRowData = function (rowid, data, w) {
    var self = this;
    return self.oTable.jqGrid('addRowData', rowid, data, w);
  };

  //保存修改行
  trasenTable.prototype.saveRow = function (rowid) {
    var self = this;
    return self.oTable.jqGrid('saveRow', rowid);
  };

  //保存表格
  trasenTable.prototype.saveRowAll = function () {
    var self = this;
    var rowIds = self.oTable.jqGrid('getDataIDs');
    for (var i in rowIds) {
      var nn = self.oTable.jqGrid('saveRow', rowIds[i]);
    }
    // return true;
  };

  //删除行
  trasenTable.prototype.delRowData = function (rowid) {
    var self = this;
    return self.oTable.jqGrid('delRowData', rowid);
  };

  //t添加行
  trasenTable.prototype.addRow = function (rowid, data) {
    var self = this;
    return self.oTable.jqGrid('addRow');
  };

  //某一单元格设置为编辑状态
  trasenTable.prototype.editCell = function (iRow, iCol) {
    var self = this;
    return self.oTable.jqGrid('editCell', iRow, iCol, true);
  };

  //设置单元格value
  trasenTable.prototype.setCell = function (rowId, cellname, value) {
    var self = this;
    return self.oTable.jqGrid('setCell', rowId, cellname, value);
  };

  //取单元格value
  trasenTable.prototype.getCell = function (rowId, iCol) {
    var self = this;
    return self.oTable.jqGrid('getCell', rowId, iCol);
  };

  //设置单元格编辑状态切换
  trasenTable.prototype.setColProp = function (rowid, name, type) {
    var self = this;
    self.oTable.setColProp(name, {
      editable: type,
    });
    self.oTable.jqGrid('setColProp', name, {
      editable: type,
    });
    // self.oTable.jqGrid("editRow",rowid,type);
  };

  //设置行编辑状态
  trasenTable.prototype.editRowT = function (rowid) {
    var self = this;
    self.oTable.jqGrid('editRow', rowid, true);
  };
  //重新渲染宽度
  trasenTable.prototype.resizeGrid = function () {
    var self = this;
    return self.oTable.jqGrid('resizeGrid');
  };

  //刷新本地表格数据，不请求服务器
  trasenTable.prototype.staticrefresh = function (data) {
    var self = this;
    self.oTable.jqGrid('clearGridData');
    self.oTable
      .jqGrid('setGridParam', {
        datatype: 'local',
        data: data,
        page: 1,
      })
      .trigger('reloadGrid');
  };

  trasenTable.prototype.refresh = function () {
    var self = this;
    var pData = self.oTable.getGridParam('postData');
    if (typeof pData == 'string') {
      pData = JSON.parse(pData);
    }
    var queryParams = {};
    // queryFormId不为空直接获取表单数据作为查询条件
    if (self.queryFormId) {
      var form = $('#' + this.queryFormId);
      var params = form.serializeArray();
      params.map(function (item) {
        // if (item.value) {
        queryParams[item.name] = item.value;
        // }
        return item;
      });
    }
    if (self.buidQueryParams) {
      queryParams = $.extend(true, queryParams, self.buidQueryParams());
    }
    var postData = $.extend(true, pData, queryParams);
    if (self.options.ajaxGridOptions) {
      if (
        self.options.ajaxGridOptions.contentType ==
        'application/json; charset=utf-8'
      ) {
        // postData = postData;
        postData = JSON.stringify(postData);
      }
    }
    self.oTable.setGridParam({
      page: 1,
      postData: postData,
    });
    self.oTable.trigger('reloadGrid');
  };
  //刷新之后停留在当前页面相同位置
  trasenTable.prototype.refreshTable = function () {
    var self = this;
    let _top = $('#' + self.tableId)
      .parents('.ui-jqgrid-bdiv')
      .scrollTop();
    var pData = self.oTable.getGridParam('postData');
    if (typeof pData == 'string') {
      pData = JSON.parse(pData);
    }
    var queryParams = {};
    // queryFormId不为空直接获取表单数据作为查询条件
    if (self.queryFormId) {
      var form = $('#' + this.queryFormId);
      var params = form.serializeArray();
      params.map(function (item) {
        // if (item.value) {
        queryParams[item.name] = item.value;
        // }
        return item;
      });
    } else {
      queryParams = self.buidQueryParams();
    }
    var postData = $.extend(true, pData, queryParams);
    if (self.options.ajaxGridOptions) {
      if (
        self.options.ajaxGridOptions.contentType ==
        'application/json; charset=utf-8'
      ) {
        postData = postData;
      }
    }
    self.oTable.setGridParam({
      page: pData.pageNo,
      postData: postData,
    });
    self.oTable.trigger('reloadGrid');
    $('#' + self.tableId)
      .parents('.ui-jqgrid-bdiv')
      .scrollTop(_top);
  };

  trasenTable.prototype.init = function () {
    var self = this;
    var options = self.options;
    var tableId = self.tableId;

    for (var i = 0; i < options.colModel.length; i++) {
      if (options.colModel[i].key === true) {
        self.options.rowKey = options.colModel[i].name;
        break;
      }
    }

    var _table = $('#' + tableId + '').jqGrid({
      url: options.url,
      datatype: options.datatype,
      // colNames: options.colNames,
      cellLayout: options.cellLayout,
      data: options.data,
      autoScroll: options.autoScroll,
      sortable: options.sortable,
      scrollOffset: options.scrollOffset,
      footerrow: options.footerrow,
      userDataOnFooter: options.userDataOnFooter, // 底部
      ajaxGridOptions: options.ajaxGridOptions || {
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
      },
      scroll: options.scroll || undefined,
      serializeGridData: options.serializeGridData || null,
      colModel: options.colModel,
      colNames: options.colNames,
      rowNum: options.rowNum,
      postData: options.postData,
      rowList: options.rowList,
      pager: options.pager,
      width: options.width || '',
      sortname: options.sortname,
      mtype: options.mtype,
      viewrecords: options.viewrecords,
      sortorder: options.sortorder,
      autowidth: options.autowidth,
      altRows: options.altRows,
      cellEdit: options.cellEdit,
      cellsubmit: options.cellsubmit,
      cellurl: options.cellurl,
      editurl: options.editurl,
      prmNames: options.prmNames,
      rownumbers: options.rownumbers,
      shrinkToFit: options.shrinkToFit,
      multiselect: options.multiselect,
      multiboxonly: options.multiboxonly,
      loadComplete: function (xhr) {
        //admin  放开权限
        if (common.mainId != 'admin') {
          if (common.showButton) {
            $.each(common.showButton, function (i, v) {
              var dom = '[id=' + v.resourceId + ']';
              $(dom).show();
            });
          }
        }
        //合并表格出现的bug处理
        var thvDom = $(this)
          .closest('.ui-jqgrid-view ')
          .find('.ui-jqgrid-hdiv thead .jqg-first-row-header');
        var ln = $(this)
          .closest('.ui-jqgrid-view ')
          .find('.ui-jqgrid-hdiv thead .ui-jqgrid-labels').length;
        if (thvDom.length > 0) {
          var tp = ln * 31.3333;
          thvDom.closest('.ui-jqgrid-hbox').addClass('tableMergeHead');
          $(this)
            .closest('.ui-jqgrid-view')
            .find('.ui-jqgrid-bdiv')
            .css({
              top: tp + 'px',
              'padding-top': '0',
            });
        }
        if (options.loadComplete) {
          options.loadComplete(xhr, this);
        }
      },
      gridComplete: function () {
        var pdom = $(this).closest('.ui-jqgrid-view');
        var thh = pdom.find('.ui-jqgrid-hdiv').height();
        var sx = pdom.find('.ui-jqgrid-bdiv').css('position');
        if (sx == 'relative') {
          thh = 0;
        }
        pdom.find('.ui-jqgrid-bdiv').css('top', thh).scrollTop(0);

        var bDiv = $(this).closest('.ui-jqgrid').find('.ui-jqgrid-bdiv');
        var hDiv = $(this).closest('.ui-jqgrid').find('.ui-jqgrid-hdiv');
        var sDiv = $(this).closest('.ui-jqgrid').find('.ui-jqgrid-sdiv');
        var fBdiv = $(this)
          .closest('.ui-jqgrid')
          .find('.frozen-bdiv.ui-jqgrid-bdiv');
        // var funs = $._data(bDiv[0], 'events');
        if (window.OverlayScrollbars) {
          if ($(this).jqGrid('getGridParam').rowNum > 200) {
            var scrollbar = OverlayScrollbars(bDiv[0]);
            scrollbar && scrollbar.destroy();
          } else {
            var scrollbar = OverlayScrollbars(bDiv[0], {
              callbacks: {
                onScroll: (eventArgs) => {
                  hDiv.scrollLeft($(eventArgs.target).scrollLeft());
                  sDiv.scrollLeft($(eventArgs.target).scrollLeft());
                  fBdiv = $(this)
                    .closest('.ui-jqgrid')
                    .find('.frozen-bdiv.ui-jqgrid-bdiv');
                  fBdiv.scrollTop($(eventArgs.target).scrollTop());
                },
              },
            });
            scrollbar.scroll(0);
          }
        }

        if (options.gridComplete) {
          options.gridComplete.apply(this);
        }
      },
      afterInsertRow: options.afterInsertRow,
      beforeSelectRow: options.beforeSelectRow,
      onPaging: options.onPaging,
      ondblClickRow: options.ondblClickRow,
      onSortCol: options.onSortCol, //当点击排序列但是数据还未进行变化时触发
      // 字表
      subGrid: options.subGrid,
      subGridRowExpanded: options.subGridRowExpanded,
      beforeSubmitCell: options.beforeSubmitCell,
      afterSubmitCell: options.afterSubmitCell,

      // 选择数据
      onSelectAll: options.onSelectAll,
      onSelectRow: options.onSelectRow,

      loadError: options.loadError,
      jsonReader: options.jsonReader,
      // 本地分页
      localReader: options.localReader,
      // onSelectRow: options.onSelectRow,
      afterEditCell: options.afterEditCell,
      afterSaveCell: options.afterSaveCell,
      onCellSelect: options.onCellSelect,

      beforeSaveCell: options.beforeSaveCell,
      /** @desc 何锴 2021/11/26 解决表格table横向滚动后修改列宽导致表头和列表对不齐 start */
      resizeStop: function (newwidth, index) {
        var bDiv = $(this).closest('.ui-jqgrid').find('.ui-jqgrid-bdiv');
        var hDiv = $(this).closest('.ui-jqgrid').find('.ui-jqgrid-hdiv');
        var sDiv = $(this).closest('.ui-jqgrid').find('.ui-jqgrid-sdiv');
        var fBdiv = $(this)
          .closest('.ui-jqgrid')
          .find('.frozen-bdiv.ui-jqgrid-bdiv');

        if (window.OverlayScrollbars) {
          var scrollbar = OverlayScrollbars(bDiv[0]);
          if (scrollbar) {
            let position = scrollbar.scroll().position || {};
            hDiv.scrollLeft(position.x - 1);
            sDiv.scrollLeft(position.x - 1);
          }
        }
        if (options.resizeStop) {
          options.resizeStop.call(this, newwidth, index);
        }
      },
      /** @desc 何锴 2021/11/26 解决表格table横向滚动后修改列宽导致表头和列表对不齐 end */
    });
    self.oTable = _table;
    self.buidQueryParams = self.options.buidQueryParams;
    self.queryFormId = options.queryFormId;
  };
  window.trasenTable = trasenTable;
  $.trasenTable = trasenTable;
})(jQuery, window, document);
