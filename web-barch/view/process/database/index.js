'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };

    module.exports = {
        init: init,
    };

    //模版页面脚本
    var perform = function () {
        layui.use(['form', 'laydate', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                zTreeSearch = layui.zTreeSearch;
            form.render();
            var trasenTable = new $.trasenTable('grid-table-processDatabase', {
                url: '/ts-form/dpTable/list',
                rowNum: 100,
                pager: 'grid-page-processDatabase',
                shrinkToFit: true,
                colModel: [
                    {
                        name: 'id',
                        index: 'id',
                        editable: false,
                        hidden: true,
                    },
                    {
                        name: 'isSyn',
                        index: 'isSyn',
                        editable: false,
                        hidden: true,
                    },
                    {
                        label: '表名',
                        sortable: false,
                        name: 'tableName',
                        index: 'table_name',
                        width: 100,
                        editable: false,
                        align: 'center',
                    },
                    {
                        label: '表描述',
                        sortable: false,
                        name: 'tableComment',
                        index: 'table_comment',
                        width: 200,
                        editable: false,
                        align: 'center',
                    },
                    {
                        label: '是否同步',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 100,
                        editable: false,
                        align: 'center',
                        formatter: function (cell, opt, row) {
                            var html = '';
                            if (row.isSyn == 1) {
                                html = '已同步';
                            } else {
                                html = '未同步';
                            }
                            return html;
                        },
                    },
                    {
                        label: '创建人员',
                        sortable: false,
                        name: 'createUserName',
                        index: 'create_user_name',
                        width: 65,
                        editable: false,
                        align: 'center',
                    },
                    {
                        label: '创建日期',
                        sortable: false,
                        name: 'createDate',
                        index: 'create_date',
                        width: 145,
                        editable: false,
                        align: 'center',
                    },
                    // {
                    //     label: '更新人员',
                    //     sortable: false,
                    //     name: 'updateUserName',
                    //     index: 'update_user_name',
                    //     width: 65,
                    //     editable: false,
                    //     align: "center"
                    // },
                    // {
                    //     label: '更新日期',
                    //     sortable: false,
                    //     name: 'updateDate',
                    //     index: 'update_date',
                    //     width: 145,
                    //     editable: false,
                    //     align: "center"
                    // },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button class="layui-btn  databaseEditBtn" class=""  title="编辑" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button>';
                            if (row.isSyn == 0) {
                                btns += '<button class="layui-btn  databaseSynBtn" class="" title="同步" row-id="' + options.rowId + '"> <i class="fa fa-refresh deal_icon" aria-hidden="true"></i>同步 </button>';
                            }
                            btns += '<button class="layui-btn  databaseDeleteBtn" class=""  title="删除" row-id="' + options.rowId + '"> <i class="fa fa-trash deal_icon"  aria-hidden="true"></i>删除 </button>';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                queryFormId: 'workflowDatabasequeryForm',
            });

            function refreshTable() {
                trasenTable.refresh();
            }
            //查询
            form.on('submit(workflowDatabasesearch)', function (data) {
                refreshTable();
            });

            //删除
            $('#processDataBase')
                .off('click', '.databaseDeleteBtn')
                .on('click', '.databaseDeleteBtn', function () {
                    var rowId = $(this).attr('row-id');
                    var rowData = trasenTable.getSourceRowData(rowId);
                    if (rowId == null) {
                        layer.msg('请选择一条需要删除的数据!');
                        return;
                    }

                    if (rowData.isSyn == 1) {
                        layer.msg('已同步数据表不能删除!');
                        return;
                    }

                    layer.confirm(
                        '确定要删除当前选中的数据吗？',
                        {
                            btn: ['确定', '取消'],
                            title: '提示',
                            closeBtn: 0
                        },
                        function (index) {
                            layer.close(index);
                            $.ajax({
                                type: 'post',
                                url: common.url + '/ts-form/dpTable/delete/' + rowData.id,
                                success: function (resp) {
                                    if (resp.success) {
                                        trasenTable.refresh();
                                        layer.closeAll();
                                        layer.msg('操作成功');
                                    } else {
                                        layer.msg(resp.message || '操作失败！');
                                    }
                                },
                            });
                        }
                    );
                });

            //同步
            $('#processDataBase')
                .off('click', '.databaseSynBtn')
                .on('click', '.databaseSynBtn', function () {
                    var rowId = $(this).attr('row-id');
                    var rowData = trasenTable.getSourceRowData(rowId);
                    if (rowId == null) {
                        layer.msg('请选择一个数据进行操作！');
                        return;
                    }

                    if (rowData.isSyn == 1) {
                        layer.msg('已同步数据,不可重复操作!');
                        return;
                    }

                    layer.confirm(
                        '确定同步当前选中的数据吗？',
                        {
                            btn: ['确定', '取消'],
                            title: '提示',
                            closeBtn: 0
                        },
                        function (index) {
                            layer.close(index);
                            $.ajax({
                                type: 'post',
                                url: common.url + '/ts-form/dpTable/synDb/' + rowData.id,
                                success: function (resp) {
                                    if (resp.success) {
                                        trasenTable.refresh();
                                        layer.closeAll();
                                        layer.msg('操作成功');
                                    } else {
                                        layer.msg(resp.message || '操作失败！');
                                    }
                                },
                            });
                        }
                    );
                });
            //修改
            $('#processDataBase')
                .off('click', '.databaseEditBtn')
                .on('click', '.databaseEditBtn', function () {
                    var rowId = $(this).attr('row-id');
                    var rowData = trasenTable.getSourceRowData(rowId);
                    if (rowId == null) {
                        layer.msg('请选择一个记录进行操作.');
                        return;
                    }
                    $.ajax({
                        type: 'post',
                        contentType: 'application/json; charset=utf-8',
                        url: common.url + '/ts-form/dpTable/findById/' + rowData.id + "?pcShow=all",
                        success: function (res) {
                            if (res.success) {
                                $.quoteFun('process/database/edit', {
                                    title: '编辑数据库表',
                                    ref: refreshTable,
                                    data: res.object,
                                });
                            } else {
                            }
                        },
                    });
                });
            //新增
            $('#processDataBase')
                .off('click', '#databaseAddBtn')
                .on('click', '#databaseAddBtn', function () {
                    $.quoteFun('process/database/edit', {
                        title: '新增数据库表',
                        ref: refreshTable,
                    });
                });
        });
    };
});
