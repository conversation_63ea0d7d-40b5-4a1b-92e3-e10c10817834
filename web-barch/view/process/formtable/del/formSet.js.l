"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var editor;
        var selDiv = null;
        layui.use(['form', 'trasen', 'element'], function () {
            var form = layui.form
            var objectData = null
            var contentJson = {},
                optionArray = [];
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                maxmin: false,
                shadeClose: false,
                area: ['100%', '100%'], //宽高
                content: html,
                success: function (layero, index) {
                    $.ajax({
                        type: "post",
                        url: '' + '/ts-form/dpFormTemplate/findById/' + opt.data.id,
                        dateType: "json",
                        contentType: 'application/json',
                        success: function (res) {
                            if (res.success) {
                                objectData = res.object
                                init(res.object)
                            } else {
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            layer.msg(res.message);
                        }
                    });
                },
                end: function () {
                    //弹窗后销毁富文本,不然再次弹出后依然不好使
                    KindEditor.remove('#editContent');
                }
            })
            //iframe绑定事件
            function iframeClick() {
                var testiframe = $('#formSetDiv').find('iframe')[0].contentWindow;
                $(testiframe.document).off('click', 'td').on('click', 'td', function () {
                    var div = $(this).children('div');
                    selDiv = div;
                    if (!div.attr('class') || div.attr('class').indexOf('wfFormItem') == -1) {
                        return
                    }
                    initAttrList()
                    var options = div.attr('class').split(" ")[1].split("%");
                    for (var i = 0; i < options.length; i++) {
                        var optionKey = options[i].split(":")[0],
                            optionValue = options[i].split(":")[1] || "";

                        if (optionKey == "keyId") {
                            $("#formSetDiv [name='keyId']").val(optionValue)
                        } else if (optionKey == "wordNameKey") {
                            $("#formSetDiv #wordName").attr("selkey", optionValue)
                        } else if (optionKey == "wordName") {
                            $("#formSetDiv #wordName").val(optionValue)
                        } else if (optionKey == "wordType") {
                            $("#formSetDiv #wordType").val(optionValue)
                        } else if (optionKey == "wordTypeKey") {
                            $("#formSetDiv #wordType").attr("selkey", optionValue)
                        } else if (optionKey == "placeholderContent") {
                            $("#formSetDiv #placeholderContent").val(optionValue)
                        } else if (optionKey == "defaultVal") {
                            $("#formSetDiv #defaultVal").val(optionValue)
                        } else if (optionKey == "isRequired") {
                            $("#formSetDiv #isRequired").prop("checked", optionValue == 'false' ? false : true)
                        } else if (optionKey == "isReadOnly") {
                            $("#formSetDiv #isReadOnly").prop("checked", optionValue == 'false' ? false : true)
                        } else if (optionKey == "inpWidth") {
                            $("#formSetDiv #inpWidth").val(optionValue)
                        } else if (optionKey == "optionsList") {
                            if (optionValue) {
                                var listOptions = optionValue.split(";"),
                                    html = "";
                                for (var j = 0; j < listOptions.length; j++) {
                                    var key = listOptions[j].split("-")[0],
                                        value = listOptions[j].split("-")[1];
                                    html += '<li><div>' +
                                        '<div><label>值</label><input type="text" class="optionKey" autocomplete="off" value="' + key + '"/></div>' +
                                        '<div><label>文本</label><input type="text" class="optionValue" autocomplete="off" value="' + value + '"/></div>' +
                                        '<span class="itemDel"><i class="fa fa-trash-o"></i></span>' +
                                        '</div></li>';
                                }
                                $("#formSetDiv .add-btn").parent().before(html);
                            }
                        }
                    }
                })
            }
            //属性列表初始化
            function initAttrList() {
                //初始化id
                var keyId = new Date().getTime()
                $("#formSetDiv [name='keyId']").val(keyId)
                $("#formSetDiv #wordName").attr("selkey", '')
                $("#formSetDiv #wordName").val("")
                $("#formSetDiv #wordType").val("")
                $("#formSetDiv #wordType").attr("selkey", '')
                $("#formSetDiv #wordTypeKey").val("")
                $("#formSetDiv #defaultVal").val("")
                $("#formSetDiv #inpWidth").val(200)
                $("#formSetDiv .add-btn").parent().siblings().remove();
            }

            function init(data) {
                var data = data || null;
                //编辑器初始化
                editor = KindEditor.create('#editContent', {
                    width: "100%",
                    height: $('#editorDiv').height() + 'px',
                    resizeType: 0,
                    allowFileManager: true,
                    afterFocus: function () {

                    },
                    afterBlur: function () {
                        this.sync();
                    }, //重新获取焦点
                    afterCreate: function () {
                        //获取点击内容
                        iframeClick()
                    }
                });
                //渲染数据，默认展示第一个字段的数据
                if (data.content || data.printTemplate) {
                    var editorHtml = ""
                    if (opt.data.type && opt.data.type == "setPrint") { //展示打印模板
                        $(".setPrint").show()
                        editorHtml = data.printTemplate ? data.printTemplate : data.content
                    } else {
                        $(".setPrint").hide()
                        editorHtml = data.content
                    }
                    editor.insertHtml(editorHtml)
                    $($("#formSetDiv [name='keyId']").val(new Date().getTime()))
                }
                if (data.contentJson) {
                    optionArray = JSON.parse(data.contentJson).wordConfigList
                }
                relatedTableInit(data)
                getDatabaseType(data)
            }

            //关联数据表渲染
            function relatedTableInit(data) {
                new $.selectPlug('#formSetDiv #relateTable', {
                    searchType: 'json',
                    url: '' + '/ts-form/dpTable/list',
                    textName: 'tableName',
                    valName: 'id',
                    inpTextName: 'tableName',
                    inpValName: 'id',
                    data: {
                        treeLevel: 1,
                        nodeType: 1
                    },
                    defaultVal: data ? data.tableName : '',
                    defaultCode: data ? data.tableId : '',
                    callback: function (res) {
                        if (res) {
                            $("#wordName").nextAll().remove();
                            $("#wordName").val("").attr("selkey", "")
                            getDatabaseField(res.id)
                        }
                    }
                })
                if (data.tableId) { //编辑模板时触发
                    getDatabaseField(data.tableId)
                }
            }
            //字段名称渲染
            function getDatabaseField(id) {
                $.ajax({
                    type: "post",
                    url: '' + '/ts-form/dpTable/findById/' + id,
                    dateType: "json",
                    contentType: 'application/json',
                    success: function (res) {
                        if (res.success) {
                            var fields = res.object.fields;
                            var arrayObj = [];
                            for (var i = 0; i < fields.length; i++) {
                                var objKey = fields[i].fieldName,
                                    objValue = fields[i].remark
                                arrayObj.push({
                                    objKey: objKey,
                                    objValue: objValue
                                });
                            }
                            $("#formSetDiv").find("#wordName").autoSelect({
                                data: arrayObj
                            })
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        layer.msg(res.message);
                    }
                });
            }
            //字段类型渲染
            function getDatabaseType(data) {
                $.ajax({
                    url: '' + 'ts-basics-bottom/dictItem/getDictItemByTypeCode',
                    type: "get",
                    data: {
                        typeCode: "FROM_EDIT_TYPE"
                    },
                    dateType: "json",
                    contentType: 'application/json',
                    success: function (res) {
                        if (res.success) {
                            var types = res.object;
                            var arrayObj = [];
                            for (var i = 0; i < types.length; i++) {
                                var objKey = types[i].itemCode,
                                    objValue = types[i].itemName
                                arrayObj.push({
                                    objKey: objKey,
                                    objValue: objValue
                                });
                            }
                            $("#formSetDiv").find("#wordType").autoSelect({
                                data: arrayObj
                            })
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        layer.msg(res.message);
                    }
                });
            }

            //list选项添加按钮
            $("#formSetDiv").off("click", ".add-btn").on("click", ".add-btn", function () {
                var html = '<li><div>' +
                    '<div><label>值</label><input type="text" class="optionKey" autocomplete="off"/></div>' +
                    '<div><label>文本</label><input type="text" class="optionValue" autocomplete="off"/></div>' +
                    '<span class="itemDel"><i class="fa fa-trash-o"></i></span>' +
                    '</div></li>';
                $(this).parent().before(html);
            })
            //list选项删除按钮
            $("#formSetDiv").delegate(".itemDel", "click", function () {
                $(this).parents("li").remove();
            })

            //删除按钮（删除编辑器中配置的字段）
            $("#formSetDiv").off("click", ".deleteConfigBtn").on("click", ".deleteConfigBtn", function () {

                var keyId = $("#formSetDiv [name='keyId']").val()
                var editorContent = $("#editContent").val()
                var objE = document.createElement("div");
                objE.innerHTML = editorContent;
                var delList = $(objE).find("div[id=" + keyId + "]")
                for (var i = 0; i < delList.length; i++) {
                    $(delList[i]).remove()
                }
                for (var i = 0; i < optionArray.length; i++) {
                    if (optionArray[i].keyId == keyId) {
                        optionArray.splice($.inArray(optionArray[i], optionArray), 1);
                        i = i - 1;
                    }
                }
                selDiv = null
                editor.html('');
                editor.insertHtml(objE.innerHTML)
                initAttrList()
            })

            //确认按钮（保存配置到标签属性）
            $("#formSetDiv").off("click", ".sureConfigBtn").on("click", ".sureConfigBtn", function () {
                var keyId = $("#formSetDiv [name='keyId']").val()
                var wordName = $("#wordName").val() || "",
                    wordNameKey = $("#wordName").attr("selkey") || "",
                    wordType = $("#wordType").val() || "",
                    wordTypeKey = $("#wordType").attr("selkey") || "",
                    placeholderContent = $("#placeholderContent").val() || "",
                    defaultVal = $("#defaultVal").val() || "",
                    mobileSort = $("#mobileSort").val() || "",
                    isRequired = $("#isRequired").prop('checked'),
                    isReadOnly = $("#isReadOnly").prop('checked'),
                    inpWidth = $("#formSetDiv #inpWidth").val() || 200,
                    optionsList = "";
                var listOptions = $(".configPanel").find(".listOptions").find("li"),
                    options = [];
                for (var i = 0; i < listOptions.length - 1; i++) {
                    var item = listOptions[i]
                    var key = $(item).find(".optionKey").val(),
                        value = $(item).find(".optionValue").val()
                    options.push(key + "-" + value)
                }
                optionsList = options.join(";");
                var isIn = -1
                for (var i = 0; i < optionArray.length; i++) {
                    if (optionArray[i].keyId == keyId) {
                        isIn = i
                    }
                }
                if (isIn != -1) {
                    optionArray[isIn] = {
                        "keyId": keyId,
                        "wordName": wordName,
                        "wordNameKey": wordNameKey,
                        "wordType": wordType,
                        "wordTypeKey": wordTypeKey,
                        "placeholderContent": placeholderContent,
                        "defaultVal": defaultVal,
                        "mobileSort": mobileSort,
                        "isRequired": isRequired,
                        "isReadOnly": isReadOnly,
                        "inpWidth": inpWidth,
                        "optionsList": optionsList
                    }
                    selDiv.removeClass().addClass("wfFormItem keyId:" + keyId + "%wordNameKey:" + wordNameKey +
                        "%wordName:" + wordName +
                        "%wordType:" + wordType +
                        "%wordTypeKey:" + wordTypeKey +
                        "%placeholderContent:" + placeholderContent +
                        "%defaultVal:" + defaultVal +
                        "%isRequired:" + isRequired +
                        "%optionsList:" + optionsList +
                        "%isReadOnly:" + isReadOnly +
                        "%inpWidth:" + inpWidth)

                } else {
                    optionArray.push({
                        "keyId": keyId,
                        "wordName": wordName,
                        "wordNameKey": wordNameKey,
                        "wordType": wordType,
                        "wordTypeKey": wordTypeKey,
                        "placeholderContent": placeholderContent,
                        "inpWidth": inpWidth,
                        "defaultVal": defaultVal,
                        "mobileSort": mobileSort,
                        "isRequired": isRequired,
                        "isReadOnly": isReadOnly,
                        "inpWidth": inpWidth,
                        "optionsList": optionsList
                    })

                    var text = "<div  class='wfFormItem keyId:" + keyId + "%wordNameKey:" + wordNameKey +
                        "%wordName:" + wordName +
                        "%wordType:" + wordType +
                        "%wordTypeKey:" + wordTypeKey +
                        "%placeholderContent:" + placeholderContent +
                        "%defaultVal:" + defaultVal +
                        "%isRequired:" + isRequired +
                        "%optionsList:" + optionsList +
                        "%isReadOnly:" + isReadOnly +
                        "%inpWidth:" + inpWidth +
                        "' id='" + keyId + "'>[" + wordName + "]</div>"
                    editor.insertHtml(text)
                }
                initAttrList()
            })
            $("#formSetDiv").off("click", "#formSetSave").on("click", "#formSetSave", function () {
                $('#formSetDiv').find('[lay-filter="formSetSave"]').trigger('click')
            })
            //保存
            form.on('submit(formSetSave)', function (data) {
                var content = "",
                    printContent = ""
                if (opt.data.type && opt.data.type == "setPrint") { //设置打印模板
                    content = editor.html();
                    printContent = data.field.editContent
                } else {
                    content = editor.html();
                    printContent = objectData.printTemplate ? objectData.printTemplate : data.field.editContent
                }
                contentJson = {
                    "formId": opt.data.id,
                    "wordConfigList": optionArray
                }

                var dataField = {
                    id: opt.data.id,
                    tableId: data.field.id || "",
                    content: content,
                    contentJson: JSON.stringify(contentJson),
                    printTemplate: printContent,
                }
                var _url = '/ts-form/dpFormTemplate/update';
                var _data = JSON.stringify(dataField);
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: _url,
                    data: _data,
                    success: function (res) {
                        if (res.success) {
                            opt.ref && opt.ref()
                            layer.closeAll();
                            layer.msg(res.message || '操作成功');
                        } else {
                            layer.msg(res.message || '操作失败！');
                        }
                    }
                });
                return false
            })

            //关闭
            $('#formSetDiv').off('click', '#close').on('click', '#close', function () {
                layer.closeAll()
            })
        })

        $.fn.autoSelect = function (options) {
            var defaults = {
                className: "emailist",
                data: [], //邮件数组
                zIndex: 11
            };
            // 最终参数
            var params = $.extend({}, defaults, options || {});

            // 是否现代浏览器
            var isModern = typeof window.screenX === "number",
                visibility = "visibility";
            // 键值与关键字
            var key = {
                "up": 38,
                "down": 40,
                "enter": 13,
                "esc": 27,
                "tab": 9
            };
            // 组装HTML的方法
            var fnEmailList = function (input) {
                var htmlEmailList = '',
                    arrValue = input.value.split("@"),
                    arrEmailNew = [];
                $.each(params.data, function (index, email) {
                    arrEmailNew.push(email);
                });
                $.each(arrEmailNew, function (index, email) {
                    htmlEmailList = htmlEmailList + '<li objKey="' + email.objKey + '"' + (input.indexSelected === index ? ' class="on"' : '') + '>' + email.objValue + '</li>';
                });
                return htmlEmailList;
            };
            // 显示还是隐藏
            var fnEmailVisible = function (ul, isIndexChange) {
                var value = $.trim(this.value),
                    htmlList = '';
                if (value === "" || (htmlList = fnEmailList(this)) === "") {
                    ul.css(visibility, "hidden");
                } else {
                    isIndexChange && (this.indexSelected = -1);
                    ul.css(visibility, "visible").html(htmlList);
                }
            };

            return $(this).each(function () {
                this.indexSelected = -1;
                // 列表容器创建
                var element = this;
                var eleUl = $('<ul></ul>').css({
                    position: "absolute",
                    minWidth: element.offsetWidth - 2,
                    left: $(element).position().left,
                    height: "200px",
                    overflowY: 'auto',
                    visibility: "hidden",
                    zIndex: params.zIndex
                }).addClass(params.className).bind("click", function (e) {
                    var target = e && e.target;
                    if (target && target.tagName.toLowerCase() === "li") {
                        $(element).attr("selKey", $(target).attr("objKey"));
                        $(element).val(target.innerHTML).trigger("change");
                        // $(element).val($(target).attr("objKey")).trigger("change");
                        $(this).css(visibility, "hidden");
                        element.focus(); // add on 2013-11-20
                    }
                });
                var eleDiv = $("<div></div>").css({
                    position: "relative"
                }).append(eleUl)
                $(this).after(eleDiv);

                // IE6的宽度
                if (!window.XMLHttpRequest) {
                    eleUl.width(element.offsetWidth - 2);
                }

                // 不同浏览器的不同事件
                isModern ? $(this).bind("input", function () {
                    fnEmailVisible.call(this, eleUl, true);
                }) : element.attachEvent("onpropertychange", function (e) {
                    if (e.propertyName !== "value") return;
                    fnEmailVisible.call(element, eleUl, true);
                });

                $(document).bind({
                    "click": function (e) {
                        var target = e && e.target,
                            htmlList = '';
                        if (target == element && (htmlList = fnEmailList(element, params.email))) {
                            eleUl.css(visibility, "visible").html(htmlList);
                        } else if (target != eleUl.get(0) && target.parentNode != eleUl.get(0)) {
                            eleUl.css(visibility, "hidden");
                        }
                    },
                    "keydown": function (e) {
                        var eleLi = eleUl.find("li");
                        if (eleUl.css(visibility) === "visible") {
                            switch (e.keyCode) {
                                case key.up: {
                                    element.indexSelected--;
                                    if (element.indexSelected < 0) {
                                        element.indexSelected = -1 + eleLi.length;
                                    }
                                    e.preventDefault && e.preventDefault();
                                    break;
                                }
                                case key.down: {
                                    element.indexSelected++;
                                    if (element.indexSelected >= eleLi.length) {
                                        element.indexSelected = 0;
                                    }
                                    e.preventDefault && e.preventDefault();
                                    break;
                                }
                                case key.enter: {
                                    e.preventDefault();
                                    eleLi.get(element.indexSelected) && $(element).val(eleLi.eq(element.indexSelected).html());
                                    eleUl.css("visibility", "hidden");
                                    break;
                                }
                                case key.tab:
                                case key.esc: {
                                    eleUl.css("visibility", "hidden");
                                    break;
                                }
                            }
                            if (element.indexSelected !== -1) {
                                eleUl.html(fnEmailList(element));
                            }
                        }
                    }
                });
            });
        };

    };
});