'use strict';
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(
      ['form', 'trasen', 'zTreeSearch', 'element', 'upload'],
      function () {
        var util = require('util');
        var form = layui.form,
          trasen = layui.trasen,
          zTreeSearch = layui.zTreeSearch,
          layUpload = layui.upload;
        var element = layui.element;
        var TestEditor;
        getDictList();

        function getDictList() {
          $.ajax({
            url: '/ts-basics-bottom/dictType/list',
            method: 'post',
            data: {
              pageSize: 1000,
              pageNo: 1,
              sidx: 'CREATE_DATE',
              sord: 'desc',
            },
            success: function (res) {
              var list = res.rows || [];
              var html = '';
              for (var i = 0; i < list.length; i++) {
                if (list[i].isDeleted == 'Y') {
                  continue;
                }
                html +=
                  '<option value="' +
                  list[i].typeCode +
                  '">' +
                  list[i].typeName +
                  '</option>';
              }
              $('#formDesign [name="dictSource"]').append(html);
            },
          });
        }
        var serialNumberRules = [];
        var editor = null;
        var iframeDocument;
        var selDiv;

        var wordConfigList = {};
        var chiocedItem = {};
        var dataSourceMap = [
          {
            value: 1,
            label: '手动录入',
          },
          {
            value: 2,
            label: '常用字段',
          },
          {
            value: 3,
            label: '公式计算',
          },
          {
            value: 4,
            label: '数据字典',
          },
          {
            value: 5,
            label: '外部来源',
          },
          {
            value: 6,
            label: '关联流程',
          },
          {
            value: 7,
            label: '接口服务',
          },
          {
            value: 8,
            label: '处理科室',
          },
          {
            value: 9,
            label: '业务类型',
          },
          {
            value: 10,
            label: '系统配置',
          },
          {
            value: 11,
            label: '子表单类型',
          },
          {
            value: 12,
            label: '子表单字段类型',
          },
        ];
        var fieldDataSourceMap = {
          input: [0, 1, 2],
          textarea: [0, 1, 2],
          number: [0, 2],
          date: [0, 1],
          radio: [0],
          checkbox: [0],
          select: [0, 3, 5, 6],
          file: [0],
          comment: [0],
          signature: [1],
          serialNumber: [0],
          personChose: [0],
          deptChose: [0],
          processChoose: [5],
          hrpHyperlink: [0],
          fileTemplate: [9],
          remark: [9],
          interworkCom: [4],
          interworkSick: [0],
          interworkSettle: [0],
          interworkPay: [0],
          interworkHosPro: [0],
          interworkTest: [0],
          workorderSetting: [7, 8],
          childForm: [10],
          childFormCount: [11],
          essentialDrugDic: [0],
          medicalSupplieDic: [0],
          leaveStatistics: [0],
          operationItem: [0]
        };
        //没有默认值的字段类型
        var noDefaultValueField = [
          'file',
          'comment',
          'signature',
          'serialNumber',
          'personChose',
          'deptChose',
          'processChoose',
          'hrpHyperlink',
          'fileTemplate',
          'remark',
          'interworkCom',
          'interworkSick',
          'interworkSettle',
          'interworkPay',
          'interworkHosPro',
          'interworkTest',
          'workorderSetting',
          'childForm',
          'childFormCount',
          'essentialDrugDic',
          'medicalSupplieDic',
          'leaveStatistics',
          'operationItem'
        ];
        var dataSourceMustField = {
          2: ['sourceField'],
          3: ['calculationRole'],
          4: ['dictSource'],
          5: ['interworkCom'],
          6: ['relationWorkflowName'],
          7: ['interfaceServices'],
          10: {
            fileTemplate: ['fileTemplate'],
            remark: ['remark'],
          },
        };

        // 关联流程数据
        let associationFormData = [];
        let associationMapList = [];
        if (opt.data) {
          for (var i = 0; i < opt.data.toaFieldSetList.length; i++) {
            wordConfigList[opt.data.toaFieldSetList[i].keyId] =
              opt.data.toaFieldSetList[i];
          }
        }
        var hisField = [];
        var wins = layer.open({
          type: 1,
          title: opt.title,
          closeBtn: 1,
          maxmin: false,
          shadeClose: false,
          area: ['100%', '100%'], //宽高
          content: html,
          success: function (layero, index) {
            TestEditor = CodeMirror.fromTextArea(
              document.getElementById('TestEditor'),
              {
                mode: 'application/xml',
                lineNumbers: true, // 是否显示行号
                matchBrackets: true, // 是否添加匹配括号高亮
              }
            );
            
            if (opt.data) {
              trasen.setNamesVal($('#formDesignForm'), opt.data);
              TestEditor.setValue(opt.data.javascriptText || '');

              getHisField();
              $('#formDesign #syncPrint').removeClass('none');
            } else {
              if (opt.selType) {
                $('#formDesign   [name="classifyId"]').val(opt.selType.id);
                $('#formDesign   [name="classifyName"]').val(opt.selType.name);
              }
            }
            form.render('select');
            form.render('radio');
            init();
          },
          cancel: function (index) {
            layer.confirm(
              '当前内容未保存，确定关闭窗口？',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0,
              },
              function (index) {
                //do something
                tinymce.remove('#editContent');
                layer.close(wins);
                layer.close(index);
              }
            );
            return false;
          },
        });

        //历史字段
        function getHisField() {
          $.ajax({
            url:
              '/ts-form/dpFormTemplate/findTableFieldByTemplateId/' +
              opt.data.id,
            method: 'post',
            success: function (res) {
              if (res.success) {
                hisField = res.object || [];
                setHisField();
              }
            },
          });
        }

        function setHisField() {
          var html = '';
          for (var i = 0; i < hisField.length; i++) {
            html += '<li index="' + i + '">' + hisField[i].showName + '</li>';
          }
          $('#formDesign .hisFields').html(html);
        }

        function setAssociationOptions(el, data) {
          let defaultOpt = document.createElement('option');
          defaultOpt.value = '';
          defaultOpt.innerText = '请选择';
          $(el).html(defaultOpt);
          for (let i = 0; i < data.length; i++) {
            let item = data[i];
            let keyId = item.key || item.keyId;
            if (chiocedItem.keyId !== keyId) {
              let html = document.createElement('option');
              html.value = keyId;
              $(html).attr('fieldName', item.fieldName);
              html.innerText = item.value || item.showName;
              el.append(html);
            }
          }
          return false;
        }

        function setAssociationFieldOption($parentEl, option) {
          var defOpt = {
            key: '',
            value: '',
          };
          var options = $.extend(defOpt, option);
          let idx = 0;
          const parentList = $parentEl.find('.layui-form-item');
          if (parentList.length > 0) {
            idx =
              parseInt($(parentList[parentList.length - 1]).attr('row-id')) + 1;
          }
          // 创建节点
          let association = document.createElement('div');
          let keySelect = document.createElement('select');
          let valueSelect = document.createElement('select');
          let label = document.createElement('div');
          let delImg = document.createElement('img');
          // 节点添加属性
          association.className = 'layui-form-item';
          $(association).attr('row-id', idx);
          // key
          keySelect.className = 'flex-grow-1';
          $(keySelect).attr('name', `filterKey-${idx}`);
          $(keySelect).attr('lay-filter', 'filterKey');
          $(keySelect).attr('lay-verify', 'required');
          // value
          valueSelect.className = 'flex-grow-1';
          $(valueSelect).attr('name', `filterValue-${idx}`);
          $(valueSelect).attr('lay-filter', 'filterValue');
          $(valueSelect).attr('lay-verify', 'required');
          label.className = 'label';
          label.innerText = '取值';
          delImg.src = '../../../static/img/other/topic-del.png';
          delImg.className = 'del-btn-icon';
          // 下拉框添加对应选项
          setAssociationOptions(keySelect, associationMapList);
          setAssociationOptions(valueSelect, associationFormData);
          // 组装
          association.append(delImg);
          association.append(keySelect);
          association.append(label);
          association.append(valueSelect);
          $parentEl.append(association);
          // 初始值
          let formData = {};
          formData[`filterKey-${idx}`] = options.key;
          formData[`filterValue-${idx}`] = options.value;
          form.val('form-design-editor', formData);
          form.render('select');
          return false;
        }

        function initDefaultSelectValue(row) {
          let data = {};
          data = $.extend(data, row);
          new $.selectPlug('#association_form_name', {
            url: '/ts-workflow/workflow/definition/list',
            datatype: 'get',
            searchType: 'json',
            textName: 'workflowName',
            valName: 'wfDefinitionId',
            searchPlaceholder: '请输入流程名称进行检索',
            inpValName: 'relationWorkflowId',
            inpTextName: 'relationWorkflowName',
            // choice: true, // 是否多选
            required: true,
            defaultVal: data.relationWorkflowId || '',
            defaultText: data.relationWorkflowName || '',
            condition: 'condition',
            callback: function (res) {
              let relationWorkflowId;
              if (res == false) {
                relationWorkflowId = data.relationWorkflowId;
              } else {
                relationWorkflowId = res.wfDefinitionId;
              }
              initRelevanceField(data, relationWorkflowId, res);
            },
          });
        }

        async function initRelevanceField(data, relationWorkflowId, result) {
          // 根据流程Id获取关联字段
          let res;
          if (relationWorkflowId) {
            res = await getFormTemplate(relationWorkflowId);
          }
          if (res && res.object) {
            associationFormData = res.object.toaFieldSetList || [];
            let associationMap;
            // 前端存在关联字段取前端的
            if (data.associationMap) {
              associationMap = data.associationMap;
            } else {
              const obj = findByFieldId(data.keyId);
              associationMap = obj || {};
            }
            var parentEl = $(
              '#formDesign .association-form .association-form-field-list'
            );
            $(parentEl).empty();
            if (result === false) {
              for (const key in associationMap) {
                (function (i, n) {
                  setAssociationFieldOption(i, n);
                })(parentEl, {
                  key: associationMap[key].key,
                  value: associationMap[key].value,
                });
              }
            }
          }
        }

        // 根据字段iD查询字段关联设置
        function findByFieldId(fieldId) {
          let def = {};
          $.ajax({
            url: `/ts-form/dpFieldRelation/findByFieldId/${fieldId}`,
            async: false,
            method: 'post',
            success: function (res) {
              def =
                res.object && res.object.relation
                  ? res.object.relation
                  : JSON.stringify({});
            },
          });
          return JSON.parse(def);
        }

        function getFormTemplate(workflowId) {
          var def = $.Deferred();
          $.ajax({
            url: `/ts-form/dpFormTemplate/findByWorkflowId/${workflowId}`,
            method: 'post',
            success: function (res) {
              def.resolve(res);
            },
            error: function (res) {
              def.reject(res);
            },
          });
          return def.promise();
        }

        element.on('tab(formDesignTab)', function (data) {
          if (data.index == 2) {
            $('#formDesign #formItemAttr .archivesTabBtn').addClass('none');
          } else {
            $('#formDesign #formItemAttr .archivesTabBtn').removeClass('none');
          }
        });

        $('#formDesign .hisFields')
          .off('click', 'li')
          .on('click', 'li', function () {
            var index = $(this).attr('index');
            var json = hisField[index];
            if (wordConfigList[json.keyId]) {
              layer.msg('请不要重复设置历史表单字段');
              return false;
            }
            var text =
              "<span class='wfFormItem' id='" +
              json.keyId +
              "' style='user-select: none;' contenteditable='false'>[" +
              json.showName +
              ']</span>';
            if ($(tymIframeFocus.id).parents('.wfFormItem').length) {
              $($(tymIframeFocus.id).parents('td')[0]).append(text);
            } else {
              tinymce.execCommand('mceInsertContent', false, text);
            }
            wordConfigList[json.keyId] = json;
          });
        //
        zTreeSearch.init('#formDesign [name="classifyName"]', {
          url: common.url + '/ts-workflow/form/classify/getClassifyTree',
          type: 'post',
          checkbox: false,
          condition: 'name',
          data: {
            classifyType: 1,
          },
          zTreeOnClick: function (treeId, treeNode) {
            $('#formDesign   [name="classifyId"]').val(treeNode.id);
            /*$("#empDeptName").val(treeNode.name);*/
          },
          callback: function () {
            $('#formDesign  [name="classifyId"]').val('');
          },
        });

        // 数值 千分位、转大写
        $('#formDesign [name="isThousandth"]')
          .off('change')
          .on('change', function () {
            var checked = $(this).prop('checked');
            if (checked) {
              $('#formDesign [name="isMakeBigger"]').prop('checked', false);
            }
          });
        $('#formDesign [name="isMakeBigger"]')
          .off('change')
          .on('change', function () {
            var checked = $(this).prop('checked');
            if (checked) {
              $('[name="isThousandth"]').prop('checked', false);
            }
          });

        // 监听是否隐藏审批意见附件上传按钮，隐藏则取消附件上传必传选中
        // form.on('checkbox(checkHideCommentFile)', function(data){
        $('#formDesign [name="isHideCommentFile"]')
          .off('change')
          .on('change', function () {
            var checked = $(this).prop('checked');
            // var checked = data.elem.checked;
            hideCommentFileFieldChange(checked);
          });

        function hideCommentFileFieldChange(checked) {
          if (checked) {
            $('#formDesign [name="isMustCommentFile"]')
              .prop('checked', false)
              .closest('.layui-form-item')
              .addClass('none');
          } else {
            $('#formDesign [name="isMustCommentFile"]')
              .closest('.layui-form-item')
              .removeClass('none');
          }
        }

        //字段类型切换
        form.on('select(formItemFieldType)', function (data) {
          var val = data.value;
          formItemFieldTypeChange(val);

          if (val == 'comment') {
            $('#formDesign [name="isHideCommentFile"]').prop('checked', 'checked');
            $('#formDesign [name="isHideCommentFile"]').trigger('change')
          } else {
            $('#formDesign [name="isHideCommentFile"]').removeProp('checked');
            $('#formDesign [name="isHideCommentFile"]').trigger('change')
          }
        });

        function formItemFieldTypeChange(val) {
          setDataSource(val);
          setDataSourceMustField();
          defaultValueCon(val);
          form.val('form-design-editor', {
            relationWorkflowName: '',
            relationWorkflowId: '',
          });
          if (val == 'number' || val == 'date' || val == 'serialNumber') {
            $('#formDesign #dataFormat')
              .removeClass('none')
              .find('.layui-form-item')
              .addClass('none');
            $('#formDesign #dataFormat')
              .find('.' + val + 'Format')
              .removeClass('none');

            if (val == 'date') {
              let optionList = Object.keys(wordConfigList)
                  .filter((keyId) => keyId != chiocedItem.keyId)
                  .map((key) => {
                    let config = wordConfigList[key];
                    return `<option value="${config.fieldName}">${config.showName}</option>`;
                  }),
                selectDom = $(
                  '#formDesign #dataFormat .dateFormat .dateRelatedSelect'
                );

              optionList.unshift('<option value="">请选择</option>');
              selectDom.empty();
              selectDom.append(optionList.join(''));
              form.render('select');
            }
          } else {
            $('#formDesign #dataFormat')
              .addClass('none')
              .find('.layui-form-item')
              .addClass('none');
          }
          if (val == 'select' || val == 'radio' || val == 'checkbox') {
            $('#formDesign [name="optionValue"]')
              .closest('.layui-form-item')
              .removeClass('none');
          } else {
            $('#formDesign [name="optionValue"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          if (val == 'fileTemplate' || val == 'remark') {
            $('#formDesign #formProp')
              .addClass('none')
              .find('.layui-form-item')
              .addClass('none');
            if (val == 'fileTemplate') {
              $('#formDesign [name="fileTemplate"]')
                .closest('.layui-form-item')
                .removeClass('none');
            } else {
              $('#formDesign [name="fileTemplate"]')
                .closest('.layui-form-item')
                .addClass('none');
            }
            if (val == 'remark') {
              $('#formDesign [name="remark"]')
                .closest('.layui-form-item')
                .removeClass('none');
            } else {
              $('#formDesign [name="remark"]')
                .closest('.layui-form-item')
                .addClass('none');
            }
          } else {
            $('#formDesign #formProp')
              .removeClass('none')
              .find('.layui-form-item')
              .removeClass('none');
          }
          if (val == 'remark' || val == 'comment' || val == 'radio' || val == 'checkbox') {
            $('#formDesign #dataStyle')
              .removeClass('none')
              .find('.layui-form-item')
              .addClass('none');
            $('#formDesign #dataStyle')
              .find('.' + val + 'Style')
              .removeClass('none');
          } else {
            $('#formDesign #dataStyle')
              .addClass('none')
              .find('.layui-form-item')
              .addClass('none');
          }

          if (val == 'interworkCom') {
            $('#formDesign [name="interworkFace"]')
              .closest('.layui-form-item')
              .removeClass('none');
          } else {
            $('#formDesign [name="interworkFace"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          /**@desc 子表单取值设置 */
          if (val == 'childForm') {
            $('#formDesign #childFormSelectDom')
              .closest('.layui-form-item')
              .removeClass('none');
            initChildFormSelect();
          } else {
            $('#formDesign #childFormSelectDom')
              .closest('.layui-form-item')
              .addClass('none');
            $('#formDesign #childFormSelectDom input').attr('lay-verify', '');
          }
          /**@desc 子表单统计取值设置 */
          if (val == 'childFormCount') {
            $('#formDesign #childFormSelectDomFiled')
              .closest('.layui-form-item')
              .removeClass('none');
            initChildFormSelectField();
            $('#formDesign #childFormCountSelectDom')
              .closest('.layui-form-item')
              .removeClass('none');
            initChildFormCountSelect()
          } else {
            $('#formDesign #childFormSelectDomFiled')
              .closest('.layui-form-item')
              .addClass('none');
            $('#formDesign #childFormSelectDomFiled input').attr('lay-verify', '');
            $('#formDesign #childFormCountSelectDom')
              .closest('.layui-form-item')
              .addClass('none');
            $('#formDesign #childFormCountSelectDom input').attr('lay-verify', '');
          }
          if (val == 'processChoose') {
            associationMapList = [];
            let ids = [];
            let idEls = $(iframeDocument.document).find('.wfFormItem');
            $.each(idEls, function () {
              let k = $(this).attr('id');
              ids.push(k);
            });
            for (let key of ids) {
              let item = wordConfigList[key];
              associationMapList.push({
                key,
                value: item.showName,
                fieldName: item.fieldName,
              });
            }
            initDefaultSelectValue();
            $('#formDesign [name="relationWorkflowName"]')
              .closest('.association-form')
              .removeClass('none');
            $('#formDesign .association-form .association-form-field').addClass(
              'none'
            );
          }
          if (val == 'signature') {
            $('#formDesign [name="sourceField"]')
              .closest('.layui-form-item')
              .removeClass('none');
            var html = $('#signatureOption').html();
            $('#formDesign [name="sourceField"]').html(html);
          } else {
            var html = $('#inputOption').html();
            $('#formDesign [name="sourceField"]').html(html);
          }
          form.render('select');
        }
        // 设置数据来源
        function setDataSource(val) {
          var html = '';
          for (var i = 0; i < fieldDataSourceMap[val].length; i++) {
            var item = dataSourceMap[fieldDataSourceMap[val][i]];
            html +=
              '<option value="' + item.value + '">' + item.label + '</option>';
          }
          $('#formDesign [name="dataSource"]').html(html);
          //自动计算规则
          $('#formDesign [name="calculationRole"]')
            .closest('.layui-form-item')
            .addClass('none');
          //常用字段来源
          $('#formDesign [name="sourceField"]')
            .closest('.layui-form-item')
            .addClass('none');
          //数据字典
          $('#formDesign [name="dictSource"]')
            .closest('.layui-form-item')
            .addClass('none');
          //接口服务
          $('#formDesign [name="interfaceServices"]')
            .closest('.layui-form-item')
            .addClass('none');
          //附件模板
          $('#formDesign [name="fileTemplate"]')
            .closest('.layui-form-item')
            .addClass('none');
          //备注
          $('#formDesign [name="remark"]')
            .closest('.layui-form-item')
            .addClass('none');
          //关联字段
          $('#formDesign [name="relationWorkflowName"]')
            .closest('.association-form')
            .addClass('none');
          form.render('select');
        }
        //默认值显示
        function defaultValueCon(val) {
          if (noDefaultValueField.indexOf(val) != -1) {
            $('#formDesign [name="defaultValue"]')
              .closest('.layui-form-item')
              .addClass('none');
          } else {
            $('#formDesign [name="defaultValue"]')
              .closest('.layui-form-item')
              .removeClass('none');
          }
        }
        //数据来源相关字段必填设置
        function setDataSourceMustField() {
          var val = $('#formDesign [name="dataSource"]').val();
          Object.keys(dataSourceMustField).forEach((key) => {
            var mustField = dataSourceMustField[key];
            if (Array.isArray(mustField)) {
              changeFieldMustStatus(mustField, key == val);
            } else {
              var fieldType = $('#formDesign [name="fieldType"]').val();
              Object.keys(mustField).forEach((k) => {
                var mustChildField = mustField[k];
                changeFieldMustStatus(mustChildField, k == fieldType);
              });
            }
          });
        }
        function changeFieldMustStatus(fieldList, status) {
          if (status) {
            for (var i = 0; i < fieldList.length; i++) {
              $('#formDesign [name="' + fieldList[i] + '"]').attr(
                'lay-verify',
                'required'
              );
            }
          } else {
            for (var i = 0; i < fieldList.length; i++) {
              $('#formDesign [name="' + fieldList[i] + '"]').removeAttr(
                'lay-verify'
              );
            }
          }
        }

        //数据来源切换
        form.on('select(formItemDataSource)', function (data) {
          var val = data.value;
          formItemDataSourceChange(val);
          setDataSourceMustField();
        });

        function formItemDataSourceChange(val, json) {
          if (val == 1) {
            defaultValueCon(
              (json && json.fieldType) || $('[name="fieldType"]').val()
            );
          } else {
            $('#formDesign [name="defaultValue"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          if (val == 2) {
            $('#formDesign [name="sourceField"]')
              .closest('.layui-form-item')
              .removeClass('none');
          } else {
            $('#formDesign [name="sourceField"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          if (val == 3) {
            $('#formDesign [name="calculationRole"]')
              .closest('.layui-form-item')
              .removeClass('none');
          } else {
            $('#formDesign [name="calculationRole"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          if (val == 4) {
            $('#formDesign [name="dictSource"]')
              .closest('.layui-form-item')
              .removeClass('none');
            $('#formDesign [name="optionValue"]')
              .closest('.layui-form-item')
              .addClass('none');
          } else {
            $('#formDesign [name="dictSource"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          if (val == 7) {
            $('#formDesign [name="interfaceServices"]')
              .closest('.layui-form-item')
              .removeClass('none');
          } else {
            $('#formDesign [name="interfaceServices"]')
              .closest('.layui-form-item')
              .addClass('none');
          }
          // 关联流程
          $(
            '#formDesign .association-form .association-form-field-list'
          ).empty();
          if (val == 6) {
            // 设置下拉列表
            associationMapList = [];
            let ids = [];
            let idEls = $(iframeDocument.document).find('.wfFormItem');
            $.each(idEls, function () {
              let k = $(this).attr('id');
              ids.push(k);
            });
            for (let key of ids) {
              let item = wordConfigList[key];
              associationMapList.push({
                key,
                value: item.showName,
                fieldName: item.fieldName,
              });
            }
            initDefaultSelectValue(json);
            // 初始化
            $('#formDesign [name="relationWorkflowName"]')
              .closest('.association-form')
              .removeClass('none');
            if (
              (json && json.fieldType == 'processChoose') ||
              $('[name="fieldType"]').val() == 'processChoose'
            ) {
              $(
                '#formDesign .association-form .association-form-field'
              ).addClass('none');
            } else {
              $(
                '#formDesign .association-form .association-form-field'
              ).removeClass('none');
            }
          } else {
            form.val('form-design-editor', {
              relationWorkflowName: '',
              relationWorkflowId: '',
            });
            $('#formDesign [name="relationWorkflowName"]')
              .closest('.association-form')
              .addClass('none');
            $('#formDesign .association-form .association-form-field').addClass(
              'none'
            );
          }
          /**@desc 子表单选择 */
          if (val == 11) {
            initChildFormSelect();
          }
          /**@desc 子表单字段选择 */
          if (val == 12) {
            initChildFormSelectField();
          }
          if (
            ((json && json.fieldType) || $('[name="fieldType"]').val()) ==
            'select'
          ) {
            if (val != 1) {
              $('#formDesign [name="optionValue"]')
                .closest('.layui-form-item')
                .addClass('none');
              $('#formDesign [name="defaultValue"]')
                .closest('.layui-form-item')
                .addClass('none');
            } else {
              $('#formDesign [name="optionValue"]')
                .closest('.layui-form-item')
                .removeClass('none');
            }
          }
        }

        // tips
        $('#formDesign .optionListInfo').layerTips({
          tips: [1, '#fff'],
        });
        //新增关联字段
        $('#formDesign .association-add-btn')
          .off('click', 'a')
          .on('click', 'a', function () {
            setAssociationFieldOption(
              $('#formDesign .association-form .association-form-field-list')
            );
            return false;
          });
        // 删除关联字段
        $('#formDesign .association-form .association-form-field-list')
          .off('click', 'img.del-btn-icon')
          .on('click', 'img.del-btn-icon', function () {
            $(this).closest('.layui-form-item').remove();
          });

        function init() {
          if (opt.data) {
            $('#editContent').val(opt.data.content);
          }
          //编辑器初始化
          editor = tinymce.init({
            selector: '#editContent',
            language: 'zh_CN',
            plugins: ['quickbars', 'link', 'table', 'code', 'paste'],
            menubar: 'edit insert view format table',
            menu: {
              edit: {
                title: 'Edit',
                items:
                  ' code | undo redo | cut copy paste pastetext | selectall',
              },
            },
            branding: false,
            elementpath: false,
            statusbar: false,
            body_class: 'editor_body',
            content_style:
              '* {margin:0}  .editor_body { margin: 0 !important;padding-top: 40px; } td.designSel{background-color: rgb(237, 239, 255) !important}',
            toolbar: [
              'undo redo | styleselect | bold italic | link image | table | fontselect fontsizeselect  | alignleft aligncenter alignright | code',
            ],
            table_clone_elements: 'p',
            table_grid: false,
            // paste_enable_default_filters: false,
            paste_retain_style_properties: 'text-align margin align',
            paste_word_valid_elements:
              'table[width|border],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody,h1,h2,h3,h4,h5,img,div[align]',
            fontsize_formats:
              '42pt 36pt 26pt 24pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 9pt 7.5pt 6.5pt 5.5pt 5pt',
            font_formats:
              "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
            width: '100%',
            height: '100%',
            auto_focus: true,
            setup: function (editor) {
              editor.on('init', function (ed) {
                ed.target.editorCommands.execCommand('fontName', false, '宋体');
              });
            },
          });
          editor.then(function () {
            //获取 tinyMce 的 编辑区域的 iframe DOM对象
            iframeDocument = $('#editContent')
              .next()
              .find('iframe')[0].contentWindow;
            tymIframeListener();
          });
        }
        function initChildFormCountSelect() {
          let childFormTableId = $('#formDesign #childFormSelectDomFiled input[name=tableId]').val() || '';
          /**@desc 渲染子表单数据 */
          let options = {
            url: `/ts-form/dpTable/getChildHeadList?tableId=${childFormTableId}&isNumber=Y`,
            datatype: 'get', // 请求方式
            searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
            textName: 'remark', // 选项值的文本的key（接口返回数据里面的参数）
            valName: 'fieldName', // 选项值的id的key（接口返回数据里面的参数）
            inpTextName: 'remark', //  需要提交的已选值文本的参数名 必填
            inpValName: 'sumField', // 需要提交的已选值id参数名  必填
            condition: 'condition',
            required: 'required',
            choice: false,
            callback: function (res = {}) {},
          };
          new $.selectPlug('#formDesign #childFormCountSelectDom', options);
        }
        function initChildFormSelectField() {
          /**@desc 渲染子表单数据 */
          let options = {
            url: '/ts-form/dpTable/list',
            datatype: 'POST', // 请求方式
            searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
            textName: 'tableComment', // 选项值的文本的key（接口返回数据里面的参数）
            valName: 'id', // 选项值的id的key（接口返回数据里面的参数）
            inpTextName: 'tableName', //  需要提交的已选值文本的参数名 必填
            inpValName: 'tableId', // 需要提交的已选值id参数名  必填
            condition: 'condition',
            required: 'required',
            choice: false,
            data: {
              pageSize: 30,
            },
            callback: function (res = {}) {
              initChildFormCountSelect();
            },
          };
          new $.selectPlug('#formDesign #childFormSelectDomFiled', options);
        }
        function initChildFormSelect() {
          /**@desc 渲染子表单数据 */
          let options = {
            url: '/ts-form/dpTable/list',
            datatype: 'POST', // 请求方式
            searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
            textName: 'tableComment', // 选项值的文本的key（接口返回数据里面的参数）
            valName: 'id', // 选项值的id的key（接口返回数据里面的参数）
            inpTextName: 'tableName', //  需要提交的已选值文本的参数名 必填
            inpValName: 'tableId', // 需要提交的已选值id参数名  必填
            condition: 'condition',
            required: 'required',
            choice: false,
            data: {
              pageSize: 30,
            },
            callback: function (res = {}) {},
          };
          new $.selectPlug('#formDesign #childFormSelectDom', options);
        }

        var tymIframeFocus = null;
        // tinyMce 的 编辑区域的 iframe  事件监听
        function tymIframeListener() {
          //去除选中背景
          $(iframeDocument.document)
            .off('click')
            .on('click', function () {
              $(iframeDocument.document).find('td').removeClass('designSel');
              // $('#formItemAttr').addClass('none');;
              initAttrList();
            });
          $(iframeDocument.document)
            .off('click', 'td')
            .on('click', 'td', function (e) {
              e.stopPropagation();
              e.preventDefault();
              $(iframeDocument.document).find('td').removeClass('designSel');
              $(this).addClass('designSel');
              tymIframeFocus = tinyMCE
                .get('editContent')
                .selection.getBookmark();
              var div = $(this).children('div.wfFormItem');
              if (div.length == 0) {
                initAttrList();
                return false;
              }
              var keyId = '';
              for (var i = 0; i < div.length; i++) {
                keyId = $(div[i]).attr('id');
                if (wordConfigList[keyId]) {
                  chiocedItem = wordConfigList[keyId];
                  selDiv = $(div[i]);
                  break;
                }
              }
              echoDom(chiocedItem);
            });
          $(iframeDocument.document)
            .off('click', 'td .wfFormItem')
            .on('click', 'td .wfFormItem', function (e) {
              e.stopPropagation();
              e.preventDefault();
              chiocedItem = {};
              $(iframeDocument.document).find('td').removeClass('designSel');
              $(this).addClass('designSel');
              tymIframeFocus = tinyMCE
                .get('editContent')
                .selection.getBookmark();
              var div = $(this);
              if (div.length == 0) {
                initAttrList();
                return false;
              }
              var keyId = '';
              for (var i = 0; i < div.length; i++) {
                keyId = $(div[i]).attr('id');
                if (wordConfigList[keyId]) {
                  chiocedItem = wordConfigList[keyId];
                  selDiv = $(div[i]);
                  break;
                }
              }
              echoDom(chiocedItem);
              return false;
            });
        }

        //文件上传
        //删除
        $('#formDesign').on('click', 'span.fileDel', function () {
          var fileId = $($(this).parents('li')[0]).attr('file-id');
          var list = fileupload.file.fileList;
          var index = -1;
          for (var i = 0; i < list.length; i++) {
            if (list[i].id == fileId) {
              index = i;
            }
          }
          if (index != -1) {
            fileupload.file.fileList.splice(index, 1);
          }
          fileupload.setFileData();
          fileupload.setFileStr();
        });
        //文件预览
        $('#formDesign').on('click', '.viewerDoc2', function (e) {
          e.stopPropagation();
          var id = $(this).attr('fileid');
          var filename = $(this).attr('filename');
          common.viewerDoc2(id, filename);
          return false;
        });
        //图片预览
        $('#formDesign').on('click', '.viewerImg', function (e) {
          e.stopPropagation();
          var item = {
            fileUrl: $(this).attr('fileurl'),
            fileName: $(this).attr('filename'),
          };
          common.viewerImg([item], $(this).attr('fileurl'));
          return false;
        });

        var fileupload = {
          file: {},
          initDom: function (dom, module) {
            this.file = {
              fileListDom: dom.siblings('.layui-upload').find('.fileList'),
              fileList: [],
              fileStr: '',
              number: dom.attr('file-number') || 999,
              inp: dom.siblings('input.fileId'),
            };
            var that = this;
            var uploadListIns = layUpload.render({
              elem: dom,
              url:
                '/ts-document/attachment/fileUpload?module=' +
                (module || 'form'),
              accept: 'file',
              exts: dom.attr('file-exts') || '',
              multiple: !dom.attr('file-number') || dom.attr('file-number') > 1,
              auto: true,
              choose: function (obj) {
                this.files = null;
                this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
              },
              before: function (obj) {
                if (
                  Object.keys(this.files).length + that.file.fileList.length >
                  that.file.number
                ) {
                  uploadListIns.config.elem.next()[0].value = '';
                  layer.msg('文件上传数量超出限制');
                  // layer.stopPropagation()
                }
              },
              done: function (res, index, upload) {
                uploadListIns.config.elem.next()[0].value = '';
                if (res.success == true) {
                  //上传成功
                  res.object[0].index = index;
                  if (that.file.fileList.length < that.file.number) {
                    that.file.fileList.push({
                      id: res.object[0].fileId,
                      originalName: res.object[0].fileName,
                      fileName: res.object[0].fileRealName,
                      filePath: res.object[0].filePath,
                      fileSize: res.object[0].fileSize,
                    });
                    that.setFileStr();
                    that.setFileData();
                  }
                  return delete this.files[index]; //删除文件队列已经上传成功的文件
                } else {
                  layer.msg(res.message || '上传失败');
                }
                this.error(index, upload);
              },
              error: function (index, upload) {},
            });
          },
          setFileStr: function () {
            var list = this.file.fileList;
            var fileStrArr = [];
            for (var i = 0; i < list.length; i++) {
              fileStrArr.push(list[i].id);
            }
            this.file.fileStr = fileStrArr.join(',');
            this.file.inp.val(fileStrArr.join(','));
          },
          setFileData: function () {
            var list = this.file.fileList;
            var html = '';
            for (var i = 0; i < list.length; i++) {
              html += '<li class="fileItem" file-id="' + list[i].id + '">';
              html +=
                '<a class="fileDown" href="/ts-document/attachment/downloadFile/' +
                list[i].id +
                '">' +
                list[i].originalName +
                '</a>';
              if (common.isImg(list[i].originalName)) {
                html +=
                  '<span class="viewerImg" fileurl="' +
                  list[i].id +
                  '" filename="' +
                  list[i].fileName +
                  '">预览</span>';
              }
              if (common.isDoc(list[i].originalName)) {
                html +=
                  '<span class="viewerDoc2" filename="' +
                  list[i].fileName +
                  '" fileid="' +
                  list[i].id +
                  '">预览</span>';
              }
              html += '<span class="fileDel">删除</span>';
              html += '</li>';
            }
            this.file.fileListDom.html(html);
          },
          initFile: function (fileIdStr) {
            var that = this;
            that.file.fileList = [];
            that.file.fileStr = '';
            that.file.inp.val('');
            that.file.fileListDom.empty();
            if (fileIdStr) {
              $.ajax({
                type: 'get',
                url: '/ts-document/attachment/selectByIds',
                data: {
                  idsStr: fileIdStr,
                },
                success: function (res) {
                  if (res.success) {
                    that.file.fileList = res.object;
                    that.setFileStr();
                    that.setFileData();
                  } else {
                    layer.msg(res.message || '上传失败');
                  }
                },
              });
            }
          },
        };
        fileupload.initDom($('#formDesign .fileUploadBtn'), 'formDesign');

        //流水号规则
        function setSerialNumberRules(arr) {
          var date = {
            yyyyMMdd: '年月日',
            yyyyMM: '年月',
            yyyy: '年',
          };
          var text = '';
          if (!arr || arr.length == 0) {
            text = '设置';
          } else {
            for (var i = 0; i < arr.length; i++) {
              if (arr[i].ruleType == 1) {
                text += '[' + arr[i].serialVal + ']';
              } else if (arr[i].ruleType == 2) {
                text += '[' + date[arr[i].serialVal] + ']';
              } else if (arr[i].ruleType == 3) {
                text += '[' + arr[i].serialVal + '位流水]';
              }
            }
          }
          $('#serialNumberRules').text(text);
        }
        //初始化属性
        function initAttrList() {
          chiocedItem = {};
          serialNumberRules = [];
          var defOpt = {
            fieldType: 'input',
            showName: '',
            promptText: '',
            isMust: '',
            isReadonly: '',
            length: 100,
            decimalDigit: '',
            treatmentMethod: '',
            isThousandth: '',
            isMakeBigger: '',
            max: '',
            min: '',
            dataFormat: 'yyyy-MM-dd',
            dataSource: 1,
            sourceField: '',
            defaultValue: '',
            optionValue: '',
            calculationRole: '',
            fileTemplate: '',
            remark: '',
            isRed: '',
            isBold: '',
            wrap: '',
            isHideCommentFile: '',
            isMustCommentFile: '',
            hideApprovalTime: '',
            approvalTimeFormat: 'yyyy-MM-dd HH:mm:ss',
            serialNumberRule: '',
            serialNumberRoleList: [],
            associationMap: [],
            relationWorkflowId: '',
            relationWorkflowName: '',
            tableName: '',
            sumField: '',
            tableId: '',
            interfaceServices: '',
          };
          $('#formItemAttr [name="fieldType"] option').prop('disabled', false);
          $('#dataFormat')
            .addClass('none')
            .find('.layui-form-item')
            .addClass('none');
          $('#dataStyle')
            .addClass('none')
            .find('.layui-form-item')
            .addClass('none');
          $('[name="sourceField"]')
            .closest('.layui-form-item')
            .addClass('none');
          $('[name="calculationRole"]')
            .closest('.layui-form-item')
            .addClass('none');
          $('[name="dictSource"]').closest('.layui-form-item').addClass('none');
          $('[name="optionValue"]')
            .closest('.layui-form-item')
            .addClass('none');
          $('[name="relationWorkflowName"]')
            .closest('.association-form')
            .addClass('none');
          $('#formItemAttr [name="fieldType"]').prop('disabled', false);
          $('#formItemAttr [name="dataSource"]').prop('disabled', false);
          $('#formItemAttr [name="dictSource"]').prop('disabled', false);
          $('#formItemAttr [name="sourceField"]').prop('disabled', false);
          $('#formItemAttr [name="relationWorkflowName"]').prop(
            'disabled',
            false
          );
          setSerialNumberRules();
          formItemFieldTypeChange(defOpt.fieldType);
          formItemDataSourceChange(defOpt.dataSource, defOpt);
          trasen.setNamesVal($('#formItemAttr'), defOpt);
          form.render('select');
          form.render('radio');
          fileupload.initFile();
          $('#formItemAttr .layui-tab-title li')
            .removeClass('layui-this')
            .eq(0)
            .addClass('layui-this');
          $('#formItemAttr .layui-tab-content .layui-tab-item')
            .removeClass('layui-show')
            .eq(0)
            .addClass('layui-show');
          $('#formItemAttr').removeClass('none');
          $('#formDesign #formItemAttr .archivesTabBtn').removeClass('none');
        }
        //属性回显
        function echoDom(json) {
          if(json.fieldTyp == 'childFormCount'){
            json.remark = json.remark.trim();
          }
          $('#formDesign [name="isHideCommentFile"]').removeProp('checked');
          $('#formDesign [name="isMustCommentFile"]').removeProp('checked');
          $('#dataFormat')
            .addClass('none')
            .find('.layui-form-item')
            .addClass('none');
          $('#dataStyle')
            .addClass('none')
            .find('.layui-form-item')
            .addClass('none');
          $('[name="sourceField"]')
            .closest('.layui-form-item')
            .addClass('none');
          $('[name="calculationRole"]')
            .closest('.layui-form-item')
            .addClass('none');
          $('[name="dictSource"]').closest('.layui-form-item').addClass('none');
          $('[name="optionValue"]')
            .closest('.layui-form-item')
            .addClass('none');
          $('#formItemAttr [name="fieldType"]').prop('disabled', false);
          $('#formItemAttr [name="dataSource"]').prop('disabled', false);
          $('#formItemAttr [name="dictSource"]').prop('disabled', false);
          $('#formItemAttr [name="sourceField"]').prop('disabled', false);
          setSerialNumberRules(json.serialNumberRoleList);
          formItemFieldTypeChange(json.fieldType);
          formItemDataSourceChange(json.dataSource, json);
          trasen.setNamesVal($('#formItemAttr'), json);
          json.fieldTyp === 'comment' && hideCommentFileFieldChange(json.isHideCommentFile);
          serialNumberRules = json.serialNumberRoleList;
          $('#formItemAttr [name="optionValue"]').off('keydown');
          $('#formItemAttr [name="fieldType"] option').prop('disabled', false);
          fileupload.initFile(json.fileTemplate);
          if (chiocedItem.id) {
            $('#formItemAttr [name="dictSource"]').prop('disabled', true);
            /** @desc 苏莉 2022-06-17 放开字段类型限制，可随意切换字段类型 start*/
            //$('#formItemAttr [name="fieldType"] option').prop('disabled', true);
            /** @desc 苏莉 2022-06-17 放开字段类型限制，可随意切换字段类型 end*/
            if (json.fieldType == 'input' || json.fieldTyp == 'textarea') {
              $('#formItemAttr [name="fieldType"]').prop('disabled', false);
              $('#formItemAttr [name="fieldType"] option[value="input"]').prop(
                'disabled',
                false
              );
              $(
                '#formItemAttr [name="fieldType"] option[value="textarea"]'
              ).prop('disabled', false);
            } else {
              $('#formItemAttr [name="fieldType"]').prop('disabled', true);
              $('#formItemAttr [name="dataSource"]').prop('disabled', true);
              $('#formItemAttr [name="sourceField"]').prop('disabled', true);
            }
            if (json.fieldType == 'radio') {
              $('#formItemAttr [name="fieldType"]').prop('disabled', false);
              $('#formItemAttr [name="fieldType"] option[value="radio"]').prop(
                'disabled',
                false
              );
              $(
                '#formItemAttr [name="fieldType"] option[value="checkbox"]'
              ).prop('disabled', false);
              $('#formItemAttr [name="fieldType"] option[value="select"]').prop(
                'disabled',
                false
              );
            }
            if (json.fieldType == 'select' && json.dataSource == 1) {
              $('#formItemAttr [name="fieldType"]').prop('disabled', false);
              $('#formItemAttr [name="fieldType"] option[value="radio"]').prop(
                'disabled',
                false
              );
              $(
                '#formItemAttr [name="fieldType"] option[value="checkbox"]'
              ).prop('disabled', false);
              $('#formItemAttr [name="fieldType"] option[value="select"]').prop(
                'disabled',
                false
              );
            }
            //禁止删除选项值
            // $('#formItemAttr [name="optionValue"]').on('keydown', function (e) {
            //     var code = e.keyCode || e.which;
            //     if (code == 8) {
            //         return false;
            //     }
            // });
          }
          form.render('select');
          form.render('radio');
          $('#formItemAttr .layui-tab-title li')
            .removeClass('layui-this')
            .eq(0)
            .addClass('layui-this');
          $('#formItemAttr .layui-tab-content .layui-tab-item')
            .removeClass('layui-show')
            .eq(0)
            .addClass('layui-show');
          $('#formItemAttr').removeClass('none');
          $('#formDesign #formItemAttr .archivesTabBtn').removeClass('none');
        }
        //计算规则设置
        $('#calculationRules')
          .off('click')
          .on('click', function () {
            // if (chiocedItem.id) {
            //     return false;
            // }
            $.quoteFun('process/formtable/formulaEdit/index', {
              title: '公式计算',
              fields: wordConfigList,
              selField: chiocedItem,
              name: $('#formDesign [name="fieldName"]').val(),
              callback: function (str) {
                chiocedItem.calculationRole = str;
                $('#formItemAttr [name="calculationRole"]').val(str);
              },
            });
          });

        //流水号设置serialNumberRules
        $('#serialNumberRules')
          .off('click')
          .on('click', function () {
            $.quoteFun('process/formtable/serialNumber/index', {
              selField: chiocedItem,
              callback: function (list) {
                serialNumberRules = list;
                chiocedItem.serialNumberRoleList = list;
                setSerialNumberRules(list);
              },
            });
          });

        //保存属性
        form.on('submit(formItemAttrSave)', function (data) {
          let queryParams = {};
          let entity = [];
          //获取关联流程中的关联字段
          var groups = $(
            '#formDesign .association-form .association-form-field-list .layui-form-item'
          );
          console.log(data);
          $.each(groups, function (i, n) {
            const rowId = $(this).attr('row-id');
            // entity[data.field[`filterKey-${rowId}`]] = data.field[`filterValue-${rowId}`]
            const key = data.field[`filterKey-${rowId}`];
            const keyFieldname = $(this)
              .find(`[value="${key}"]`)
              .attr('fieldname');
            const value = data.field[`filterValue-${rowId}`];
            const valueFieldname = $(this)
              .find(`[value="${value}"]`)
              .attr('fieldname');
            entity.push({
              key,
              keyFieldname,
              value,
              valueFieldname,
            });
            delete data.field[`filterKey-${rowId}`];
            delete data.field[`filterValue-${rowId}`];
          });
          data.field.associationMap = entity;
          if (chiocedItem.keyId) {
            chiocedItem = $.extend({}, chiocedItem, data.field);
            if (!data.field.isReadonly) {
              chiocedItem.isReadonly = '';
            }
            if (!data.field.isMakeBigger) {
              chiocedItem.isMakeBigger = '';
            }
            if (!data.field.isThousandth) {
              chiocedItem.isThousandth = '';
            }
            if (!data.field.isMust) {
              chiocedItem.isMust = '';
            }
            if (
              data.field.max &&
              data.field.min &&
              data.field.min > data.field.max
            ) {
              layer.msg('最大值不能小于最小值');
              return false;
            }
            if (!data.field.isRed) {
              chiocedItem.isRed = '';
            }
            if (!data.field.isBold) {
              chiocedItem.isBold = '';
            }
            if (!data.field.wrap) {
              chiocedItem.wrap = '';
            }
            if (!data.field.isHideCommentFile) {
              chiocedItem.isHideCommentFile = '';
            }
            if (!data.field.isMustCommentFile) {
              chiocedItem.isMustCommentFile = '';
            }
            if (!data.field.hideApprovalTime) {
              chiocedItem.hideApprovalTime = '';
            }
            if (!data.field.approvalTimeFormat) {
              chiocedItem.approvalTimeFormat = '';
            }
            chiocedItem.serialNumberRoleList = serialNumberRules;
            $(iframeDocument.document)
              .find('#' + chiocedItem.keyId)
              .html('[' + chiocedItem.showName + ']');
            wordConfigList[chiocedItem.keyId] = chiocedItem;
            queryParams = chiocedItem;
          } else {
            var d;
            d = data.field;
            if (!data.field.isMakeBigger) {
              d.isMakeBigger = '';
            }
            if (!data.field.isThousandth) {
              d.isThousandth = '';
            }
            if (!data.field.isMust) {
              d.isMust = '';
            }
            if (!data.field.isReadonly) {
              chiocedItem.isReadonly = '';
            }
            d.keyId = util.guid();
            d.serialNumberRoleList = serialNumberRules;
            var text =
              "<span class='wfFormItem' id='" +
              d.keyId +
              "' style='user-select: none;' contenteditable='false'>[" +
              d.showName +
              ']</span>';
            // 处理没有获取焦点的情况
            if (
              tymIframeFocus &&
              $(tymIframeFocus.id).parents('.wfFormItem').length
            ) {
              $($(tymIframeFocus.id).parents('td')[0]).append(text);
            } else {
              tinymce.execCommand('mceInsertContent', false, text);
            }
            wordConfigList[d.keyId] = d;
            queryParams = d;
          }
          if (queryParams.dataSource == '6') {
            const params = {
              fieldId: queryParams.keyId,
              workflowId: queryParams.relationWorkflowId,
              relation: JSON.stringify(queryParams.associationMap),
            };
            $.ajax({
              url: '/ts-form/dpFieldRelation/save',
              method: 'post',
              contentType: 'application/json',
              data: JSON.stringify(params),
              success: function (res) {},
            });
          }
          return false;
        });
        $('#formDesign #resetFormItemAttr')
          .off('click')
          .on('click', function () {
            initAttrList();
            // $('#formItemAttr').addClass('none');;
            return false;
          });

        $('#formDesign #syncPrint')
          .off('click')
          .on('click', function () {
            syncPrint();
            return false;
          });

        function syncPrint() {
          var d = opt.data;
          d.printTemplate = tinymce.get('editContent').getContent();
          $.ajax({
            url: '/ts-form/dpFormTemplate/update',
            method: 'post',
            contentType: 'application/json',
            data: JSON.stringify(d),
            success: function (res) {
              if (res.success) {
                opt.ref && opt.ref();
                layer.msg(res.message || '同步打印模板成功');
              } else {
                layer.msg(res.message || '失败');
              }
            },
          });
        }

        function saveFormDesign(callback) {
          $(iframeDocument.document).find('td').removeClass('designSel');
          var keys = $(iframeDocument.document).find('.wfFormItem');
          var list = [];
          var hisFieldIdList = [];
          $.each(keys, function () {
            var k = $(this).attr('id');
            list.push(wordConfigList[k]);
          });
          $.each(list, function (i, item) {
            var isHisField = hisField.some((field) => field.id == item.id);
            if (isHisField) {
              item.isDeleted = 'N';
              hisFieldIdList.push(item.id);
            }
          });
          var content = tinymce.get('editContent').getContent();
          var d = {
            toaFieldSetList: list,
            templateName: $('#formDesign [name="templateName"]').val(),
            templateCode: $('#formDesign [name="templateCode"]').val(),
            classifyName: $('#formDesign [name="classifyName"]').val(),
            templateType: $('#formDesign [name="templateType"]').val(),
            classifyId: $('#formDesign [name="classifyId"]').val(),
            content: content,
          };
          d.javascriptText = TestEditor.getValue();
          var url = '/ts-form/dpFormTemplate/insert';
          if (opt.data && opt.data.id) {
            url = '/ts-form/dpFormTemplate/update';
            d = $.extend({}, opt.data, d);
          }
          //过滤无效字段
          var items = $(iframeDocument.document).find('.wfFormItem');
          var names = [];
          var repName = [];
          var ids = [];
          for (var i = 0; i < items.length; i++) {
            ids.push($(items[i]).attr('id'));
          }
          for (var i = d.toaFieldSetList.length - 1; i >= 0; i--) {
            if (ids.indexOf(d.toaFieldSetList[i].keyId) == -1) {
              d.toaFieldSetList.splice(i, 1);
            } else {
              if (names.indexOf(d.toaFieldSetList[i].showName) == -1) {
                names.push(d.toaFieldSetList[i].showName);
              } else {
                repName.push(d.toaFieldSetList[i].showName);
              }
            }
          }
          if (names.length != d.toaFieldSetList.length) {
            layer.msg('字段名称不能重复!');
            return false;
          }
          if (hisFieldIdList.length) {
            $.ajax({
              url: '/ts-form/dpFormTemplate/restoreLisField',
              method: 'post',
              async: false,
              contentType: 'application/json',
              data: JSON.stringify(hisFieldIdList),
              success: function (res) {},
            });
          }
          $.ajax({
            url: url,
            method: 'post',
            async: false,
            contentType: 'application/json',
            data: JSON.stringify(d),
            success: function (res) {
              if (res.success) {
                tinymce.remove('#editContent');
                opt.ref && opt.ref();
                layer.msg(res.message || '成功');
                callback && callback(res);
              } else {
                layer.msg(res.message || '失败');
              }
            },
          });
        }

        form.on('submit(customerFormListFormSave)', function (data) {
          saveFormDesign(function () {
            tinymce.remove('#editContent');
            layer.close(wins);
          });
          return false;
        });
        form.on('submit(customerFormListFormSaveNext)', function (data) {
          saveFormDesign(function (data) {
            $.ajax({
              type: 'post',
              url: '/ts-form/dpFormTemplate/findById/' + data.object,
              dateType: 'json',
              contentType: 'application/json',
              success: function (res) {
                if (res.success) {
                  var objectData = res.object;
                  var items = $(iframeDocument.document).find('.wfFormItem');
                  var keys = [];
                  for (var i = 0; i < items.length; i++) {
                    for (
                      var j = 0;
                      j < objectData.toaFieldSetList.length;
                      j++
                    ) {
                      if (
                        objectData.toaFieldSetList[j].keyId ==
                        $(items[i]).attr('id')
                      ) {
                        keys.push(objectData.toaFieldSetList[j]);
                        break;
                      }
                    }
                  }
                  tinymce.remove('#editContent');
                  layer.close(wins);
                  $.quoteFun('process/formtable/formdesign/phoneTemplate', {
                    title: '手机模板设置',
                    data: objectData,
                    itemList: keys,
                    ref: opt.ref,
                  });
                } else {
                  layer.msg(res.message);
                }
              },
              error: function (res) {
                layer.msg(res.message);
              },
            });
          });
          return false;
        });
        //保存
        $('#formDesign')
          .off('click', '#customerFormListFormSave')
          .on('click', '#customerFormListFormSave', function () {
            $('[lay-filter="customerFormListFormSave"]').trigger('click');
            return false;
          });
        //保存 下一步
        $('#formDesign')
          .off('click', '#customerFormListFormSaveNext')
          .on('click', '#customerFormListFormSaveNext', function () {
            $('[lay-filter="customerFormListFormSaveNext"]').trigger('click');
            return false;
          });
        //关闭
        $('#formDesign')
          .off('click', '#close')
          .on('click', '#close', function () {
            layer.confirm(
              '当前内容未保存，确定关闭窗口？',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0,
              },
              function (index) {
                //do something
                tinymce.remove('#editContent');
                layer.close(wins);
                layer.close(index);
              }
            );
          });
      }
    );
  };
});
