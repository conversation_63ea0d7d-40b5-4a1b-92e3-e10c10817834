'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;
            
            var tab = 0;
            $('#hrmsWarningBox .oa-nav .oa-nav_item ').off('click').on('click', function () {
                tab = $(this).index();
                $(this).addClass('active').siblings().removeClass('active');
                $('#hrmsWarningBox .tab-box').addClass('none').eq(tab).removeClass('none');
                if(1 == tab){
                	resrtForm();
                }
                return false;
            });
            
            //表格
            var warningRecordTable = new $.trasenTable('warningRecordTable', {
                url: common.url + '/ts-hrms/api/warningRecord/list' ,
                pager: '#warningRecordPager',
                mtype: 'get',
                shrinkToFit: true,
                sortname: 'create_date desc,warning_title desc,due_date',
                sortorder: 'desc',
                rownumbers: true,
                colModel: [
                    {
                        label: 'id',
                        name: 'id',
                        hidden: true
                    },
                    {
                        label: 'empId',
                        name: 'empId',
                        hidden: true
                    },
                    {
                        label: '工号',
                        sortable: true,
                        width:120,
                        name: 'empCode',
                        index:'emp_code',
                        align: 'center'
                    },
                    {
                        label: '员工姓名',
                        sortable: true,
                        width:90,
                        index:'emp_name',
                        name: 'empName',
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                        	return '<p style="cursor:pointer;"><span emp_id="' + rowObject.empId + '" class="showUserTimeLine dealLink">' + cellvalue + '</span></p>';
                        }
                    },
                    {
                        label: '手机号码',
                        sortable: true,
                        name: 'empPhone',
                        index:'emp_phone',
                        align: 'center',
                        width: 100
                    },
                    {
                        label: '性别',
                        sortable: true,
                        name: 'empSex',
                        index: 'emp_sex',
                        align: 'center',
                        width: 50,
                        formatter: function (cellvalue, options, rowObject) {
                        	if("0" == cellvalue){
                        		return "男";
                        	}else if("1" == cellvalue){
                        		return "女";
                        	}else{
                        		return "未知";
                        	}
                        }
                    },
                    {
                        label: '组织机构',
                        sortable: true,
                        name: 'empDeptName',
                        index:'emp_dept_name',
                        width: 120,
                        align: 'left'
                    },
                    {
                        label: '预警名称',
                        sortable: true,
                        width: 90,
                        name: 'warningTitle',
                        index:'warning_title',
                        align: 'center'
                    },
                    {
                        label: '到期时间',
                        sortable: true,
                        width:90,
                        name: 'dueDate',
                        index:'due_date',
                        align: 'center'
                    },
                    {
                        label: '剩余时间(天)',
                        sortable: true,
                        width:80,
                        name: 'surplusDate',
                        index:'surplus_date',
                        align: 'center'
                    }
                ],
                buidQueryParams: function () {
                    var search = $('#warningRecordSearchForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value.trim();
                    }
                    return data;
                },
            });

            $("#warningRecordTable").jqGrid('setLabel','0','序号','labelstyle');
            

			//搜索
			$('#warningRecordSearchBtn').funs('click', function () {
				warningRecordTable.refresh();
			})
			
			//重置
			$('#warningRecordResetBtn').funs('click', function () {
				$("#warningRecordSearchForm")[0].reset();
				warningRecordTable.refresh();
			})
			
			//导出
            $('#warningRecordExportBtn').funs('click', function () {
            	var search = $('#warningRecordSearchForm').serializeArray();
                var opt =  [];
                var data = {};
                for (var i in search) {
                    opt.push(search[i]);
                }
                var params = "";
                for (var i in opt) {
                    params += opt[i].name + "=" +opt[i].value + "&";
                }
                
                window.location.href = common.url + '/ts-hrms/api/warningRecord/exportHrmsWarningRecord?' + params.substring(0,params.length-1);
            });
			
			//详情
            $('body').off('click', '.showUserTimeLine').on('click', '.showUserTimeLine', function () {
            	var rowData = warningRecordTable.getSelectRowData(); 
            	var data = {};
            	data.employeeId = rowData.empId;
            	$.quoteFun('personnelmgr/userTimeLine/index', {
                    data: data,
                    title: '个人主页'
                });
            });
            
            //加载预警设置数据
            var warningSettingList = null;
            selectWarningList();
            function selectWarningList(){
            	$.ajax({
                    url:'/ts-hrms/api/warningSetting/selectWarningList',
                    method: 'get',
                    contentType: 'application/json;charset=UTF-8',
                    success: function (res) {
                        if (res.success) {
                        	var htmlStr = '';
                        	$("#warningTitleUl").empty();
                        	warningSettingList = res.object;
                        	$("#warningTitleSelect").empty();
                        	$("#warningTitleSelect").append(new Option("全部",""));
                        	$.each(res.object,function(i,item){
                        		$("#warningTitleSelect").append(new Option(item.warningTitle,item.warningTitle));
                        		htmlStr += '<li index="' + i + '" class="warningTitleItem">' + item.warningTitle + '</li>';
                        	})
                        	$("#warningTitleUl").append(htmlStr);
                        	form.render();
                        } else {
                            layer.msg(res.message || '查询失败');
                        }
                    },
                });
            }
            
            
            $('#hrmsWarningBox').off('click', '.warningTitleItem').on('click', '.warningTitleItem', function () {
                $(this).addClass('warningTitleItemPress').siblings('.warningTitleItem').removeClass('warningTitleItemPress');
                var index = $(this).attr('index');
                var warningSetting = warningSettingList[index];
                initWarningConditions(warningSetting.warningType);
                initCycleDate(warningSetting.warningCycle);
                trasen.setNamesVal($('#warningSettingForm'),warningSetting);
                if("1" == warningSetting.status){
            		$("#warningSettingstatus").prop("checked", true);
                }else{
                	$("#warningSettingstatus").prop("checked", false);
                }
                $("#warningTitle").attr("disabled","disabled");
                var hrmsWarningCompareList =  warningSetting.hrmsWarningCompareList;
                $('#warningCompareTable tr:not(:first)').each(function(){
                	$(this).remove();
	            });
                if(null != hrmsWarningCompareList && hrmsWarningCompareList.length > 0){
                	
                	$.each(hrmsWarningCompareList,function(i,item){
                		initWarningCompareTable(item.id,item);
                		$("#compareType_" + item.id).val(item.compareType);
    					$("#compareSymbols_" + item.id).val(item.compareSymbols);
    					$("#compareValue_" + item.id).val(item.compareValue);
    					$("#compareLogic_" + item.id).val(item.compareLogic);
                	})
                }
                let noticeTypeArr = warningSetting.noticeType.split(",");
            	$.each(noticeTypeArr,function(i,item){
            	    let node = $('input[type="checkbox"][name^="noticeType"][value="' + item + '"]');
            		if (node && node.length) {
            	        node[0].checked = true;
            	    }
            	})
            	$("#delWarningSetting").show();
                calculateNoticeTemplateNums();
                form.render();
            });
            
            //监听预警类型下拉框
            form.on('select(warningType)', function (data) {
            	$("#warningConditions").empty();
            	$("#warningConditions").append(new Option("请选择",""));
            	initWarningConditions(data.value);
                form.render('select');
            });
            
            
            
            function initWarningConditions(value){
            	if("到期提醒" == value){
            		$("#conditionsNumbersDiv").hide();
            		$("conditionsNumbers").removeAttr("lay-verify");
            		$("#warningConditions").append(new Option("合同到期","合同到期"));
                	$("#warningConditions").append(new Option("转正日期","转正日期"));
                	$("#warningConditions").append(new Option("岗位晋升日期","岗位晋升日期"));
                	$("#warningConditions").append(new Option("职称晋升日期","职称晋升日期"));
                	$("#warningConditions").append(new Option("生日日期","生日日期"));
                	$("#warningConditions").append(new Option("请假结束日期","请假结束日期"));
            	}else{
            		$("#conditionsNumbersDiv").show();
            		$("conditionsNumbers").attr("lay-verify","required");
            		$("#warningConditions").append(new Option("退休年龄","退休年龄"));
                	$("#warningConditions").append(new Option("党龄","党龄"));
                	$("#warningConditions").append(new Option("单位连续工龄","单位连续工龄"));
            	}
            }
            
            //监听预警周期下拉框
            form.on('select(warningCycle)', function (data) {
            	initCycleDate(data.value);
            	form.render('select');
            });
            
            //监听启用按钮
            form.on('switch(warningSettingstatusBox)', function (data) {
				if (data.elem.checked == true) {
					$("#hrmsWarningBox #status").val("1");
				} else {
					$("#hrmsWarningBox #status").val("2");
				}
			});
            
            
            function initCycleDate(warningCycleValue){
            	$("#cycleDateDayDiv").empty();
            	var htmlStr = '';
            	if("每周" == warningCycleValue){
            		htmlStr = '<select id="cycleDateDay" name="cycleDate" lay-verify="required" lay-search>'
        			htmlStr += '<option value=""></option>';
            		htmlStr += '<option value="Monday">星期一</option>';
            		htmlStr += '<option value="Tuesday">星期二</option>';
            		htmlStr += '<option value="Wednesday">星期三</option>';
            		htmlStr += '<option value="Thursday">星期四</option>';
            		htmlStr += '<option value="Friday">星期五</option>';
            		htmlStr += '<option value="Saturday">星期六</option>';
            		htmlStr += '<option value="Sunday">星期日</option>';
            		htmlStr += '</select>';
            		$("#cycleDateDayDiv").html(htmlStr);
            	}
            	if("每月" == warningCycleValue){
            		htmlStr = '<select id="cycleDateDay" name="cycleDate" lay-search lay-verify="required">'
        			
            		for (var i = 1; i <= 31; i++) {
            			htmlStr += '<option value="' + replenishZero(i) + '">' + replenishZero(i) + '</option>';
					}	
            		htmlStr += '</select>';
            		$("#cycleDateDayDiv").html(htmlStr);
            	}
            	if("每年" == warningCycleValue){
            		htmlStr += '<input type="text" lay-verify="required" name="cycleDate" class="layui-input" id="cycleDateDay" placeholder="yyyy-MM-dd">';
            		$("#cycleDateDayDiv").html(htmlStr);
            		laydate.render({
                        elem: '#cycleDateDay',
                        format:'yyyy-MM-dd',
                        showBottom: true,
                        trigger: 'click'
                        //min:new Date().toLocaleString('chinese',{hour12:false})
                    });
            	}
            }
            
            function  replenishZero(s) {
            	 return  s < 10 ?  '0'  + s: s;
            }
               
            
           
            laydate.render({
                elem: '#cycleDateDayTime',
                type: 'time',
                format:'HH:mm',
                showBottom: true,
                trigger: 'click'
                //min:new Date().toLocaleString('chinese',{hour12:false})
            });
            
            $('.addCompareBtn').funs('click', function () {
            	var trId = createUUID();
            	initWarningCompareTable(trId);
            });
            
            function initWarningCompareTable(trId){
            	var htmlStr = '<tr id="' + trId + '"><td><select lay-search id="compareType_' + trId + '">';
            	htmlStr += '<option value="employee_name">姓名</option>';
            	htmlStr += '<option value="gender">性别</option>';
            	htmlStr += '<option value="establishment_type">编制类型</option>';
            	htmlStr += '<option value="employee_category">编制类别</option>';
            	htmlStr += '<option value="political_status">政治面貌</option>';
            	htmlStr += '<option value="employee_status">员工状态</option>';
            	htmlStr += '<option value="position_id">职务</option>';
            	htmlStr += '<option value="marriage_status">婚姻状况</option>';
            	htmlStr += '<option value="org_id">组织机构</option>';
            	htmlStr += '<option value="shifouzhongcengganbu">是否中层干部</option>';
            	htmlStr += '<option value="plgw">岗位类别</option>';
            	htmlStr += '<option value="gwdj">岗位等级</option>';
            	htmlStr += '<option value="education_type">学历</option>';
            	htmlStr += '</select></td><td><select id="compareSymbols_' + trId + '">';
            	htmlStr += '<option value="=">等于</option>';
            	htmlStr += '<option value="!=">不等于</option>';
            	/*htmlStr += '<option value=">=">大于等于</option>';
            	htmlStr += '<option value=">">大于</option>';
            	htmlStr += '<option value="<">小于</option>';
            	htmlStr += '<option value="<=">小于等于</option>';*/
            	htmlStr += '</select></td><td>';
            	htmlStr += '<input type="text" autocomplete="off" lay-verify="required"  class="layui-input" id="compareValue_' + trId + '" />';
            	htmlStr += '</td><td><select id="compareLogic_' + trId + '">';
            	htmlStr += '<option value=" and ">且</option>';
            	htmlStr += '<option value=" or ">或</option>';
            	htmlStr += '</select></td><td>';
            	htmlStr += '<a href="javascript:void(0);" class="delCompareBtn">删除</a>';
            	htmlStr += '</td></tr>'
            	$("#warningCompareTable").append(htmlStr);
            	form.render('select');
            }
            
        	
        	$('body').off('click', '.delCompareBtn').on('click', '.delCompareBtn', function () {
        		$(this).parent().parent().remove();
            });
        	
        	//通知人选择
        	$('#noticeUserName').funs('click', function () {
				var data = {
					isCheckDept: 'N',
					user_str: 'noticeUserName',
					user_id: "noticeUser",
					user_code: "noticeUser"
				};
				$.quoteFun('/common/userSel', {
					trasen: trasenTable,
					title: '人员选择',
					data: data
				});
			})
			
			$("#warningSettingForm").off('input', '#noticeTemplate').on('input', '#noticeTemplate', function (event) {
				calculateNoticeTemplateNums();
			});
			
        	//计算字数
			function calculateNoticeTemplateNums(){
            	if ($("#noticeTemplate").val().length >= 70) {
            		$("#noticeTemplate").val($("#noticeTemplate").val().substring(0, 70))
				}
				var msg = $("#noticeTemplate").val();
				$("#noticeTemplateNums").html(msg.length);
            }
        	
			$('.insertValueBtn').funs('click', function () {
            	if (typeof document.selection !="undefined") {//IE时执行		
					var noticeTemplate = $("#noticeTemplate");
					noticeTemplate.focus();
					var sel = document.selection.createRange();
					sel.text = $(this).val();
					noticeTemplate.focus();
				}else{//非ie时执行
					var ubb = document.getElementById("noticeTemplate");
					var ubbLength = ubb.value.length;
					var val = $(this).val();
					ubb.focus();
					ubb.value= ubb.value.substr(0,ubb.selectionStart)+ val + ubb.value.substring(ubb.selectionStart,ubbLength);
					ubb.focus();
				}	
            	calculateNoticeTemplateNums();
            });
			
			//异常通知
			form.on('switch(warningSettingstatus)', function (data) {
				if (data.elem.checked == true) {
					$("#status").val("1");
				} else {
					$("#status").val("2");
				}
			});
			
			//删除
			$("#delWarningSetting").funs('click',function(){
				var id = $("#warningSettingId").val();
				if(null != id && '' != id){
					 layer.confirm('确定删除当前数据吗？',{
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                     },function (index) {
						 $.ajax({
			                    url:'/ts-hrms/api/warningSetting/delete/' + id,
			                    method: 'post',
			                    contentType: 'application/json;charset=UTF-8',
			                    success: function (res) {
			                        if (res.success) {
			                        	layer.msg('删除成功');
			                        	resrtForm();
			                        } else {
			                            layer.msg(res.message || '删除失败');
			                        }
			                    },
			                });
					 });
				}else{
					layer.msg('请先选择一条需要删除的数据！');
				}
			})
			
			//重置
			$("#resetWarningSetting").funs('click',function(){
				resrtForm();
			})
			
			
			// 保存
            form.on('submit(warningSettingSubmitCofirm)', function (data) {
                var d = data.field;
                
                var warningCompareArr = [];
				$('#warningCompareTable tr:not(:first)').each(function(){
					var warningCompare = {};
					var warningCompareId = $(this).attr("id");
					warningCompare.id = warningCompareId;
					warningCompare.compareType = $("#compareType_" + warningCompareId).val();
					warningCompare.compareSymbols = $("#compareSymbols_" + warningCompareId).val();
					warningCompare.compareValue = $("#compareValue_" + warningCompareId).val();
					warningCompare.compareLogic = $("#compareLogic_" + warningCompareId).val();
					warningCompareArr.push(warningCompare);
	            });
				
				var noticeTypeArr = [];
                $('input[name=noticeType]:checked').each(function(){
                     noticeTypeArr.push($(this).val());
                });
				
				d.hrmsWarningCompareList = warningCompareArr;
				d.noticeType = noticeTypeArr.join(",");
				
                var url;
                if (d.id) {
                    url = common.url + '/ts-hrms/api/warningSetting/update';
                }else{
                	url = common.url +  '/ts-hrms/api/warningSetting/save';
                }
                if (!d.id) {
                    delete d.id;
                }
                
                $.loadings();
                $.ajax({
                    url: url,
                    method: 'post',
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify(d),
                    success: function (res) {
                        if (res.success) {
                            layer.msg(res.message || '保存成功');
                            resrtForm();
                        } else {
                            layer.msg(res.message || '保存失败');
                        }
                    },
                 });
            });
			
			function resrtForm(){
				$('#warningSettingForm')[0].reset();
				$("#warningSettingId").val("");
                $("#noticeTemplateNums").html(0);
                $('#warningCompareTable tr:not(:first)').each(function(){
                	$(this).remove();
	            });
                $("#warningConditions").empty();
                $("#delWarningSetting").hide();
                $("#warningTitle").removeAttr("disabled");
                selectWarningList();
			}
			
            form.render();
        });
    };
});
