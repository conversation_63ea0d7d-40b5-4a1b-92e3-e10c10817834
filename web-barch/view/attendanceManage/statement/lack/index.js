"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'upload', 'trasen','zTreeSearch'], function() {
            var form = layui.form,
            layer = layui.layer,
            laydate = layui.laydate,
            upload = layui.upload,
            zTreeSearch = layui.zTreeSearch;
            form.render("select");
            var trasenTable;

            // 发放月份的日期控件
   /*         var date = new Date();
            var year = date.getFullYear(); // 获取完整的年份(4位)
            var month = date.getMonth(); // 获取当前月份(0-11,0代表1月)的上一个月
            if (month < 10) {
                month = "0" + month;
            }
            var lastDay = new Date(year, month, 0); //得到上个月月底日期
            var yearmonth = year + '-' + month;
            var date = year + '-' + month + '-' + lastDay.getDate();*/
			var myDate = new Date();
	        var time = myDate.toLocaleDateString().split('/').join('-');//将1970/08/08转化成1970-08-08
			var preMonth = getPreMonth(time);
			
			function getPreMonth(date) {
				var arr = date.split('-');
				var year = arr[0]; //获取当前日期的年份
				var month = arr[1]; //获取当前日期的月份
				var day = arr[2]; //获取当前日期的日
				var days = new Date(year, month, 0);
				days = days.getDate(); //获取当前日期中月的天数
				var year2 = year;
				var month2 = parseInt(month) - 1;
				if (month2 == 0) {
					year2 = parseInt(year2) - 1;
					month2 = 12;
				}
				var day2 = day;
				var days2 = new Date(year2, month2, 0);
				days2 = days2.getDate();
				if (day2 > days2) {
					day2 = days2;
				}
				if (month2 < 10) {
					month2 = '0' + month2;
				}
				var t2 = year2 + '-' + month2 + '-' + day2;
				return t2;
			}
            
            laydate.render({
                elem: '#statementLackSearchDate'
                ,type: 'month'
                ,range: '~'
                ,format: 'yyyy-MM'
                ,value: preMonth.substring(0,7)+ ' ~ ' + preMonth.substring(0,7)// 默认选择当前日期的上一个月
                ,max: preMonth // 最大值只可选择当前日期的上一个月（max不支持只填写年月的格式）
				,done: function(value, date, endDate){
					$('#statementLackSearchDate').val(value)
                	refreshTable();
                }
              });
            form.render();

            // 查询
            form.on('submit(statementLackSearch)', function(data) {
            	refreshTable()
            });

			// 重置
            form.on('submit(statementLackReset)', function(data) {
				$("#statementLackForm")[0].reset();
				$('#statementLackForm #statementLackSearchDate').val(preMonth.substring(0,7)+ ' ~ ' + preMonth.substring(0,7));
                form.render();
            	refreshTable()
            });
            
            // 导出月请假数据
            $(".areaButtonBoxR").off("click", "#statementLackTableExportCount").on("click", "#statementLackTableExportCount", function () {
                layer.confirm('确定要导出吗？', {
									btn: ['确定', '取消'],
									title: '提示',
									closeBtn: 0
                }, function (index) {
                	var date =  $("#statementLackSearchDate").val();  //月份
                	var title = date + "全院休假月报表";
                	var split = date.split(" ~ ");
                	if(split[0] == split[1]){
                		title = split[0].replace("-","年") + "月全院休假月报表"; 
                	}else{
                		title = split[0].replace("-","年") + "月至"+split[1].replace("-","年")+"月全院休假月报表"; 
                	}
                	var orgId = $("#statementLackForm input[name='orgId']").val();
                	var orgName = $("#statementLackForm input[name='orgName']").val();
                	if(orgName == ''){
                		orgId = '';
                	}
                	if(orgId != ""){
                		title = date + orgName +"人员全勤统计数据";
                	}
                    var url = common.url + "/ts-hrms/attendanceStatistics/exportStatementAllList?attendanceDate="
                    +date+"&title="+title+"&isQQ=1&orgId="+orgId;
                    location.href = (url);
                    layer.close(index);
                }, function () { });
            
            });
            
            //加载搜索区域部门下拉框
            treeSelect();
            function treeSelect() {
                //新部门
                zTreeSearch.init('#statementLackDeptNameSearch', {
                    url: common.url + '/ts-basics-bottom/organization/getTree',
                    type: 'post',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function(treeId, treeNode) {
                        $("input[name='orgId']").val(treeNode.id); 
                    }
                });
            }
            
            // 表格刷新
            function refreshTable() {
            	var orgName = $("#statementLackForm input[name='orgName']").val();
            	var orgId = $("#statementLackForm input[name='orgId']").val();
            	if(orgName == ""){
            		$("#statementLackForm input[name='orgId']").val("");
            	}
//            	initGrid();
            	$("#statementLackTable").jqGrid('setGridParam', {
                   postData: {
                	   orgId: $("#statementLackForm input[name='orgId']").val(),
                       attendanceDate: $("#statementLackSearchDate").val(),
                       isQQ:'1'
                   } 
                }).trigger("reloadGrid");
				if(columnArray.length != "" && columnArray.length > 0 ){
					var hideArray = new Array();
					var showArray = new Array();
					for(var i = 0; i < columnArray.length; i++){
						if(columnArray[i].index != ""){
							var count = $("#statementLackTable").getCol(columnArray[i].name, false, 'sum'); //统计要计算的列
							if(count == 0){
								hideArray.push(columnArray[i].name);
							}else{
								showArray.push(columnArray[i].name);
							}
						}
					}
					$("#statementLackTable").setGridParam().hideCol(hideArray).trigger("reloadGrid"); //要隐藏的列
					$("#statementLackTable").setGridParam().showCol(showArray).trigger("reloadGrid"); //要隐藏的列
				}
            }
            
            var columnArray;
            // 动态表格
            initGrid();
            function initGrid(){
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: common.url + '/ts-hrms/attendanceStatistics/getTableHeadColsByPerson',
                    async:false,
                    success:function(res) {
                        if (res.success) {
                        	columnArray = res.object;
                            // 表格渲染   
                        	$('#statementlackDiv .table-box').html('<table id="statementLackTable"></table>')
                        	trasenTable =  $("#statementLackTable").jqGrid({
                            	mtype: 'POST',
                            	datatype: "json", 
                                url: common.url + "/ts-hrms/attendanceStatistics/geStatementAllList",
//                                pager: 'personnelAllDayAuditPager',
                                sortname: 't2.create_date',
                                rowNum : 5000,
                                postData: {
                                    orgId: $("#statementLackForm input[name='orgId']").val(),
                                    attendanceDate: preMonth.substring(0,7) + ' ~ ' + preMonth.substring(0,7),
                                    isQQ:'1'
                                },
                                colModel: res.object,
                                rownumbers: true,
                                footerrow: true,
                                queryFormId: 'statementLackForm',
                                gridComplete: completeMethod
                            });
                        	$("#statementLackTable").jqGrid('setFrozenColumns');
                        }
                    }
                });
            }
            
            function completeMethod(){
            	//合计
            	var data = {}; 
		      	var colNames=$("#statementLackTable").jqGrid('getGridParam','colNames');
				var colModel=$("#statementLackTable").jqGrid('getGridParam','colModel');
			   //i =3 开始  基本工资
				for (var i=4;i<colNames.length;i++) {
					var columnName = colModel[i].name;
					var count = $("#statementLackTable").getCol(columnName,false,'sum');
					data[columnName] = count;
				}
				data['attendanceDate'] = "合计";
				$("#statementLackTable").jqGrid("footerData", "set", data);
            	var count = $("#statementLackTable").getGridParam("records");//当前有几行
    	        if (count > 0) {
    	        	if(columnArray.length != "" && columnArray.length > 0 ){
						var hideArray = new Array();
						var showArray = new Array();
						for(var i = 0; i < columnArray.length; i++){
							if(columnArray[i].index != ""){
								var count = $("#statementLackTable").getCol(columnArray[i].name, false, 'sum'); //统计要计算的列
								if(count == 0){
									hideArray.push(columnArray[i].name);
								}else{
									showArray.push(columnArray[i].name);
								}
							}
						}
						$("#statementLackTable").setGridParam().hideCol(hideArray).trigger("reloadGrid"); //要隐藏的列
						$("#statementLackTable").setGridParam().showCol(showArray).trigger("reloadGrid"); //要隐藏的列
					}
    	        }
				$("#statementlackDiv .ui-jqgrid-sdiv").addClass('ui-jqgrid-sdiv-box');
				$("#statementlackDiv .ui-jqgrid-bdiv").addClass('ui-jqgrid-bdiv-box-bottom');
				$("#statementlackDiv .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
				$('#statementlackDiv .trasen-con-box .ui-jqgrid .ui-jqgrid-view .ui-jqgrid-bdiv').css('bottom', '34px');
            } 
        });
        

     

        
    };
});