'use strict';
define(function (require, exports, module) {
  var init = function () {
    return perform();
  };
  module.exports = {
    init: init,
  };
  var perform = function () {
    layui.use(['form', 'laydate', 'trasen'], function () {
      common.overlayScrollbarsSet('.scrollbar-box')

      let API = {
        hrindexYggk: '/ts-hrms/hrindex/yggk',
        hrindexYuJingtips: '/ts-hrms/hrindex/yujingtips',
        hrindexTrainingRecord: '/ts-hrms/hrindex/trainingrecord',
        hrindexZyz: '/ts-hrms/hrindex/zyz',
        hrindexDgdb: '/ts-hrms/hrindex/dgdb',
        hrindexLeave: '/ts-hrms/hrindex/leave',
        hrindexPbsj: '/ts-hrms/hrindex/pbsj',
        planCount: '/ts-hrms/recruitPlan/planCount',
      }

      let earlyWarningNum = 0
      let earlyWarningViewNum = 6
      let earlyWarningClickNum = 0
			
			renderHrindexYuJingtipsHandle()
			renderStaffSurveyHandle()
			renderTrainingRecordHandle()
			renderZyzHandle()
			renderDgdbHandle()
			renderLeaveHandle()
			renderPlanCount()

      // 我的排班
      var now = new Date();
      var weekArr = [];
      var today = {
        year: now.getFullYear(),
        month: addZero(now.getMonth() + 1),
        date: now.getDate(),
        day: now.getDay()
      };
      getWeekArr(now);

      // 月份格式化
      function addZero (num) {
        var num = num - 0;
        return num > 9 ? num : '0' + num;
      }

      // 根据当前时间 获取本周信息
      function getWeekArr (time) {
        var times = new Date(time);
        var arr = [];
        var day = times.getDay() == 0 ? 7 : times.getDay();
        for (var i = 1; i < 8; i++) {
          arr.push(getWeekDay(times.getTime() + 1000 * 60 * 60 * 24 * (i - day)));
        }
        weekArr = arr;
        renderHomePagePersonScheduling()
      }

      function getWeekDay (time) {
        var time = new Date(time);
        return {
          year: time.getFullYear(),
          month: addZero(time.getMonth() + 1),
          date: addZero(time.getDate()),
        };
      }

      // 切换上一周下一周
      function currentWeek (number) {
        var t = new Date();
        t.setFullYear(weekArr[weekArr.length - 1].year);
        t.setMonth(weekArr[weekArr.length - 1].month - 1);
        t.setDate(weekArr[weekArr.length - 1].date);
        t.setTime(t.getTime() + 1000 * 60 * 60 * 24 * number);
        getWeekArr(t);
      }

      // 渲染排班数据
      function renderHomePagePersonScheduling () {
        $('#personnelHomePage .my-schedule-box .my-schedule-content').removeClass('schedule-noData')
        $('#personnelHomePage .my-schedule-box .my-schedule-content').html('')
        let pbDateObj = {
          startDate: `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`,
          endDate: `${weekArr[6].year}-${weekArr[6].month}-${weekArr[6].date}`
        }
        let scheduleData = []
        var isWeek = false
        $('#personnelHomePage #ScheduleYearHomePage').text(weekArr[0].year)
        $('#personnelHomePage #ScheduleMonthHomePage').text(weekArr[0].month)
        // 排班数据查询
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          async: false,
          url: common.url + API.hrindexPbsj,
          data: JSON.stringify(pbDateObj),
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              if (res.object && res.object.cqtj) {
                $('#personnelHomePage .my-schedule-box #cq').text(res.object.cqtj.cq || 0)
                $('#personnelHomePage .my-schedule-box #xj').text(res.object.cqtj.xj || 0)
                $('#personnelHomePage .my-schedule-box #jq').text(res.object.cqtj.jq || 0)
              }
              scheduleData = res.object.scheduleMap
            }
          },
        });

        for (let index = 0; index < weekArr.length; index++) {
          const element = weekArr[index];
          if (today.year == element.year && today.month == element.month && today.date == element.date) {
            isWeek = true;
          }
          if (isWeek) {
            $('#personnelHomePage #ScheduleThisWeek').addClass('active');
          } else {
            $('#personnelHomePage #ScheduleThisWeek').removeClass('active');
          }
        }

        if (scheduleData.length === 0) {
          $('#personnelHomePage .my-schedule-box .my-schedule-content').addClass('schedule-noData')
          return false
        }

        let str = ''
        let dateChineseArr = ['一', '二', '三', '四', '五', '六', '日']
        for (let index = 0; index < weekArr.length; index++) {
          let scheduleStr = ''
          let tipsStr = ''
          const element = weekArr[index];
          let date = `${element.year}-${element.month}-${element.date}`
          let todayDate = `${today.year}-${today.month}-${today.date}`
          let pastTense = false
          if (todayDate > date) {
            pastTense = true
          }
          scheduleData.forEach(scheduleItem => {
            if (date === scheduleItem.scheduling_date) {
              scheduleStr += `<span class="schedule-name">${scheduleItem.frequency_name}</span>
                              <span class="schedule-time">${scheduleItem.frequency_time}</span>`
              tipsStr += `${scheduleItem.frequency_name}   ${scheduleItem.frequency_time}   `
            }
          })
          if (!scheduleStr) scheduleStr += `<p class='no-data'>暂无排班数据</p>`
          str += `
                <li>
                  <span class="weekChinese">${dateChineseArr[index]}</span>
                  <span class="date ${pastTense ? 'past-tense' : ''}">${element.date}</span>
                  <p class="schedule-p-class" title="${tipsStr}">
                    ${scheduleStr}
                  </p>
                </li>
                `
        }
        $('#personnelHomePage .my-schedule-box .my-schedule-content').html(str)
      }


      // 渲染预警提醒方法
      function renderHrindexYuJingtipsHandle () {
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          url: common.url + API.hrindexYuJingtips,
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              let imgObj = {
                '合同': 'tips-htdq',
                '试用期': 'tips-syq',
                '退休': 'tips-tx',
                '生日': 'tips-sr',
                '党龄': 'tips-dl',
                '进修': 'tips-jx',
                '转正': 'tips-zz',
              }
              res.object.forEach(item => item.title = item.title.replace("预警", ""))
              let tipsArr = res.object
              let liStr = '';
              let imgNameArr = Object.keys(imgObj)
              for (let i = 0; i < tipsArr.length; i++) {
                const item = tipsArr[i];
                if (!item) break
                let imgIndex = 1
                for (let index = 0; index < imgNameArr.length; index++) {
                  const element = imgNameArr[index];
                  if (item.title.indexOf(element) !== -1) {
                    imgIndex = index
                  }
                }
                if (item) {
                  liStr += `
                          <li>
                            <div class="img-box">
                              <img src="/static/img/personnelhomePage/${imgObj[imgNameArr[imgIndex]]}.png" alt="">
                            </div>
                            <div class="font-box">
                              <p title='${item.title}'>${item.title}</p>
                              <span class="font-num">${item.number}</span>
                              <span class="font-company">件</span>
                            </div>
                          </li>
                          `
                }
              }
              if (tipsArr.length > 6) {
                $('#personnelHomePage #EarlyWarningRightMore').show()
                earlyWarningNum = tipsArr.length
              }
              $('#personnelHomePage .early-warning-item').html(liStr)
            }
          },
        });
      }
      $('#personnelHomePage .early-warning-item').off('click').on('click', function () {
        location.href = '#/warning'
      });

      $('#personnelHomePage #EarlyWarningLeftMore').off('click').on('click', function () {
        earlyWarningClickNum--
        if (earlyWarningClickNum === 0) {
          $('#personnelHomePage #EarlyWarningLeftMore').hide()
        } else {
          $('#personnelHomePage #EarlyWarningRightMore').show()
        }
        EarlyWarningMoreTransform('left')
      });

      $('#personnelHomePage #EarlyWarningRightMore').off('click').on('click', function () {
        earlyWarningClickNum++

        if (earlyWarningNum - 6 <= earlyWarningClickNum) {
          $('#personnelHomePage #EarlyWarningRightMore').hide()
        } else {
          $('#personnelHomePage #EarlyWarningLeftMore').show()
        }
        EarlyWarningMoreTransform('right')
      });

      function EarlyWarningMoreTransform (path) {
        const MOVE = $('#personnelHomePage .early-warning-item')
        const childWidth = MOVE.children().eq(0).width()
        const pending = MOVE.css('transform').split(/[()]/)[1];
        
        let moveNum = Number(pending.split(',')[4]);

        if (path === 'left') {
          moveNum += childWidth
        }
        if (path === 'right') {
          moveNum -= childWidth
        }

        MOVE.css('transform', `translateX(${moveNum}px)`)
      }


      $('#personnelHomePage .bottom-box .bottom-item').off('click').on('click', function () {
        const search = $(this).data('search')
        window.location.href = "#/personnelmgr/transaction";
        //创建事件监听对象
        Event.create('personnelHomePageOverview').trigger('jump', {search});
      })

      // 渲染员工概况方法
      function renderStaffSurveyHandle () {
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          url: common.url + API.hrindexYggk,
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              $('#personnelHomePage #SurveyBox #ZRS').text(res.object.zrs || 0)
              $('#personnelHomePage #SurveyBox #BN').text(res.object.bn || 0)
              $('#personnelHomePage #SurveyBox #HTZ').text(res.object.htz || 0)
              $('#personnelHomePage #SurveyBox #TGTC').text(res.object.tgtc || 0)
              $('#personnelHomePage #SurveyBox #LWPQ').text(res.object.lwpq || 0)
              $('#personnelHomePage #SurveyBox #RZ').text(res.object.rgrz || 0)
              $('#personnelHomePage #SurveyBox #LZ').text(res.object.lz || 0)
              $('#personnelHomePage #SurveyBox #FP').text(res.object.fp || 0)
              $('#personnelHomePage #SurveyBox #TX').text(res.object.tx || 0)
              $('#personnelHomePage #SurveyBox #DD').text(res.object.dd || 0)
              $('#personnelHomePage #SurveyBox #SQ').text(res.object.sq || 0)
              $('#personnelHomePage #SurveyBox #JJ').text(res.object.jj || 0)
              $('#personnelHomePage #SurveyBox #XX').text(res.object.xx || 0)
            }
          },
        });
      }

      // 渲染最新培训方法
      function renderTrainingRecordHandle () {
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          url: common.url + API.hrindexTrainingRecord,
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              if (res.object.length > 0) {
                let liStr = ''
                res.object.forEach(item => {
                  let date = item.createDate.slice(0, 10).split('-').join('.')
                  liStr += `
                          <li class="latest-training-item-li" row-id="${item.trainPlanId}">
                            <p class="li-titlt">${item.trainTopic}</p>
                            <p class="date">${date}</p>
                          </li>
                           `
                })
                $('#personnelHomePage .latest-training-ul-box').html(liStr)
              } else {
                $('#personnelHomePage .latest-training-ul-box').addClass('noData')
              }
            }
          },
        });
      }

      $("#personnelHomePage").off("click", ".latest-training-ul-box").on("click", ".latest-training-ul-box", function () {
        var trainPlanId = $(this).attr('row-id');
          $.quoteFun('train/record/modules/index', {
            title: '培训记录',
            data:trainPlanId
          })
        })

      var VolunteerServiceBox;

      // 渲染志愿者服务方法
      function renderZyzHandle () {
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          url: common.url + API.hrindexZyz,
          success: function (res) {
            if (res.success && res.statusCode === 200 && JSON.stringify(res.object) !== '{}') {
              $('#personnelHomePage .total-duration .total-num').text(res.object.count || 0)
              if (res.object.details && res.object.details.length > 0) {
                res.object.details.forEach(item => {
                  item.name = item.value + 'h' + '   ' + item.name
                })
              }
              // 志愿者服务 Echarts
              VolunteerServiceBox = echarts.init(document.getElementById('VolunteerServiceBox'));
              let VolunteerServiceBoxOption = {
                color: ['#74BAEA', '#F0BB80', '#4b7aff', '#F08086', '#C2C2C2'],
                tooltip: {
                  show: false,
                  trigger: 'item',
                  alwaysShowContent: true,
                  formatter: '{c}小时'
                },
                legend: {
                  orient: 'vertical',
                  left: '3%',
                  top: '20%'
                },
                series: [{
                  data: res.object.details || [],
                  name: 'Access From',
                  type: 'pie',
                  radius: ['45%', '65%'],
                  center: ['80%', '50%'],
                  avoidLabelOverlap: false,
                  label: {
                    show: false,
                    position: 'center'
                  },
                  labelLine: {
                    show: false
                  }
                }]
              };
              VolunteerServiceBox.setOption(VolunteerServiceBoxOption);
            }
          },
        });
      }

      var FixedPostsAndStaffing

      // 渲染定岗定编方法
      function renderDgdbHandle () {
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          url: common.url + API.hrindexDgdb,
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              let keyObj = {
                'houqin': 0,
                'hushi': 1,
                'jishi': 2,
                'yaoshi': 3,
                'yishi': 4,
              }
              let titleArr = []
              let customizedArr = []
              let actualArr = []
              let lackArr = []
              if (JSON.stringify(res.object.data1) !== '{}') {
                for (const key in res.object.data1) {
                  lackArr.push({
                    num: ((res.object.data3[key] * 100) - (res.object.data2[key] * 100)) / 100,
                    title: res.object.data1[key]
                  })
                }
                for (const nameKey in lackArr) {
                  const nameItem = lackArr[nameKey]
                  titleArr[nameKey] = nameItem.title + (nameItem.num < 0 ? `(缺${Math.abs(nameItem.num)}人)` : `(超${Math.abs(nameItem.num)}人)`)
                }
              }
              if (JSON.stringify(res.object.data2) !== '{}') {
                for (const key in res.object.data2) {
                  customizedArr[keyObj[key]] = res.object.data2[key]
                }
              }
              if (JSON.stringify(res.object.data3) !== '{}') {
                for (const key in res.object.data3) {
                  actualArr[keyObj[key]] = res.object.data3[key]
                }
              }
              // 实际-定编 为负数就缺多少人
              // 定岗定编 Echarts
              FixedPostsAndStaffing = echarts.init(document.getElementById('FixedPostsAndStaffing'));
              let FixedPostsAndStaffingOption = {
                tooltip: {
                  show: true,
                  trigger: 'item',
                  formatter: function (params) {
                    const {
                      value,
                      name
                    } = params;
                    return `${name} ${value}`;
                  }
                },
                legend: {
                  icon: 'horizontal',
                  textStyle: {
                    color: '#999999',
                    fontSize: 12
                  },
                  right: '5%',
                  itemWidth: 12,
                  selectedMode: false
                },
                xAxis: [{
                  data: titleArr,
                  nameTextStyle: {
                    overflow: true
                  },
                  axisTick: {
                    show: false //去除x轴刻度线
                  },
                  axisLabel: {
                    interval: 0, //强制显示所有x轴内容
                    formatter: function (value) {
                      let name = value.substring(0, 2) + "\n" + value.substring(2)
                      return `{a| ${name} }`;
                    },
                    rich: {
                      a: {
                        color: '#EC7B25'
                      }
                    }
                  }
                }],
                yAxis: [{
                  nameTextStyle: {
                    fontSize: 12, //y轴名字的字体大小
                    color: '#333'
                  },
                  axisLabel: {
                    show: true,
                    textStyle: {
                      color: '#333',
                      fontSize: 12
                    }
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: '#C2CCDC'
                    }
                  }
                }],
                series: [{
                  name: '定编',
                  type: 'bar',
                  data: customizedArr,
                  barWidth: 16, //柱状图 宽度 可以是百分比也可以是数字
                  itemStyle: {
                    normal: {
                      color: '#F08086'
                    }
                  }
                },
                  {
                    name: '实际',
                    type: 'bar',
                    data: actualArr,
                    barWidth: 16, //柱状图 宽度 可以是百分比也可以是数字
                    itemStyle: {
                      normal: {
                        color: '#4b7aff'
                      }
                    }
                  }
                ],
                grid: {
                  top: '10%',
                  left: '6.55%',
                  right: '6.55%',
                  bottom: '7%',
                  containLabel: true
                }
              }
              FixedPostsAndStaffing.setOption(FixedPostsAndStaffingOption);
            }
          },
        });
      }

      var CheckWorkAttendance;

      // 渲染考勤情况方法
      function renderLeaveHandle (type = '1') {
        $.ajax({
          type: 'post',
          contentType: 'application/json; charset=utf-8',
          url: common.url + API.hrindexLeave + '?type=' + type,
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              if (JSON.stringify(res.object) !== '{}') {
                let titleArr = Object.keys(res.object)
                let valueArr = Object.values(res.object)
                // 考勤情况 Echarts
                CheckWorkAttendance = echarts.init(document.getElementById('CheckWorkAttendance'));
                let CheckWorkAttendanceOption = {
                  tooltip: {
                    show: true,
                    trigger: 'item',
                    formatter: function (params) {
                      const {
                        value,
                        name
                      } = params;
                      return `${name} ${value}`;
                    }
                  },
                  grid: {
                    top: '15%',
                    left: '3%',
                    right: '4%',
                    bottom: '0%',
                    containLabel: true
                  },
                  xAxis: [{
                    type: 'category',
                    data: titleArr,
                    axisTick: {
                      alignWithLabel: true
                    },
                    axisLabel: {
                      rotate: 45, // 旋转30度
                      show: true, //这行代码控制着坐标轴x轴的文字是否显示
                      textStyle: {
                        color: '#000', //x轴上的字体颜色
                        fontSize: '14' // x轴字体大小
                      }
                    }
                  }],
                  yAxis: [{
                    type: 'value'
                  }],
                  series: [{
                    name: 'Direct',
                    type: 'bar',
                    barWidth: '30%',
                    data: valueArr,
                    itemStyle: {
                      normal: {
                        color: function (params) {
                          var colorList = [
                            '#F08086',
                            '#4b7aff',
                            '#F0BB80',
                            '#A474EA',
                            '#0AAF9F',
                            '#F08086',
                            '#4b7aff',
                            '#F0BB80',
                            '#A474EA',
                            '#0AAF9F',
                            '#F08086',
                          ];
                          return colorList[params.dataIndex];
                        },
                        label: {
                          show: true,
                          position: 'top',
                          formatter: '{c}'
                        }
                      }
                    }
                  }]
                };
                CheckWorkAttendance.setOption(CheckWorkAttendanceOption);
              }
            }
          },
        });
      }

      // 渲染招聘总数方法
      let recruitTitle = today.year + '年度'+ common.userInfo.hospName + '人才招聘专场'
      $('#personnelHomePage #RecruitTips').text(recruitTitle)

      function renderPlanCount() {
        $.ajax({
          type: "post",
          url: common.url + API.planCount,
          data: JSON.stringify({
            planId: ''
          }),
          contentType: "application/json;charset=UTF-8",
          success: function (res) {
            if (res.success && res.statusCode === 200) {
              let signUpStr = res.object.signUp > 0 ? `审查(${res.object.signUp})` : "审查";
              let signUpExamStr = res.object.signUpExam > 0 ? `笔试(${res.object.signUpExam})` : "笔试";
              let signUpInterviewStr = res.object.signUpInterview > 0 ? `面试(${res.object.signUpInterview})` : "面试";

              const progressItemDom = $("#personnelHomePage .recruit-count");
              progressItemDom.eq(0).text(signUpStr);
              progressItemDom.eq(1).text(signUpExamStr);
              progressItemDom.eq(2).text(signUpInterviewStr);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          }
        });
      }

      // 渲染常用入口模块
      getCommonEntranceHandle() // 常用入口

      $('#personnelHomePage #accessSet')
      .off('click')
      .on('click', function () {
          $.quoteFun('index/components/usesMenu/set', {
              ref: getCommonEntranceHandle,
          });
      });
      function getCommonEntranceHandle () {
        $('#personnelHomePage .common-entrance .common-entrance-page').html('');
        $.ajax({
          url: '/ts-basics-bottom/quickMenu/selectQuickMenuList',
          method: 'post',
          success: function (res) {
            if (res.success && res.object && res.object.length > 0) {
              let columnNum = 3;
              let pageNum = 6;
              let widthType = 100 / columnNum

              var currentWidth = $('#personnelHomePage .common-entrance').width();
              var html = '';
              for (let i = 0; i < res.object.length; i++) {
                const element = res.object[i];
                if (i % pageNum == 0) {
                  html += `<li class="access-list-item" style="width:${currentWidth}px">`;
                }
                html +=
                  `
                  <div class="item-icon-Jump" style="width:${widthType}%">
                  <a href="#${element.menuUrl}">
                  <span class="icon-box">
                  <i class="oaicon ${(element.menuIcon || 'oa-icon-rukou')}"></i>
                  </span>
                  <p class="access-text">${element.menuName}</p>
                  </a>
                  </div>
                  `;
                if (i % pageNum == pageNum - 1 || i == res.object.length - 1) {
                  html += '</li>';
                }
              }
              $('#personnelHomePage .common-entrance .common-entrance-page').append(html);
              slide();
            } else {
              $('#personnelHomePage .common-entrance .common-entrance-page').addClass('noData')
              $('#personnelHomePage .common-entrance .common-entrance-page').css('height', '100%')
              $('#personnelHomePage .common-entrance .common-entrance-footer').hide()
            }
          },
        });
      }

      function slide () {
        var currentWidth = $('#personnelHomePage .common-entrance').width();
        var currentLength = $('#personnelHomePage .access-list-item').length || 1;
        if (currentLength == 1) {
          $('#personnelHomePage a.operate_prev_page').addClass('none');
          $('#personnelHomePage a.control_next_page').addClass('none');
        } else {
          $('#personnelHomePage a.operate_prev_page').removeClass('none');
          $('#personnelHomePage a.control_next_page').removeClass('none');
        }
        var ulWidth = currentWidth * currentLength;
        $('#personnelHomePage .common-entrance-page').css({
          width: ulWidth,
          marginLeft: currentLength == 1 ? 0 : -currentWidth,
        });
        $('#personnelHomePage .access-list-item:last-child').prependTo('#personnelHomePage .common-entrance-page');

        function moveLeft () {
          $('#personnelHomePage .common-entrance-page').animate({
              left: +currentWidth,
            },
            200,
            function () {
              $('#personnelHomePage .access-list-item:last-child').prependTo('#personnelHomePage .common-entrance-page');
              $('#personnelHomePage .common-entrance-page').css('left', '');
            }
          );
        }

        function moveRight () {
          $('#personnelHomePage .common-entrance-page').animate({
              left: -currentWidth,
            },
            200,
            function () {
              $('#personnelHomePage .access-list-item:last-child').prependTo('#personnelHomePage .common-entrance-page');
              $('#personnelHomePage .common-entrance-page').css('left', '');
            }
          );
        }

        $('#personnelHomePage a.operate_prev_page').off('click').on('click', function () {
          moveLeft();
        });

        $('#personnelHomePage a.control_next_page').off('click').on('click', function () {
          moveRight();
        });
      }

      // 考勤情况 月份切换
      $('#personnelHomePage .left-five-box .time-card-ul').off('click', 'li').on('click', 'li', function () {
        const type = $(this).attr('type')
        $(this).addClass('active').siblings().removeClass('active')
        renderLeaveHandle(type)
      });
      // 上一周
      $('#personnelHomePage').off('click', '#my-schedule-prev-week').on('click', '#my-schedule-prev-week', function () {
        currentWeek(-7)
      })
      // 本周
      $('#personnelHomePage').off('click', '#ScheduleThisWeek').on('click', '#ScheduleThisWeek', function () {
        getWeekArr(now);
      })
      // 下一周
      $('#personnelHomePage').off('click', '#my-schedule-next-week').on('click', '#my-schedule-next-week', function () {
        currentWeek(7)
      })

      $('#FixedPostsAndStaffing').resize(function () {
        FixedPostsAndStaffing.resize();
      })
      $('#CheckWorkAttendance').resize(function () {
        CheckWorkAttendance.resize();
      })
      $('#VolunteerServiceBox').resize(function () {
        VolunteerServiceBox.resize();
      })

      // 常用流程切换事件
      var processIndex = 1;
      var processUrl = {
        1: '/ts-workflow/workflow/instance/getMyHandleWorkflowList?handleStatus=1&_search=false&nd=1616224446359&pageSize=20&pageNo=1&sidx=inst.create_date&sord=desc',
        2: '/ts-workflow/workflow/instance/getMyHandleWorkflowList?handleStatus=2&status=1&_search=false&nd=1616224586672&pageSize=20&pageNo=1&sidx=inst.update_date&sord=desc',
        3: '/ts-workflow/workflow/instance/getMyLaunchWorkflowList?handleStatus=&status=1&_search=false&nd=1616224629534&pageSize=20&pageNo=1&sidx=inst.status asc,inst.create_date&sord=desc',
        4: '/ts-workflow/workflow/instance/getCopyToMyWorkflowList?_search=false&nd=1616224657218&pageSize=20&pageNo=1&sidx=inst.create_date&sord=desc&status=1',
      };
      let first = false
      $('#personnelHomePage .common-processes .processHeader li').click(function () {
        $('#personnelHomePage .common-processes .processHeader li').siblings().removeClass('active');
        $(this).addClass('active');
        var index = $(this).attr('data-refTable');
        processIndex = index;

        $("#personnelHomePage .common-process").removeClass('noData');
        $("#personnelHomePage .waitDeal").removeClass('noData');
        if (0 == index) {
          $("#personnelHomePage .common-process").show();
          $("#personnelHomePage .waitDeal").hide();
          initCommonProcessList()
        } else {
          $("#personnelHomePage .waitDeal").show();
          $("#personnelHomePage .common-process").hide();
          waitDeal();
        }
      });
      $('.personnel-home-age-right .common-processes #TabsMorePage').click(function () {
        if (processIndex == 0) {
          window.location = '#/process/start'
          return false
        } else {
          window.location = '#/process/index'
          Event.create('toDealList').trigger('changeStatus', processIndex - 1, {});
        }
      })

      waitDeal()

      function initCommonProcessList () {
        $.ajax({
          url: '/ts-workflow/workflow/definition/getCommonlyUsed',
          method: 'get',
          success: function (res) {
            $('.personnel-home-age-right .common-processes #TabsMorePage').show()
            if (null != res && null != res.rows && res.rows.length > 0) {
              $('#personnelHomePage .common-process').html('')
              new common.simpleEvent({
                el: '#personnelHomePage .common-process',
                list: res.rows,
                event: 'click',
                classes: 'one-line',
                print: function (item) {
                  return '<i class="' + item.icon + '" style="color: ' + item.color + '"></i>' + item.workflowName + '';
                },
                func: function (item) {
                  var workflowNo = item.workflowNo;
                  $.ajax({
                    method: 'get',
                    url: '' + '/ts-workflow/workflow/definition/code/' + workflowNo,
                    success: function (res) {
                      if (res.success) {
                        if (res.object && res.object.isNormal == 'N') {
                          var son = common.processDeal.start(workflowNo);
                          common.openedWindow.push(son);
                        } else if (res.object && res.object.isNormal == 'Y') {
                          var opt = {
                            title: '流程发起',
                            ref: waitDeal,
                          };
                          $.quoteFun(res.object.initiatePageUrl, opt);
                        } else {
                          layer.msg(res.message || '获取流程信息失败');
                        }
                      } else {
                        layer.msg(res.message || '发起失败');
                      }
                    },
                  });
                },
              });
            } else {
              $("#personnelHomePage .common-process").addClass('noData');
              $('.personnel-home-age-right .common-processes #TabsMorePage').text('暂无常用流程, 点我去发起')
              $('.personnel-home-age-right .common-processes #TabsMorePage').css('bottom', '75px')
              $('.personnel-home-age-right .common-processes #TabsMorePage').css('background', '#fff')
            }
          },
        });
      }

      //清除定时器
      common.indexProcessListen && clearInterval(common.indexProcessListen);
      //移除监听
      Event.create('indexProcess').remove('change');
      var indexWinSon = [];
      common.indexProcessListen = setInterval(function () {
        //监听子页面关闭事件,轮询时间1000毫秒
        for (var i = indexWinSon.length - 1; i >= 0; i--) {
          if(indexWinSon[i] && indexWinSon[i].closed) {
            if(indexWinSon[i].openWiner) {
              processRef();
            }
            var openedWindowIndex = common.openedWindow.findIndex(item => {
              indexWinSon[i].name = item.name
            })
            if(openedWindowIndex != -1) common.openedWindow.splice(openedWindowIndex, 1);
            indexWinSon.splice(i, 1);
            break;
          }
        }
      }, 1000);

      function processDeal (item) {
        $.ajax({
          method: 'get',
          url: '' + '/ts-workflow/workflow/definition/code/' + item.workflowNo,
          success: function (res) {
            if (res.success) {
              if (res.object && res.object.isNormal == 'N') {
                dealWay(item);
              } else if (res.object && res.object.isNormal == 'Y') {
                normalDeal(item, res);
              } else {
                layer.msg(res.message || '获取流程信息失败');
              }
            } else {
              layer.msg(res.message || '发起失败');
            }
          },
        });
      }

      function dealWay (data) {
        var son;
        if (processIndex == 1) {
          if (data.currentStepName == '重新提交') {
            son = common.processDeal.restart(data);
          } else {
            son = common.processDeal.confirm(data);
          }
        } else if (processIndex == 2) {
          son = common.processDeal.dealSee(data);
        } else if (processIndex == 3) {
          if (data.currentStepName == '重新提交') {
            son = common.processDeal.restart(data);
          } else {
            son = common.processDeal.selfSee(data);
          }
        } else if (processIndex == 4) {
          son = common.processDeal.dealSee(data);
        }
        common.openedWindow.push(son);
        indexWinSon.push(son);
      }

      function processRef() {
        //触发我的流程刷新
        Event.create('toDealList').trigger('waitDeal');
        waitDeal();
      }

      function normalDeal (data, res) {
        if (processIndex == 1) {
          if (data.currentStepName == '重新提交') {
            var opt = {
              data: {
                id: data.businessId,
                taskId: data.taskId,
                workId: data.wfInstanceId,
                currentStepName: data.currentStepName,
                currentStepNo: data.currentStepNo,
                workflowNumber: data.workflowNumber,
                wfInstanceId: data.wfInstanceId,
              },
              title: '重新发起',
              ref: processRef,
            };
            $.quoteFun(res.object.examinePageUrl, opt);
            return;
          }
          var opt = {
            data: {
              id: data.businessId,
              taskId: data.taskId,
              workId: data.wfInstanceId,
              wfInstanceId: data.wfInstanceId,
              currentStepName: data.currentStepName,
              currentStepNo: data.currentStepNo,
              workflowNumber: data.workflowNumber,
            },
            rowData: data,
            ref: processRef,
            title: '流程审批',
          };
          $.quoteFun(res.object.examinePageUrl, opt);
        } else if (processIndex == 2) {
          var opt = {
            data: {
              details: 1,
              id: data.businessId,
              taskId: data.taskId,
              workId: data.wfInstanceId,
              currentStepName: data.currentStepName,
              currentStepNo: data.currentStepNo,
              workflowNumber: data.workflowNumber,
              wfInstanceId: data.wfInstanceId,
              status: data.status,
            },
            rowData: data,
            title: '查看详情',
            ref: processRef,
          };
          $.quoteFun(res.object.examinePageUrl, opt);
        } else if (processIndex == 3) {
          var opt = {
            data: {
              details: 1,
              id: data.businessId,
              taskId: data.taskId,
              workId: data.wfInstanceId,
              currentStepName: data.currentStepName,
              currentStepNo: data.currentStepNo,
              workflowNumber: data.workflowNumber,
              wfInstanceId: data.wfInstanceId,
              status: data.status,
            },
            rowData: data,
            title: '查看详情',
            ref: processRef,
          };
          $.quoteFun(res.object.examinePageUrl, opt);
        } else if (processIndex == 4) {
          var opt = {
            data: {
              details: 1,
              id: data.businessId,
              taskId: data.taskId,
              workId: data.wfInstanceId,
              currentStepName: data.currentStepName,
              currentStepNo: data.currentStepNo,
              workflowNumber: data.workflowNumber,
              wfInstanceId: data.wfInstanceId,
              status: data.status,
            },
            rowData: data,
            title: '查看详情',
            ref: processRef,
          };
          $.quoteFun(res.object.examinePageUrl, opt);
        }
      }

      function waitDealNum () {
        $.ajax({
          url: '/ts-workflow/workflow/wfInst/getHomePageWfCount',
          type: 'get',
          async: false,
          success: function (res) {
            if (res.success && res.object) {
              // res.object.toDoNum ? $('.processnum.wait').text(`(${res.object.toDoNum})` || '(0)').removeClass('none') : $('.processnum.wait').addClass('none');
              // res.object.haveDoNum ? $('.processnum.has').text(`(${res.object.haveDoNum})` || '(0)').removeClass('none') : $('.processnum.has').addClass('none');
              // res.object.byMeStartNum ? $('.processnum.mydeal').text(`(${res.object.byMeStartNum})` || '(0)').removeClass('none') : $('.processnum.mydeal').addClass('none');
              // res.object.copyToMeNum ? $('.processnum.copy').text(`(${res.object.copyToMeNum})` || '(0)').removeClass('none') : $('.processnum.copy').addClass('none');
              let toDoNum = res.object.toDoNum > 99 ? '99+' : res.object.toDoNum
              let haveDoNum = res.object.haveDoNum > 99 ? '99+' : res.object.haveDoNum
              let byMeStartNum = res.object.byMeStartNum > 99 ? '99+' : res.object.byMeStartNum
              let copyToMeNum = res.object.copyToMeNum > 99 ? '99+' : res.object.copyToMeNum
              $('#personnelHomePage .processnum.wait').text(toDoNum || '').removeClass('none')
              $('#personnelHomePage .processnum.has').text(haveDoNum || '').removeClass('none')
              $('#personnelHomePage .processnum.mydeal').text(byMeStartNum || '').removeClass('none')
              $('#personnelHomePage .processnum.copy').text(copyToMeNum || '').removeClass('none')
              if (!res.object.toDoNum && !first) {
                first = true
                $('#personnelHomePage .common-processes #processBtn').click()
              }
            }
          },
        });
      }

      function waitDeal () {
        waitDealNum();
        $.ajax({
          type: 'get',
          url: processUrl[processIndex],
          success: function (res) {
            $('#personnelHomePage .waitDeal').html('');
            $('.personnel-home-age-right .common-processes #TabsMorePage').css('bottom', '0px')
            $('.personnel-home-age-right .common-processes #TabsMorePage').text('查看更多')
            $('.personnel-home-age-right .common-processes #TabsMorePage').css('background', '#FAFAFA')
            if (res.rows && res.rows.length > 0) {
              $('.personnel-home-age-right .common-processes #TabsMorePage').show()
              new common.simpleEvent({
                el: '#personnelHomePage .waitDeal',
                list: res.rows,
                classes: 'box',
                event: 'click',
                print: function (item) {
                  var wfStepName = item.currentStepName;
                  var cl = 'start';
                  if (wfStepName != '重新提交') {
                    cl = 'deal';
                  }
                  var step = '';
                  const newYear = new Date().getFullYear()
                  const resYear = item.createDate.slice(0, 4)
                  const showTime = newYear == resYear ? new Date(item.createDate).format('MM-dd hh:mm') : new Date(item.createDate).format('yyyy-MM-dd hh:mm')
                  var sub_tit = '<p>' + item.createUserName + '</p>' + '<p style="padding-left: 8px;">' + showTime + '</p>';
                  return `<div class="waitDeal-item">
                                        <div class="first-title ">
                                            <p class="type-name-style one-line">${item.workflowName}</p>
                                            <div class="second-title">
                                              ${sub_tit}
                                            </div>
                                        </div>
                                    </div>
                            `;
                },
                func: function (item) {
                  processDeal(item);
                },
              });
            } else {
              $("#personnelHomePage .waitDeal").addClass('noData');
              $('.personnel-home-age-right .common-processes #TabsMorePage').hide()
            }
          },
          error: function (res) {
          },
        });
      }

      Event.create('indexProcess').listen('change', function () {
        waitDeal();
      });
    });
  };
});
