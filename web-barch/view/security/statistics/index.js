'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;
            var index = 0;
            $('#securityStatisticsListBox .oa-nav .oa-nav_item').click(function () {
                $('#securityStatisticsListBox .oa-nav .oa-nav_item').removeClass('active');
                $(this).addClass('active');
                index = $(this).attr('data-value');
                $('#securityStatisticsListBox .securityCheckListBox').hide();
                $('#securityStatisticsListBox .securityCheckListBox').eq($(this).index()).show();
                $('#securityStatisticsListBox .queryForm').hide();
                $('#securityStatisticsListBox .queryForm').eq($(this).index()).show();
                refreshTable();
            });
            function refreshTable() {
                if (index == 0) {
                    securityProblemStatisticsTable.refresh();
                } else if (index == 1) {
                    if (leaderCheckListTable) {
                        leaderCheckListTable.refresh();
                    } else {
                        initleaderCheckListTable();
                    }
                }  
            }

             
            // 查询
            $('.securityStartCheckSearch').funs('click', function () {
                refreshTable();
            });

            var securityProblemStatisticsTable;
            var leaderCheckListTable;
            

            initsecurityProblemStatisticsTable();
            //问题列表
            function initsecurityProblemStatisticsTable() {
                draftsecurityCheckTable = new $.trasenTable('securityProblemStatisticsListTable', {
                    url: common.url + '/ts-oa/api/api/checkStatistics/getProblemList',
                    pager: '#securityProblemStatisticsListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 0,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: false,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '交办科室',
                            sortable: false,
                            name: 'routeName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '检查内容',
                            sortable: false,
                            name: 'checkAddress',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '不达标说明',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '是否已整改',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '整改完成日期',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn editEvaBaseLinkMan"    title="编辑" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                                btns += '<button class="layui-btn "   title="删除" row-id="' + options.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var opt = $('#securityProblemStatisticsForm').serializeArray();
                        var data = {};
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        data.status = 0;
                        data.checkRoutes = arrToString(securityCheckRouteChoose.getSelData(), 'id');
                        return data;
                    },
                });
            }
            //待确认
            function initleaderCheckListTable() {
                confirmedsecurityCheckTable = new $.trasenTable('leaderCheckListTable', {
                    url: common.url + '/ts-oa/api/checkStatistics/getLeaderCheckList',
                    pager: '#leaderCheckListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 1,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: false,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '检查路线',
                            sortable: false,
                            name: 'routeName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '检查地点',
                            sortable: false,
                            name: 'checkAddress',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '检查人',
                            sortable: false,
                            name: 'checkUserName',
                            width: 120,
                            align: 'center',
                        },

                        {
                            label: '核查人',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '检查结果',
                            sortable: false,
                            name: 'unqualifiedCount',
                            width: 120,
                            align: 'center',
                            formatter: function (cell, opt, row) {
                                var html = '';
                                if (cell > 0) {
                                    html = cell + '项不达标';
                                } else {
                                    html = '全部达标';
                                }
                                return html;
                            },
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn confirmCheck"   title="确认" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>确认 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var opt = $('#securityConfirmedForm').serializeArray();
                        var data = {};
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        return data;
                    },
                });
            }

          
          
            form.render('select');

            
            function getRowData(id) {
                var data;
                if (index == 0) {
                    data = draftsecurityCheckTable.getSourceRowData(id);
                } else if (index == 1) {
                    data = confirmedsecurityCheckTable.getSourceRowData(id);
                } else if (index == 2) {
                    data = failsecurityCheckTable.getSourceRowData(id);
                } else if (index == 3) {
                    data = rectificationsecurityCheckTable.getSourceRowData(id);
                } else if (index == 4) {
                    data = completedSecuritysecurityCheckTable.getSourceRowData(id);
                }
                return data;
            }

            //导出
            $('.expotCheckBut').funs('click', function () {
                var queryData;
                if (index == 0) {
                    queryData = securityProblemStatisticsTable.oTable.getGridParam('postData'); securityProblemStatisticsTable.refresh();
                } else if (index == 1) {
                    queryData = leaderCheckListTable.oTable.getGridParam('postData'); leaderCheckListTable.refresh();
                } e
                var url = common.url + '/ts-oa/api/checkStatistics/export?';
                var exportParam = '';
                for (var key in queryData) {
                    exportParam += key + '=' + queryData[key] + '&';
                }
                location.href = url + exportParam;
            });
        });
    };
});
