"use strict";

define(function (require, exports, module) {
  var init = function () {
    return perform();
  };
  module.exports = {
    init: init,
  };
  var perform = function () {
    layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
      var form = layui.form,
        layer = layui.layer,
        layedit = layui.layedit,
        upload = layui.upload,
        laydate = layui.laydate,
        trasen = layui.trasen,
        util = layui.util;

      /*	WordPaster.getInstance({
				    PostUrl:common.url + '/ts-document/attachment/fileUpload?module=information&fillupf=2',
				    Cookie:document.cookie
				}).Load();//加载控件
*/
      //监听tab点击事件
      $('#messageRelease .messageReleaseTop .oa-nav_item')
        .off('click')
        .on('click', function () {
          $(this).addClass('active').siblings().removeClass('active');
          var index = $(this).attr('data-refTable');
          if (index == 1) {
            $('#messageReleaseTable').setGridParam().hideCol('createDeptName');
            $('#messageReleaseTable').setGridParam().hideCol('createUserName');
            $('#messageReleaseTable').setGridWidth($(window).width() - 246);
          } else {
            $('#messageReleaseTable').setGridParam().showCol('createDeptName');
            $('#messageReleaseTable').setGridParam().showCol('createUserName');
            $('#messageReleaseTable').setGridWidth($(window).width() - 246);
          }
          if (index == 1 || index == 3) {
            $('#informationStatusDiv').show();
          } else {
            $('#informationStatusDiv').hide();
          }
          if (index == 3) {
            $('#messageRelease .oa-more-btn').removeClass('none');
          } else {
            $('#messageRelease .oa-more-btn').addClass('none');
          }
          $('#messageRelease #seachChannelId').val('');
          initTree('#messageReleaseModTree', index);
          $('#messageReleaseIndex').val(index);
          refresh();
        });

      //初始化table表格
      var trasenTable = new $.trasenTable('messageReleaseTable', {
        url: common.url + '/ts-information/information/list',
        pager: '#messageReleasePage',
        mtype: 'get',
        sortname: 'releaseDate',
        sortorder: 'DESC',
        rowNum: 30,
        shrinkToFit: true,
        rownumbers: false,
        rowList: [15, 30, 50, 100],
        multiselect: true,
        colModel: [
          {
            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
            name: '',
            sortable: false,
            width: 40,
            editable: false,
            title: false,
            align: 'center',
            classes: 'visible jqgrid-rownum ui-state-default',
            formatter: function (cell, options, rowObject) {
              var html =
                '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
              var btns = '';
              if (1 == rowObject.index) {
                if (
                  rowObject.informationStatus == 4 ||
                  rowObject.informationStatus == 2 ||
                  rowObject.informationStatus == 5
                ) {
                  btns +=
                    '<button class="layui-btn " id="editInfoBtn" title="编辑" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑</button>';
                }
                if (
                  rowObject.informationStatus == 2 ||
                  rowObject.informationStatus == 5 ||
                  rowObject.informationStatus == 4
                ) {
                  btns +=
                    '<button class="layui-btn " id="deleteInfoBtn"  title="删除" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-trash deal_icon"  aria-hidden="true"></i>删除</button>';
                }
                if (rowObject.informationStatus == 0) {
                  btns +=
                    '<button class="layui-btn " id="cancelBtn"  title="撤销" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-trash deal_icon"  aria-hidden="true"></i>撤销</button>';
                }
                btns +=
                  "<button class='layui-btn  infoViewBtn'   row-workid='" +
                  rowObject.workflowId +
                  "' row-id='" +
                  rowObject.id +
                  "' ><i class='fa fa-eye deal_icon'  aria-hidden='true'></i>查看</button>";
              }
              if (2 == rowObject.index) {
                if (null != rowObject.taskId) {
                  btns +=
                    "<button class='layui-btn  ' id='infoExamineBtn'  row-workid='" +
                    rowObject.workflowId +
                    "' row-taskid='" +
                    rowObject.taskId +
                    "'  row-id='" +
                    rowObject.id +
                    "' ><i class='oaicon oa-icon-shenpi deal_icon'  aria-hidden='true'></i>审批</button>";
                } else {
                  btns +=
                    "<button class='layui-btn  infoViewBtn'   row-workid='" +
                    rowObject.workflowId +
                    "' row-taskid='" +
                    rowObject.taskId +
                    "' row-id='" +
                    rowObject.id +
                    "' ><i class='fa fa-eye deal_icon'  aria-hidden='true'></i>查看</button>";
                }
              }
              if (3 == rowObject.index) {
                btns +=
                  '<button class="layui-btn " id="editInfoBtn" title="编辑" row-id="' +
                  options.rowId +
                  '"><i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑</button>';
                btns +=
                  '<button class="layui-btn " id="deleteInfoBtn"  title="删除" row-id="' +
                  options.rowId +
                  '"><i class="fa fa-trash deal_icon"  aria-hidden="true"></i>删除</button>';
                if (1 == rowObject.informationStatus) {
                  btns +=
                    '<button class="layui-btn " id="cancelRelease" title="取消发布" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-flag-o deal_icon" aria-hidden="true"></i>取消发布</button>';
                }
                if (3 == rowObject.informationStatus) {
                  btns +=
                    '<button class="layui-btn " id="recoveryRelease" title="恢复发布" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-flag deal_icon" aria-hidden="true"></i>恢复发布</button>';
                }
                if (1 == rowObject.titleColor) {
                  btns +=
                    '<button class="layui-btn " id="cancelTitleColorBtn" title="取消标红" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-circle-o deal_icon" aria-hidden="true"></i>取消标红</button>';
                } else {
                  btns +=
                    '<button class="layui-btn " id="titleColorBtn" title="标红" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-circle deal_icon" aria-hidden="true"></i>标红</button>';
                }
                if (1 == rowObject.showSign) {
                  btns +=
                    '<button class="layui-btn " id="cancelShowSignBtn" title="取消置顶" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-thumbs-down deal_icon" aria-hidden="true"></i>取消置顶</button>';
                } else {
                  btns +=
                    '<button class="layui-btn " id="showSignBtn" title="置顶" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-thumb-tack deal_icon" aria-hidden="true"></i>置顶</button>';
                }
                if (1 == rowObject.isMarrow) {
                  btns +=
                    '<button class="layui-btn " id="cancelIsMarrowBtn" title="取消加精" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-bell-slash deal_icon" aria-hidden="true"></i>取消加精</button>';
                } else {
                  btns +=
                    '<button class="layui-btn " id="isMarrowBtn" title="加精" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-bell deal_icon" aria-hidden="true"></i>加精</button>';
                }
                if (1 == rowObject.isBanner) {
                  btns +=
                    '<button class="layui-btn " id="cancelIsBannerBtn" title="取消轮播图" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-pause-circle deal_icon" aria-hidden="true"></i>取消轮播图</button>';
                } else {
                  btns +=
                    '<button class="layui-btn " id="isBannerBtn" title="轮播图" infoPhotoUrl="' +
                    rowObject.infoPhotoUrl +
                    '" row-id="' +
                    options.rowId +
                    '"><i class="fa fa-play-circle-o deal_icon" aria-hidden="true"></i>轮播图</button>';
                }
              }
              html += btns + '</div></div>';
              return html;
            },
          },
          {
            label: '标题',
            sortable: false,
            name: 'informationTitle',
            width: 280,
            align: 'left',
            formatter: function (cellvalue, options, rowObject) {
              var tdStr = '';
              tdStr += "<dl class='info-item'>";
              /*if (null != rowObject.infoPhotoUrl && '' != rowObject.infoPhotoUrl) {
									tdStr += '<dt>'
									tdStr += "<img style='width:50px;height:50px' src='" + common.url + "/ts-document/attachment/" + rowObject.infoPhotoUrl + "'>";
									tdStr += '</dt>'
								}*/
              tdStr += "<dd class='info-item-content'>";
              tdStr +=
                "<p class='info-item-intro' style=';margin-top:2px'   row-workid='" +
                rowObject.workflowId +
                "' row-id='" +
                rowObject.id +
                "' row-taskid='" +
                rowObject.taskId +
                "'>";
              /*	if (1 == rowObject.titleColor) {
									tdStr += "<span class='message_important'>重</span>";
								}*/
              if (1 == rowObject.showSign) {
                tdStr += "<span class='message_top'>顶</span>";
              }
              if (1 == rowObject.isMarrow) {
                tdStr += "<span  class='message_vip'>精</span>";
              }
              if (1 == rowObject.titleColor) {
                tdStr +=
                  "<span style='color:#D50D0D;'>" + cellvalue + '</span>';
              } else {
                tdStr += "<span class='dealLink'>" + cellvalue + '</span>';
              }
              tdStr += '</p>';
              /*	tdStr += "<p style='color:#999;margin-top:5px'>"
								tdStr += "<span style='margin-right:20px;'>图:" + (rowObject.pictureAuthor || '匿名') + "</span>";
								tdStr += "<span>文:" + (rowObject.informationAuthor || '匿名') + "</span>";
								tdStr += "</p>";*/
              tdStr += '</dd>';

              tdStr += '</dl>';
              return tdStr;
              if (2 == rowObject.index) {
                if (null != rowObject.taskId) {
                  tdStr +=
                    "<span style='color:#F59A23' class='my-approval-status'>待我办理<span>";
                } else {
                  tdStr +=
                    "<span style='color:#ccc' class='my-approval-status'>我已办理<span>";
                }
              }

              tdStr += '</div>';
              return tdStr;
            },
          },
          {
            label: '栏目名称',
            sortable: false,
            align: 'left',
            name: 'channelName',
            width: 90,
          },
          {
            label: '发布单位',
            sortable: false,
            align: 'left',
            name: 'createDeptName',
            width: 100,
            hidden: true,
          },
          {
            label: '发布人',
            align: 'left',
            sortable: false,
            name: 'createUserName',
            width: 60,
            hidden: true,
          },
          {
            label: '发布时间',
            sortable: true,
            align: 'left',
            index: 'RELEASE_DATE',
            name: 'releaseDate',
            width: 120,
            formatter: function (cellvalue, options, rowObject) {
              if (cellvalue == '' || cellvalue == null) {
                return '待发布';
              } else {
                return util.toDateString(cellvalue, 'yyyy-MM-dd HH:mm');
              }
            },
          },
          {
            label: '状态',
            sortable: false,
            align: 'left',
            name: 'informationStatus',
            width: 60,
            align: 'center',
            formatter: function (cellvalue, options, rowObject) {
              if (rowObject.timing != null) {
                var today = new Date();
                var timing = new Date(
                  Date.parse(rowObject.timing.replace('-', '/'))
                );
                if (timing > today) {
                  return "<span style='color:#bd2b1c'>预发布</span>";
                }
              }
              if (rowObject.validendTime != null) {
                var today = new Date();
                var validendTime = new Date(
                  Date.parse(rowObject.validendTime.replace('-', '/'))
                );
                if (today > validendTime) {
                  return "<span style='color:#5d1811'>已过期</span>";
                }
              }

              if (cellvalue == 0)
                return (
                  "<span  class='infoViewBtn' row-workid='" +
                  rowObject.workflowId +
                  "' row-id='" +
                  rowObject.id +
                  "' style='color:#F59A23;cursor: pointer'>待审批</span>"
                );
              if (cellvalue == 1)
                return "<span style='color:#70B603'>已发布</span>";
              if (cellvalue == 2)
                return (
                  "<span class='infoViewBtn' row-workid='" +
                  rowObject.workflowId +
                  "' row-id='" +
                  rowObject.id +
                  "' style='color:red;cursor: pointer'>已驳回</span>"
                );
              if (cellvalue == 3)
                return "<span style='color:#a7aaab'>取消发布</span>";
              if (cellvalue == 4)
                return "<span style='color:#a7aaab'>草稿</span>";
              if (cellvalue == 5)
                return "<span style='color:#a7aaab'>已撤销</span>";
            },
          },
          {
            label: '附件',
            sortable: false,
            name: 'fileNumber',
            align: 'center',
            width: 60,
          },
          {
            label: '阅读数',
            align: 'center',
            sortable: false,
            name: 'informationKits',
            width: 60,
            formatter: function (cellvalue, options, rowObject) {
              return "<span style='color:#5260ff'>" + cellvalue + '</span>';
            },
          },
          {
            label: 'id',
            sortable: false,
            name: 'id',
            hidden: true,
          },
        ],
        buidQueryParams: function () {
          var search = $('#messageReleaseSearchForm').serializeArray();
          var opt = [];
          var data = {};
          for (var i in search) {
            opt.push(search[i]);
          }
          for (var i in opt) {
            data[opt[i].name] = opt[i].value;
          }
          return data;
        },
        onCellSelect: function (row, iCol, cellcontent, e) {
          if (iCol >= 1) {
            $('#messageReleaseTable').jqGrid('setSelection', row, false);
          }
          if (iCol == 2) {
            var rowData = $('#messageReleaseTable').jqGrid('getRowData', row);
            rowData.reader = 'yes';
            $.quoteFun('/information/messageRelease/msgDetail', {
              trasen: trasenTable,
              data: rowData,
              title: '查看详情',
              ref: refresh,
            });
          }
        },
        /*ondblClickRow:function(row){
					var rowData = $('#messageReleaseTable').jqGrid('getRowData',row);
		            $.quoteFun('/information/messageRelease/msgDetail', {
		                trasen: trasenTable,
		                data: rowData,
		                title: '查看详情',
		                ref: refresh
		            });
				}*/
      });

      form.render();

      //时间控件
      laydate.render({
        elem: '#messageReleaseDate',
        range: '~',
        trigger: 'click',
        showBottom: true,
        done: function (value, date, endDate) {
          var dateArr = value.split(' ~ ');
          $('#startMessageReleaseDate').val(dateArr[0]);
          $('#endMessageReleaseDate').val(dateArr[1]);
        },
      });

      // 新增
      $('#addMessageRelease').funs('click', function () {
        $.quoteFun('/information/messageRelease/add', {
          trasen: trasenTable,
          title: '新增消息',
          ref: refresh,
        });
      });
      var setting = {
        data: {
          simpleData: {
            enable: false,
          },
        },
        callback: {
          onClick: onClick,
          onNodeCreated: function (e, id, node) {
            var node = node;
            var treeObj = $.fn.zTree.getZTreeObj('messageReleaseModTree');
            var nodes = treeObj.getNodes();
            treeObj.updateNode(node);
          },
        },
        view: {
          dblClickExpand: true,
          showTitle: true,
          showLine: false,
        },
      };

      function onClick(event, treeId, treeNode) {
        $('#messageRelease #seachChannelId').val(treeNode.id);
        trasenTable.refresh();
      }
      initTree('#messageReleaseModTree', 1);

      function initTree(el, index) {
        $.ajax({
          type: 'get',
          url:
            '/ts-information/information/selectInfoCountByChannel?index=' +
            index,
          success: function (res) {
            if (res.success && res.object) {
              var infoChannelListHtml =
                '<li class="infoChannelItem infoChannelItemPress">全部</li>';
              for (var i = 0; i < res.object.length; i++) {
                infoChannelListHtml +=
                  '<li  class="infoChannelItem" data-id="' +
                  res.object[i].channelId +
                  '">' +
                  res.object[i].channelName +
                  '</li>';
              }

              $(el).html(infoChannelListHtml);

              /*var nodes = []
								var all = {
									name: '全部',
									id: '',
									open: true,
									children: []
								}
								var totalNum = 0;
								for (var i = 0; i < res.object.length; i++) {
									totalNum += parseInt(res.object[i].infoNumber)
									all.children.push({
										name: res.object[i].channelName + "(" + res.object[i].infoNumber + ")",
										id: res.object[i].channelId
									})
								}
								all.name = "全部" + "(" + totalNum + ")"
								nodes.push(all)
								var zNodes = nodes;
								$.fn.zTree.init($(el), setting, zNodes);*/
            }
          },
        });
      }

      $('#messageRelease')
        .off('click', '.infoChannelItem')
        .on('click', '.infoChannelItem', function () {
          $(this)
            .addClass('infoChannelItemPress')
            .siblings('.infoChannelItem')
            .removeClass('infoChannelItemPress');
          $('#seachChannelId').val($(this).attr('data-id'));
          trasenTable.refresh();
        });

      //查询待审批数量
      initApprovalDataCount();

      function initApprovalDataCount() {
        $.ajax({
          type: 'get',
          url: common.url + '/ts-information/information/getApprovalDataCount',
          success: function (res) {
            if (res.success) {
              $('#approvalDataCount').removeClass('none').html(res.object);
            } else {
              layer.msg(res.message);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          },
        });
      }

      //查看详情
      $('body')
        .off('click', '.infoViewBtn')
        .on('click', '.infoViewBtn', function () {
          var workid = $(this).attr('row-workid');
          var taskid = $(this).attr('row-taskid');
          var d = {
            id: $(this).attr('row-id'),
            details: 1,
            taskId: taskid,
            workId: workid,
          };
          var url = '/information/messageRelease/msgDetail';
          /*if ('null' != workid && '' != workid && undefined != workid) {
						url = "/information/messageRelease/Review";
					} else {
						url = "/information/messageRelease/msgDetail";
					}*/
          $.quoteFun(url, {
            trasen: trasenTable,
            data: d,
            title: '查看详情',
            ref: refresh,
          });
        });
      //审批
      $('body')
        .off('click', '#infoExamineBtn')
        .on('click', '#infoExamineBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            taskId: $(this).attr('row-taskid'),
            workId: $(this).attr('row-workid'),
          };
          $.quoteFun('/information/messageRelease/Review', {
            trasen: trasenTable,
            data: d,
            title: '流程审批',
            ref: refresh,
          });
        });

      //编辑
      $('body')
        .off('click', '#editInfoBtn')
        .on('click', '#editInfoBtn', function () {
          //var data = trasenTable.getSourceRowData(); //  选中行数据
          var data = {
            id: $(this).attr('row-id'),
          };
          $.quoteFun('/information/messageRelease/add', {
            trasen: trasenTable,
            title: '编辑消息',
            data: data,
            ref: refresh,
          });
        });
      //删除
      $('body')
        .off('click', '#deleteInfoBtn')
        .on('click', '#deleteInfoBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
          };
          layer.confirm(
            '删除后数据将无法恢复，确定要删除吗？',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              postInformationData('/ts-information/information/deletedById', d);
            }
          );
        });
      //撤销
      $('body')
        .off('click', '#cancelBtn')
        .on('click', '#cancelBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
          };
          layer.confirm(
            '确定要撤销吗？',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              postInformationData('/ts-information/information/cancelById', d);
            }
          );
        });

      //批量删除
      $('body')
        .off('click', '#deleteAllBtn')
        .on('click', '#deleteAllBtn', function () {
          var ids = $('#messageReleaseTable').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (ids.length <= 0) {
            layer.msg('请选择需要操作的数据！');
            return;
          }
          var d = {
            id: ids.join(','),
          };
          layer.confirm(
            '删除后数据将无法恢复，确定要删除吗？',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              postInformationData('/ts-information/information/deletedById', d);
            }
          );
        });

      //取消发布
      $('body')
        .off('click', '#cancelRelease')
        .on('click', '#cancelRelease', function () {
          var d = {
            id: $(this).attr('row-id'),
            informationStatus: 3,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //恢复发布
      $('body')
        .off('click', '#recoveryRelease')
        .on('click', '#recoveryRelease', function () {
          var d = {
            id: $(this).attr('row-id'),
            informationStatus: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //标红
      $('body')
        .off('click', '#titleColorBtn')
        .on('click', '#titleColorBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            titleColor: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //批量标红
      $('body')
        .off('click', '#titleColorAllBtn')
        .on('click', '#titleColorAllBtn', function () {
          var ids = $('#messageReleaseTable').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (ids.length <= 0) {
            layer.msg('请选择需要操作的数据！');
            return;
          }
          var d = {
            id: ids.join(','),
            titleColor: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //取消标红
      $('body')
        .off('click', '#cancelTitleColorBtn')
        .on('click', '#cancelTitleColorBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            titleColor: 0,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //置顶
      $('body')
        .off('click', '#showSignBtn')
        .on('click', '#showSignBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            showSign: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //取消置顶
      $('body')
        .off('click', '#cancelShowSignBtn')
        .on('click', '#cancelShowSignBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            showSign: 0,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //加精
      $('body')
        .off('click', '#isMarrowBtn')
        .on('click', '#isMarrowBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            isMarrow: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //批量加精
      $('body')
        .off('click', '#marrowAllBtn')
        .on('click', '#marrowAllBtn', function () {
          var ids = $('#messageReleaseTable').jqGrid(
            'getGridParam',
            'selarrrow'
          );
          if (ids.length <= 0) {
            layer.msg('请选择需要操作的数据！');
            return;
          }
          var d = {
            id: ids.join(','),
            isMarrow: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //取消加精
      $('body')
        .off('click', '#cancelIsMarrowBtn')
        .on('click', '#cancelIsMarrowBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            isMarrow: 0,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //广告
      $('body')
        .off('click', '#isBannerBtn')
        .on('click', '#isBannerBtn', function () {
          if (
            null == $(this).attr('infoPhotoUrl') ||
            '' == $(this).attr('infoPhotoUrl')
          ) {
            layer.msg('此数据没有主图，请先维护主图数据');
            return false;
          }
          var d = {
            id: $(this).attr('row-id'),
            isBanner: 1,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });
      //取消广告
      $('body')
        .off('click', '#cancelIsBannerBtn')
        .on('click', '#cancelIsBannerBtn', function () {
          var d = {
            id: $(this).attr('row-id'),
            isBanner: 0,
          };
          postInformationData(
            '/ts-information/information/operateInformation',
            d
          );
        });

      //ajax请求
      function postInformationData(url, d) {
        $.ajax({
          type: 'post',
          url: common.url + url,
          contentType: 'application/json;charset=UTF-8',
          data: JSON.stringify(d),
          success: function (res) {
            $.closeloadings();
            if (res.success) {
              layer.closeAll();
              layer.msg(res.object);
              refresh();
            } else {
              layer.closeAll();
              layer.msg(res.message);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          },
        });
      }

      //刷新表格
      function refresh() {
        trasenTable.refresh();
        initApprovalDataCount();
      }

      $('#messageReleaseSearch').funs('click', function () {
        refresh();
      });

      $('#messageReleaseReset').funs('click', function () {
        $('#startMessageReleaseDate').val('');
        $('#endMessageReleaseDate').val('');
        $('#messageReleaseDate').val('');
        $('#informationTitle').val('');
        $('#informationStatus').val('');
        form.render();
        refresh();
      });
    });
  };
});
