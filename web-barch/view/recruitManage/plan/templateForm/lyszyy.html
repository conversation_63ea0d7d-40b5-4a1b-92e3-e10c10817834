<style>
    #ExaminationDialogBox * {
        box-sizing: border-box;
    }

    #ExaminationDialogBox {
        width: 100%;
        height: 100%;
        display: flex;
        background: #F4F4F4;
        padding: 25px 88px;
        box-sizing: border-box;
    }

    #ExaminationDialogBox .left-Examination-form-box {
        width: 70%;
        height: 100%;
        padding: 16px;
        background: #fff;
        margin-right: 8px;
    }

    #ExaminationDialogBox .left-Examination-form-box h1 {
        text-align: center;
        margin-bottom: 8px;
    }

    #ExaminationDialogBox .right-operation-box {
        width: 30%;
        height: 100%;
        padding: 24px 32px 0;
        background: #fff;
    }

    #ExaminationDialogBox .left-Examination-form-box #ExaminationTable {
        width: 100%;
        border-right: 1px solid #333;
        border-bottom: 1px solid #333;
    }

    #ExaminationDialogBox .left-Examination-form-box #ExaminationTable tr {
        height: 40px;
    }

    #ExaminationDialogBox .left-Examination-form-box #ExaminationTable td {
        text-align: center;
        border-left: 1px solid #333;
        border-top: 1px solid #333;
    }

    #ExaminationDialogBox .right-operation-box .examination-post-name {
        font-size: 18px;
        font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
        font-weight: bold;
        color: #333333;
        line-height: 24px;
        margin-bottom: 16px;
    }

    #ExaminationDialogBox .right-operation-box .examination-post-info {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #EC7B25;
        line-height: 19px;
        display: flex;
        flex-direction: column;
    }

    #ExaminationDialogBox .right-operation-box .examination-reason-box {
        display: flex;
        flex-wrap: wrap;
        margin: 8px 0 0 0;
    }

    #ExaminationDialogBox .right-operation-box .examination-reason-box li {
        padding: 5px 8px;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
        background: #FAFAFA;
        border-radius: 2px;
        border: 1px solid #E7EBF0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        margin-bottom: 8px;
        cursor: pointer;
    }

    #ExaminationDialogBox .right-operation-box .examination-reason-box li.active {
        background: rgba(82, 96, 255, 0.2);
    }

    #ExaminationDialogBox .examination-checkbox-box {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: #999999;
        line-height: 16px;
    }

    #ExaminationDialogBox .examination-checkbox-box input {
        margin: 8px 5px 8px 0;
    }

    #ExaminationDialogBox #ExaminationFileUl li {
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 19px;
        margin: 5px 0;
    }

    #ExaminationDialogBox #ExaminationFileUl li .viewerImg {
        font-size: 12px;
        font-family: MicrosoftYaHei;
        color: #5260FF;
        margin: 0 16px 0 40px;
        line-height: 16px;
    }

    #ExaminationDialogBox .pending_review_box {
        margin: 16px 0;
        font-size: 16px;
        font-family: MicrosoftYaHei;
        color: #333333;
        line-height: 21px;
    }

    #ExaminationDialogBox .pending_review_ul {
        display: flex;
        flex-direction: column;
        height: 450px;
        overflow-y: auto;
    }

    #ExaminationDialogBox .pending_review_ul li {
        width: 100%;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        padding-left: 8px ;
        font-size: 14px;
        font-family: MicrosoftYaHei;
        color: #666666;
        margin-bottom: 8px;
    }

    #ExaminationDialogBox .button_broder {
        border-bottom: 1px solid #E4E4E4;
        padding-bottom: 24px;
    }
</style>

<div id="ExaminationDialogBox">
    <div class="left-Examination-form-box">
        <h1></h1>
        <table id="ExaminationTable">
        </table>
        <ul id="ExaminationFileUl"></ul>
    </div>
    <div class="right-operation-box">
        <p class="examination-post-name"></p>
        <div class="examination-post-info">
            <span>专业方向：<span class="info-direction"></span></span>
            <span>其他要求：<span class="info-requirement"></span></span>
        </div>
        <ul class="examination-reason-box">
        </ul>
        <textarea id="ExaminationReasonsForFailure" style="width: 100%;" placeholder="请输入不通过原因"
            class="layui-textarea" />

        <label class="examination-checkbox-box">
            <input id="OpenAutoExamination" type="checkbox" autocomplete="off" value="1" title="处理后自动打开下一个待审查人员"
                lay-skin="primary" checked />
            处理后自动打开下一个待审查人员
        </label>
        <div class="button_broder">
            <button type="button" class="layui-btn" id="RecruitManageAdoptBtn">通过</button>
            <button type="button" class="layui-btn layui-btn-primary" id="RecruitManageNoAdoptBtn">不通过</button>
        </div>

        <p class="pending_review_box"></p>
        <ul class="pending_review_ul">
            
        </ul>
    </div>
</div>