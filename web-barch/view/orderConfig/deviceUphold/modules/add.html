<style>
    #deviceUpholdAdd .flex {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
  }
  #deviceUpholdAdd .device-uphold-info-box {
    width: 220px;
  }
  #deviceUpholdAdd .label-text{
    width: 63px;
    line-height: 30px;
    margin-right: 4px;
  }
  #deviceUpholdAdd .device-uphold-add-wrap{
    margin-top: 10px;
    justify-content: center;
  }
  #deviceUpholdAdd .order-save-btn {
    width: 60px;
    height: 30px;
    background: #5260FF;
    border-radius: 2px;
  }

  #deviceUpholdAdd .order-other-btn {
    width: 60px;
    height: 30px;
    background: #FFFFFF;
    border-radius: 2px;
    border: 1px solid #5260FF;
    color: #5260FF;
  }
</style>

<div id="deviceUpholdAdd">
  <form class="layui-form">

    <!-- <div class="layui-form-item device-uphold-add-wrap flex">
      <label class="label-text"><span class="required">*</span>设备ID</label>
      <div class="device-uphold-info-box">
        <input type="text" name="pkFaultEquipmentId" placeholder="请输入设备ID" class="layui-input" lay-verify="required" />
      </div>
    </div> -->

    <div class="layui-form-item device-uphold-add-wrap flex">
      <label class="label-text"><span class="required">*</span>设备编码</label>
      <div class="device-uphold-info-box">
        <input type="text" name="equipmentNumber" placeholder="请输入设备编码" class="layui-input" lay-verify="required" maxlength="50"/>
      </div>
    </div>

    <div class="layui-form-item device-uphold-add-wrap flex">
      <label class="label-text"><span class="required">*</span>设备名称</label>
      <div class="device-uphold-info-box">
        <textarea type="text" name="equipmentName" placeholder="请输入设备名称" class="layui-textarea" lay-verify="required" maxlength="30" 
          style="resize: none;min-height: auto; height: 70px;"></textarea>
      </div>
    </div>

    <div class="layui-form-item device-uphold-add-wrap flex">
      <label class="label-text"><span class="required">*</span>设备描述</label>
      <div class="device-uphold-info-box">
        <textarea type="text" name="equipmentRemark" placeholder="请输入设备描述" class="layui-textarea" lay-verify="required" maxlength="30" 
          style="resize: none;min-height: auto; height: 70px;"></textarea>
      </div>
    </div>

    <div class="layui-form-item device-uphold-add-wrap flex">
      <label class="label-text"><span class="required">*</span>所属科室</label>
      <div class="device-uphold-info-box">
        <!-- <input type="text" name="fkDeptName" placeholder="请输入所属科室" class="layui-input" lay-verify="required" /> -->
        <input type="text" name="fkDeptName" placeholder="请输入所属科室" class="layui-input" id="addEquipName" lay-verify="required" autocomplete="off"/>
        <input type="hidden" name="fkDeptId" class="layui-input" id="addEquipId" />
      </div>
    </div>
    <!-- 待完成 -->
    <!-- <div class="layui-form-item device-uphold-add-wrap flex">
        <label class="label-text">设备图形码</label>
    </div> -->

    <div class="layui-form-item flex" style="justify-content: center; padding-top: 8px; border-top: 1px solid #EEE;">
      <button type="button" class="order-save-btn layui-btn" lay-submit="" lay-filter="deviceUpholdConfirm">保存</button>
      <button type="button" class="layui-btn order-other-btn" id="deviceUpholdCancel">取消</button>
    </div>

  </form>
</div>