<style>
  #myHandleOrder {
    width: 100%;
    height: 100%;
  }

  #myHandleOrder .flex {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
  }

  #myHandleOrder .my-work-order-box {
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px #e6eaf0;
    border-radius: 4px;
  }

  #myHandleOrder .my-work-order-font {
    font-family: PingFangSC-Regular, PingFang SC;
  }

  #myHandleOrder .my-work-order-container {
    padding: 8px;
    height: 100%;
  }

  #myHandleOrder .my-work-order-options {
    height: 36px;
    background: #e8ecf2;
    border-radius: 4px;
    padding-left: 1px;
    padding-right: 1px;
  }

  #myHandleOrder .myHandleOrder-option-active-text {
    color: #5260ff !important;
    background: #ffffff;
  }

  #myHandleOrder .myHandleOrder-options-item-text {
    font-weight: 400;
    color: #333333;
    width: 100px;
    height: 32px;
    margin: 2px 1px 2px 1px;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
  }

  #myHandleOrder .myHandleOrder-options-item-text:hover {
    color: #5260ff;
  }

  #myHandleOrder .trasen-con-box {
    margin-top: 99px;
    margin-right: 8px;
  }

  #myHandleOrder .mymorder-form-label {
    line-height: 30px;
    width: 80px;
    margin-right: 4px;
    text-align: right;
  }

  #myHandleOrder .search-bar {
    margin-top: 8px;
  }

  #myHandleOrder .keyword-input {
    margin-left: 30px;
  }

  #myHandleOrder .order-confirm-btn {
    width: 68px;
    height: 30px;
    background: #5260ff;
    border-radius: 4px;
  }
  #myHandleOrder .search-btn-wrap {
    margin-left: 30px;
  }
  #myHandleOrder .order-create-btn {
    width: 74px;
    height: 30px;
    border-radius: 4px;
    background-color: #f45555;
    border: 1px #f45555 solid;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
    cursor: pointer;
  }
  #myHandleOrder .more-search-box {
    margin-right: 15px;
  }
  #myHandleOrder .tra-lay-select-title.pubSelectPesonBox,
  #myHandleOrder #myWorkSheet1-fkUserName input {
    font-size: inherit !important;
  }
  .tra-lay-select-dl dl dd,
  .tra-lay-select-dl dl dt {
    font-size: inherit;
  }
  #myHandleOrder .search-bar .layui-col-md6 {
    padding: 5px 0;
  }
  #myHandleOrder .flex-center {
    align-items: center;
    justify-content: center;
  }
  #myHandleOrder .flex-between {
    justify-content: space-between;
  }
  #myHandleOrder .emergencyIcon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: linear-gradient(45deg, red, #f27940);
    border-radius: 50%;
    align-items: center;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    font-weight: 500;
    color: #fff;
    margin-right: 5px;
    cursor: default;
  }
  #myHandleOrder .rebackIcon {
    background: #33333380;
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    line-height: 20px;
    text-align: center;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    margin-right: 5px;
    cursor: default;
  }
  #myHandleOrder [name='export'] {
    height: 30px;
    color: #5260ff;
    border-radius: 2px;
    border: 1px solid #5260ff;
    background-color: #fff;
  }
  #myHandleOrder [name='myorderCreaderOrderBtn'] {
    height: 30px;
    background-color: #5260ff;
    border-radius: 2px;
    border: 1px solid #5260ff;
    color: #fff;
  }
  #myHandleOrder .table-cell-action-span {
    cursor: pointer;
  }
  #myHandleOrder .table-cell-action-span:hover span {
    text-decoration: underline;
    color: #4395ff;
  }
  #myHandleOrder .search-time-range {
    white-space: nowrap;
  }
  #myHandleOrder .oa-nav-search [render='time'] {
    padding-right: 30px;
    background: url(../../static/img/other/icon_riqi.png) no-repeat right 8px
      top 6px;
    background-size: 16px;
  }
  #myHandleOrder .oa-nav-search .layui-edge,
  #myHandleOrder .oa-nav-search .tra-lay-select-title i.icon {
    border: none;
    display: inline-block;
    background: url(../../static/img/other/icon_xiala.png) no-repeat;
    background-size: 16px;
    height: 16px;
    width: 16px;
    top: 8px;
  }
  #myHandleOrder .oa-nav-search .layui-edge {
    top: 10px;
  }
  #myHandleOrder .oa-nav-search .layui-form-selected .layui-edge {
    top: 15px;
  }
  #myHandleOrder .ui-jqgrid-view .ui-jqgrid-htable th * {
    text-align: center;
  }
  #myHandleOrder .shell-search-box {
    margin-right: 8px;
  }
  #myHandleOrder .table-action{
    font-size: 12px;
    transform: scale(0.9);
    cursor: pointer;
    color: #4395ff;
  }
  #myHandleOrder .table-action:hover{
    opacity: 0.8;
    text-decoration: underline;
  }
  #myHandleOrder .svg-icon {
    display: inline-block;
    height: 27px;
    width: 27px;
    margin-right: 5px;
    background-repeat: no-repeat;
    background-size: 14px 14px;
    background-position: center;
  }
  #myHandleOrder .icon_work_order_handle{
    background-image: url(/static/img/other/icon_work_order_handle.svg);
  }
  #myHandleOrder .icon_work_order_send_message{
    background-image: url(/static/img/other/icon_work_order_send_message.svg);
  }
  #myHandleOrder .initiate_application{
    background-image: url(/static/img/other/initiate_application.svg);
  }
  #myHandleOrder .come-from-tech {
    margin-left: 8px;
    cursor: pointer;
    user-select: none;
  }
</style>

<div id="myHandleOrder">
  <div class="my-work-order-container my-work-order-box">
    <!-- 头部页签 -->
    <div class="flex flex-center flex-between">
      <div class="my-work-order-options flex" id="myWorkOrderOptionsBar">
        <div class="myHandleOrder-options-item-text" data-index="0">全部</div>
        <div class="myHandleOrder-options-item-text" data-index="1" style="display: none;">待派单(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="2">待接单(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="3">处理中(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="4">待验收(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="5">待评价(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="6">已完成</div>
        <div class="myHandleOrder-options-item-text" data-index="7">已暂停(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="8">已终止(0)</div>
        <div class="myHandleOrder-options-item-text" data-index="9">参与过的</div>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="oa-nav-search search-bar">
      <!-- 全部 搜索栏 -->
      <div class="queryForm">
        <form
          class="layui-form"
          id="myWorkSheet1-searchForm"
          lay-filter="myHandleOrder1"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet1"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet1"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修时间</span>
            <div class="shell-layer-input-box" style="width: auto;">
              <div class="layui-inline search-time-range flex">
                <div class="layui-input-inline" style="width: 150px;">
                  <input
                    class="layui-input"
                    name="beginTime"
                    render="time"
                    placeholder="开始日期"
                  />
                </div>
                <span>-</span>
                <div class="layui-input-inline" style="width: 150px;">
                  <input
                    class="layui-input"
                    name="endTime"
                    render="time"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet1"
              id="myWorkSheet1-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox0"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet1-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>

        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox0"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet1-searchFormHidden"
              lay-filter="myHandleOrder1-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="searchBar-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName0"
                    data-repairman-id="myWorkSheet1-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId0"
                    id="myWorkSheet1-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                      id="myWorkSheet1-repairName"
                      render="repairMan"
                      repairMan="0"
                    ></div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet1-searchBtnHidden"
              search-btn="myWorkSheet1-1"
              screen-box-tar="myOrderSearchBox0"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet1-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet1-close"
              screen-box-tar="myOrderSearchBox0"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 待派单 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet2-searchForm"
          lay-filter="myHandleOrder2"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                search-input="myWorkSheet2"
                placeholder="请输入故障描述"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">紧急程度</span>
            <div class="shell-layer-input-box">
              <select lay-search name="faultEmergency">
                <option value="">请选择紧急程度</option>
                <option value="1">非常紧急</option>
                <option value="2">比较急</option>
                <option value="3">常规处理</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">影响范围</span>
            <div class="shell-layer-input-box">
              <select lay-search name="faultAffectScope">
                <option value="">请选择影响范围</option>
                <option value="1">个人事件</option>
                <option value="2">科室事件</option>
                <option value="3">多科室事件</option>
                <option value="3">全院事件</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet2"
              id="myWorkSheet2-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox1"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet2-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox1"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet2-searchFormHidden"
              lay-filter="myHandleOrder2-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="myWorkSheet2-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName1"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId1"
                    id="myWorkSheet2-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="myWorkSheet2-repairName"
                    render="repairMan"
                    repairMan="1"
                  ></div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">工单编号</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="number"
                    name="workNumber"
                    class="layui-input"
                    search-input="myWorkSheet2"
                    placeholder="请输入工单编号"
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md10">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                        autocomplete="off"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet2-searchBtnHidden"
              search-btn="myWorkSheet2-1"
              screen-box-tar="myOrderSearchBox1"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet2-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet2-close"
              screen-box-tar="myOrderSearchBox1"
              >关闭</span
            >
          </div>
        </div>
      </div>
      <!-- 待接单 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet3-searchForm"
          lay-filter="myHandleOrder3"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet3"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet3"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">紧急程度</span>
            <div class="shell-layer-input-box">
              <select lay-search name="faultEmergency">
                <option value="">请选择紧急程度</option>
                <option value="1">非常紧急</option>
                <option value="2">比较急</option>
                <option value="3">常规处理</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet3"
              id="myWorkSheet3-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox2"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet3-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox2"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet3-searchFormHidden"
              lay-filter="myHandleOrder3-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="myWorkSheet3-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName2"
                    data-repairman-id="myWorkSheet3-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId2"
                    id="myWorkSheet3-repairManDeptId"
                    value=""
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">影响范围</div>
                </div>
                <div class="layui-col-md7">
                  <select lay-search name="faultAffectScope">
                    <option value="">请选择影响范围</option>
                    <option value="1">个人事件</option>
                    <option value="2">科室事件</option>
                    <option value="3">多科室事件</option>
                    <option value="3">全院事件</option>
                  </select>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="myWorkSheet3-repairName"
                    render="repairMan"
                    repairMan="2"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6" style="float: unset;">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                        autocomplete="off"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        autocomplete="off"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet3-searchBtnHidden"
              search-btn="myWorkSheet3-1"
              screen-box-tar="myOrderSearchBox2"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet3-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet3-close"
              screen-box-tar="myOrderSearchBox2"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 处理中 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet4-searchForm"
          lay-filter="myHandleOrder4"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet4"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet4"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">紧急程度</span>
            <div class="shell-layer-input-box">
              <select lay-search name="faultEmergency">
                <option value="">请选择紧急程度</option>
                <option value="1">非常紧急</option>
                <option value="2">比较急</option>
                <option value="3">常规处理</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet4"
              id="myWorkSheet4-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox3"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet4-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox3"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet4-searchFormHidden"
              lay-filter="myHandleOrder4-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="myWorkSheet4-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName3"
                    data-repairman-id="myWorkSheet4-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId3"
                    id="myWorkSheet4-repairManDeptId"
                    value=""
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">影响范围</div>
                </div>
                <div class="layui-col-md7">
                  <select lay-search name="faultAffectScope">
                    <option value="">请选择影响范围</option>
                    <option value="1">个人事件</option>
                    <option value="2">科室事件</option>
                    <option value="3">多科室事件</option>
                    <option value="3">全院事件</option>
                  </select>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="myWorkSheet4-repairName"
                    render="repairMan"
                    repairMan="3"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6" style="float: unset;">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                        autocomplete="off"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet4-searchBtnHidden"
              search-btn="myWorkSheet4-1"
              screen-box-tar="myOrderSearchBox3"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet4-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet4-close"
              screen-box-tar="myOrderSearchBox3"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 待验收 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet5-searchForm"
          lay-filter="myHandleOrder5"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet5"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet5"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">紧急程度</span>
            <div class="shell-layer-input-box">
              <select lay-search name="faultEmergency">
                <option value="">请选择紧急程度</option>
                <option value="1">非常紧急</option>
                <option value="2">比较急</option>
                <option value="3">常规处理</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet5"
              id="myWorkSheet5-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox4"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet5-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox4"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet5-searchFormHidden"
              lay-filter="myHandleOrder5-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="myWorkSheet5-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName4"
                    data-repairman-id="myWorkSheet5-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId4"
                    id="myWorkSheet5-repairManDeptId"
                    value=""
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">影响范围</div>
                </div>
                <div class="layui-col-md7">
                  <select lay-search name="faultAffectScope">
                    <option value="">请选择影响范围</option>
                    <option value="1">个人事件</option>
                    <option value="2">科室事件</option>
                    <option value="3">多科室事件</option>
                    <option value="3">全院事件</option>
                  </select>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="myWorkSheet5-repairName"
                    render="repairMan"
                    repairMan="4"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6" style="float: unset;">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                        autocomplete="off"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet5-searchBtnHidden"
              search-btn="myWorkSheet5-1"
              screen-box-tar="myOrderSearchBox4"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet5-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet5-close"
              screen-box-tar="myOrderSearchBox4"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 待评价 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet6-searchForm"
          lay-filter="myHandleOrder6"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet6"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet6"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修人</span>
            <div class="shell-layer-input-box">
              <div
                id="myWorkSheet6-repairName"
                render="repairMan"
                repairMan="5"
              ></div>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet6"
              id="myWorkSheet6-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet6-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>
      </div>

      <!-- 已完成 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet7-searchForm"
          lay-filter="myHandleOrder7"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet7"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet7"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修时间</span>
            <div class="shell-layer-input-box" style="width: auto;">
              <div class="layui-inline search-time-range" style="display: flex;">
                <div class="layui-input-inline" style="width: 150px;">
                  <input
                    class="layui-input"
                    name="beginTime"
                    render="time"
                    placeholder="开始日期"
                  />
                </div>
                <span>-</span>
                <div class="layui-input-inline" style="width: 150px;">
                  <input
                    class="layui-input"
                    name="endTime"
                    render="time"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet7"
              id="myWorkSheet7-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox6"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet7-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox6"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet7-searchFormHidden"
              lay-filter="myHandleOrder7-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="myWorkSheet7-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName5"
                    data-repairman-id="myWorkSheet7-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId5"
                    id="myWorkSheet7-repairManDeptId"
                    value=""
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="myWorkSheet7-repairName"
                    render="repairMan"
                    repairMan="6"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">完成时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginActualCompletionTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endActualCompletionTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet7-searchBtnHidden"
              search-btn="myWorkSheet7-1"
              screen-box-tar="myOrderSearchBox6"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet7-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet7-close"
              screen-box-tar="myOrderSearchBox6"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 已暂停 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet8-searchForm"
          lay-filter="myHandleOrder8"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet8"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet8"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修人</span>
            <div class="shell-layer-input-box">
              <div
                id="myWorkSheet8-repairName"
                render="repairMan"
                repairMan="7"
              ></div>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet8"
              id="myWorkSheet8-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet8-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>
      </div>

      <!-- 已终止 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet9-searchForm"
          lay-filter="myHandleOrder9"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet9"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet9"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修时间</span>
            <div class="shell-layer-input-box" style="width: auto;">
              <div class="layui-inline search-time-range" style="display: flex;">
                <div class="layui-input-inline" style="width: 150px;">
                  <input
                    class="layui-input"
                    name="beginTime"
                    render="time"
                    placeholder="开始日期"
                  />
                </div>
                <span>-</span>
                <div class="layui-input-inline" style="width: 150px;">
                  <input
                    class="layui-input"
                    name="endTime"
                    render="time"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet9"
              id="myWorkSheet9-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox8"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet9-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>

        <div
          class="screen-box more-search-box"
          screen-box="myOrderSearchBox8"
        >
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="myWorkSheet9-searchFormHidden"
              lay-filter="myHandleOrder9-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="myWorkSheet9-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName6"
                    data-repairman-id="myWorkSheet9-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId6"
                    id="myWorkSheet9-repairManDeptId"
                    value=""
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="myWorkSheet9-repairName"
                    render="repairMan"
                    repairMan="8"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">终止时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTerminationOfTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTerminationOfTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="myWorkSheet9-searchBtnHidden"
              search-btn="myWorkSheet9-1"
              screen-box-tar="myOrderSearchBox8"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="myWorkSheet9-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="myWorkSheet9-close"
              screen-box-tar="myOrderSearchBox8"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 参与过的 搜索栏 -->
      <div class="queryForm" style="display: none;">
        <form
          class="layui-form"
          id="myWorkSheet10-searchForm"
          lay-filter="myHandleOrder10"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="myWorkSheet10"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="myWorkSheet10"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修人</span>
            <div class="shell-layer-input-box">
              <div
                id="myWorkSheet10-repairName"
                render="repairMan"
                repairMan="9"
              ></div>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="myWorkSheet10"
              id="myWorkSheet10-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="myWorkSheet10-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <!-- <button
            type="button"
            class="layui-btn"
            name="myorderCreaderOrderBtn"
          >
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button> -->
          <button class="layui-btn" type="button" name="export">
            <i class="fa fa-sign-out"></i>
            导出
          </button>
        </div>
      </div>
    </div>

    <!--other-->
    <form id="orderAllForm">
      <div class="trasen-con-box">
        <div class="table-box" style="margin-left: 8px;">
          <!-- 表单 -->
          <table id="allHandleOrderTable"></table>
          <!-- 分页 -->
          <div id="allHandleOrderPager"></div>
        </div>
      </div>
    </form>

    <!--参与过的-->
    <form id="participatedForm" style="display: none;">
      <div class="trasen-con-box">
        <div class="table-box" style="margin-left: 8px;">
          <!-- 表单 -->
          <table id="handleOrderParticipatedTable"></table>
          <!-- 分页 -->
          <div id="handleOrderParticipatedPager"></div>
        </div>
      </div>
    </form>
  </div>
</div>