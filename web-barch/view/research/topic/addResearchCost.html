<style>
    #addResearchCostBox {
        padding: 15px 32px 0;
    }

    #addResearchCostBox > h4 {
        font-size: 14px;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #333333;
        line-height: 20px;
        margin-bottom: 16px;
    }

    #addResearchCostBox .progress-and-totalM {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333333;
        line-height: 20px;
        margin-bottom: 20px;
    }

    #addResearchCostBox .progress-and-totalM .total-m {
        width: 183px;
        height: 30px;
        background: #FAFAFA;
        border-radius: 15px;
        border: 1px solid #E7EBF0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    #addResearchCostBox .progress-and-totalM .total-m #surplusPrice {
        color: #EC7B25;
    }

    #addResearchCostBox #addResearchCostProgress {
        position: relative;
        flex: 1;
        margin-right: 32px;
        background: #FAFAFA;
        height: 8px;
        border: 1px solid #E7EBF0;
    }

    #addResearchCostBox #addResearchCostProgress .pesearch-progress-num {
        position: absolute;
        top: -20px;
    }

    #addResearchCostBox #addResearchCostProgress .layui-progress-bar {
        background: #4b7aff;
        height: 8px;
    }

    #addResearchCostBox #addResearchCostProgress .for-m {
        display: flex;
        position: absolute;
        top: 12px;
        right: 0;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333333;
        line-height: 17px;
    }

    #addResearchCostBox .addResearchCost-table {
        display: flex;
        flex-direction: column;
    }

    #addResearchCostBox .addResearchCost-table .add-item-btn {
        display: flex;
        justify-content: flex-end;
    }

    #addResearchCostBox .addResearchCost-table .add-item-btn .addResearchCostTableBtn {
        width: 35px;
        height: 35px;
        cursor: pointer;
    }

    #addResearchCostBox .cost-table-box {
        height: 320px;
        overflow-y: auto;
    }

    #addResearchCostBox #CostTable .userTableTd .layui-input {
        height: 38px;
        border: none;
    }

    #addResearchCostBox #CostTable .userTableTd .tra-lay-select-title i.icon {
        top: 17px;
    }

    #addResearchCostBox #CostTable .btn {
        border: transparent;
        background: transparent;
        cursor: pointer;
    }

    #addResearchCostBox #CostTable .edit-or-save {
        color: #5260FF;
        margin-right: 10px;
    }

    #addResearchCostBox #CostTable .cancel-or-del {
        color: #999999;
    }

    #addResearchCostBox .fakeTotal {
        color: #EC7B25;
    }
</style>
<div id="addResearchCostBox">
  <h4>抑菌沐浴汤在预防重症病人院内感染中的应用</h4>
  <div class="progress-and-totalM">
    <div class="layui-progress layui-progress-big" id="addResearchCostProgress" lay-filter="addResearchCostProgress">
      <span class="pesearch-progress-num" id="totalPriceUse"></span>
      <div class="layui-progress-bar"></div>
      <div class="for-m" id="totalPrice">
      </div>
    </div>
    <div class="total-m">
      剩余经费 <span id="surplusPrice" style="margin: 0 5px;"></span> 元
    </div>
  </div>
  <div class="addResearchCost-table">
    <div class="add-item-btn">
      <img class="addResearchCostTableBtn" src="../static/img/other/add.svg" title="新增"/>
    </div>
    <div class="cost-table-box">
      <table class="oa-table no-zebra" id="CostTable">
        <thead>
        <tr>
          <th style="width: 115px;">经费名称</th>
          <th>具体内容</th>
          <th style="width: 140px">使用科室</th>
          <th style="width: 100px">使用人员</th>
          <th style="width: 100px;text-align: right">金额（元）</th>
          <th style="width: 100px">上报日期</th>
          <th style="width: 120px">操作</th>
        </tr>
        </thead>
        <tbody>

        </tbody>
      </table>
    </div>
    <div class="layer-btn archivesTabBtn">
      <span style="position: absolute; left: 10px;top: 12px">
        合计 <span class="fakeTotal">0.00</span> 元（剩余经费<span class="fakeSurplus"></span>元）
      </span>
      <button class="layui-btn" id="submitCostBtn" type="button">保存</button>
      <button class="layui-btn layui-btn-primary" id="closeaddResearchCostBtn" type="button">取消</button>
    </div>
  </div>
</div>
