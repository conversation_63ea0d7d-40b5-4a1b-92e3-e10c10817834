'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        opt.$dom.html(html);
        common.overlayScrollbarsSet(' .scrollbar-box');

        var now = new Date();
        var today = {
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            date: now.getDate(),
            day: now.getDay(),
        };
        var currentTime = {
            year: now.getFullYear(),
            month: now.getMonth() + 1,
        };
        var selDay = {
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            date: now.getDate(),
            day: now.getDay(),
        };
        var weekArr = [];
        var isWeek = false

        function getRegPool() {
            var TIME_INTERVAL = {
                1: '上午',
                2: '中午',
                3: '下午',
                4: '晚上',
                5: '深夜',
            };
            var TIME_INTERVAL_Class = {
                1: 'morning',
                2: 'noon',
                3: 'afternoon',
                4: 'night',
                5: 'evening',
            };
            common.getRegPool({
                start: selDay.year + '-' + selDay.month + '-' + selDay.date,
                end: selDay.year + '-' + selDay.month + '-' + selDay.date,
                callback: function (arr) {
                    if (!arr.length) {
                        $('.calendar-content .regPool').addClass('none');
                    } else {
                        $('.calendar-content .regPool').removeClass('none');
                    }
                    var obj = {};
                    for (var i = 0; i < arr.length; i++) {
                        var d = new Date(arr[i].REG_DATE).format('yyyy-MM-dd');
                        !obj[d] && (obj[d] = '');
                        obj[d] += '<li layer-tips="' + TIME_INTERVAL[arr[i].TIME_INTERVAL] + '门诊预约' + arr[i].REG_NUM + '人" class="' + TIME_INTERVAL_Class[arr[i].TIME_INTERVAL] + '"><div class="t">' + TIME_INTERVAL[arr[i].TIME_INTERVAL] + '</div><div class="n">' + arr[i].REG_NUM + '</div></li>';
                    }
                    for (var key in obj) {
                        $('.calendar-content .regPool').html('<ul>' + obj[key] + '</ul>');
                    }
                    $('.regPool li').layerTips({
                        tips: [3, '#fff'],
                    });
                },
            });
        }
        function setCurrentTime() {
            // $('#calendar-year').text(selDay.year);
            // $('#calendar-month').text(selDay.month);
            // $('#calendar-date').text(selDay.date);
            // $('#calendar-year').text(weekArr[0].year);
            // $('#calendar-month').text(weekArr[0].month);
            // $('#calendar-date').text(weekArr[0].date);
            $('#calendar-year-l').text(weekArr[6].year);
            $('#calendar-month-l').text(weekArr[6].month);
            // $('#calendar-date-l').text(weekArr[6].date);
        }

        function getWeekArr(time) {
            var time = new Date(time);
            var arr = [];
            var day = time.getDay();

            for (var i = 0; i < 7; i++) {
                arr.push(getWeekDay(time.getTime() + 1000 * 60 * 60 * 24 * (i - day)));
            }
            weekArr = arr;
            var startDate = arr[0].year + '-' + arr[0].month + '-' + arr[0].date;
            var endDate = arr[6].year + '-' + arr[6].month + '-' + arr[6].date;
            getCalendarData(startDate, endDate);
        }

        function getWeekDay(time) {
            var time = new Date(time);
            return {
                year: time.getFullYear(),
                month: time.getMonth() + 1,
                date: time.getDate(),
            };
        }

        function calendarDate(data) {
            var html = '';
            for (var i = 0; i < weekArr.length; i++) {
                var isToday = '';
                var isActive = '';
                var isBefore = '';
                if (weekArr[i].year == today.year && weekArr[i].month == today.month && today.date == weekArr[i].date) {
                    isToday = 'today';
                }
                if (selDay.year == weekArr[i].year && selDay.month == weekArr[i].month && selDay.date == weekArr[i].date) {
                    isActive = 'active';
                }
                var isHas = '';
                if (data) {
                    for (var j = 0; j < data.length; j++) {
                        if (data[j].year == weekArr[i].year && data[j].month == weekArr[i].month && data[j].date == weekArr[i].date) {
                            isHas = 'has';
                        }
                    }
                    if ((isToday && isHas) || isActive) {
                        $('#indexIFrame .calendar-content-detail').html('');
                        getCalendarContent(selDay.year + '-' + selDay.month + '-' + selDay.date);
                    }
                }
                if ((common.addZero(weekArr[i].year) + common.addZero(weekArr[i].month) + common.addZero(weekArr[i].date)) * 1 < (common.addZero(today.year) + common.addZero(today.month) + common.addZero(today.date)) * 1) {
                    isBefore = 'before';
                }

                html += '<div class="flex-box-1"><span class="time ' + isBefore + ' ' + isToday + ' ' + isHas + ' ' + isActive + '" data-day="' + weekArr[i].date + '" data-year="' + weekArr[i].year + '" data-month="' + weekArr[i].month + '">' + weekArr[i].date + '</span></div>';
            }
            $('.calendar-date-week .week-time').html(html);
        }
        getWeekArr(now);
        isWeekFunction()
        setCurrentTime();
        getRegPool();
        function currentWeek(number) {
            isWeek = false;
            var number = number || 0;
            var time = new Date();
            // time.setFullYear(currentTime.year)
            // time.setMonth(currentTime.month - 1)
            time.setFullYear(weekArr[weekArr.length - 1].year);
            time.setMonth(weekArr[weekArr.length - 1].month - 1, 1);
            time.setDate(weekArr[weekArr.length - 1].date);
            time.setTime(time.getTime() + 1000 * 60 * 60 * 24 * number);
            getWeekArr(time);
            setCurrentTime();
            isWeekFunction()
        }

        function isWeekFunction() {
            for (var i = 0; i < weekArr.length; i++) {
                if (today.year == weekArr[i].year && today.month == weekArr[i].month && today.date == weekArr[i].date) {
                    isWeek = true;
                }
            }
            if (isWeek) {
                $('#indexIFrame #CalendarNowWeek').addClass('active');
            } else {
                $('#indexIFrame #CalendarNowWeek').removeClass('active');
            }
        }

        $('#calendar-next-week')
            .off('click')
            .on('click', function (e) {
                e.stopPropagation();
                currentWeek(7);
            });
        $('#calendar-prev-week')
            .off('click')
            .on('click', function (e) {
                e.stopPropagation();
                currentWeek(-7);
            });

        // 本周按钮
        $('#CalendarNowWeek')
            .off('click')
            .on('click', function (e) {
                e.stopPropagation();
                getWeekArr(now);
                currentWeek();
            });
        var times = [];
        $('#indexIFrame .calendar-date-week')
            .off('click', '.time')
            .on('click', '.time', function () {
                times.push(new Date().getTime());
                if (times.length > 1) {
                    return;
                }
                var date = $(this).attr('data-day');
                selDay.year = $(this).attr('data-year');
                selDay.month = $(this).attr('data-month');
                selDay.date = date;
                $(this).addClass('active').parent().siblings().find('.time').removeClass('active');
                // setCurrentTime();
                $('#indexIFrame .calendar-content-detail').html('');
                if ($(this).hasClass('has')) {
                    getCalendarContent(selDay.year + '-' + selDay.month + '-' + selDay.date);
                } else {
                    $('#indexIFrame .calendar-content-detail').closest('.content-bg-con').removeClass('noBg');
                }
                getRegPool();
                setTimeout(function () {
                    times = [];
                }, 300);
            });
        $('#indexIFrame .calendar-date-week')
            .off('dblclick', '.time')
            .on('dblclick', '.time', function () {
                var date = $(this).attr('data-day');
                var year = $(this).attr('data-year');
                var month = $(this).attr('data-month');
                $.quoteFun('/hrm/schudler/add', {
                    title: '添加日程',
                    data: {
                        id: null,
                        date: new Date(year + '-' + month + '-' + date).Format('yyyy-MM-dd'),
                    },
                    ref: currentWeek,
                });
            });
        $('#indexIFrame')
            .off('click', '#calendar-add')
            .on('click', '#calendar-add', function () {
                $.quoteFun('/hrm/schudler/add', {
                    title: '添加日程',
                    data: {
                        id: null,
                    },
                    ref: currentWeek,
                });
            });
        function setSelCalendarData() {
            getCalendarContent(selDay.year + '-' + selDay.month + '-' + selDay.date);
        }
        function getCalendarData(startDate, endDate) {
            calendarDate();
            $.ajax({
                url: '/ts-oa/schedule/selectScheduleDate',
                type: 'get',
                data: {
                    startDate: startDate,
                    endDate: endDate,
                },
                success: function (res) {
                    if (res.success && res.object) {
                        var allTime = [];
                        for (var i = 0; i < res.object.length; i++) {
                            var arr = res.object[i].split('-');
                            allTime.push({
                                year: arr[0],
                                month: arr[1],
                                date: arr[2],
                            });
                        }
                        calendarDate(allTime);
                    }
                },
            });
        }

        function getCalendarContent(scheduleDate) {
            $.ajax({
                type: 'post',
                url: '/ts-oa/schedule/selectScheduleDetailsList',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify({
                    scheduleDate: scheduleDate,
                }),
                success: function (res) {
                    var meetingType = {
                        工作日程: 'work',
                        会议日程: 'meeting',
                    };
                    var iconType = {
                        work: 'fa-briefcase',
                        meeting: 'fa-briefcase',
                    };
                    if (res.success && res.object.length) {
                        $('#indexIFrame .calendar-content-detail').html('');
                        $('#indexIFrame .calendar-content-detail').closest('.content-bg-con').addClass('noBg');
                        new common.simpleEvent({
                            el: '#indexIFrame .calendar-content-detail',
                            list: res.object,
                            event: 'click',
                            print: function (item) {
                                var meetingTime = new Date(item.scheduleDate + ' ' + item.scheduleEndTime).getTime();
                                var now = new Date().getTime();
                                var isPast = now > meetingTime ? 'past' : '';
                                var address = '';
                                // address = '<div class="address" title="' + item.scheduleAddress + '"> ' + item.scheduleAddress + ' </div>';
                                // if (item.scheduleAddress.length >= 8) {
                                //     address = '<div class="address line-two" title="' + item.scheduleAddress + '"><div class="text">' + item.scheduleAddress + '</div></div>';
                                // }
                                return (
                                    '<div class="calendar-content-item ' +
                                    ' ' +
                                    meetingType[item.scheduleType] +
                                    '">' +
                                    '<div class="  flex-box">' +
                                    '<div class="time">' +
                                    item.scheduleStartTime +
                                    '</div>' +
                                    '<p class="con flex-box-1 edit" title="' +
                                    item.scheduleSubject +
                                    '">' +
                                    item.scheduleSubject +
                                    '</p>' +
                                    address +
                                    '<span class="calendar-opera-btn "><i class="oaicon oa-icon-error del"></i></span>' +
                                    '</div>' +
                                    '</div>'
                                );
                            },
                            func: function (item, index, e) {
                                var target = e.target;
                                e.stopPropagation();
                                if (target.classList.contains('edit')) {
                                    $.quoteFun('/hrm/schudler/add', {
                                        title: '修改日程',
                                        data: {
                                            id: item.id,
                                        },
                                        ref: currentWeek,
                                    });
                                } else if (target.classList.contains('del')) {
                                    layer.confirm('确定要删除该数据吗?', {
                                        btn: ['确定', '取消'],
                                        title: '提示',
                                        closeBtn: 0
                                    }, function (index) {
                                        $.ajax({
                                            url: '/ts-oa/schedule/delete',
                                            method: 'post',
                                            data: {
                                                id: item.id,
                                            },
                                            success: function (res) {
                                                if (res.success) {
                                                    currentWeek();
                                                    layer.msg('删除成功');
                                                } else {
                                                    layer.msg(res.message || '删除失败');
                                                }
                                                layer.close(index);
                                            },
                                        });
                                    });
                                }
                            },
                        });
                    } else {
                        $('#indexIFrame .calendar-content').closest('.content-bg-con').removeClass('noBg');
                    }
                },
            });
        }
    };
});