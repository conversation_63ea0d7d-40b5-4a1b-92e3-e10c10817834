'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch','element'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch,
                element = layui.element;
            
            var tab = 0;
            $('#messageSettingBox .oa-nav .oa-nav_item ').off('click').on('click', function () {
                tab = $(this).index();
                $(this).addClass('active').siblings().removeClass('active');
                $('#messageSettingBox .tab-box').addClass('none').eq(tab).removeClass('none');
                return false;
            });
            
            $('#basicMessageTips').layerTips({
                tips: [2, '#fff'],
            });
            
            $('#sysMessageTips').layerTips({
                tips: [2, '#fff'],
            });
            
            $("#monthWarnNumber").blur(function(){
            	if("1" == $("#monthMaxSwitch").val()){
            		if(Number($("#monthWarnNumber").val()) > Number($("#monthMaxNumber").val())){
               			layer.msg('每月预警数量已超过当月最大可发送条数，是否仍保存？');
            		}
            	}
            })
            
            $("#dayWarnNumber").blur(function(){
            	if("1" == $("#dayMaxSwitch").val()){
            		if(Number($("#dayWarnNumber").val()) > Number($("#dayMaxNumber").val())){
               			layer.msg('每天预警数量已超过当天最大可发送条数，是否仍保存？');
            		}
            	}
            })
            
            //查询余额
			if ("cssdeshfly" != common.globalSetting.orgCode) {
				$.ajax({
				    url:'/ts-information/messageInternal/getSmsBalance',
				    method: 'get',
				    contentType: 'application/json;charset=UTF-8',
				    success: function (res) {
				        if (res.success) {
				        	var result = JSON.parse(res.object);
				        	if(result.code == '0'){
				        		$("#smsBalanceSpan").html("当前短信余额：" + result.balance + "条");
				            	if(null != result.balance && result.balance < 5000){
				            		$("#smsBalanceRemindSpan").html("余额不足，请尽快充值");
				            	}
				        	}
				        } else {
				            layer.msg(res.message || '查询余额失败');
				        }
				    },
				});
			}
            
            //查询基础短信设置数据
            $.ajax({
                url: '/ts-information/api/messageInternal/basicSetting/selectMessageBasicSetting',
                method: 'get',
                contentType: 'application/json;charset=UTF-8',
                success: function (res) {
                    if (res.success) {
                    	trasen.setNamesVal($('#basicMessageSettingForm'), res.object);
                    	$("#noticeNameSpan").html(res.object.userNamePhone);
                    	$("#maxNumberByMonth").html(res.object.maxNumberByMonth);
                    	$("#sumNumberByMonth").html(res.object.sumNumberByMonth);
                    	$("#maxNumberByDay").html(res.object.maxNumberByDay);
                    	$("#sumNumberByDay").html(res.object.sumNumberByDay);
                    	let noticeTypeArr = res.object.noticeType.split(",");
                    	$.each(noticeTypeArr,function(i,item){
                    	    let node = $('input[type="checkbox"][name^="noticeType"][value="' + item + '"]');
                    		if (node && node.length) {
                    	        node[0].checked = true;
                    	    }
                    	})
                    	if("1" == res.object.monthMaxSwitch){
                    		$("#monthMaxSwitchCheckbox").prop("checked", false);
                    		$("#monthMaxNumber").removeClass("disabledClass");
                    		$("#monthMaxNumber").removeAttr("disabled");
                    		$("#monthMaxSpan1").removeClass("disabledSpan");
        					$("#monthMaxSpan2").removeClass("disabledSpan");
                    	}else{
                    		$("#monthMaxSwitchCheckbox").prop("checked", true);
                    		$("#monthMaxNumber").addClass("disabledClass");
                    		$("#monthMaxNumber").attr("disabled", "disabled");
                    		$("#monthMaxSpan1").addClass("disabledSpan");
        					$("#monthMaxSpan2").addClass("disabledSpan");
                    	}
                    	if("1" == res.object.monthWarnSwitch){
                    		$("#monthWarnSwitchCheckbox").prop("checked", true);
                    		$("#monthWarnNumber").removeClass("disabledClass");
                    		$("#monthWarnNumber").removeAttr("disabled");
                    		$("#monthWarnSpan1").removeClass("disabledSpan");
        					$("#monthWarnSpan2").removeClass("disabledSpan");
                    	}else{
                    		$("#monthWarnSwitchCheckbox").prop("checked", false);
                    		$("#monthWarnNumber").addClass("disabledClass");
                    		$("#monthWarnNumber").attr("disabled", "disabled");
                    		$("#monthWarnSpan1").addClass("disabledSpan");
        					$("#monthWarnSpan2").addClass("disabledSpan");
                    	}
                    	if("1" == res.object.dayMaxSwitch){
                    		$("#dayMaxSwitchCheckbox").prop("checked", false);
                    		$("#dayMaxNumber").removeClass("disabledClass");
                    		$("#dayMaxNumber").removeAttr("disabled");
                    		$("#dayMaxSwitchSpan1").removeClass("disabledSpan");
        					$("#dayMaxSwitchSpan2").removeClass("disabledSpan");
        					/*$("#sumNumberByDayProgress").show();
                    		$("#sumNumberByDaySpan").hide();
                    		$("#sumNumberByDay2").html(res.object.sumNumberByDay + "/" + res.object.dayMaxNumber);
                        	var dd = (res.object.sumNumberByDay / res.object.dayMaxNumber * 100) + "%";
                        	element.progress('sumNumberByDayProgress',dd);*/
                    	}else{
                    		/*$("#sumNumberByDayProgress").hide();
        					$("#sumNumberByDaySpan").show();*/
                    		$("#dayMaxSwitchCheckbox").prop("checked", true);
                    		$("#dayMaxNumber").addClass("disabledClass");
                    		$("#dayMaxNumber").attr("disabled", "disabled");
                    		$("#dayMaxSwitchSpan1").addClass("disabledSpan");
        					$("#dayMaxSwitchSpan2").addClass("disabledSpan");
                    	}
                    	if("1" == res.object.dayWarnSwitch){
                    		$("#dayWarnSwitchCheckbox").prop("checked", true);
                    		$("#dayWarnNumber").removeClass("disabledClass");
                    		$("#dayWarnNumber").removeAttr("disabled");
                    		$("#dayWarnSwitchSpan1").removeClass("disabledSpan");
        					$("#dayWarnSwitchSpan2").removeClass("disabledSpan");
                    	}else{
                    		$("#dayWarnSwitchCheckbox").prop("checked", false);
                    		$("#dayWarnNumber").addClass("disabledClass");
                    		$("#dayWarnNumber").attr("disabled", "disabled");
                    		$("#dayWarnSwitchSpan1").addClass("disabledSpan");
        					$("#dayWarnSwitchSpan2").addClass("disabledSpan");
                    	}
                    	if("1" == res.object.failureSwitch){
                    		$("#failureSwitchCheckbox").prop("checked", true);
                    		$("#failureRate").removeClass("disabledClass");
                    		$("#failureRate").removeAttr("disabled");
                    		$("#failureSwitchSpan1").removeClass("disabledSpan");
        					$("#failureSwitchSpan2").removeClass("disabledSpan");
                    	}else{
                    		$("#failureSwitchCheckbox").prop("checked", false);
                    		$("#failureRate").addClass("disabledClass");
                    		$("#failureRate").attr("disabled", "disabled");
                    		$("#failureSwitchSpan1").addClass("disabledSpan");
        					$("#failureSwitchSpan2").addClass("disabledSpan");
                    	}
                    	form.render();
                    }
                },
            });
            
            //查询系统短信
            loadSysSettingList();
            function loadSysSettingList(){
            	$.ajax({
                    url:'/ts-information/api/messageInternal/sysSetting/getSysSettingList',
                    method: 'get',
                    contentType: 'application/json;charset=UTF-8',
                    success: function (res) {
                        if (res.success) {
                        	$("#sysSettingBox").empty();
                        	var htmlStr = "";
                        	if(null != res.object && res.object.length > 0){
                        		$("#sysMessageSetDiv").removeClass("content_bg");
                        	}else{
                        		$("#sysMessageSetDiv").addClass("content_bg");
                        	}
                        	$.each(res.object,function(i,item){
                        		htmlStr += '<div class="sysSettingBox">';
                        		htmlStr += '<div>短信类型：' + item.messageType + '</div>';
                        		htmlStr += '<div>适用人员：';
                        		var sendUserNameArr = item.sendUserName.split(",");
                        		if(sendUserNameArr.length > 6){
                        			htmlStr += sendUserNameArr[0] + "," + sendUserNameArr[1] + "," + sendUserNameArr[2] + "," + sendUserNameArr[3] + "," + sendUserNameArr[4] + "," + sendUserNameArr[5] + "等" + sendUserNameArr.length + "人";
                        			htmlStr += '<i text=' + item.sendUserName + ' class="layui-icon showUserNameTips" style="margin-left: 16px;cursor: pointer;color: blue;">查看</i></div>';
                        		}else{
                        			if("" == item.sendUserName || null  == item.sendUserName){
                        				htmlStr += '全院人员</div>';
                        			}else{
                        				htmlStr += item.sendUserName + '</div>';
                        			}
                        		}
                        		
                        		htmlStr += '<div style="width:890px;">短信内容：' + item.sendContent + '</div>';
                        		htmlStr += '<div>发送时间：当天' + item.sendTime + '</div>';
                        		htmlStr += '<div class="opera">';
                        		htmlStr += '<i id=' + item.id + ' class="fa fa-pencil-square-o homo-page-edit" style="cursor: pointer;"></i>';
                        		htmlStr += '<i id=' + item.id + ' class="fa fa-trash-o homo-page-del" style="margin-left: 30px;cursor: pointer;"></i>';
                        		htmlStr += '</div></div>';
                        	})
                        	$("#sysSettingBox").html(htmlStr);
                        	
                        	
                        } else {
                            layer.msg(res.message || '查询系统短信失败');
                        }
                    },
                });
            }
            
            //新增系统短信
            $("#addSysMessageSetBtn").funs('click', function () {
				$.quoteFun('/message/messageSetting/sysMessageSet', {
					title: '系统短信',
					ref:loadSysSettingList
				});
			})
            
			//显示全部适用人员
            $('body').on('click', '.showUserNameTips', function () {
            	layer.alert($(this).attr('text'));
            });
            
            //编辑
            $('body').off('click', '#messageSettingBox .homo-page-edit').on('click', '#messageSettingBox .homo-page-edit', function () {
            	$.quoteFun('/message/messageSetting/sysMessageSet', {
					title: '系统短信',
					ref:loadSysSettingList,
					data:$(this).attr("id")
				});
            });
            
            //删除
            $('body').off('click', '#messageSettingBox .homo-page-del').on('click', '#messageSettingBox .homo-page-del', function () {
            	var id = $(this).attr("id");
            	layer.confirm("删除后将无法恢复，确定要删除该短信发送规则吗？",{
								btn: ['确定', '取消'],
								title: '提示',
								closeBtn: 0
							},function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-information//api/messageInternal/sysSetting/delete/' + id,
                        success: function (res) {
                            if (res.success) {
                                layer.msg('删除成功');
                                loadSysSettingList();
                            } else {
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            	
            });
            
           
            $('#messageSettingBox .selectNoticeAdminUser').funs('click', function () {
                var data = {
                    isCheckDept: 'N',
                    user_str: 'noticeName',
                    user_id: 'noticeUser',
                    user_code: 'noticeUser',
                    user_name_phone:'userNamePhone'
                };
                $.quoteFun('/common/userSel', {
                    title: '人员选择',
                    data: data,
                    callback: success_callback
                });
            });
            
            function success_callback(selUserNameVal, userIdArr, userCodeArr, deptNameArr, deptUserNameArr, userNamePhoneArr) {
            	$('#noticeNameSpan').empty();
                $('#noticeNameSpan').html(userNamePhoneArr.join(","));
            }
            
            //监听是否限制（月）
			form.on('checkbox(monthMaxSwitchCheckbox)', function (data) {
				if (data.elem.checked == true) {
					$("#monthMaxSwitch").val("0");
					$("#monthMaxNumber").attr("disabled", "disabled");
					$("#monthMaxNumber").addClass("disabledClass");
					$("#monthMaxNumber").removeAttr("lay-verify");
					//$("#monthMaxNumber").val("");
					$("#monthMaxSpan1").addClass("disabledSpan");
					$("#monthMaxSpan2").addClass("disabledSpan");
				} else {
					$("#monthMaxSwitch").val("1");
					$("#monthMaxNumber").removeAttr("disabled");
					$("#monthMaxNumber").removeClass("disabledClass");
					$("#monthMaxNumber").attr("lay-verify", "required|number");
					$("#monthMaxSpan1").removeClass("disabledSpan");
					$("#monthMaxSpan2").removeClass("disabledSpan");
				}
			});
			//监听预警（月）
			form.on('switch(monthWarnSwitchCheckbox)', function (data) {
				if (data.elem.checked == true) {
					$("#monthWarnSwitch").val("1");
					$("#monthWarnNumber").removeAttr("disabled");
					$("#monthWarnNumber").removeClass("disabledClass");
					$("#monthWarnNumber").attr("lay-verify", "required");
					$("#monthWarnSpan1").removeClass("disabledSpan");
					$("#monthWarnSpan2").removeClass("disabledSpan");
				} else {
					$("#monthWarnSwitch").val("0");
					$("#monthWarnNumber").attr("disabled", "disabled");
					$("#monthWarnNumber").addClass("disabledClass")
					$("#monthWarnNumber").removeAttr("lay-verify");
					//$("#monthWarnNumber").val("");
					$("#monthWarnSpan1").addClass("disabledSpan");
					$("#monthWarnSpan2").addClass("disabledSpan");
				}
			});
			//监听是否限制（天）
			form.on('checkbox(dayMaxSwitchCheckbox)', function (data) {
				if (data.elem.checked == true) {
					$("#dayMaxSwitch").val("0");
					$("#dayMaxNumber").attr("disabled", "disabled");
					$("#dayMaxNumber").addClass("disabledClass");
					$("#dayMaxNumber").removeAttr("lay-verify");
					//$("#dayMaxNumber").val("");
					$("#dayMaxSwitchSpan1").addClass("disabledSpan");
					$("#dayMaxSwitchSpan2").addClass("disabledSpan");
					/*$("#sumNumberByDayProgress").hide();
            		$("#sumNumberByDaySpan").show();*/
				} else {
					/*$("#sumNumberByDayProgress").show();
            		$("#sumNumberByDaySpan").hide();
            		$("#sumNumberByDay2").html($("#dayMaxNumber").val() + "/" + res.object.dayMaxNumber);
                	var dd = (res.object.sumNumberByDay / res.object.dayMaxNumber * 100) + "%";
                	element.progress('sumNumberByDayProgress',dd);*/
					$("#dayMaxSwitch").val("1");
					$("#dayMaxNumber").removeAttr("disabled");
					$("#dayMaxNumber").removeClass("disabledClass");
					$("#dayMaxNumber").attr("lay-verify", "required|number");
					$("#dayMaxSwitchSpan1").removeClass("disabledSpan");
					$("#dayMaxSwitchSpan2").removeClass("disabledSpan");
				}
			});
			//监听预警（天）
			form.on('switch(dayWarnSwitchCheckbox)', function (data) {
				if (data.elem.checked == true) {
					$("#dayWarnSwitch").val("1");
					$("#dayWarnNumber").removeAttr("disabled");
					$("#dayWarnNumber").removeClass("disabledClass");
					$("#dayWarnNumber").attr("lay-verify", "required");
					$("#dayWarnSwitchSpan1").removeClass("disabledSpan");
					$("#dayWarnSwitchSpan2").removeClass("disabledSpan");
				} else {
					$("#dayWarnSwitch").val("0");
					$("#dayWarnNumber").attr("disabled", "disabled");
					$("#dayWarnNumber").addClass("disabledClass")
					$("#dayWarnNumber").removeAttr("lay-verify");
					//$("#dayWarnNumber").val("");
					$("#dayWarnSwitchSpan1").addClass("disabledSpan");
					$("#dayWarnSwitchSpan2").addClass("disabledSpan");
				}
			});
			//异常通知
			form.on('switch(failureSwitchCheckbox)', function (data) {
				if (data.elem.checked == true) {
					$("#failureSwitch").val("1");
					$("#failureRate").removeAttr("disabled");
					$("#failureRate").removeClass("disabledClass");
					$("#failureRate").attr("lay-verify", "required");
					$("#failureSwitchSpan1").removeClass("disabledSpan");
					$("#failureSwitchSpan2").removeClass("disabledSpan");
				} else {
					$("#failureSwitch").val("0");
					$("#failureRate").attr("disabled", "disabled");
					$("#failureRate").addClass("disabledClass")
					$("#failureRate").removeAttr("lay-verify");
					//$("#failureRate").val("");
					$("#failureSwitchSpan1").addClass("disabledSpan");
					$("#failureSwitchSpan2").addClass("disabledSpan");
				}
			});
			
			
			// 保存
            form.on('submit(basicMessageSettingSubmit)', function (data) {
                var d = data.field;
                var url;
                if (d.id) {
					url = '/ts-information/api/messageInternal/basicSetting/update';
				} else {
					url = '/ts-information/api/messageInternal/basicSetting/save';
				}
                
                var noticeTypeArr = [];
                $('input[name=noticeType]:checked').each(function(){
                     noticeTypeArr.push($(this).val());
                });
                if("" == d.noticeUser || null == d.noticeUser || noticeTypeArr.length <= 0){
                	layer.msg('您开启了通知，请设置通知人员和通知方式');
                	return;
                }
                d.noticeType = noticeTypeArr.join(",");
                $.loadings();
                $.ajax({
                    url:url,
                    method: 'post',
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify(d),
                    success: function (res) {
                        if (res.success) {
                            layer.msg(res.message || '保存成功');
                        } else {
                            layer.msg(res.message || '保存失败');
                        }
                    },
                });
            });
            
            
            laydate.render({
                elem: '#startStatisticsMessageTime',
                type: 'month',
                trigger: 'click'
            });
            laydate.render({
                elem: '#endStatisticsMessageTime',
                type: 'month',
                trigger: 'click'
            });
            
            
            $('#messageSettingBox #statisticsMessageSerchBtn').funs('click', function () {
            	var startStatisticsMessageTime = $("#startStatisticsMessageTime").val();
            	var endStatisticsMessageTime =  $("#endStatisticsMessageTime").val();
            	if('' == startStatisticsMessageTime || '' == endStatisticsMessageTime){
            		layer.msg('时间区间不能为空');
            		return;
            	}
            	initContainer(startStatisticsMessageTime,endStatisticsMessageTime);
            });
            
            $('#messageSettingBox #statisticsMessageReset').funs('click', function () {
            	$("#startStatisticsMessageTime").val("");
            	$("#endStatisticsMessageTime").val("");
            	initContainer("","");
            });
            
            
            initContainer("","");
            function initContainer(startStatisticsMessageTime,endStatisticsMessageTime){
            	 var statisticsMessageContainer = document.getElementById("statisticsMessageContainer");
                 var myChart = echarts.init(statisticsMessageContainer);

                 var option;
                 var legendData = [];
                 var seriesOAData = [];
                 var seriesSystemData = [];
                 var seriesTotalData = [];
                 var seriesWarningData = [];
                 var xAxisData = [];
                 $.ajax({
                     type: 'get',
                     url: common.url + '/ts-information/messageInternal/selectStatisticsMessage?startStatisticsMessageTime=' + startStatisticsMessageTime + "&endStatisticsMessageTime=" + endStatisticsMessageTime,
                     async:false,
                     success: function (res) {
                         if (res.success) {
                        	 legendData = res.object.legendData;
                        	 seriesOAData = res.object.seriesOAData;
                        	 seriesSystemData = res.object.seriesSystemData;
                        	 seriesTotalData = res.object.seriesTotalData;
                        	 seriesWarningData = res.object.seriesWarningData;
                        	 xAxisData = res.object.xAxisData;
                         } else {
                             layer.msg(res.message);
                         }
                     },
                     error: function (res) {
                         res = JSON.parse(res.responseText);
                         layer.msg(res.message);
                     }
                 });
                 
                 option = {
                   title: {
                     text: '短信发送统计'
                   },
                   tooltip: {
                     trigger: 'axis'
                   },
                   legend: {
                     data: legendData
                   },
                   grid: {
                     left: '3%',
                     right: '4%',
                     bottom: '3%',
                     containLabel: true
                   },
                   toolbox: {
                	 x:1000,
                     y:0,
                     feature: {
                       saveAsImage: {}
                     }
                   },
                   xAxis: {
                     type: 'category',
                     boundaryGap: false,
                     data: xAxisData
                   },
                   yAxis: {
                     type: 'value'
                   },
                   series: [
                     {
                       name: '总数',
                       type: 'line',
                       data: seriesTotalData
                     },
                     {
                       name: '系统短信',
                       type: 'line',
                       data: seriesSystemData
                     },
                     {
                       name: '普通短信',
                       type: 'line',
                       data: seriesOAData
                     },
                     {
                         name: '当前预警线',
                         type: 'line',
                         data: seriesWarningData
                      },
                   ]
                 };

                 if (option && typeof option === 'object') {
                     myChart.setOption(option);
                 }
                 
            }
            
            form.render();
        });
    };
});
