"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {

            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layedit = layui.layedit,
                zTreeSearch = layui.zTreeSearch;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['500px', '200px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        trasen.setNamesVal($('#evaBaseAddForm'), opt.data);
                        $('#id').val(opt.data.id);
                    }
                    form.render();
                }
            });

            // 保存
            form.on('submit(documentSubmitCofirm)', function (data) {
                var newOriginalName = $("#newOriginalName").val();
                var d = data.field;
                d.originalName = newOriginalName;
                var url = '/ts-document/attachment/update';
                if (!d.id) {
                    delete d.id;
                }
                $.loadings();
                $.ajax({
                    type: "post",
                    url: common.url + url,
                    dateType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.ref();
                        } else {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.ref();
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    }
                });

            });

        })
    };
});