"use strict";

define(function (require, exports, module) {
    var init = function () {
        return perform();
    }

    module.exports = {
        init: init
    }

    //模版页面脚本
    var perform = function () {
        layui.use(['form', 'laytpl', 'tree', 'layedit', 'laydate', 'trasen', 'workflow','zTreeSearch','upload'], function () {
            var form = layui.form,
                laytpl = layui.laytpl,
                layer = layui.layer,
                workflow = layui.workflow,
                trasen = layui.trasen,
                upload = layui.upload,
                laydate = layui.laydate,
                zTreeSearch = layui.zTreeSearch;
				
				  form.render("select");
            
            // 当前操作
 			var opr;
            
            // 时间选择器
            laydate.render({
                elem: '#voluntaryRangeDate',
                type: 'date',
                range: '~',
                done: function(value, date, endDate){
                    var dateArr = value.split(' ~ ');
                    $("#voluntaryStartDate").val(dateArr[0])
                    $("#voluntaryEndDate").val(dateArr[1])
                    refreshTable();
                }
            });
            
            // 表格渲染
            var trasenTable = new $.trasenTable("grid-table-voluntaryTable", {
                url: common.url + '/ts-hrms/voluntary/list',
                pager: 'grid-pager-voluntaryPager',
                shrinkToFit: true,
                mtype: 'post',
                sortname: 'create_date',
                colModel: [
                	{ label: 'ID', name: 'id', index: 'id', width: "auto", editable: false, hidden: true },
                	{ label: '姓名', name: 'employeeName', index: 'employee_name',   width: 70, editable: false,align:'center'},
                	{ label: '工号', name: 'employeeNo', index: 'employee_no', width: 110, editable: false,align:'center'},
					{ label: '手机号', name: 'phoneNumber', index: 'phone_number',   width: 110, editable: false,align:'center'},
                	{ label: '性别', name: 'gender', index: 'gender',   width: 60, editable: false,align:'center'},
                	{ label: '邮箱', name: 'email', index: 'email',   width: 110, editable: false,align:'center'},
                	{ label: '身份证', name: 'identityNumber', index: 'identity_number',   width: 110, editable: false,align:'center'},
                	{ label: '政治面貌', name: 'politicalStatus', index: 'political_status',   width: 70, editable: false,align:'center'},
                	{ label: '学历', name: 'education', index: 'education',   width: 80, editable: false,align:'center'},
					{ label: '科室', name: 'employeeDept', index: 'employee_dept',  width: 140, editable: false,align:'left'},
                    { label: '活动时间', name: 'voluntaryDate', index: 'voluntary_date',  width: 70, editable: false,align:'center' },
                    { label: '活动时长(小时)', name: 'voluntaryDuration', index: 'voluntary_duration', width: 60, editable: false,align:'left'},
					{ label: '活动说明', name: 'remark', index: 'remark', hidden: true},
					{ label: '员工id', name: 'employeeId', index: 'employee_id', hidden: true},
					{ label: '生日', name: 'birthday', index: 'birthday', hidden: true}
					
                ],
               footerrow: true,
                gridComplete: function() {
                    var sum_HJ = $("#grid-table-voluntaryTable").getCol('voluntaryDuration',false,'sum');    
                    $("#grid-table-voluntaryTable").jqGrid("footerData", "set", {employeeDept: "时长合计：",voluntaryDuration: sum_HJ});
                },
                queryFormId: 'voluntaryform'
            });
            
            function refreshTable() {
            	//验证时间
            	var transferStartDate = $("#voluntaryStartDate").val();
            	var transferEndDate = $("#voluntaryEndDate").val();
                if(transferStartDate.length > 0 && transferEndDate.length > 0){   
                    var startTmp=transferStartDate.split("-");
                    var endTmp=transferEndDate.split("-");
                    var sd=new Date(startTmp[0],startTmp[1],startTmp[2]);
                    var ed=new Date(endTmp[0],endTmp[1],endTmp[2]);
                    if(sd.getTime()>ed.getTime()){ 
                    	 layer.msg('开始日期不能大于结束日期'); 
                        return false;   
                    }   
                }else if((transferStartDate.length > 0 && transferEndDate.length == 0) 
                		|| (transferStartDate.length == 0 && transferEndDate.length > 0)){
	               	 layer.msg('时间条件不完整'); 
	                 return false; 
               }
                trasenTable.refresh();
            }
            //查询
            form.on('submit(voluntarySearch)', function (data) {
                refreshTable();
            });

            //重置
            form.on('submit(voluntaryReset)', function (data) {
                $("#voluntaryform")[0].reset();
                form.render();
                refreshTable();
            });
            
            //删除
            $('.areaButtonBoxR').off('click', '#deleteVoluntary').on('click', '#deleteVoluntary', function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条需要删除的数据!')
                    return false;
                }
                layer.confirm('确定要删除当前选中数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                	var uri = '/ts-hrms/voluntary/deletedById/' + rowData.id;
                    trasen.post(common.url + uri, null, function (data) {
                        layer.closeAll();
                        refreshTable()
                    })
                })
            });
			
			//新增
		$('.areaButtonBoxR')
			    .off('click', '#addVoluntary')
			    .on('click', '#addVoluntary', function () {
			        $.quoteFun('voluntary/voluntary/modules/add', {
			            title: '活动登记',
			            ref: refreshTable
			        }); 
		});
		
		$('.areaButtonBoxR')
		    .off('click', '#editVoluntary')
		    .on('click', '#editVoluntary', function () {
		        var rowData = trasenTable.getSelectRowData();
		        $.quoteFun('voluntary/voluntary/modules/add', {
		            title: '编辑活动',
		            data: rowData,
		            ref: refreshTable
		        });
		    });
           
            //下载模板
            $('.areaButtonBoxR').off('click', '#improtTemplate').on('click', '#improtTemplate', function () {
            	location.href = common.url + "/ts-hrms/voluntary/improtTemplate";
            });

            //导入数据
          	upload.render({
                elem: '#improtVoluntary',
                url: common.url + '/ts-hrms/voluntary/excelImport',
                accept: 'file',
                done: function(res) {
                    if (res.success) {
                    	refreshTable();
                        layer.closeAll();
                        layer.msg("导入成功：" + res.object + "条");
                    } else {
                        layer.msg(res.message || "导入失败");
                        layer.close(openIndex);
                    }
                }
            });

            //导出数据
            $(".areaButtonBoxR").off("click", "#exportVoluntary").on("click", "#exportVoluntary", function () {
            	var employeeName = $("#voluntaryform input[name='employeeName']").val();
            	var employeeDept = $("#voluntaryform input[name='employeeDept']").val();
				var politicalStatus = $("#voluntaryform select[name='politicalStatus']").val();
            	var startDate = $("#voluntaryStartDate").val();
            	var endDate = $("#voluntaryEndDate").val();
            	location.href = common.url + "/ts-hrms/voluntary/downLoad2?employeeName="+employeeName+
            	"&employeeDept="+employeeDept+"&startDate="+startDate+"&endDate="+endDate+"&politicalStatus="+politicalStatus;
            });
        });
    };

})

