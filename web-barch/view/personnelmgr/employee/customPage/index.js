"use strict";
define(function (require, exports, module) {
  module.exports = {
    init: init,
    cancle: function () {
      Event.create("personnelInfo").remove("deptId");
      Event.create("personnelInfo").remove("editInfo");
    },
  };

  function init() {
    layui.use(["form", "trasen", "element", "laydate"], function () {
      var form = layui.form;
      var trasen = layui.trasen;
      var element = layui.element;
      var laydate = layui.laydate;

      var treeObj;

      var employeeStatusChoose = null;
      var establishmentTypeChoose = null;
      var orgAttributesChoose = null;
      var employeeCategoryChoose = null;
      var nationalityChoose = null;
      var politicalStatusChoose = null;
      var marriageStatusChoose = null;
      var positionNameChoose = null;
      var personalIdentityChoose = null;
      var operationTypeChoose = null;
      var educationTypeChoose = null;
      var gwdjChoose = null;
      var plgwChoose = null;

      var jobtitleCategoryChoose = null;
      var jobtitleNameChoose = null;
      var menuId = $.cookie("lastMenuIdqx");
      var hos = null;
      var checkSelectArr = []; //多选下拉框
      var isAdmin = false; // 判断是否为管理员
      var isLyszyyBoolean = false; // 判断是否为管理员

      var _orgIds = null;
	    var _hospCode = null;
      var jumpEmployeeNo = null;
      var _sysRoleCode = "";

      var personalIdentityChoose = null;
      var setting = {
        data: {
          simpleData: {
            enable: false,
          },
        },
        check: {
          chkboxType: {
            Y: "s",
            N: "s",
          },
          enable: true,
          chkStyle: "checkbox",
        },
        callback: {
          onClick: function (event, treeId, treeNode) {
            treeObj.expandNode(treeNode);
            treeObj.cancelSelectedNode();
            var nodes = treeObj.getCheckedNodes(true);
            var orgIds = "";
            if (nodes.length > 0) {
              $.each(nodes, function (key, value) {
                orgIds += value.id + ",";
              });
            }
            $('#customEmployeeQueryForm [name="orgIds"]').val(orgIds);
            return false;
          },
          onNodeCreated: function (e, id, node) {},
          onCheck: function (e, id, treeNode) {
            treeObj.cancelSelectedNode();
            var nodes = treeObj.getCheckedNodes(true);
            var orgIds = "";
            if (nodes.length > 0) {
              $.each(nodes, function (key, value) {
                orgIds += value.id + ",";
              });
            }
            $('#customEmployeeQueryForm [name="orgIds"]').val(orgIds);
            initTable();
          },
        },
        view: {
          dblClickExpand: true,
          showTitle: true,
          showLine: false,
        },
      };
      // 判断用户是否为管理员  是 则展示重置密码按钮
      $.ajax({
        url: common.url + "/ts-basics-bottom/user/isAdmin",
        type: "get",
        async: false,
        success: function (res) {
          if (res.success && res.statusCode === 200) {
            isAdmin = res.object;
          }
        },
      });

      if ("lyszyyy" == common.globalSetting.orgCode) {
        isLyszyyBoolean = true;
      }
        // 获取用户科室id
        if (!isAdmin) {
            $.ajax({
                url: common.url + "/ts-system/user/info",
                type: "get",
                async: false,
                success: function (res) {
                    if (res.success) {
                        if (!(res.object.sysRoleCode.indexOf("SYS_ARCHIVIST") != -1) && !(res.object.sysRoleCode.indexOf("GROUP_LEADER") != -1)) {
                          _orgIds = res.object.deptId;
                          _hospCode = res.object.hospCode;
                        }
                        _sysRoleCode = res.object.sysRoleCode;
                    }
                },
            });
        }
      //北海妇幼 才展示同步his按钮

      if ("北海市妇幼保健院" == common.globalSetting.orgCode) {
        if (isAdmin || _sysRoleCode.indexOf("SYS_ARCHIVIST") != -1) {
          $("button[id='synchis']").show();
        }
      } else {
        $("button[id='synchis']").hide();
      }

      if ("csjkyy" == common.globalSetting.orgCode) {
        $("div[key='shifouxinglinrencai']").remove();
        $("div[key='zhuanyeyingcai']").remove();
        $("div[key='shifouzhongcengganbu']").remove();
        $("div[key='shifouguipeirenyuan']").remove();
      }



      function initTree() {
        $.ajax({
          url: common.url + "/ts-basics-bottom/organization/getTree3",
          type: "post",
          async: false,
          success: function (res) {
            if (res.success) {
              var zNodes = res.object;
              treeObj = $.fn.zTree.init(
                $("#customEmployeeTree"),
                setting,
                zNodes
              );
              //展开所有节点 SYS_ARCHIVIST
              if (!isAdmin && !(_sysRoleCode.indexOf("SYS_ARCHIVIST") != -1) && !(_sysRoleCode.indexOf("GROUP_LEADER") != -1)) {  //人事管理员
                // 迁移sass版本 保留（可能无用）
                // treeObj.expandAll(true);
                // var node = treeObj.getNodeByParam("id", _orgIds);
                // treeObj.checkNode(node, true, true);
                // treeObj.setChkDisabled(node, true);
                // var fNodes = treeObj.getCheckedNodes(false);
                // for (var i = 0, l = fNodes.length; i < l; i++) {
                //   treeObj.setChkDisabled(fNodes[i], true);
                // }

                treeObj.expandAll(true);
                var node = treeObj.getNodeByParam("id", _orgIds);
				        var hospCodeNode = treeObj.getNodeByParam("id", _hospCode);
                console.log(hospCodeNode);
                if(hospCodeNode != null){
                  treeObj.setChkDisabled(hospCodeNode, true);
                }
				
                // var fNodes = treeObj.getCheckedNodes(false);
                // for (var i = 0, l = fNodes.length; i < l; i++) {
                //   treeObj.setChkDisabled(fNodes[i], true);
                // }
              }
            } else {
              layer.msg("树加载失败.");
            }
          },
        });
      }

      //获取字段

      initTree();
      var nodesLists = treeObj.getNodes();
      //getNum(nodesLists);
      initNodeNum(nodesLists);
      //展示获取子节点的数目
      function initNodeNum(nodesLists) {
        var nodes = nodesLists; //获取 zTree 当前显示的节点数据
        var nodess = treeObj.transformToArray(nodes); //获取所有节点
        for (var i = 0, l = nodess.length; i < l; i++) {
          //遍历节点数据
          var nodeId = nodess[i].id; //记录节点id
          var nodeName = nodess[i].name; //记录节点名称
          var isParent = nodess[i].isParent; //记录节点是否是父
          var pId = nodess[i].pId; //记录节点父id
          if (isParent) {
            //如果是父节点
            var count = 0;
            count = count + nodess[i].pNumber;
            count = getAllChildrenNodes(nodess[i], count);
            var orgCount = "";
            var orgName = "";
            var newname = "";
            if (nodeName.indexOf("〔") > -1) {
              orgCount = nodeName.substring(
                nodeName.indexOf("〔") + 1,
                nodeName.length - 1
              ); //人数
              orgName = nodeName.substring(0, nodeName.indexOf("〔")); //名称
              newname = orgName + "〔" + count + "〕"; //计算上节点数并加上括号   〔〕
            } else {
              newname = nodeName + "〔" + count + "〕"; //计算上节点数并加上括号   〔〕
            }
            nodess[i].name = newname; //重新命名
          } else {
            var newName = nodess[i].name;
            nodess[i].name = newName + "〔" + nodess[i].pNumber + "〕"; //重新命名
          }
          treeObj.updateNode(nodess[i]); //并更新节点信息
        }
      }

      function getAllChildrenNodes(treeNode, count) {
        //给定一个节点对象
        if (treeNode.isParent) {
          //如果是父
          var childrenNodes = treeNode.children;
          if (childrenNodes) {
            for (var i = 0; i < childrenNodes.length; i++) {
              count += childrenNodes[i].pNumber;
              count = getAllChildrenNodes(childrenNodes[i], count);
            }
          }
        }
        return count;
      }

      $.each($("#customEmployee .oa-date"), function () {
        laydate.render({
          elem: this,
          trigger: "click",
        });
      });
      $("#customEmployee")
        .off("input", "#customEmployeeTreeSearch")
        .on(
          "input",
          "#customEmployeeTreeSearch",
          common.debounce(function () {
            var val = $(this).val();
            $.fn.ztreeQueryHandler(treeObj, val);
          }, 200)
        );

      form.render("select");
      var list = [];
      if (common.globalSetting.orgCode == 'pjxdyrmyy') {
        list = [
          {
            itemName: "在职",
            itemCode: "1",
          },
          {
            itemName: "试用期",
            itemCode: "99",
          },
          {
            itemName: "借调",
            itemCode: "9",
          },
        ]
      } else {
        list = [
          {
            itemName: "在职",
            itemCode: "1",
          },
          {
            itemName: "院内返聘",
            itemCode: "6",
          },
          {
            itemName: "院外返聘",
            itemCode: "12",
          },
          {
            itemName: "试用期",
            itemCode: "99",
          },
          {
            itemName: "借调",
            itemCode: "9",
          },
        ]
      }
      employeeStatusChoose = new $.checkSelect(
        "#customEmployeeQueryForm #employeeStatusChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "employee_status",
          },
          default: list,
          value: "itemCode",
          condition: "condition",
        }
      );
      //编制类型
      establishmentTypeChoose = new $.checkSelect(
        "#customEmployeeScreening #establishmentTypeChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "establishment_type",
          },
          value: "itemCode",
          condition: "condition",
        }
      );

      //人员类别
      orgAttributesChoose = new $.checkSelect(
        "#customEmployeeScreening #orgAttributesChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "ORG_ATTRIBUTES",
          },
          value: "itemCode",
          condition: "condition",
        }
      );

      //编制类别
      employeeCategoryChoose = new $.checkSelect(
        "#customEmployeeScreening #employeeCategoryChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "employee_category",
          },
          value: "itemCode",
          condition: "condition",
        }
      );

      //民族
      nationalityChoose = new $.checkSelect(
        "#customEmployeeScreening #nationalityChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "nationality_name",
          },
          value: "itemCode",
          condition: "condition",
        }
      );
      //政治面貌
      politicalStatusChoose = new $.checkSelect(
        "#customEmployeeScreening #politicalStatusChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "political_status",
          },
          value: "itemCode",
          condition: "condition",
        }
      );
      //婚姻状况
      marriageStatusChoose = new $.checkSelect(
        "#customEmployeeScreening #marriageStatusChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "marriage_status",
          },
          value: "itemCode",
          condition: "condition",
        }
      );
      //职务
      positionNameChoose = new $.checkSelect(
        "#customEmployeeScreening #positionNameChoose",
        {
          url: "/ts-basics-bottom/position/list",
          type: "post", //请求类型
          datatype: "json",
          label: "positionName",
          value: "positionId",
          condition: "condition",
        }
      );
      //岗位名称
      personalIdentityChoose = new $.checkSelect(
        "#customEmployeeScreening #personalIdentityChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "personal_identity",
          },
          value: "itemCode",
          condition: "condition",
        }
      );
      //执业类别
      operationTypeChoose = new $.checkSelect(
        "#customEmployeeScreening #operationTypeChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "operation_type",
          },
          value: "itemCode",
          condition: "condition",
        }
      );

      //岗位类别
      plgwChoose = new $.checkSelect("#customEmployeeScreening #plgwChoose", {
        url: "/ts-basics-bottom/dictItem/list",
        type: "post", //请求类型
        datatype: "json",
        label: "itemName",
        postData: {
          dicTypeId: "post_category",
        },
        value: "itemCode",
        condition: "condition",
      });

      //岗位等级
      gwdjChoose = new $.checkSelect("#customEmployeeScreening #gwdjChoose", {
        url: "/ts-basics-bottom/post/getPageList",
        type: "post", //请求类型
        datatype: "json",
        label: "postName",
        // postData: {
        //     postName: $("#plgwChoose").val(),
        // },
        value: "postId",
        condition: "postName",
      });
      //学历类型
      educationTypeChoose = new $.checkSelect(
        "#customEmployeeScreening #educationTypeChoose",
        {
          url: "/ts-basics-bottom/dictItem/list",
          type: "post", //请求类型
          datatype: "json",
          label: "itemName",
          postData: {
            dicTypeId: "education_type",
          },
          value: "itemCode",
          condition: "condition",
        }
      );
      //职称类别
      jobtitleCategoryChoose = new $.checkSelect(
        "#customEmployeeScreening #jobtitleCategoryChoose",
        {
          url: "/ts-basics-bottom/jobtitleBasic/getCategoryPageList",
          type: "post", //请求类型
          datatype: "json",
          label: "jobtitleBasicName",
          postData: {
            jobtitleBasicPid: "0",
          },
          value: "jobtitleBasicId",
          condition: "jobtitleBasicName",
        }
      );

      //职称名称
      jobtitleNameChoose = new $.checkSelect(
        "#customEmployeeScreening #jobtitleNameChoose",
        {
          url: "/ts-basics-bottom/jobtitleBasic/getCategoryPageList2",
          type: "post", //请求类型
          datatype: "json",
          label: "jobtitleBasicName",
          postData: {
            jobtitleBasicGrade: "3",
          },
          value: "jobtitleBasicId",
          condition: "jobtitleBasicName",
        }
      );
      $('#customEmployeeScreening [name="startAge"]')
        .off("blur")
        .on("blur", checkAgeRangeQualified);
      $('#customEmployeeScreening [name="endAge"]')
        .off("blur")
        .on("blur", checkAgeRangeQualified);
      /**@desc 检查年龄区间是否合格 */
      function checkAgeRangeQualified() {
        let startAge = $('#customEmployeeScreening [name="startAge"]').val(),
          endAge = $('#customEmployeeScreening [name="endAge"]').val();
        if (startAge && endAge && startAge > endAge) {
          $(this).val("");
          layer.msg("请输入正确的年龄区间");
        }
      }
      var isListen = false;
      var trasenTable = null;

      function initTable() {
        if (trasenTable) {
          refTable();
        } else {
          trasenTableFun();
        }
      }

      Event.create("personnelInfo").listen("editInfo", function (id) {
        jumpEmployeeNo = id;

        if (trasenTable) {
          // postData缓存问题
          var queryData = trasenTable.oTable.getGridParam("postData");
          queryData.employeeNo = jumpEmployeeNo;
        }
        initTable();

        let findDom = [];
        let findDomInterval = null;
        findDomInterval = setInterval(() => {
          findDom = $("#grid-table-customEmployee_frozen").find("tr[id=1]");
          if (findDom && findDom.length) {
            $(findDom).find(".edit").trigger("click");
            clearInterval(findDomInterval);
          }
        }, 500);
      });

      //模块监听
      Event.create("personnelInfo").listen("deptId", function (id) {
        isListen = true;
        _orgIds = "";
        var node = treeObj.getNodeByParam("id", id);
        treeObj.checkAllNodes(false);
        treeObj.checkNode(node, true, true);
        var checkedNodes = treeObj.getCheckedNodes(true);
        if (checkedNodes.length > 0) {
          $.each(checkedNodes, function (key, value) {
            _orgIds += value.id + ",";
          });
          var level = node.level;
          var curNode = node;

          function otherNodeFilter(item) {
            return item.level == curNode.level && item.id != curNode.id;
          }
          //折叠兄弟节点
          var otherNodes = treeObj.getNodesByFilter(otherNodeFilter);
          $.each(otherNodes, function (key, value) {
            if (otherNodes[key].open)
              treeObj.expandNode(otherNodes[key], false, true, true);
          });
          //展开自身节点及子节点
          if (!node.open && node.level != 0)
            treeObj.expandNode(node, true, true, true);
          //展开所有父节点，折叠其他非父节点及其子节点
          while (level) {
            curNode = curNode.getParentNode();
            var pOtherNodes = treeObj.getNodesByFilter(otherNodeFilter);
            $.each(pOtherNodes, function (key, value) {
              if (pOtherNodes[key].open)
                treeObj.expandNode(pOtherNodes[key], false, true, true);
            });
            if (!curNode.open) treeObj.expandNode(curNode, true);
            level = curNode.level;
          }
        }
        $('#customEmployeeQueryForm [name="orgIds"]').val(_orgIds);
        initTable();
      });

      if (!isListen) {
        initTable();
      }

      function trasenTableFun() {
        var columnArray = [];
        // 先获取要显示的字段
        $.ajax({
          type: "post",
          contentType: "application/json; charset=utf-8",
          url: common.url + "/ts-basics-bottom/commemployeesho/getList",
          async: false,
          success: function (res) {
            if (res.success) {
              columnArray = res.object;
            }
          },
        });

        for (var i = 0; i < columnArray.length; i++) {
          if ("employee_name" == columnArray[i].name) {
            columnArray[i].formatter = function (cell, opt, row) {
              if (row.disable_status == 0) {
                return `
                  <font 
                    color="red" 
                    data-row-id=${opt.rowId} 
                    name="employeeName"
                  >${row.employee_name}</font>`;
              } else {
                let processIcon = '';
                if(row.operationId) {
                  processIcon = `<img 
                            class="show-process"
                            style="height: 22px;vertical-align: middle;margin: 0 4px;cursor: pointer;" 
                            src="/static/img/other/from_process.svg"/>`;
                }
                return `
                  ${processIcon}
                  <span 
                    data-row-id=${opt.rowId}
                    name="employeeName"
                    class="detail-span"
                  >
                    ${row.employee_name}
                  </span>`;
              }
            };
          }
        }
        let defaultVal = ''
        if (common.globalSetting.orgCode == 'pjxdyrmyy') {
          defaultVal = '1,99,9';
        } else {
          defaultVal = '1,6,12,99,9';
        }
        trasenTable = new $.trasenTable("grid-table-customEmployee", {
          url: "/ts-basics-bottom/cusotm/getEmployeePageListByCustom",
          pager: "grid-pager-customEmployee",
          mtype: "post",
          async: false,
          autoScroll: false,
          shrinkToFit: false,
          autowidth: false,
          sortable: false,
		  rowList: [30, 50, 100, 200,2000,3000],
          postData: {
            employeeStatuses: defaultVal,
            orgIds: _orgIds,
            employeeNo: jumpEmployeeNo || "",
          },
          // shrinkToFit: true,
          colModel: [
            {
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: "",
              sortable: false,
              width: 75,
              editable: false,
              align: "center",
              title: false,
              frozen: true,
              classes: "visible jqgrid-rownum ui-state-default",
              formatter: function (cell, opt, row) {
                var html =
                  `<div class="table-more-btn">
                      <div class="more-btn">
                        <i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i>
                      </div>
                      <div class="more-box">
                  `;
                var btns = "";
                btns =`
                  <button 
                    class="layui-btn edit person-operation"
                    row-id=${opt.rowId}
                  >
                    <img class="person-operation-icon" src="/static/img/other/person-info-edit.svg" />
                    编辑
                  </button>`;
                if (isAdmin) {
                  btns += `
                    <button 
                      class="layui-btn deletedEmployeeBut person-operation"
                      row-id=${opt.rowId}
                    >
                      <img class="person-operation-icon" src="/static/img/other/person-info-del.svg" />
                      删除
                    </button>`;
                }

                // 人事管理员和系统管理员有重置密码的权限
                if (isAdmin || _sysRoleCode.indexOf("SYS_ARCHIVIST") != -1) {
				  if ("lyszyyy" == common.globalSetting.orgCode) {
					 btns += `
					   <button 
					     class="layui-btn jobDescriptionBtn person-operation" 
					     row-id="${opt.rowId}" 
					     id="NewJobDescriptionBtn"
					   >
					     <img class="person-operation-icon" src="/static/img/other/person-info-post.svg" />
					     岗位职责
					   </button>`;
				  }
                  btns += `
                    <button 
                      class="layui-btn person-operation" 
                      row-id=${opt.rowId} 
                      id="PersonnelSettingBtn"
                    >
                      <img class="person-operation-icon" src="/static/img/other/person-info-password.svg" />
                      员工设置
                    </button>`;
                  btns += `
                    <button 
                      class="layui-btn person-operation" 
                      row-id=${opt.rowId} 
                      id="ResetPasswordBtn"
                    >
                      <img class="person-operation-icon" src="/static/img/other/person-info-password.svg" />
                      重置密码
                    </button>`;
                  btns +=`
                    <button 
                      class="layui-btn person-operation" 
                      row-id=${opt.rowId}
                      id="ResetJobNumberBtn"
                    >
                      <img class="person-operation-icon" src="/static/img/other/person-info-number.svg" />
                      更新工号
                    </button>`;
                  btns += `
                    <button 
                      class="layui-btn person-operation" 
                      row-id=${opt.rowId} 
                      id="empQiyongBtn"
                    >
                      <img class="person-operation-icon" src="/static/img/other/qidong.svg" />
                      启用账号
                    </button>`;
                  btns +=`
                    <button 
                      class="layui-btn person-operation" 
                      row-id=${opt.rowId} 
                      id="empDisableBtn"
                    >
                      <img class="person-operation-icon" src="/static/img/other/outofservice.svg" />
                      停用账号
                    </button>`;
				btns +=`
				  <button 
					class="layui-btn person-operation" 
					row-id=${opt.rowId} 
					id="empUnlockBtn"
				  >
					<img class="person-operation-icon" src="/static/img/other/unlock.svg" />
					解锁账号
				  </button>`;
                }
                if (isLyszyyBoolean) {
                  btns += `
                    <button 
                      class="layui-btn person-operation"
                      row-id=${opt.rowId} 
                      id="empCollection"
                    >
                      <img class="person-operation-icon" src="/static/img/other/person-info-post.svg" />
                      信息采集
                    </button>`;
                }
                html +=`${btns}
                  </div></div>`;
                return html;
              },
            },
            ...columnArray,
          ],
          buidQueryParams: function () {
            var search = $("#customEmployeeQueryForm").serializeArray();
            var opt = $("#customEmployeeScreening").serializeArray();
            var data = {};
            for (var i in search) {
              opt.push(search[i]);
            }
            for (var i in opt) {
              data[opt[i].name] = opt[i].value;
            }
            var treeObj = $.fn.zTree.getZTreeObj("customEmployeeTree");
            var nodes = treeObj.getCheckedNodes(true);
            var orgIds = "";
            for (var i = 0; i < nodes.length; i++) {
              orgIds += nodes[i].id + ",";
            }

            if (!data.orgIds) {
              data.orgIds = orgIds;
            }

            if (!data.employeeStatuses) {
              data.employeeStatuses = arrToString(
                employeeStatusChoose.getSelData() || [],
                "itemCode"
              );
            }
            if (!data.establishmentTypes) {
              data.establishmentTypes = arrToString(
                establishmentTypeChoose.getSelData() || [],
                "itemCode"
              );
            }
            if (!data.orgAttributestypes) {
              data.orgAttributestypes = arrToString(
                orgAttributesChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.politicalStatuses) {
              data.politicalStatuses = arrToString(
                politicalStatusChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.nationalityes) {
              data.nationalityes = arrToString(
                nationalityChoose.getSelData() || [],
                "itemCode"
              );
            }
            if (!data.marriageStatuses) {
              data.marriageStatuses = arrToString(
                marriageStatusChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.positionNames) {
              data.positionNames = arrToString(
                positionNameChoose.getSelData() || [],
                "positionId"
              );
            }

            if (!data.personalIdentitys) {
              data.personalIdentitys = arrToString(
                personalIdentityChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.operationTypes) {
              data.operationTypes = arrToString(
                operationTypeChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.employeeCategorys) {
              data.employeeCategorys = arrToString(
                employeeCategoryChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.educationTypes) {
              data.educationTypes = arrToString(
                educationTypeChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.plgws) {
              data.plgws = arrToString(
                plgwChoose.getSelData() || [],
                "itemCode"
              );
            }

            if (!data.gwdjs) {
              data.gwdjs = arrToString(gwdjChoose.getSelData() || [], "postId");
            }

            if (!data.jobtitleName) {
              data.jobtitleName = arrToString(
                jobtitleNameChoose.getSelData() || [],
                "jobtitleBasicId"
              );
            }

            if (!data.jobtitleCategory) {
              data.jobtitleCategory = arrToString(
                jobtitleCategoryChoose.getSelData() || [],
                "jobtitleBasicId"
              );
            }

            return data;
          },
        });
        trasenTable.oTable.jqGrid("setFrozenColumns");
      }

      function refTable() {
        trasenTable.refresh();
      }

      function arrToString(arr, field) {
        var str = "";
        for (var i = 0; i < arr.length; i++) {
          str += arr[i][field] + ",";
        }
        if (str.length > 0) {
          str = str.substr(0, str.length - 1);
        }
        return str;
      }

      $('#customEmployee').off('click', '.show-process').on('click', '.show-process', function () {
        var rowid = $(this).attr("row-id");
        var data = trasenTable.getSourceRowData(rowid);
        var rowData = common.deepClone(data);
        rowData.createUserName = rowData.wfCreateUserName;
        rowData.createUserDeptName = rowData.wfCreateUserDeptName;

        $.quoteFun('personnelmgr/employee/approval/approval', {
            data: {
                details: 1,
                taskId: rowData.taskId,
                wfInstanceId: rowData.wfInstanceId,
                workflowNumber: rowData.workflowNumber,
                currentStepNo: rowData.currentStepNo || 'end',
                currentStepName: rowData.wfCurrentStepName || '结束',
                id: rowData.operationId,
            },
            rowData: rowData,
            ref: refTable,
        });
        return false;
      });

      $("#customEmployeeButDiv")
        .off("click", "#exportBtn")
        .on("click", "#exportBtn", function () {
          var queryData = trasenTable.oTable.getGridParam("postData");
          var url =
            common.url +
            "/ts-basics-bottom/cusotmEmployee/exportColumnEmployee?";
          var exportParam = "";
          for (var key in queryData) {
            exportParam += key + "=" + queryData[key] + "&";
          }
          location.href = url + exportParam;
        });

      initFilterSel();
      //检索
      $("#customEmployee")
        .off("click", "#customEmployeeSearch,#customEmployeeScreeningSub")
        .on(
          "click",
          "#customEmployeeSearch,#customEmployeeScreeningSub",
          function () {
            refTable();
            return false;
          }
        );
      $("#customEmployee")
        .off("click", "#customEmployeeRefreshBtn,#customEmployeeScreenCRest")
        .on(
          "click",
          "#customEmployeeRefreshBtn,#customEmployeeScreenCRest",
          function () {
            $("#customEmployeeScreening")[0].reset();
            $("#customEmployeeQueryForm")[0].reset();

            establishmentTypeChoose.setSelData([]);
            orgAttributesChoose.setSelData([]);
            politicalStatusChoose.setSelData([]);
            nationalityChoose.setSelData([]);
            marriageStatusChoose.setSelData([]);
            positionNameChoose.setSelData([]);
            personalIdentityChoose.setSelData([]);
            operationTypeChoose.setSelData([]);

            educationTypeChoose.setSelData([]);
            plgwChoose.setSelData([]);
            gwdjChoose.setSelData([]);
            employeeCategoryChoose.setSelData([]);

            $(".conditionHiddenDiv input").each(function () {
              $(this).val("");
            });
            initFilterSel();
            refTable();
            return false;
          }
        );

      // 新增员工档案
      $("#customEmployee")
        .off("click", "#add")
        .on("click", "#add", function () {
          $.quoteFun("personnelmgr/employee/customPage/add", {
            type: "add",
            ref: refTable,
          });
        });

      //导入
      $("#customEmployee #personnelmgrEmployeeTableImport").funs(
        "click",
        function () {
          $.quoteFun("personnelmgr/employee/customPage/upload", {
            ref: refTable,
          });
        }
      );
      
      //重置密码
      $("#customEmployee")
        .off("click", "#ResetPasswordBtn")
        .on("click", "#ResetPasswordBtn", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.quoteFun("personnelmgr/employee/customPage/resetPassword", {
            title: "重置密码",
            ref: refTable,
            data: {
              empCode: rowData.employee_no,
            },
          });
        });

      //重置工号
      $("#customEmployee")
        .off("click", "#ResetJobNumberBtn")
        .on("click", "#ResetJobNumberBtn", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.quoteFun("personnelmgr/employee/customPage/resetJobNumber", {
            title: "重置工号",
            ref: refTable,
            data: {
              empId: rowData.employee_id,
              empNo: rowData.employee_no,
            },
          });
        });
      
      //信息采集
      $("#customEmployee")
        .off("click", "#empCollection")
        .on("click", "#empCollection", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);

          $.quoteFun("personnelmgr/employee/customPage/empCollection", {
            ref: refTable,
            employeeId: rowData.employee_id,
          });
        });

      //启用
      $("#customEmployee")
        .off("click", "#empQiyongBtn")
        .on("click", "#empQiyongBtn", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.ajax({
            url: "/ts-basics-bottom/cusotmEmployee/disable",
            contentType: "application/json",
            method: "post",
            data: JSON.stringify({
              id: rowData.employee_id,
              status: "1",
            }),
            success: function (res) {
              if (res.success) {
                $(
                  "#grid-table-customEmployee_frozen tr[id='" +
                    rowid +
                    "'] td[aria-describedby='grid-table-customEmployee_employee_name']"
                ).html(rowData.employee_name);
                layer.msg("操作成功");
              } else {
                layer.msg(res.message || "操作失败");
              }
            },
            error: function () {
              layer.msg("操作失败");
            },
          });
        });

      //停用
      $("#customEmployee")
        .off("click", "#empDisableBtn")
        .on("click", "#empDisableBtn", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.ajax({
            url: "/ts-basics-bottom/cusotmEmployee/disable",
            contentType: "application/json",
            method: "post",
            data: JSON.stringify({
              id: rowData.employee_id,
              status: "0",
            }),
            success: function (res) {
              if (res.success) {
                layer.msg("操作成功");
                $(
                  "#grid-table-customEmployee_frozen tr[id='" +
                    rowid +
                    "'] td[aria-describedby='grid-table-customEmployee_employee_name']"
                ).html(
                  "<font color='red'>" + rowData.employee_name + "</font>"
                );
              } else {
                layer.msg(res.message || "操作失败");
              }
            },
            error: function () {
              layer.msg("操作失败");
            },
          });
        });
      
	  $("#customEmployee")
	    .off("click", "#empUnlockBtn")
	    .on("click", "#empUnlockBtn", function () {
	      var rowid = $(this).attr("row-id");
	      var rowData = trasenTable.getSourceRowData(rowid);
	      $.ajax({
	        url: "/ts-hrms/employee/unlock",
	        contentType: "application/json",
	        method: "post",
	        data: JSON.stringify({
	          id: rowData.employee_id
	        }),
	        success: function (res) {
	          if (res.success) {
	            layer.msg(res.message || "解锁成功");
	          } else {
	            layer.msg(res.message || "操作失败");
	          }
	        },
	        error: function () {
	          layer.msg("操作失败");
	        },
	      });
	    });
	  
      //删除
      $("#customEmployee")
        .off("click", ".deletedEmployeeBut")
        .on("click", ".deletedEmployeeBut", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          var id = rowData.employee_id;
          if (!id) {
            layer.msg("请选择一条需要删除的数据！");
            return false;
          }
          layer.confirm(
            "确定要删除该条数据吗？",
            {
              btn: ["确定", "取消"],
              title: "提示",
              closeBtn: 0,
            },
            function (index) {
              layer.close(index);
              $.ajax({
                type: "post",
                contentType: "application/json; charset=utf-8",
                url: "/ts-basics-bottom/cusotmEmployee/deleteById/" + id,
                success: function (resp) {
                  if (resp.success) {
                    refTable();
                    layer.closeAll();
                    layer.msg("操作成功");
                  } else {
                    layer.msg(resp.message || "操作失败！");
                  }
                },
              });
            }
          );
        });

      //编辑
      $("#customEmployee")
        .off("click", ".edit")
        .on("click", ".edit", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.ajax({
            url:
              "/ts-basics-bottom/cusotmEmployee/findByIdAndDetails/" +
              rowData.employee_id,
            method: "post",
            success: function (res) {
              if (res.success) {
                $.quoteFun("personnelmgr/employee/customPage/add", {
                  type: "edit",
                  title: "员工档案编辑",
                  ref: () => {
                    if (jumpEmployeeNo) {
                      // 清除postData缓存问题
                      var queryData =
                        trasenTable.oTable.getGridParam("postData");
                      delete queryData.employeeNo;

                      jumpEmployeeNo = "";
                      window.dispatchEvent(
                        new CustomEvent("sendToNewFrameMessage", {
                          detail: {
                            type: "dispatchPoliticalEditDialog",
                            data: {
                              jumpEmployeeNo,
                            },
                          },
                        })
                      );
                    }
                    refTable();
                  },
                  data: res.object,
                  employeeId: rowData.employee_id,
                });
              } else {
                layer.msg(res.message || "查询个人信息失败");
              }
            },
          });
        });

      // 查看详情
      $("#customEmployee")
        .off("click", '[name="employeeName"]')
        .on("click", ' [name="employeeName"]', function () {
          var rowid = $(this).attr("data-row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.ajax({
            url:
              "/ts-basics-bottom/cusotmEmployee/findByIdAndDetails/" +
              rowData.employee_id,
            method: "post",
            success: function (res) {
              if (res.success) {
                $.quoteFun("personnelmgr/employee/customPage/portrait", {
                  type: "preview",
                  title: "人才画像",
                  ref: refTable,
                  data: res.object,
                  employeeId: rowData.employee_id,
                });
              } else {
                layer.msg(res.message || "查询个人信息失败");
              }
            },
          });
        });

      //岗位说明书
      // $('#customEmployee').off('click', '.jobDescriptionBtn').on('click', '.jobDescriptionBtn', function () {
      //     var rowid = $(this).attr('row-id');
      //     var rowData = trasenTable.getSourceRowData(rowid);
      //     $.quoteFun('personnelmgr/employee/customPage/jobDescription', {
      //         type: 'edit',
      //         title: '岗位说明书',
      //         data: rowData
      //     });
      // });

      // 新岗位说明书
      $("#customEmployee")
        .off("click", "#NewJobDescriptionBtn")
        .on("click", "#NewJobDescriptionBtn", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.quoteFun("personnelmgr/employee/customPage/NewJobDescription", {
            title: "岗位说明书",
            data: rowData,
          });
        });
      
      //员工设置
      $("#customEmployee")
        .off("click", "#PersonnelSettingBtn")
        .on("click", "#PersonnelSettingBtn", function () {
          var rowid = $(this).attr("row-id");
          var rowData = trasenTable.getSourceRowData(rowid);
          $.quoteFun("personnelmgr/employee/customPage/PersonnelSetting", {
            title: "员工设置",
            data: rowData,
          });
        });

      // 同步his接口
      $("#customEmployee")
        .off("click", "#synchis")
        .on("click", "#synchis", function () {
          $.ajax({
            url: "/ts-basics-bottom/syncplatform/bhfyempsync",
            method: "post",
            success: function (res) {
              if (res.success) {
                layer.msg(res.message || "同步成功");
                refTable();
              } else {
                layer.msg(res.message || "同步失败、请联系管理员");
              }
            },
          });
        });

      //全选
      $("#customEmployee")
        .off("click", "#selectAllBtn")
        .on("click", "#selectAllBtn", function () {
          const inputCheckboxArr = $(
            '#customEmployee .checkbox-item-box input[name="filterCheck"]'
          );
          $.each(inputCheckboxArr, function (index, obj) {
            $(obj).attr("checked", "checked");
          });
        });

      //初始化筛选器
      function initFilterSel() {
        new $.selectPlug("#customEmployee #filterSelectDemand", {
          url: "/ts-basics-bottom/personalFilter/getList",
          datatype: "POST", //请求类型
          searchType: "json",
          data: {
            menuId: menuId,
          },
          textName: "filterName",
          valName: "id",
          inpTextName: "filterName",
          inpValName: "filterId",
          condition: "", // 检索,
          callback: function (res) {
            if (JSON.stringify(res) == "{}") {
              form.render();
              refTable();
            }
            if (res && res.id) {
              getFilterContent(res.id);
            }
          },
        });
      }

      function getFilterContent(id) {
        $.ajax({
          method: "post",
          url: "/ts-basics-bottom/personalFilter/findById/" + id,
        }).then(function (res) {
          if (res.success) {
            var data = res.obejct;
            var filterCon = JSON.parse(res.object.conditionMap);
            $("#conditionMap").val(filterCon);
            trasen.setNamesVal($("#customEmployeeQueryForm"), filterCon);
            trasen.setNamesVal($("#customEmployeeScreening"), filterCon);
            form.render();
            refTable();
          }
        });
      }

      $("#customEmployee")
        .off("click", "#filterSaveBtn")
        .on("click", "#filterSaveBtn", function () {
          var data = getFilterCon();
          $.quoteFun("personnelmgr/employee/customPage/filterCon/index", {
            title: "保存筛选器",
            menuId: menuId,
            data: data,
            filterId: $('#product [name="filterId"]').val(),
            filterName: $('#product [name="filterName"]').val(),
            type: "save",
            ref: initFilterSel,
          });
        });

      //删除筛选器
      $("#customEmployee")
        .off("click", "#filterDelBtn")
        .on("click", "#filterDelBtn", function () {
          if (!$('#customEmployee [name="filterId"]').val()) {
            layer.msg("请先选择一个筛选器");
            return false;
          }
          layer.confirm(
            "确定要删除筛选器吗？",
            {
              btn: ["确定", "取消"],
              title: "提示",
              closeBtn: 0,
            },
            function (index) {
              $.ajax({
                url:
                  "/ts-basics-bottom/personalFilter/deleteById/" +
                  $('#customEmployee [name="filterId"]').val(),
                type: "post",
                contentType: "application/json; charset=utf-8",
                success: function (resp) {
                  if (resp && resp.success) {
                    layer.msg("删除成功");
                    initFilterSel();
                  } else {
                    layer.msg("操作失败");
                  }
                },
              });
            },
            function () {}
          );
        });

      function getFilterCon() {
        var arr = $("#customEmployeeQueryForm").serializeArray();
        var data = {};
        $.each(arr, function (i, v) {
          data[v.name] = v.value;
        });
        var filterArr = $("#customEmployeeScreening").serializeArray();
        var newData = {};

        // newData.dealUser = data.dealUser;
        $.each(filterArr, function (i, v) {
          data[v.name] = v.value;
        });

        data.nationalityes = arrToString(
          nationalityChoose.getSelData(),
          "itemCode"
        );
        data.employeeStatuses = arrToString(
          employeeStatusChoose.getSelData(),
          "itemCode"
        );
        data.establishmentTypes = arrToString(
          establishmentTypeChoose.getSelData(),
          "itemCode"
        );
        // data.orgAttributestypes = arrToString(
        //   orgAttributesChoosed.getSelData(),
        //   "itemCode"
        // );
        data.politicalStatuses = arrToString(
          politicalStatusChoose.getSelData(),
          "itemCode"
        );
        data.marriageStatuses = arrToString(
          marriageStatusChoose.getSelData(),
          "itemCode"
        );
        data.positionNames = arrToString(
          positionNameChoose.getSelData(),
          "positionId"
        );
        data.personalIdentitys = arrToString(
          personalIdentityChoose.getSelData(),
          "itemCode"
        );
        data.operationTypes = arrToString(
          operationTypeChoose.getSelData(),
          "itemCode"
        );
        data.employeeCategorys = arrToString(
          employeeCategoryChoose.getSelData(),
          "itemCode"
        );

        data.educationTypes = arrToString(
          educationTypeChoose.getSelData(),
          "itemCode"
        );
        data.plgws = arrToString(plgwChoose.getSelData(), "itemCode");

        data.gwdjs = arrToString(gwdjChoose.getSelData(), "postId");
        data.jobtitleName = arrToString(
          jobtitleNameChoose.getSelData(),
          "jobtitleBasicId"
        );
        data.jobtitleCategory = arrToString(
          jobtitleCategoryChoose.getSelData(),
          "jobtitleBasicId"
        );

        var nodes = treeObj.getCheckedNodes(true);
        var orgIds = "";
        for (var i = 0; i < nodes.length; i++) {
          orgIds += nodes[i].id + ",";
        }
        if (orgIds != "") {
          data.orgIds = orgIds;
        }
        return data;
      }

      $("#filterExportBtn").on("click", function () {
        var _url = common.url + "/ts-basics-bottom/employee/getEmployeeFields";
        $.ajax({
          type: "post",
          contentType: "application/json; charset=utf-8",
          url: _url,
          data: {},
          success: function (res) {
            if (res.success) {
              var html = "";
              var _t = "";
              $.each(res.object, function (i, v) {
                $.each(v, function (key, value) {
                  if (
                    value == "员工姓名" ||
                    value == "员工工号" ||
                    value == "序号"
                  ) {
                    html +=
                      '<li> <input name="filterCheck" type="checkbox" value="' +
                      key +
                      '" filedName=' +
                      value +
                      " checked /><label>" +
                      value +
                      "</label></li>";
                  } else {
                    html +=
                      '<li> <input name="filterCheck" type="checkbox" value="' +
                      key +
                      '" filedName=' +
                      value +
                      " /><label>" +
                      value +
                      "</label></li>";
                  }
                });
              });
              $("#employeeFiledUl").html(html);
            }
          },
        });
      });

      $("#customEmployee #filterCancel").on("click", function () {
        $("#customEmployee #customEmployeeExportDialog").removeClass("show");
      });

      $("#filterSubmit").on("click", function () {
        var search = $("#customEmployeeQueryForm").serializeArray();
        var opt = $("#customEmployeeScreening").serializeArray();
        var data = {};
        for (var i in search) {
          opt.push(search[i]);
        }
        for (var i in opt) {
          data[opt[i].name] = opt[i].value;
        }
        var treeObj = $.fn.zTree.getZTreeObj("customEmployeeTree");
        var nodes = treeObj.getCheckedNodes(true);
        var orgIds = "";
        for (var i = 0; i < nodes.length; i++) {
          orgIds += nodes[i].id + ",";
        }

        if (!data.orgIds) {
          data.orgIds = orgIds;
        }

        if (!data.employeeStatuses) {
          data.employeeStatuses = arrToString(
            employeeStatusChoose.getSelData() || [],
            "itemCode"
          );
        }
        if (!data.establishmentTypes) {
          data.establishmentTypes = arrToString(
            establishmentTypeChoose.getSelData() || [],
            "itemCode"
          );
        }
        // if (!data.orgAttributestypes) {
        //   data.orgAttributestypes = arrToString(
        //     orgAttributesChoosed.getSelData() || [],
        //     "itemCode"
        //   );
        // }

        if (!data.politicalStatuses) {
          data.politicalStatuses = arrToString(
            politicalStatusChoose.getSelData() || [],
            "itemCode"
          );
        }

        if (!data.nationalityes) {
          data.nationalityes = arrToString(
            nationalityChoose.getSelData() || [],
            "itemCode"
          );
        }
        if (!data.marriageStatuses) {
          data.marriageStatuses = arrToString(
            marriageStatusChoose.getSelData() || [],
            "itemCode"
          );
        }

        if (!data.positionNames) {
          data.positionNames = arrToString(
            positionNameChoose.getSelData() || [],
            "positionId"
          );
        }

        if (!data.personalIdentitys) {
          data.personalIdentitys = arrToString(
            personalIdentityChoose.getSelData() || [],
            "itemCode"
          );
        }

        if (!data.operationTypes) {
          data.operationTypes = arrToString(
            operationTypeChoose.getSelData() || [],
            "itemCode"
          );
        }

        if (!data.employeeCategorys) {
          data.employeeCategorys = arrToString(
            employeeCategoryChoose.getSelData() || [],
            "itemCode"
          );
        }

        if (!data.educationTypes) {
          data.educationTypes = arrToString(
            educationTypeChoose.getSelData() || [],
            "itemCode"
          );
        }

        if (!data.plgws) {
          data.plgws = arrToString(plgwChoose.getSelData() || [], "itemCode");
        }

        if (!data.gwdjs) {
          data.gwdjs = arrToString(gwdjChoose.getSelData() || [], "postId");
        }

        if (!data.jobtitleName) {
          data.jobtitleName = arrToString(
            jobtitleNameChoose.getSelData() || [],
            "jobtitleBasicId"
          );
        }

        if (!data.jobtitleCategory) {
          data.jobtitleCategory = arrToString(
            jobtitleCategoryChoose.getSelData() || [],
            "jobtitleBasicId"
          );
        }

        var exportParam = "";
        var _url =
          common.url + "/ts-basics-bottom/cusotmEmployee/customeExport?";
        $.each(data, function (name, value) {
          if (value) {
            exportParam += name + "=" + value + "&";
          }
        });

        var exportFieldName = "";
        $("input[name='filterCheck']").each(function () {
          var checkStatus = this.checked;
          if (checkStatus) {
            exportFieldName +=
              $(this).attr("filedname") + "=" + $(this).val() + ",";
          }
        });
        exportParam += "exportFields=" + exportFieldName;
        _url += exportParam;
        _url = _url.substring(0, _url.length - 1);
        location.href = _url;
        $("#customEmployee #customEmployeeExportDialog").removeClass("show");
      });
    });
  }
});
