'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch,
            	util = layui.util;

            var trasenTable = new $.trasenTable('hrmsPartyMembersTable', {
                url: common.url + '/ts-hrms/api/partyMembers/list',
                pager: '#hrmsPartyMembersPage',
                mtype: 'get',
                sortname: 'party_status,create_date',
                sortorder: 'desc',
                shrinkToFit: true,
                multiselect: true,  
                multiboxonly:true,  
                gridComplete: hideSelectAll,
                beforeSelectRow: beforeSelectRow,
                colModel: [
                	{
                        label: '工号',
                        sortable: true,
                        align: 'center',
                        index: 'emp_code',
                        name: 'empCode',
                        width: 60
                    },
                    {
                        label: 'empName',
                        name: 'empName',
                        hidden: true
                    },
                    {
                        label: 'filesId',
                        name: 'filesId',
                        hidden: true
                    },
                    {
                        label: '姓名',
                        sortable: true,
                        align: 'left',
                        index: 'emp_name',
                        name: 'empNameText',
                        width: 60,
                        formatter: function (cellvalue, options, rowObject) {
                        	return '<p style="cursor: pointer;"><span class="partyMembersDetails dealLink">' + rowObject.empName + '</span></p>';
                        }
                    },
                    {
                        label: '科室',
                        sortable: true,
                        align: 'left',
                        index: 'dept_name',
                        name: 'deptName',
                        width: 80
                    },
                    {
                        label: '身份证',
                        sortable: true,
                        align: 'center',
                        index: 'idcard',
                        name: 'idcard',
                        width: 140
                    },
                    {
                        label: 'postName',
                        name: 'postName',
                        hidden: true
                    },
                    {
                        label: '岗位',
                        sortable: true,
                        align: 'center',
                        index: 'post_name',
                        name: 'postNameText',
                        width: 60
                    },
                    {
                        label: 'politicalStatus',
                        name: 'politicalStatus',
                        hidden: true,
                    },
                    {
                        label: '政治面貌',
                        sortable: true,
                        name: 'politicalStatusText',
                        index: 'political_status',
                        width: 80
                    },
                    {
                        label: '入党时间',
                        sortable: true,
                        align: 'center',
                        index: 'party_date',
                        name: 'partyDate',
                        width: 80
                    },
                    {
                        label: '退休时间',
                        sortable: true,
                        align: 'center',
                        index: 'retire_date',
                        name: 'retireDate',
                        width: 80
                    },
                    {
                        label: '状态',
                        sortable: true,
                        align: 'center',
                        index: 'party_status',
                        name: 'partyStatusText',
                        width: 60,
                        formatter: function (cell, options, row) {
                        	if('1' == row.partyStatus){
                        		return '已审核';
                        	}else{
                        		return '<span style="color:red">待审核</span>';
                        	}
                        }
                    },
                    {
                        label: 'partyStatus',
                        name: 'partyStatus',
                        hidden: true
                    },
                    {
                        label: '创建时间',
                        sortable: true,
                        align: 'center',
                        index: 'create_date',
                        name: 'createDate',
                        width: 120,
                        formatter: function (cell, options, row) {
                        	return util.toDateString(cell,'yyyy-MM-dd HH:mm');
                        }
                    },
                    {
                        label: '创建人',
                        sortable: true,
                        align: 'center',
                        index: 'create_user_name',
                        name: 'createUserName',
                        width: 60
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 60,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, options, row) {
                    		var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            if('0' == row.partyStatus){
                            	btns += '<button class="layui-btn " id="editHrmsPartyMembers" > <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i> 编辑 </button>';
                                btns += '<button class="layui-btn " id="delHrmsPartyMembers" > <i class="fa fa-trash deal_icon"  aria-hidden="true"></i> 删除 </button>';
                            }else{
                            	btns += '<button class="layui-btn " id="showPartyMembers" > <i class="fa fa-eye deal_icon"  aria-hidden="true"></i> 查看 </button>';
                            }
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        name: 'id',
                        hidden: true,
                        key: true,
                    },
                    {
                        label: 'empId',
                        name: 'empId',
                        hidden: true,
                    }
                ],
                buidQueryParams: function () {
                    var search = $('#hrmsPartyMembersSearchForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });
            
            form.render();
            
            function hideSelectAll() {  
                $("#jqgh_hrmsPartyMembersTable_cb").hide();  
                return(true);  
            } 

            function beforeSelectRow() {  
                $("#hrmsPartyMembersTable").jqGrid('resetSelection');  
                return(true);  
            }  

            function refresh() {
                trasenTable.refresh();
            }
            // 人事首页跳转监听
            Event.create('mainMessage').listen('partyMembers', data => {
                Event.create('hashChangeLoadJs').remove('jsLoaded');
                setTimeout(() => {
                    $("#hrmsPartyMembersSearchForm input[name='nameOrCode']").val(data.data.employee_name);
                    form.render();
                    trasenTable.refresh();
                },1000)
            })
            //时间控件
            laydate.render({
                  elem: '#partyDateTime',
                  range: '~',
                  trigger: 'click',
                  showBottom: true,
                  done: function (value, date, endDate) {
                      var dateArr = value.split(' ~ ');
                      $('#partyDateStartTime').val(dateArr[0]);
                      $('#partyDateEndTime').val(dateArr[1]);
                  },
            });
            
            
            //搜索
            $('#hrmsPartyMembersSearch').funs('click', function () {
                refresh();
            });

            //重置
            $('#hrmsPartyMembersResetBtn').funs('click', function () {
            	$("#hrmsPartyMembersSearchForm")[0].reset();
            	$("#partyDateStartTime").val('');
            	$("#partyDateEndTime").val('');
                refresh();
            });

            // 新增
            $('#addHrmsPartyMembers').funs('click', function () {
                $.quoteFun('/personnelmgr/partyMembers/add', {
                    trasen: trasenTable,
                    title: '新增',
                    ref: refresh
                });
            });
            
          //查看
            $('body').off('click', '.partyMembersDetails').on('click', '.partyMembersDetails', function () {
            	 var rowData = trasenTable.getSelectRowData(); 
            	 rowData.userName = rowData.empName;
            	 rowData.userCode = rowData.empCode;
            	 $.quoteFun('/personnelmgr/partyMembers/add', {
                     data: rowData,
                     title: '详情',
                     optType:'details'
                 });
            });
            
            //查看
            $('body').off('click', '#showPartyMembers').on('click', '#showPartyMembers', function () {
	           	 var rowData = trasenTable.getSelectRowData(); 
	           	 rowData.userName = rowData.empName;
	           	 rowData.userCode = rowData.empCode;
	           	 $.quoteFun('/personnelmgr/partyMembers/add', {
	           		 data: rowData,
	           		 title: '详情',
	           		 optType:'details'
	           	 });
           });
           
           //编辑
           $('body').off('click', '#editHrmsPartyMembers').on('click', '#editHrmsPartyMembers', function () {
	           	 var rowData = trasenTable.getSelectRowData(); 
	           	 rowData.userName = rowData.empName;
	           	 rowData.userCode = rowData.empCode;
	           	 $.quoteFun('/personnelmgr/partyMembers/add', {
                    trasen: trasenTable,
                    title: '编辑',
                    ref: refresh,
                    data: rowData
                });
           });
            
           //删除
           $('body').off('click', '#delHrmsPartyMembers').on('click', '#delHrmsPartyMembers', function () {
                var rowData = trasenTable.getSelectRowData(); 
	           	 
                layer.confirm('删除后将无法恢复，确定要删除吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
	                $.ajax({
                        type: "post",
                        url: common.url + '/ts-hrms/api/partyMembers/delete/' + rowData.id,
                        dateType: "json",
                        contentType: 'application/json',
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                refresh();
                            }
                            layer.msg(res.message);
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        }
	               });
                })
            });
            
            //审核
            $('#examineHrmsPartyMembers').funs('click', function () {
            	 var ids = $("#hrmsPartyMembersTable").jqGrid('getGridParam', 'selarrrow'); // 获取
            	 
            	 if(ids.length <= 0){
            		 layer.msg("请选择需要审批的数据");
            		 return;
            	 }
            	 var rowData = $("#hrmsPartyMembersTable").jqGrid('getRowData', ids[0]); 
            	 
            	 if('1' == rowData.partyStatus){
            		 layer.msg(rowData.empName + "已经被审批");
            		 return;
            	 }
            	 
	           	 var d = {
	           		 id: rowData.id,
	           		 partyStatus: '1',
	           		 empId: rowData.empId,
	           		 politicalStatus: rowData.politicalStatus,
	           		 partyDate: rowData.partyDate
	           	 }
	             layer.confirm('确定要审批该条数据吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function () {
	               $.ajax({
	                 type: "post",
	                 url: common.url + '/ts-hrms/api/partyMembers/update',
	                 dateType: "json",
	                 contentType: 'application/json',
	                 data : JSON.stringify(d),
	                 success: function (res) {
	                   $.closeloadings();
	                   if (res.success) {
	                	   refresh();
	                	   layer.msg("审批数据成功");
	                   }
	                 },
	                 error: function (res) {
	                   res = JSON.parse(res.responseText);
	                   layer.msg(res.message);
	                 }
	               });
	             })
            });
            
            //同步数据
            $('#hrmsPartyMembersBox')
            .off('click', '#syncHrmsPartyMembers')
            .on('click', '#syncHrmsPartyMembers', function () {
            	$.ajax({
                    type: 'post',
                    url: common.url + '/ts-hrms/api/partyMembers/syncEmployeeData',
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                        	 refresh();
                        } 
                        layer.closeAll();
                        layer.msg(res.message);
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
            });
            
            
            //导出
            $('#hrmsPartyMembersBox')
                .off('click', '#exportHrmsPartyMembers')
                .on('click', '#exportHrmsPartyMembers', function () {
                    var opt = $('#hrmsPartyMembersSearchForm').serializeArray();
                    var params = "";
                    for (var i in opt) {
                        params += opt[i].name + "=" +opt[i].value + "&";
                    }
                    
                    window.location.href = common.url + '/ts-hrms/api/partyMembers/export?' + params.substring(0,params.length-1);
             });
        });
    };
    
});
