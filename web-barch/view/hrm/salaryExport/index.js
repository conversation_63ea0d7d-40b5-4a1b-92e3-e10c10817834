'use strict';

define(function (require, exports, module) {
    module.exports = {
        init: init,
        cancle: function () {},
    };
    function init() {
        layui.use(['form', 'laydate', 'zTreeSearch'], function () {
            var laydate = layui.laydate;
            var index = 0;
            var year = '';
            var month = '';
            var trasenTable = null;
            var trasenTablePer = null;
            var dataType = {};
            var tabList = [];
            function getTabList() {
                $.ajax({
                    url: '/ts-oa/salary/salarySet/salaryTypeMenu',
                    method: 'get',
                    async: false,
                    success: function (res) {
                        let obj = {};
                        tabList = res.object;
                        res.object.forEach((item, index) => {
                            let node = ''
                            if(index === 0){
                                node = '<a href="javascript:;" class="oa-nav_item active">'+item.itemName+'</a>'
                            }else{
                                node = '<a href="javascript:;" class="oa-nav_item">'+item.itemName+'</a>'
                            }
                            $('#salaryExport .oa-nav').append(node);
                            obj[index] = 'toa_salary_' + item.itemCode
                        })
                        dataType = obj;
                    },
                });
            }
            getTabList();
            function getSet() {
                $.ajax({
                    url: '/ts-basics-bottom/globalSetting/getAllGlobalSetting',
                    method: 'get',
                    async: false,
                    success: function (res) {
                        var salaryType = res.object.salaryType.split(',');
                        if(salaryType.length <= 1) {
                            $('#salaryExport .oa-nav_item').addClass('none');
                            if(salaryType.includes('2')) {
                                index = 1;
                                $('#salaryExport .oa-nav_item').eq(1).addClass('active').siblings().removeClass('active');
                            }
                        } else {
                            $('#salaryExport .oa-nav_item').removeClass('none');
                        }
                    },
                });
            }
            // getSet();
            //切换月份
            $('#salaryExport')
                .off('click', '.time_month .month')
                .on('click', '.time_month .month', function () {
                    month = $(this).index();
                    $(this).addClass('active').siblings().removeClass('active');
                    hideChange();
                });

            function hideChange() {
                if ($('#salaryExport .time_month .month').eq(month).hasClass('empty')) {
                    $('#salaryExport .empty_con').addClass('none');
                    $('#salaryExport .content_bg').removeClass('no_bg');
                } else {
                    $('#salaryExport .empty_con').removeClass('none');
                    $('#salaryExport .content_bg').addClass('no_bg');
                    setTable();
                }
            }

            //切换
            $('#salaryExport')
                .off('click', '.oa-nav .oa-nav_item')
                .on('click', '.oa-nav .oa-nav_item', function () {
                    index = $(this).index();
                    getMonthD();
                    $(this).addClass('active').siblings().removeClass('active');
                });

            $('#salaryExport')
                .off('click', '#search')
                .on('click', '#search', function () {
                    trasenTable.refresh();
                    // if (index == 0) {
                    // } else {
                    //     trasenTablePer.refresh();
                    // }
                });
            $('#salaryExport')
                .off('click', '#salaryExportReset')
                .on('click', '#salaryExportReset', function () {
                    $('#salaryExportQueryForm')[0].reset();
                    trasenTable.refresh();
                    // if (index == 0) {
                    // } else {
                    //     trasenTablePer.refresh();
                    // }
                });

            function setDefTime() {
                var time = new Date();
                year = time.format('yyyy');
                month = time.format('MM');
                if (month == 0) {
                    year = year - 1;
                    month = 11;
                } else {
                    month = month - 1;
                }
                laydate.render({
                    elem: '#salaryExport .time_year .date',
                    format: 'yyyy年',
                    type: 'year',
                    value: year + '年',
                    btns: ['now', 'confirm'],
                    done: function (value, data) {
                        year = data.year;
                        getMonthD();
                    },
                });
                getMonthD();
            }
            function monthClick() {
                $('#salaryExport .time_month .month').eq(month).trigger('click');
            }
            setDefTime();
            //获取月份数据  基本工资-绩效工资
            function getMonthD() {
                $('#salaryExport .time_year .date').text(year + '年');
                $.ajax({
                    url: '/ts-oa/salary/salarySet/existSalaryDeta/' + dataType[index] + '/' + year,
                    success: function (res) {
                        if (res.success && res.object) {
                            $('#salaryExport .time_month .month').addClass('empty').removeClass('active');
                            for (var i = 0; i < res.object.length; i++) {
                                var item = res.object[i];
                                var m = item.salaryDate && item.salaryDate.split('-')[1] * 1;
                                $('#salaryExport .time_month .month')
                                    .eq(m - 1)
                                    .removeClass('empty');
                            }
                        }
                        monthClick();
                    },
                });
                // setTable();
            }

            function getQueryData() {
                var search = $('#salaryExportQueryForm').serializeArray();
                var opt = [];
                var data = {};
                for (var i in search) {
                    opt.push(search[i]);
                }
                for (var i in opt) {
                    data[opt[i].name] = opt[i].value;
                }
                data.dateStr = year + '-' + addZero(month + 1);
                return data;
            }

            function addZero(num) {
                return num > 9 ? num : '0' + num;
            }
            //表格设置
            function setTable() {
                // $('.content-tab').eq(index).removeClass('none').siblings().addClass('none');
                if (trasenTable) {
                    $.jgrid.gridUnload('grid-table-salaryExport');
                }
                $.ajax({
                    type: 'post',
                    contentType: 'application/json; charset=utf-8',
                    url: common.url + '/ts-oa/salary/salarySet/getTableHeadCols/' + dataType[index] + '/' + year + '-' + addZero(month + 1),
                    success: function (res) {
                        if (res.success) {
                            var colModel = res.object.headCols || [];
                            for (var i = 0; i < colModel.length; i++) {
                                if (colModel[i].name == 'status') {
                                    colModel[i].formatter = function (cell, opt, row) {
                                        var d = {
                                            0: '未发送',
                                            1: '正常',
                                            2: '已撤回',
                                        };
                                        return d[cell];
                                    };
                                }
                                if (colModel[i].name == 'batch_number' || colModel[i].name == 'emp_code' || colModel[i].name == 'emp_name' || colModel[i].name == 'id') {
                                    colModel[i].frozen = true;
                                } else {
                                    colModel[i].frozen = false;
                                }
                            }
                            colModel.unshift({
                                // label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                                label: '操作',
                                sortable: false,
                                name: '',
                                index: '',
                                frozen: true,
                                width: 120,
                                editable: false,
                                title: false,
                                align: 'center',
                                classes: 'visible jqgrid-rownum ui-state-default',
                                formatter: function (cellvalue, options, row) {
                                    var html = '<div class="table-btn">';
                                    var btns = ''; 
                                    if (row.status == 1) {
                                        btns += '<div class="back hand" title="撤回工资条" row-id="' + options.rowId + '">撤回工资条</div>';
                                    } else {
                                        btns += '<div class="send hand" title="发送工资条" row-id="' + options.rowId + '">发送工资条</div>';
                                    }
                                    btns += '<div class="del hand" title="删除" row-id="' + options.rowId + '">删除</div>';
                                    html += btns + '</div>';
                                    return html;
                                },
                            });
                            baseTable(colModel);
                        }
                    },
                });
                // if (index == 0) {
                // } else {
                //     if (trasenTablePer) {
                //         $.jgrid.gridUnload('grid-table-salaryExportPer');
                //     }
                //     $.ajax({
                //         type: 'post',
                //         contentType: 'application/json; charset=utf-8',
                //         url: common.url + '/ts-oa/salary/salarySet/getTableHeadCols/' + dataType[index] + '/' + year + '-' + addZero(month + 1),
                //         success: function (res) {
                //             if (res.success) {
                //                 var colModel = res.object.headCols || [];
                //                 for (var i = 0; i < colModel.length; i++) {
                //                     if (colModel[i].name == 'status') {
                //                         colModel[i].formatter = function (cell, opt, row) {
                //                             var d = {
                //                                 0: '未发送',
                //                                 1: '正常',
                //                                 2: '已撤回',
                //                             };
                //                             return d[cell];
                //                         };
                //                         break;
                //                     } 71
                //                 }
                //                 colModel.unshift({
                //                     label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                //                     sortable: false,
                //                     name: '',
                //                     index: '',
                //                     frozen: true,
                //                     width: 120,
                //                     editable: false,
                //                     title: false,
                //                     align: 'center',
                //                     classes: 'visible jqgrid-rownum ui-state-default',
                //                     formatter: function (cellvalue, options, row) {
                //                         // var html = '<div class="table-more-btn table-more-btn-right"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                //                         // var btns = '';
                //                         // if (row.status == 1) {
                //                         //     btns += '<button class="layui-btn  backPer" class=""  title="撤回工资条" row-id="' + options.rowId + '"><i class="fa fa-upload deal_icon" aria-hidden="true"></i> 撤回工资条 </button>';
                //                         // } else {
                //                         //     btns += '<button class="layui-btn  sendPer" class="" title="发送工资条" row-id="' + options.rowId + '"> <i class="layui-icon layui-icon-template-1 deal_icon" aria-hidden="true"></i>发送工资条 </button>';
                //                         // }
                //                         // btns += '<button class="layui-btn  delPer" class=""  title="删除" row-id="' + options.rowId + '"><i class="fa fa-trash deal_icon" aria-hidden="true"></i> 删除 </button>';
                //                         // html += btns + '</div></div>';

                //                         var html = '<div class="table-btn">';
                //                         var btns = ''; 
                //                         if (row.status == 1) {
                //                             btns += '<div class="back hand" title="撤回工资条" row-id="' + options.rowId + '">撤回工资条</div>';
                //                         } else {
                //                             btns += '<div class="send hand" title="发送工资条" row-id="' + options.rowId + '">发送工资条</div>';
                //                         }
                //                         btns += '<div class="del hand" title="删除" row-id="' + options.rowId + '">删除</div>';
                //                         html += btns + '</div>';
                //                         return html;
                //                     },
                //                 });
                //                 perTable(colModel);
                //             }
                //         },
                //     });
                // }
            }

            function baseTable(colModel) {
                trasenTable = new $.trasenTable('grid-table-salaryExport', {
                    url: common.url + '/ts-oa/salary/salarySet/salaryList/' + dataType[index],
                    pager: 'grid-pager-salaryExport',
                    sortname: 'salary_date',
                    rowNum: 30,
                    shrinkToFit: false,
					autoScroll: false,
					autowidth: false,
					sortable: false,
                    rowList: [15, 30, 50, 100],
                    postData: getQueryData(),
                    colModel: colModel || [],
                    buidQueryParams: function () {
                        return getQueryData();
                    },
                });
                trasenTable.oTable.jqGrid('setFrozenColumns');
            }
            function perTable(colModel) {
                trasenTablePer = new $.trasenTable('grid-table-salaryExportPer', {
                    url: common.url + '/ts-oa/salary/salarySet/salaryList/' + dataType[index],
                    pager: 'grid-pager-salaryExportPer',
                    sortname: 'salary_date',
                    rowNum: 30,
                    shrinkToFit: false,
					autoScroll: false,
					autowidth: false,
					sortable: false,
                    rowList: [15, 30, 50, 100],
                    postData: getQueryData(),
                    colModel: colModel || [],
                    buidQueryParams: function () {
                        return getQueryData();
                    },
                });
                trasenTablePer.oTable.jqGrid('setFrozenColumns');
            }
            //
            $('#salaryExport')
                .off('click', '#back')
                .on('click', '#back', function () {
                    $.quoteFun('/hrm/salaryExport/wins/back', {
                        ref: getMonthD,
                        dateStr: year + '-' + addZero(month + 1),
                        type: dataType[index],
                    });
                    return false;
                });
            $('#salaryExport')
                .off('click', '#send')
                .on('click', '#send', function () {
                    $.quoteFun('/hrm/salaryExport/wins/send', {
                        ref: getMonthD,
                        dateStr: year + '-' + addZero(month + 1),
                        type: dataType[index],
                    });
                    return false;
                });
            $('#salaryExport')
                .off('click', '#del')
                .on('click', '#del', function () {
                    $.quoteFun('/hrm/salaryExport/wins/del', {
                        ref: getMonthD,
                        dateStr: year + '-' + addZero(month + 1),
                        type: dataType[index],
                    });
                    return false;
                });
            //密码验证
            function checkPwd(pwd, cb) {
                $.ajax({
                    url: '/ts-oa/salary/salarySet/verifyPassword',
                    method: 'post',
                    data: {
                        password: pwd,
                    },
                    success: function (res) {
                        if (!res.object) {
                            layer.msg('密码错误');
                        } else {
                            cb();
                        }
                    },
                });
            }
            //表格操作
            $('#salaryExport')
                .off('click', '.back')
                .on('click', '.back', function () {
                    var rowId = $(this).attr('rowid');
                    var rowData = trasenTable.getSourceRowData(rowId);
                    layer.confirm(
                        '<div class="confirm-page">撤回后将给对应人员发送通知，对方无法查看该月工资条内容，确定要撤回吗？' + $('#loginPwd').html() + '</div>', 
                        { 
                            btn: ['确定', '取消'],
                            title: '提示',
                            closeBtn: 0, 
                            area: ['450px', 'auto'], 
                            type: 1 
                        }, 
                        function (indexs) {
                            if (!$('#confirmPwd').val()) {
                                layer.msg('请输入登录密码');
                                return false;
                            }
                            checkPwd($('#confirmPwd').val(), function () {
                                $.ajax({
                                    url: '/ts-oa/salary/salarySet/cancelSalaryData/' + dataType[index] + '/' + year + '-' + addZero(month + 1),
                                    method: 'post',
                                    data: {
                                        empPayroll: rowData.emp_code,
                                    },
                                    success: function (res) {
                                        layer.close(indexs);
                                        if (res.success) {
                                            trasenTable.refresh();
                                            layer.msg(res.message || '工资条撤回成功');
                                        } else {
                                            layer.msg(res.message || '工资条撤回失败');
                                        }
                                    },
                                });
                        });
                    });
                    return false;
                });
            $('#salaryExport')
                .off('click', '.send')
                .on('click', '.send', function () {
                    var rowId = $(this).attr('rowid');
                    var rowData = trasenTable.getSourceRowData(rowId);
                    if (rowData.status == 1) {
                        var d = {
                            0: '基本工资',
                            1: '绩效工资',
                        };
                        layer.confirm(
                            rowData.emp_name + year + '年' + (month + 1) + '月份' + d[index] + '条为正常状态，确定要发送工资条吗？', 
                            {  
                                btn: ['确定', '取消'],
                                title: '提示',
                                closeBtn: 0
                            }, 
                            function (indexs) {
                                send(function () {
                                    layer.close(indexs);
                                });
                        });
                    } else {
                        send();
                    }
                    function send(fun) {
                        $.ajax({
                            url: '/ts-oa/salary/salarySet/sendMsgSalaryDataByMonth/' + dataType[index] + '/' + year + '-' + addZero(month + 1) + '/' + rowData.emp_code,
                            method: 'post',
                            data: {
                                empPayroll: rowData.emp_code,
                            },
                            success: function (res) {
                                fun && fun();
                                if (res.success) {
                                    trasenTable.refresh();
                                    layer.msg(res.object || '工资条发送成功');
                                } else {
                                    layer.msg(res.message || '工资条发送失败');
                                }
                            },
                        });
                    }

                    return false;
                });
            $('#salaryExport')
                .off('click', '.del')
                .on('click', '.del', function () {
                    var rowId = $(this).attr('rowid');
                    var rowData = trasenTable.getSourceRowData(rowId);
                    layer.confirm('<div class="confirm-page">删除后将无法恢复，确定要删除数据吗？' + $('#loginPwd').html() + '</div>', { 
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0,
                        area: ['450px', 'auto'], 
                        type: 1 
                    }, function (indexs) {
                        if (!$('#confirmPwd').val()) {
                            layer.msg('请输入登录密码');
                            return false;
                        }
                        checkPwd($('#confirmPwd').val(), function () {
                            $.ajax({
                                url: '/ts-oa/salary/salarySet/deletedSalaryData/' + dataType[index] + '/' + year + '-' + addZero(month + 1) + '/' + rowData.id,
                                method: 'post',
                                success: function (res) {
                                    layer.close(indexs);
                                    if (res.success) {
                                        trasenTable.refresh();
                                        layer.msg(res.message || '工资条删除成功');
                                    } else {
                                        layer.msg(res.message || '工资条删除失败');
                                    }
                                },
                            });
                        });
                    });
                    return false;
                });
            // $('#salaryExport')
            //     .off('click', '.backPer')
            //     .on('click', '.backPer', function () {
            //         var rowId = $(this).attr('rowid');
            //         var rowData = trasenTablePer.getSourceRowData(rowId);
            //         layer.confirm('<div class="confirm-page">撤回后将给对应人员发送通知，对方无法查看该月工资条内容，确定要撤回吗？' + $('#loginPwd').html() + '</div>', 
            //             { 
            //                 btn: ['确定', '取消'],
            //                 title: '提示',
            //                 closeBtn: 0,
            //                 area: ['450px', 'auto'], 
            //                 type: 1 
            //             }, 
            //             function (indexs) {
            //                 if (!$('#confirmPwd').val()) {
            //                     layer.msg('请输入登录密码');
            //                     return false;
            //                 }
            //                 checkPwd($('#confirmPwd').val(), function () {
            //                     $.ajax({
            //                         url: '/ts-oa/salary/salarySet/cancelSalaryData/' + dataType[index] + '/' + year + '-' + addZero(month + 1),
            //                         method: 'post',
            //                         data: {
            //                             empPayroll: rowData.emp_code,
            //                         },
            //                         success: function (res) {
            //                             layer.close(indexs);
            //                             if (res.success) {
            //                                 trasenTablePer.refresh();
            //                                 layer.msg(res.message || '工资条撤回成功');
            //                             } else {
            //                                 layer.msg(res.message || '工资条撤回失败');
            //                             }
            //                         },
            //                     });
            //                 });
            //         });
            //         return false;
            //     });
            // $('#salaryExport')
            //     .off('click', '.sendPer')
            //     .on('click', '.sendPer', function () {
            //         var rowId = $(this).attr('rowid');
            //         var rowData = trasenTablePer.getSourceRowData(rowId);
            //         if (rowData.status == 1) {
            //             var d = {
            //                 0: '基本工资',
            //                 1: '绩效工资',
            //             };
            //             layer.confirm(rowData.emp_name + year + '年' + (month + 1) + '月份' + d[index] + '条为正常状态，确定要发送工资条吗？', 
            //                 { 
            //                     btn: ['确定', '取消'],
            //                     title: '提示',
            //                     closeBtn: 0
            //                 }, function (indexs) {
            //                     send(function () {
            //                         layer.close(indexs);
            //                     });
            //             });
            //         } else {
            //             send();
            //         }
            //         function send(fun) {
            //             $.ajax({
            //                 url: '/ts-oa/salary/salarySet/sendMsgSalaryDataByMonth/' + dataType[index] + '/' + year + '-' + addZero(month + 1),
            //                 method: 'post',
            //                 data: {
            //                     empPayroll: rowData.emp_code,
            //                 },
            //                 success: function (res) {
            //                     fun && fun();
            //                     if (res.success) {
            //                         trasenTablePer.refresh();
            //                         layer.msg(res.object || '工资条发送成功');
            //                     } else {
            //                         layer.msg(res.message || '工资条发送失败');
            //                     }
            //                 },
            //             });
            //         }

            //         return false;
            //     });
            // $('#salaryExport')
            //     .off('click', '.delPer')
            //     .on('click', '.delPer', function () {
            //         var rowId = $(this).attr('rowid');
            //         var rowData = trasenTablePer.getSourceRowData(rowId);
            //         layer.confirm('<div class="confirm-page">删除后将无法恢复，确定要删除数据吗？' + $('#loginPwd').html() + '</div>', 
            //             { 
            //                 btn: ['确定', '取消'],
            //                 title: '提示',
            //                 closeBtn: 0,
            //                 area: ['450px', 'auto'], 
            //                 type: 1 
            //             }, 
            //             function (indexs) {
            //                 if (!$('#confirmPwd').val()) {
            //                     layer.msg('请输入登录密码');
            //                     return false;
            //                 }
            //                 checkPwd($('#confirmPwd').val(), function () {
            //                     $.ajax({
            //                         url: '/ts-oa/salary/salarySet/deletedSalaryData/' + dataType[index] + '/' + year + '-' + addZero(month + 1) + '/' + rowData.id,
            //                         method: 'post',
            //                         success: function (res) {
            //                             layer.close(indexs);
            //                             if (res.success) {
            //                                 trasenTablePer.refresh();
            //                                 layer.msg(res.message || '工资条删除成功');
            //                             } else {
            //                                 layer.msg(res.message || '工资条删除失败');
            //                             }
            //                         },
            //                     });
            //                 });
            //         });

            //         return false;
            //     });

            $('#salaryExport')
                .off('click', '#prevYear')
                .on('click', '#prevYear', function () {
                    year--;
                    getMonthD();
                    return false;
                });

            $('#salaryExport')
                .off('click', '#nextYear')
                .on('click', '#nextYear', function () {
                    year++;
                    getMonthD();
                    return false;
                });

            $('#salaryExport')
                .off('click', '#import')
                .on('click', '#import', function () {
                    $.quoteFun('/hrm/salaryExport/import', {
                        ref: getMonthD,
                        dateStr: year + '-' + addZero(month + 1),
                        type: dataType[index],
                    });
                });

            $('#salaryExport')
                .off('click', '#importHis')
                .on('click', '#importHis', function () {
                    $.quoteFun('/personal/salary/salaryRecord', {
                        title: tabList[index].itemName+'操作记录',
                        salaryType: tabList[index].itemName,
                    });
                    // if (index == 0) {
                    //     $.quoteFun('/personal/salary/salaryRecord', {
                    //         title: '基本工资操作记录',
                    //         salaryType: '基本工资',
                    //     });
                    // } else {
                    //     $.quoteFun('/personal/salary/salaryRecord', {
                    //         title: '绩效工资操作记录',
                    //         salaryType: '绩效工资',
                    //     });
                    // }
                });
        });
    }
});
