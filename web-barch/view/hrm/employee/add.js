'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch', 'layedit'], function () {
            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload,
                layer = layui.layer,
                element = layui.element,
                zTreeSearch = layui.zTreeSearch,
                layedit = layui.layedit;

            var fileArray = new Array();
            var oldFileArray = new Array();

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: opt.from == 'index' ? 0 : 1,
                shadeClose: false,
                area: ['850px', '650px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    //出生年月
                    laydate.render({
                        elem: '#empBirth',
                        showBottom: true,
                        trigger: 'click',
                        done: function (value, date, endDate) {
                            if (CompareDate(value, dataTime)) {
                                layer.msg('请选择正确的出生年月');
                                $('#empAge').val('');
                            } else {
                                // 计算年龄
                                $('#empAge').val(jsGetAge(value));
                            }
                        },
                    });

                    //入职日期
                    laydate.render({
                        elem: '#empHiredate',
                        showBottom: true,
                        trigger: 'click',
                    });

                    //离职日期
                    laydate.render({
                        elem: '#empFiredate',
                        showBottom: true,
                        trigger: 'click',
                    });
                    $('#id').val(opt.id);
                    loadBaseInfo();
                    if (opt.data) {
                        trasen.setNamesVal($('#evaBaseAddForm'), opt.data);
                        $('#empPassword').val('');
                        $('#empBirth').val(opt.data.empBirth); //出生年月
                        $('#empHiredate').val(opt.data.empHiredate); //入职时间
                        $('#empFiredate').val(opt.data.empFiredate); //离职时间
                        if (opt.data.empHeadImg && opt.data.empHeadImg != undefined) {
                            $('#UserPhoto').attr('src', common.url + '/ts-document/attachment/' + opt.data.empHeadImg); //头像
                        }
                        if (opt.data.signatureImgName && opt.data.signatureImgName != undefined) {
                            $('#signatureImg').attr('src', common.url + '/ts-document/attachment/' + opt.data.signatureImgName); //签章图片
                        }
                        if (opt.data.isSmsReminder && opt.data.isSmsReminder == 1) {
                            $('#isSmsReminderAdd').prop('checked', true);
                        }else{
                        	$('#isSmsReminderAdd').prop('checked', false);
                        }
                        if (opt.data.isVoiceReminder && opt.data.isVoiceReminder == 1) {
                            $('#isVoiceReminderAdd').prop('checked', true);
                        }else{
                        	$('#isVoiceReminderAdd').prop('checked', false);
                        }
                        if (opt.data.isWxReminder && opt.data.isWxReminder == 1) {
                            $('#isWxReminderAdd').prop('checked', true);
                        }else{
                        	$('#isWxReminderAdd').prop('checked', false);
                        }
                        if (opt.data.isDisplayPhoneNo && opt.data.isDisplayPhoneNo == 1) {
                            $('#isDisplayPhoneNoAdd').prop('checked', true);
                        }else{
                        	$('#isDisplayPhoneNoAdd').prop('checked', false);
                        }
                        if (opt.data.isUseSignature && opt.data.isUseSignature == 1) {
                            $('#isUseSignatureAdd').prop('checked', true);
                        }else{
                        	$('#isUseSignatureAdd').prop('checked', false);
                        }

                        if (opt.none) {
                            $('#empPayroll').removeAttr('readonly');
                            $('#userAccounts').removeAttr('readonly');
                            if ('true' != opt.data.isHrmAdmin) {
                                $('#empPassword').removeAttr('readonly');
                            }else{
                            	$("#uploadFileSizeDiv").removeClass("none");
                            }
                        }
                    }
                    //人员信息完善
                    if (opt.from == 'index') {
                        $('#evaBaseAddForm #empName').prop('readonly', true);
                        $('#evaBaseAddForm #closeLyer').remove();
                        $('#evaBaseAddForm #empPassword').closest('.layui-col-xs6').remove();
                        // $('#evaBaseAddForm #empIdcard').attr('lay-verify', 'required').closest('.layui-col-xs6').find('.shell-layui-form-label').contents().before('<span class="required">*</span>')
                        // $('#evaBaseAddForm #empPhone').attr('lay-verify', 'required').closest('.layui-col-xs6').find('.shell-layui-form-label').contents().before('<span class="required">*</span>')
                    }
                    form.render();
                },
            });

            //部门下拉框赋值
            function treeSelect() {
                zTreeSearch.init('#evaBaseAddForm #empDeptName', {
                    url: common.url + '/ts-oa/thpsSysetm/getDeptList',
                    type: 'get',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function (treeId, treeNode) {
                        $('#evaBaseAddForm #empDeptId').val(treeNode.id);
                        $('#evaBaseAddForm #empDeptName').val(treeNode.name);
                        $('#evaBaseAddForm #empDeptCode').val(treeNode.id);
                    },
                });
            }

            treeSelect();
            
            //加载基础数据
            function loadBaseInfo(){
            	  //职务下拉框赋值
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-oa/employee/duty/list',
                    async:false,
                    data: {
                        pageSize: 200,
                        pageNo: 1,
                    },
                    success: function (res) {
                        if (res) {
                            var postType = ' <option value="">请选择</option>';
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.dutyCode + '">' + v.dutyName + '</option>';
                            });
                            $('#empDutyId').html(postType);
                            form.render();
                        }
                    },
                });

                //员工类型下拉框赋值
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-oa/employee/employeeType/list',
                    data: {
                        pageSize: 200,
                        pageNo: 1,
                    },
                    async:false,
                    success: function (res) {
                        if (res) {
                            var postType = ' <option value="">请选择</option>';
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.userType + '">' + v.userType + '</option>';
                            });
                            $('#empType').html(postType);
                            form.render();
                        }
                    },
                });

                //员工职称下拉框赋值
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-oa/employee/title/list',
                    data: {
                        pageSize: 200,
                        pageNo: 1,
                    },
                    async:false,
                    success: function (res) {
                        if (res) {
                            var postType = ' <option value="">请选择</option>';
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.id + '">' + v.titleName + '</option>';
                            });
                            $('#empTitleId').html(postType);
                            form.render();
                        }
                    },
                });
            }

            //监听下拉选择
            form.on('select(empDutyId)', function (data) {
                var dutyName = data.elem[data.elem.selectedIndex].text;
                $('#empDutyName').val(dutyName);
                form.render();
            });

            form.on('select(empTitleId)', function (data) {
                var titleName = data.elem[data.elem.selectedIndex].text;
                $('#empTitleName').val(titleName);
                form.render();
            });

            //头像上传
            var uploadInst = upload.render({
                elem: '#UserPhotoBtn',
                url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                field: 'file',
                before: function (obj) {
                    //预读本地文件示例，不支持ie8
                    obj.preview(function (index, file, result) {
                        $('#UserPhoto').attr('src', result); //图片链接（base64）
                    });
                },
                done: function (res, index, upload) {
                    //如果上传失败
                    if (res.code > 0) {
                        return layer.msg('上传失败');
                    } else {
                        //上传成功
                        $('#empHeadImg').val(res.object[0].filePath);
                    }
                },
                error: function () {
                    //演示失败状态，并实现重传
                    var demoText = $('#UserPhotoText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function () {
                        uploadInst.upload();
                    });
                },
            });

            //签章图片
            var signatureImg = upload.render({
                elem: '#signatureImgBtn',
                url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                field: 'file',
                before: function (obj) {
                    //预读本地文件示例，不支持ie8
                    obj.preview(function (index, file, result) {
                        $('#signatureImg').attr('src', result); //图片链接（base64）
                    });
                },
                done: function (res, index, upload) {
                    //如果上传失败
                    if (res.code > 0) {
                        return layer.msg('上传失败');
                    } else {
                        //上传成功
                        $('#signatureImgName').val(res.object[0].filePath);
                    }
                },
                error: function () {
                    //演示失败状态，并实现重传
                    var demoText = $('#signatureImgText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function () {
                        signatureImg.upload();
                    });
                },
            });

            // 保存
            form.on('submit(evaBaseSubmitCofirm)', function (data) {
                var empBirth = $('#empBirth').val();
                var empHiredate = $('#empHiredate').val();
                var empFiredate = $('#empFiredate').val();
                var empDeptId = $('#empDeptId').val();
                var d = data.field;
                d.empBirth = empBirth;
                d.empHiredate = empHiredate;
                d.empFiredate = empFiredate;
                d.empDeptId = empDeptId;

                var uploadedFile = '';
                $.each(fileArray, function (index, obj) {
                    uploadedFile += obj.fileId + ',';
                });
                $.each(oldFileArray, function (index, obj) {
                    uploadedFile += obj + ',';
                });

                d.uploadedFile = uploadedFile.substring(0, uploadedFile.length - 1);

                var isSmsReminder = '';
                if ($('[name="isSmsReminder"]').is(':checked')) {
                    d.isSmsReminder = 1;
                } else {
                    d.isSmsReminder = 0;
                }

                var isVoiceReminder = '';
                if ($('[name="isVoiceReminder"]').is(':checked')) {
                    d.isVoiceReminder = 1;
                } else {
                    d.isVoiceReminder = 0;
                }

                var isWxReminder = '';
                if ($('[name="isWxReminder"]').is(':checked')) {
                    d.isWxReminder = 1;
                } else {
                    d.isWxReminder = 0;
                }

                var isDisplayPhoneNo = '';
                if ($('[name="isDisplayPhoneNo"]').is(':checked')) {
                    d.isDisplayPhoneNo = 1;
                }else{
                	d.isDisplayPhoneNo = 0;
                }

                var isUseSignature = '';
                if ($('[name="isUseSignature"]').is(':checked')) {
                    d.isUseSignature = 1;
                } else {
                    d.isUseSignature = 0;
                }
                var url;
                if (d.id) {
                    url = '/ts-oa/employee/update';
                } else {
                    url = '/ts-oa/employee/save';
                }
                if (!d.id) {
                    delete d.id;
                }

                $.ajax({
                    type: 'post',
                    url: common.url + url,
                    dateType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify(d),
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg(res.object);
                            opt.ref();
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
            });

            //根据身份证号获取出生日期
            function getBirthdayFromIdCard(idCard) {
                var birthday = '';
                if (idCard != null && idCard != '') {
                    if (idCard.length == 15) {
                        birthday = '19' + idCard.substr(6, 6);
                    } else if (idCard.length == 18) {
                        birthday = idCard.substr(6, 8);
                    }

                    birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-');
                }
                return birthday;
            }

            var myDate = new Date();
            var year = myDate.getFullYear();
            var monthTime = myDate.getMonth() + 1;
            var day = myDate.getDate();
            var dataTime = year + '-' + monthTime + '-' + day;
            // 输入身份证就算出生年月和年龄
            $('#empIdcard').bind('input propertychange', function () {
                var idCard = $(this).val() == null || $(this).val() == '' ? '' : $(this).val();
                var birth;
                if (idCard.length == 18) {
                    birth = getBirthdayFromIdCard(idCard);
                    if (CompareDate(birth, dataTime)) {
                        layer.msg('请输入正确的身份证号码');
                        $('#empBirth').val('');
                    } else {
                        $('#empBirth').val(birth); // 给日期赋值
                    }
                } else {
                    $('#empBirth').val('');
                }
                // 计算年龄
                $('#empAge').val(jsGetAge(birth));
            });

            //时间的比较(开始时间，结束时间) yyyy-MM-dd hh ss mm
            function CompareDate(time1, time2) {
                return new Date(time1.replace(/-/g, '/')) > new Date(time2.replace(/-/g, '/'));
            }

            /*根据出生日期算出年龄*/
            function jsGetAge(strBirthday) {
                var returnAge;
                if (strBirthday && typeof strBirthday != 'undefined') {
                    var strBirthdayArr = strBirthday.split('-');
                    var birthYear = strBirthdayArr[0];
                    var birthMonth = strBirthdayArr[1];
                    var birthDay = strBirthdayArr[2];

                    var d = new Date();
                    var nowYear = d.getFullYear();
                    var nowMonth = d.getMonth() + 1;
                    var nowDay = d.getDate();

                    if (nowYear == birthYear) {
                        returnAge = 0; //同年 则为0岁
                    } else {
                        var ageDiff = nowYear - birthYear; //年之差
                        if (ageDiff > 0) {
                            if (nowMonth == birthMonth) {
                                var dayDiff = nowDay - birthDay; //日之差
                                if (dayDiff < 0) {
                                    returnAge = ageDiff - 1;
                                } else {
                                    returnAge = ageDiff;
                                }
                            } else {
                                var monthDiff = nowMonth - birthMonth; //月之差
                                if (monthDiff < 0) {
                                    returnAge = ageDiff - 1;
                                } else {
                                    returnAge = ageDiff;
                                }
                            }
                        } else {
                            //returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
                            returnAge = ''; //返回"" 表示出生日期输入错误 晚于今天
                        }
                    }
                }
                return returnAge; //返回周岁年龄
            }
        });
    };
});
