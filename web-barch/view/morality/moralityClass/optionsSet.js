'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                laydate = layui.laydate,
                trasen = layui.trasen,
                upload = layui.upload;
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['80%', '80%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    if (opt.data) {
                        loadMoralityItem(opt.data);
                        var moralityOptionsTable = new $.trasenTable('moralityOptionsTable', {
                            url: common.url + '/ts-oa/api/moralityOptions/list',
                            mtype: 'post',
                            postData: { itemId: opt.data },
                            datatype: 'json',
                            colModel: [
                                {
                                    label: '评分内容',
                                    sortable: true,
                                    name: 'optionTitle',
                                    index: 'option_title',
                                    width: 250,
                                    align: 'center',
                                },
                                {
                                    label: '单次扣分',
                                    sortable: true,
                                    name: 'optionScore',
                                    index: 'option_score',
                                    width: 80,
                                    align: 'center',
                                    formatter: function (cellvalue, options, rowObject) {
                                        if ('610b999bb5a3ad3a28fd6e21' == rowObject.itemId) {
                                            return '+' + cellvalue;
                                        } else {
                                            return '-' + cellvalue;
                                        }
                                    },
                                },
                                {
                                    label: '一票否决',
                                    sortable: true,
                                    name: 'veto',
                                    index: 'veto',
                                    width: 80,
                                    align: 'center',
                                    formatter: function (cellvalue, options, rowObject) {
                                        if (cellvalue == '0') {
                                            return '否';
                                        } else {
                                            return '是';
                                        }
                                    },
                                },
                                {
                                    label: '状态',
                                    sortable: true,
                                    name: 'status',
                                    index: 'status',
                                    width: 80,
                                    align: 'center',
                                    formatter: function (cellvalue, options, rowObject) {
                                        if (cellvalue == '0') {
                                            return '启用';
                                        } else {
                                            return '<span style="color:red">停用</span>';
                                        }
                                    },
                                },
                                {
                                    label: '创建人',
                                    name: 'createUserName',
                                    index: 'create_user_name',
                                    width: 100,
                                    align: 'center',
                                    formatter: function (cellvalue, options, rowObject) {
                                        return rowObject.createDeptName + '-' + cellvalue;
                                    },
                                },
                                {
                                    label: '创建时间',
                                    sortable: true,
                                    index: 'create_date',
                                    name: 'createDate',
                                    width: 120,
                                    align: 'center',
                                },
                                { label: 'id', name: 'id', hidden: true },
                                { label: 'itemId', name: 'itemId', hidden: true },
                                { label: '创建部门', name: 'createDeptName', hidden: true },
                                {
                                    label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                                    align: 'center',
                                    classes: 'visible jqgrid-rownum ui-state-default',
                                    name: '',
                                    width: 40,
                                    title: false,
                                    formatter: function (cellvalue, options, row) {
                                        var btnStr = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                        btnStr += '<button style="color: #5260ff;cursor:pointer;" class="moralityOptionsEdit" id="moralityOptionsEdit" row-id="' + options.rowId + '">编辑</button>';
                                        btnStr += '<button style="color: #5260ff;cursor:pointer;" class="moralityOptionsDel" id="moralityOptionsDel" row-id="' + options.rowId + '">删除</button>';
                                        if (row.status == '0') {
                                            btnStr += '<button style="color: #f0000b;cursor:pointer;" class="moralityOptionsDisable" id="moralityOptionsDisable" row-id="' + options.rowId + '">停用</button>';
                                        } else {
                                            btnStr += '<button style="color: #0015f0;cursor:pointer;" class="moralityOptionsEnable" id="moralityOptionsEnable" row-id="' + options.rowId + '">启用</button>';
                                        }
                                        btnStr += '</div></div>';
                                        return btnStr;
                                    },
                                },
                            ],
                            height: 'auto',
                            pager: '#moralityOptionsPage',
                            rowNum: 10,
                            rownumbers: true,
                            rowList: [15, 30, 50, 100],
                            shrinkToFit: true,
                            queryFormId: 'moralityOptionsForm',
                        });
                    }

                    $('#moralityOptionsTable').jqGrid('setLabel', '0', '序号', 'labelstyle');

                    var len = $('#moralityOptionsTable').getGridParam('width');
                    if ('610b999bb5a3ad3a28fd6e21' == opt.data) {
                        $('#moralityOptionsTable').jqGrid('setLabel', '2', '单次加分', '单次扣分');
                        $('#moralityOptionsTable').setGridParam().hideCol('veto');
                        $('#moralityOptionsTable').setGridWidth(len);
                    } else {
                        $('#moralityOptionsTable').setGridParam().showCol('veto');
                        $('#moralityOptionsTable').setGridWidth(len);
                    }

                    //修改
                    $('body')
                        .off('click', '#moralityOptionsEdit')
                        .on('click', '#moralityOptionsEdit', function () {
                            var data = moralityOptionsTable.getSelectRowData();
                            if (data === null) {
                                trasen.info('请选择一条需要修改的数据!');
                                return false;
                            } else {
                                $.quoteFun('/morality/moralityClass/addOptions', {
                                    itemId: opt.data,
                                    itemMaxScore: $('#itemMaxScore').val(),
                                    data: data,
                                    none: true,
                                    title: '修改评分项',
                                    ref: refreshMoralityOptions,
                                });
                            }
                        });

                    //删除
                    $('body')
                        .off('click', '#moralityOptionsDel')
                        .on('click', '#moralityOptionsDel', function () {
                            var id = $('#moralityOptionsTable').getGridParam('selrow');
                            var d = {
                                id: id,
                                isDeleted: 'Y',
                            };
                            updateMoralityOptions(d, '删除后将无法恢复，确定要删除吗？');
                            return false;
                        });

                    //停用
                    $('body')
                        .off('click', '#moralityOptionsDisable')
                        .on('click', '#moralityOptionsDisable', function () {
                            var id = $('#moralityOptionsTable').getGridParam('selrow');
                            var d = {
                                id: id,
                                status: '1',
                            };
                            updateMoralityOptions(d, '确定停用当前选中数据吗？');
                            return false;
                        });

                    //启用
                    $('body')
                        .off('click', '#moralityOptionsEnable')
                        .on('click', '#moralityOptionsEnable', function () {
                            var id = $('#moralityOptionsTable').getGridParam('selrow');
                            var d = {
                                id: id,
                                status: '0',
                            };
                            updateMoralityOptions(d, '确定启用当前选中数据吗？');
                            return false;
                        });

                    function updateMoralityOptions(d, title) {
                        layer.confirm(
                            title,
                            {
                                btn: ['确定', '取消'],
                                title: '提示',
                                closeBtn: 0
                            },
                            function () {
                                $.ajax({
                                    type: 'post',
                                    url: common.url + '/ts-oa/api/moralityOptions/update',
                                    dateType: 'json',
                                    contentType: 'application/json',
                                    data: JSON.stringify(d),
                                    success: function (res) {
                                        $.closeloadings();
                                        if (res.success) {
                                            layer.msg('操作成功');
                                            refreshMoralityOptions();
                                        } else {
                                            layer.msg(res.message);
                                        }
                                    },
                                    error: function (res) {
                                        res = JSON.parse(res.responseText);
                                        layer.msg(res.message);
                                    },
                                });
                            }
                        );
                    }

                    function loadMoralityItem(itemId) {
                        $.ajax({
                            type: 'get',
                            url: common.url + '/ts-oa/api/moralityItem/' + itemId,
                            contentType: 'application/json',
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    $('#itemMaxScore').val(res.object.maxScore);
                                    $('#moralityItemTitle').html(res.object.itemTitle.substring(2, res.object.itemTitle.length) + '<br>总分值：' + res.object.maxScore + '分');
                                } else {
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }

                    $('#moralityOptionsAdd').funs('click', function () {
                        $.quoteFun('/morality/moralityClass/addOptions', {
                            trasen: moralityOptionsTable,
                            itemId: opt.data,
                            itemMaxScore: $('#itemMaxScore').val(),
                            title: '新增评分项',
                            ref: refreshMoralityOptions,
                        });
                    });

                    function refreshMoralityOptions() {
                        moralityOptionsTable.refresh();
                    }
                },
            });
        });
    };
});
