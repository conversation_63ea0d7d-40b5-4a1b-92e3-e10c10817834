"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'laytpl', 'upload', 'trasen', 'element'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laytpl = layui.laytpl,
                upload = layui.upload,
                laydate = layui.laydate,
                element = layui.element,
                trasen = layui.trasen;
            //表格渲染
            var trasenTable = new $.trasenTable("grid-table-systemProjectBonusManagerTable", {
                url: common.url + '/ts-cp/bnousInfo/projectBase/list',
                pager: 'grid-pager-systemProjectBonusManagerPager',
                //表格字段
                colModel: [
                	{ label: 'ID', name: 'projectBonusBaseId', index: 'project_bonus_base_id', width: "auto", align: "center", editable: false, hidden: true },
                    { label: '', name: 'projectInfoId', index: 'project_info_id', width: "auto", align: "center", editable: false, hidden: true },
                    { label: '', name: 'projectBonusInfoId', index: 'projectBonusInfoId', width: "100", align: "center", editable: false, hidden: true },
                    { label: '', name: 'projectBonus', index: 'projectBonus', width: "100", align: "center", editable: false, hidden: true },
                    { label: '', name: 'factSchedule', index: 'factSchedule', width: "100", align: "center", editable: false, hidden: true },
                    { label: '', name: 'developQuality', index: 'developQuality', width: "100", align: "center", editable: false, hidden: true },
                    { label: '合同编号', name: 'contractNo', index: 'contract_no', width: "100", align: "center", editable: false, hidden: true },
                    { label: '合同名称', name: 'contractName', index: 'contract_name', width: "100", align: "center", editable: false, hidden: true },
                    { label: '项目编号', name: 'projectCode', index: 'project_code', width: 100, align: "center", editable: false },
                    { label: '项目名称', name: 'projectName', index: 'project_name', width: 300, align: "center", editable: false,
                    	formatter: function (cellvalue, options, rowObject ) {
                    		return '<a href="javascript:;" style="color:#0090ff;" id="projectBudgetTableEditor" data-rowid="' + options.rowId + '">' + rowObject.projectName + '</a>';
                    	}
                    },
                    { label: '', name: 'bonusType', index: 'bonus_type', width: 100, align: "center", editable: false, hidden: true },
                    { label: '奖金核算类型', name: 'bonusTypeValue', index: 'bonus_type_value', width: 100, align: "center", editable: false },
                    { label: '所属部门', name: 'deptName', index: 'dept_name', width: 100, align: "center", editable: false },
                    { label: '项目经理', name: 'pmName', index: 'pm_name', width: 80, align: "center", editable: false },
                    { label: '项目状态', name: 'statusValue', index: 'status', width: 150, align: "center", editable: false },
                    { label: '审批状态', name: 'isExamine', index: 'is_Examine', width: 150, align: "center", editable: false, hidden: true },
                    { label: '奖金审批状态', name: 'isExamineValue', index: 'isExamineValue', width: 100, align: "center", editable: false },
                    { label: '已结算进度(%)', name: 'projectSchedule', index: 'project_schedule', width: 80, align: "center", editable: false,
                    	formatter: function (cellvalue, options, rowObject) {
                            if (cellvalue) {
                                cellvalue = cellvalue+'%'
                            } else {
                                cellvalue = '';
                            }
                            return cellvalue;
                        }
                    },
                    { label: '研发质量(%)', name: 'developQuality', index: 'develop_quality', width: 100, align: "center", editable: false },
                   	{ label: '结余质量奖金(￥)', name: 'balanceQualityBonus', index: 'balance_quality_bonus', width: 120, align: "center", editable: false,
                   		formatter: function (cellvalue, options, rowObject) {
                    		if(cellvalue){
								return formatCurrency(cellvalue)
                    		}else{
                    			return '';
                    		}
                    	}	
                   	},
                    { label: '项目奖金', name: 'projectBonus', index: 'project_bonus', width: 100, align: "center", editable: false,
                    	formatter: function (cellvalue, options, rowObject) {
                    		if(cellvalue){
								return formatCurrency(cellvalue)
                    		}else{
                    			return '';
                    		}
                    	}	
                    },
                    { label: '是否结项', name: 'isHighLines', index: 'is_high_lines', width: 100, align: "center", editable: false, hidden: true },
                    { label: '难度系数', name: 'difficultyType', index: 'difficulty_type', width: 100, align: "center", editable: false, hidden: true },
                    { label: '评级系数', name: 'gradeType', index: 'grade_type', width: 100, align: "center", editable: false, hidden: true },
                    { label: '进度系数', name: 'scheduleType', index: 'schedule_type', width: 100, align: "center", editable: false, hidden: true },
                    { label: '部门占比', name: 'factPercent', index: 'fact_percent', width: 100, align: "center", editable: false, hidden: true },
                    { label: '项目预算', name: 'projectBudget', index: 'project_budget', width: 100, align: "center", editable: false, hidden: true },
                    { label: '项目奖金', name: 'projectBonus', index: 'project_bonus', width: 80, align: "center", editable: false, formatter: "number", hidden: true,
                        formatoptions: {
                            decimalSeparator: ".",
                            thousandsSeparator: ",",
                            decimalPlaces: 2,
                            defaulValue: 0
                        }
                    },
                    { label: '差旅费', name: 'travelAmount', index: 'travel_amount', width: 100, align: "center", editable: false, hidden: true },
                    { label: '奖金系数', name: 'bonusCoefficient', index: 'bonus_coefficient', width: 80, align: "center", editable: false, hidden: true },
                    { label: '相关人员', name: 'refUserNameList', index: 'refUserNameList', width: 150, align: "center", editable: false ,hidden: true},
                    { label: '相关人员代码', name: 'refUserCodeList', index: 'refUserCodeList', width: 150, align: "center", editable: false ,hidden: true},
                    { label: '创建人', name: 'createUserName', index: 'create_user_name', width: 80, align: "center", editable: false },
                    { label: '创建时间', name: 'createDate', index: 'create_date', width: 150, align: "center", editable: false }
                ],
                buidQueryParams: function() {
					var search = $("#systemProjectBonusManagerQueryForm").serializeArray();
					var opt = $("#systemProjectBonusManagerScreening").serializeArray();
					var data = {};
					for(var i in search) {
						opt.push(search[i]);
					}
					for(var i in opt) {
						data[opt[i].name] = opt[i].value;
					}
					return data;
				}
            });

            //表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }
            
            initProjectType();
            initProjectStatus();

            $('body').on('click', '#systemProjectBonusManagerCancel', function () {
                layer.closeAll('page');
            });

            //查询
            form.on('submit(systemProjectBonusManagerSearch)', function (data) {
                refreshTable()
            });

            //项目经理
            function initEmployeeSelect(el) {
                $.SelectPullDown(el, {
                    url: common.url + "/ts-hr/employee/selectList",
                    textName: 'name',
                    valName: 'code',
                    inpTextName: 'pmName',
                    inpValName: 'pmCode',
                    callback: function (res) {
                        if (res) {
                            var _url = common.url + "/ts-hr/employee/get/"+res.employeeId;
                            $.ajax({
                                type: "post",
                                contentType: "application/json; charset=utf-8",
                                url: _url,
                                success: function (data) {
                                    var obj = data.object;
                                    if(obj != null && obj != undefined && obj != '') {
                                        $("#systemProjectBonusManagerAddForm #deptName").val(obj.organizationName);
                                        $("#systemProjectBonusManagerAddForm #deptCode").val(obj.organizationId);
                                    }
                                }
                            });
                        }
                    }
                });
    
            }
            
            //所属部门
            function initdeptCodeSelect(el) {
                $.allTreeSelect('#systemProjectBonusManagerAddFormDiv #deptName',{
                    id:'#deptCode',
                    url:common.url + "/ts-hr/organization/getTree",
                    callback:function(data){
                        $('#systemProjectBonusManagerAddFormDiv #deptCode').val(data.id);
                    }
                });
            }
            
            //筛选
			$("body").off('click', '#systemProjectBonusManagerScreen').on("click", "#systemProjectBonusManagerScreen", function() {
				var type = $('#systemProjectBonusManager .screeningBox').css('display');
				if(type != 'none') {
					//ClearForm();
					$("#systemProjectBonusManager .screeningBox").fadeOut(200);
					return false;
				}
				$("#systemProjectBonusManager .screeningBox").fadeIn(200);
				
				form.render('select');

	            
			});
            initEmployeeSelect('#systemProjectBonusManagerQuerypmNameChooseBox');

			//重新渲染表单
			function renderForm() {
				layui.use('form', function() {
					var form = layui.form;
					form.render();
				});
			};
			
			// 查询
			form.on('submit(systemProjectBonusManagerScreeningSub)', function(data) {
				refreshTable();
			});
			
			// 取消
			$("#systemProjectBonusManagerScreeningBox").off("click", "#systemProjectBonusManagerScreenCancel").on("click", "#systemProjectBonusManagerScreenCancel", function() {
				$("#systemProjectBonusManager .screeningBox").fadeOut(200);
			});
			
			// 重置
			$("body").off("click", "#systemProjectBonusManagerScreenCRest").on("click", "#systemProjectBonusManagerScreenCRest", function() {
//				document.getElementById("systemProjectBonusManagerScreening").reset();
				$('#systemProjectBonusManagerScreening input,#systemProjectBonusManagerScreening select').val('');
				$('#systemProjectBonusManagerScreening .select2-selection__rendered').attr('title','').html('<span class="select2-selection__placeholder">请选择</span>');
				refreshTable();
				return false;
			});
			
			// 初始化项目类型
			function initProjectType(){
				$.publicSelect(form, $("#systemProjectBonusManagerScreening select[name='projectType']"), {
					url: '/ts-hr/dict/combobox/cp_project_project_type',
					selectedValue: '',
					type: 'get',
				})
			}
			
			// 初始化项目状态
			function initProjectStatus(){
				$.publicSelect(form, $("#systemProjectBonusManagerScreening select[name='status']"), {
					url: '/ts-hr/dict/combobox/cp_project_project_status',
					selectedValue: '',
					type: 'get',
				})
			}
			
			// 初始化项目状态-添加
			function initProjectStatusAdd(){
				$.publicSelect(form, $("#systemProjectBonusManagerAddForm select[name='status']"), {
					url: '/ts-hr/dict/combobox/cp_project_project_status',
					selectedValue: 1,
					type: 'get',
				})
			}

			// 搜索-项目经理
			//$("body").off("change", "#pmName");
			
			var projectCodeIsExist = false;
			$("body").off('blur', '#systemProjectBonusManagerAddFormDiv #projectCode').on('blur', '#systemProjectBonusManagerAddFormDiv #projectCode', function(){
				var projectCode = $(this).val();
				$.ajax({
                    type: 'get',
                    url: common.url + "/ts-cp/baseProject/findByCode/" + projectCode,
                    success: function (data) {
                    	if(data && data.length != 0){
                    		layer.msg('项目编码已存在！');
                    		projectCodeIsExist = true;
                    	}else{
                    		projectCodeIsExist = false;
                    	}
                    }
                }); 
			});
			
			//新增
			$("body").off("click","#systemProjectBonusManagerAdd").on("click","#systemProjectBonusManagerAdd",function(){
				var html = systemProjectBonusManagerAddFormHtml.innerHTML;
                layer.open({
                    type: 1,
                    title: '新增项目',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['780px', '380px'], //宽高
                    content: html,
                    scrollbar: true,
                    success: function (layero, index) {
                        $(".layDate").each(function (i, e) {
                            laydate.render({
                                elem: this
                            });
                        })
                        $("#systemProjectBonusManagerAddFormDiv input").removeClass("layui-disabled");
                        initEmployeeSelect('#systemProjectBonusManagerAddFormDiv #pmName');
                        initdeptCodeSelect('#systemProjectBonusManagerAddFormDiv #deptCodeChooseBox');
                        initIncomeContractNoSelect();
                        form.render("radio");
                        form.render("select","selectBonusTypeForm");
                    },
                });
			});
			
			//编辑
			$("body").off("click","#systemProjectBonusManagerEditor").on("click","#systemProjectBonusManagerEditor",function(){
				var html = systemProjectBonusManagerAddFormHtml.innerHTML;
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                if(rowData.isExamine == 2 || rowData.isExamine == 3 || rowData.isExamine == 4){
                	layer.msg('该项目已开始申请项目奖，请选择其他记录操作！')
                    return false;
                }
                layer.open({
                    type: 1,
                    title: '编辑',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['780px', '380px'], //宽高
                    content: html,
                    success: function (layero, index) {
                        $(".layDate").each(function (i, e) {
                            laydate.render({
                                elem: this
                            });
                        })
                        $("#systemProjectBonusManagerAddFormDiv #contractIdName").val(rowData.contractNo);
                		$("#systemProjectBonusManagerAddFormDiv #contractIdName").text(rowData.contractName);
						initIncomeContractNoSelect();
                        
                        var id = rowData.projectBonusBaseId;
                        $.ajax({
                            type: 'post',
                            url: common.url + "/ts-cp/baseProject/findById/" + id,
                            success: function (data) {
                                if (data && data.success) {
                                    //项目类型
                                    var html = "";
                                    if (data.object.projectType == 1) {
                                        html += '<input type="radio" name="projectType" disabled value="1" title="新技术研发" checked="">'
                                        html += '<input type="radio" name="projectType" disabled value="2" title="新产品(项目)研发">';
                                        html += '<input type="radio" name="projectType" disabled value="3" title="产品升级改造">';
                                        html += '<input type="radio" name="projectType" disabled value="4" title="合同项目">';
                                    } else if (data.object.projectType == 2) {
                                        html += '<input type="radio" name="projectType" disabled value="1" title="新技术研发" >'
                                        html += '<input type="radio" name="projectType" disabled value="2" title="新产品(项目)研发" checked="">';
                                        html += '<input type="radio" name="projectType" disabled value="3" title="产品升级改造">';
                                        html += '<input type="radio" name="projectType" disabled value="4" title="合同项目">';
                                    } else if (data.object.projectType == 3) {
                                        html += '<input type="radio" name="projectType" disabled value="1" title="新技术研发">'
                                        html += '<input type="radio" name="projectType" disabled value="2" title="新产品(项目)研发">';
                                        html += '<input type="radio" name="projectType" disabled value="3" title="产品升级改造"  checked="">';
                                        html += '<input type="radio" name="projectType" disabled value="4" title="合同项目">';
                                    } else if (data.object.projectType == 4) {
                                        html += '<input type="radio" name="projectType" disabled value="1" title="新技术研发" >'
                                        html += '<input type="radio" name="projectType" disabled value="2" title="新产品(项目)研发">';
                                        html += '<input type="radio" name="projectType" disabled value="3" title="产品升级改造">';
                                        html += '<input type="radio" name="projectType" disabled value="4" title="合同项目" checked="">';
                                    }
                                   	
                                   	$("#systemProjectBonusManagerAddFormDiv input").prop("disabled","disabled");
                                   	$("#systemProjectBonusManagerAddFormDiv select").prop("disabled","disabled");
                                   	$("#systemProjectBonusManagerAddFormDiv #deptName").prop("disabled","");
                                   	$("#systemProjectBonusManagerAddFormDiv select[name='bonusType']").prop("disabled",false);
                                   	
                                   	var accountingBonusHtml = "";
                                 	if (data.object.isAccountingBonus == 1) {
                                 		accountingBonusHtml += '<input type="radio" name="isAccountingBonus" value="1" title="是" checked="" disabled>';
                                 		accountingBonusHtml += '<input type="radio" name="isAccountingBonus" value="2" title="否" disabled>';
                                 	} else if (data.object.isAccountingBonus == 2) {
                                 		accountingBonusHtml += '<input type="radio" name="isAccountingBonus" value="1" title="是">';
                                 		accountingBonusHtml += '<input type="radio" name="isAccountingBonus" value="2" title="否" checked="">';
                                 	} else {
                                 		accountingBonusHtml += '<input type="radio" name="isAccountingBonus" value="1" title="是" checked="">';
                                 		accountingBonusHtml += '<input type="radio" name="isAccountingBonus" value="2" title="否">';
                                 	}
                                   	
                                    initEmployeeSelect('#systemProjectBonusManagerAddFormDiv #pmName');
                                    initdeptCodeSelect('#systemProjectBonusManagerAddFormDiv #deptCodeChooseBox');
                                    trasen.setNamesVal(layero, data.object);
                                    form.render("select","selectBonusTypeForm");
                                    $("#systemProjectBonusManagerAddFormDiv #projectType").html(html);
                                    $("#systemProjectBonusManagerAddFormDiv #isAccountingBonus").html(accountingBonusHtml);
                                    form.render("radio");
                                }
                            }
                        })

                    }
                });
			});
			
			//初始化收入合同号下拉框
            function initIncomeContractNoSelect() {
                $.publicSelect2($("#systemProjectBonusManagerAddFormDiv #contractId"), {
                    url: "/ts-cp/income/contract/customerId/list",
                    valueName: "incomeContractInfoId",
                    textName: "contractNo,contractName",
                    condition: $("#systemProjectBonusManagerAddFormDiv #customerId").val(),
                    selectedCallback:function (repo) {
                        //查询合同信息
                        if(repo.id) {
                            $.ajax({
                                type: "get",
                                contentType: "application/json; charset=utf-8",
                                url: common.url + "/ts-cp/income/contract/info/"+repo.id,
                                success: function (res) {
                                    if (res.success) {
                                        var obj=res.object;
                                        if(obj){
                                        	$("#systemProjectBonusManagerAddFormDiv #contractName").val(obj.contractName);
                                        	$("#systemProjectBonusManagerAddFormDiv #contractNo").val(obj.contractNo);
                                        }
                                    }
                                }
                            });
                        }
                    }
                })
            }
			
			// 搜索-客户名称
			$("body").off("change", "#systemProjectBonusManagerAddFormDiv #customerId");
			form.on('select(customerId)', function(data) {
				var id = $("#systemProjectBonusManagerAddFormDiv #customerId").val();
			});
			
			//提交数据
            form.on('submit(systemProjectBonusManagerFormSaveProjectInfo)', function (data) {
                //获取员工选中的文本 赋值给data
                var projectBonusBaseId = data.field.projectBonusBaseId;
                if(!projectCodeIsExist){
                	if (projectBonusBaseId) {
	                    trasen.ajax({
	                        url: common.url + '/ts-cp/baseProject/update',
	                        type: 'POST',
	                        data: JSON.stringify(data.field),
	                        success: function (data) {
	                            layer.closeAll('page');
	                            refreshTable();
	                        }
	                    })
	                } else {
	                    trasen.ajax({
	                        url: common.url + '/ts-cp/baseProject/save',
	                        type: 'post',
	                        data: JSON.stringify(data.field),
	                        success: function (data) {
	                            layer.closeAll('page');
	                            refreshTable();
	                        }
	                    })
	                }
                }else{
                	layer.msg("项目编码已存在！");
                }
                
            });
			
			
			
/********************************************奖金设置*******************************************************************/
			
			//下拉字典
            var dictionariesDatas = {};
            common.tableSelect([
                {
                    dictionaries: 'contract_receive_project', //字典的参数名
                    cellname: 'receiveProject',  //表格下拉editoptions 里的value参数名
                    callback: function (res) {
                        dictionariesDatas = res;
                    }
                }
            ]);
			
			var tabIndex = 0;
            var basicinfoData, sendData = {};// 数据
            var selectData = [];
            var selectProData = [];
            var software, softwareReceivable, looktype, projectTaskTable, projectModulTable, projectModuleScheduleTable;
            var productClassifyCode = -1;// 产品类型
            sendData.cp = [];
			var productTableHardware_s, version_table;
			var projectTaskIdList = [];
			var projectModuleIdList = [];
            var trasenTableTick,
                addType;//产品列表新增判断是编辑新增变更   0:新增编辑 
			
			//关闭layer
            $("body").on("click", "#close", function () {
                layer.closeAll();
                sendData.initData = false;
                looktype = false;
                addType = -1;
            });
            
            //防止重复提交
            var flag = true;
            
			//设置奖金
            $("body").off("click", "#systemProjectBonusManagerSet").on("click", "#systemProjectBonusManagerSet", function () {
                var html = systemProjectBonusManagerBonusSetHtml.innerHTML;
                var rowdata = trasenTable.getSelectRowData();
                var bonusType = rowdata.bonusType;
                var isExamine = rowdata.isExamine;
                if (rowdata.length || rowdata.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                
                if(isExamine == 2){
    				layer.msg('该记录正在审批中，请选择其他记录进行操作！')
    				return false;
    			}else if(isExamine == 4){
    				var projectSchedule = rowdata.projectSchedule.replace('%','');
    				if(100 <= projectSchedule){
    					layer.msg('该项目的项目奖金已结算完毕，请选择其他记录进行操作！')
    					return false;
    				}
    				layer.confirm('该项目的项目奖金已结算完'+projectSchedule+'%，请问是否继续？', {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
	                }, function (index) {
	                	layer.close(index);
	                	//获取项目奖金审核批状态
		                var data = {projectBonusBaseId : rowdata.projectBonusBaseId};
		                data = JSON.stringify(data);
		                $.ajax({
		                    url: common.url + "/ts-cp/bnousInfo/findByProjectId",
		                    type: "post",
		                    contentType: "application/json; charset=utf-8",
		                    data: data,
		                    success: function (res) {
		                    	if(res.success){
					                tabIndex = 0;
					                basicinfoData = {};
					                sendData.changetype = 0;
					                softwareReceivable = null;
					                projectTaskTable = null;
					                //productTableHardware = null;
					                software = null;
					                var biglay = layer.open({
					                    type: 1,
					                    title: '设置奖金',
					                    closeBtn: 1,
					                    maxmin: true,
					                    shadeClose: false,
					                    area: ['100%', '100%'], //宽高
					                    skin: 'yourclass',
					                    content: html,
					                    success: function (layero, index) {
					                    	selectData = rowdata;
					                    	//判断项目类型，分配属性
					                    	if (bonusType == 1) {
							                	$("#systemProjectBonusManagerBonusSetDiv .travelAmount").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv .gradeType").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv #gradeType").removeAttr("lay-verify");
							                	$("#systemProjectBonusManagerBonusSetDiv #projectBonusPic").html("项目奖金 = 项目预算 * 奖金系数 * 难度系数 * (1 - 部门占比)");
							                } else if (bonusType == 2) {
							                	$("#systemProjectBonusManagerBonusSetDiv .travelAmount").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv .difficultyType").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv #difficultyType").removeAttr("lay-verify");
							                	$("#systemProjectBonusManagerBonusSetDiv .isHighLines").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv #isHighLines").removeAttr("lay-verify");
							                	$("#systemProjectBonusManagerBonusSetDiv #projectBonusPic").html("项目奖金 = 项目预算 * 奖金系数 * 评级系数 * (1 - 部门占比)");
							                	//计算奖金
						                        $("body").off("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics")
						                        		.on("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics", function () {
								                    computeProjectBonus();
								                });
								                form.on('select(gradeType)', function(data){
								                	computeProjectBonus(data.value);
								                });
							                } else if (bonusType == 3) {
							                	$("#systemProjectBonusManagerBonusSetDiv .difficultyType").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv #difficultyType").removeAttr("lay-verify");
							                	$("#systemProjectBonusManagerBonusSetDiv .gradeType").hide();
							                	$("#systemProjectBonusManagerBonusSetDiv #gradeType").removeAttr("lay-verify");
							                	$("#systemProjectBonusManagerBonusSetDiv #projectBonusPic").html("项目奖金 = 项目预算 * 奖金系数 * (1 - 部门占比) - 差旅费");
							                	//计算奖金
							                    $("body").off("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #travelAmountbasics")
							                    		.on("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #travelAmountbasics", function () {
								                    computeProjectBonus();
								                });
								                //form.on('select(difficultyType)', function(data){
								                	//computeProjectBonus(data.value);
												//});
							                }
							                //第二次及第二次以上进行奖金分配，部分属性不可修改
							                $("#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow,#systemProjectBonusManagerBonusSetDiv #bonusCoefficient").prop("disabled","disabled");
							                $("#systemProjectBonusManagerBonusSetDiv #difficultyType,#systemProjectBonusManagerBonusSetDiv #factPercent").prop("disabled","disabled");
					                        //计算奖金
					                        $("body").off("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics")
					                        		.on("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics", function () {
							                    var projectBudget=$("#systemProjectBonusManagerBonusSetDiv input[name='projectBudget']").val();
							                    projectBudget = projectBudget ? projectBudget.replace(/,/g,"") : 0;//项目预算
							                    var bonusCoefficient=$("#systemProjectBonusManagerBonusSetDiv input[name='bonusCoefficient']").val();
							                    bonusCoefficient = bonusCoefficient ? bonusCoefficient.replace(/,/g,"") : 0;//奖金系数
							                    var factPercent=$("#systemProjectBonusManagerBonusSetDiv input[name='factPercent']").val();
							                    factPercent = factPercent ? factPercent.replace(/,/g,"") : 0;//百分比
							                    //计算项目奖金
							                    $("#systemProjectBonusManagerBonusSetDiv input[name='projectBonus']").val((projectBudget*bonusCoefficient*factPercent/100).toFixed(2));
							                    $('#systemProjectBonusManagerBonusSetDiv #projectBonusbasicsShow').val($.formMoney((projectBudget*bonusCoefficient*factPercent/100).toFixed(2)));
							                });
							                
					                        //获取项目奖金
					                        if(res.object){
					                        	trasen.setNamesVal(layero, res.object);
					                        	$("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusInfoId']").val(res.object.id);
					                        	$('#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow').val($.formMoney(res.object.projectBudget));
					                        	$('#systemProjectBonusManagerBonusSetDiv #projectBonusbasicsShow').val($.formMoney(res.object.projectBonus));
					                        	$('#systemProjectBonusManagerBonusSetDiv #travelAmountbasicsShow').val($.formMoney(res.object.travelAmount));
					                        }
					                        $("#systemProjectBonusManagerBonusSetDiv input[name='bonusType']").val(bonusType);
					                        
					                        $("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusBaseId']").val(rowdata.projectBonusBaseId);
											projectTaskTable = new $.trasenTable("grid-table-bonusManagerTaskTable", {
						                        url: common.url + "/ts-cp/taskInfo/list",
						                        cellEdit: true,
						                        shrinkToFit: true,
						                        pager: '',
						                        sortorder: "asc",
						                        colNames: ['ID', '', '', '项目任务', '任务权重(%)', '权重说明', '项目权重(%)', '操作'],
						                        colModel: [
						                            { name: 'id', index: 'id', width: "auto", classes: 'triggerClick', align: "left", editable: false, hidden: true },
						                            { name: 'projectInfoId', index: 'project_info_id', width: "auto", align: "left", editable: false, hidden: true },
						                            { name: 'projectBonusInfoId', index: 'project_bonus_info_id', width: "auto", align: "left", editable: false, hidden: true },
						                            { name: 'taskName', index: 'task_name', width: 100, align: "left", editable: true },
						                            { name: 'taskWeight', index: 'task_weight', width: 60, align: "left", editable: true, edittype: 'text',
						                            	editrules: {
						                                    // number:true,minValue:10,maxValue:100,
						                                    custom: true,
						                                    custom_func: function (value, colNames) {
						                                        value = Number(value);
						                                        if (value > 100 || value < 0) {
						                                            return [false, "数值不在0～100"];
						                                        } else {
						                                            return [true];
						                                        }
						                                    }
						                                },
						                                formatter: function (cellvalue, options, rowObject) {
						                                    if (cellvalue) {
						                                        cellvalue = Number(cellvalue).toFixed(2);
						                                    } else {
						                                        cellvalue = '';
						                                    }
						                                    return cellvalue;
						                                }
						                            },
						                            { name: 'weightExplain', index: 'weight_explain', width: 300, align: "left",  editable: true},
						                            { name: 'projectWeight', index: 'project_weight', width: 60, align: "left", classes: 'redColor', editable: true },
						                            {
						                                name: '', index: '', width: 50, align: "center", editable: false, formatter: function (cellvalue, options, rowObject) {
						                                    return '<a href="javascript:;" class="projectTaskTablerowDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
						                                }
						                            }
						                        ],
						                        postData:{
						                        	projectBonusInfoId:$("#systemProjectBonusManagerBonusSetDiv #projectBonusInfoId").val()
						                        },
						                        serializeGridData: function(params){
						                        	return params;
						                        },
						                        afterEditCell: function (rowid, cellname, value, iRow, iCol) {
						                            
						                        },
						                        afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
						                        	if(cellname == 'taskWeight'){
						                        		//计算项目权重
						                        		projectTaskTable.setCell(rowid, 'projectWeight', Number(value).toFixed(2));
						                        	}
						                        	//计算合计
					                        		var self = this;
					                        		$("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerTaskTable .ui-jqgrid-sdiv").show();
					                                var taskWeight = jQuery(this).getCol('taskWeight', false, 'sum');
					                                var projectWeight = jQuery(this).getCol('projectWeight', false, 'sum');
					                                $("#systemProjectBonusManagerBonusSetDiv #" + self.id).jqGrid('footerData', 'set', { 'taskWeight': taskWeight, 'projectWeight': projectWeight });
					                                $("#systemProjectBonusManagerBonusSetDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
						                        },
						                        footerrow: true,
						                        gridComplete: function () {
													var self = this;
						                            var rowNum = parseInt($(this).getGridParam('records'), 10);
						                            if (rowNum > 0) {
						                                $("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerTaskTable .ui-jqgrid-sdiv").show();
						                                var taskWeight = jQuery(this).getCol('taskWeight', false, 'sum');
						                                var projectWeight = jQuery(this).getCol('projectWeight', false, 'sum');
						                                $("#systemProjectBonusManagerBonusSetDiv #" + self.id).jqGrid('footerData', 'set', { 'taskWeight': taskWeight, 'projectWeight': projectWeight });
						                                $("#systemProjectBonusManagerBonusSetDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
						                            } else {
						                                $("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerTaskTable .ui-jqgrid-sdiv").hide();
						                            }
						                        },
						                        userDataOnFooter: true
						                    });
						                    refreshTable();
						                    projectTaskTable.refresh();
					                        form.render();
					                    },
					                    cancel: function () {
					                        addType = -1;
					                    }
					                });
					                return false;
		                    	}
		                    }
		                });
	                }, function () {
                	});
    			}else{
    				//获取项目奖金审核批状态
	                var data = {projectBonusBaseId : rowdata.projectBonusBaseId};
	                data = JSON.stringify(data);
	                $.ajax({
	                    url: common.url + "/ts-cp/bnousInfo/findByProjectId",
	                    type: "post",
	                    contentType: "application/json; charset=utf-8",
	                    data: data,
	                    success: function (res) {
	                    	if(res.success){
				                tabIndex = 0;
				                basicinfoData = {};
				                sendData.changetype = 0;
				                softwareReceivable = null;
				                projectTaskTable = null;
				                //productTableHardware = null;
				                software = null;
				                var biglay = layer.open({
				                    type: 1,
				                    title: '设置奖金',
				                    closeBtn: 1,
				                    maxmin: true,
				                    shadeClose: false,
				                    area: ['100%', '100%'], //宽高
				                    skin: 'yourclass',
				                    content: html,
				                    success: function (layero, index) {
				                    	selectData = rowdata;
				                    	//判断项目类型，分配属性
				                    	if (bonusType == 1) {
						            		/*自主研发类*/
						                	$("#systemProjectBonusManagerBonusSetDiv .travelAmount").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv .gradeType").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv #gradeType").removeAttr("lay-verify");
						                	$("#systemProjectBonusManagerBonusSetDiv #projectBonusPic").html("项目奖金 = 项目预算 * 奖金系数 * 难度系数 * (1 - 部门占比)");
						                	//计算奖金
						                    $("body").off("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics")
						                    		.on("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics", function () {
							                    computeProjectBonus();
							                });
							                form.on('select(difficultyType)', function(data){
							                	computeProjectBonus(data.value);
											});
						                } else if (bonusType == 2) {
						                	/*维护类*/
						                	$("#systemProjectBonusManagerBonusSetDiv .travelAmount").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv .difficultyType").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv #difficultyType").removeAttr("lay-verify");
						                	$("#systemProjectBonusManagerBonusSetDiv .isHighLines").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv #isHighLines").removeAttr("lay-verify");
						                	$("#systemProjectBonusManagerBonusSetDiv #projectBonusPic").html("项目奖金 = 项目预算 * 奖金系数 * 评级系数 * (1 - 部门占比)");
						                	//计算奖金
						                    $("body").off("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent, #systemProjectBonusManagerBonusSetDiv #projectBudgetbasics")
						                    		.on("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent, #systemProjectBonusManagerBonusSetDiv #projectBudgetbasics", function () {
							                    computeProjectBonus();
							                });
							                form.on('select(gradeType)', function(data){
							                	computeProjectBonus(data.value);
											});
						                } else if (bonusType == 3) {
						                	/*实施类*/
						                	$("#systemProjectBonusManagerBonusSetDiv .difficultyType").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv #difficultyType").removeAttr("lay-verify");
						                	$("#systemProjectBonusManagerBonusSetDiv .gradeType").hide();
						                	$("#systemProjectBonusManagerBonusSetDiv #gradeType").removeAttr("lay-verify");
						                	$("#systemProjectBonusManagerBonusSetDiv #projectBonusPic").html("项目奖金 = 项目预算 * 奖金系数 * (1 - 部门占比) - 差旅费");
						                	//计算奖金
						                    $("body").off("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #travelAmountbasics")
						                    		.on("change", "#systemProjectBonusManagerBonusSetDiv .focusEvent,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #travelAmountbasics", function () {
							                    computeProjectBonus();
							                });
							                //form.on('select(difficultyType)', function(data){
							                	//computeProjectBonus(data.value);
											//});
						                }
						                
						                if(rowdata.projectSchedule){
						                	//第二次及第二次以上进行奖金分配，部分属性不可修改
							                $("#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow,#systemProjectBonusManagerBonusSetDiv #bonusCoefficient").prop("disabled","disabled");
								            $("#systemProjectBonusManagerBonusSetDiv #difficultyType,#systemProjectBonusManagerBonusSetDiv #factPercent").prop("disabled","disabled");
								            $("#systemProjectBonusManagerBonusSetDiv #projectBudgetbasics,#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow,#systemProjectBonusManagerBonusSetDiv #bonusCoefficient").addClass("layui-disabled")
						                }
						                
				                        //获取项目奖金
				                        if(res.object){
				                        	trasen.setNamesVal(layero, res.object);
				                        	$("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusInfoId']").val(res.object.id);
				                        	$('#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow').val($.formMoney(res.object.projectBudget));
				                        	$('#systemProjectBonusManagerBonusSetDiv #projectBonusbasicsShow').val($.formMoney(res.object.projectBonus));
				                        	$('#systemProjectBonusManagerBonusSetDiv #travelAmountbasicsShow').val($.formMoney(res.object.travelAmount));
				                        }
				                        $("#systemProjectBonusManagerBonusSetDiv input[name='bonusType']").val(bonusType);
				                        
				                        $("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusBaseId']").val(rowdata.projectBonusBaseId);
										projectTaskTable = new $.trasenTable("grid-table-bonusManagerTaskTable", {
					                        url: common.url + "/ts-cp/taskInfo/list",
					                        cellEdit: true,
					                        shrinkToFit: true,
					                        pager: '',
					                        sortorder: "asc",
					                        colNames: ['ID', '', '', '项目任务', '任务权重(%)', '权重说明', '项目权重(%)', '操作'],
					                        colModel: [
					                            { name: 'id', index: 'id', width: "auto", classes: 'triggerClick', align: "left", editable: false, hidden: true },
					                            { name: 'projectInfoId', index: 'project_info_id', width: "auto", align: "left", editable: false, hidden: true },
					                            { name: 'projectBonusInfoId', index: 'project_bonus_info_id', width: "auto", align: "left", editable: false, hidden: true },
					                            { name: 'taskName', index: 'task_name', width: 100, align: "left", editable: true },
					                            { name: 'taskWeight', index: 'task_weight', width: 60, align: "left", editable: true, edittype: 'text',
					                            	editrules: {
					                                    // number:true,minValue:10,maxValue:100,
					                                    custom: true,
					                                    custom_func: function (value, colNames) {
					                                        value = Number(value);
					                                        if (value > 100 || value < 0) {
					                                            return [false, "数值不在0～100"];
					                                        } else {
					                                            return [true];
					                                        }
					                                    }
					                                },
					                                formatter: function (cellvalue, options, rowObject) {
					                                    if (cellvalue) {
					                                        cellvalue = Number(cellvalue).toFixed(2);
					                                    } else {
					                                        cellvalue = '';
					                                    }
					                                    return cellvalue;
					                                }
					                            },
					                            { name: 'weightExplain', index: 'weight_explain', width: 300, align: "left",  editable: true},
					                            { name: 'projectWeight', index: 'project_weight', width: 60, align: "left", classes: 'redColor', editable: true },
					                            {
					                                name: '', index: '', width: 50, align: "center", editable: false, formatter: function (cellvalue, options, rowObject) {
					                                    return '<a href="javascript:;" class="projectTaskTablerowDel" ><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
					                                }
					                            }
					                        ],
					                        postData:{
					                        	projectBonusInfoId:$("#systemProjectBonusManagerBonusSetDiv #projectBonusInfoId").val()
					                        },
					                        serializeGridData: function(params){
					                        	params.projectBonusInfoId = $("#systemProjectBonusManagerBonusSetDiv #projectBonusInfoId").val()
					                        	return params;
					                        },
					                        afterEditCell: function (rowid, cellname, value, iRow, iCol) {
					                            
					                        },
					                        afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
					                        	if(cellname == 'taskWeight'){
					                        		//计算项目权重
					                        		projectTaskTable.setCell(rowid, 'projectWeight', Number(value).toFixed(2));
					                        	}
					                        	//计算合计
				                        		var self = this;
				                        		$("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerTaskTable .ui-jqgrid-sdiv").show();
				                                var taskWeight = jQuery(this).getCol('taskWeight', false, 'sum');
				                                var projectWeight = jQuery(this).getCol('projectWeight', false, 'sum');
				                                $("#systemProjectBonusManagerBonusSetDiv #" + self.id).jqGrid('footerData', 'set', { 'taskWeight': taskWeight, 'projectWeight': projectWeight });
				                                $("#systemProjectBonusManagerBonusSetDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
					                        },
					                        footerrow: true,
					                        gridComplete: function () {
												var self = this;
					                            var rowNum = parseInt($(this).getGridParam('records'), 10);
					                            if (rowNum > 0) {
					                                $("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").show();
					                                var taskWeight = jQuery(this).getCol('taskWeight', false, 'sum');
					                                var projectWeight = jQuery(this).getCol('projectWeight', false, 'sum');
					                                $("#systemProjectBonusManagerBonusSetDiv #" + self.id).jqGrid('footerData', 'set', { 'taskWeight': taskWeight, 'projectWeight': projectWeight });
					                                $("#systemProjectBonusManagerBonusSetDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
					                            } else {
					                                $("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").hide();
					                            }
					                        },
					                        userDataOnFooter: true
					                    });
					                    projectTaskTable.refresh();
				                        form.render();
				                    },
				                    cancel: function () {
				                        addType = -1;
				                    }
				                });
				                return false;
	                    	}
	                    }
	                });
    			}
            });
            
            var i=1000;
            //新增项目
            $('body').off('click','#systemProjectBonusManagerBonusSetDiv #addprojectTaskTable').on('click','#systemProjectBonusManagerBonusSetDiv #addprojectTaskTable',function(){
                $('#systemProjectBonusManagerBonusSetDiv #grid-table-hardwareReceivable .triggerClick').trigger('click');
                var data = {id:i};
                projectTaskTable.addRowData(i,data,'last');
                i--;
            });
            //新增模块
            $('body').off('click','#systemProjectBonusManagerBonusSetDiv #addprojectModulTable').on('click','#systemProjectBonusManagerBonusSetDiv #addprojectModulTable',function(){
                $('#systemProjectBonusManagerBonusSetDiv #grid-table-hardwareReceivable .triggerClick').trigger('click');
                var data = {id:i};
                projectModulTable.addRowData(i,data,'last');
                i--;
            });
            
            // 下一步保存操作
            form.on('submit(systemProjectBonusManagerBasicinfo)', function (data) {
                var t = $(this).attr('clicktype');
                if (t == 0) {
                    $(this).attr('sendtype', 0)
                    return false
                }
                addsub(data.field);
            });
            //提交
//          form.on('submit(addSub)', function (data) {
//              var index = $('.layui-tab-title li.layui-this').index();
////              $('#systemProjectBonusManagerBasicinfo').attr('clicktype', '0');
////              $('#systemProjectBonusManagerBasicinfo').trigger('click');
//              saveFun(data, index, true);
//              layer.closeAll();
//          });
            //保存继续
            form.on('submit(systemProjectBonusManagerAddSave)', function (data) {
                var index = $('#systemProjectBonusManagerBonusSetDiv .layui-tab-title li.layui-this').index();
                saveFun(data, index, true);
            });
            
            //新增保存
            function addsub(data) {
                var tTp = $('#systemProjectBonusManagerBasicinfo').attr('data-type');
                var tId = $('#systemProjectBonusManagerBonusSetDiv #id').val();
                var commonUrl = '';
                if(tId){
                	commonUrl = common.url + "/ts-cp/bnousInfo/update";
                }else{
                	commonUrl = common.url + "/ts-cp/bnousInfo/save";
                }
                var totalTaskWeight = 0;
                var totalProjectWeight = 0;
                for(var i in basicinfoData){
                	//添加需删除的任务ID
                	basicinfoData[i].projectTaskIdList = projectTaskIdList;
                	var projectWeight = basicinfoData[i].projectWeight*1;
                	var taskWeight = basicinfoData[i].taskWeight*1;
    				totalTaskWeight = numAdd(totalTaskWeight, taskWeight);
    				totalProjectWeight = numAdd(totalProjectWeight, projectWeight);
    			}
                totalProjectWeight = totalProjectWeight.toFixed(2)*1;
                if(totalProjectWeight != 100){
                	layer.msg('项目权重总和必须等于100%!');
                    return false;
                }
                totalTaskWeight = totalTaskWeight.toFixed(2)*1;
                if(totalTaskWeight != 100){
                	layer.msg('任务权重总和必须等于100%!');
                    return false;
                }
                if(data.projectBonus <= 0){
                	layer.msg('项目奖金必须大于0!');
                    return false;
                }
                sendData = data;
                data.projectTaskList = basicinfoData;
                //清空集合
				projectTaskIdList= [];
                var figure = 0;
                data = JSON.stringify(data);
                
                trasen.loading();
                if(flag){
                	flag = false;
                	$.ajax({
	                    url: commonUrl,
	                    type: "post",
	                    contentType: "application/json; charset=utf-8",
	                    data: data,
	                    success: function (res) {
	                        trasen.closeLoading();
	                        if(!tId && res.success){
	                        	$("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusInfoId']").val(res.object);
	                        }
	                        refreshTable();
	                        projectTaskTable.refresh();
	                        setTimeout(function() {
	                        	flag = true;
	                        }, 500);
	                        layer.msg('保存成功！');
	                    },
	                    error: function (res) {
	                    	flag = true;
	                        trasen.closeLoading();
	                        layer.msg(res.message);
	                    }
	                });
                }
                
            }
            
            function saveFun(data, index, type) {
            	if (index == 0 && type != false) {
            		basicinfoData = projectTaskTable.getAllData();
            		if (type) {
	                    $('#systemProjectBonusManagerBasicinfo').attr('data-type', 1);
	                }
	                $('#systemProjectBonusManagerBasicinfo').trigger('click');
	                return false
            	}
            	if (index == 1 || type == false) {
            		var projectInfoId = $("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusBaseId']").val();
            		var projectBonusInfoId = $("#systemProjectBonusManagerBonusSetDiv input[name='projectBonusInfoId']").val();
            		var projectTaskInfoId = $("#systemProjectBonusManagerBonusSetDiv input[name='projectTaskInfoId']").val();
            		var data = {
            			"projectInfoId" : projectInfoId, 
            			"projectBonusInfoId" : projectBonusInfoId,
            			"projectTaskInfoId" : projectTaskInfoId
            		};
            		basicinfoData = projectModulTable.getAllData();
            		var totalModuleWeight = 0;
            		var totalProjectWeight = 0;
            		if(basicinfoData){
            			for(var i in basicinfoData){
            				basicinfoData[i].projectInfoId = projectInfoId;
            				basicinfoData[i].projectBonusInfoId = projectBonusInfoId;
            				basicinfoData[i].projectTaskInfoId = projectTaskInfoId;
            				//添加需删除的模块ID
            				basicinfoData[i].projectModuleIdList = projectModuleIdList;
            				var moduleWeight = basicinfoData[i].moduleWeight*1;
            				var projectWeight = basicinfoData[i].projectWeight*1;
            				
    						totalModuleWeight = numAdd(totalModuleWeight, moduleWeight);
    						totalProjectWeight = numAdd(totalProjectWeight, projectWeight);
            			}
            		}else{
            			layer.msg('请添加模块信息！');
	                    return false;
            		}
            		var taskWeight = $("#systemProjectBonusManagerBonusSetDiv #moduleWeightPic").html().replace('%','');
            		totalProjectWeight = totalProjectWeight.toFixed(2);
            		if(totalProjectWeight > taskWeight){
            			layer.msg('项目权重总和不能大于任务权重');
	                    return false;
            		}
	                if(totalModuleWeight != 100){
	                	layer.msg('模块权重总和必须等于100%!');
	                    return false;
	                }
	                //清空集合
            		projectModuleIdList = [];
            		data.projectTaskModuleList = basicinfoData;
            		data = JSON.stringify(data);
            		trasen.loading();
            		if(flag){
            			flag= false;
            			$.ajax({
		                    url: common.url + "/ts-cp/taskModuleInfo/multiterm/save",
		                    type: "post",
		                    contentType: "application/json; charset=utf-8",
		                    data: data,
		                    success: function (res) {
		                        trasen.closeLoading();
		                        projectModulTable.refresh();
		                        setTimeout(function() {
		                        	flag = true;
		                        }, 500);
		                        layer.msg('保存成功！');
		                    },
		                    error: function (res) {
		                    	flag = true;
		                        trasen.closeLoading();
		                        layer.msg(res.message);
		                    }
		                });
            		}
	                
            	}
            }
            
            //行删除
            $('body').off('click', '#systemProjectBonusManagerBonusSetDiv .projectTaskTablerowDel').on('click', '#systemProjectBonusManagerBonusSetDiv .projectTaskTablerowDel', function () {
                var rowid;
                if(projectModulTable){
                	rowid = projectModulTable.getSelectRowId();
                	layer.confirm('确定要删除该模块吗？</br>若删除该模块，将同时删除该模块下的人员及进度信息！', {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
	                }, function (index) {
						projectModuleIdList.push(rowid);
						projectModulTable.delRowData(rowid);
	                    layer.close(index);
	                }, function () {
	                });
                }else{
                	rowid = projectTaskTable.getSelectRowId();
                	layer.confirm('确定要删除该任务吗？</br>若删除该任务，将同时删除该任务下的所有子模块！', {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
	                }, function (index) {
						projectTaskIdList.push(rowid);
						projectTaskTable.delRowData(rowid);
		                layer.close(index);
					}, function () {
	               });
                }
            });

            //tab切换
            element.on('tab(systemProjectBonusManagerNcomeLaytab)', function () {
                var txt = $(this).attr('title');
                var ind = $(this).index();
                tabIndex = ind;
                projectModulTable = null;
                if (ind == 1) {
                    //
                    var getRowData = projectTaskTable.getSelectRowData();
                    if (getRowData.length || getRowData.length == 0) {
                        layer.msg('请先选择一条项目任务');
                        tabIndex--
                        $('#systemProjectBonusManagerBonusSetDiv .layui-tab-title li').eq(tabIndex).trigger('click');
                        return false
                    }
                    if (getRowData.id && getRowData.id.length < 10) {
                        layer.msg('请先保存当前任务！');
                        tabIndex--
                        $('#systemProjectBonusManagerBonusSetDiv .layui-tab-title li').eq(tabIndex).trigger('click');
                        return false
                    }
                    var projectInfoId = getRowData.projectInfoId;
                    var projectBonusInfoId = getRowData.projectBonusInfoId;
                    var projectTaskInfoId = getRowData.id;
                    $("#systemProjectBonusManagerBonusSetDiv #moduleWeightPic").html(getRowData.taskWeight+"%");
//                  $("input[name='projectBonusBaseId']").val(projectInfoId);
//                  $("input[name='projectBonusInfoId']").val(projectBonusInfoId);
                    $("#systemProjectBonusManagerBonusSetDiv input[name='projectTaskInfoId']").val(projectTaskInfoId);

                    projectModulTable = new $.trasenTable("grid-table-bonusManagerModuleTable", {
                        url: common.url + "/ts-cp/taskModuleInfo/list",
                        cellEdit: true,
                        shrinkToFit: true,
                        pager: '',
                        sortorder: "asc",
                        colNames: ['ID', '', '', '', '模块名称', '模块权重(%)', '权重说明', '项目权重(%)', '操作'],
                        colModel: [
                            { name: 'id', index: 'id', width: "auto", classes: 'triggerClick', align: "left", editable: false, hidden: true },
                            { name: 'projectInfoId', index: 'project_info_id', width: "auto", align: "left", editable: false, hidden: true },
                            { name: 'projectBonusInfoId', index: 'project_bonus_info_id', width: "auto", align: "left", editable: false, hidden: true },
                            { name: 'projectTaskInfoId', index: 'project_task_info_id', width: "auto", align: "left", editable: false, hidden: true },
                            { name: 'moduleName', index: 'module_name', width: 160, align: "left", editable: true },
                            { name: 'moduleWeight', index: 'module_weight', width: 100, align: "left", editable: true, edittype: 'text',
                            	editrules: {
                                    custom: true,
                                    custom_func: function (value, colNames) {
                                        value = Number(value);
                                        if (value > 100 || value < 0) {
                                            return [false, "数值不在0～100"];
                                        } else {
                                            return [true];
                                        }
                                    }
                                },
                                formatter: function (cellvalue, options, rowObject) {
                                    if (cellvalue) {
                                        cellvalue = Number(cellvalue).toFixed(2);
                                    } else {
                                        cellvalue = '';
                                    }
                                    return cellvalue;
                                }
                            },
                            { name: 'weightExplain', index: 'weight_explain', width: 240, align: "left", editable: true },
                            {
                                name: 'projectWeight', index: 'project_weight', width: 100, align: "right", classes: 'redColor', editable: true, edittype: 'text',
                                formatter: "number",
                                formatoptions: {
                                    decimalSeparator: ".",
                                    thousandsSeparator: ",",
                                    decimalPlaces: 2,
                                    defaulValue: 0
                                }
                            },
                            {
                                name: '', index: '', width: 50, align: "center", editable: false, formatter: function (cellvalue, options, rowObject) {
                                    return '<a href="javascript:;" class="projectTaskTablerowDel" id="incomeLayDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                                }
                            }
                        ],
                        afterEditCell: function (rowid, cellname, value, iRow, iCol) {
                            
                        },
                        postData:{
                        	projectTaskInfoId : projectTaskInfoId
                        },
                        buidQueryParams: function() {
							var data = {};
							data["projectTaskInfoId"] = projectTaskInfoId;
							return data;
						},
                        afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                            if(cellname == 'moduleWeight'){
                        		//计算项目权重
                        		var taskWeight = $("#systemProjectBonusManagerBonusSetDiv #moduleWeightPic").html().replace('%','');
                        		projectModulTable.setCell(rowid, 'projectWeight', (value*taskWeight/100).toFixed(2));
                        	}
                            //计算合计
                    		var self = this;
                    		$("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerModuleTable .ui-jqgrid-sdiv").show();
                            var moduleWeight = jQuery(this).getCol('moduleWeight', false, 'sum');
                            var projectWeight = jQuery(this).getCol('projectWeight', false, 'sum');
                            $("#systemProjectBonusManagerBonusSetDiv #" + self.id).jqGrid('footerData', 'set', { 'moduleWeight': moduleWeight, 'projectWeight': projectWeight });
                            $("#systemProjectBonusManagerBonusSetDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                        },
                        footerrow: true,
                        gridComplete: function () {
                            var self = this;
                            var rowNum = parseInt($(this).getGridParam('records'), 10);
                            if (rowNum > 0) {
                                $("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerModuleTable .ui-jqgrid-sdiv").show();
                                var moduleWeight = jQuery(this).getCol('moduleWeight', false, 'sum');
                                var projectWeight = jQuery(this).getCol('projectWeight', false, 'sum');
                                $("#systemProjectBonusManagerBonusSetDiv #" + self.id).jqGrid('footerData', 'set', { 'moduleWeight': moduleWeight, 'projectWeight': projectWeight });
                                $("#systemProjectBonusManagerBonusSetDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                            } else {
                                $("#systemProjectBonusManagerBonusSetDiv #gview_grid-table-bonusManagerModuleTable .ui-jqgrid-sdiv").hide();
                            }
                        },
                        userDataOnFooter: true
                    });

                    projectModulTable.refresh();


                    //项目任务 end
                } else if (ind == 2) {
                    addType = 0;
                    //任务模块
                    var tId = $('#systemProjectBonusManagerBonusSetDiv #remittance').val();
                    if (tId == '') {
                        layer.msg('请先编辑基本信息');
                        tabIndex--
                        $('#systemProjectBonusManagerBonusSetDiv .layui-tab-title li').eq(tabIndex).trigger('click');
                        return false
                    }
                    projectModuleScheduleTable = new $.trasenTable("project-module-schedule-table", {
                        url: common.url + "/ts-cp/taskModuleInfo/find/list",
                        cellEdit: true,
                        shrinkToFit: true,
                        pager: '',
                        colNames: ['ID', '', '', '', '任务名称', '模块名称', '模块权重(%)', '完成度(%)', '人员分配', '个人比重(%)', '项目权重(%)', '操作'],
                        colModel: [
                            { name: 'id', index: 'id', width: "auto", classes: 'triggerClick', align: "left", editable: false, hidden: true },
                            { name: 'projectInfoId', index: 'project_info_id', width: "auto", align: "left", editable: false, hidden: true },
                            { name: 'projectBonusInfoId', index: 'project_bonus_info_id', width: "auto", align: "left", editable: false, hidden: true },
                            { name: 'projectTaskInfoId', index: 'project_task_info_id', width: "auto", align: "left", editable: false, hidden: true },
                            { name: 'projectTaskInfoName', index: 'projectTaskInfoName', width: 120, editable: false, sortable: false, 
                            	cellattr: function(rowId, tv, rawObject, cm, rdata) {
                                    //合并单元格
                                    return 'id=\'projectTaskInfoName' + rowId + "\'";
                                }
                            },
                            { name: 'moduleName', index: 'module_name', width: 120, editable: false, sortable: false, 
                            	cellattr: function(rowId, tv, rawObject, cm, rdata) {
                                    //合并单元格
                                    return 'id=\'moduleName' + rowId + "\'";
                                }
                            },
                            { name: 'moduleWeight', index: 'module_weight', width: 60, editable: false, sortable: false, 
                            	cellattr: function(rowId, tv, rawObject, cm, rdata) {
                                    //合并单元格
                                    return 'id=\'moduleWeight' + rowId + "\'";
                                }
                            },
                            { name: 'moduleExecution', index: 'module_execution', width: 60, editable: true, sortable: false, 
                            	cellattr: function(rowId, tv, rawObject, cm, rdata) {
                                    //合并单元格
                                    return 'id=\'moduleExecution' + rowId + "\'";
                                }
                            },
                            {
                                name: 'weightExplain', index: 'weight_explain', width: 60, align: "left", editable: true, edittype: 'text',
                                editrules: {
                                    // number:true,minValue:10,maxValue:100,
                                    custom: true,
                                    custom_func: function (value, colNames) {
                                        value = Number(value);
                                        if (value > 100 || value < 0) {
                                            return [false, "数值不在0～100"];
                                        } else {
                                            return [true];
                                        }
                                    }
                                },
                                formatter: function (cellvalue, options, rowObject) {
                                    if (cellvalue) {
                                        cellvalue = Number(cellvalue).toFixed(1);
                                    } else {
                                        cellvalue = '';
                                    }
                                    return cellvalue;
                                }
                            },
                            {
                                name: 'personProportion', index: 'person_proportion', width: 60, align: "right", classes: 'redColor', editable: false, edittype: 'text',
                                formatter: "number",
                                formatoptions: {
                                    decimalSeparator: ".",
                                    thousandsSeparator: ",",
                                    decimalPlaces: 2,
                                    defaulValue: 0
                                }
                            },
                            {
                                name: 'projectWeight', index: 'project_weight', width: 60, align: "right", classes: 'redColor', editable: false, edittype: 'text',
                                formatter: "number",
                                formatoptions: {
                                    decimalSeparator: ".",
                                    thousandsSeparator: ",",
                                    decimalPlaces: 2,
                                    defaulValue: 0
                                }
                            },
                            {
                                name: '', index: '', width: 50, align: "center", editable: false, formatter: function (cellvalue, options, rowObject) {
                                    return '<a href="javascript:;" class="projectModulTablerowDel" id="incomeLayDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>&nbsp;&nbsp;'
                                    	
                                    	+'<a href="javascript:;" class="projectModulTablerowDel" data-table="projectModuleScheduleTable" id="addprojectModuleScheduleTable"><i class="fa fa-plus-circle" aria-hidden="true"></i></a>';
                                }
                            }
                        ],
                        afterEditCell: function (rowid, cellname, value, iRow, iCol) {
                            
                        },
                        postData:{
                        	projectTaskInfoId : projectTaskInfoId
                        },
                        buidQueryParams: function() {
							var data = {};
							data["projectTaskInfoId"] = projectTaskInfoId;
							return data;
						},
                        afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                        },
                        footerrow: true,
                        gridComplete: function () {
							//②在gridComplete调用合并方法
                            var gridName = "project-module-schedule-table";
                            Merger(gridName, ['projectTaskInfoName', 'moduleName', 'moduleWeight', 'moduleExecution']);
                        },
                        userDataOnFooter: true
                    });
                    
                    projectModuleScheduleTable.refresh();
                    //产品 end
                }
            });
            
            //空白出退出编辑状态
            $('body').off('click', '#systemProjectBonusManagerBonusSetDiv .layui-layer-shade, .layui-layer.layui-layer-page.yourclass').on('click', '#systemProjectBonusManagerBonusSetDiv .layui-layer-shade, .layui-layer.layui-layer-page.yourclass', function (e) {
                $('#systemProjectBonusManagerBonusSetDiv #taskHtml .selected-row.ui-state-hover td').eq(0).trigger('click');
                $('#systemProjectBonusManagerBonusSetDiv #softwareReceivablePicHtml .selected-row.ui-state-hover td').eq(0).trigger('click');
            });
            $('body').off('click', '#systemProjectBonusManagerBonusSetDiv input[name="bonusCoefficient"],#systemProjectBonusManagerBonusSetDiv input[name="factPercent"],#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow,#systemProjectBonusManagerBonusSetDiv #travelAmountbasicsShow').on(
            		'click', '#systemProjectBonusManagerBonusSetDiv input[name="bonusCoefficient"],#systemProjectBonusManagerBonusSetDiv input[name="factPercent"],#systemProjectBonusManagerBonusSetDiv #projectBudgetbasicsShow,#systemProjectBonusManagerBonusSetDiv #travelAmountbasicsShow', function (e) {
                e.stopPropagation();
            })
            $('body').off('click', '#systemProjectBonusManagerBonusSetDiv #taskHtml td,#systemProjectBonusManagerBonusSetDiv #softwareReceivablePicHtml td').on('click', '#systemProjectBonusManagerBonusSetDiv #taskHtml td,#systemProjectBonusManagerBonusSetDiv #softwareReceivablePicHtml td', function (e) {
                e.stopPropagation();
            })
            
            //查看
            $("body").off("click", "#projectBudgetTableEditor").on("click", "#projectBudgetTableEditor", function () {
                var rowid = $(this).attr('data-rowId');
                var rowData = trasenTable.getRowData(rowid);
                var bonusType = rowData.bonusType;
                var projectBonusInfoId = rowData.projectBonusInfoId;
                if (rowData.projectBonusBaseId == null || rowData.projectBonusBaseId == undefined || rowData.projectBonusBaseId == '') {
                    layer.msg('请选中需要操作数据');
                    return false;
                }
                if(!rowData.projectBonusInfoId){
                	layer.msg('请选择 已申请/申请中 项目奖金的数据操作');
                    return false;
                }
                var html = systemProjectBonusManagerDetailHtml.innerHTML;
                layer.open({
                    type: 1,
                    title: '详情',
                    closeBtn: 1,
                    shadeClose: false,
                    maxmin: true,
                    area: ['100%', '100%'], //宽高
                    skin: 'yourclass',
                    content: html,
                    success: function (layero, index) {
                    	var id = rowData.projectBonusInfoId;
                    	$.ajax({
                            type: "get",
                            url: common.url + '/ts-cp/bnousInfo/findById/'+id,
                            success: function (data) {
                            	if(data.success){
                            		rowData = data.object;
                            		//判断项目类型，分配属性
			                        if (bonusType == 1) {
			                            /*自主研发类*/
			                            $("#systemProjectBonusManagerDetailDiv .travelAmount").hide();
			                            $("#systemProjectBonusManagerDetailDiv .gradeType").hide();
			                        } else if (bonusType == 2) {
			                            /*维护类*/
			                            $("#systemProjectBonusManagerDetailDiv .travelAmount").hide();
			                            $("#systemProjectBonusManagerDetailDiv .difficultyType").hide();
			                            $("#systemProjectBonusManagerDetailDiv .isHighLines").hide();
			                        } else if (bonusType == 3) {
			                            /*实施类*/
			                            $("#systemProjectBonusManagerDetailDiv .difficultyType").hide();
			                            $("#systemProjectBonusManagerDetailDiv .gradeType").hide();
			                        }
			
			                        trasen.setNamesVal(layero, rowData);
			                        $("#systemProjectBonusManagerDetailDiv #remark").val("");
			                        form.render('select');
			                        var projectBonus = rowData.projectBonus * 1;
			                        var factSchedule = rowData.factSchedule ? rowData.factSchedule * 1 : 0;
			                        var projectSchedule = rowData.projectSchedule ? rowData.projectSchedule * 1 : 0;
			                        var developQuality = rowData.developQuality ? rowData.developQuality * 1 : 0;
			                        var balanceQualityBonus = rowData.balanceQualityBonus ? rowData.balanceQualityBonus * 1 : 0;

                                    $("#systemProjectBonusManagerDetailDiv #thisSchedule").val(factSchedule - projectSchedule);
									$("#systemProjectBonusManagerDetailDiv #travelAmountbasicsShow").val($.formMoney(rowData.travelAmount));
			                        $('#systemProjectBonusManagerDetailDiv #projectBudgetbasicsShow').val($.formMoney(rowData.projectBudget));
			                        $('#systemProjectBonusManagerDetailDiv #projectBonusbasicsShow').val($.formMoney(rowData.projectBonus));
			
			                        if (factSchedule != 0) {
			                            var thisBalanceQualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
			                            $('#systemProjectBonusManagerDetailDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(thisBalanceQualityBonus));
			                            $('#systemProjectBonusManagerDetailDiv #thisBalanceQualityBonusbasics').val(thisBalanceQualityBonus);
			                        } else {
			                            $('#systemProjectBonusManagerDetailDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(0));
			                            $('#systemProjectBonusManagerDetailDiv #thisBalanceQualityBonusbasics').val(0);
			                        }
			                        if (balanceQualityBonus != 0) {
			                            $('#systemProjectBonusManagerDetailDiv #balanceQualityBonusbasicsShow').val($.formMoney(balanceQualityBonus));
			                            $('#systemProjectBonusManagerDetailDiv #balanceQualityBonusbasics').val(balanceQualityBonus);
			                        } else {
			                            $('#systemProjectBonusManagerDetailDiv #balanceQualityBonusbasicsShow').val($.formMoney(0));
			                            $('#systemProjectBonusManagerDetailDiv #balanceQualityBonusbasics').val(0);
			                        }
			
			                        var bonus = '';
			                        var qualityBonus = '';
			                        if (factSchedule && developQuality) {
			                            bonus = (projectBonus * (factSchedule - projectSchedule) * developQuality / 10000).toFixed(2);
			                            qualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
			                        }
			
									$('#systemProjectBonusManagerDetailDiv #projectBonusPic').html($.formMoney(rowData.projectBonus));
			                        if(factSchedule == 100){
			                        	$("#systemProjectBonusManagerDetailDiv #scheduleTypeDiv").show();
			                        	$("#systemProjectBonusManagerDetailDiv #delayBonusDiv").show();
			                        	$("#systemProjectBonusManagerDetailDiv #delayBonusbasicsShow").val($.formMoney(rowData.delayBonus));
			                        	$("#systemProjectBonusManagerDetailDiv #thisBonus").html($.formMoney((rowData.projectBonus * (factSchedule-projectSchedule) / 100 + balanceQualityBonus + rowData.delayBonus*1).toFixed(2)));
			                        	$('#systemProjectBonusManagerDetailDiv #projectBonusPic').html($.formMoney(rowData.projectBonus*1 + rowData.delayBonus*1));
			                        }else{
			                        	$("#systemProjectBonusManagerDetailDiv #thisBonus").html($.formMoney((rowData.projectBonus * (factSchedule-projectSchedule) * developQuality / 10000).toFixed(2)));
			                        }
                            	}else{
                            		layer.closeAll();
                            		layer.msg("系统异常！");
                            	}
                            }
                        });
                        
//                  	$('#bonusDetailPic').html("本次发放奖金额度："+bonus+"￥  本次结余质量奖金："+qualityBonus+"￥");
                        var projectExamineDetail = new $.trasenTable("grid-table-bonusManagerExamineDetailTable", {
                            url: common.url + '/ts-cp/moduleSchedule/find/list',
                            pager: '',
                            shrinkToFit: true,
                            sortname: "t4.project_task_info_id,t4.id,t4.create_date",
                            sortorder: "asc",
                            //表格字段
                            colModel: [
                                { label: 'rowId', name: 'Id', index: 'id', width: "auto", align: "center", editable: false, hidden: true, key: true },
                                { label: '项目ID', name: 'projectInfoId', index: 'project_info_id', width: 150, align: "center", editable: false, hidden: true },
                                { label: '项目名称', name: 'projectName', index: 'project_name', width: 120, align: "center", editable: false, hidden: true },
                                { label: '研发进度', name: 'developSchedule', index: 'develop_schedule', width: 60, align: "center", editable: false, hidden: true },
                                { label: '研发质量', name: 'developQuality', index: 'develop_quality', width: 60, align: "center", editable: false, hidden: true },
                                { label: '实际进展', name: 'factSchedule', index: 'fact_schedule', width: 60, align: "center", editable: false, hidden: true },
                                { label: '奖金预算ID', name: 'projectBonusInfoId', index: 'project_bonus_info_id', width: 150, align: "center", editable: false, hidden: true },
                                { label: '所属任务ID', name: 'projectTaskInfoId', index: 'project_task_info_id', width: 100, align: "center", editable: false, hidden: true },
                                { label: '任务名称', name: 'taskName', index: 'task_name', width: 130, align: "center", editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'taskName' + rowId + "\'";
                                    }
                                },
                                { label: '所属模块ID', name: 'projectTaskModuleInfoId', index: 'project_task_module_info_id', width: 150, align: "center", editable: false, hidden: true },
                                { label: '模块名称', name: 'moduleName', index: 'module_name', width: 130, align: "center", editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleName' + rowId + "\'";
                                    }
                                },
                                { label: '项目权重(%)', name: 'projectWeight', index: 'project_weight', width: 120, align: "center", editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'projectWeight' + rowId + "\'";
                                    }
                                },
                                { label: '模块完成进度(%)', name: 'moduleExecution', index: 'module_execution', width: 150, align: "center", editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleExecution' + rowId + "\'";
                                    }
                                },
                                { label: '人员code', name: 'personCode', index: 'person_code', width: 80, align: "center", editable: false, hidden: true },
                                { label: '相关人员', name: 'personName', index: 'person_name', width: 120, align: "center", editable: false },
                                { label: '个人比重(%)', name: 'personProportion', index: 'person_proportion', width: 120, align: "center", editable: false },
                                { label: '个人权重(%)', name: 'projectWeightProportion', index: 'project_weight_proportion', width: 120, align: "center", classes: 'redColor', editable: false },
                                { label: '实际分配金额（￥）', name: 'personBonus', index: 'personBonus', width: 140, align: "center", classes: 'redColor', editable: false,
                                    formatter: "number",
                                    formatoptions: {
                                        decimalSeparator: ".",
                                        thousandsSeparator: ",",
                                        decimalPlaces: 2,
                                        defaulValue: 0
                                    }
                                }
                            ],
                            postData: {
                                projectBonusInfoId: projectBonusInfoId
                            },
                            footerrow: true,
                            gridComplete: function () {
                                var self = this;
                                var rowNum = parseInt($(this).getGridParam('records'), 10);
                                if (rowNum > 0) {
                                    $("#systemProjectBonusManagerDetailDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").show();
                                    var projectWeightProportion = jQuery(this).getCol('projectWeightProportion', false, 'sum').toFixed(2);
                                    var personBonus = jQuery(this).getCol('personBonus', false, 'sum').toFixed(2);
                                    $("#systemProjectBonusManagerDetailDiv #" + self.id).jqGrid('footerData', 'set', {
                                        'projectWeightProportion': projectWeightProportion,
                                        'personBonus': personBonus
                                    });
                                    $("#systemProjectBonusManagerDetailDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                } else {
                                    $("#systemProjectBonusManagerDetailDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").hide();
                                }
                                //②在gridComplete调用合并方法
                                var gridName = "grid-table-bonusManagerExamineDetailTable";
                                Merger(gridName, ['taskName', 'moduleName', 'projectWeight', 'moduleExecution']);
                            },
                            userDataOnFooter: true
                        });
                        projectExamineDetail.refresh();


                        //预览
                        $("#systemProjectBonusManagerDetailDiv").off("mouseleave", "#detailPreview").on("mouseleave", "#detailPreview", function () {
                            $(this).children("span").next().fadeOut();
                            setTimeout(function () {
                                $(this).children("span").next().remove()
                            },300)

                        });
                        $("#systemProjectBonusManagerDetailDiv").off("mouseenter", "#detailPreview>span").on("mouseenter", "#detailPreview>span", function (e) {
                            e.stopPropagation();
                            var html = bonusManagerPreviewFormHtml.innerHTML;
                            $(this).parent().append(html).children("span").next().fadeIn();
                            var previewPersonBonusTable = new $.trasenTable("grid-table-bonusManagerPreviewTable", {
                                url: common.url + "/ts-cp/moduleSchedule/emplyeeBonus",
                                cellEdit: true,
                                shrinkToFit: true,
                                pager: '',
                                colNames: ['ID', '项目ID', '预算ID', '任务ID', '部门code', '部门名称', '责任人code', '责任人', '项目权重(%)', '项目质量', '分配奖金'],
                                colModel: [
                                    {
                                        name: 'id',
                                        index: 'id',
                                        width: "auto",
                                        classes: 'triggerClick',
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectInfoId',
                                        index: 'project_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectBonusInfoId',
                                        index: 'project_bonus_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectTaskInfoId',
                                        index: 'project_task_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'deptCode',
                                        index: 'dept_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'deptName', index: 'dept_name', width: 100, editable: false, hidden: true},
                                    {
                                        name: 'personCode',
                                        index: 'person_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'personName', index: 'person_name', width: 100, editable: false},
                                    {
                                        name: 'projectWeightTotal',
                                        index: 'projectWeightTotal',
                                        width: 100,
                                        align: "right",
                                        classes: 'redColor',
                                        editable: false,
                                        edittype: 'text',
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    },
                                    {
                                        name: 'developQuality',
                                        index: 'develop_quality',
                                        width: 100,
                                        align: "left",
                                        editable: false
                                    },
                                    {
                                        name: 'projectBonusTotal',
                                        index: 'projectBonusTotal',
                                        width: 100,
                                        align: "left",
                                        editable: false,
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    }
                                ],
                                afterEditCell: function (rowid, cellname, value, iRow, iCol) {

                                },
                                postData: {
                                    projectBonusInfoId: projectBonusInfoId,
                                    select:1
                                },
                                buidQueryParams: function () {
                                },
                                afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                                },
                                footerrow: true,
                                gridComplete: function () {
                                    var self = this;
                                    var rowNum = parseInt($(this).getGridParam('records'), 10);
                                    if (rowNum > 0) {
                                        $("#systemProjectBonusManagerDetailDiv #gview_project-table .ui-jqgrid-sdiv").show();
                                        var projectBonusTotal = jQuery(this).getCol('projectBonusTotal', false, 'sum');
                                        var projectWeightTotal = jQuery(this).getCol('projectWeightTotal', false, 'sum');
                                        $("#systemProjectBonusManagerDetailDiv #" + self.id).jqGrid('footerData', 'set', {
                                            'projectBonusTotal': projectBonusTotal,
                                            'projectWeightTotal': projectWeightTotal
                                        });
                                        $("#systemProjectBonusManagerDetailDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                        // $('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').html($.formMoney(parseFloat($('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').text()).toFixed(2)))
                                    } else {
                                        $("#systemProjectBonusManagerDetailDiv #gview_project-table .ui-jqgrid-sdiv").hide();
                                    }
                                    $(this).parents("#systemProjectBonusManagerDetailDiv .staticTable").parent().parent().height($(this).parents("#systemProjectBonusManagerDetailDiv .staticTable").children().height() + 50)
                                    
                                },
                                userDataOnFooter: true
                            });

                            previewPersonBonusTable.refresh();

                        })
                    }
                });
            });
            
            
            
            //金额格式化
            function formatCurrency(num) {
                num = num.toString().replace(/\$|\,/g,'');
                if(isNaN(num))
                    num = "0";
                var sign = (num == (num = Math.abs(num)));
                num = Math.floor(num*100+0.50000000001);
                var cents = num%100;
                num = Math.floor(num/100).toString();
                if(cents<10)
                    cents = "0" + cents;
                for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++)
                    num = num.substring(0,num.length-(4*i+3))+','+
                        num.substring(num.length-(4*i+3));
                return (((sign)?'':'-') + num + '.' + cents);
            }
            
            /**
			 * 加法运算，避免数据相加小数点后产生多位数和计算精度损失。
			 * 
			 * @param num1加数1 | num2加数2
			 */
			function numAdd(num1, num2) {
			    var baseNum, baseNum1, baseNum2;
			    try {
			        baseNum1 = num1.toString().split(".")[1].length;
			    } catch (e) {
			        baseNum1 = 0;
			    }
			    try {
			        baseNum2 = num2.toString().split(".")[1].length;
			    } catch (e) {
			        baseNum2 = 0;
			    }
			    baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
			    return (num1 * baseNum + num2 * baseNum) / baseNum;
			};
            
            /*公共调用方法*/
            function Merger(gridName, CellNames) {
                //得到显示到界面的id集合
                var mya = $("#" + gridName + "").getDataIDs();
                //当前显示多少条
                var length = mya.length;
                for(var k = 0; k < CellNames.length; k++){
                	var CellName = CellNames[k];
                	for (var i = 0; i < mya.length; i++) {
	                    //从上到下获取一条信息
	                    var before = $("#" + gridName + "").jqGrid('getRowData', mya[i]);
	                    //定义合并行数
	                    var rowSpanTaxCount = 1;
	                    for (var j = i + 1; j <= mya.length; j++) {
	                        //和上边的信息对比 如果值一样就合并行数+1 然后设置rowspan 让当前单元格隐藏
	                        var end = $("#" + gridName + "").jqGrid('getRowData', mya[j]);
	                        if(k != 0 ){
	                        	if (before[CellName] == end[CellName] && before[CellNames[0]] == end[CellNames[0]]) {
		                            rowSpanTaxCount++;
		                            $("#" + gridName + "").setCell(mya[j], CellName, '', { display: 'none' });
		                        } else {
		                            rowSpanTaxCount = 1;
		                            break;
		                        }
	                        }else{
	                        	if (before[CellName] == end[CellName]) {
		                            rowSpanTaxCount++;
		                            $("#" + gridName + "").setCell(mya[j], CellName, '', { display: 'none' });
		                        } else {
		                            rowSpanTaxCount = 1;
		                            break;
		                        }
	                        }
	                        
	                        $("#" + CellName + mya[i] + "").attr("rowspan", rowSpanTaxCount);
	                    }
	                }
                }
                
            }
            
            /*难度系数*/
            function convertDifficultyRatio(val){
            	if(val == 1){
            		return 80;
            	} else if (val == 2) {
            		return 100
            	} else if (val == 3) {
            		return 120
            	} else if (val == 4) {
            		return 150
            	} else {
            		return 0;
            	}
            }
            
            /*评级系数*/
            function convertGradeRatio(val){
            	if(val == 1){
            		return 150;
            	} else if (val == 2) {
            		return 100
            	} else if (val == 3) {
            		return 90
            	} else if (val == 4) {
            		return 80
            	} else if (val == 5) {
            		return 70
            	} else {
            		return 0;
            	}
            }
			
			/*
			 * 计算项目奖金
			 * 
			 * val：下拉框值
			 */
			function computeProjectBonus(val){
				var bonusType = selectData.bonusType;
				var bonus = 0;
				var projectBudget=$("#systemProjectBonusManagerBonusSetDiv input[name='projectBudget']").val();
                projectBudget = projectBudget ? projectBudget.replace(/,/g,"") : 0;//项目预算
                var bonusCoefficient=$("#systemProjectBonusManagerBonusSetDiv input[name='bonusCoefficient']").val();
                bonusCoefficient = bonusCoefficient ? bonusCoefficient.replace(/,/g,"") : 0;//奖金系数
                var factPercent=$("#systemProjectBonusManagerBonusSetDiv input[name='factPercent']").val();
                factPercent = factPercent ? 100 - factPercent.replace(/,/g,"") : 100;//部门百分比
				if (bonusType == 1) {
            		/*自主研发类*/
                    if(!val){
                    	val = $("#systemProjectBonusManagerBonusSetDiv #difficultyType").val();
                    }
                	var difficultyRatio = convertDifficultyRatio(val); //难度系数
                	
                    //计算项目奖金
                    bonus = (projectBudget*difficultyRatio*bonusCoefficient*factPercent/10000).toFixed(2);
                } else if (bonusType == 2) {
                	/*维护类*/
                	if(!val){
                		val = $("#systemProjectBonusManagerBonusSetDiv #gradeType").val();
                	}
                	var gradeRatio = convertGradeRatio(val); //评级系数
                    //计算项目奖金
                    bonus = (projectBudget*gradeRatio*bonusCoefficient*factPercent/10000).toFixed(2);
                } else if (bonusType == 3) {
                	/*实施类*/
                	//if(!val){
                		//val = $("#difficultyType").val();
                	//}
                	//var difficultyRatio = convertDifficultyRatio(val); //难度系数
                	var travelAmount = $("#systemProjectBonusManagerBonusSetDiv #travelAmountbasics").val();
                	travelAmount = travelAmount ? travelAmount.replace(/,/g,"") : 0;//差旅费
                	
                	bonus = (projectBudget*bonusCoefficient*factPercent/100).toFixed(2);
                	if(bonus && bonus != 0 && travelAmount && travelAmount != 0 && bonus*1 < travelAmount*1){
                		layer.msg("差旅费和项目奖金分配不合理，请重新分配！");
                	}
                	bonus = bonus - travelAmount;
                }
                //计算项目奖金
                $("#systemProjectBonusManagerBonusSetDiv input[name='projectBonus']").val(bonus);
                $('#systemProjectBonusManagerBonusSetDiv #projectBonusbasicsShow').val($.formMoney(bonus));
			}
			
			
        })
    }
})
