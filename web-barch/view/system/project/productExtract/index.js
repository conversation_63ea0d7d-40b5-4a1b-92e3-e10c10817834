"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'laytpl', 'upload', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laytpl = layui.laytpl,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;

            //表格渲染
            var trasenTable = new $.trasenTable("grid-table-systemProjectProductExtract", {
                url: common.url + '/ts-base-data/productFeeCfg/list',
                pager: 'grid-pager-systemProjectProductExtract',
                colNames: ['ID','所属机构', '部门', '产品线', '产品名称', '提成基准金额'],//表格头
                //表格字段
                colModel: [
                    { name: 'roductFeeCfgId', index: 'roduct_fee_cfg_id', width: "auto", align: "center", editable: false, hidden: true },
                    { name: 'companyName', index: 'company_name', width: 150, align: "center", editable: false },
                    { name: 'deptName', index: 'dept_name', width: 150, align: "center", editable: false },
                    { name: 'productLineName', index: 'productLineName', width: 100, align: "center", editable: false,sortable:false},
                    { name: 'productInfoName', index: 'productInfoName', width: 150, align: "center", editable: false,sortable:false },
                    { name: 'baseAmount', index: 'base_amount', width: 150, align: "center", editable: false }

                ],
                queryFormId: 'systemProjectProductExtractqueryForm'
            });

            //表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            $(document).on('click', '#cancel', function () {
                layer.closeAll('page');
            });

             //查询
             form.on('submit(systemProjectProductExtractsearch)', function (data) {
                refreshTable()
            });

            // 新增操作
            $("#systemProjectProductExtract").off("click", "#addProductFeeCfg").on("click", "#addProductFeeCfg", function () {
                var html = systemProjectProductExtractAddHtml.innerHTML;
                layer.open({
                    type: 1,
                    title: '新增提成设置',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['480px', '380px'], //宽高
                    content: html,
                    scrollbar: true,

                    success: function (layero, index) {
                        //查询公司
                        $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #companyCode"), {
                            url: '/ts-hr/company/list',
                            valueName: 'organizationId',
                            textName: 'name'
                        })
                        // $.publicSelect("/ts-hr/company/list","organizationId","name","","companyCode",form);
                        $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #productLineCfgId"), {
                            url: '/ts-base-data/product/line/cfg/getCheckBoxList',
                            valueName: 'productLineCfgId',
                            textName: 'productLineName'
                        })
                        //查询产品线
                        // $.publicSelect("/ts-base-data/product/line/cfg/getCheckBoxList","productLineCfgId","productLineName","","productLineCfgId",form);
                    },
                });
            });

            //提交数据
            form.on('submit(systemProjectProductExtractAddHtmlDivformSaveBaseProductFeeCfg)', function (data) {
                var roductFeeCfgId = data.field['roductFeeCfgId'];
                if (roductFeeCfgId) {
                    trasen.ajax({
                        url: common.url + '/ts-base-data/productFeeCfg/update',
                        type: 'POST',
                        data: JSON.stringify(data.field),
                        success: function (data) {
                            layer.closeAll('page');
                            refreshTable();
                        }
                    })
                } else {
                    trasen.ajax({
                        url: common.url + '/ts-base-data/productFeeCfg/save',
                        type: 'post',
                        data: JSON.stringify(data.field),
                        success: function (data) {
                            layer.closeAll('page');
                            refreshTable();
                        }
                    })
                }
            });

            //编辑
            $("#systemProjectProductExtract").off("click", "#ProductFeeCfgEditor").on("click", "#ProductFeeCfgEditor", function () {
                var html = systemProjectProductExtractAddHtml.innerHTML;
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.open({
                    type: 1,
                    title: '编辑',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['480px', '380px'], //宽高
                    content: html,
                    success: function (layero, index) {
                        // $("#addBaseProductFeeCfgForm #systemProjectProductExtractAddHtml").show();
                        var id = rowData['roductFeeCfgId'];
                        $.ajax({
                            type: 'post',
                            url: common.url + "/ts-base-data/productFeeCfg/findById/" + id,
                            success: function (data) {
                                if (data && data.success) {
                                    var companyCode = data.object.companyCode;
                                    var deptCode = data.object.deptCode;
                                    var productLineCfgId = data.object.productLineCfgId;
                                    var productInfoId = data.object.productInfoId;
                                    //查询公司
                                    $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #companyCode"), {
                                        url: '/ts-hr/company/list',
                                        selectedValue: companyCode,
                                        valueName: 'organizationId',
                                        textName: 'name'
                                    })
                                    //查询部门
                                    $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #deptCode"), {
                                        url: '/ts-hr/organization/getOrganizationByParentId/' + companyCode,
                                        selectedValue: deptCode,
                                        valueName: 'organizationId',
                                        textName: 'name'
                                    })

                                    //查询产品线
                                    $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #productLineCfgId"), {
                                        url: '/ts-base-data/product/line/cfg/getCheckBoxList',
                                        selectedValue: productLineCfgId,
                                        valueName: 'productLineCfgId',
                                        textName: 'productLineName'
                                    })
                                    //查询产品库
                                    $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #productInfoId"), {
                                        url: '/ts-base-data/product/info/findByProductLineCfgId/' + productLineCfgId,
                                        selectedValue: productInfoId,
                                        valueName: 'productInfoId',
                                        textName: 'productName'
                                    })
                                    trasen.setNamesVal(layero, data.object);
                                }
                            }
                        })
                        form.render();
                    }
                });
            });

            //删除
            $("#systemProjectProductExtract").off("click", "#ProductFeeCfgDelete").on("click", "#ProductFeeCfgDelete", function () {
                var rowData = trasenTable.getSelectRowData();
                var id = rowData.roductFeeCfgId;
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.confirm('确定要删除吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    trasen.post(common.url + "/ts-base-data/productFeeCfg/delete/" + id, null, function (data) {
                        layer.closeAll('page');
                        layer.msg('操作成功.', { icon: 1 });
                        refreshTable();
                    })
                }, function () {
                    layer.closeAll('page');
                });
            })

            //监听选择公司级联部门
            form.on('select(systemProjectProductExtractAddHtmlDivcompanySelect)', function (data) {
                var companyName = $("#systemProjectProductExtractAddHtmlDiv #companyCode").find("option:selected").text();
                $("#systemProjectProductExtractAddHtmlDiv #companyName").val(companyName);
                var parentId = data.value;
                if (parentId != '') {
                    getSubDeptByCompanyId(parentId,'');
                }

            });

            //监听选择产品线级联产品
            form.on('select(systemProjectProductExtractAddHtmlDivproductLineCfgSelect)', function (data) {
                var productLineCfgId = data.value;
                //查询产品线的产品
                // $.publicSelect('/ts-base-data/product/info/findByProductLineCfgId/' + productLineCfgId,"productInfoId","productName","","productInfoId",form);

                $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #productInfoId"), {
                    url: '/ts-base-data/product/info/findByProductLineCfgId/' + productLineCfgId,
                    selectedValue: productInfoId,
                    valueName: 'productInfoId',
                    textName: 'productName'
                })
                
            });
            //监听选择公司级联部门
            form.on('select(systemProjectProductExtractAddHtmlDivdeptCodeSelect)', function (data) {
                var deptName = $("#systemProjectProductExtractAddHtmlDiv #deptCode").find("option:selected").text();
                $("#systemProjectProductExtractAddHtmlDiv #deptName").val(deptName);
            });

            //根据公司id查询部门
            function getSubDeptByCompanyId(parentId,subCode) {
                $.publicSelect(form, $("#systemProjectProductExtractAddHtmlDiv #deptCode"), {
                    url: '/ts-hr/organization/getOrganizationByParentId/' + parentId,
                    selectedValue: deptCode,
                    valueName: 'organizationId',
                    textName: 'name'
                })
            }
        })
    }
})
