"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'laytpl', 'upload', 'trasen', 'element'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laytpl = layui.laytpl,
                upload = layui.upload,
                laydate = layui.laydate,
                element = layui.element,
                trasen = layui.trasen;
            //表格渲染
            var trasenTable = new $.trasenTable("grid-table-bonusScheduleTable", {
                url: common.url + '/ts-cp/bnousInfo/list',
                pager: 'grid-pager-bonusSchedulePager',
                //表格字段
                colModel: [
                    { label: 'ID', name: 'id', index: 'id', width: "auto", align: "center", editable: false, hidden: true, key: true },
                    { label: '项目ID', name: 'projectBonusBaseId', index: 'project_bonus_base_id', width: 150, align: "center", editable: false, hidden: true },
                    {
                        label: '工作流ID',
                        name: 'workflowId',
                        index: 'workflow_id',
                        width: 150,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '任务ID',
                        name: 'taskId',
                        index: 'task_id',
                        width: 150,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '项目编号',
                        name: 'projectCode',
                        index: 'project_code',
                        width: 100,
                        align: "center",
                        editable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            var htmlBtn = '<a href="javascript:;" class="lookInfoA" id="showBtn" data-rowId="' + options.rowId + '" data-id=' + rowObject.workflowId + '>' + cellvalue + '</a>';
                            return htmlBtn;
                        }
                    },
                    {
                        label: '项目名称',
                        name: 'projectName',
                        index: 'project_name',
                        width: 150,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '项目预算',
                        name: 'projectBudget',
                        index: 'project_budget',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '奖金核算类型',
                        name: 'bonusType',
                        index: 'bonusType',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '是否核算项目奖金',
                        name: 'isAccountingBonus',
                        index: 'isAccountingBonus',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '是否结项',
                        name: 'isHighLines',
                        index: 'is_high_lines',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '难度系数',
                        name: 'difficultyType',
                        index: 'difficulty_type',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '评级系数',
                        name: 'gradeType',
                        index: 'grade_type',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '进度系数',
                        name: 'scheduleType',
                        index: 'schedule_type',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '差旅费',
                        name: 'travelAmount',
                        index: 'travel_amount',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '进度奖金',
                        name: 'delayBonus',
                        index: 'delay_bonus',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '部门占比',
                        name: 'factPercent',
                        index: 'fact_percent',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '项目预算',
                        name: 'projectBudgetDetail',
                        index: 'project_budget',
                        width: 100,
                        align: "center",
                        editable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            return '<a href="javascript:;" style="color:#0090ff;" id="rojectBudgetTableEditor" data-rowid="' + options.rowId + '">' + formatCurrency(rowObject.projectBudget) + '</a>';
                        }
                    },
                    {
                        label: '奖金系数',
                        name: 'bonusCoefficient',
                        index: 'bonus_coefficient',
                        width: 80,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '项目奖金',
                        name: 'projectBonus',
                        index: 'project_bonus',
                        width: 80,
                        align: "center",
                        editable: false,
                        formatter: "number",
                        formatoptions: {
                            decimalSeparator: ".",
                            thousandsSeparator: ",",
                            decimalPlaces: 2,
                            defaulValue: 0
                        }
                    },
                    {
                        label: '研发进度(%)',
                        name: 'developSchedule',
                        index: 'develop_schedule',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '研发质量(%)',
                        name: 'developQuality',
                        index: 'develop_quality',
                        width: 100,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '奖金申请比(%)',
                        name: 'factSchedule',
                        index: 'fact_schedule',
                        width: 100,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '已结算奖金比(%)',
                        name: 'projectSchedule',
                        index: 'project_schedule',
                        width: 100,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '结余质量奖金(￥)',
                        name: 'balanceQualityBonus',
                        index: 'balance_quality_bonus',
                        width: 100,
                        align: "center",
                        editable: false,
                        formatter: "number",
                        formatoptions: {
                            decimalSeparator: ".",
                            thousandsSeparator: ",",
                            decimalPlaces: 2,
                            defaulValue: 0
                        }
                    },
                    {
                        label: '审核状态',
                        name: 'isExamine',
                        index: 'is_examine',
                        width: 100,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '状态',
                        name: 'examineName',
                        index: 'examineName',
                        width: 100,
                        align: "center",
                        editable: false,
                        formatter: function (cellvalue, options, rowObject) {
                            var text;
                            cellvalue = rowObject.isExamine;
                            if (cellvalue == 1) {
                                text = "未提交";
                            } else if (cellvalue == 2) {
                                text = "待审核";
                            } else if (cellvalue == 3) {
                                text = "驳回";
                            } else {
                                text = "同意";
                            }
                            return text;
                        }
                    },
                    {label: '备注', name: 'remark', index: 'remark', width: 160, align: "center", editable: false},
                ],
                postData: {
                    workflowStatus: 1
                },
                buidQueryParams: function () {
                    var search = $("#bonusScheduleQueryForm").serializeArray();
                    var opt = $("#screening").serializeArray();
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                }
            });

            //查看
            $("body").off("click", "#systemProjectScheduleManager #rojectBudgetTableEditor").on("click", "#systemProjectScheduleManager #rojectBudgetTableEditor", function () {
                var rowid = $(this).attr('data-rowId');
                var rowData = trasenTable.getRowData(rowid);
                var bonusType = rowData.bonusType;
                if (rowData.id == null || rowData.id == undefined || rowData.id == '') {
                    layer.msg('请选中需要操作数据');
                    return false;
                }
                var html = bonusScheduleExamineFormHtml.innerHTML;
                layer.open({
                    type: 1,
                    title: '详情',
                    closeBtn: 1,
                    shadeClose: false,
                    maxmin: true,
                    area: ['100%', '100%'], //宽高
                    skin: 'yourclass',
                    content: html,
                    success: function (layero, index) {
                        $("#bonusScheduleConfirmProjectBonusBtn").hide();
                        $("#bonusScheduleRejectProjectBonusBtn").hide();
                        $("#bonusScheduleExamineFormDiv #remark").prop("disabled", "disabled");
                        if(rowData.workflowId){
                        	getOptList(rowData.workflowId);
                        }
                        //判断项目类型，分配属性
                        if (bonusType == 1) {
                            /*自主研发类*/
                            $("#bonusScheduleExamineFormDiv .travelAmount").hide();
                            $("#bonusScheduleExamineFormDiv .gradeType").hide();
                        } else if (bonusType == 2) {
                            /*维护类*/
                            $("#bonusScheduleExamineFormDiv .travelAmount").hide();
                            $("#bonusScheduleExamineFormDiv .difficultyType").hide();
                            $("#bonusScheduleExamineFormDiv .isHighLines").hide();
                        } else if (bonusType == 3) {
                            /*实施类*/
                            $("#bonusScheduleExamineFormDiv .difficultyType").hide();
                            $("#bonusScheduleExamineFormDiv .gradeType").hide();
                        }

                        trasen.setNamesVal(layero, rowData);
                        $("#bonusScheduleExamineFormDiv #remark").val("");
                        form.render('select');
                        var projectBonus = rowData.projectBonus * 1;
                        var factSchedule = rowData.factSchedule ? rowData.factSchedule * 1 : 0;
                        var projectSchedule = rowData.projectSchedule ? rowData.projectSchedule * 1 : 0;
                        var developQuality = rowData.developQuality ? rowData.developQuality * 1 : 0;
                        var balanceQualityBonus = rowData.balanceQualityBonus ? rowData.balanceQualityBonus * 1 : 0;

                        $("#bonusScheduleExamineFormDiv #thisSchedule").val(factSchedule - projectSchedule);
                        $("#bonusScheduleExamineFormDiv #travelAmountbasicsShow").val($.formMoney(rowData.travelAmount));
                        $('#bonusScheduleExamineFormDiv #projectBudgetbasicsShow').val($.formMoney(rowData.projectBudget));
                        $('#bonusScheduleExamineFormDiv #projectBonusbasicsShow').val($.formMoney(rowData.projectBonus));

                        if (factSchedule != 0) {
                            var thisBalanceQualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(thisBalanceQualityBonus));
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasics').val(thisBalanceQualityBonus);
                        } else {
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(0));
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasics').val(0);
                        }
                        if(factSchedule == 100){
                        	$("#bonusScheduleExamineFormDiv #scheduleTypeDiv").show();
                        	$("#bonusScheduleExamineFormDiv #delayBonusDiv").show();
                        	$("#bonusScheduleExamineFormDiv #delayBonusbasicsShow").val($.formMoney(rowData.delayBonus));
		                    $("#bonusScheduleExamineFormDiv #scheduleType").prop("disabled", "disabled");
                        }
                        
                        if (balanceQualityBonus != 0) {
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasicsShow').val($.formMoney(balanceQualityBonus));
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasics').val(balanceQualityBonus);
                        } else {
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasicsShow').val($.formMoney(0));
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasics').val(0);
                        }

                        var projectBonus = rowData.projectBonus * 1;
                        var factSchedule = rowData.factSchedule ? rowData.factSchedule * 1 : 0;
                        var developQuality = rowData.developQuality ? rowData.developQuality * 1 : 0;
                        var projectSchedule = rowData.projectSchedule ? rowData.projectSchedule * 1 : 0;
                        var bonus = '';
                        var qualityBonus = '';
                        if (factSchedule && developQuality) {
                            bonus = (projectBonus * (factSchedule - projectSchedule) * developQuality / 10000).toFixed(2);
                            qualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
                        }

                        $('#bonusScheduleExamineFormDiv #projectBonusPic').html($.formMoney(rowData.projectBonus));
                        if(factSchedule == 100){
                            $("#bonusScheduleExamineFormDiv #thisBonus").html($.formMoney((rowData.projectBonus * (factSchedule-projectSchedule) / 100 + balanceQualityBonus + rowData.delayBonus*1).toFixed(2)));
                            $('#bonusScheduleExamineFormDiv #projectBonusPic').html($.formMoney(rowData.projectBonus*1 + rowData.delayBonus*1));
                        }else{
                            $("#bonusScheduleExamineFormDiv #thisBonus").html($.formMoney((rowData.projectBonus * (factSchedule-projectSchedule) * developQuality / 10000).toFixed(2)));
                        }
//                  	$('#bonusDetailPic').html("本次发放奖金额度："+bonus+"￥  本次结余质量奖金："+qualityBonus+"￥");
                        var projectExamineDetail = new $.trasenTable("grid-table-bonusScheduleExamineDetailTable", {
                            url: common.url + '/ts-cp/moduleSchedule/find/list',
                            pager: '',
                            shrinkToFit: true,
                            sortname: "t4.project_task_info_id,t4.id,t4.create_date",
                            sortorder: "asc",
                            //表格字段
                            colModel: [
                                {
                                    label: 'rowId',
                                    name: 'Id',
                                    index: 'id',
                                    width: "auto",
                                    align: "center",
                                    editable: false,
                                    hidden: true,
                                    key: true
                                },
                                {
                                    label: '项目ID',
                                    name: 'projectInfoId',
                                    index: 'project_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '项目名称',
                                    name: 'projectName',
                                    index: 'project_name',
                                    width: 120,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '研发进度',
                                    name: 'developSchedule',
                                    index: 'develop_schedule',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '研发质量',
                                    name: 'developQuality',
                                    index: 'develop_quality',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '奖金申请比',
                                    name: 'factSchedule',
                                    index: 'fact_schedule',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '奖金预算ID',
                                    name: 'projectBonusInfoId',
                                    index: 'project_bonus_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '所属任务ID',
                                    name: 'projectTaskInfoId',
                                    index: 'project_task_info_id',
                                    width: 100,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '任务名称',
                                    name: 'taskName',
                                    index: 'task_name',
                                    width: 130,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'taskName' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '所属模块ID',
                                    name: 'projectTaskModuleInfoId',
                                    index: 'project_task_module_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '模块名称',
                                    name: 'moduleName',
                                    index: 'module_name',
                                    width: 130,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleName' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '项目权重(%)',
                                    name: 'projectWeight',
                                    index: 'project_weight',
                                    width: 120,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'projectWeight' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '模块完成进度(%)',
                                    name: 'moduleExecution',
                                    index: 'module_execution',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleExecution' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '人员code',
                                    name: 'personCode',
                                    index: 'person_code',
                                    width: 80,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '相关人员',
                                    name: 'personName',
                                    index: 'person_name',
                                    width: 120,
                                    align: "center",
                                    editable: false
                                },
                                {
                                    label: '个人比重(%)',
                                    name: 'personProportion',
                                    index: 'person_proportion',
                                    width: 120,
                                    align: "center",
                                    editable: false
                                },
                                {
                                    label: '个人权重(%)',
                                    name: 'projectWeightProportion',
                                    index: 'project_weight_proportion',
                                    width: 120,
                                    align: "center",
                                    classes: 'redColor',
                                    editable: false
                                },
                                {
                                    label: '实际分配金额（￥）',
                                    name: 'personBonus',
                                    index: 'personBonus',
                                    width: 140,
                                    align: "center",
                                    classes: 'redColor',
                                    editable: false,
                                    formatter: "number",
                                    formatoptions: {
                                        decimalSeparator: ".",
                                        thousandsSeparator: ",",
                                        decimalPlaces: 2,
                                        defaulValue: 0
                                    }
                                }
                            ],
                            postData: {
                                projectBonusInfoId: rowData.id
                            },
                            footerrow: true,
                            gridComplete: function () {
                                var self = this;
                                var rowNum = parseInt($(this).getGridParam('records'), 10);
                                if (rowNum > 0) {
                                    $("#bonusScheduleExamineFormDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").show();
                                    var projectWeightProportion = jQuery(this).getCol('projectWeightProportion', false, 'sum').toFixed(2);
                                    var personBonus = jQuery(this).getCol('personBonus', false, 'sum').toFixed(2);
                                    $("#bonusScheduleExamineFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                        'projectWeightProportion': projectWeightProportion,
                                        'personBonus': personBonus
                                    });
                                    $("#bonusScheduleExamineFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                } else {
                                    $("#bonusScheduleExamineFormDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").hide();
                                }
                                //②在gridComplete调用合并方法
                                var gridName = "grid-table-bonusScheduleExamineDetailTable";
                                Merger(gridName, ['taskName', 'moduleName', 'projectWeight', 'moduleExecution']);
                            },
                            userDataOnFooter: true
                        });
                        projectExamineDetail.refresh();


                        //预览
                        $("#bonusScheduleExamineFormDiv").off("mouseleave", "#detailPreview").on("mouseleave", "#detailPreview", function () {
                            $(this).children("span").next().fadeOut();
                            setTimeout(function () {
                                $(this).children("span").next().remove()
                            },300)
                        });

                        $("#bonusScheduleExamineFormDiv").off("mouseenter", "#detailPreview>span").on("mouseenter", "#detailPreview>span", function (e) {
                            e.stopPropagation();
                            var projectBonusInfoId = $("#bonusScheduleExamineFormDiv input[name='id']").val();
                            var html = bonusSchedulePreviewFormHtml.innerHTML;
                            $(this).parent().append(html).children("span").next().fadeIn();
                            var previewPersonBonusTable = new $.trasenTable("grid-table-bonusSchedulePreviewTable", {
                                url: common.url + "/ts-cp/moduleSchedule/emplyeeBonus",
                                cellEdit: true,
                                shrinkToFit: true,
                                pager: '',
                                colNames: ['ID', '项目ID', '预算ID', '任务ID', '部门code', '部门名称', '责任人code', '责任人', '项目权重(%)', '分配奖金'],
                                colModel: [
                                    {
                                        name: 'id',
                                        index: 'id',
                                        width: "auto",
                                        classes: 'triggerClick',
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectInfoId',
                                        index: 'project_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectBonusInfoId',
                                        index: 'project_bonus_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectTaskInfoId',
                                        index: 'project_task_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'deptCode',
                                        index: 'dept_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'deptName', index: 'dept_name', width: 100, editable: false, hidden: true},
                                    {
                                        name: 'personCode',
                                        index: 'person_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'personName', index: 'person_name', width: 100, editable: false},
                                    {
                                        name: 'projectWeightTotal',
                                        index: 'projectWeightTotal',
                                        width: 100,
                                        align: "right",
                                        classes: 'redColor',
                                        editable: false,
                                        edittype: 'text',
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    },
                                    {
                                        name: 'projectBonusTotal',
                                        index: 'projectBonusTotal',
                                        width: 100,
                                        align: "left",
                                        editable: false,
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    }
                                ],
                                afterEditCell: function (rowid, cellname, value, iRow, iCol) {

                                },
                                postData: {
                                    projectBonusInfoId: projectBonusInfoId,
                                    select: 1
                                },
                                buidQueryParams: function () {
                                },
                                afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                                },
                                footerrow: true,
                                gridComplete: function () {
                                    var self = this;
                                    var rowNum = parseInt($(this).getGridParam('records'), 10);
                                    if (rowNum > 0) {
                                        $("#bonusScheduleExamineFormDiv #gview_grid-table-bonusScheduleTaskTable .ui-jqgrid-sdiv").show();
                                        var projectBonusTotal = jQuery(this).getCol('projectBonusTotal', false, 'sum');
                                        var projectWeightTotal = jQuery(this).getCol('projectWeightTotal', false, 'sum');
                                        $("#bonusScheduleExamineFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                            'projectBonusTotal': projectBonusTotal,
                                            'projectWeightTotal': projectWeightTotal
                                        });
                                        $("#bonusScheduleExamineFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                        // $('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').html($.formMoney(parseFloat($('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').text()).toFixed(2)))
                                    } else {
                                        $("#bonusScheduleExamineFormDiv #gview_grid-table-bonusScheduleTaskTable .ui-jqgrid-sdiv").hide();
                                    }
                                    $(this).parents("#bonusScheduleExamineFormDiv .staticTable").parent().parent().height($(this).parents("#bonusScheduleExamineFormDiv .staticTable").height() + 50)
                                },
                                userDataOnFooter: true
                            });

                            previewPersonBonusTable.refresh();

                        });

                    }
                });
            });

            // 查看工作流
            $("body").off("click", "#systemProjectScheduleManager #showBtn").on("click", "#systemProjectScheduleManager #showBtn", function () {
                var id = $(this).attr("data-id");
                trasen.workflowTable(id);
            });

            //表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            $(document).on('click', '#bonusScheduleCancel', function () {
                layer.closeAll('page');
            });

            //查询
            form.on('submit(bonusScheduleSearch)', function (data) {
                refreshTable()
            });

            $("#bonusScheduleWorkflowStatus button").on("click", function () {
                $(this).addClass("active").siblings().removeClass("active");
                var state = $(this).attr('data-value');
                if (state == 1) {
                    $("#bonusScheduleAdd").prop("disabled", "");
                    $("#bonusScheduleSubmit").prop("disabled", "");
                    $("#bonusScheduleExamine").prop("disabled", "");
                } else if (state == 2) {
                    $("#bonusScheduleAdd").prop("disabled", "disabled");
                    $("#bonusScheduleSubmit").prop("disabled", "disabled");
                    $("#bonusScheduleExamine").prop("disabled", "disabled");
                } else if (state == 3) {
                    $("#bonusScheduleAdd").prop("disabled", "disabled");
                    $("#bonusScheduleSubmit").prop("disabled", "disabled");
                    $("#bonusScheduleExamine").prop("disabled", "disabled");
                } else if (state == 4) {
                    $("#bonusScheduleAdd").prop("disabled", "");
                    $("#bonusScheduleSubmit").prop("disabled", "disabled");
                    $("#bonusScheduleExamine").prop("disabled", "disabled");
                }
                $("#bonusScheduleQueryForm input[name='workflowStatus']").val(state);
                refreshTable();
            })
            //防止重复提交
            var flag = true;

            var tabIndex = 0;
            var selectData = [];
            var projectTaskTable, projectModulTable;
            var projectModuleScheduleIdList = [];

            $("body").off("input", "#bonusScheduleAddFormDiv #factSchedule").on("input", "#bonusScheduleAddFormDiv #factSchedule", function () {
                var isHighLines = $("#bonusScheduleAddFormDiv #isHighLines").val();
                var factSchedule = $("#bonusScheduleAddFormDiv #factSchedule").val();
                var developQuality = $('#bonusScheduleAddFormDiv #developQuality').val();
                if (isHighLines == 2 && factSchedule > 50) {
                    $("#bonusScheduleAddFormDiv #factSchedule").val(factSchedule.substring(0, factSchedule.length - 1));
                    layer.msg('该项目还未结项，奖金发放比例不得超过50%');
                    return false;
                }
                if (factSchedule == 100) {
                    $("#bonusScheduleAddFormDiv #scheduleTypeDiv").show();
                    $("#bonusScheduleAddFormDiv #delayBonusDiv").show();
                    $("#bonusScheduleAddFormDiv #scheduleType").prop("disabled", "");
                    $("#bonusScheduleAddFormDiv #scheduleType").attr("lay-verify", "required");
                    $("#bonusScheduleAddFormDiv #scheduleType").parent().prev().find("span").show();
                } else {
                    $("#bonusScheduleAddFormDiv #scheduleTypeDiv").hide();
                    $("#bonusScheduleAddFormDiv #delayBonusDiv").hide();
                    $("#bonusScheduleAddFormDiv #scheduleType").val("");
                    $("#bonusScheduleAddFormDiv #scheduleType").removeClass();
                    $("#bonusScheduleAddFormDiv #scheduleType").prop("disabled", "disabled");
                    $("#bonusScheduleAddFormDiv #scheduleType").removeAttr("lay-verify");
                    $("#bonusScheduleAddFormDiv #scheduleType").parent().prev().find("span").hide();
                }
                form.render("select");
            });

            // 编辑操作
            $("body").off("click", "#bonusScheduleAdd").on("click", "#bonusScheduleAdd", function () {
                var html = bonusScheduleAddFormHtml.innerHTML;
                var rowdata = trasenTable.getSelectRowData();
                var bounsType = rowdata.bonusType;
                if (rowdata.length || rowdata.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                if (rowdata.isExamine == 2) {
                    layer.msg('该条记录正在审批中，请选择其他记录编辑！')
                    return false;
                }
                if (rowdata.isExamine == 4) {
                    layer.msg('该条记录已审核同意，请选择其他记录编辑！')
                    return false;
                }
                layer.open({
                    type: 1,
                    title: '进度管理',
                    closeBtn: 1,
                    shadeClose: false,
                    maxmin: true,
                    area: ['100%', '100%'],
                    skin: 'yourclass',
                    content: html,
                    scrollbar: true,
                    success: function (layero, index) {
                        selectData = rowdata;

                        //监听进度系数，计算延迟扣除奖金
                        form.on('select(scheduleType)', function (data) {
                            computeProjectBonus(data.value);
                        });

                        trasen.setNamesVal(layero, rowdata);
                        //计算本期结余奖金
                        var projectBonus = rowdata.projectBonus * 1;
                        var factSchedule = rowdata.factSchedule ? rowdata.factSchedule * 1 : 0;
                        var projectSchedule = rowdata.projectSchedule ? rowdata.projectSchedule * 1 : 0;
                        var developQuality = rowdata.developQuality ? rowdata.developQuality * 1 : 0;
                        var balanceQualityBonus = rowdata.balanceQualityBonus ? rowdata.balanceQualityBonus * 1 : 0;
                        if (developQuality == 0) {
                            $('#bonusScheduleAddFormDiv input[name="developQuality"]').val(100)
                        }
                        //计算本期质量奖金
                        if (factSchedule != 0) {
                            var thisBalanceQualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
                            $('#bonusScheduleAddFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(thisBalanceQualityBonus));
                            $('#bonusScheduleAddFormDiv #thisBalanceQualityBonusbasics').val(thisBalanceQualityBonus);
                        } else {
                            $('#bonusScheduleAddFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(0));
                            $('#bonusScheduleAddFormDiv #thisBalanceQualityBonusbasics').val(0);
                        }
                        if(factSchedule == 100){
                        	$("#bonusScheduleAddFormDiv #scheduleTypeDiv").show();
                        	$("#bonusScheduleAddFormDiv #delayBonusDiv").show();
                        	$("#bonusScheduleAddFormDiv #delayBonusbasicsShow").val($.formMoney(rowdata.delayBonus));
		                    $("#bonusScheduleAddFormDiv #scheduleType").prop("disabled", "");
		                    $("#bonusScheduleAddFormDiv #scheduleType").attr("lay-verify", "required");
		                    $("#bonusScheduleAddFormDiv #scheduleType").parent().prev().find("span").show();
                        }
                        
                        //计算历史质量结余奖金
                        if (balanceQualityBonus != 0) {
                            $('#bonusScheduleAddFormDiv #balanceQualityBonusbasicsShow').val($.formMoney(balanceQualityBonus));
                            $('#bonusScheduleAddFormDiv #balanceQualityBonusbasics').val(balanceQualityBonus);
                        } else {
                            $('#bonusScheduleAddFormDiv #balanceQualityBonusbasicsShow').val($.formMoney(0));
                            $('#bonusScheduleAddFormDiv #balanceQualityBonusbasics').val(0);
                        }

                        if (rowdata.projectBonus) {
                            $('#bonusScheduleAddFormDiv #projectBonusbasicsShow').val($.formMoney(rowdata.projectBonus));
                        }

                        $('#bonusScheduleAddFormDiv #projectBonusPic').html($.formMoney(rowdata.projectBonus));
                        if(factSchedule == 100){
                            $("#bonusScheduleAddFormDiv #thisBonus").html($.formMoney((rowdata.projectBonus * (factSchedule-projectSchedule) / 100 + balanceQualityBonus + rowdata.delayBonus*1).toFixed(2)));
                            $('#bonusScheduleAddFormDiv #projectBonusPic').html($.formMoney(rowdata.projectBonus*1 + rowdata.delayBonus*1));
                        }else{
                            $("#bonusScheduleAddFormDiv #thisBonus").html($.formMoney((rowdata.projectBonus * (factSchedule-projectSchedule) * developQuality / 10000).toFixed(2)));
                        }
                        //加载任务模块人员信息
                        projectTaskTable = new $.trasenTable("grid-table-bonusScheduleTaskTable", {
                            url: common.url + '/ts-cp/moduleSchedule/find/list',
                            shrinkToFit: true,
                            pager: '',
                            sortname: "t4.project_task_info_id,t4.id,t4.create_date",
                            sortorder: "asc",
                            //表格字段
                            colModel: [
                                {
                                    label: 'rowId',
                                    name: 'Id',
                                    index: 'id',
                                    width: "auto",
                                    align: "center",
                                    editable: false,
                                    hidden: true,
                                    key: true
                                },
                                {
                                    label: '项目ID',
                                    name: 'projectInfoId',
                                    index: 'project_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '项目名称',
                                    name: 'projectName',
                                    index: 'project_name',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '研发进度',
                                    name: 'developSchedule',
                                    index: 'develop_schedule',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '研发质量',
                                    name: 'developQuality',
                                    index: 'develop_quality',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '奖金申请比',
                                    name: 'factSchedule',
                                    index: 'fact_schedule',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '奖金预算ID',
                                    name: 'projectBonusInfoId',
                                    index: 'project_bonus_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '所属任务ID',
                                    name: 'projectTaskInfoId',
                                    index: 'project_task_info_id',
                                    width: 100,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '任务名称',
                                    name: 'taskName',
                                    index: 'task_name',
                                    width: 180,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'taskName' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '所属模块ID',
                                    name: 'projectTaskModuleInfoId',
                                    index: 'project_task_module_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '模块名称',
                                    name: 'moduleName',
                                    index: 'module_name',
                                    width: 180,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleName' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '项目权重(%)',
                                    name: 'projectWeight',
                                    index: 'project_weight',
                                    width: 120,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'projectWeight' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '模块完成进度(%)',
                                    name: 'moduleExecution',
                                    index: 'module_execution',
                                    width: 180,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleExecution' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '人员code',
                                    name: 'personCode',
                                    index: 'person_code',
                                    width: 80,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '相关人员',
                                    name: 'personName',
                                    index: 'person_name',
                                    width: 150,
                                    align: "center",
                                    editable: false
                                },
                                {
                                    label: '个人占比(%)',
                                    name: 'personProportion',
                                    index: 'person_proportion',
                                    width: 120,
                                    align: "center",
                                    editable: false
                                },
                                {
                                    label: '个人权重(%)',
                                    name: 'projectWeightProportion',
                                    index: 'project_weight_proportion',
                                    width: 120,
                                    align: "center",
                                    classes: 'redColor',
                                    editable: false
                                },
                                {
                                    label: '分配金额(￥)',
                                    name: 'personBonus',
                                    index: 'personBonus',
                                    width: 120,
                                    align: "center",
                                    classes: 'redColor',
                                    editable: false,
                                    formatter: "number",
                                    formatoptions: {
                                        decimalSeparator: ".",
                                        thousandsSeparator: ",",
                                        decimalPlaces: 2,
                                        defaulValue: 0
                                    }
                                },
                                {
                                    label: '项目总进度',
                                    name: 'projectExecution',
                                    index: 'projectExecution',
                                    width: 80,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                }
                            ],
                            afterEditCell: function (rowid, cellname, value, iRow, iCol) {

                            },
                            postData: {
                                projectBonusInfoId: $("#id").val()
                            },
                            buidQueryParams: function () {
                                var data = {};
                                data["projectBonusInfoId"] = $("#id").val();
                                return data;
                            },
                            footerrow: true,
                            gridComplete: function () {
                                var self = this;
                                var rowNum = parseInt($(this).getGridParam('records'), 10);
                                if (rowNum > 0) {
                                	var projectExecution = $(this).jqGrid("getRowData",1).projectExecution;
                                    $("#bonusScheduleAddFormDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").show();
                                    var projectWeightProportion = jQuery(this).getCol('projectWeightProportion', false, 'sum').toFixed(2);
                                    var personBonus = jQuery(this).getCol('personBonus', false, 'sum').toFixed(2);
                                    $("#bonusScheduleAddFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                        'projectWeightProportion': projectWeightProportion,
                                        'personBonus': personBonus,
                                        'moduleExecution' : projectExecution
                                    });
                                    $("#bonusScheduleAddFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                    $("#bonusScheduleAddFormDiv input[name='developSchedule']").val(projectExecution);
                                } else {
                                    $("#bonusScheduleAddFormDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").hide();
                                }
                                //②在gridComplete调用合并方法
                                var gridName = "grid-table-bonusScheduleTaskTable";
                                Merger(gridName, ['taskName', 'moduleName', 'projectWeight', 'moduleExecution']);
                            },
                            userDataOnFooter: true
                        });

                        projectTaskTable.refresh();

                        form.render("radio");
                        form.render("select");

                        $("#bonusScheduleAddFormDiv").off("mouseleave", "#detailPreview").on("mouseleave", "#detailPreview", function () {
                            $(this).children("span").next().fadeOut();
                            setTimeout(function () {
                                $(this).children("span").next().remove()
                            },300)
                        });
                        $("#bonusScheduleAddFormDiv").off("mouseenter", "#detailPreview>span").on("mouseenter", "#detailPreview>span", function (e) {
                            e.stopPropagation();
                            var projectBonusInfoId = $("input[name='id']").val();
                            var html = bonusSchedulePreviewFormHtml.innerHTML;
                            $(this).parent().append(html).children("span").next().fadeIn();
                            var previewPersonBonusTable = new $.trasenTable("grid-table-bonusSchedulePreviewTable", {
                                url: common.url + "/ts-cp/moduleSchedule/emplyeeBonus",
                                cellEdit: true,
                                shrinkToFit: true,
                                pager: '',
                                colNames: ['ID', '项目ID', '预算ID', '任务ID', '部门code', '部门名称', '责任人code', '责任人', '项目权重(%)', '分配奖金'],
                                colModel: [
                                    {
                                        name: 'id',
                                        index: 'id',
                                        width: "auto",
                                        classes: 'triggerClick',
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectInfoId',
                                        index: 'project_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectBonusInfoId',
                                        index: 'project_bonus_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectTaskInfoId',
                                        index: 'project_task_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'deptCode',
                                        index: 'dept_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'deptName', index: 'dept_name', width: 100, editable: false, hidden: true},
                                    {
                                        name: 'personCode',
                                        index: 'person_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'personName', index: 'person_name', width: 100, editable: false},
                                    {
                                        name: 'projectWeightTotal',
                                        index: 'projectWeightTotal',
                                        width: 100,
                                        align: "right",
                                        classes: 'redColor',
                                        editable: false,
                                        edittype: 'text',
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    },
                                    {
                                        name: 'projectBonusTotal',
                                        index: 'projectBonusTotal',
                                        width: 100,
                                        align: "left",
                                        editable: false,
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    }
                                ],
                                afterEditCell: function (rowid, cellname, value, iRow, iCol) {

                                },
                                postData: {
                                    projectBonusInfoId: projectBonusInfoId,
                                    select: 1
                                },
                                buidQueryParams: function () {
                                },
                                afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                                },
                                footerrow: true,
                                gridComplete: function () {
                                    var self = this;
                                    var rowNum = parseInt($(this).getGridParam('records'), 10);
                                    if (rowNum > 0) {
                                        $("#bonusScheduleAddFormDiv #gview_grid-table-bonusScheduleTaskTable .ui-jqgrid-sdiv").show();
                                        var projectBonusTotal = jQuery(this).getCol('projectBonusTotal', false, 'sum');
                                        var projectWeightTotal = jQuery(this).getCol('projectWeightTotal', false, 'sum');
                                        $("#bonusScheduleAddFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                            'projectBonusTotal': projectBonusTotal,
                                            'projectWeightTotal': projectWeightTotal
                                        });
                                        $("#bonusScheduleAddFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                        // $('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').html($.formMoney(parseFloat($('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').text()).toFixed(2)))
                                    } else {
                                        $("#bonusScheduleAddFormDiv #gview_grid-table-bonusScheduleTaskTable .ui-jqgrid-sdiv").hide();
                                    }
                                    $(this).parents("#bonusScheduleAddFormDiv .staticTable").parent().parent().height($(this).parents("#bonusScheduleAddFormDiv .staticTable").height() + 50)
                                },
                                userDataOnFooter: true
                            });

                            previewPersonBonusTable.refresh();

                        })
                    },
                });
            });


            //关闭layer
            $("body").on("click", "#bonusScheduleClose", function () {
                layer.closeAll();
            });

            //绑定失去焦点事件
            $('body').off('blur', '#bonusScheduleAddFormDiv #factSchedule,#bonusScheduleAddFormDiv #developQuality').on('blur', '#bonusScheduleAddFormDiv #factSchedule,#bonusScheduleAddFormDiv #developQuality', function (e) {
                var projectBonus = $('#bonusScheduleAddFormDiv input[name="projectBonus"]').val() * 1;
                var factSchedule = $('#bonusScheduleAddFormDiv #factSchedule').val() ? $('#bonusScheduleAddFormDiv #factSchedule').val() * 1 : 0;
                var projectSchedule = $('#bonusScheduleAddFormDiv #projectSchedule').val() ? $('#bonusScheduleAddFormDiv #projectSchedule').val() * 1 : 0;
                var developQuality = $('#bonusScheduleAddFormDiv #developQuality').val() ? $('#bonusScheduleAddFormDiv #developQuality').val() * 1 : 0;
                if(projectSchedule > factSchedule){
                    $("#bonusScheduleAddFormDiv #factSchedule").val("");
                    layer.msg('本期奖金申请比不得小于已结算进度');
                    return false;
                }
                if(factSchedule == 100 && developQuality != 100){
                	$("#bonusScheduleAddFormDiv #developQuality").val(100); 
                	layer.msg('最后一次申请项目奖,质量必须达到100%');
                }
                if (factSchedule != 0 && developQuality != 0) {
                    var thisBalanceQualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
                    $('#bonusScheduleAddFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(thisBalanceQualityBonus));
                    $('#bonusScheduleAddFormDiv #thisBalanceQualityBonusbasics').val(thisBalanceQualityBonus);
                }
                computeProjectBonus($("#bonusScheduleAddFormDiv #scheduleType").val());
            });

            //行内下拉选择
            $('body').off('click', '#bonusScheduleAddFormDiv .appraiseAppraises').on('click', '#bonusScheduleAddFormDiv .appraiseAppraises', function (e) {
                interviewPeoClose();
                e.stopPropagation();
                $('body').trigger('click');
                var txt = $(this).text();
                $(this).attr('id', 'interviewPeoBoxse');
                $(this).closest('td').removeClass('appraiseAppraises').addClass('interviewPeoTd');
                setTimeout(function () {
                    //人员选择
                    new $.selectPlug('#bonusScheduleAddFormDiv #interviewPeoBoxse', {
                        url: common.url + "/ts-hr/employee/selectList",
                        searchType: 'json',
                        textName: 'name',  // 选项文字的key
                        valName: 'code', // 选项id的key
                        inpValId: 'interviewPeoval',  // 页面表单需要的 val参数的id
                        inpTextId: 'interviewPeoname',  // 页面表单需要的 name参数的id
                        callback: function (res) {
                            if (res) {
                                var rowid = $('#bonusScheduleAddFormDiv .interviewPeoTd').closest('tr').attr('id');
                                $('#bonusScheduleAddFormDiv .interviewPeoTd').attr('id', '');
                                $('#bonusScheduleAddFormDiv .interviewPeoTd').addClass('appraiseAppraises').removeClass('interviewPeoTd');
                                projectModulTable.setCell(rowid, 'personCode', res.code);
                                projectModulTable.setCell(rowid, 'personName', res.name);
                                projectModulTable.setCell(rowid, 'deptCode', res.organizationCode);
                                projectModulTable.setCell(rowid, 'deptName', res.organizationName);
                                //projects.setCell(rowid,'score',res.score);
//                              var rows = projects.getAllData();
//                              var i=0;
//                              rows.forEach(function (value,index){
//                                  if(value.score){
//                                      i+=Number(value.score);
//                                  }
//                              })
                                //$("#scores").html(i.toFixed(2));
                            } else {
                                $('#bonusScheduleAddFormDiv #interviewPeoname').val(txt);
                            }
                        }
                    });
                    $('#bonusScheduleAddFormDiv #interviewPeoname').trigger('click');
                })
            });

            //点击其他地方关闭面试部门选择
            function interviewPeoClose() {
                if ($('#bonusScheduleAddFormDiv #interviewPeoname').length > 0) {
                    var value = $('#bonusScheduleAddFormDiv #interviewPeoname').val();
                    var rowid = $('#bonusScheduleAddFormDiv .interviewPeoTd').closest('tr').attr('id');
                    $('#bonusScheduleAddFormDiv .interviewPeoTd').attr('id', '');
                    $('#bonusScheduleAddFormDiv .interviewPeoTd').addClass('appraiseAppraises').removeClass('interviewPeoTd');
                    projectModulTable.setCell(rowid, 'personName', value); //保存name
                }
            }

            $(document).off('click', '#bonusScheduleAddFormDiv .layui-layer.layui-layer-page,#bonusScheduleAddFormDiv .layui-layer-shade')
            .on('click', '#bonusScheduleAddFormDiv .layui-layer.layui-layer-page,#bonusScheduleAddFormDiv .layui-layer-shade', function () {
                interviewPeoClose();
            })

            var i = 1000;
            $('body').off('click', '#bonusScheduleAddFormDiv #addsoftwareReceivable').on('click', '#bonusScheduleAddFormDiv #addsoftwareReceivable', function () {
                $('#bonusScheduleAddFormDiv #grid-table-hardwareReceivable .triggerClick').trigger('click');
                var rowdata = projectTaskTable.getSelectRowData();
                var data = {
                    id: i,
                    projectInfoId: rowdata.projectInfoId,
                    projectBonusInfoId: rowdata.projectBonusInfoId,
                    projectTaskInfoId: rowdata.projectTaskInfoId,
                    taskName: rowdata.taskName,
                    projectTaskModuleInfoId: rowdata.projectTaskModuleInfoId,
                    moduleName: rowdata.moduleName
                };
                projectModulTable.addRowData(i, data, 'last');
                i--;
            });
            //提交
//          form.on('submit(addSub)', function (data) {
//              var index = $('.layui-tab-title li.layui-this').index();
//              saveFun(data, index, true);
//              layer.closeAll();
//          });
            //保存并继续
            form.on('submit(bonusScheduleAddSave)', function (data) {
                var index = $('#bonusScheduleAddFormDiv .layui-tab-title li.layui-this').index();
                saveFun(data, index);
            });

            function saveFun(data, index) {
                if (index == 0) {
                    $('#bonusScheduleBasicinfo').trigger('click');
                } else if (index == 1) {
                    $('#bonusScheduleModuleinfo').trigger('click');
                }
            }

            // 下一步保存操作
            form.on('submit(bonusScheduleBasicinfo)', function (data) {
                var factSchedule = data.field.factSchedule * 1;
                var projectSchedule = data.field.projectSchedule ? data.field.projectSchedule * 1 : 0;
                if (factSchedule == 0) {
                    layer.msg("奖金申请比不能为0,且大于已结算奖金比！");
                    return false;
                }
                if (factSchedule <= projectSchedule) {
                    layer.msg("奖金申请比必须大于已结算奖金比！");
                    return false;
                }
                if (flag) {
                    flag = false;
                    trasen.ajax({
                        url: common.url + '/ts-cp/bnousInfo/update',
                        type: 'post',
                        data: JSON.stringify(data.field),
                        success: function (data) {
                            layer.msg('保存成功！');
                            projectTaskTable.refresh();
                            setTimeout(function () {
                                flag = true;
                            }, 500);
                            refreshTable();
                        },
                        error: function (res) {
                            flag = true;
                            layer.msg(res.message);
                        }
                    });
                }

            });
            // 下一步保存操作
            form.on('submit(bonusScheduleModuleinfo)', function (data) {
                var basicinfoData = projectModulTable.getAllData();

                var totalProjectWeiht = 0;
                var totalPersonProportion = 0
                for (var i in basicinfoData) {
                    delete basicinfoData[i][""];
                    //添加需删除的人员ID
                    basicinfoData[i].projectModuleScheduleIdList = projectModuleScheduleIdList;
                    var projectWeightProportion = basicinfoData[i].projectWeightProportion * 1;
                    var personProportion = basicinfoData[i].personProportion * 1;
                    totalProjectWeiht = numAdd(totalProjectWeiht, projectWeightProportion);
                    totalPersonProportion = numAdd(totalPersonProportion, personProportion);
                }
                totalPersonProportion = totalPersonProportion.toFixed(2)*1;
                if (totalPersonProportion != 100) {
                    layer.msg('个人比重和必须等于100%');
                    return false;
                }
                var projectWeight = $("#bonusScheduleAddFormDiv #projectWeightPic").html().replace('%', '');
                totalProjectWeiht = totalProjectWeiht.toFixed(2)*1;
                if (totalProjectWeiht != projectWeight) {
                    layer.msg('人员权重总和必须等于模块权重');
                    return false;
                }
                //清空集合
                projectModuleScheduleIdList = [];
                data.field.moduleScheduleList = basicinfoData;
                data.field.id = data.field.projectTaskModuleInfoId;
                /*保存基本信息*/
               	data.field.factSchedule = $("#bonusScheduleAddFormDiv #factSchedule").val();
               	data.field.developQuality = $("#bonusScheduleAddFormDiv #developQuality").val();
               	data.field.scheduleType = $("#bonusScheduleAddFormDiv #scheduleType").val();
               	data.field.delayBonus = $("#bonusScheduleAddFormDiv #delayBonusbasics").val();
                if (flag) {
                    flag = false;
                    trasen.ajax({
                        url: common.url + '/ts-cp/moduleSchedule/saveOrUpdate',
                        type: 'post',
                        data: JSON.stringify(data.field),
                        success: function (data) {
                            layer.msg('保存成功！');
                            projectTaskTable.refresh();
                            projectModulTable.refresh();
                            setTimeout(function () {
                                flag = true;
                            }, 500);
                            refreshTable();
                        },
                        error: function (res) {
                            flag = true;
                            layer.msg(res.message);
                        }
                    })
                }

            });

            //提交数据
            // form.on('submit(save)', function (data) {
            //     var basicinfoData = projectModulTable.getAllData();
            //     data.field.moduleScheduleList = basicinfoData;
            //     data.field.id = data.field.projectBonusInfoId;
            //     trasen.ajax({
            //         url: common.url + '/ts-cp/moduleSchedule/saveOrUpdate',
            //         type: 'post',
            //         data: JSON.stringify(data.field),
            //         success: function (data) {
            //             layer.closeAll('page');
            //             refreshTable();
            //         }
            //     })
            //
            // });

            //tab切换
            element.on('tab(bonusScheduleNcomeLaytab)', function () {
                var txt = $(this).attr('title');
                var ind = $(this).index();
                tabIndex = ind;
                projectModulTable = null;
//              var rowdata = trasenTable.getSelectRowData();
//              $("input[name='developSchedule']").val(rowdata.developSchedule);
                if (ind == 1) {
                    //更新进度
                    var getRowData = projectTaskTable.getSelectRowData();
                    if (getRowData.length || getRowData.length == 0) {
                        layer.msg('请先选择一条任务模块');
                        tabIndex--
                        $('#bonusScheduleAddFormDiv .layui-tab-title li').eq(tabIndex).trigger('click');
                        return false
                    }
                    var projectInfoId = getRowData.projectInfoId;
                    var projectBonusInfoId = getRowData.projectBonusInfoId;
                    var projectTaskInfoId = getRowData.projectTaskInfoId;
                    var projectTaskModuleInfoId = getRowData.projectTaskModuleInfoId;
                    var moduleExecution = getRowData.moduleExecution;
                    $("#bonusScheduleAddFormDiv #projectWeightPic").html(getRowData.projectWeight + "%");
                    $("#bonusScheduleAddFormDiv input[name='projectInfoId']").val(projectInfoId);
                    $("#bonusScheduleAddFormDiv input[name='projectBonusInfoId']").val(projectBonusInfoId);
                    $("#bonusScheduleAddFormDiv input[name='projectTaskInfoId']").val(projectTaskInfoId);
                    $("#bonusScheduleAddFormDiv input[name='projectTaskModuleInfoId']").val(projectTaskModuleInfoId);
                    $("#bonusScheduleAddFormDiv input[name='moduleExecution']").val(moduleExecution);
                    projectModulTable = new $.trasenTable("grid-table-bonusScheduleModuleTable", {
                        url: common.url + "/ts-cp/moduleSchedule/findModulePerson/list",
                        cellEdit: true,
                        shrinkToFit: true,
                        pager: '',
                        sortname: "t4.project_info_id,t4.project_task_info_id",
                        sortorder: "asc",
                        colNames: ['ID', '项目ID', '预算ID', '任务ID', '模块ID', '任务名称', '模块名称', '部门code', '部门名称', '责任人code', '责任人', '个人占比(%)', '权重(%)', '操作'],
                        colModel: [
                            {
                                name: 'id',
                                index: 'id',
                                width: "auto",
                                classes: 'triggerClick',
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'projectInfoId',
                                index: 'project_info_id',
                                width: "auto",
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'projectBonusInfoId',
                                index: 'project_bonus_info_id',
                                width: "auto",
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'projectTaskInfoId',
                                index: 'project_task_info_id',
                                width: "auto",
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'projectTaskModuleInfoId',
                                index: 'project_task_module_info_id',
                                width: "auto",
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'taskName', index: 'task_name', width: 120, align: "left", editable: false,
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    //合并单元格
                                    return 'id=\'taskName' + rowId + "\'";
                                }
                            },
                            {
                                name: 'moduleName', index: 'nodule_name', width: 120, align: "left", editable: false,
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    //合并单元格
                                    return 'id=\'moduleName' + rowId + "\'";
                                }
                            },
                            {
                                name: 'deptCode',
                                index: 'dept_code',
                                width: "auto",
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'deptName',
                                index: 'dept_name',
                                width: 100,
                                classes: 'appraiseAppraises',
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'personCode',
                                index: 'person_code',
                                width: "auto",
                                align: "left",
                                editable: false,
                                hidden: true
                            },
                            {
                                name: 'personName',
                                index: 'person_name',
                                width: 100,
                                classes: 'appraiseAppraises',
                                editable: false
                            },
                            {
                                name: 'personProportion',
                                index: 'person_proportion',
                                width: 100,
                                align: "right",
                                classes: 'redColor',
                                editable: true,
                                edittype: 'text',
                                formatter: "number",
                                formatoptions: {
                                    decimalSeparator: ".",
                                    thousandsSeparator: ",",
                                    decimalPlaces: 2,
                                    defaulValue: 0
                                }
                            },
                            {
                                name: 'projectWeightProportion',
                                index: 'project_weight_proportion',
                                width: 100,
                                align: "right",
                                editable: true
                            },
                            {
                                name: '',
                                index: '',
                                width: 50,
                                align: "center",
                                editable: false,
                                formatter: function (cellvalue, options, rowObject) {
                                    return '<a href="javascript:;" class="projectTaskTablerowDel"><i class="fa fa-trash-o" aria-hidden="true"></i></a>';
                                }
                            }
                        ],
                        afterEditCell: function (rowid, cellname, value, iRow, iCol) {

                        },
                        postData: {
                            projectTaskModuleInfoId: $("#bonusScheduleAddFormDiv #projectTaskModuleInfoId").val()
                        },
                        buidQueryParams: function () {
                            var data = {};
                            data["projectTaskModuleInfoId"] = $("#bonusScheduleAddFormDiv #projectTaskModuleInfoId").val();
                            return data;
                        },
                        afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                            if (cellname == 'personProportion') {
//	                            //计算个人权重
                                var projectWeight = $("#bonusScheduleAddFormDiv #projectWeightPic").html().replace('%', '');
                                var projectWeightProportion = value * projectWeight / 100
                                projectModulTable.setCell(rowid, 'projectWeightProportion', projectWeightProportion.toFixed(2));
                            }
                            //计算合计
                            var self = this;
                            $("#bonusScheduleAddFormDiv #gview_grid-table-bonusScheduleModuleTable .ui-jqgrid-sdiv").show();
                            var personProportion = jQuery(this).getCol('personProportion', false, 'sum');
                            var projectWeightProportion = jQuery(this).getCol('projectWeightProportion', false, 'sum');
                            $("#bonusScheduleAddFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                'projectWeightProportion': projectWeightProportion.toFixed(2),
                                'personProportion': personProportion
                            });
                            $("#bonusScheduleAddFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                        },
                        footerrow: true,
                        gridComplete: function () {
                            $("#bonusScheduleAddFormDiv #gview_grid-table-bonusScheduleModuleTable .ui-jqgrid-sdiv").find("tbody").find("td").eq(11).removeClass();
                            var self = this;
                            var rowNum = parseInt($(this).getGridParam('records'), 10);
                            if (rowNum > 0) {
                                $("#bonusScheduleAddFormDiv #gview_grid-table-bonusScheduleModuleTable .ui-jqgrid-sdiv").show();
                                var personProportion = jQuery(this).getCol('personProportion', false, 'sum');
                                var projectWeightProportion = jQuery(this).getCol('projectWeightProportion', false, 'sum');
                                $("#bonusScheduleAddFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                    'projectWeightProportion': projectWeightProportion.toFixed(2),
                                    'personProportion': personProportion
                                });
                                $("#bonusScheduleAddFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                            } else {
                                $("#bonusScheduleAddFormDiv #gview_grid-table-bonusScheduleModuleTable .ui-jqgrid-sdiv").hide();
                            }
                            //②在gridComplete调用合并方法
                            var gridName = "grid-table-bonusScheduleModuleTable";
                            Merger(gridName, ['taskName', 'moduleName']);
                        },
                        userDataOnFooter: true
                    });

                    projectModulTable.refresh();

                    //人员明细 end
                }
            });

            //行删除
            $('body').off('click', '#bonusScheduleAddFormDiv .projectTaskTablerowDel').on('click', '#bonusScheduleAddFormDiv .projectTaskTablerowDel', function () {
                var rowid = projectModulTable.getSelectRowId();
                layer.confirm('确定要删除该人员吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    projectModuleScheduleIdList.push(rowid);
                    projectModulTable.delRowData(rowid);
                    layer.close(index);
                }, function () {
                });
            });

            //提交
            $('body').off('click', '#bonusScheduleSubmit').on('click', '#bonusScheduleSubmit', function () {
                var rowdata = trasenTable.getSelectRowData();
                if (rowdata.length || rowdata.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                if (rowdata.isExamine == 2) {
                    layer.msg('已提交记录不能进行操作！')
                    return false;
                }
                if (rowdata.isExamine == 4) {
                    layer.msg('已同意记录不能进行操作！')
                    return false;
                }
                var factSchedule = rowdata.factSchedule ? rowdata.factSchedule * 1 : 0;
                var projectSchedule = rowdata.projectSchedule ? rowdata.projectSchedule * 1 : 0;
                if (factSchedule - projectSchedule <= 0) {
                    layer.msg('奖金申请比应大于已结算奖金比！')
                    return false;
                }
                layer.confirm('确定要提交吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    if (flag) {
                        flag = false;
                        $.ajax({
                            url: common.url + "/ts-cp/bnousInfo/update",
                            type: "post",
                            data: JSON.stringify(rowdata),
                            contentType: "application/json; charset=utf-8",
                            success: function (res) {
                                if (res.success) {
                                    refreshTable();
                                    setTimeout(function () {
                                        flag = true;
                                    }, 500);
                                    layer.msg('操作成功');
                                } else {
                                    layer.msg(res.message);
                                    flag = true;
                                    return false;
                                }
                            }
                        })
                    }
                    layer.close(index);
                }, function () {
                });


            });
            //打开审核弹窗
            $('body').off('click', '#bonusScheduleExamine').on('click', '#bonusScheduleExamine', function () {
                var rowData = trasenTable.getSelectRowData();
                var bonusType = rowData.bonusType;
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                if (rowData.isExamine == 4) {
                    layer.msg('已同意记录不能进行操作！')
                    return false;
                }
                var html = bonusScheduleExamineFormHtml.innerHTML;
                layer.open({
                    type: 1,
                    title: '审核',
                    closeBtn: 1,
                    shadeClose: false,
                    maxmin: true,
                    area: ['100%', '100%'], //宽高
                    skin: 'yourclass',
                    content: html,
                    success: function (layero, index) {
                        getOptList(rowData.workflowId);
                        //判断项目类型，分配属性
                        if (bonusType == 1) {
                            /*自主研发类*/
                            $("#bonusScheduleExamineFormDiv .travelAmount").hide();
                            $("#bonusScheduleExamineFormDiv .gradeType").hide();
                        } else if (bonusType == 2) {
                            /*维护类*/
                            $("#bonusScheduleExamineFormDiv .travelAmount").hide();
                            $("#bonusScheduleExamineFormDiv .difficultyType").hide();
                            $("#bonusScheduleExamineFormDiv .isHighLines").hide();
                        } else if (bonusType == 3) {
                            /*实施类*/
                            $("#bonusScheduleExamineFormDiv .difficultyType").hide();
                            $("#bonusScheduleExamineFormDiv .gradeType").hide();
                        }

                        trasen.setNamesVal(layero, rowData);
                        $("#bonusScheduleExamineFormDiv #remark").val("");
                        form.render('select');
                        var projectBonus = rowData.projectBonus * 1;
                        var factSchedule = rowData.factSchedule ? rowData.factSchedule * 1 : 0;
                        var projectSchedule = rowData.projectSchedule ? rowData.projectSchedule * 1 : 0;
                        var developQuality = rowData.developQuality ? rowData.developQuality * 1 : 0;
                        var balanceQualityBonus = rowData.balanceQualityBonus ? rowData.balanceQualityBonus * 1 : 0;

                        $("#bonusScheduleExamineFormDiv #thisSchedule").val(factSchedule - projectSchedule);
                        $("#bonusScheduleExamineFormDiv #travelAmountbasicsShow").val($.formMoney(rowData.travelAmount));
                        $('#bonusScheduleExamineFormDiv #projectBudgetbasicsShow').val($.formMoney(rowData.projectBudget));
                        $('#bonusScheduleExamineFormDiv #projectBonusbasicsShow').val($.formMoney(rowData.projectBonus));

                        if (factSchedule != 0) {
                            var thisBalanceQualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(thisBalanceQualityBonus));
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasics').val(thisBalanceQualityBonus);
                        } else {
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasicsShow').val($.formMoney(0));
                            $('#bonusScheduleExamineFormDiv #thisBalanceQualityBonusbasics').val(0);
                        }
                    	if(factSchedule == 100){
	                    	$("#bonusScheduleExamineFormDiv #scheduleTypeDiv").show();
	                    	$("#bonusScheduleExamineFormDiv #delayBonusDiv").show();
	                    	$("#bonusScheduleExamineFormDiv #delayBonusbasicsShow").val($.formMoney(rowData.delayBonus));
		                    $("#bonusScheduleExamineFormDiv #scheduleType").prop("disabled", "disabled");
                        }
                        if (balanceQualityBonus != 0) {
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasicsShow').val($.formMoney(balanceQualityBonus));
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasics').val(balanceQualityBonus);
                        } else {
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasicsShow').val($.formMoney(0));
                            $('#bonusScheduleExamineFormDiv #balanceQualityBonusbasics').val(0);
                        }

                        var projectBonus = rowData.projectBonus * 1;
                        var factSchedule = rowData.factSchedule ? rowData.factSchedule * 1 : 0;
                        var developQuality = rowData.developQuality ? rowData.developQuality * 1 : 0;
                        var projectSchedule = rowData.projectSchedule ? rowData.projectSchedule * 1 : 0;
                        var bonus = '';
                        var qualityBonus = '';
                        if (factSchedule && developQuality) {
                            bonus = (projectBonus * (factSchedule - projectSchedule) * developQuality / 10000).toFixed(2);
                            qualityBonus = (projectBonus * (factSchedule - projectSchedule) * (100 - developQuality) / 10000).toFixed(2);
                        }

                        $('#bonusScheduleExamineFormDiv #projectBonusPic').html($.formMoney(rowData.projectBonus));
                        if(factSchedule == 100){
                            $("#bonusScheduleExamineFormDiv #thisBonus").html($.formMoney((rowData.projectBonus * (factSchedule-projectSchedule) / 100 + balanceQualityBonus + rowData.delayBonus*1).toFixed(2)));
                            $('#bonusScheduleExamineFormDiv #projectBonusPic').html($.formMoney(rowData.projectBonus*1 + + rowData.delayBonus*1));
                        }else{
                            $("#bonusScheduleExamineFormDiv #thisBonus").html($.formMoney((rowData.projectBonus * (factSchedule-projectSchedule) * developQuality / 10000).toFixed(2)));
                        }
//                  	$('#bonusDetailPic').html("本次发放奖金额度："+bonus+"￥  本次结余质量奖金："+qualityBonus+"￥");
                        var projectExamineDetail = new $.trasenTable("grid-table-bonusScheduleExamineDetailTable", {
                            url: common.url + '/ts-cp/moduleSchedule/find/list',
                            pager: '',
                            shrinkToFit: true,
                            sortname: "t4.project_task_info_id,t4.id,t4.create_date",
                            sortorder: "asc",
                            //表格字段
                            colModel: [
                                {
                                    label: 'rowId',
                                    name: 'Id',
                                    index: 'id',
                                    width: "auto",
                                    align: "center",
                                    editable: false,
                                    hidden: true,
                                    key: true
                                },
                                {
                                    label: '项目ID',
                                    name: 'projectInfoId',
                                    index: 'project_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '项目名称',
                                    name: 'projectName',
                                    index: 'project_name',
                                    width: 120,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '研发进度',
                                    name: 'developSchedule',
                                    index: 'develop_schedule',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '研发质量',
                                    name: 'developQuality',
                                    index: 'develop_quality',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '奖金申请比',
                                    name: 'factSchedule',
                                    index: 'fact_schedule',
                                    width: 60,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '奖金预算ID',
                                    name: 'projectBonusInfoId',
                                    index: 'project_bonus_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '所属任务ID',
                                    name: 'projectTaskInfoId',
                                    index: 'project_task_info_id',
                                    width: 100,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '任务名称',
                                    name: 'taskName',
                                    index: 'task_name',
                                    width: 130,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'taskName' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '所属模块ID',
                                    name: 'projectTaskModuleInfoId',
                                    index: 'project_task_module_info_id',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '模块名称',
                                    name: 'moduleName',
                                    index: 'module_name',
                                    width: 130,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleName' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '项目权重(%)',
                                    name: 'projectWeight',
                                    index: 'project_weight',
                                    width: 120,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'projectWeight' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '模块完成进度(%)',
                                    name: 'moduleExecution',
                                    index: 'module_execution',
                                    width: 150,
                                    align: "center",
                                    editable: false,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //合并单元格
                                        return 'id=\'moduleExecution' + rowId + "\'";
                                    }
                                },
                                {
                                    label: '人员code',
                                    name: 'personCode',
                                    index: 'person_code',
                                    width: 80,
                                    align: "center",
                                    editable: false,
                                    hidden: true
                                },
                                {
                                    label: '相关人员',
                                    name: 'personName',
                                    index: 'person_name',
                                    width: 120,
                                    align: "center",
                                    editable: false
                                },
                                {
                                    label: '个人比重(%)',
                                    name: 'personProportion',
                                    index: 'person_proportion',
                                    width: 120,
                                    align: "center",
                                    editable: false
                                },
                                {
                                    label: '个人权重(%)',
                                    name: 'projectWeightProportion',
                                    index: 'project_weight_proportion',
                                    width: 120,
                                    align: "center",
                                    classes: 'redColor',
                                    editable: false
                                },
                                {
                                    label: '实际分配金额（￥）',
                                    name: 'personBonus',
                                    index: 'personBonus',
                                    width: 140,
                                    align: "center",
                                    classes: 'redColor',
                                    editable: false,
                                    formatter: "number",
                                    formatoptions: {
                                        decimalSeparator: ".",
                                        thousandsSeparator: ",",
                                        decimalPlaces: 2,
                                        defaulValue: 0
                                    }
                                }
                            ],
                            postData: {
                                projectBonusInfoId: rowData.id
                            },
                            footerrow: true,
                            gridComplete: function () {
                                var self = this;
                                var rowNum = parseInt($(this).getGridParam('records'), 10);
                                if (rowNum > 0) {
                                    $("#bonusScheduleExamineFormDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").show();
                                    var projectWeightProportion = jQuery(this).getCol('projectWeightProportion', false, 'sum').toFixed(2);
                                    var personBonus = jQuery(this).getCol('personBonus', false, 'sum').toFixed(2);
                                    $("#bonusScheduleExamineFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                        'projectWeightProportion': projectWeightProportion,
                                        'personBonus': personBonus
                                    });
                                    $("#bonusScheduleExamineFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                } else {
                                    $("#bonusScheduleExamineFormDiv #gview_grid-table-softwareReceivable .ui-jqgrid-sdiv").hide();
                                }
                                //②在gridComplete调用合并方法
                                var gridName = "grid-table-bonusScheduleExamineDetailTable";
                                Merger(gridName, ['taskName', 'moduleName', 'projectWeight', 'moduleExecution']);
                            },
                            userDataOnFooter: true
                        });
                        projectExamineDetail.refresh();
                        
                        $("#bonusScheduleExamineFormDiv").off("mouseleave", "#detailPreview").on("mouseleave", "#detailPreview", function () {
                            $(this).children("span").next().fadeOut();
                            setTimeout(function () {
                                $(this).children("span").next().remove()
                            },300)
                        });
                        $("#bonusScheduleExamineFormDiv").off("mouseenter", "#detailPreview>span").on("mouseenter", "#detailPreview>span", function (e) {
                            e.stopPropagation();
                            var projectBonusInfoId = $("#bonusScheduleExamineFormDiv input[name='id']").val();
                            var html = bonusSchedulePreviewFormHtml.innerHTML;
                            $(this).parent().append(html).children("span").next().fadeIn();
                            var previewPersonBonusTable = new $.trasenTable("grid-table-bonusSchedulePreviewTable", {
                                url: common.url + "/ts-cp/moduleSchedule/emplyeeBonus",
                                cellEdit: true,
                                shrinkToFit: true,
                                pager: '',
                                colNames: ['ID', '项目ID', '预算ID', '任务ID', '部门code', '部门名称', '责任人code', '责任人', '项目权重(%)', '分配奖金'],
                                colModel: [
                                    {
                                        name: 'id',
                                        index: 'id',
                                        width: "auto",
                                        classes: 'triggerClick',
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectInfoId',
                                        index: 'project_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectBonusInfoId',
                                        index: 'project_bonus_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'projectTaskInfoId',
                                        index: 'project_task_info_id',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {
                                        name: 'deptCode',
                                        index: 'dept_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'deptName', index: 'dept_name', width: 100, editable: false, hidden: true},
                                    {
                                        name: 'personCode',
                                        index: 'person_code',
                                        width: "auto",
                                        align: "left",
                                        editable: false,
                                        hidden: true
                                    },
                                    {name: 'personName', index: 'person_name', width: 100, editable: false},
                                    {
                                        name: 'projectWeightTotal',
                                        index: 'projectWeightTotal',
                                        width: 100,
                                        align: "right",
                                        classes: 'redColor',
                                        editable: false,
                                        edittype: 'text',
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    },
                                    {
                                        name: 'projectBonusTotal',
                                        index: 'projectBonusTotal',
                                        width: 100,
                                        align: "left",
                                        editable: false,
                                        formatter: "number",
                                        formatoptions: {
                                            decimalSeparator: ".",
                                            thousandsSeparator: ",",
                                            decimalPlaces: 2,
                                            defaulValue: 0
                                        }
                                    }
                                ],
                                afterEditCell: function (rowid, cellname, value, iRow, iCol) {

                                },
                                postData: {
                                    projectBonusInfoId: projectBonusInfoId,
                                    select: 1
                                },
                                buidQueryParams: function () {
                                },
                                afterSaveCell: function (rowid, cellname, value, iRow, iCol) {
                                },
                                footerrow: true,
                                gridComplete: function () {
                                    var self = this;
                                    var rowNum = parseInt($(this).getGridParam('records'), 10);
                                    if (rowNum > 0) {
                                        $("#bonusScheduleExamineFormDiv #gview_grid-table-bonusScheduleTaskTable .ui-jqgrid-sdiv").show();
                                        var projectBonusTotal = jQuery(this).getCol('projectBonusTotal', false, 'sum');
                                        var projectWeightTotal = jQuery(this).getCol('projectWeightTotal', false, 'sum');
                                        $("#bonusScheduleExamineFormDiv #" + self.id).jqGrid('footerData', 'set', {
                                            'projectBonusTotal': projectBonusTotal,
                                            'projectWeightTotal': projectWeightTotal
                                        });
                                        $("#bonusScheduleExamineFormDiv .tra-table-box .ui-jqgrid-sdiv .tableSelectTd").removeClass('tableSelectTd');
                                        // $('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').html($.formMoney(parseFloat($('.tra-table-box .ui-jqgrid-sdiv [aria-describedby="grid-table-hardware_ratio"]').text()).toFixed(2)))
                                    } else {
                                        $("#bonusScheduleExamineFormDiv #gview_grid-table-bonusScheduleTaskTable .ui-jqgrid-sdiv").hide();
                                    }
                                    $(this).parents("#bonusScheduleExamineFormDiv .staticTable").parent().parent().height($(this).parents("#bonusScheduleExamineFormDiv .staticTable").height() + 50)
                                },
                                userDataOnFooter: true
                            });

                            previewPersonBonusTable.refresh();

                        })
                    }
                });
            });

            //同意审批
            form.on('submit(bonusScheduleConfirmProjectBonusBtn)', function (data) {
                data.field.examineIdentification = "1";
                data.field.isExamine = 4;
                if (flag) {
                    flag = false;
                    $.ajax({
                        url: common.url + "/ts-cp/bnousInfo/update",
                        type: "post",
                        data: JSON.stringify(data.field),
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            if (res.success) {
                                layer.closeAll();
                                refreshTable();
                                setTimeout(function () {
                                    flag = true;
                                }, 500);
                                layer.msg('操作成功');
                            } else {
                                layer.msg(res.message);
                                flag = true;
                                return false;
                            }
                        }
                    })
                } else {
                    layer.msg('处理中...', {shade: [0.8, '#393D49'], time: 60 * 60 * 1000});
                }
            });

            //驳回审批
            form.on('submit(bonusScheduleRejectProjectBonusBtn)', function (data) {
                data.field.examineIdentification = "1";
                data.field.isExamine = 3;
                if (!data.field.remark.trim()) {
                    layer.msg('请填写驳回意见！')
                    return false;
                }
                if (flag) {
                    flag = false;
                    $.ajax({
                        url: common.url + "/ts-cp/bnousInfo/update",
                        type: "post",
                        data: JSON.stringify(data.field),
                        contentType: "application/json; charset=utf-8",
                        success: function (res) {
                            if (res.success) {
                                layer.closeAll();
                                refreshTable();
                                setTimeout(function () {
                                    flag = true;
                                }, 500);
                                layer.msg('操作成功');
                            } else {
                                layer.msg(res.message);
                                flag = true;
                                return false;
                            }
                        }
                    })
                } else {
                    layer.msg('处理中...', {shade: [0.8, '#393D49'], time: 60 * 60 * 1000});
                }
            });

            //获取操作记录
            function getOptList(wfInstId) {
                $.ajax({
                    type: "get",
                    contentType: "application/json; charset=utf-8",
                    url: common.url + "/ts-workflow/workflow/task/his/list?sidx=create_date&sord=asc&wfInstId=" + wfInstId,
                    success: function (res) {
                        var optFlowList = res.rows;
                        var html = "";
                        if (optFlowList != null) {
                            for (var i = 0; i < optFlowList.length; i++) {
                                html += '<li class="layui-timeline-item row">';
                                html += ' <i class="layui-icon layui-timeline-axis">&#xe63f;</i>';
                                html += ' <div class="layui-timeline-content layui-text">';
                                html += '<h3>' + optFlowList[i].wfStepName + '</h3>'
                                html += '<div class="text">';

                                if (optFlowList[i].remark != null) {
                                    html += '<p><font>' + optFlowList[i].remark + '</font></p>';
                                }
                                if (optFlowList[i].actAssigneeName) {
                                    html += ' <p>' + optFlowList[i].actAssigneeName + '<span>&nbsp;&nbsp;&nbsp; ' + optFlowList[i].createDate + '</span></p></div></div></li>';
                                } else {
                                    html += ' <p>' + optFlowList[i].assigneeName + '<span>&nbsp;&nbsp;&nbsp;待审核</span></p></div></div></li>';
                                }

                            }
                            $("#bonusScheduleExamineFormDiv #expenseOptFlowUls").append(html);
                        }
                        form.render('select', 'select');
                    }
                })
            }


            //空白出退出编辑状态
            $('body').off('click', '#bonusScheduleAddFormDiv .layui-layer-shade,.layui-layer.layui-layer-page').on('click', '#bonusScheduleAddFormDiv .layui-layer-shade,.layui-layer.layui-layer-page', function () {
                $('#bonusScheduleAddFormDiv #moduleHtml .selected-row.ui-state-hover td').eq(0).trigger('click');
            })
            $('body').off('click', '#bonusScheduleAddFormDiv input[name="moduleExecution"]').on('click', '#bonusScheduleAddFormDiv input[name="moduleExecution"]', function (e) {
                e.stopPropagation();
            })


            $('body').off('click', '#bonusScheduleAddFormDiv #moduleHtml td').on('click', '#bonusScheduleAddFormDiv #moduleHtml td', function (e) {
                e.stopPropagation();
            })
            
            form.verify({
			  //我们既支持上述函数式的方式，也支持下述数组的形式
			  //数组的两个值分别代表：[正则匹配、匹配不符时的提示文字]
			  	number: [
					/^100$|^([1-9]|[1-9]\d)(\.\d{1,2})*$/,'模块完成度必须大于0且小于等于100'
			  	] 
			}); 


            //公共调用方法
            function Merger(gridName, CellNames) {
                //得到显示到界面的id集合
                var mya = $("#" + gridName + "").getDataIDs();
                //当前显示多少条
                var length = mya.length;
                for (var k = 0; k < CellNames.length; k++) {
                    var CellName = CellNames[k];
                    for (var i = 0; i < mya.length; i++) {
                        //从上到下获取一条信息
                        var before = $("#" + gridName + "").jqGrid('getRowData', mya[i]);
                        //定义合并行数
                        var rowSpanTaxCount = 1;
                        for (var j = i + 1; j <= mya.length; j++) {
                            //和上边的信息对比 如果值一样就合并行数+1 然后设置rowspan 让当前单元格隐藏
                            var end = $("#" + gridName + "").jqGrid('getRowData', mya[j]);
                            if (k > 1) {
                                if (before[CellName] == end[CellName] && before[CellNames[1]] == end[CellNames[1]]) {
                                    rowSpanTaxCount++;
                                    $("#" + gridName + "").setCell(mya[j], CellName, '', {display: 'none'});
                                } else {
                                    rowSpanTaxCount = 1;
                                    break;
                                }
                            } else {
                                if (before[CellName] == end[CellName]) {
                                    rowSpanTaxCount++;
                                    $("#" + gridName + "").setCell(mya[j], CellName, '', {display: 'none'});
                                } else {
                                    rowSpanTaxCount = 1;
                                    break;
                                }
                            }

                            $("#" + CellName + mya[i] + "").attr("rowspan", rowSpanTaxCount);
                        }
                    }
                }

            }

            //重新渲染表单
            function renderForm() {
                layui.use('form', function () {
                    var form = layui.form;
                    form.render();
                });
            };


            /**
             * 乘法运算，避免数据相乘小数点后产生多位数和计算精度损失。
             *
             * @param num1被乘数 | num2乘数 | baseNum 初始小数位数
             */
            function numMulti(num1, num2, baseNum) {
                try {
                    baseNum += num1.toString().split(".")[1].length;
                } catch (e) {
                }
                try {
                    baseNum += num2.toString().split(".")[1].length;
                } catch (e) {
                }
                return Number(num1.toString().replace(".", "")) * Number(num2.toString().replace(".", "")) / Math.pow(10, baseNum);
            }

            /**
             * 加法运算，避免数据相加小数点后产生多位数和计算精度损失。
             *
             * @param num1加数1 | num2加数2
             */
            function numAdd(num1, num2) {
                var baseNum, baseNum1, baseNum2;
                try {
                    baseNum1 = num1.toString().split(".")[1].length;
                } catch (e) {
                    baseNum1 = 0;
                }
                try {
                    baseNum2 = num2.toString().split(".")[1].length;
                } catch (e) {
                    baseNum2 = 0;
                }
                baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
                return (num1 * baseNum + num2 * baseNum) / baseNum;
            };

            //金额格式化
            function formatCurrency(num) {
                num = num.toString().replace(/\$|\,/g, '');
                if (isNaN(num))
                    num = "0";
                var sign = (num == (num = Math.abs(num)));
                num = Math.floor(num * 100 + 0.50000000001);
                var cents = num % 100;
                num = Math.floor(num / 100).toString();
                if (cents < 10)
                    cents = "0" + cents;
                for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++)
                    num = num.substring(0, num.length - (4 * i + 3)) + ',' +
                        num.substring(num.length - (4 * i + 3));
                return (((sign) ? '' : '-') + num + '.' + cents);
            }

            /*难度系数*/
            function convertDifficultyRatio(val) {
                if (val == 1) {
                    return 80;
                } else if (val == 2) {
                    return 100
                } else if (val == 3) {
                    return 120
                } else if (val == 4) {
                    return 150
                } else {
                    return 0;
                }
            }

            /*评级系数*/
            function convertGradeRatio(val) {
                if (val == 1) {
                    return 150;
                } else if (val == 2) {
                    return 100
                } else if (val == 3) {
                    return 90
                } else if (val == 4) {
                    return 80
                } else if (val == 5) {
                    return 70
                } else {
                    return 0;
                }
            }

            //进度系数
            function convertScheduleRatio(val) {
                if (val == 1) {
                    return 120;
                } else if (val == 2) {
                    return 100
                } else if (val == 3) {
                    return 80
                } else if (val == 4) {
                    return 60
                } else if (val == 5) {
                    return 40
                } else if (val == 6) {
                    return 20
                } else {
                    return 0;
                }
            }

            /*
			 * 计算项目奖金
			 *
			 * val：进度系数
			 */
            function computeProjectBonus(scheduleType) {
                var bonusType = selectData.bonusType;
                var bonus = 0;
                var delayBonus = 0;
                var projectBudget = selectData.projectBudget;
                projectBudget = projectBudget ? projectBudget.replace(/,/g, "") : 0;//项目预算
                var bonusCoefficient = selectData.bonusCoefficient;
                bonusCoefficient = bonusCoefficient ? bonusCoefficient.replace(/,/g, "") : 0;//奖金系数
                var factPercent = selectData.factPercent;
                factPercent = factPercent ? 100 - factPercent.replace(/,/g, "") : 100;//部门百分比
                var factSchedule = $("#bonusScheduleAddFormDiv #factSchedule").val() ? $("#bonusScheduleAddFormDiv #factSchedule").val() : 0;
                var projectSchedule = $("#bonusScheduleAddFormDiv #projectSchedule").val() ? $("#bonusScheduleAddFormDiv #projectSchedule").val() : 0;
                var schedule = factSchedule - projectSchedule; //本次发放奖金比
                var developQuality = $("#bonusScheduleAddFormDiv #developQuality").val() ? $("#bonusScheduleAddFormDiv #developQuality").val() : 0;
                var balanceQualityBonus = $("#bonusScheduleAddFormDiv #balanceQualityBonusbasics").val();
                balanceQualityBonus = balanceQualityBonus ? balanceQualityBonus.replace(/,/g, "") : 0;//历史结余质量奖

                var scheduleRatio = convertScheduleRatio(scheduleType); //进度系数
                if (bonusType == 1) {
                    /*自主研发类*/
                    var difficultyType = selectData.difficultyType;
                    var difficultyRatio = convertDifficultyRatio(difficultyType); //难度系数
                    //计算部门所占奖金
                    $('#bonusScheduleAddFormDiv input[name="thisDeptBonus"]').val((projectBudget*difficultyRatio*bonusCoefficient*(100-factPercent)*schedule/1000000).toFixed(2));
                    //计算项目奖金
                    bonus = (projectBudget * difficultyRatio * bonusCoefficient * factPercent / 10000).toFixed(2);
                } else if (bonusType == 2) {
                    /*维护类*/
                    var gradeType = selectData.gradeType;
                    var gradeRatio = convertGradeRatio(gradeType); //评级系数
                    //计算部门所占奖金
                    $('#bonusScheduleAddFormDiv input[name="thisDeptBonus"]').val((projectBudget*gradeRatio*bonusCoefficient*(100-factPercent)*schedule/1000000).toFixed(2));
                    //计算项目奖金
                    bonus = (projectBudget * gradeRatio * bonusCoefficient * factPercent / 10000).toFixed(2);
                } else if (bonusType == 3) {
                    /*实施类*/
                    var difficultyType = selectData.difficultyType;
                    //var difficultyRatio = convertDifficultyRatio(difficultyType); //难度系数
                    var travelAmount = selectData.travelAmount;
                    travelAmount = travelAmount ? travelAmount.replace(/,/g, "") : 0;//差旅费
                    //计算部门所占奖金
                    $('#bonusScheduleAddFormDiv input[name="thisDeptBonus"]').val((projectBudget*bonusCoefficient*(100-factPercent)*schedule/10000).toFixed(2));
                    //计算项目奖金
                    bonus = (projectBudget * bonusCoefficient * factPercent / 100 - travelAmount).toFixed(2);
                }
                //计算进度系数奖金
                delayBonus = (scheduleRatio * bonus / 100).toFixed(2);
                //计算本次申请奖金额度
                if(factSchedule == 100){
                    if(delayBonus == 0){
                        $("#bonusScheduleAddFormDiv #thisBonus").html($.formMoney(((bonus * schedule /100) + balanceQualityBonus*1).toFixed(2)));
                    }else{
                        $("#bonusScheduleAddFormDiv #thisBonus").html($.formMoney(((bonus * schedule /100) + balanceQualityBonus*1 + (delayBonus - bonus)).toFixed(2)));
                    }
                }else{
                    $("#bonusScheduleAddFormDiv #thisBonus").html($.formMoney((bonus * schedule * developQuality /10000).toFixed(2)));
                }
                if(scheduleRatio != 0){
                    $('#bonusScheduleAddFormDiv input[name="delayBonus"]').val(delayBonus ? delayBonus - bonus : 0.00);
                    $("#bonusScheduleAddFormDiv #delayBonusbasicsShow").val($.formMoney(delayBonus ? delayBonus - bonus : 0.00));
                    //计算项目奖金
                    $("#bonusScheduleAddFormDiv input[name='projectBonus']").val(bonus);
                    $('#bonusScheduleAddFormDiv #projectBonusbasicsShow').val($.formMoney(bonus));
                    $("#bonusScheduleAddFormDiv #projectBonusPic").html($.formMoney(delayBonus ? delayBonus : bonus));
                }
            }

        })
    }
})
