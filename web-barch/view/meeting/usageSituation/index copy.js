'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            var queryData = {
                boardroom_search: '',
                searchTime: '',
                pattern: 1,
            };
            var meetingDetailListTable = null;
            loadPercentage();
            //会议室使用情况
            function loadPercentage() {
                $.ajax({
                    type: 'post',
                    url: common.url + '/ts-resource/boardroom/place/getLoadPercentage',
                    success: function (res) {
                        if (res.object) {
                            $('#roomAvailableNumber').text(res.object.available);
                        }
                    },
                });
            }
            $('#usageSituation .oa-btn-nav .oa-btn-nav_item')
                .off('click')
                .on('click', function () {
                    $(this).addClass('active').siblings().removeClass('active');
                    var index = $(this).index();
                    if (index == 0) {
                        dayDeal();
                    } else {
                        weekDeal();
                    }
                });
            dayDeal();
            //周处理
            function weekDeal() {
                queryData.pattern = 1;
                $('#usageSituation .weekTable .weekHead').removeClass('none');
                $('#usageSituation .weekTable .dayHead').addClass('none');
                $('#usageSituation  .weekCon').removeClass('none');
                $('#usageSituation  .dayCon').addClass('none');
                $('#usageSituation  #weekBody').removeClass('none');
                $('#usageSituation  #dayBody').addClass('none');
                var now = new Date();
                var weekData = [];
                var today = {
                    year: now.getFullYear(),
                    month: now.getMonth() + 1,
                    date: now.getDate(),
                    day: now.getDay(),
                };
                var currentTime = {
                    year: now.getFullYear(),
                    month: now.getMonth() + 1,
                };
                var selDay = {
                    year: now.getFullYear(),
                    month: now.getMonth() + 1,
                    date: now.getDate(),
                    day: now.getDay(),
                };
                var weekArr = [];
                getWeekArr(now);
                setCurrentTime();

                function getWeekArr(time) {
                    var times = new Date(time);
                    var arr = [];
                    var day = times.getDay() == 0 ? 7 : times.getDay();
                    for (var i = 1; i < 8; i++) {
                        arr.push(getWeekDay(times.getTime() + 1000 * 60 * 60 * 24 * (i - day)));
                    }
                    weekArr = arr;
                    getCalendarData();
                }

                function getWeekDay(time) {
                    var time = new Date(time);
                    return {
                        year: time.getFullYear(),
                        month: addZero(time.getMonth() + 1),
                        date: addZero(time.getDate()),
                    };
                }

                function currentWeek(number) {
                    var t = new Date();
                    t.setFullYear(weekArr[weekArr.length - 1].year);
                    t.setMonth(weekArr[weekArr.length - 1].month - 1);
                    t.setDate(weekArr[weekArr.length - 1].date);
                    t.setTime(t.getTime() + 1000 * 60 * 60 * 24 * number);
                    getWeekArr(t);
                }

                function setCurrentTime() {
                    var isWeek = false;
                    $('#begin-year').text(weekArr[0].year);
                    $('#begin-month').text(weekArr[0].month);
                    $('#begin-date').text(weekArr[0].date);
                    $('#end-year').text(weekArr[6].year);
                    $('#end-month').text(weekArr[6].month);
                    $('#end-date').text(weekArr[6].date);
                    for (var i = 0; i < weekArr.length; i++) {
                        $('#usageSituation #weekHead-' + i).text(weekArr[i].month + '月' + weekArr[i].date + '日');
                        if (today.year == weekArr[i].year && today.month == weekArr[i].month && today.date == weekArr[i].date) {
                            isWeek = true;
                        }
                    }
                    if (isWeek) {
                        $('#localWeek').addClass('active');
                    } else {
                        $('#localWeek').removeClass('active');
                    }
                }
                $('#usageSituation')
                    .off('click', '#prev-week')
                    .on('click', '#prev-week', function () {
                        currentWeek(-7);
                        setCurrentTime();
                    });
                $('#usageSituation')
                    .off('click', '#next-week')
                    .on('click', '#next-week', function () {
                        currentWeek(7);
                        setCurrentTime();
                    });
                $('#usageSituation')
                    .off('click', '#localWeek')
                    .on('click', '#localWeek', function () {
                        getWeekArr(now);
                        setCurrentTime();
                    });

                function getCalendarData() {
                    queryData.searchTime = weekArr[0].year + '-' + weekArr[0].month + '-' + weekArr[0].date + ' 00:00:00';
                    queryData.boardroom_search = $('#search_meet').val();
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-resource/boardroom/place/selectBoardroomApplyBylist',
                        data: {
                            boardroom_search: $('#search_meet').val(),
                            pattern: 1,
                            searchTime: weekArr[0].year + '-' + weekArr[0].month + '-' + weekArr[0].date + ' 00:00:00',
                        },
                        success: function (res) {
                            weekData = res.object || [];
                            weekBodyView();
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                    refmeetingDetailListTable();
                }

                function weekBodyView() {
                    $('#weekBody').html('');
                    var cols = weekData.length;
                    var html = '<tr>';
                    html += '<td class="room-data"></td>';
                    for (var i = 0; i < weekArr.length; i++) {
                        html += '<td class="meets" week-day="' + weekArr[i].year + '-' + weekArr[i].month + '-' + weekArr[i].date + '"></td>';
                    }
                    html += '</tr>';
                    $('#weekBody').append(html);
                    var bo = '';
                    for (var i = 0; i < weekData.length; i++) {
                        bo += '<div class="room" room-id="' + weekData[i].id + '">' + weekData[i].name + '</div>';
                    }
                    $('#weekBody .room-data').html(bo);
                }
                //设置表格内容
                function setWeekRoomData(room) {
                    $('#weekBody td.room-data').siblings().html('');
                    var meetings = room.boardroomApply;
                    for (var i = 0; i < meetings.length; i++) {
                        var html = '';
                        var meet = meetings[i];
                        var date = new Date(meet.startTime).Format('yyyy-MM-dd');
                        var time = new Date(meet.startTime).Format('hh:mm');
                        var isUse = meet.status == 1 ? 'onUse' : 'onOrder';
                        // if (new Date(meet.endTime).getTime() <= new Date().getTime()) {
                        // 	isUse = 'onUse'
                        // }
                        if (meet.isArtificial == 2) {
                            continue;
                        }
                        html += '<div class="meeting ' + isUse + '">' + time + ' ' + meet.motif + '</div>';
                        $('#weekBody td[week-day="' + date + '"]').append(html);
                    }
                }
                //会议室切换
                $('#usageSituation')
                    .off('click', '#weekBody .room')
                    .on('click', '#weekBody .room', function () {
                        var id = $(this).attr('room-id');
                        $(this).addClass('active').siblings().removeClass('active');
                        var room;
                        for (var i = 0; i < weekData.length; i++) {
                            if (weekData[i].id == id) {
                                room = weekData[i];
                                break;
                            }
                        }
                        setWeekRoomData(room);
                        queryData.boardroom_search = room.name;
                        refmeetingDetailListTable();
                    });
                $('#usageSituation')
                    .off('mouseenter', '#weekBody .room')
                    .on('mouseenter', '#weekBody .room', function () {
                        var id = $(this).attr('room-id');
                        var room;
                        for (var i = 0; i < weekData.length; i++) {
                            if (weekData[i].id == id) {
                                room = weekData[i];
                                break;
                            }
                        }
                        var top = $(this).offset().top;
                        $('#usageSituation .roomDetail ').css({
                            top: top,
                            marginTop: -120,
                        });
                        $('#usageSituation .roomDetail #meet-name').text(room.name);
                        $('#usageSituation .roomDetail #meet-location').text(room.location);
                        $('#usageSituation .roomDetail #meet-video').text(returnVideo(room));
                        $('#usageSituation .roomDetail #meet-capacitance').text(room.capacitance + '人');
                        $('#usageSituation .roomDetail #meet-manageOrgName').text(room.manageOrgName || '');
                        $('#usageSituation .roomDetail #meet-hardware').text(returnEquipment(room));
                        $('#usageSituation .roomDetail ').removeClass('none');
                    })
                    .off('mouseout', '#weekBody .room')
                    .on('mouseout', '#weekBody .room', function () {
                        $('#usageSituation .roomDetail ').addClass('none');
                    });

                //搜索
                $('#usageSituation')
                    .off('keydown', '#search_meet')
                    .on('keydown', '#search_meet', function (e) {
                        if (e.keyCode == 13) {
                            getCalendarData();
                        }
                    });
                $('#usageSituation')
                    .off('click', '#fm-fileQueryReset')
                    .on('click', '#fm-fileQueryReset', function (e) {
                        $('#usageSituation #search_meet').val('');
                        getCalendarData();
                    });
            }

            function addZero(num) {
                var num = num - 0;
                return num > 9 ? num : '0' + num;
            }
            //日处理
            function dayDeal() {
                queryData.pattern = 0;
                $('#usageSituation .weekTable .weekHead').addClass('none');
                $('#usageSituation .weekTable .dayHead').removeClass('none');
                $('#usageSituation  .weekCon').addClass('none');
                $('#usageSituation  .dayCon').removeClass('none');
                $('#usageSituation  #weekBody').addClass('none');
                $('#usageSituation  #dayBody').removeClass('none');
                var now = new Date();
                var today = {
                    year: now.getFullYear(),
                    month: addZero(now.getMonth() + 1),
                    date: addZero(now.getDate()),
                };
                var currenTime = {
                    year: now.getFullYear(),
                    month: addZero(now.getMonth() + 1),
                    date: addZero(now.getDate()),
                };
                var dayData;
                setCurrentTime();
                getCalendarData();

                function setCurrentTime() {
                    $('#day-year').text(currenTime.year);
                    $('#day-month').text(currenTime.month);
                    $('#day-date').text(currenTime.date);
                    if (currenTime.year == today.year && currenTime.month == today.month && currenTime.date == today.date) {
                        $('#localDay').addClass('active');
                    } else {
                        $('#localDay').removeClass('active');
                    }
                }

                function currentTime(num) {
                    var date = new Date();
                    date.setFullYear(currenTime.year);
                    date.setMonth(currenTime.month - 1);
                    date.setDate(currenTime.date);
                    date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * num);
                    currenTime.year = date.getFullYear();
                    currenTime.month = addZero(date.getMonth() + 1);
                    currenTime.date = addZero(date.getDate());
                }

                function getCalendarData() {
                    queryData.searchTime = currenTime.year + '-' + currenTime.month + '-' + currenTime.date + ' 00:00:00';
                    queryData.boardroom_search = $('#search_meet').val();
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-resource/boardroom/place/selectBoardroomApplyBylist',
                        data: {
                            boardroom_search: $('#search_meet').val(),
                            pattern: 0,
                            searchTime: currenTime.year + '-' + currenTime.month + '-' + currenTime.date + ' 00:00:00',
                        },
                        success: function (res) {
                            dayData = res.object || [];
                            datBodyView();
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                    refmeetingDetailListTable();
                }

                function datBodyView() {
                    $('#dayBody').html('');
                    var html = '';
                    for (var i = 0; i < dayData.length; i++) {
                        html += '<tr><td class="roomData"><div class="day-room" day-room-id="' + dayData[i].id + '">' + dayData[i].name + '</div>	</td>' + otherDayTd() + '</tr>';
                    }
                    $('#dayBody').html(html);
                    setDayRoomData();
                }

                function setDayRoomData() {
                    for (var i = 0; i < dayData.length; i++) {
                        var meeting = dayData[i];
                        var row = $('[day-room-id="' + meeting.id + '"]').closest('tr');
                        var meets = meeting.boardroomApply;
                        var times = dealTime(meets);
                        for (var time in times) {
                            row.find('td[day-time="' + time + '"]').addClass('onUse');
                        }
                        var times = dealTimeYD(meets);
                        for (var time in times) {
                            row.find('td[day-time="' + time + '"]').addClass('onOrder');
                        }
                    }
                }

                function dealTime(data) {
                    var timeO = {};
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].isArtificial == 2) {
                            continue;
                        }
                        if (data[i].status == -1) {
                            continue;
                        }
                        if (data[i].status != 0) {
                            var staHour = new Date(data[i].startTime).Format('hh');
                            var endHour = new Date(data[i].endTime).Format('hh');
                            if (new Date(data[i].endTime).Format('mm') * 1 > 0) {
                                endHour = endHour * 1 + 1;
                            }
                            for (var hour = staHour; hour < endHour; hour++) {
                                timeO[addZero(hour) + ':00'] = true;
                            }
                        }
                    }
                    return timeO;
                }

                function dealTimeYD(data) {
                    var timeO = {};
                    for (var i = 0; i < data.length; i++) {
                        if (data[i].isArtificial == 2) {
                            continue;
                        }
                        if (data[i].status == 0) {
                            var staHour = new Date(data[i].startTime).Format('hh');
                            var endHour = new Date(data[i].endTime).Format('hh');
                            if (new Date(data[i].endTime).Format('mm') * 1 > 0) {
                                endHour = endHour * 1 + 1;
                            }
                            for (var hour = staHour; hour < endHour; hour++) {
                                timeO[addZero(hour) + ':00'] = true;
                            }
                        }
                    }
                    return timeO;
                }

                function otherDayTd() {
                    return (
                        '<td>' +
                        '	<table style="width: 100%;border-collapse: collapse;border-width:0px; border-style:hidden;">' +
                        '		<tr>' +
                        '		<td day-time="00:00"></td>' +
                        '		<td day-time="01:00"></td>' +
                        '		<td day-time="02:00"></td>' +
                        '		<td day-time="03:00"></td>' +
                        '		<td day-time="04:00"></td>' +
                        '		<td day-time="05:00"></td>' +
                        '		<td day-time="06:00"></td>' +
                        '		<td day-time="07:00"></td>' +
                        '		</tr>' +
                        '	</table>' +
                        '</td>' +
                        '<td day-time="08:00"></td>' +
                        '<td day-time="09:00"></td>' +
                        '<td day-time="10:00"></td>' +
                        '<td day-time="11:00"></td>' +
                        '<td day-time="12:00"></td>' +
                        '<td day-time="13:00"></td>' +
                        '<td day-time="14:00"></td>' +
                        '<td day-time="15:00"></td>' +
                        '<td day-time="16:00"></td>' +
                        '<td day-time="17:00"></td>' +
                        '<td day-time="18:00"></td>' +
                        '<td day-time="19:00"></td>' +
                        '<td day-time="20:00"></td>' +
                        '<td >' +
                        '	<table style="width: 100%;border-collapse: collapse;border-width:0px; border-style:hidden;">' +
                        '		<tr>' +
                        '			<td day-time="21:00"></td>' +
                        '			<td day-time="22:00"></td>' +
                        '			<td day-time="23:00"></td>' +
                        '		</tr>' +
                        '	</table>' +
                        '</td>'
                    );
                }

                $('#usageSituation')
                    .off('click', '#prev-date')
                    .on('click', '#prev-date', function () {
                        currentTime(-1);
                        setCurrentTime();
                        getCalendarData();
                    });
                $('#usageSituation')
                    .off('click', '#next-date')
                    .on('click', '#next-date', function () {
                        currentTime(1);
                        setCurrentTime();
                        getCalendarData();
                    });
                $('#usageSituation')
                    .off('click', '#localDay')
                    .on('click', '#localDay', function () {
                        currenTime = today;
                        setCurrentTime();
                        getCalendarData();
                    });
                //会议室切换
                $('#usageSituation')
                    .off('click', '#dayBody .day-room')
                    .on('click', '#dayBody .day-room', function () {
                        var id = $(this).attr('day-room-id');
                        $('#usageSituation #dayBody .day-room').removeClass('active');
                        $(this).addClass('active');
                        var room;
                        for (var i = 0; i < dayData.length; i++) {
                            if (dayData[i].id == id) {
                                room = dayData[i];
                                break;
                            }
                        }
                        queryData.boardroom_search = room.name;
                        refmeetingDetailListTable();
                    });
                $('#usageSituation')
                    .off('mouseenter', '#dayBody .day-room')
                    .on('mouseenter', '#dayBody .day-room', function () {
                        var id = $(this).attr('day-room-id');
                        var room;
                        for (var i = 0; i < dayData.length; i++) {
                            if (dayData[i].id == id) {
                                room = dayData[i];
                                break;
                            }
                        }
                        var top = $(this).offset().top;
                        $('#usageSituation .roomDetail ').css({
                            top: top,
                            marginTop: -120,
                        });
                        var url = '/static/img/other/meet_02.png';
                        if (room.emphasis) {
                            url = '/ts-document/attachment/' + room.emphasis;
                        }
                        $('#usageSituation .roomDetail .img_bg .img').css('background-image', 'url(' + url + ')');
                        $('#usageSituation .roomDetail #meet-name').text(room.name);
                        $('#usageSituation .roomDetail #meet-location').text(room.location);
                        $('#usageSituation .roomDetail #meet-video').text(returnVideo(room));
                        $('#usageSituation .roomDetail #meet-capacitance').text(room.capacitance + '人');
                        $('#usageSituation .roomDetail #meet-manageOrgName').text(room.manageAuthority || '');
                        $('#usageSituation .roomDetail #meet-hardware').text(returnEquipment(room));
                        $('#usageSituation .roomDetail ').removeClass('none');
                    })
                    .off('mouseout', '#dayBody .day-room')
                    .on('mouseout', '#dayBody .day-room', function () {
                        $('#usageSituation .roomDetail ').addClass('none');
                    });
                $('#usageSituation')
                    .off('keydown', '#search_meet')
                    .on('keydown', '#search_meet', function (e) {
                        if (e.keyCode == 13) {
                            getCalendarData();
                        }
                    });
                $('#usageSituation')
                    .off('click', '#fm-fileQueryReset')
                    .on('click', '#fm-fileQueryReset', function (e) {
                        $('#usageSituation #search_meet').val('');
                        getCalendarData();
                    });
                var isSel = false;
                var selTr = null;
                var pageX = 0;
                var tdX = [];
                //拖拽选中
                $('#usageSituation')
                    .off('mousedown', '#dayBody td[day-time]')
                    .on('mousedown', '#dayBody td[day-time]', function (e) {
                        $('#dayBody td.selTime').removeClass('selTime');
                        $(this).addClass('selTime');
                        selTr = $(this).parents('tr').length == 2 ? $(this).parents('tr').eq(1) : $(this).closest('tr');
                        isSel = true;
                        for (var i = 0; i < 24; i++) {
                            var str = i > 9 ? i : '0' + i;
                            tdX.push($('#dayBody [day-time="' + str + ':00"]').offset().left);
                        }
                        pageX = e.pageX;
                        for (var i = 0; i < tdX.length; i++) {
                            if (tdX[i] - pageX <= 0 && tdX[i + 1] - pageX >= 0) {
                                pageX = tdX[i];
                                break;
                            }
                        }
                    });
                $('#usageSituation')
                    .off('mousemove', '#dayBody')
                    .on('mousemove', '#dayBody', function (e) {
                        if (isSel) {
                            var _x = e.pageX;
                            if (_x - pageX >= 0) {
                                for (var i = 0; i < tdX.length; i++) {
                                    var str = i > 9 ? i : '0' + i;
                                    var td = selTr.find('[day-time="' + str + ':00"]');
                                    if (pageX <= tdX[i] && _x >= tdX[i]) {
                                        td.addClass('selTime');
                                    } else {
                                        td.removeClass('selTime');
                                    }
                                }
                            }
                        }
                    });
                //结束时间
                $('#usageSituation')
                    .off('mouseup', '#dayBody td[day-time]')
                    .on('mouseup', '#dayBody td[day-time]', function (e) {
                        isSel = false;
                        var data = {
                            boardroomId: selTr.find('.roomData .day-room').attr('day-room-id'),
                            startTime: '',
                            endTime: '',
                        };
                        var tds = $('#dayBody td.selTime');
                        var startTime = tds.eq(0).attr('day-time');
                        var endTime = tds.eq(tds.length - 1).attr('day-time');
                        // endTime = parseInt(endTime) + 1 > 9 ? parseInt(endTime) + 1 + ':00' : '0' + (parseInt(endTime) + 1) + ':00';
                        endTime = parseInt(endTime) + 1 > 9 ? parseInt(endTime) + ':59' : '0' + parseInt(endTime) + ':59';
                        data.startTime = currenTime.year + '-' + currenTime.month + '-' + currenTime.date + ' ' + startTime + ':00';
                        data.endTime = currenTime.year + '-' + currenTime.month + '-' + currenTime.date + ' ' + endTime + ':00';
                        if (parseInt(new Date(data.startTime).Format('yyyyMMddhhmm')) < parseInt(new Date().Format('yyyyMMddhhmm'))) {
                            layer.msg('选中时间段必须在当前时间之后！');
                            return false;
                        }
                        $('#dayBody td.selTime').removeClass('selTime');
                        $.ajax({
                            url: '/ts-resource/boardroom/apply/checkOccupy',
                            contentType: 'application/json',
                            method: 'post',
                            data: JSON.stringify(data),
                            success: function (res) {
                                if (res.success && !res.object) {
                                    var room = {};
                                    for (var i = 0; i < dayData.length; i++) {
                                        if (dayData[i].id == data.boardroomId) {
                                            room = dayData[i];
                                            break;
                                        }
                                    }
                                    $.quoteFun('/meeting/usageSituation/approval', {
                                        trasen: trasenTable,
                                        timeData: {
                                            day: currenTime.year + '-' + currenTime.month + '-' + currenTime.date,
                                            startTime: startTime,
                                            endTime: endTime,
                                            room: room,
                                        },
                                        title: '会议室预约',
                                        ref: dayDeal,
                                    });
                                } else {
                                    layer.msg('当前所选时间段内会议室已被占用！');
                                }
                            },
                        });
                    });
            }
            // 会议室预约申请点击事件
            $('#meetingApproval').funs('click', function () {
                var index = $('.oa-btn-nav_item.active').index();
                if (index == 0) {
                    $.quoteFun('/meeting/usageSituation/approval', {
                        trasen: trasenTable,
                        title: '会议室预约',
                        ref: dayDeal,
                    });
                } else {
                    $.quoteFun('/meeting/usageSituation/approval', {
                        trasen: trasenTable,
                        title: '会议室预约',
                        ref: weekDeal,
                    });
                }
            });

            function refmeetingDetailListTable() {
                if (meetingDetailListTable) {
                    meetingDetailListTable.refresh();
                } else {
                    meetingDetailListTable = new $.trasenTable('meetingDetailListTable', {
                        url: common.url + '/ts-resource/boardroom/apply/list',
                        pager: '#meetingDetailListPage',
                        postData: queryData,
                        mtype: 'POST', // post请求需要加
                        shrinkToFit: true,
                        altRows: true,
                        sortname: 'START_TIME',
                        sortorder: 'asc',
                        colModel: [
                            {
                                label: '会议名称',
                                sortable: false,
                                name: 'motif',
                                width: 200,
                                border: 'none',
                                align: 'left',
                            },
                            {
                                label: '组织人',
                                sortable: false,
                                name: 'applyEmpname',
                                width: 100,
                                border: 'none',
                                align: 'left',
                            },
                            {
                                label: '组织科室',
                                sortable: false,
                                name: 'applyOrgname',
                                width: 130,
                                border: 'none',
                                align: 'left',
                            },
                            {
                                label: '会议地点',
                                sortable: false,
                                name: 'meetingAddress',
                                width: 200,
                                border: 'none',
                                align: 'left',
                                formatter: function (cellvalue, options, rowObject) {
                                    return rowObject.location + rowObject.name;
                                },
                            },
                            {
                                label: '开始时间',
                                sortable: false,
                                name: 'startTime',
                                width: 130,
                                border: 'none',

                                align: 'left',
                                formatter: function (cellvalue, options, rowObject) {
                                    return dateTimeFormatter(cellvalue);
                                },
                            },
                            {
                                label: '结束时间',
                                sortable: false,
                                name: 'endTime',
                                width: 130,
                                border: 'none',
                                align: 'left',
                                formatter: function (cellvalue, options, rowObject) {
                                    return dateTimeFormatter(cellvalue);
                                },
                            },
                            {
                                label: '预定状态',
                                sortable: false,
                                name: 'status',
                                width: 95,
                                border: 'none',
                                align: 'center',
                                formatter: function (cellvalue, options, rowObject) {
                                    if (cellvalue == 0) {
                                        return "<span style='color:#F59A23'>待审核</span>";
                                    } else if (cellvalue == 1) {
                                        return "<span style='color:#70B603'>预定成功</span>";
                                    } else if (cellvalue == -1) {
                                        return "<span style='color:red'>不通过</span>";
                                    } else if (cellvalue == 3) {
                                        return "<span style='color:red'>已撤销</span>";
                                    } else {
                                        return '';
                                    }
                                },
                            },
                            {
                                label: '会议状态',
                                sortable: false,
                                name: 'meetingStatus',
                                width: 95,
                                border: 'none',
                                align: 'center',
                                formatter: function (cellvalue, options, rowObject) {
                                    if (cellvalue == 0) {
                                        return '<span>未开始</span>';
                                    }
                                    if (cellvalue == -1) {
                                        return '<span>已结束</span>';
                                    }
                                    if (cellvalue == 1) {
                                        return '<span>会议中</span>';
                                    }
                                },
                            },
                        ],
                        buidQueryParams: function () {
                            // var searchTime = $("#calendarPicker").text() + " 00:00:00";
                            // var pattern = $(".date-btn.btn-gosh.btn-primary").attr("date-type") == "day" ? '0' : '1'; // 0
                            // var boardroom_search = $(":input[name='boardroom_search']").val();
                            // return {
                            // 	searchTime: searchTime,
                            // 	pattern: pattern,
                            // 	boardroom_search: boardroom_search
                            // }
                            return queryData;
                        },
                        // queryFormId: 'resourcesModForm' ,form表单数据作为查询条件
                    });
                }
            }

            //设备
            function returnEquipment(obj) {
                // 投影 / 白板 / 电脑 / 话筒
                var text = '';
                if (obj.projector == 1) {
                    text = ' 投影 /';
                }
                if (obj.computer == 1) {
                    text += ' 电脑 /';
                }
                if (obj.whiteboard == 1) {
                    text += ' 白板 /';
                }
                if (obj.voicetube == 1) {
                    text += ' 话筒 /';
                }
                if (obj.airconditioner == 1) {
                    text += ' 空调 /';
                }
                if (obj.heating == 1) {
                    text += ' 暖气';
                }
                return text.slice(text.length - 1) === '/' ? text.slice(0, -1) : text;
            }
            //资源类型
            function returnVideo(obj) {
                var data = {
                    0: '会议室',
                    3: '教室',
                    4: '场地',
                };
                return data[obj.isVideo];
            }

            function dateTimeFormatter(t) {
                if (!t) return '';
                t = new Date(t).getTime();
                t = new Date(t);
                var year = t.getFullYear();
                var month = t.getMonth() + 1;
                month = checkAddZone(month);

                var date = t.getDate();
                date = checkAddZone(date);

                var hour = t.getHours();
                hour = checkAddZone(hour);

                var min = t.getMinutes();
                min = checkAddZone(min);

                var se = t.getSeconds();
                se = checkAddZone(se);

                return year + '-' + month + '-' + date + ' ' + hour + ':' + min + ':' + se;
            }

            function checkAddZone(num) {
                return num < 10 ? '0' + num.toString() : num;
            }
        });
    };
});
