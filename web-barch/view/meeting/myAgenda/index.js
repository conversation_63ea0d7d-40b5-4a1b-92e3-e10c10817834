'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;

            var myAgendaTable;

            function laodTable() {
                if (myAgendaTable) {
                    myAgendaTable.refresh();
                }
                myAgendaTable = new $.trasenTable('myAgendaTable', {
                    url: common.url + '/ts-resource/boardroom/apply/myMeetingSchedule',
                    pager: '#myAgendaPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    sortname: 't2.START_TIME',
                    sortorder: 'desc',
                    postData: {
                        meetingStatus: '0',
                        meetingSerachType: '0',
                    },

                    colModel: [
                        {
                            label: '会议名称',
                            sortable: false,
                            name: 'motif',
                            width: 200,
                            border: 'none',
                            align: 'center',
                        },
                        {
                            label: '组织人',
                            sortable: false,
                            name: 'applyEmpname',
                            width: 100,
                            border: 'none',
                            align: 'center',
                        },
                        {
                            label: '组织科室',
                            sortable: false,
                            name: 'applyOrgname',
                            width: 130,
                            border: 'none',
                            align: 'center',
                        },
                        {
                            label: '会议地点',
                            sortable: false,
                            name: 'meetingAddress',
                            width: 200,
                            border: 'none',
                            align: 'center',
                            formatter: function (cellvalue, options, rowObject) {
                                return rowObject.location + ' ' + rowObject.name;
                            },
                        },
                        {
                            label: '开始时间',
                            sortable: true,
                            name: 'startTime',
                            width: 130,
                            border: 'none',
                            align: 'center',
                            index: 't2.START_TIME',
                        },
                        {
                            label: '结束时间',
                            sortable: false,
                            name: 'endTime',
                            width: 130,
                            border: 'none',
                            align: 'center',
                        },
                        {
                            label: '会议状态',
                            sortable: false,
                            name: 'meetingStatus',
                            width: 95,
                            border: 'none',
                            align: 'center',
                            formatter: function (cellvalue, options, rowObject) {
                                if (cellvalue == 0) return "<span style='color:#F59A23'>未开始</span>";
                                if (cellvalue == 1) return "<span style='color:#70B603'>会议中</span>";
                                if (cellvalue == -1) return "<span style='color:red'>已结束</span>";
                            },
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            sortable: false,
                            name: '',
                            index: '',
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, rowObject) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                if (rowObject.meetingStatus == '0') {
                                    html += "<button class='layui-btn  determine'  data-signinId='" + rowObject.signinId + "'   data-businessId='" + rowObject.businessId + "'   data-value=" + rowObject.id + "><i class='oaicon oa-icon-zhaopinpeizhi deal_icon' aria-hidden='true'></i>参会</button>";
                                    html += "<button class='layui-btn  leave'   data-reasonLeave='" + rowObject.reasonLeave + "'    data-signinId='" + rowObject.signinId + "'  data-businessId='" + rowObject.businessId + "'   data-value=" + rowObject.id + "><i class='oaicon oa-icon-leave deal_icon' aria-hidden='true'></i>请假</button>";
                                }
                                html += "<button class='layui-btn  oa_myAgenda_list_showView' data-signinId='" + rowObject.signinId + "'   data-businessId='" + rowObject.businessId + "'   data-value=" + rowObject.id + "><i class='fa fa-info-circle deal_icon' aria-hidden='true'></i>详情</button>";
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                    ],
                    buidQueryParams: function () {
                        //查询条件
                        var searchTime = $('#myAgendaCalendar').text().replace('年', '-').replace('月', '-');
                        var meetingSerachType = $('#myAgenda .oa-btn-nav_item.active').attr('date-type') == 'month' ? '0' : '1'; // 0月
                        var meetingStatus = $('#myAgenda .oa-nav_item.active').attr('value');
                        return {
                            searchTime: searchTime,
                            meetingSerachType: meetingSerachType,
                            meetingStatus: meetingStatus,
                        };
                    },
                });
            }

            //显示第一条信息
            //			var onePar = 1;
            //			var oneTopId = '';
            //			showTop(onePar);

            /*		function showTop(onePar) {
						$.ajax({
							type: "post",
							url: common.url + '/ts-resource/boardroom/apply/myMeetingScheduleOne',
							data: {
								meetingSerachType: '0',
								meetingStatus: '0',
								onePar: onePar
							},
							success: function (res) {
								var obj = res.object;
								if (obj != null) {
									oneTopId = obj.id;
									$("#oa_showTop_myAgenda_nextcontent").empty();
									var html = '<p style="color: #F59A23;font-size: 14px;font-weight: bold;">下次会议在' + obj.startTime + '召开</p>' +
										'<p>' + obj.motif + '</p><p style="color: #999;"><i class="iconfont icon-address"></i>' + obj.location + ' ' + obj.name + '</p>' +
										'<p style="color: #999;"><i class="iconfont icon-clock"></i>' + obj.startTime + ' ~~ ' + obj.endTime + '</p>';
									$("#oa_showTop_myAgenda_nextcontent").attr("data-value", obj.id);
									$("#oa_showTop_myAgenda_nextcontent").attr("data-businessId", obj.businessId);
									$("#oa_showTop_myAgenda_nextcontent").append(html);
								} else {

									if (oneTopId) {
										layer.msg("没有下一条了");
									} else {
										$("#oa_showTop_next").hide();
									}
								}

							},
							error: function (res) {
								res = JSON.parse(res.responseText);
								layer.msg(res.message);
							}
						});
					}*/
            //下一条
            /*	$("#oa_showTop_next").click(function () {
					onePar++;
					showTop(onePar);
				})*/

            //会议时间
            laydate.render({
                elem: '#myAgendaCalendar',
                type: 'month',
                range: false,
                showBottom: true,
                format: 'yyyy年MM月',
                value: new Date(),
            });

            //初始化form表单
            form.render();

            //刷新
            function refresh() {
                myAgendaTable.refresh();
            }

            //绑定日期改变事件
            //日期改变事间
            $('#myAgenda #myAgendaCalendar').on('DOMNodeInserted', function (e) {
                refresh();
            });

            //切换月/全部事件
            $('#myAgenda .myAgenda-timeSelect')
                .off('click', '.myAgenda-time-item')
                .on('click', '.myAgenda-time-item', function () {
                    refresh();
                });

            //上一个月点击事件
            $('#myAgenda .myAgenda-bforeBtn').click(function () {
                var selectedVal = $('#myAgendaCalendar').text().replace('年', '-').replace('月', '-');
                var selectPickerVal = showMonth(-1, selectedVal);
                $('#myAgendaCalendar').text(selectPickerVal);
            });

            //下一个月点击事件
            $('#myAgenda .myAgenda-afterBtn').click(function () {
                var selectedVal = $('#myAgendaCalendar').text().replace('年', '-').replace('月', '-');
                var selectPickerVal = showMonth(1, selectedVal);
                $('#myAgendaCalendar').text(selectPickerVal);
            });

            $('#myAgenda')
                .off('click', '.oa_myAgenda_list_showView')
                .on('click', '.oa_myAgenda_list_showView', function () {
                    $.quoteFun('/meeting/myAgenda/meetingDetails', {
                        title: '会议详情',
                        applyId: $(this).attr('data-value'),
                        businessId: $(this).attr('data-businessId'),
                        ref: refresh,
                    });
                });

            //确定参会
            $('#myAgenda')
                .off('click', '.determine')
                .on('click', '.determine', function () {
                    var signinId = $(this).attr('data-signinId');
                    var d = {};
                    d['id'] = signinId;
                    d['isAffirm'] = '1';
                    d = JSON.stringify(d);
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-resource/boardroom/signin/boardroomMeetingConfirm',
                        data: d,
                        contentType: 'application/json',
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.msg('已确定参会');
                            } else {
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            //请假
            $('#myAgenda')
                .off('click', '.leave')
                .on('click', '.leave', function () {
                    $.quoteFun('/meeting/myAgenda/leave', {
                        title: '请假',
                        signinId: $(this).attr('data-signinId'),
                        reasonLeave: $(this).attr('data-reasonLeave'),
                        ref: refresh,
                    });
                });

            //查看详情
            $('#myAgenda')
                .off('click', '#oa_showTop_myAgenda_nextcontent')
                .on('click', '#oa_showTop_myAgenda_nextcontent', function () {
                    $.quoteFun('/meeting/myAgenda/meetingDetails', {
                        title: '会议详情',
                        applyId: $(this).attr('data-value'),
                        businessId: $(this).attr('data-businessId'),
                        ref: refresh,
                    });
                });

            //得到月份  主方法
            function showMonth(pdVal, value) {
                var trans_day = '';
                var cur_date = value == '' ? new Date() : new Date(value); /* 如果日期框内为空的话就获取系统的时间为输入框初始化赋值，如果有值（用户自己选择的时间），那就获取用户自己选择的时间 */
                var cur_year = cur_date.getFullYear();
                var cur_month = cur_date.getMonth() + 1;
                cur_month = cur_month > 9 ? cur_month : '0' + cur_month;
                var eT = cur_year + '-' + cur_month + '-01';
                if (pdVal == 1) {
                    trans_day = addByTransMonth(eT); //下一月
                } else if (pdVal == -1) {
                    trans_day = reduceByTransMonth(eT); //上一月
                }
                //处理
                return trans_day;
            }
            //获取下一月
            function addByTransMonth(dateParameter) {
                var translateDate,
                    monthString = '';
                translateDate = dateParameter.replace('-', '/').replace('-', '/');
                var newDate = new Date(translateDate);
                var relativeYear = newDate.getFullYear();
                var relativeMonth = newDate.getMonth();
                relativeMonth++;
                if (relativeMonth == 12) {
                    relativeYear++;
                    relativeMonth = 0;
                }
                //如果月份长度少于2，则前加 0 补位
                if ((relativeMonth + 1).toString().length == 1) {
                    monthString = 0 + '' + (relativeMonth + 1);
                } else {
                    monthString = relativeMonth + 1;
                }
                var dateMonthStr = relativeYear + '年' + monthString + '月';
                return dateMonthStr;
            }
            //获取上一月
            function reduceByTransMonth(dateParameter) {
                var translateDate,
                    monthString = '';
                translateDate = dateParameter.replace('-', '/').replace('-', '/');
                var newDate = new Date(translateDate);
                var relativeYear = newDate.getFullYear();
                var relativeMonth = newDate.getMonth();
                if (relativeMonth == 0) {
                    relativeYear--;
                    relativeMonth = 12;
                }
                relativeMonth--;
                //如果月份长度少于2，则前加 0 补位
                if ((relativeMonth + 1).toString().length == 1) {
                    monthString = 0 + '' + (relativeMonth + 1);
                } else {
                    monthString = relativeMonth + 1;
                }
                var dateMonthStr = relativeYear + '年' + monthString + '月';
                return dateMonthStr;
            }

            $('#myAgenda .oa-nav .oa-nav_item').click(function () {
                $(this).siblings().removeClass('active'); // 删除其他兄弟元素的样式
                $(this).addClass('active'); // 添加当前元素的样式
                refresh();
            });

            $('#myAgenda .oa-btn-nav span').click(function () {
                $(this).siblings('span').removeClass('active'); // 删除其他兄弟元素的样式
                $(this).addClass('active'); // 添加当前元素的样式
                refresh();
            });
            laodTable();
        });
    };
});
