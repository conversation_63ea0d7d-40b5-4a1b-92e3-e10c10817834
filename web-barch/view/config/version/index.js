'use strict';

define(function(require, exports, module) {
  var init = function() {
    return perform();
  };
  module.exports = {
    init: init
  };

  var perform = function() {
    layui.use(
      ['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'],
      function() {
        var form = layui.form,
          layer = layui.layer,
          layedit = layui.layedit,
          upload = layui.upload,
          laydate = layui.laydate,
          trasen = layui.trasen,
          zTreeSearch = layui.zTreeSearch,
          infoBtnList = [], // 按钮 权限 列表
          infoDataList = [], // 信息数据 列表
          pageInfo = {
            pageNo: 1,
            pageSize: 100
          },
          listScroll = null,
          infoScroll = null;

        /**@desc 加载权限按钮 */
        (function() {
          var ids = $.cookie('lastMenuIdqx');
          if (!ids) {
            return false;
          }
          $.ajax({
            type: 'get',
            async: false, // 同步
            url:
              common.url +
              '/ts-system/resource/getMenuResource/' +
              ids +
              '?n=' +
              Math.random(),
            success: function(res) {
              if (typeof res == 'string') {
                res = JSON.parse(res);
              }
              if (!res.length) {
                return;
              }
              infoBtnList = res.map(item => item.resourceId) || [];
              if (!infoBtnList.includes('versionAddBtn')) {
                $('#version #versionAdd').css('display', 'none');
              }
            },
            error: function(res) {
              console.log(res);
            }
          });
        })();

        /**@desc 初始化 滚动条 */
        (function() {
          if (!window.OverlayScrollbars) {
            return;
          }
          [
            '#version .version-list-content',
            '#version .version-info-list-content'
          ].map(selector => {
            var instance = OverlayScrollbars(
              document.querySelectorAll(selector)
            );
            for (var i = 0; i < instance; i++) {
              instance[i].destroy();
            }
            OverlayScrollbars(document.querySelectorAll(selector), {
              callbacks: {
                onScroll: handleScroll
              }
            });
          });
        })();
        function handleScroll(e) {
          let target = e.currentTarget,
            { offsetHeight = 0, scrollHeight = 0, scrollTop = 0 } = target;
          if (scrollHeight - offsetHeight - scrollTop > 20) {
            return;
          }
          refresh();
        }
        form.render();

        // 刷新
        var refresh = (function() {
          let isLoading = false,
            finished = false;
          return (type, activeKey) => {
            if (type == 'reload') {
              isLoading = false;
              finished = false;
              $('#version .version-list-content ul li').remove();
              $('#version .version-info-list-content ul li').remove();
            }
            if (isLoading || finished) {
              return;
            }
            isLoading = true;
            let data = {
              ...pageInfo,
              version: $('#version #versionForm [name="version"]').val(),
              sord: 'desc',
              sidx: 'create_date'
            };
            $.ajax({
              url: '/ts-basics-bottom/version/list',
              type: 'POST',
              data: data,
              contentType: 'application/x-www-form-urlencoded',
              success: res => {
                isLoading = false;
                if (res.rows) {
                  const rows = res.rows || [];
                  infoDataList = infoDataList.concat(rows);
                  finished = infoDataList.length >= res.totalCount;
                  pageInfo.pageNo++;

                  /**@desc 左侧渲染 */
                  let listContent = $('#version .version-list-content ul'),
                    infoContent = $('#version .version-info-list-content ul');
                  rows.map(row => {
                    let listDom = document.createElement('li');
                    $(listDom).attr('data-key', row.id);
                    $(listDom).append(`
                          <span style="margin-right: 12px;">
                            ${row.versionDate}
                          </span>
                          <span>${row.version}</span>
                        `);
                    $(listDom).on('click', handleVersionListCick);
                    listContent.append(listDom);

                    /**@desc 右侧版本详情预览 */
                    let infoDom = document.createElement('li'),
                      rowActionHtml = '';
                    $(infoDom).attr('data-key', row.id);
                    infoBtnList.includes('versionEditBtn') &&
                      (rowActionHtml += `<i class="fa fa-pencil-square-o" title="编辑"></i>`);
                    infoBtnList.includes('versionDeleteBtn') &&
                      (rowActionHtml += `<i class="fa fa-trash-o" title="删除"></i>`);
                    $(infoDom).append(`
                          <div class="info-item-top-content">
                            <span>
                              <i class="iconfont icon_icon-test5"></i>
                              ${row.versionDate}
                              <span style="margin-left: 12px;">
                                ${row.version}
                              </span>
                            </span>
                            ${rowActionHtml}
                          </div>
                          <div class="info-item-content">
                            ${row.content}
                            <div class="file-item">
                              <i class="fa fa-paperclip"></i>
                              版本安全监测报告.pdf
                            </div>
                            <div class="file-item">
                              <i class="fa fa-paperclip"></i>
                              版本安全监测报告.pdf
                            </div>
                          </div>
                        `);

                    $('.info-item-top-content > span', infoDom)
                      .off('click')
                      .on('click', handleVersionInfoClick);
                    $('.info-item-top-content > .fa-pencil-square-o', infoDom)
                      .off('click')
                      .on('click', function() {
                        handleEditVersionInfo(row);
                      });
                    $('.info-item-top-content > .fa-trash-o', infoDom)
                      .off('click')
                      .on('click', function() {
                        handleDeleteVersionInfo(row);
                      });
                    infoContent.append(infoDom);
                  });

                  activeKey && $(`#version .version-list-content ul li[data-key="${activeKey}"]`).click();
                  if(!activeKey && pageInfo.pageNo == 2) {
                    $('#version .version-list-content ul li:first-child').click();
                  }
                }
              },
              error() {
                isLoading = false;
              }
            });
          };
        })();
        refresh();
        function reload() {
          pageInfo = {
            pageNo: 1,
            pageSize: 100
          };
          refresh('reload');
        }

        //搜索
        $('#versionBaseSearch').funs('click', reload);

        //重置
        $('#versionBaseReset').funs('click', function() {
          $('#version #versionName').val('');
          reload();
        });

        // 新增
        $('#versionAdd').funs('click', function() {
          $.quoteFun('/config/version/add', {
            title: '新增版本信息',
            ref: reload
          });
        });

        // 修改
        function handleEditVersionInfo(row) {
          $.quoteFun('/config/version/add', {
            title: '编辑版本信息',
            data: row,
            ref: function() {
              pageInfo = {
                pageNo: 1,
                pageSize: 100
              };
              refresh('reload', row.id);
            }
          });
        }

        // 删除
        function handleDeleteVersionInfo(row) {
          var d = {
            id: row.id
          };
          layer.confirm(
            '确定删除该数据?',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0
            },
            function(index) {
              $.ajax({
                type: 'post',
                url: common.url + '/ts-basics-bottom/version/deletedById',
                dateType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(d),
                success: function(res) {
                  $.closeloadings();
                  if (res.success) {
                    layer.closeAll();
                    layer.msg('操作成功');
                    reload();
                  } else {
                    layer.closeAll();
                    layer.msg(res.message);
                  }
                },
                error: function(res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res.message);
                }
              });
            }
          );
        }

        /**@desc 左侧版本信息点击事件 */
        function handleVersionListCick(e) {
          $('#version .version-list-content ul li').removeClass('active');
          $(e.currentTarget).addClass('active');
          let dataKey = $(e.currentTarget).attr('data-key');
          $(
            `#version .version-info-list-content ul li[data-key="${dataKey}"]:not(.active) .info-item-top-content > span`
          ).click();

          let { offsetTop } = e.currentTarget,
            {
              offsetHeight,
              scrollHeight
            } = e.currentTarget.offsetParent,
            scroll = 0,
            maxScroll = scrollHeight - offsetHeight;
          if (offsetTop > offsetHeight / 2) {
            scroll = offsetTop - offsetHeight / 2;
          }
          if (!listScroll) {
            listScroll = OverlayScrollbars(
              document.querySelector('#version .version-list-content')
            );
          }
          listScroll.scroll({
            y: scroll < maxScroll ? scroll : maxScroll
          });
        }
        function handleVersionInfoClick(e) {
          let eventLi = $(this).closest('li'),
            dataKey = eventLi.attr('data-key');
          $('#version .version-info-list-content li').removeClass('active');
          // /ts-basics-bottom/version/selectVersionAccessoryByVersionId
          eventLi.addClass('active');
          /**@desc 左侧 对应列表点击 */
          $(
            `#version .version-list-content li[data-key="${dataKey}"]:not(.active)`
          ).click();
          /**@desc 滚动条滚动至指定位置 */
          requestAnimationFrame(() => {
            let { offsetTop } = eventLi[0],
              {
                offsetHeight,
                scrollHeight
              } = eventLi[0].offsetParent,
              scroll = 0,
              maxScroll = scrollHeight - offsetHeight;
            if (offsetTop > offsetHeight / 2) {
              scroll = offsetTop - offsetHeight / 2;
            }
            if (!infoScroll) {
              infoScroll = OverlayScrollbars(
                document.querySelector('#version .version-info-list-content')
              );
            }
            infoScroll.scroll({
              y: scroll < maxScroll ? scroll : maxScroll
            });
          });
          /**@desc 获取附件 */
          $('.info-item-content .file-item', eventLi).remove();
          $.ajax({
            type: 'get',
            url:
              common.url +
              '/ts-basics-bottom/version/selectVersionAccessoryByVersionId?versionId=' +
              dataKey,
            success: function(res) {
              if (res.success) {
                let fileList = [];
                (res.object || []).map(file => {
                  let fileUrl = common.url + "/ts-basics-bottom/fileAttachment/downloadFile/" + file.id;
                  fileList.push(`
                    <div class="file-item">
                      <a href="${fileUrl}">
                        <i class="fa fa-paperclip"></i>
                        ${file.name}
                      </a>
                    </div>
                  `)
                })
                $('.info-item-content', eventLi).append(fileList.join(''));
              } else {
                layer.msg(res.message);
              }
            },
            error: function(res) {
              res = JSON.parse(res.responseText);
              layer.msg(res.message);
            }
          });
        }
      }
    );
  };
});
