"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    let API = {
      getScheduleClinical: "/ts-hrms/scheduleinfo/getScheduleClinical",
      getholidaysByDate: "/ts-hrms/schedulingholidays/getholidaysByDate",
      Export: "/ts-hrms/schedulingholidays/getholidaysByDate/Export",
    };

    let selectModeActive = null;
    var ClinicalDeptShiftScheduleTable = null;
    var formDateDefualValue = "";
    var dateArr = [];
    var personList = [];
    layui.use(
      ["form", "zTreeSearch", "trasen", "element", "laydate"],
      function () {
        var laydate = layui.laydate,
          form = layui.form;

        // 切换周或者月份排班模式
        $("#ClinicalDeptShiftSchedule")
          .off("click", ".select-mode span")
          .on("click", ".select-mode span", function () {
            const active = $(this).attr("data-tab");
            if (selectModeActive === active) {
              return false;
            }

            selectModeActive = active;
            $(this).toggleClass("active");
            $(this).siblings().removeClass();
            let start, end;
            switch (selectModeActive) {
              case "1":
                start = dayjs().startOf("month").format("YYYY-MM-DD");
                end = dayjs().endOf("month").format("YYYY-MM-DD");
                break;
              case "0":
                start = dayjs()
                  .startOf("week")
                  .add(1, "day")
                  .format("YYYY-MM-DD");
                end = dayjs().endOf("week").add(1, "day").format("YYYY-MM-DD");
                break;
            }
            formDateDefualValue = `${start} ~ ${end}`;
            dateArr = getAllDate(start, end);

            renderDate();
            render();
          });

        $(`#ClinicalDeptShiftSchedule span[data-tab=1]`).trigger("click");

        function renderDate() {
          //时间控件
          laydate.render({
            elem: "#ClinicalDeptShiftSchedule #EmergencyDispatchDate",
            range: "~",
            value: formDateDefualValue || "",
            showBottom: true,
            done: function (value = "", date, endDate) {
              if (value) {
                formDateDefualValue = value;
                let [start, end] = formDateDefualValue.split(" ~ ");
                dateArr = getAllDate(start, end);
                render();
              } else {
                selectModeActive = null;
                $(`#ClinicalDeptShiftSchedule span`).removeClass("active");
                $(`#ClinicalDeptShiftSchedule span[data-tab=1]`).trigger(
                  "click"
                );
              }
            },
          });
        }
      }
    );

    // 获取 时间段中的日期
    function getAllDate(start, end) {
      let dateArr = [];
      let startArr = start.split("-");
      let endArr = end.split("-");
      let db = new Date();
      db.setUTCFullYear(startArr[0], startArr[1] - 1, startArr[2]);
      let de = new Date();
      de.setUTCFullYear(endArr[0], endArr[1] - 1, endArr[2]);
      let unixDb = db.getTime();
      let unixDe = de.getTime();
      let stamp;
      const oneDay = 24 * 60 * 60 * 1000;
      for (stamp = unixDb; stamp <= unixDe; ) {
        // 转为 year month date 对象
        let dateYYYYMMDD = dateFormatChange(new Date(parseInt(stamp)));
        let reg = /(\d{4})\-(\d{2})\-(\d{2})\-([\u4e00-\u9fff]{2})/;
        let localDateValue = dateYYYYMMDD.match(reg);

        let dateObj = {
          year: localDateValue[1],
          month: localDateValue[2],
          date: localDateValue[3],
          week: localDateValue[4],
        };

        dateArr.push(dateObj);
        stamp = stamp + oneDay;
      }

      // yyyy-mm-dd
      function dateFormatChange(time) {
        let ymd = "";
        let mouth =
          time.getMonth() + 1 >= 10
            ? time.getMonth() + 1
            : "0" + (time.getMonth() + 1);
        let day = time.getDate() >= 10 ? time.getDate() : "0" + time.getDate();
        ymd += time.getFullYear() + "-"; // 获取年份。
        ymd += mouth + "-"; // 获取月份。
        ymd += day; // 获取日。
        let dir = {
          Monday: "周一",
          Tuesday: "周二",
          Wednesday: "周三",
          Thursday: "周四",
          Friday: "周五",
          Saturday: "周六",
          Sunday: "周日",
        };

        ymd += "-" + dir[dayjs(time).format("dddd")];
        return ymd; // 返回日期。
      }
      return dateArr;
    }

    $("#ClinicalDeptShiftScheduleResetBtn").funs("click", function () {
      $("#ClinicalDeptShiftScheduleForm")[0].reset();
      let num = selectModeActive;
      $(
        `#ClinicalDeptShiftSchedule span[data-tab=${selectModeActive}]`
      ).removeClass();
      selectModeActive = null;
      $(`#ClinicalDeptShiftSchedule span[data-tab=${num}]`).trigger("click");
    });

    $("#ClinicalDeptShiftScheduleSearch").funs("click", function () {
      render();
    });

    $("#ClinicalDeptShiftSchedule")
      .off("click", "#MonthExport")
      .on("click", "#MonthExport", function () {
        if (personList && personList.length > 0) {
          let [startDate, endDate] = formDateDefualValue.split(" ~ ") || [
            "",
            "",
          ];
          let empOrgId = $("#ClinicalDeptShiftSchedule #empOrgId").val() || "";
          var url =
            common.url +
            "/ts-hrms/scheduleinfo/getScheduleClinicalExoprt?" +
            "&startDate=" +
            startDate +
            "&endDate=" +
            endDate +
            "&empOrgId=" +
            empOrgId;
          location.href = url;
        } else {
          layer.msg("暂无数据");
        }
      });

    function render() {
      $("#ClinicalDeptShiftScheduleBox").html(
        '<table id="ClinicalDeptShiftScheduleTable"></table>'
      );
      ClinicalDeptShiftScheduleTable = null;

      let [startDate, endDate] = formDateDefualValue.split(" ~ ") || ["", ""];
      let paramsData = {
        startDate,
        endDate,
        empOrgId: $("#ClinicalDeptShiftSchedule #empOrgId").val() || "",
      };
      let holidaysParamsData = {
        searchStartDate: startDate,
        searchEndDate: endDate,
      };

      $("#ClinicalDeptShiftSchedule .table-title").text(
        `临床科室 ${startDate}至${endDate}排/派班统计情况`
      );

      let holidayDir = {};
      $.ajax({
        url: API.getholidaysByDate,
        type: "get",
        async: false,
        data: holidaysParamsData,
        success: function (res) {
          if (res.success) {
            let row = res.object || [];
            row.forEach((item) => {
              const { holidaysDate, holidaysName } = item;
              holidayDir[holidaysDate] = holidaysName;
            });
          }
        },
      });

      $.ajax({
        url: API.getScheduleClinical,
        type: "get",
        async: false,
        data: paramsData,
        success: function (res) {
          personList = res.rows || [];
        },
      });

      let colModel = [
        {
          name: "employeeName",
          width: 90,
          sortable: false,
          align: "center",
          frozen: true,
          fixed: true,
        },
        {
          name: "name",
          width: 130,
          sortable: false,
          align: "center",
          fixed: true,
          frozen: true,
        },
        {
          name: "phone",
          width: 120,
          sortable: false,
          align: "center",
          fixed: true,
          frozen: true,
        },
      ];
      let colNames = ["姓名", "科室", "手机号"];

      dateArr.forEach((item, index) => {
        const { year, month, date } = item;
        let dateKey = `${year}-${month}-${date}`;

        let holidayName = "";
        if (holidayDir[dateKey]) {
          holidayName = ` (${holidayDir[dateKey]})`;
        }
        colModel.push({
          name: dateKey,
          width: 120,
          sortable: false,
          align: "center",
        });

        colNames.push(item.week + holidayName);
      });

      ClinicalDeptShiftScheduleTable = new $.trasenTable(
        "ClinicalDeptShiftScheduleTable",
        {
          datatype: "local",
          data: personList || [],
          colModel,
          colNames,
          shrinkToFit: false,
          autoScroll: true,
          autowidth: true,
          sortable: false,
        }
      );
      // 设置二级表头
      let groupHeaders = [];
      for (let i = 0; i < dateArr.length; i++) {
        const item = dateArr[i];
        const { year, month, date } = item;

        groupHeaders.push({
          startColumnName: `${year}-${month}-${date}`,
          numberOfColumns: 1,
          titleText: `${month}.${date}`,
        });
      }
      ClinicalDeptShiftScheduleTable.oTable.jqGrid("setFrozenColumns");
      $("#ClinicalDeptShiftScheduleTable").jqGrid("setGroupHeaders", {
        useColSpanStyle: true,
        groupHeaders,
      });
    }
  };
});
