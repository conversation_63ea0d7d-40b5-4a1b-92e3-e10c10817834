"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    $("#scheduling-box #schedulingTypeContent")[0].innerHTML = html;
    let API = {
      getPageAllList: "/ts-hrms/schedulinggrouping/getPageAllList",
      organizationGetTree: "/ts-basics-bottom/organization/getTree3",
      schedulingfrequencyGetList: "/ts-hrms/schedulingfrequency/getPageList",
      frequencygroupGetList: "/ts-hrms/schedulingfrequencygroup/getPageList",
      cheduleinfoSave: "/ts-hrms/scheduleinfo/save",
      cheduleinfoGetSchedule: "/ts-hrms/scheduleinfo/getSchedule",
      getLastWeekSchedule: "/ts-hrms/scheduleinfo/getLastWeekSchedule",
      savesort: "/ts-hrms/scheduleinfo/savesort",
    };

    var weekArr = [];
    var monthArr = [];
    var monthSelectPersonId = null;
    var monthSelectPersonGroupId = null;
    var selNode = null;
    var now = new Date();
    var today = {
      year: now.getFullYear(),
      month: addZero(now.getMonth() + 1),
      date: now.getDate(),
      day: now.getDay(),
    };

    var setting = {
      data: {
        simpleData: {
          enable: false,
        },
      },
      callback: {
        onClick: function (event, treeId, treeNode) {
          if (isSetBoolean) {
            layer.msg("请先结束排班");
            return false;
          }
          monthSelectPersonId = null;
          monthSelectPersonGroupId = null;

          selNode = treeNode;
          $("#personnelScheduling #orgId").val(selNode.id);
          $("#personnelScheduling #orgName").val(selNode.name);

          if (selectModeActive === "1") {
            let nowYearMonth = `${monthArr[0].year}-${monthArr[0].month}`;
            handleGetMonthArr(nowYearMonth);
          }
          render();
          return false;
        },
        onNodeCreated: function (e, id, node) {
          var node = node;
          var treeObj = $.fn.zTree.getZTreeObj("schedulingTree");
          $("#personnelScheduling #orgId").val(common.userInfo.deptId);

          if (node.id == common.userInfo.deptId) {
            treeObj.selectNode(node);
          }
        },
        onCheck: function (e, id, treeNode) {},
      },
      view: {
        dblClickExpand: true,
        showTitle: true,
        showLine: false,
      },
    };

    let selectModeActive = null;
    let startWeekDate = "";
    let endWeekDate = "";

    let selectSetTypeIndex = 0; // 单个排版还是组合排版
    let isSetBoolean = false; // 排版开始状态

    let globalPersonArr = []; // 科室人员数据
    let globalShiftArr = []; // 班次数组
    let globalShiftGroundArr = []; // 组合班次数组
    let useScheduleArr = []; // 使用的班次 （去重）

    layui.use(
      ["form", "zTreeSearch", "trasen", "element", "laydate"],
      function () {
        var form = layui.form,
          laydate = layui.laydate,
          trasen = layui.trasen,
          zTreeSearch = layui.zTreeSearch,
          element = layui.element;
        var treeObj;

        $("#personnelScheduling")
          .off("input", "#TreeSearch")
          .on(
            "input",
            "#TreeSearch",
            common.debounce(function () {
              var val = $(this).val();
              $.fn.ztreeQueryHandler(treeObj, val);
            }, 200)
          );

        // 切换周或者月份排班模式
        $("#personnelScheduling")
          .off("click", ".select-mode span")
          .on("click", ".select-mode span", function () {
            if (isSetBoolean) {
              layer.msg("请先结束排班");
              return;
            }
            const active = $(this).attr("data-tab");
            if (selectModeActive === active) {
              return false;
            }
            weekArr = [];
            monthArr = [];
            monthSelectPersonId = null;
            monthSelectPersonGroupId = null;

            let opposeStr = active === "0" ? "1" : "0";
            $(`#personnelScheduling .crew-scheduling-mode-${active}`).show();
            $(`#personnelScheduling .crew-scheduling-mode-${opposeStr}`).hide();

            selectModeActive = active;
            $(this).toggleClass("active");
            $(this).siblings().removeClass();
            if (selectModeActive === "0") {
              $("#personnelScheduling #MonthSchedulingTboU").html("");
              $("#personnelScheduling #SetMonthClassesTable tbody").html("");
              handleGetWeekArr();
            }
            if (selectModeActive === "1") {
              $("#personnelScheduling #WeekSchedulingTboU").html("");
              handleGetMonthArr(dayjs().format("YYYY-MM-DD"));
            }

            handleSetHeardTimeLabel();
            render();
          });

        // 切换班次与组合班次
        element.on("tab(SetType)", function (data) {
          selectSetTypeIndex = data.index;
          $("#filterShiftInput").val("");
          $("#SchedulingDialog #ExistenceShift").html("");
          if (data.index === 0) {
            handleGetShiftDataFn();
          } else {
            handleGroundShiftDataFn();
          }
        });

        initTree();
        function initTree() {
          $.ajax({
            url: common.url + API.organizationGetTree,
            type: "post",
            async: false,
            success: function (res) {
              if (res.success) {
                var zNodes = res.object;
                treeObj = $.fn.zTree.init(
                  $("#schedulingTree"),
                  setting,
                  zNodes
                );
              } else {
                layer.msg("树加载失败.");
              }
            },
          });
        }
        // 获取班次
        handleGetShiftDataFn();

        setTimeout(() => {
          $(`#personnelScheduling span[data-tab=1]`).trigger("click");
        }, 200)
      }
    );

    function addZero(num) {
      var num = num - 0;
      return num > 9 ? num : "0" + num;
    }

    // 获取本周 日期时间段
    function handleGetWeekArr() {
      // let today = dayjs().day() === 0 ? dayjs().add('-1', 'day') : dayjs()

      // // 本周开始时间
      // let weeklyReportDateStart = dayjs(today).startOf('week').add(1, 'day').format('YYYY-MM-DD');
      // // 本周结束时间
      // let weeklyReportDateEnd = dayjs(today).endOf('week').add(1, 'day').format('YYYY-MM-DD');

      const now = new Date();
      const nowTime = now.getTime();
      // getDay()返回0-6，其中0表示周日，需特殊处理
      const day = now.getDay() > 0 ? now.getDay() : 7; // 表示当前是周几
      const oneDayTime = 24 * 60 * 60 * 1000; // 一天的总ms
      // 本周一时间戳
      const MondayTime = nowTime - (day - 1) * oneDayTime;
      // 本周日时间戳
      const SundayTime = nowTime + (7 - day) * oneDayTime;
      // 格式化时间
      const monday = new Date(MondayTime);
      const sunday = new Date(SundayTime);

      // 本周开始时间
      let weeklyReportDateStart = dayjs(monday).format("YYYY-MM-DD");
      // 本周结束时间
      let weeklyReportDateEnd = dayjs(sunday).format("YYYY-MM-DD");
      weekArr = getAllDate(weeklyReportDateStart, weeklyReportDateEnd);
    }

    function handleGetMonthArr(month) {
      let monthStart = dayjs(month).startOf("month").format("YYYY-MM-DD");
      let monthEnd = dayjs(month).endOf("month").format("YYYY-MM-DD");
      monthArr = getAllDate(monthStart, monthEnd);
      let loaclMonthArr = monthArr.slice(0);

      // dayjs 周日为0 转换为7
      const monthStartWeek = dayjs(monthStart).day() || 7;
      const monthEndWeek = dayjs(monthEnd).day() || 7;
      // 月开始 不为周一 则进行补全第一行 td数量
      if (monthStartWeek !== 1) {
        for (let i = 0; i < monthStartWeek - 1; i++) {
          loaclMonthArr.unshift({});
        }
      }

      // 月结束 不为周日 则进行补全最后一行 td数量
      if (monthEndWeek !== 7) {
        for (let i = 0; i < 7 - monthEndWeek; i++) {
          loaclMonthArr.push({});
        }
      }

      const monthTrArr = splitArray(loaclMonthArr, 7);
      $("#personnelScheduling #SetMonthClassesTable tbody").html("");

      const SetMonthClassesTableHeight = $(
        "#personnelScheduling #SetMonthClassesTable tbody"
      ).height();
      let trHeight = (SetMonthClassesTableHeight - 2) / monthTrArr.length;

      for (let i = 0; i < monthTrArr.length; i++) {
        const item = monthTrArr[i];
        let tr = document.createElement("tr");

        for (let j = 0; j < item.length; j++) {
          const tdItem = item[j];
          const pathDate = `${tdItem.year}-${tdItem.month}-${tdItem.date}`;
          const fakeItem = JSON.stringify(item[j]) === "{}";

          $(tr).append(`<td 
                          class=${!fakeItem ? "areaContent" : ""}
                          date-day=${tdItem.date}
                          date=${pathDate}
                          style="height:${trHeight}px;box-sizing: border-box;"
                        >
                          <p class="date">${tdItem.date || ""}</p>
                        </td>`);
        }
        $("#personnelScheduling #SetMonthClassesTable tbody").append(tr);
      }
    }

    // 获取 时间段中的日期
    function getAllDate(start, end) {
      let dateArr = [];
      let startArr = start.split("-");
      let endArr = end.split("-");
      let db = new Date();
      db.setUTCFullYear(startArr[0], startArr[1] - 1, startArr[2]);
      let de = new Date();
      de.setUTCFullYear(endArr[0], endArr[1] - 1, endArr[2]);
      let unixDb = db.getTime();
      let unixDe = de.getTime();
      let stamp;
      const oneDay = 24 * 60 * 60 * 1000;
      for (stamp = unixDb; stamp <= unixDe; ) {
        // 转为 year month date 对象
        let dateYYYYMMDD = dateFormatChange(new Date(parseInt(stamp)));
        let reg = /(\d{4})\-(\d{2})\-(\d{2})/;
        let localDateValue = dateYYYYMMDD.match(reg);

        let dateObj = {
          year: localDateValue[1],
          month: localDateValue[2],
          date: localDateValue[3],
        };

        dateArr.push(dateObj);
        stamp = stamp + oneDay;
      }

      // yyyy-mm-dd
      function dateFormatChange(time) {
        let ymd = "";
        let mouth =
          time.getMonth() + 1 >= 10
            ? time.getMonth() + 1
            : "0" + (time.getMonth() + 1);
        let day = time.getDate() >= 10 ? time.getDate() : "0" + time.getDate();
        ymd += time.getFullYear() + "-"; // 获取年份。
        ymd += mouth + "-"; // 获取月份。
        ymd += day; // 获取日。
        return ymd; // 返回日期。
      }
      return dateArr;
    }

    // 渲染列表表头时间
    function handleSetHeardTimeLabel() {
      if (selectModeActive === "0") {
        $("#personnelScheduling .week-show").show();
        $("#personnelScheduling .month-show").hide();

        $("#personnelScheduling #ThisWeek").show();
        $("#personnelScheduling #ThisMonth").hide();

        $("#personnelScheduling #schedulingExportBtn").show();
        $("#personnelScheduling #JKSchedulingExport").show();
        $("#personnelScheduling #schedulingPrinitBtn").show();
        $("#personnelScheduling #JKSchedulingPrinit").show();
        $("#personnelScheduling #MonthExport").hide();
        setWeekTopTitle();
      } else {
        $("#personnelScheduling .week-show").hide();
        $("#personnelScheduling .month-show").show();

        $("#personnelScheduling #ThisWeek").hide();
        $("#personnelScheduling #ThisMonth").show();

        $("#personnelScheduling #schedulingExportBtn").hide();
        $("#personnelScheduling #JKSchedulingExport").hide();
        $("#personnelScheduling #schedulingPrinitBtn").hide();
        $("#personnelScheduling #JKSchedulingPrinit").hide();
        $("#personnelScheduling #MonthExport").show();
        setMonthTopTitle();
      }

      function setWeekTopTitle() {
        var isWeek = false;
        $("#personnelScheduling #titleScheduling #begin-year").text(
          weekArr[0].year
        );
        $("#personnelScheduling #titleScheduling #begin-month").text(
          weekArr[0].month
        );
        $("#personnelScheduling #titleScheduling #begin-date").text(
          weekArr[0].date
        );
        $("#personnelScheduling #titleScheduling #end-year").text(
          weekArr[6].year
        );
        $("#personnelScheduling #titleScheduling #end-month").text(
          weekArr[6].month
        );
        $("#personnelScheduling #titleScheduling #end-date").text(
          weekArr[6].date
        );

        for (var i = 0; i < weekArr.length; i++) {
          let item = weekArr[i],
            year = item.year,
            month = item.month,
            date = item.date;
          $(`#personnelScheduling #WeekPaibanBox #areaThead-${i}`).text(
            month + "月" + date + "日"
          );
          $(`#personnelScheduling #WeekPaibanBox #areaThead-${i}`)
            .parent()
            .attr("date", `${year}-${month}-${date}`);
          if (
            today.year == year &&
            today.month == month &&
            today.date == date
          ) {
            isWeek = true;
          }
        }

        if (isWeek) {
          $("#personnelScheduling #titleScheduling #ThisWeek").removeClass(
            "this-week-month-color"
          );
        } else {
          $("#personnelScheduling #titleScheduling #ThisWeek").addClass(
            "this-week-month-color"
          );
        }
      }
      function setMonthTopTitle() {
        var isMonth = false;

        const newDataYear = monthArr[0].year;
        const newDataMonth = monthArr[0].month;

        $("#personnelScheduling #titleScheduling #local-year").text(
          newDataYear
        );
        $("#personnelScheduling #titleScheduling #local-month").text(
          newDataMonth
        );
        if (today.year == newDataYear && today.month == newDataMonth) {
          isMonth = true;
        }

        if (isMonth) {
          $("#personnelScheduling #titleScheduling #ThisMonth").removeClass(
            "this-week-month-color"
          );
        } else {
          $("#personnelScheduling #titleScheduling #ThisMonth").addClass(
            "this-week-month-color"
          );
        }
      }
    }

    // 上一周按钮
    $("#personnelScheduling")
      .off("click", "#prev-week")
      .on("click", "#prev-week", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        let prevWeekMonday = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        // 本周开始时间
        let prevStart = dayjs(prevWeekMonday)
          .add(-1, "week")
          .startOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        // 本周结束时间
        let prevEnd = dayjs(prevWeekMonday)
          .add(-1, "week")
          .endOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        weekArr = getAllDate(prevStart, prevEnd);
        handleSetHeardTimeLabel();
        render();
      });
    // 下一周按钮
    $("#personnelScheduling")
      .off("click", "#next-week")
      .on("click", "#next-week", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }

        let nextWeekMonday = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        // 本周开始时间
        let nextStart = dayjs(nextWeekMonday)
          .add(1, "week")
          .startOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        // 本周结束时间
        let nextEnd = dayjs(nextWeekMonday)
          .add(1, "week")
          .endOf("week")
          .add(1, "day")
          .format("YYYY-MM-DD");
        weekArr = getAllDate(nextStart, nextEnd);
        handleSetHeardTimeLabel();
        render();
      });
    // 本周按钮
    $("#personnelScheduling")
      .off("click", "#ThisWeek")
      .on("click", "#ThisWeek", function () {
        if (
          $("#personnelScheduling #ThisWeek")
            .attr("class")
            .indexOf("this-week-month-color") === -1
        ) {
          return;
        }
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        handleGetWeekArr();
        handleSetHeardTimeLabel();
        render();
      });

    // 上个月按钮
    $("#personnelScheduling")
      .off("click", "#prev-month")
      .on("click", "#prev-month", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        let nowYearMonth = `${monthArr[0].year}-${monthArr[0].month}`;
        let changeYearMonth = dayjs(nowYearMonth)
          .subtract(1, "month")
          .format("YYYY-MM");
        handleGetMonthArr(dayjs(changeYearMonth).format("YYYY-MM-DD"));
        handleSetHeardTimeLabel();
        render();
      });

    // 下个月按钮
    $("#personnelScheduling")
      .off("click", "#next-month")
      .on("click", "#next-month", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }
        let nowYearMonth = `${monthArr[0].year}-${monthArr[0].month}`;
        let changeYearMonth = dayjs(nowYearMonth)
          .add(1, "month")
          .format("YYYY-MM");
        handleGetMonthArr(dayjs(changeYearMonth).format("YYYY-MM-DD"));
        handleSetHeardTimeLabel();
        render();
      });

    // 本月按钮
    $("#personnelScheduling")
      .off("click", "#ThisMonth")
      .on("click", "#ThisMonth", function () {
        if (isSetBoolean) {
          layer.msg("请先结束排班");
          return;
        }

        handleGetMonthArr(dayjs().format("YYYY-MM-DD"));
        handleSetHeardTimeLabel();
        render();
      });

    function render() {
      let paramsData = {
        orgId: $("#personnelScheduling #orgId").val() || "",
        movementType: "1",
      };
      if (selectModeActive === "0") {
        paramsData.searchStartDate = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        paramsData.searchEndDate = `${weekArr[6].year}-${weekArr[6].month}-${weekArr[6].date}`;
      }
      if (selectModeActive === "1") {
        let length = monthArr.length - 1;
        paramsData.searchStartDate = `${monthArr[0].year}-${monthArr[0].month}-${monthArr[0].date}`;
        paramsData.searchEndDate = `${monthArr[length].year}-${monthArr[length].month}-${monthArr[length].date}`;
      }
      $.ajax({
        url: API.getPageAllList,
        type: "post",
        async: false,
        data: paramsData,
        success: function (res) {
          if (res.success) {
            if (res.object.length > 0) {
              const groundObject = {}; // 组合分组人员
              globalPersonArr = res.object;

              res.object.forEach(
                (item) => (groundObject[item.frequencyGroupingId] = [])
              );

              for (const inKey in groundObject) {
                let data = res.object.filter(
                  (filter) => inKey === String(filter.frequencyGroupingId)
                );
                groundObject[inKey] = groundObject[inKey].concat(data);
              }

              let eachArr = Object.keys(groundObject);
              eachArr.sort((a, b) => {
                if (a === "null") {
                  return -1; // a 排在 b 前面
                } else if (b === "null") {
                  return 1; // b 排在 a 前面
                }
                return 0; // 保持原有顺序
              });

              let strGroundPerson = ""; // 表格数据
              if (selectModeActive === "0") {
                eachArr.forEach(groundKey => {
                  strGroundPerson += renderWeekMonthPersonTrFn(
                    groundObject[groundKey],
                    groundKey,
                    "week"
                  );
                })
                $("#personnelScheduling #WeekSchedulingTboU").html(
                  strGroundPerson
                );
              } else {
                eachArr.forEach(groundKey => {
                  strGroundPerson += renderWeekMonthPersonTrFn(
                    groundObject[groundKey],
                    groundKey,
                    "month"
                  );
                })
                $("#personnelScheduling #MonthSchedulingTboU").html(
                  strGroundPerson
                );

                let echoSelectMonthPerson = null;
                if (monthSelectPersonId) {
                  echoSelectMonthPerson = $(
                    "#personnelScheduling #MonthSchedulingTboU"
                  ).find(`[employeeid=${monthSelectPersonId}]`);
                } else {
                  echoSelectMonthPerson = $(
                    "#personnelScheduling #MonthSchedulingTboU"
                  )
                    .children()
                    .eq(0);
                }

                monthSelectPersonId = echoSelectMonthPerson.attr("employeeid");
                monthSelectPersonGroupId =
                  echoSelectMonthPerson.attr("groupid");

                if (monthSelectPersonGroupId === "null") {
                  monthSelectPersonGroupId = "";
                }

                echoSelectMonthPerson.toggleClass("active");
              }
              handleResetTrNoFn();
              handleEachSchedulingData();
            } else {
              let colSpan = selectModeActive === "0" ? 10 : 3;
              let NoDataTr = `<tr><td colSpan=${colSpan}>暂无数据</td></tr>`;

              if (selectModeActive === "0") {
                $("#personnelScheduling #WeekSchedulingTboU").html(NoDataTr);
              }

              if (selectModeActive === "1") {
                monthSelectPersonId = null;
                monthSelectPersonGroupId = null;
                $("#personnelScheduling #MonthSchedulingTboU").html(NoDataTr);
                $("#personnelScheduling #SetMonthClassesTable tbody").html(
                  '<tr><td colSpan="7">暂无数据</td></tr>'
                );
              }
            }
          } else {
            layer.msg("操作失败");
          }
        },
      });
    }

    // 回显排班数据 接口
    function handleEachSchedulingData(orgId) {
      if (selectModeActive === "0") {
        $("#personnelScheduling .areaContent").each((index, item) =>
          $(item).text("")
        );
      } else {
        $("#personnelScheduling .areaContent").each((index, item) => {
          const dayTopItem = $(item).children().eq(0);
          $(item).text("");
          $(item).prepend(dayTopItem);
        });
      }

      useScheduleArr = [];

      const titleScheduling = $("#personnelScheduling #titleScheduling");
      const beginYear = $(titleScheduling).find("#begin-year").text();
      const beginMonth = $(titleScheduling).find("#begin-month").text();
      const beginDate = $(titleScheduling).find("#begin-date").text();
      const endYear = $(titleScheduling).find("#end-year").text();
      const endMonth = $(titleScheduling).find("#end-month").text();
      const endDate = $(titleScheduling).find("#end-date").text();

      startWeekDate = `${beginYear}年${beginMonth}月${beginDate}日`;
      endWeekDate = `${endYear}年${endMonth}月${endDate}日`;

      let dataStartDate = `${beginYear}-${beginMonth}-${beginDate}`;
      let dataEndDate = `${endYear}-${endMonth}-${endDate}`;

      const data = {
        employeeIds: globalPersonArr.map((item) => item.employeeId),
        empOrgId: $("#personnelScheduling #orgId").val() || "",
        startDate: dataStartDate,
        endDate: dataEndDate,
      };

      if (selectModeActive === "1") {
        let length = monthArr.length - 1;
        data.employeeIds = [monthSelectPersonId];
        data.startDate = `${monthArr[0].year}-${monthArr[0].month}-${monthArr[0].date}`;
        data.endDate = `${monthArr[length].year}-${monthArr[length].month}-${monthArr[length].date}`;
      }

      $.ajax({
        url: API.cheduleinfoGetSchedule,
        contentType: "application/json; charset=utf-8",
        type: "post",
        async: false,
        data: JSON.stringify(data),
        success: function (res) {
          if (res.success && res.object.length > 0) {
            let map = new Map();
            for (let item of res.object) {
              if (!map.has(item.frequencyId)) {
                map.set(item.frequencyId, item);
              }
            }
            useScheduleArr = [...map.values()];

            res.object.forEach((dataItem) => {
              // 获取thead头部 日期的索引
              const dateTr = $("#personnelScheduling #SchedulingTable #titleId")
                .find(`[date="${dataItem.schedulingDate}"]`)
                .index();
              // 拿到索引 与用户tr进行匹配 找到用户那一天的 排班小格子
              const employeeIdTr = $(
                "#personnelScheduling #WeekSchedulingTboU"
              ).find(`[employeeId="${dataItem.employeeId}"]`);
              let userTrTd = employeeIdTr.children()[dateTr];

              if (selectModeActive === "1") {
                userTrTd = $("#personnelScheduling #SetMonthClassesTable").find(
                  `[date="${dataItem.schedulingDate}"]`
                )[0];
              }

              if (userTrTd) {
                const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
                if (colorWhite.indexOf(dataItem.frequencyColour) !== -1) {
                  dataItem.frequencyColour = "#000";
                }
                //回显排班时间
                let timeHtml = "";
                const frequencyTimeArr =
                  dataItem.frequencyTime.split(",") || [];
                frequencyTimeArr.forEach((time) => {
                  timeHtml += `<div
                                  class="shiftTime"
                                >${time}
                                </div>`;
                });
                // 将已选班次 拼接入table内
                let span = `<span
                            data-id='${dataItem.frequencyId}'
                            data-color='${dataItem.frequencyColour}'
                            data-name='${dataItem.frequencyName}'
                            data-time='${dataItem.frequencyTime}'
                            style="color: ${dataItem.frequencyColour}"
                          >
                            ${dataItem.frequencyName}
                          </span>
                          ${timeHtml}
                          `;
                userTrTd.innerHTML += span;
              }
            });
          }
        },
      });
    }

    function renderWeekMonthPersonTrFn(arr, key, type) {
      //value       分组id
      //groundName  分组name
      //data        分组数据
      let html = "";
      let isgroup = Number(key !== "null");
      if (key !== "null") {
        html += `<tr class="groupTit" groupid="${key}">
                  <td 
                    style="background-color: rgb(255, 153, 0);
                    text-align: left !important;" 
                    colSpan="10" 
                    isgroup="${isgroup}" 
                    group="${arr.length}"
                   >
                    ${arr[0].frequencyGroupingName}
                  </td>
                </tr>`;
      }
      let tableSelectArea = "";
      if (type === "week") {
        let yyyymmdd0 = `${weekArr[0].year}-${weekArr[0].month}-${weekArr[0].date}`;
        let yyyymmdd1 = `${weekArr[1].year}-${weekArr[1].month}-${weekArr[1].date}`;
        let yyyymmdd2 = `${weekArr[2].year}-${weekArr[2].month}-${weekArr[2].date}`;
        let yyyymmdd3 = `${weekArr[3].year}-${weekArr[3].month}-${weekArr[3].date}`;
        let yyyymmdd4 = `${weekArr[4].year}-${weekArr[4].month}-${weekArr[4].date}`;
        let yyyymmdd5 = `${weekArr[5].year}-${weekArr[5].month}-${weekArr[5].date}`;
        let yyyymmdd6 = `${weekArr[6].year}-${weekArr[6].month}-${weekArr[6].date}`;
        tableSelectArea = `
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd0}></td>
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd1}></td>
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd2}></td>
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd3}></td>
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd4}></td>
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd5}></td>
          <td draggable="false" width="11%" class="areaContent" date=${yyyymmdd6}></td>
        `;
      }
      arr.forEach((j) => {
        html += `
              <tr 
                employeeId="${j.employeeId}" 
                sort="${j.sort}"
                employeeName="${j.employeeName}"
                employeeNo="${j.employeeNo}"
                groupid="${key}"
                groupname="${arr[0].frequencyGroupingName}"
              >
                <td width="4%" style="font-weight: bold;background-color: #F9FAFB;"></td>
                <td width="12%" style="font-weight: bold;">${j.employeeNo}</td>
                <td width="7%" id="SchedulingDraggable" draggable="false" style="font-weight: bold;">${j.employeeName}</td>
                ${tableSelectArea}
              </tr>`;
      });
      return html;
    }

    // 查看个人排班详情
    $("#personnelScheduling")
      .off("click", "#SchedulingDraggable")
      .on("click", "#SchedulingDraggable", function (e) {
        if (selectModeActive === "0") {
          const year = $("#personnelScheduling #begin-year").text();
          const month = $("#personnelScheduling #begin-month").text();
          const data = {
            employeeId: $(this).parent().attr("employeeid"),
            empOrgId: $("#personnelScheduling #orgId").val(),
            schedulingDate: `${year}-${month}`,
          };
          $.quoteFun(
            "workforceManagement/workforce/modules/crewschedulingInfo",
            {
              title: "排班详情",
              data,
              statisticsPage: false,
              isWorkOvertime: false,
              ref: function () {},
            }
          );
        } else {
          let prevPersonSaveStatus = true;
          const saveThObject = $(
            '#personnelScheduling .areaContent[updata="1"]'
          );
          // 正在排班 查看是否操作过排班数据
          if (isSetBoolean && saveThObject.length > 0) {
            const SaveData = [];
            saveThObject.each((index, domItem) => {
              const dateValue = $(domItem).attr("date");
              let employeeId = monthSelectPersonId;
              let frequencyGroupingId = monthSelectPersonGroupId;

              const spans = $(domItem).children("span");
              if (spans.length > 0) {
                // 班次保存 兼容多个
                spans.each((index, item) => {
                  let data = {
                    schedulingDate: dateValue,
                    employeeId,
                    frequencyId: $(item).attr("data-id") || "",
                    frequencyGroupingId,
                    empOrgId: $("#personnelScheduling #orgId").val(),
                  };
                  SaveData.push(data);
                });
              } else {
                // 删除排班
                let data = {
                  schedulingDate: dateValue,
                  employeeId,
                  frequencyId: "",
                  frequencyGroupingId,
                  empOrgId: $("#personnelScheduling #orgId").val(),
                };
                SaveData.push(data);
              }

              $.ajax({
                type: "post",
                contentType: "application/json; charset=utf-8",
                url: common.url + API.cheduleinfoSave,
                data: JSON.stringify(SaveData),
                async: false,
                success: function (res) {
                  saveThObject.each((clearIndex, clearItem) => {
                    $(clearItem).attr("updata", "");
                  });
                  if (res.success) {
                    layer.msg("保存排班成功!");
                  } else {
                    saveThObject.each((clearIndex, clearItem) => {
                      $(clearItem).text("");
                    });
                    layer.msg(res.message || "保存操作失败");
                    prevPersonSaveStatus = false;
                  }
                },
              });
            });
          }

          if (!prevPersonSaveStatus) {
            return false;
          }

          const clickTr = $(this).parent();
          if (!$(clickTr).hasClass("active")) {
            clickTr.toggleClass("active");
          }
          clickTr.siblings().removeClass();

          monthSelectPersonId = "";
          monthSelectPersonGroupId = "";
          monthSelectPersonId = clickTr.attr("employeeid");
          monthSelectPersonGroupId = clickTr.attr("groupid");

          if (monthSelectPersonGroupId === "null") {
            monthSelectPersonGroupId = "";
          }

          handleEachSchedulingData();
        }
      });

    // 开始排班 or 结束排班
    $("#personnelScheduling")
      .off("click", "#startOrEndBtn")
      .on("click", "#startOrEndBtn", function () {
        const _this = $(this);
        if (!isSetBoolean) {
          _this.addClass("layui-btn-normal");
          _this.removeClass("layui-btn-warm");
          _this.html(
            '<i class="layui-icon layui-font-12 layui-icon-ok-circle"></i>结束排班'
          );
          $("#personnelScheduling .areaContent:not(td[data-noclick=1])").css(
            "background-color",
            "#FFFAF4"
          );

          if (selectModeActive === "0") {
            $("#personnelScheduling #copyLastWeekSchedulingBtn").show();
          }
          if (selectModeActive === "1") {
            $("#personnelScheduling #copyLastMonthSchedulingBtn").show();
          }

          $("#personnelScheduling .areaContent").attr("draggable", "true");
          isSetBoolean = true;
          dragRowTableHandle();
        } else {
          $("#personnelScheduling #copyLastWeekSchedulingBtn").hide();
          $("#personnelScheduling #copyLastMonthSchedulingBtn").hide();
          $("#personnelScheduling .areaContent").attr("draggable", "false");
          const saveThObject = $(
            '#personnelScheduling .areaContent[updata="1"]'
          );

          const SaveData = [];
          saveThObject.each((index, domItem) => {
            const dateValue = $(domItem).attr("date");

            let employeeId = $(domItem).parent().attr("employeeid");
            let frequencyGroupingId = $(domItem).parent().attr("groupid");
            if (frequencyGroupingId === "null") frequencyGroupingId = "";

            if (selectModeActive === "1") {
              employeeId = monthSelectPersonId;
              frequencyGroupingId = monthSelectPersonGroupId;
            }
            const spans = $(domItem).children("span");

            if (spans.length > 0) {
              // 班次保存 兼容多个
              spans.each((index, item) => {
                let data = {
                  schedulingDate: dateValue,
                  employeeId,
                  frequencyId: $(item).attr("data-id") || "",
                  frequencyGroupingId,
                  empOrgId: $("#personnelScheduling #orgId").val(),
                };
                SaveData.push(data);
              });
            } else {
              // 删除排班
              let data = {
                schedulingDate: dateValue,
                employeeId,
                frequencyId: "",
                frequencyGroupingId,
                empOrgId: $("#personnelScheduling #orgId").val(),
              };
              SaveData.push(data);
            }
          });

          let sortTrList = $("#personnelScheduling").find("[employeeId]");
          let sortData = [];
          sortTrList.each((index, item) => {
            sortData.push({
              employeeId: $(item).attr("employeeid"),
              empOrgId: $("#personnelScheduling #orgId").val(),
              sort: $(item).attr("sort"),
            });
          });

          $.ajax({
            type: "post",
            contentType: "application/json; charset=utf-8",
            url: common.url + API.savesort,
            data: JSON.stringify(sortData),
            async: false,
            success: function (res) {
              if (res.success) {
                render();
                layer.msg("排序保存成功!");
              } else {
                layer.msg(res.message || "排序保存操作失败");
              }
            },
          });

          $.ajax({
            type: "post",
            contentType: "application/json; charset=utf-8",
            url: common.url + API.cheduleinfoSave,
            data: JSON.stringify(SaveData),
            async: false,
            success: function (res) {
              saveThObject.each((clearIndex, clearItem) => {
                $(clearItem).attr("updata", "");
              });
              if (res.success) {
                layer.msg("保存排班成功!");
                // 渲染最新排班数据 班次名称与时间;
                handleEachSchedulingData();
              } else {
                saveThObject.each((clearIndex, clearItem) => {
                  $(clearItem).text("");
                });
                layer.msg(res.message || "保存操作失败");
              }
            },
          });
          _this.addClass("layui-btn-warm");
          _this.removeClass("layui-btn-normal");
          _this.html(
            `<i class="layui-icon layui-font-12 layui-icon-triangle-r"></i>开始排班`
          );
          $("#personnelScheduling .areaContent:not(td[data-noclick=1])").css(
            "background-color",
            "#FFF"
          );
          isSetBoolean = false;

          $("#personnelScheduling #SchedulingDialog").hide();
          // 取消拖拽
          customElementDragEvent(
            {
              columns: $("#personnelScheduling").find("#SchedulingDraggable"),
              elementParent: "#personnelScheduling",
              element: "td#SchedulingDraggable",
              isUse: 1,
            },
            function (srcObj, curObj) {}
          );
        }
      });

    // 启动拖拽
    function dragRowTableHandle() {
      const areaContent = $("#personnelScheduling .areaContent");
      let drageNeverDom = {};
      areaContent.each((index, item) => {
        // 拖拽 触碰到的盒子 获取dom和sord
        item.ondragleave = function (event) {
          let dom = $(event.target).closest(".areaContent");
          drageNeverDom.dom = dom;
        };
        // 拖拽结束
        item.ondragend = function (event) {
          // 拖动元素和sord
          const thisDom = $(event.target);
          // // 最终拖拽至dom元素和sord
          const finalDom = drageNeverDom.dom;

          let copyHtml = thisDom.children().clone(true);

          if (selectModeActive === "1") {
            $(copyHtml).eq(0).text(finalDom.find(".date").text());
          }

          finalDom.html(copyHtml);
          finalDom.attr("updata", "1");
        };
      });
      customElementDragEvent(
        {
          columns: $("#personnelScheduling").find("#SchedulingDraggable"),
          elementParent: "#personnelScheduling",
          element: "td#SchedulingDraggable",
          style: "",
          dropEffect: "move",
          mozCursor: "default",
        },
        function (srcObj, curObj) {
          dragMoveRows(srcObj, curObj);
        }
      );
    }

    //拖拽移动换行
    function dragMoveRows(curObj, srcObj) {
      let curNow = $(curObj).parent(); // 替换行
      let curNowSort = $(curObj).parent().attr("sort"); // 替换行Sort

      let srcNow = $(srcObj).parent(); // 目标行
      let srcNowSort = $(srcObj).parent().attr("sort"); // 目标行Sort
      if (curNow.attr("groupname") !== srcNow.attr("groupname")) {
        let newGroupingId =
          srcNow.attr("groupid") === "null" ? "" : srcNow.attr("groupid");
        let oldGroupingId =
          curNow.attr("groupid") === "null" ? "" : curNow.attr("groupid"); //原来的行
        const employeeId = curNow.attr("employeeId");
        const employeeName = curNow.attr("employeeName");
        const employeeNo = curNow.attr("employeeNo");
        const orgId = $("#personnelScheduling #orgId").val();
        const _data = {
          oldGroupingId,
          newGroupingId,
          employeeId,
          employeeName,
          employeeNo,
          orgId,
        };
        $.ajax({
          type: "post",
          contentType: "application/json; charset=utf-8",
          url: common.url + "/ts-hrms/schedulinggrouping/updateGroup",
          data: JSON.stringify(_data),
          success: function (res) {
            if (res.success) {
              let tempCurNow = $(
                ".groupTit[groupid$='" + curNow.attr("groupid") + "']"
              )
                .children("td")
                .eq(0)
                .attr("group");
              let tempSrcNow = $(
                ".groupTit[groupid$='" + srcNow.attr("groupid") + "']"
              )
                .children("td")
                .eq(0)
                .attr("group");
              $(".groupTit[groupid$='" + curNow.attr("groupid") + "']")
                .children("td")
                .eq(0)
                .attr("group", Number(tempCurNow) - 1);
              $(".groupTit[groupid$='" + srcNow.attr("groupid") + "']")
                .children("td")
                .eq(0)
                .attr("group", Number(tempSrcNow) + 1);
              if (srcNow.index() > curNow.index()) {
                srcNow.after(curNow);
                curNow.attr("groupname", srcNow.attr("groupname"));
                curNow.attr("groupid", srcNow.attr("groupid"));
              } else {
                srcNow.before(curNow);
                curNow.attr(
                  "groupname",
                  srcNow.attr("groupname") == null
                    ? "无分组"
                    : srcNow.attr("groupname")
                );
                curNow.attr(
                  "groupid",
                  srcNow.attr("groupid") == null ? "0" : srcNow.attr("groupid")
                );
              }
              handleResetTrNoFn();
              layer.msg("已重新分组");
            } else {
              layer.msg(res.message || "操作失败");
            }
          },
        });

        // 请求成功执行
      } else {
        if (srcNowSort > curNowSort) {
          srcNow.after(curNow);
        } else {
          srcNow.before(curNow);
        }

        srcNow.attr("sort", curNowSort);
        curNow.attr("sort", srcNowSort);
        handleResetTrNoFn();
      }
    }

    function customElementDragEvent(options, callback) {
      var defaults = {
        columns: document.querySelectorAll("#divFormTitle .showPcDiv"), //所有能够拖拽的对象
        elementParent: "#divFormTitle", //拖拽父类选择器
        element: ".showPcDiv", //当前元素选择器
        style: "",
        dropEffect: "copy", //复制 还是移动 支持move和copy
        mozCursor: "auto", //鼠标样式  default 默认  auto 系统自动
        isUse: 0, //默认0 启用 1关闭
      };
      var opts = $.extend(defaults, options);
      opts.columns.each(function () {
        $(this).attr("draggable", "true");
      });
      var columns = document.querySelectorAll(
        opts.elementParent + " " + opts.element
      );
      $(opts.elementParent).off("mousedown", opts.element);
      if (opts.isUse == 0) {
        $(opts.elementParent).on("mousedown", opts.element, function (e) {
          $(this).attr("draggable", "true");
          this.addEventListener("dragstart", domdrugstart, false);
          this.addEventListener("dragenter", domdrugenter, false);
          this.addEventListener("dragover", domdrugover, false);
          this.addEventListener("dragleave", domdrugleave, false);
          this.addEventListener("drop", domdrop, false);
          this.addEventListener("dragend", domdrapend, false);
          columns = opts.columns;
        });
      } else {
        $(opts.elementParent).on("mousedown", opts.element, function (e) {
          $(this).attr("draggable", "false");
          this.removeEventListener("dragstart", domdrugstart, false);
          this.removeEventListener("dragenter", domdrugenter, false);
          this.removeEventListener("dragover", domdrugover, false);
          this.removeEventListener("dragleave", domdrugleave, false);
          this.removeEventListener("drop", domdrop, false);
          this.removeEventListener("dragend", domdrapend, false);
          columns = opts.columns;
        });
      }
      var dragEl = null;
      var tempObj = {};
      var srcObj = {};
      if (opts.isUse == 0) {
        [].forEach.call(columns, function (column) {
          column.addEventListener("dragstart", domdrugstart, false);
          column.addEventListener("dragenter", domdrugenter, false);
          column.addEventListener("dragover", domdrugover, false);
          column.addEventListener("dragleave", domdrugleave, false);
          column.addEventListener("drop", domdrop, false);
          column.addEventListener("dragend", domdrapend, false);
        });
      } else {
        [].forEach.call(columns, function (column) {
          column.removeEventListener("dragstart", domdrugstart, false);
          column.removeEventListener("dragenter", domdrugenter, false);
          column.removeEventListener("dragover", domdrugover, false);
          column.removeEventListener("dragleave", domdrugleave, false);
          column.removeEventListener("drop", domdrop, false);
          column.removeEventListener("dragend", domdrapend, false);
        });
      }

      //设置样式
      function domdrugstart(e) {
        e.target.style.opacity = "0.5";
        dragEl = this;
        if (dragEl.innerHTML == "") {
          e.preventDefault();
        } else {
          e.dataTransfer.effectAllowed = opts.dropEffect;
          e.dataTransfer.mozCursor = opts.mozCursor;
          e.dataTransfer.setData("text/html", this.innerHTML);
        }
      }

      function domdrugenter(e) {
        e.target.classList.add("over");
      }

      function domdrugover(e) {
        if (e.preventDefault) {
          e.preventDefault();
        }
        e.dataTransfer.dropEffect = opts.dropEffect;
        return false;
      }

      function domdrugleave(e) {
        e.target.classList.remove("over");
      }

      function domdrop(e) {
        if (e.stopPropagation) {
          e.stopPropagation();
        }
        if (dragEl != this) {
          callback(dragEl, this);
        }
        return false;
      }

      function domdrapend(e) {
        [].forEach.call(columns, function (column) {
          column.classList.remove("over");
          column.style.opacity = "1";
        });
      }
    }

    //重新对序号排序的父子算法
    function handleResetTrNoFn() {
      let index = 1;
      let tableDom =
        selectModeActive === "0"
          ? $("#personnelScheduling #WeekSchedulingTboU")
          : $("#personnelScheduling #MonthSchedulingTboU");

      let trArr = $(tableDom).find("[employeeId]");
      for (let i = 0; i < trArr.length; i++) {
        $(trArr[i]).find("td")[0].innerHTML = index;
        $(trArr[i]).attr("sort", index);
        index++;
      }
    }

    // 点击单元格
    $("#personnelScheduling")
      .off("click", ".areaContent")
      .on("click", ".areaContent", function (event) {
        if (!isSetBoolean) {
          layer.msg("请先进入排班状态!");
          return;
        }
        const curClickObj = $(this);
        // 点击table排班回显
        if (curClickObj.children().length > 0) {
          const renderShiftSpanArr = curClickObj.children("span");
          let renderExistenceStr = "";
          renderShiftSpanArr.each((index, item) => {
            const renderShiftData = $(item).data();
            let data = {
              color: renderShiftData.color,
              id: renderShiftData.id,
              iswhite: renderShiftData.white,
              name: renderShiftData.name,
              time: renderShiftData.time,
            };
            renderShiftData.white = ifWhiteHandle(renderShiftData.color);
            renderExistenceStr += ExistenceShiftRenderList(data);
          });
          $("#personnelScheduling #SchedulingDialog #ExistenceShift").html(
            renderExistenceStr
          );
          // const renderShiftData = curClickObj.children().eq(0).data()
          // renderShiftData.white = ifWhiteHandle(renderShiftData.color)
          // let data = {
          //   color: renderShiftData.color,
          //   id: renderShiftData.id,
          //   iswhite: renderShiftData.white,
          //   name: renderShiftData.name,
          //   time: renderShiftData.time,
          // }
          // $('#SchedulingDialog #ExistenceShift').html(ExistenceShiftRenderList(data))
        } else {
          $("#personnelScheduling #ExistenceShift").html("");
        }

        // 浏览器可用高度
        const clientWidth = document.body.clientWidth;
        const clientHeight = document.body.clientHeight;
        const clickX = event.clientX;
        const clickY = event.clientY;
        const dialog = $("#personnelScheduling #SchedulingDialog");
        const dialogWidth = dialog.width();
        const dialogHeight = dialog.height();
        // click针对 web页面可用区域的点击位置
        let margin = 10;
        if (
          clickX + dialogWidth < clientWidth &&
          clickY + dialogHeight < clientHeight
        ) {
          dialog.css("top", clickY + margin);
          dialog.css("left", clickX + margin);
        }

        if (
          clickX + dialogWidth > clientWidth &&
          clickY + dialogHeight < clientHeight
        ) {
          dialog.css("top", clickY + margin);
          dialog.css("left", clickX - dialogWidth - 30);
        }

        if (
          clickX + dialogWidth < clientWidth &&
          clickY + dialogHeight > clientHeight
        ) {
          dialog.css("top", clickY - dialogHeight - 30);
          dialog.css("left", clickX + margin);
        }

        if (
          clickX + dialogWidth > clientWidth &&
          clickY + dialogHeight > clientHeight
        ) {
          dialog.css("top", clickY - dialogHeight - 30);
          dialog.css("left", clickX - dialogWidth - 30);
        }

        handleGetShiftDataFn();
        // 初始化 tabs切换为班次
        const selectUl = $(
          "#personnelScheduling #SchedulingDialog .select-ul"
        ).children();
        const selectContent = $(
          "#personnelScheduling #SchedulingDialog .select-content"
        ).children();
        selectUl.eq(0).attr("class", "layui-this");
        selectUl.eq(1).attr("class", "");
        selectContent.eq(0).attr("class", "layui-tab-item layui-show");
        selectContent.eq(1).attr("class", "layui-tab-item");
        selectSetTypeIndex = 0;
        dialog.show();
        bindClickItemHandle();
        // 添加点击保存事件
        bindClickSaveHandle(curClickObj, dialog);
      });

    // 渲染班次方法
    function handleGetShiftDataFn() {
      $("#SchedulingDialog #Shift").html("");
      $.ajax({
        type: "post",
        async: false,
        url: common.url + API.schedulingfrequencyGetList,
        data: {
          pageSize: 9999,
          orgId: $("#personnelScheduling #orgId").val(), // 组织机构名称
        },
        success: function (res) {
          if (res.rows.length > 0) {
            globalShiftArr = res.rows;

            let html = "";
            res.rows.forEach((item) => {
              item.white = ifWhiteHandle(item.frequencyColour);

              html += `<li 
                      class="classes-item ${item.white ? "white" : ""}" 
                      style="background:${item.frequencyColour}" 
                      data-id="${item.frequencyId}"
                      data-name="${item.frequencyName}"
                      data-color="${item.frequencyColour}"
                      data-isWhite="${item.white}"
                      data-time="${item.frequencyTime}"
                      title="${item.frequencyTime}"
                    >
                        ${item.frequencyName}
                    </li>`;
            });

            $("#personnelScheduling #SchedulingDialog #Shift").html(html);
          } else {
            $("#personnelScheduling #SchedulingDialog #Shift").html(
              "暂无班次,请新增。"
            );
          }
        },
      });
    }

    // 渲染组合班次方法
    function handleGroundShiftDataFn() {
      $("#personnelScheduling #SchedulingDialog #GroundShift").html("");

      $.ajax({
        type: "post",
        async: false,
        url: common.url + API.frequencygroupGetList,
        data: {
          pageSize: 9999,
          orgId: $("#personnelScheduling #orgId").val(), // 组织机构名称
        },
        success: function (res) {
          if (res.rows.length > 0) {
            globalShiftGroundArr = res.rows;

            let html = "";
            res.rows.forEach((item) => {
              html += `<li 
                      class="classes-item white" 
                      data-id="${item.frequencyGroupId}"
                      >
                        ${item.frequencyGroupName}
                    </li>`;
            });
            $("#personnelScheduling #SchedulingDialog #GroundShift").html(html);
          } else {
            $("#personnelScheduling #SchedulingDialog #GroundShift").html(
              "暂无组合班次,请新增。"
            );
          }
        },
      });
    }

    // 添加班次事件
    function bindClickItemHandle() {
      // 班次添加事件
      $("#personnelScheduling #SchedulingDialog #Shift")
        .off("click", ".classes-item")
        .on("click", ".classes-item", function (event) {
          let html = "";
          html += ExistenceShiftRenderList($(this).data());
          // $('#SchedulingDialog #ExistenceShift').html(html)
          $("#personnelScheduling #SchedulingDialog #ExistenceShift").append(
            html
          );
        });

      // 组合班次添加事件
      $("#personnelScheduling #SchedulingDialog #GroundShift")
        .off("click", ".classes-item")
        .on("click", ".classes-item", function (event) {
          const groundId = $(this).data().id;
          const {
            frequencyColours: colors,
            frequencyGroupContent: names,
            frequencyGroupContentId: ids,
            frequencyTimes: times,
          } = globalShiftGroundArr.filter(
            (item) => item.frequencyGroupId === String(groundId)
          )[0];

          let idArr = ids.split("|");

          const data = {
            colors: colors.split("|"),
            names: names.split("|"),
            ids: idArr,
            times: times.split("|"),
          };

          const shiftArr = idArr.map((item, index) => {
            return {
              name: data.names[index],
              id: data.ids[index],
              color: data.colors[index],
              time: data.times[index],
            };
          });
          let shiftHtml = "";
          shiftArr.forEach((item) => {
            const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
            if (colorWhite.includes(item.color)) {
              item.color = "#000";
            }
            shiftHtml += `
                      <li
                          class="classes-item"
                          style="background:${item.color};color:#fff;"
                          data-id="${item.id}"
                          data-name="${item.name}"
                          data-color="${item.color}"
                          data-time="${item.time}"
                          >
                            ${item.name}
                        </li>
                       `;
          });
          $("#personnelScheduling #SchedulingDialog #ExistenceShift").html(
            shiftHtml
          );
        });

      $("#personnelScheduling #SchedulingDialog #ExistenceShift")
        .off("click", ".classes-item")
        .on("click", ".classes-item", function (event) {
          $(this).remove();
        });
    }

    // 点击保存
    function bindClickSaveHandle(curClickObj, dialog) {
      $("#personnelScheduling #SchedulingDialog")
        .off("click", "#SaveShiftButton")
        .on("click", "#SaveShiftButton", function (event) {
          const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
          const groundSave = $(
            "#personnelScheduling #SchedulingDialog #ExistenceShift"
          ).children();

          if (selectSetTypeIndex === 0) {
            let itemDataArr = [];
            groundSave.each((index, item) => {
              itemDataArr.push($(item).data());
            });

            // 将已选班次 拼接入table内
            let span = "";
            if (itemDataArr.length > 0) {
              itemDataArr.forEach((itemData) => {
                if (colorWhite.includes(itemData.color))
                  itemData.color = "#000";

                let timeHtml = "";
                const frequencyTimeArr = itemData.time.split(",");
                frequencyTimeArr.forEach((time) => {
                  timeHtml += `<div class="shiftTime">${time}</div>`;
                });
                span += `<span
                      data-id='${itemData.id}'
                      data-color='${itemData.color}'
                      data-name='${itemData.name}'
                      data-time='${itemData.time}'
                      style="color:${itemData.color}"
                    >
                      ${itemData.name}
                    </span>
                    ${timeHtml}
                    `;
              });
            }
            curClickObj.attr("updata", 1);
            curClickObj.html(span);

            if (selectModeActive === "1") {
              const dataDay = curClickObj.attr("date-day");
              curClickObj.prepend(`<p class="date">${dataDay || ""}</p>`);
            }
          } else if (selectSetTypeIndex === 1) {
            if (selectModeActive === "0") {
              // 周排班 组合班次渲染
              // 获取到点击表格的索引e
              const curClickIdex = curClickObj.index();
              const curClickTr = curClickObj.parent().children();
              let num = curClickIdex;

              groundSave.each((index, dom) => {
                if ($(curClickTr[num]).length !== 0) {
                  let spans = "";
                  const itemData = $(dom).data();
                  if (colorWhite.includes(itemData.color)) {
                    itemData.color = "#000";
                  }

                  const itemTimeArr = itemData.time.split(",");
                  let timeHtml = "";
                  itemTimeArr.forEach((timeItem) => {
                    timeHtml += `<div class="shiftTime">${timeItem}</div>`;
                  });

                  spans += `<span
                      data-id='${itemData.id}'
                      data-color='${itemData.color}'
                      data-name='${itemData.name}'
                      data-time='${itemData.time}'
                      style="color:${itemData.color}"
                    >
                      ${itemData.name}
                    </span>
                     ${timeHtml}
                    `;
                  $(curClickTr[num]).attr("updata", 1);
                  $(curClickTr[num]).html(spans);
                  num++;
                }
              });
            } else {
              const clickDate = $(curClickObj).attr("date");

              groundSave.each((index, dom) => {
                const date = dayjs(clickDate)
                  .add(index, "day")
                  .format("YYYY-MM-DD");

                const itemTipsDay = dayjs(clickDate)
                  .add(index, "day")
                  .format("DD");

                const setItem = $(
                  "#personnelScheduling #SetMonthClassesTable"
                ).find(`[date=${date}]`);

                if ($(setItem).length !== 0) {
                  let spans = "";
                  const itemData = $(dom).data();
                  if (colorWhite.includes(itemData.color)) {
                    itemData.color = "#000";
                  }

                  const itemTimeArr = itemData.time.split(",");
                  let timeHtml = "";
                  itemTimeArr.forEach((timeItem) => {
                    timeHtml += `<div class="shiftTime">${timeItem}</div>`;
                  });

                  spans += `<span
                      data-id='${itemData.id}'
                      data-color='${itemData.color}'
                      data-name='${itemData.name}'
                      data-time='${itemData.time}'
                      style="color:${itemData.color}"
                    >
                      ${itemData.name}
                    </span>
                     ${timeHtml}
                    `;
                  $(setItem).attr("updata", 1);
                  $(setItem).html(spans);
                  $(setItem).prepend(
                    `<p class="date">${itemTipsDay || ""}</p>`
                  );
                }
              });
            }
          }
          $("#personnelScheduling #SchedulingDialog #ExistenceShift").html("");
          dialog.hide();
        });

      $("#personnelScheduling")
        .off("click", "#cancelShiftButton")
        .on("click", "#cancelShiftButton", function (event) {
          $("#personnelScheduling #ExistenceShift").html("");
          dialog.hide();
        });
    }

    // 导出1
    $("#personnelScheduling")
      .off("click", "#schedulingExportBtn")
      .on("click", "#schedulingExportBtn", function () {
        printOrExportTableFunction("export", 1);
      });

    // 打印1
    $("#personnelScheduling")
      .off("click", "#schedulingPrinitBtn")
      .on("click", "#schedulingPrinitBtn", function () {
        printOrExportTableFunction("print", 1);
      });

    // 导出2
    $("#personnelScheduling")
      .off("click", "#JKSchedulingExport")
      .on("click", "#JKSchedulingExport", function () {
        $("#personnelScheduling #SchedulingTable .shiftTime").remove();
        printOrExportTableFunction("export", 2);
        setTimeout(() => {
          render();
        }, 1000);
      });

    // 月导出
    $("#personnelScheduling")
      .off("click", "#MonthExport")
      .on("click", "#MonthExport", function () {
        var schedulingDate = `${monthArr[0].year}-${monthArr[0].month}`;
        var empOrgId = $("#personnelScheduling #orgId").val() || "";
        var empOrgName = $("#personnelScheduling #orgName").val() || "";
        var url =
          common.url +
          "/ts-hrms/schedulingmanage/getWorkingtableMonthExport?schedulingDate=" +
          schedulingDate +
          "&empOrgId=" +
          empOrgId +
          "&empOrgName=" +
          empOrgName;
        location.href = url;
      });

    // 打印2
    $("#personnelScheduling")
      .off("click", "#JKSchedulingPrinit")
      .on("click", "#JKSchedulingPrinit", function () {
        $("#personnelScheduling #SchedulingTable .shiftTime").hide();
        printOrExportTableFunction("print", 2);
        $("#personnelScheduling #SchedulingTable .shiftTime").show();
      });

    //复制上周班次
    $("#personnelScheduling")
      .off("click", "#copyLastWeekSchedulingBtn")
      .on("click", "#copyLastWeekSchedulingBtn", function () {
        const beginYear = $("#titleScheduling #begin-year").text();
        const beginMonth = $("#titleScheduling #begin-month").text();
        const beginDate = $("#titleScheduling #begin-date").text();
        const endYear = $("#titleScheduling #end-year").text();
        const endMonth = $("#titleScheduling #end-month").text();
        const endDate = $("#titleScheduling #end-date").text();
        let dataStartDate = `${beginYear}-${beginMonth}-${beginDate}`;
        let dataEndDate = `${endYear}-${endMonth}-${endDate}`;
        const data = {
          condition: 1,
          employeeIds: globalPersonArr.map((item) => item.employeeId),
          startDate: dataStartDate,
          endDate: dataEndDate,
        };

        $.ajax({
          method: "post",
          url: API.getLastWeekSchedule,
          contentType: "application/json; charset=utf-8",
          async: false,
          data: JSON.stringify(data),
          success: function (res) {
            if (res.success && res.object.length > 0) {
              //将同一个人、同一天的排班数据整合成一条数据
              let datas = [];
              res.object.forEach((item) => {
                const dataIndex = datas.findIndex(
                  (dataItem) =>
                    dataItem.employeeId == item.employeeId &&
                    dataItem.schedulingDate == item.schedulingDate
                );
                if (dataIndex !== -1) {
                  datas[dataIndex].schedulingList.push(item);
                } else {
                  const schedulingItem = Object.assign({}, item, {
                    schedulingList: [item],
                  });
                  datas.push(schedulingItem);
                }
              });
              datas.forEach((dataItem) => {
                // 获取thead头部 日期的索引
                const dateTr = $(
                  "#personnelScheduling #SchedulingTable #titleId"
                )
                  .find(`[date="${dataItem.schedulingDate}"]`)
                  .index();
                // 拿到索引 与用户tr进行匹配 找到用户那一天的 排班小格子
                const employeeIdTr = $(
                  "#personnelScheduling #WeekSchedulingTboU"
                ).find(`[employeeId="${dataItem.employeeId}"]`);
                const userTrTd = employeeIdTr.children()[dateTr];
                // 本周排班有值则不进行 复制
                if ($(userTrTd).text()) {
                  return false;
                }
                if (userTrTd) {
                  const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
                  if (colorWhite.indexOf(dataItem.frequencyColour) !== -1) {
                    dataItem.frequencyColour = "#000";
                  }
                  dataItem.schedulingList.forEach((schedulingItem) => {
                    //回显排班时间
                    let timeHtml = "";
                    const frequencyTimeArr =
                      schedulingItem.frequencyTime.split(",") || [];
                    frequencyTimeArr.forEach((time) => {
                      timeHtml += `<div
										                  class="shiftTime"
										                >${time}
										                </div>`;
                    });
                    // 将已选班次 拼接入table内
                    let span = `<span
										            data-id='${schedulingItem.frequencyId}'
										            data-color='${schedulingItem.frequencyColour}'
										            data-name='${schedulingItem.frequencyName}'
										            data-time='${schedulingItem.frequencyTime}'
										            style="color: ${schedulingItem.frequencyColour}"
										          >
										            ${schedulingItem.frequencyName}
										          </span>
										          ${timeHtml}
										          `;
                    userTrTd.innerHTML += span;
                  });
                }
                $(userTrTd).attr("updata", "1");
              });
            }
          },
        });
      });

    //复制上月班次
    $("#personnelScheduling")
      .off("click", "#copyLastMonthSchedulingBtn")
      .on("click", "#copyLastMonthSchedulingBtn", function () {
        let length = monthArr.length - 1;
        const data = {
          condition: 2,
          employeeIds: [monthSelectPersonId],
          startDate: `${monthArr[0].year}-${monthArr[0].month}-${monthArr[0].date}`,
          endDate: `${monthArr[length].year}-${monthArr[length].month}-${monthArr[length].date}`,
        };

        $.ajax({
          method: "post",
          url: API.getLastWeekSchedule,
          contentType: "application/json; charset=utf-8",
          async: false,
          data: JSON.stringify(data),
          success: function (res) {
            if (res.success && res.object.length > 0) {
              //将同一个人、同一天的排班数据整合成一条数据
              let datas = [];
              res.object.forEach((item) => {
                const dataIndex = datas.findIndex(
                  (dataItem) =>
                    dataItem.employeeId == item.employeeId &&
                    dataItem.schedulingDate == item.schedulingDate
                );
                if (dataIndex !== -1) {
                  datas[dataIndex].schedulingList.push(item);
                } else {
                  const schedulingItem = Object.assign({}, item, {
                    schedulingList: [item],
                  });
                  datas.push(schedulingItem);
                }
              });
              datas.forEach((dataItem) => {
                const userTrTd = $(
                  "#personnelScheduling #SetMonthClassesTable"
                ).find(`[date="${dataItem.schedulingDate}"]`)[0];

                // 本周排班有值则不进行 复制
                if ($(userTrTd).find("span").length) {
                  return false;
                }

                if (userTrTd) {
                  const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
                  if (colorWhite.indexOf(dataItem.frequencyColour) !== -1) {
                    dataItem.frequencyColour = "#000";
                  }
                  dataItem.schedulingList.forEach((schedulingItem) => {
                    //回显排班时间
                    let timeHtml = "";
                    const frequencyTimeArr =
                      schedulingItem.frequencyTime.split(",") || [];
                    frequencyTimeArr.forEach((time) => {
                      timeHtml += `<div
										                  class="shiftTime"
										                >${time}
										                </div>`;
                    });
                    // 将已选班次 拼接入table内
                    let span = `<span
										            data-id='${schedulingItem.frequencyId}'
										            data-color='${schedulingItem.frequencyColour}'
										            data-name='${schedulingItem.frequencyName}'
										            data-time='${schedulingItem.frequencyTime}'
										            style="color: ${schedulingItem.frequencyColour}"
										          >
										            ${schedulingItem.frequencyName}
										          </span>
										          ${timeHtml}
										          `;
                    userTrTd.innerHTML += span;
                  });
                }
                $(userTrTd).attr("updata", "1");
              });
            }
          },
        });
      });

    function printOrExportTableFunction(type, styleType) {
      if (isSetBoolean) {
        layer.msg("请先结束排班");
        return;
      }
      let titleType = type === "export" ? "导出" : "打印";
      let orgName = $("#personnelScheduling #personnelFormTreeSearch").val();
      if (isEmpty(orgName)) {
        orgName = common.hrUserInfo.empDeptName;
      }

      let printOrExportTitleHtml = `<tr
                            printOrExport="true"
                            style="text-align: center;font-size: 20px;"
                          >
                            <td colSpan="10">
                              ${orgName}排班表
                            </td>
                          </tr>
                          <tr
                            class="groupTit"
                            printOrExport="true"
                          >
                            <td colSpan="10">
                              排班日期: ${startWeekDate} ～ ${endWeekDate}
                            </td>
                          </tr>
                          `;
      let printOrExportFootHtml = `
                          <tr id="ExportOrPrintTableFoot" printOrExport="true">
                            <td colspan="4">
                                科室名称: ${orgName}
                            </td>
                            <td colspan="3">
                               ${titleType}日期: ${today.year}/${today.month}/${today.date}
                            </td>
                            <td colspan="3">
                                排班人: ${common.userInfo.username}
                            </td>
                          </tr>
                          `;

      $("#personnelScheduling #SchedulingTable thead").prepend(
        printOrExportTitleHtml
      );

      if (styleType === 2) {
        let scheduleInfoTr = ``;
        useScheduleArr.forEach((item) => {
          scheduleInfoTr += `
            <tr printOrExport="true">
              <td colspan="4">
                班次名称: ${item.frequencyName}
              </td>
              <td colspan="6">
                班次时间: ${item.frequencyTime}
              </td>
            </tr>
          `;
        });
        $("#personnelScheduling #SchedulingTable #WeekSchedulingTboU").append(
          scheduleInfoTr
        );
      }

      $("#personnelScheduling #SchedulingTable #WeekSchedulingTboU").append(
        printOrExportFootHtml
      );

      $("#personnelScheduling #SchedulingTable").attr("border", "1");
      $("#personnelScheduling #SchedulingTable tr td").css(
        "text-align",
        "center"
      );
      $("#personnelScheduling #SchedulingTable tr th").css(
        "text-align",
        "center"
      );
      $("#personnelScheduling #SchedulingTable .groupTit td").css(
        "text-align",
        "left"
      );

      $("#personnelScheduling #ExportOrPrintTableFoot").css({
        border: "transparent",
      });
      $("#personnelScheduling #ExportOrPrintTableFoot").children().eq(0).css({
        "text-align": "left",
      });
      $("#personnelScheduling #ExportOrPrintTableFoot").children().eq(1).css({
        "text-align": "center",
      });
      $("#personnelScheduling #ExportOrPrintTableFoot").children().eq(2).css({
        "text-align": "right",
      });
      const exprotName = `${today.year}年${today.month}月${orgName}排班`;
      switch (type) {
        case "export":
          tableToExcel("SchedulingTable", "schedulingExportBtn", exprotName);
          break;
        case "print":
          $("#personnelScheduling #SchedulingTable").jqprint();
          break;
      }
      setTimeout(() => {
        $(
          '#personnelScheduling #SchedulingTable tr[printOrExport="true"]'
        ).remove();
        $("#personnelScheduling #SchedulingTable").attr("border", "0");
      }, 500);
    }

    function tableToExcel(tableid, btnname, exprotName) {
      var uri = "data:application/vnd.ms-excel;base64,",
        template =
          '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
        base64 = function (s) {
          return window.btoa(unescape(encodeURIComponent(s)));
        },
        dataFormat = function (s, c) {
          return s.replace(/{(\w+)}/g, function (m, p) {
            return c[p];
          });
        };
      //根据ID获取table表格HTML
      var table = document.getElementById(tableid);
      var ctx = {
        worksheet: "Worksheet",
        table: table.innerHTML,
      };

      var alink = document.createElement("a");
      alink.href = uri + base64(dataFormat(template, ctx));
      alink.download = `${exprotName}.xls`;
      alink.click();
    }

    function ifWhiteHandle(color) {
      const colorWhite = ["#fff", "#ffffff", "#FFF", "#FFFFFF"];
      if (colorWhite.indexOf(color) !== -1) {
        return true;
      } else {
        return "";
      }
    }

    $("#personnelScheduling #filterShiftInput").keyup((e) => {
      const value = $.trim(e.target.value);
      const shiftChildren = $(
        "#personnelScheduling #SchedulingDialog #Shift"
      ).children();
      shiftChildren.each((index, dom) => {
        const shiftName = dom.getAttribute("data-name");
        if (shiftName.indexOf(value) !== -1) {
          $(dom).show();
        } else {
          $(dom).hide();
        }
      });
    });

    function ExistenceShiftRenderList(data) {
      const { color, id, iswhite, name, time } = data;
      let html = "";
      html += `<li 
              class="classes-item ${iswhite ? "white" : ""}" 
              style="background:${color}" 
              data-id="${id}"
              data-name="${name}"
              data-color="${color}"
              data-isWhite="${iswhite}"
              data-time="${time}"
              >
                ${name}
            </li>`;
      return html;
    }

    function splitArray(arr, chunkSize) {
      let result = [];
      for (let i = 0, len = arr.length; i < len; i += chunkSize) {
        result.push(arr.slice(i, i + chunkSize));
      }
      return result;
    }
  };
});
