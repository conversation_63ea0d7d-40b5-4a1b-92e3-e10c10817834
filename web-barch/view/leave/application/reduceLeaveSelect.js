"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect'], function () {

            var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload,
                layer = layui.layer;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['800px', '500px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    var grid = $("#leaveWorkflowTable");
                    var dataurl = common.url + '/ts-leave/leaveRecord/getUserRecordlist';
                    grid.jqGrid({
                        url: dataurl,
                        mtype: "post",
                        datatype: "json",
                        postData: {
                            "fromType":"leaveReduce" //销假列表
                        },
                        // sortname: "a.deptcode",
                        colModel: [
                            {
                                label: 'id',
                                name: 'id',
                                hidden: true
                            },
                            {
                                label: '流程id',
                                name: 'workFlowId',
                                hidden: true
                            },
                            {
                                label: '流程标题',
                                name: 'wfTitle',
                                width: 120,
                                align: 'center',
                                editable: false
                            },
                            {
                                label: '假期类型',
                                name: 'leaveTypeName',
                                align: 'center',
                                width: 120
                            },
                            {
                                label: '当前节点',
                                name: 'wfCurrentPoint',
                                align: 'center',
                                width: 120
                            },
                            {
                                label: '办理状态',
                                name: 'auditStatus',
                                align: 'center',
                                width: 80,
                                formatter: function (cellvalue, options, rowObject) {
                                    var data = {
                                        0: '草稿',
                                        1: '在办',
                                        2: '办结',
                                        3: '强制结束',
                                        4: '撤销',
                                    }
                                    return data[rowObject.auditStatus]
                                }
                            },
                            {
                                label: '销假',
                                name: 'isLeaveConfirm',
                                align: 'center',
                                width: 80,
                                formatter: function (cellvalue, options, rowObject) {
                                    var data = {
                                        N: '未销假',
                                        Y: '已销假',
                                    }
                                    return data[rowObject.isLeaveConfirm]
                                }
                            },
                            {
                                label:'申请时间',
                                name:'leaveStartDate',
                                width:120,
                                align:'center'
                            }
                        ],
                        viewrecords: true,
                        height: "auto",
                        rowNum: 15,
                        rownumbers: true,
                        multiselect: true,
                        forceFit: false,
                        shrinkToFit: true,
                        gridview: true,
                        altRows: true,
                        onSelectRow: function (rowId, status, e) {
                            var lastSel = "";
                            if (rowId == lastSel) {
                                $(this).jqGrid("resetSelection");
                                lastSel = undefined;
                                status = false;
                            } else {
                                lastSel = rowId;
                            }
                        },
                        beforeSelectRow: function(){
                            grid.jqGrid('resetSelection');
                            return(true);
                        },
                        loadComplete: function () {
                            grid.setGridWidth($('#centerDiv').width());
                            grid.jqGrid('setLabel','rn', '序号', {'text-align':'center','vertical-align':'middle'},'');
                        },
                        pager: "#leaveWorkflowPage"
                    });


                    //关闭
                    form.on('submit(closeSelectBtn)', function () {
                        layer.close(layer.index);
                    })

                    // 确认
                    form.on('submit(confirmSelectBtn)', function(data) {
                        var rowid = grid.jqGrid("getGridParam","selrow");
                        var rowData = grid.jqGrid("getRowData",rowid);
                        var workflowNo = "LEAVE_REDUCE_APPPLY";
                        var workFlowId = rowData.workFlowId; //关联流程Id

                        $.ajax({
                            method: 'get',
                            url: '' + '/ts-workflow/workflow/definition/code/' + workflowNo,
                            success: function (res) {
                                if (res.success) {
                                    if(res.object){
                                        var son = window.open('/otherView/processInfo/index.html?workflowNo=' + workflowNo, createUUID(), 'menubar=0,titlebar=0,toolbar=0,width=1300, height=600')
                                        common.openedWindow.push(son);
                                    } else{
                                        layer.msg(res.message || '获取流程信息失败')
                                    }
                                } else {
                                    layer.msg(res.message || '发起失败')
                                }
                            }
                        });
                    });

                }

            });

        })

    };
});