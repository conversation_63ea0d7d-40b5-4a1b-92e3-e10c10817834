define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'laytpl'], function () {
            var form = layui.form,
                laytpl = layui.laytpl,
                trasen = layui.trasen;

            var color = '#d81e06',
                type = 'top',
                ystype = '1', // 元素类型
                imgformdata = false, // 图表数据
                imgformthdata = false, // 图表数据
                imgformi = 1,
                imgformList = ''; // 图表类型列表
            var editor = null; // 编辑器对象
            var urlArr = []; // 数据源地址
            var fastMvConType = 1;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                maxmin: false,
                shadeClose: false,
                shade: 0.2,
                area: ['1200px', '98%'],
                content: html,
                success: function (layero, index) {
                    if (opt.type == 1) {
                        initData();
                    } else {
                        init();
                        imgFormType();
                        menuShowPrew('portalElementLinksMenuInput0');
                    }
                    form.render();
                },
            });

            // 菜单加载
            var allmenu = null;

            // 菜单树渲染
            function menuShowPrew(dom) {
                if (allmenu == null) {
                    var config = tra.config;
                    $.ajax({
                        url: common.url + '/ts-system/api/ThpsAppModel/list',
                        type: 'get',
                        success: function (res) {
                            var syscode = '';
                            for (var i in res.rows) {
                                if (i == 0) {
                                    syscode = res.rows[i].sysCode;
                                } else {
                                    syscode += ',' + res.rows[i].sysCode;
                                }
                            }
                            config.menuinfo.data.syscode = syscode;
                            $.ajax({
                                type: config.menuinfo.type,
                                url: config.menuinfo.url,
                                data: config.menuinfo.data || {},
                                dataType: 'json',
                                success: function (res) {
                                    if (res.success == true) {
                                        allmenu = menuFormatter(res.object || []);
                                        $.trees(dom, {
                                            type: 'local',
                                            data: allmenu,
                                            checkbox: false,
                                            condition: 'name',
                                            zTreeOnClick: function (treeId, node) {
                                                // id  packageName alink
                                                var _dom = $('#' + dom).closest('.portalElementAddlinksRow');
                                                var cslinks = '';
                                                if (treeId != false) {
                                                    var pg = node.packageName ? node.packageName : -1;
                                                    _dom.find('[name="menuid"]').val(node.id);
                                                    _dom.find('[name="sysCode"]').val(node.sysCode);
                                                    _dom.find('[name="packageName"]').val(pg);
                                                    _dom.find('[name="link"]').val(node.alink).attr('readonly', true);
                                                } else {
                                                    _dom.find('[name="menuid"]').val('');
                                                    _dom.find('[name="sysCode"]').val('');
                                                    _dom.find('[name="packageName"]').val('');
                                                    _dom.find('[name="link"]').val('').attr('readonly', false);
                                                }
                                            },
                                        });
                                    }
                                },
                            });
                        },
                    });
                }
            }

            // 菜单数据格式化
            function menuFormatter(data) {
                var arr = [];
                data.forEach(function (v, i) {
                    v.children = v.menus;
                    v.name = v.menuname;
                    if (v.children.length > 0) {
                        v.children = menuFormatter(v.children);
                    } else {
                        v.children = null;
                    }
                    arr.push(v);
                });
                return arr;
            }

            // 初始化
            function init() {
                typelist(); // 元素类型渲染

                // 编译器
                var E = window.wangEditor;
                editor = new E('#myEditor');
                // 或者 var editor = new E( document.getElementById('editor') )
                editor.customConfig.menus = ['head', 'bold', 'italic', 'underline', 'italic', 'strikeThrough', 'foreColor', 'backColor', 'justify'];
                editor.create();

                // 数据源  默认
                articledata(null);
            }

            // 编辑初始化
            function initData() {
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-portal/api/portal/elment/getElmentDetailById',
                    data: {
                        elementid: opt.id,
                    },
                    success: function (res) {
                        if (res.success) {
                            typelist(res.object[0].typeId); // 元素类型渲染
                            $('#portalElementAddYStypeHtml .item').eq(0).hide();
                            $('#portalElementAddYStypeHtml .item')
                                .eq(Number(res.object[0].typeId) - 1)
                                .show();
                            trasen.setNamesVal($('#portalElementAddFORM'), res.object[0]);
                            form.render();
                            var content = res.object[0].cotent;
                            ystype = res.object[0].typeId;
                            signSelect();
                            switch (res.object[0].typeId) {
                                case '1':
                                    // 默认
                                    content = content ? JSON.parse(content) : [];
                                    articledata(content);
                                    break;
                                case '2':
                                    // 文本
                                    // 编译器
                                    var E = window.wangEditor;
                                    editor = new E('#myEditor');
                                    // 或者 var editor = new E( document.getElementById('editor') )
                                    editor.create();
                                    editor.txt.html(content);
                                    break;
                                case '3':
                                    // 图片
                                    ediImg(content);
                                    break;
                                case '4':
                                    // 媒体
                                    ediVideo(content);
                                    break;
                                case '5':
                                    // HTML
                                    break;
                                case '6':
                                    // 报表工具
                                    content = content ? JSON.parse(content) : [];
                                    imgFormType(content);
                                    break;
                                case '7':
                                    // 快捷链接
                                    content = content ? JSON.parse(content) : [];
                                    if (content.set) {
                                        trasen.setNamesVal($('#portalElementAddlinksRowIndex_cspz'), content.set);
                                        // content = content.con;
                                    }
                                    fastLinksShow(content);
                                    break;
                                case '8':
                                    // 多页签
                                    content = content ? JSON.parse(content) : [];
                                    ediPagesShow(content);
                                    break;
                                case '10':
                                    // 方块签
                                    content = content ? JSON.parse(content) : [];
                                    signSelect(content);
                                    break;
                                case '11':
                                    content = JSON.parse(content);
                                    $.each($('[name="portalElementAddCalenderType"]'), function (i, v) {
                                        var type = $(v).val();
                                        if (content.type == type) {
                                            $(v).prop('checked', true);
                                        } else {
                                            $(v).prop('checked', false);
                                        }
                                    });
                                    form.render();
                                    Calender(content.calender);
                                    CalenderDate(content.line);
                                    break;
                            }
                        } else {
                            layer.msg(res.message);
                        }
                    },
                });
            }

            // 数据源
            function datasource(dom, index, value, fn) {
                new $.selectPlug('#' + dom, {
                    url: common.url + '/ts-portal/api/portal/datasource/getdataSourceByName',
                    datatype: 'get', // 请求方式
                    searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: {}, //动态数据时，请求需要提交的参数  searchType为local是，该参数为静态数据
                    localData: {}, // 静态数据
                    textName: 'dsName',
                    valName: 'dsId', 
                    inpTextId: dom + 'name',
                    inpValId: dom + 'val', 
                    inpTextName: dom + 'name',
                    inpValName: dom + 'val', 
                    defaultText: value.name, // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: value.code, // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                        if (res || value.name != '') {
                            if (typeof fn == 'function') {
                                fn(res, index);
                                $('#' + dom + 'val').attr('url', res.colUrl);
                            }
                        }
                    },
                });
            }

            // 数据源表头字段
            function dataThfn(data, fn) {
                var url = common.url + data.colUrl;
                if (!data.colUrl) {
                    return false;
                }
                if (url.indexOf('http://') > -1 || url.indexOf('https://') > -1) {
                    url = data.colUrl;
                }
                url = $.replaceUrl(url);
                $.ajax({
                    type: 'get',
                    url: decodeURIComponent(url || ''),
                    data: {},
                    success: function (res) {
                        if (res) {
                            if (typeof res.rows == 'object' && typeof res.object != 'object') {
                                res.rows = res.rows == null ? [] : res.rows;
                                var d = res.rows.length > 0 ? res.rows[0] : {};
                                var obj = {
                                    btxxlb: [],
                                    sjlb: res.rows,
                                };
                                var arr = Object.keys(d).forEach(function (v, i) {
                                    var o = {
                                        btzd: v,
                                        btzdN: v,
                                    };
                                    obj.btxxlb.push(o);
                                });
                                if (ystype == 10) {
                                    obj = res.rows;
                                }
                                if (typeof fn == 'function') {
                                    fn(obj);
                                }
                            } else if (typeof res.rows != 'object' && typeof res.object == 'object') {
                                var obj = res.object;
                                if (typeof fn == 'function') {
                                    fn(obj);
                                }
                            }
                        } else {
                        }
                    },
                    error: function (rq) {},
                });
            }

            // 下拉框
            function selectAll(elm, data, o, choice) {
                new $.selectPlug('#' + elm, {
                    searchType: 'local', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: data, //动态数据时，请求需要提交的参数  searchType为local是，该参数为静态数据
                    textName: o.name || '', 
                    valName: o.code || '', 
                    inpTextId: elm + 'name', 
                    inpValId: elm + 'val', 
                    inpTextName: o.sname || elm + 'name', 
                    inpValName: o.scode || elm + 'val',
                    choice: choice || false, // 是否多选
                    defaultText: o.nvalue, // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: o.cvalue, // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                        if (res) {
                        }
                    },
                });
            }

            // -------------- 图片\视频 -------------
            // 图片删除
            $(document)
                .off('click', '#portalElementAddImgConclose')
                .on('click', '#portalElementAddImgConclose', function () {
                    var dom = $(this).closest('.portalElementAddlimgBox');
                    var src = dom.find('src').attr('src');
                    dom.remove();
                });

            // 编辑图片渲染
            function ediImg(arr) {
                arr = JSON.parse(arr);
                for (var i in arr) {
                    var h = $('#portalElementAddImg__html').html();
                    laytpl(h).render(arr[i], function (html) {
                        $('#portalElementAddImgLIst').prepend(html);
                    });
                }
            }

            // 编辑视频渲染
            function ediVideo(src) {
                $('#portalElementAddVideoBox').html('<video src="' + src + '" controls="controls" width="300" height="200" id="portalElementAddVideo"></video>');
                $('#portalElementAddVideoCon').val(src);
            }
            // -------------- 图片\视频 end -------------

            // -------------- 文章列表 -------------
            // 文章列表数据源
            function articledata(data) {
                // data = data == null ? {}:data;
                datasource(
                    'portalElementAddAllocation',
                    imgformi,
                    {
                        name: data ? data.dsname : '',
                        code: data ? data.dsid : '',
                    },
                    function (res) {
                        // $('#portalElementAddDeploy').html('');
                        if (res == false) {
                            res = {
                                dsId: data.dsid,
                                colUrl: data.dsurl,
                            };
                            $('#portalElementAddAllocationval').attr('url', data.dsurl);
                        } else {
                            if (data == null) {
                                data = res;
                                data.content = {};
                            }
                        }
                        dataThfn(res, function (rq) {
                            var h = '';
                            var th = rq.btxxlb || [];
                            var orchecked = $('#portalElementAddOrTh').prop('checked') || $('#portalElementAddOrTable').prop('checked');
                            var dis = 'display: none;';
                            var ratherCed = $('#portalElementAddOrRather').prop('checked');
                            var rdis = 'display: none;';
                            // var lumpCed = $('#portalElementAddOrLump').prop('checked');
                            var lumpdis = 'display: none;';
                            var type = $('#portalElementAddClassOR').attr('type');
                            var arr = [];
                            if (data) {
                                var _lt = '';
                                if (data.content.orlist == 0) {
                                    _lt = true;
                                    $('#portalElementAddOrTh').prop('checked', true);
                                } else if (data.content.orlist == 1) {
                                    _lt = false;
                                    $('#portalElementAddOrTh').prop('checked', false);
                                }

                                var _it = '';
                                if (data.content.istable == 0) {
                                    _it = true;
                                    $('#portalElementAddOrTable').prop('checked', true);
                                } else if (data.content.istable == 1) {
                                    _it = false;
                                    $('#portalElementAddOrTable').prop('checked', false);
                                }

                                if (_it == true || _lt == true || (_it == true && _lt == false) || (_it == false && _lt == true)) {
                                    orchecked = true;
                                } else {
                                    orchecked = false;
                                }

                                if (data.content.rather == 0) {
                                    ratherCed = true;
                                    $('#portalElementAddOrRather').prop('checked', true);
                                } else if (data.content.rather == 1) {
                                    ratherCed = false;
                                    $('#portalElementAddOrRather').prop('checked', false);
                                }

                                if (data.content.isSrc == 0) {
                                    $('#portalElementAddOrIsSRC').prop('checked', true);
                                } else {
                                    $('#portalElementAddOrIsSRC').prop('checked', false);
                                }

                                // if(data.content.lump == 0){
                                //     lumpCed = true;
                                //     $('#portalElementAddOrLump').prop('checked', true);
                                // }else if(data.content.lump == 1){
                                //     lumpCed = false;
                                //     $('#portalElementAddOrLump').prop('checked', false);
                                // }

                                // 跳转类型
                                data.content.linkType = data.content.linkType || 1;
                                $.each($('#portalElementAdd__formlinkType input'), function (i, v) {
                                    var val = $(v).val();
                                    if (val == data.content.linkType) {
                                        $(v).prop('checked', true);
                                    }
                                });
                                $('#portalElementAdd-linkType__link .items')
                                    .eq(parseInt(data.content.linkType || 1) - 1)
                                    .show()
                                    .siblings()
                                    .hide();

                                // 列表分类渲染
                                $.each($('#portalElementAddClassOR input'), function (i, v) {
                                    var val = $(v).val();
                                    if (val == data.content.category) {
                                        $(v).prop('checked', true);
                                    }
                                });
                                if (data.content.category == 1 || data.content.category == undefined) {
                                    $('#portalElementAddClassORChild').show();
                                } else {
                                    $('#portalElementAddClassORChild').hide();
                                }

                                arr = data.content.rows || []; // 属性配置项
                            }
                            if (orchecked == true) {
                                dis = '';
                            }
                            if (ratherCed == true) {
                                rdis = '';
                            }
                            for (var i in th) {
                                var o = {};
                                var check = '';
                                if (opt.type == 1) {
                                    for (var l in arr) {
                                        if (th[i].btzd == arr[l].value) {
                                            o = arr[l];
                                            check = 'checked="checked"';
                                        }
                                    }
                                }
                                h +=
                                    '<div class="layui-col-xs6 row mrBox" style="padding-bottom: 10px;">\
                                    <div class="fl" style="padding-top: 5px;">\
                                        <input type="checkbox" name="" class="check" value="' +
                                    th[i].btzd +
                                    '" title="' +
                                    th[i].btzdN +
                                    '" lay-skin="primary" lay-filter="portalElementAddDeploy" ' +
                                    check +
                                    '>\
                                    </div>\
                                    <div class="fl" style="padding-left: 10px; color: #666;">\
                                        排序：<input type="number" class="layui-input number" value="' +
                                    (o.sorts || '') +
                                    '" name="" style="width: 40px !important; text-align: center;display: inline-block;">\
                                    </div>\
                                    <div class="fl widths" style="padding-left: 10px; color: #666;">\
                                        宽度：<input type="number" class="layui-input width" value="' +
                                    (o.width || '') +
                                    '" name="" style="width: 60px !important; text-align: center;display: inline-block;">\
                                    </div>\
                                    <div class="fl namebox" style="padding-left: 10px; color: #666; ' +
                                    dis +
                                    '">\
                                        列名：<input type="text" class="layui-input name" value="' +
                                    (o.name || th[i].btzdN) +
                                    '" name="" style="width: 100px !important;display: inline-block;">\
                                    </div>\
                                    <div class="row"></div>\
                                    <div class="fl" style="width: 200px; padding-left: 70px; color: #666; padding-top: 10px; position: relative;">\
                                        <div style="position: absolute; left: 22px; line-height: 28px;">对齐：</div>\
                                        <div class="tra-lay-select-title" id="portalElementAddAlign">\
                                             <input type="radio" name="align' +
                                    i +
                                    '" class="align" ' +
                                    (!o.align ? 'checked="checked"' : o.align == 'left' ? 'checked="checked"' : '') +
                                    ' lay-skin="primary" title="左" value="left">\
                                             <input type="radio" name="align' +
                                    i +
                                    '" class="align" ' +
                                    (o.align == 'center' ? 'checked="checked"' : '') +
                                    ' lay-skin="primary" title="中" value="center">\
                                             <input type="radio" name="align' +
                                    i +
                                    '" class="align" ' +
                                    (o.align == 'right' ? 'checked="checked"' : '') +
                                    ' lay-skin="primary" title="右" value="right">\
                                        </div>\
                                    </div>\
                                    <div class="fl ratherbox" style="width: 200px; padding-left: 70px; color: #666; ' +
                                    rdis +
                                    ' padding-top: 10px; position: relative;">\
                                        <div style="position: absolute; left: 22px; line-height: 28px;">对比列：</div><div class="tra-lay-select-title" id="portalElementAddOrRatherSlecct' +
                                    i +
                                    '"></div>\
                                    </div>\
                                </div>';
                            }
                            trasen.setNamesVal($('#portalElementAdd__formBox'), data.content);
                            $('#portalElementAddDeploy').html(h);
                            $('#portalElementAdd-form__urls').val(data.content.urls || '');
                            portalElementAddClass(data.content.category || 1);
                            $('#portalElementAdd__formdetailUrl').val(data.content.detailUrl || '');

                            if (data.content.isSrc == 0) {
                                $('#portalElementAddDeploy__scroll').show();
                            }

                            // 下拉框
                            // 地址参数
                            selectAll(
                                'portalElementAdd-form__argument',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.argument || '',
                                    cvalue: data.content.argument || '',
                                },
                                true
                            );

                            // 详情查询条件
                            selectAll(
                                'portalElementAdd-linkType__detailArgument',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.detailArgument || '',
                                    cvalue: data.content.detailArgument || '',
                                    sname: '',
                                    scode: 'detailArgument',
                                },
                                true
                            );

                            // 详情标题
                            selectAll(
                                'portalElementAdd-linkType__detailTitle',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.detailTitle || '',
                                    cvalue: data.content.detailTitle || '',
                                    sname: '',
                                    scode: 'detailTitle',
                                },
                                false
                            );

                            // 详情详细内容
                            selectAll(
                                'portalElementAdd-linkType__detailCon',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.detailCon || '',
                                    cvalue: data.content.detailCon || '',
                                    sname: '',
                                    scode: 'detailCon',
                                },
                                false
                            );

                            // 详情发布人
                            selectAll(
                                'portalElementAdd-linkType__detailUeser',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.detailUeser || '',
                                    cvalue: data.content.detailUeser || '',
                                    sname: '',
                                    scode: 'detailUeser',
                                },
                                false
                            );

                            // 详情部门
                            selectAll(
                                'portalElementAdd-linkType__detailDep',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.detailDep || '',
                                    cvalue: data.content.detailDep || '',
                                    sname: '',
                                    scode: 'detailDep',
                                },
                                false
                            );

                            // 详情发布时间
                            selectAll(
                                'portalElementAdd-linkType__detailDate',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.detailDate || '',
                                    cvalue: data.content.detailDate || '',
                                    sname: '',
                                    scode: 'detailDate',
                                },
                                false
                            );

                            /*-- 简介列表属性选择价值 -- */
                            // 类别名称
                            selectAll(
                                'portalElementAdd-intro__name',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.cgy || '',
                                    cvalue: data.content.cgy || '',
                                },
                                false
                            );

                            // 标题
                            selectAll(
                                'portalElementAdd-intro__title',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.title || '',
                                    cvalue: data.content.title || '',
                                },
                                true
                            );

                            // 简介内容
                            selectAll(
                                'portalElementAdd-intro__con',
                                th,
                                {
                                    name: 'btzd',
                                    code: 'btzdN',
                                    nvalue: data.content.con || '',
                                    cvalue: data.content.con || '',
                                },
                                true
                            );
                            // 下拉框

                            for (var i in th) {
                                var o = {};
                                if (opt.type == 1) {
                                    for (var l in arr) {
                                        if (th[i].btzd == arr[l].value) {
                                            o = arr[l];
                                            break;
                                        }
                                    }
                                }
                                OrRatherSlecct('portalElementAddOrRatherSlecct' + i, th, {
                                    name: o.rather || '',
                                    code: o.rather || '',
                                });
                            }
                            // portalElementAdd__formdetailUrl
                            $('#portalElementAdd__formBox').show();
                            form.render();
                        });
                    }
                );
            }
            // 对比选择
            function OrRatherSlecct(dom, data, value) {
                new $.selectPlug('#' + dom, {
                    url: common.url + '/ts-portal/api/portal/datasource/getdataSourceByName',
                    datatype: 'get', // 请求方式
                    searchType: 'local', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: data, //动态数据时，请求需要提交的参数  searchType为local是，该参数为静态数据
                    localData: data, // 静态数据
                    textName: 'btzdN', 
                    valName: 'btzd', 
                    inpTextId: dom + 'name', 
                    inpValId: 'rather', 
                    inpTextName: dom + 'name', 
                    inpValName: dom + 'val', 
                    defaultText: value.name || '', // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: value.code || '', // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                    },
                });
            }

            // 列表分类选择
            form.on('radio(portalElementAddClassOR)', function (data) {
                var value = data.value;
                portalElementAddClass(value);
            });

            // 列表分类选择
            function portalElementAddClass(value) {
                $('#portalElementAddClassOR').attr('type', value);
                if (value == 1) {
                    $('#portalElementAddClassORChild, #portalElementAddDeploy__tips, #portalElementAddDeploy .widths').show();
                } else {
                    $('#portalElementAddClassORChild, #portalElementAddDeploy__tips, #portalElementAddDeploy .widths').hide();
                }
                if (value == 2) {
                    $('#portalElementAddDeploy__kzlist').show();
                } else {
                    $('#portalElementAddDeploy__kzlist').hide();
                }
                // 简介列表
                if (value == 3 || value == 4) {
                    $('#portalElementAddDeploy').hide();
                    $('#portalElementAdd-intro__box').show();
                } else {
                    $('#portalElementAddDeploy').show();
                    $('#portalElementAdd-intro__box').hide();
                }
            }

            // 滚动选择
            form.on('checkbox(portalElementAddOrIsSRC)', function (data, a, b, c) {
                var value = data.value;
                var bool = $('#portalElementAddOrIsSRC').prop('checked');
                if (bool == true) {
                    $('#portalElementAddDeploy__scroll').show();
                } else {
                    $('#portalElementAddDeploy__scroll').hide();
                }
            });

            // 跳转类型选择
            form.on('radio(portalElementAdd__linkTyperadio)', function (data) {
                var value = data.value;
                $('#portalElementAdd-linkType__link .items')
                    .eq(parseInt(value) - 1)
                    .show()
                    .siblings()
                    .hide();
            });

            // 列表是否开启列名
            form.on('checkbox(portalElementAddOrTh)', function (data) {
                var elm = data.elem;
                orThFun(elm, 'portalElementAddOrTable');
            });

            // 是否表格
            form.on('checkbox(portalElementAddOrTable)', function (data) {
                var elm = data.elem;
                var value = $(elm).prop('checked');
                orThFun(elm, 'portalElementAddOrTh');
            });
            function orThFun(elm, id) {
                var value = $(elm).prop('checked');
                var _val = $('#' + id).prop('checked');
                if (value == true || _val == true) {
                    $('#portalElementAddDeploy .namebox').show();
                } else {
                    $('#portalElementAddDeploy .namebox').hide();
                }
            }

            // 列表是否启用对比列
            form.on('checkbox(portalElementAddOrRather)', function (data) {
                var elm = data.elem;
                var value = $(elm).prop('checked');
                if (value == true) {
                    $('#portalElementAddDeploy .ratherbox').show();
                } else {
                    $('#portalElementAddDeploy .ratherbox').hide();
                }
            });
            // -------------- 文章列表 end -------------

            // -------------- 多页签 -------------
            // 多页签添加
            var pglen = 1;
            $(document)
                .off('click', '#portalElementAddMorePage_addbtn')
                .on('click', '#portalElementAddMorePage_addbtn', function () {
                    var h = $('#portalElementAddMorePagehtml').html();
                    var len = $('#portalElementAddMorePageBox .portalElementAddMorePageRow').length;
                    pglen = pglen > len ? pglen : len;
                    $('#portalElementAddMorePageBox').append(h);
                    $('#portalElementAddMorePage_content').attr('id', 'portalElementAddMorePage_content' + pglen);
                    morePageSelect('portalElementAddMorePage_content' + pglen);
                    pglen++;
                });

            // 多页签删除
            $(document)
                .off('click', '#portalElementAddMorePage_delbtn')
                .on('click', '#portalElementAddMorePage_delbtn', function () {
                    $(this).closest('.portalElementAddMorePageRow').remove();
                });
            morePageSelect('portalElementAddMorePage_content0');

            // 多页签下拉框
            function morePageSelect(dom, name, id) {
                new $.selectPlug('#' + dom, {
                    url: common.url + '/ts-portal/api/portal/Aftintemplate/getAftintemplateListByname',
                    datatype: 'get', // 请求方式
                    searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: {}, //动态数据时，请求需要提交的参数  searchType为local是，该参数为静态数据
                    localData: {}, // 静态数据
                    textName: 'templateName', 
                    valName: 'templateId', 
                    inpTextId: dom + 'name', 
                    inpValId: dom + 'val', 
                    inpTextName: 'morePageName', 
                    inpValName: 'morePageId', 
                    defaultText: name || '', // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: id || '', // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                    },
                });
            }

            // 多页签渲染
            function ediPagesShow(content) {
                $('#portalElementAddMorePageBox').html('');
                content.forEach(function (v, i) {
                    var h = $('#portalElementAddMorePagehtml').html();
                    $('#portalElementAddMorePageBox').append(h);
                    var elem = $('#portalElementAddMorePageBox .portalElementAddMorePageRow').eq(i);
                    elem.find('[name="name"]').val(v.name);
                    $('#portalElementAddMorePage_content').attr('id', 'portalElementAddMorePage_content' + i);
                    morePageSelect('portalElementAddMorePage_content' + i, v.text, v.id);
                });
            }
            // -------------- 多页签 end -------------

            // -------------- 图表 -------------

            // 编辑图表渲染
            function ediImgFormshow(arr) {
                for (var i in arr) {
                    imgformi = Number(i) + 1;
                    addIMGform(imgformi, arr[i], function (_i) {
                        TBtypeSelSmall(_i, arr[i].type);
                    });

                    $('#portalElementAddTBtitle' + imgformi).val(arr[i].title), // 名称
                        // $('#portalElementAddTBsource' + imgformi + 'name').val(arr[i].dsidname), // 数据源name
                        // $('#portalElementAddTBsource' + imgformi + 'val').val(arr[i].dsid), // 数据源id
                        // $('#portalElementAddTBacross' + imgformi + 'name').val(arr[i].xn), // 横坐标name
                        // $('#portalElementAddTBacross' + imgformi + 'val').val(arr[i].x), // 横坐标code
                        // $('#portalElementAddTBendlong' + imgformi + 'name').val(arr[i].yn), // 纵坐标name
                        // $('#portalElementAddTBendlong' + imgformi + 'val').val(arr[i].y), // 纵坐标code
                        $('[lay-filter="portalElementAddTBtypeSel' + imgformi + '"]').val(arr[i].type), // 图类型
                        $('#portalElementAddTBtitleShow' + imgformi).html(arr[i].title);
                    form.render();
                }
            }

            // 图表类型
            function imgFormType(d) {
                $.ajax({
                    url: common.url + '/ts-portal/api/portal/chart/getAllChartType',
                    type: 'get',
                    data: {},
                    async: false,
                    success: function (res) {
                        if (res.success) {
                            // imgformList
                            var arr = res.object;
                            imgformList = '<select lay-search lay-filter="portalElementAddTBtypeSel"><option>请选择</option>';
                            for (var i in arr) {
                                imgformList += '<option value="' + arr[i].chartTypeid + '">' + arr[i].chartTypename + '</option>';
                            }
                            imgformList += '</select>';
                            if (d != null && typeof d == 'object') {
                                ediImgFormshow(d);
                            }
                        } else {
                        }
                    },
                    error: function (res) {},
                });
            }

            // 添加图表
            function addIMGform(_index, data, fns) {
                var html = $('#portalElementAddTBCONboxScript').html();
                $('#portalElementAddTBCONbox').append(html);
                $('#portalElementAddTBtitle').attr('id', 'portalElementAddTBtitle' + _index);
                $('#portalElementAddTBsource').attr('id', 'portalElementAddTBsource' + _index);
                $('#portalElementAddTBacross').attr('id', 'portalElementAddTBacross' + _index);
                $('#portalElementAddTBendlong').attr('id', 'portalElementAddTBendlong' + _index);
                $('#portalElementAddTBRimg').attr('id', 'portalElementAddTBRimg' + _index);
                $('#portalElementAddTBtitleShow').attr('id', 'portalElementAddTBtitleShow' + _index);
                $('#portalElementAddTBRimgFormBox').attr('id', 'portalElementAddTBRimgFormBox' + _index);
                $('#portalElementAddTBRimgForm').attr('id', 'portalElementAddTBRimgForm' + _index);
                // 图表类型
                $('#portalElementAddTBtypeSel').html(imgformList);
                $('#portalElementAddTBtypeSel select').attr('lay-filter', 'portalElementAddTBtypeSel' + _index);
                $('#portalElementAddTBtypeSel').attr('id', 'portalElementAddTBtypeSel' + _index);
                form.render();
                TBtypeSel(_index);
                // 数据源
                datasource(
                    'portalElementAddTBsource' + _index,
                    _index,
                    {
                        name: data ? data.dsidname : '',
                        code: data ? data.dsid : '',
                    },
                    function (res, index) {
                        if (res == false && data) {
                            res = {
                                dsId: data.dsid,
                                colUrl: data.dsurl,
                            };
                        }
                        dataThfn(res, function (rq) {
                            imgformdata = rq.sjlb;
                            imgformthdata = rq.btxxlb;
                            // 横
                            coordxy(
                                'portalElementAddTBacross' + index,
                                index,
                                rq.btxxlb,
                                false,
                                {
                                    name: data ? data.xn : '',
                                    code: data ? data.x : '',
                                },
                                function (req) {
                                    // imgformdata
                                    tbs(index);
                                }
                            );
                            // 纵
                            coordxy(
                                'portalElementAddTBendlong' + index,
                                index,
                                rq.btxxlb,
                                true,
                                {
                                    name: data ? data.yn : '',
                                    code: data ? data.y : '',
                                },
                                function (req) {
                                    tbs(index);
                                }
                            );
                            if (opt.type == 1 && typeof fns == 'function') {
                                fns(_index);
                            }
                        });
                    }
                );
            }

            // 横纵坐标下拉框
            function coordxy(dom, index, data, choice, value, fn) {
                new $.selectPlug('#' + dom, {
                    searchType: 'local', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: data, //动态数据时，请求需要提交的参数  searchType为local是，该参数为静态数据
                    localData: {}, // 静态数据
                    textName: 'btzdN', // 选项文字的key（接口返回数据里面的参数）
                    valName: 'btzd', // 选项id的key（接口返回数据里面的参数）
                    inpTextId: dom + 'name', // 需要提交的已选文本的输入框的id属性值
                    inpValId: dom + 'val', // 需要提交的已选值的输入框的id属性值
                    inpTextName: dom + 'name', // 需要提交的已选文本的输入框的name属性值
                    inpValName: dom + 'val', // 需要提交的已选值的输入框的name属性值
                    defaultText: value.name, // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: value.code, // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    choice: choice, // 是否多选
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                        if (res) {
                            if (typeof fn == 'function') {
                                fn(res);
                            }
                        }
                    },
                });
            }

            // 图表small img 渲染
            function TBtypeSelSmall(index, v) {
                $.ajax({
                    url: common.url + '/ts-portal/api/portal/chart/getAllChartListByTypeId',
                    type: 'get',
                    data: {
                        chartTypeid: v,
                    },
                    async: false,
                    success: function (res) {
                        if (res.success) {
                            var arr = res.object;
                            var h = '';
                            for (var i in arr) {
                                var n = '';
                                if (i == 0) {
                                    n = 'active';
                                }
                                h +=
                                    '<div class="layui-col-xs3 img ' +
                                    n +
                                    '" state="' +
                                    arr[i].chartId +
                                    '">\
                                        <img src="' +
                                    arr[i].chartImage +
                                    '" title="' +
                                    arr[i].chartName +
                                    '">\
                                    </div>';
                            }
                            $('#portalElementAddTBRimg' + index).html(h);
                            tbs(index);
                        } else {
                        }
                    },
                    error: function (res) {},
                });
            }

            // 图表类型选择
            function TBtypeSel(index) {
                form.on('select(portalElementAddTBtypeSel' + index + ')', function (data) {
                    var v = data.value;
                    TBtypeSelSmall(index, v);
                });

                // 图切换
                $('#portalElementAddTBRimg' + index + ' .img').funs('click', function () {
                    var state = $(this).attr('state');
                    $(this).addClass('active').siblings().removeClass('active');
                    tbs(index);
                });

                // 图表标题
                $(document).on('input', '#portalElementAddTBtitle' + index, function () {
                    var v = $(this).val();
                    $('#portalElementAddTBtitleShow' + index).html(v);
                });
            }

            // 图表添加事件
            $(document)
                .off('click', '#portalElementAddTBCONboxBtn')
                .on('click', '#portalElementAddTBCONboxBtn', function () {
                    imgformi++;
                    addIMGform(imgformi);
                });

            // 图表删除
            $(document)
                .off('click', '#portalElementAddTBCONboxMod_delbtn')
                .on('click', '#portalElementAddTBCONboxMod_delbtn', function () {
                    $(this).closest('.portalElementAddTBCONboxMod__box').remove();
                });

            // 图表渲染
            function tbs(index) {
                var state = $('#portalElementAddTBRimg' + index + ' .active').attr('state');
                var xn = '#portalElementAddTBacross' + index + 'name';
                var x = $(xn).val(); // 横坐标名字
                var xv = '#portalElementAddTBacross' + index + 'val';
                var _x = $(xv).val();
                var yn = '#portalElementAddTBendlong' + index + 'name';
                var y = $(yn).val(); // 纵坐标名字
                var yv = '#portalElementAddTBendlong' + index + 'val';
                var _y = $(yv).val();
                if (x == '' || y == '' || x == undefined || y == undefined || state == undefined) {
                    return false;
                }
                y = y.split(',');
                _y = _y.split(',');
                var data = [];
                for (var i in imgformdata) {
                    for (var l in y) {
                        var d = {
                            category: imgformdata[i][_x],
                        };
                        d.column = y[l];
                        d.value = imgformdata[i][_y[l]];
                        data.push(d);
                    }
                }
                state = parseInt(state);
                $('#portalElementAddTBRimgFormBox' + index).html('<div id="portalElementAddTBRimgForm' + index + '"></div>');
                $('#portalElementAddTBRimgFormBox' + index).show();
                setChart('portalElementAddTBRimgForm' + index, data, state, {
                    height: 200,
                });
            }

            // 元素类型渲染
            function typelist(type) {
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-portal/api/portal/elment/getElementTypeByName',
                    success: function (res) {
                        if (res.success) {
                            var arr = res.object;
                            var h = '';
                            for (var i in arr) {
                                var c = '';
                                if (arr[i].typeId == type || (!type && i == 0)) {
                                    c = 'selected="selected"';
                                    $('#portalElementAddYStypeName').val(arr[i].typeName);
                                }
                                if (arr[i].typeId != 5) {
                                    h += '<option value="' + arr[i].typeId + '" ' + c + '>' + arr[i].typeName + '</option>';
                                }
                            }
                            $('#portalElementAddYStype').html(h);
                            form.render();
                        } else {
                        }
                    },
                    error: function (res) {},
                });
            }

            // 图片修改
            $(document)
                .off('click', '.portalElementAddlimg')
                .on('click', '.portalElementAddlimg', function () {
                    var i = $(this).closest('.portalElementAddlimgBox').index();
                    $('#portalElementAddUploadFile').attr('state', i).trigger('click');
                });

            // 文件上传
            $('#portalElementAddUploadFile').funs('input', function () {
                var state = $(this).attr('state');
                var url = '/ts-portal/api/portal/elementtype/fileUpload';
                var dom = document.querySelector('#portalElementAddUploadFile').files[0];
                var data = new FormData();
                data.append('file', dom);
                if (ystype == 4) {
                    url = '/ts-portal/api/portal/elementtype/meidiaUpload';
                }
                $(this).attr('state', -1);
                $.ajax({
                    type: 'post',
                    url: common.url + url,
                    data: data,
                    processData: false,
                    contentType: false,
                    xhr: function () {
                        myXhr = $.ajaxSettings.xhr();
                        if (myXhr.upload) {
                            // check if upload property exists
                            myXhr.upload.addEventListener(
                                'progress',
                                function (e) {
                                    var loaded = e.loaded; //已经上传大小情况
                                    var tot = e.total; //附件总大小
                                    var per = Math.floor((100 * loaded) / tot); //已经上传的百分比
                                    // $("#son").html( per +"%" );
                                    $('#uploadprogress').css('width', per + '%');
                                },
                                false
                            ); // for handling the progress of the upload
                        }
                        return myXhr;
                    },
                    success: function (res) {
                        $('#uploadprogress').css('width', 0);
                        if (res.success) {
                            var src = res.object.url;
                            if (ystype == 4) {
                                // 媒体
                                $('#portalElementAddVideoBox').html('<video src="' + src + '" controls="controls" width="300" height="200" id="portalElementAddVideo"></video>');
                                $('#portalElementAddVideoCon').val(src);
                            } else if (ystype == 3) {
                                // 图片
                                // var h = '<div class="portalElementAddlimg"><img src="' + src + '" width="100" height="100"></div>';
                                if (state == -1) {
                                    var h = $('#portalElementAddImg__html').html();
                                    _d = {
                                        src: src,
                                        title: '',
                                    };
                                    laytpl(h).render(_d, function (html) {
                                        var v = $('#portalElementAddImgCon').val();
                                        $('#portalElementAddImgLIst').prepend(html);
                                        if (v == '') {
                                            v = src;
                                        } else {
                                            v += ',' + src;
                                        }
                                        $('#portalElementAddImgCon').val(v);
                                    });
                                } else {
                                    $('#portalElementAddImgLIst .portalElementAddlimgBox')
                                        .eq(state)
                                        .find('.portalElementAddlimg')
                                        .css('background-image', 'url(' + src + ')');
                                    $('#portalElementAddImgLIst .portalElementAddlimgBox').eq(state).find('.portalElementAddlimg img').attr('src', src);
                                }
                            } else if (ystype == 7) {
                                // 快捷链接
                                var index = $('#portalElementAddlinksRowIndex').val();
                                var h = '<i class="img" style="background-image: url(' + src + ');"></i>';
                                var dom = $('#portalElementAddLinksBox .portalElementAddlinksRow').eq(index);
                                dom.find('[id="portalElementLinksIcon"]').html(h);
                                dom.find('[id="portalElementLinksIconSrc"]').val(src);
                                dom.find('[id="portalElementLinksIconSrcType"]').val(0); // 0; 上传图片 1: 默认图标
                            }
                        } else {
                            layer.msg('文件上传失败!');
                        }
                    },
                    error: function (res) {},
                });
            });
            // -------------- 图表 end -------------

            // -------------- 快捷链接配置 -------------
            // 打开图标选择层
            $(document)
                .off('click', '#portalElementLinksIconSelect')
                .on('click', '#portalElementLinksIconSelect', function () {
                    var dom = $(this).closest('.portalElementAddlinksRow').find('.portalElementAddIconListBox');
                    var height = dom.height();
                    var h = $('#portalElementAddIconListHtml').html();
                    if (height == 0) {
                        dom.html(h);
                        var _h = dom.find('.portalElementAddIconList').height();
                        dom.height(_h + 17);
                    } else {
                        dom.height(0);
                        setTimeout(function () {
                            dom.html('');
                        }, 300);
                    }
                });

            // 图标选择
            $(document)
                .off('click', '#portalElementAddIconList .item')
                .on('click', '#portalElementAddIconList .item', function () {
                    var c = $(this).find('i').attr('class');
                    var h = '<i class="' + c + '" aria-hidden="true"></i>';
                    var dom = $(this).closest('.portalElementAddlinksRow');
                    dom.find('[id="portalElementLinksIcon"]').html(h);
                    dom.find('[id="portalElementLinksIconSrc"]').val(c);
                    dom.find('[id="portalElementLinksIconSrcType"]').val(1); // 0; 上传图片 1: 默认图标
                });

            // 图标上传
            $(document)
                .off('click', '#portalElementLinksIconUpload')
                .on('click', '#portalElementLinksIconUpload', function () {
                    var i = $(this).closest('.portalElementAddlinksRow').index();
                    $('#portalElementAddlinksRowIndex').val(i);
                    $('#portalElementAddUploadFile').trigger('click');
                });

            // 快捷链接添加
            var fastlen = 1;
            $(document)
                .off('click', '#portalElementAddLinks_addbtn')
                .on('click', '#portalElementAddLinks_addbtn', function () {
                    var h = $('#portalElementAddFastLinkstHtml').html();
                    var len = $('#portalElementAddLinksBox .portalElementAddlinksRow').length;
                    fastlen = fastlen > len ? fastlen : len;
                    $('#portalElementAddLinksBox').append(h);
                    $('#portalElementLinksMenuInput').attr('id', 'portalElementLinksMenuInput' + fastlen);
                    $('#portalElementLinksMoveTitleBox').attr('id', 'portalElementLinksMoveTitleBox' + fastlen);
                    fastSJYs('portalElementLinksMoveTitleBox' + fastlen, fastlen, {}); // 数据源
                    menuShowPrew('portalElementLinksMenuInput' + fastlen);
                    fastShowType(fastMvConType);
                    fastColorInit(fastlen, '333333', 'f7f7f7'); // 颜色
                    fastlen++;
                });

            // 快捷链接删除
            $(document)
                .off('click', '#portalElementAddMoreLinks_delbtn')
                .on('click', '#portalElementAddMoreLinks_delbtn', function () {
                    $(this).closest('.portalElementAddlinksRow').remove();
                });

            // 类型选择
            form.on('radio(portalElementAddlinksMVCON__radio)', function (data) {
                fastMvConType = data.value;
                fastShowType(fastMvConType);
            });

            // 快捷链接渲染
            function fastLinksShow(content) {
                var con = content.con;
                var set = content.set;
                fastMvConType = set.f_type;
                $('#portalElementAddLinksBox').html('');
                $.each($('[lay-filter="portalElementAddlinksMVCON__radio"]'), function (i, v) {
                    var state = $(v).val();
                    if (state == fastMvConType) {
                        $(v).prop('checked', true);
                    } else {
                        $(v).prop('checked', false);
                    }
                });
                for (var i in con) {
                    var v = con[i];
                    var h = $('#portalElementAddFastLinkstHtml').html();
                    $('#portalElementAddLinksBox').append(h);
                    $('#portalElementLinksMenuInput').attr('id', 'portalElementLinksMenuInput' + i);
                    var dom = $('#portalElementAddLinksBox .portalElementAddlinksRow').eq(i);
                    if (v.type == 1) {
                        dom.find('.portalElementLinksIcon').html('<i class="' + v.src + '" aria-hidden="true"></i>');
                    } else {
                        dom.find('.portalElementLinksIcon').html('<i class="img" style="background-image: url(' + v.src + ');"></i>');
                    }
                    if (i == 0) {
                        $('#portalElementAddLinksRownumber').val(v.number);
                    }
                    trasen.setNamesVal(dom, v);
                    menuShowPrew('portalElementLinksMenuInput' + i);
                    $('#portalElementLinksMoveTitleBox').attr('id', 'portalElementLinksMoveTitleBox' + i);
                    fastSJYs('portalElementLinksMoveTitleBox' + i, i, {
                        name: v.mvsjyname,
                        code: v.mvsjycode,
                    }); // 数据源
                    fastShowType(set.f_type);
                    fastColorInit(i, v.fontcolor, v.bgcolor); // 颜色
                }
                form.render();
            }

            // 显示类型
            function fastShowType(type) {
                if (type == 1) {
                    $('.portalElementAddlinksIcon__select').css('display', 'flex');
                    $('.portalElementAddlinksMVCON__select').css('display', 'none');
                } else {
                    $('.portalElementAddlinksIcon__select').css('display', 'none');
                    $('.portalElementAddlinksMVCON__select').css('display', 'flex');
                }
            }

            // 颜色初始化
            function fastColorInit(index, fnc, bgc) {
                $('#portalElementAddlinksMVCON__bg').attr('id', 'portalElementAddlinksMVCON__bg' + index);
                $('#portalElementAddlinksMVCON__font').attr('id', 'portalElementAddlinksMVCON__font' + index);
                $('#portalElementAddlinksMVCON__bg' + index + ' .bg').css('background-color', '#' + bgc);
                $('#portalElementAddlinksMVCON__font' + index + ' .bg').css('background-color', '#' + fnc);
                colorFn('portalElementAddlinksMVCON__bg' + index, bgc);
                colorFn('portalElementAddlinksMVCON__font' + index, fnc);
            }

            // 数据源渲染
            function fastSJYs(elm, index, o) {
                datasource(elm, index, o, function (res) {
                    if (res) {
                        var dm = $('#' + elm).closest('.portalElementAddlinksMVCON__select');
                        dm.find('[name="mvsjycode"]').val(res.dsId);
                        dm.find('[name="mvsjyname"]').val(res.dsName);
                        dm.find('[name="mvsjyurl"]').val(res.colUrl);
                    }
                });
            }
            // -------------- 快捷链接配置 end -------------

            // -------------- 方块签 -------------
            if (opt.type != 1) {
                signSelect();
            }

            // 渲染配置
            function signShow(data, arr) {
                data = data || [];
                $('#portalElementAddsignBox').html('');
                data.forEach(function (v, i) {
                    var h = $('#portalElementAddFastsignHtml').html();
                    $('#portalElementAddsignBox').append(h);
                    var fontc = $('#portalelementSetSelectSignFont .text').val();
                    var bgc = $('#portalelementSetSelectSignBg .text').val();
                    if (arr.length > 0) {
                        arr[i].name = v.name;
                        fontc = arr[i].fontcolor;
                        bgc = arr[i].bgcolor;
                    }

                    $('#portalelementSetSelectSignFont').attr('id', 'portalelementSetSelectSignFont' + i);
                    $('#portalelementSetSelectSignBg').attr('id', 'portalelementSetSelectSignBg' + i);
                    colorFn('portalelementSetSelectSignFont' + i, fontc);
                    colorFn('portalelementSetSelectSignBg' + i, bgc);
                    var dom = $('#portalElementAddsignBox .portalElementAddsignRow').eq(i);
                    var d = $.extend(true, v, arr[i]);
                    d.fontcolor = d.fontcolor || '333';
                    d.bgcolor = d.bgcolor || 'f7f7f7';
                    trasen.setNamesVal(dom, d);
                    $('#portalelementSetSelectSignFont' + i + ' .bg').css('background-color', '#' + d.fontcolor);
                    $('#portalelementSetSelectSignBg' + i + ' .bg').css('background-color', '#' + d.bgcolor);
                });
            }

            // 下拉框
            function signSelect(data) {
                datasource(
                    'portalElementAddAllocationSign',
                    '',
                    {
                        name: data ? data.dsname : '',
                        code: data ? data.dsid : '',
                    },
                    function (res) {
                        if (res == false) {
                            res = {
                                colUrl: data.dsurl,
                            };
                            $('#portalElementAddAllocationSignval').attr('url', data.dsurl);
                        }
                        var arr = null;
                        if (data) {
                            arr = data.arr;
                        }
                        dataThfn(res, function (rq) {
                            arr = arr == null ? rq : arr;
                            signShow(rq, arr);
                        });
                    }
                );
            }
            // -------------- 方块签 end -------------

            // -------------- 日程 -------------
            // 日历
            function Calender(data) {
                datasource(
                    'portalElementAddCalender',
                    -1,
                    {
                        name: data ? data.dsname : '',
                        code: data ? data.dsid : '',
                    },
                    function (res) {
                        if (res == false) {
                            res = {
                                colUrl: data.dsurl,
                            };
                            $('#portalElementAddCalenderval').attr('url', data.dsurl);
                        }
                        dataThfn(res, function (res) {
                            if (typeof res == 'object' && res.length > 0) {
                                var arr = [];
                                Object.keys(res[0]).forEach(function (v, i) {
                                    var d = {
                                        name: v,
                                        code: v,
                                    };
                                    arr.push(d);
                                });
                                selectAll('portalElementAddCalenderTime', arr, {
                                    name: 'name',
                                    code: 'code',
                                    nvalue: data ? data.timeName : '',
                                    cvalue: data ? data.timeName : '',
                                });
                            }
                        });
                    }
                );
            }
            // 时间线
            function CalenderDate(data) {
                datasource(
                    'portalElementAddCalenderLine',
                    -1,
                    {
                        name: data ? data.dsname : '',
                        code: data ? data.dsid : '',
                    },
                    function (res) {
                        if (res == false) {
                            res = {
                                colUrl: data.dsurl,
                            };
                            $('#portalElementAddCalenderLineval').attr('url', data.dsurl);
                        }
                        dataThfn(res, function (res) {
                            if (typeof res == 'object' && res.length > 0) {
                                var arr = [];
                                var html = '';
                                html += '<div class="layui-col-xs12 zdm">';
                                html += '<div class="layui-col-xs12" style="padding: 5px 0;">内容显示字段</div>';
                                html += '<div class="layui-col-xs12">';
                                Object.keys(res[0]).forEach(function (v, i) {
                                    var ck = '';
                                    for (var l in data.arr) {
                                        if (v == data.arr[l].name) {
                                            ck = 'checked="checked"';
                                            break;
                                        }
                                    }
                                    html += '<div class="fl row" style="padding-right: 20px; padding-bottom: 5px;">';
                                    html += '<div class="fl" style="padding-top: 5px;">';
                                    html += '<input type="checkbox" name="" class="check" value="' + v + '" title="' + v + '" lay-skin="primary" lay-filter="portalElementAddCalenderLine" ' + ck + '>';
                                    html += '</div>';
                                    html += '<div class="fl" style="padding-left: 10px; color: #666;">';
                                    html += '排序：<input type="number" class="layui-input number" value="" name="" style="width: 40px !important; text-align: center;display: inline-block;">';
                                    html += '</div>';
                                    html += '</div>';
                                    var d = {
                                        name: v,
                                        code: v,
                                    };
                                    arr.push(d);
                                });
                                html += '</div>';
                                html += '</div>';
                                $('#portalElementAddCalenderLineBox').html(html);
                                form.render();

                                selectAll('portalElementAddCalenderLineTime', arr, {
                                    name: 'name',
                                    code: 'code',
                                    nvalue: data ? data.timeName : '',
                                    cvalue: data ? data.timeName : '',
                                });
                            }
                        });
                    }
                );
            }
            if (opt.type != 1) {
                Calender();
                CalenderDate({});
            }

            // 日程类型选择
            form.on('radio(portalElementAddCalenderType)', function (data) {
                $('#portalElementAddCalenderTypeInput').val(data.value);
            });

            // -------------- 日程 end -------------

            // -------------- 保存数据获取 -------------
            // 列表取数据
            function mrCheckbox() {
                var arr = [];
                $.each($('#portalElementAddDeploy .check'), function (i, v) {
                    var n = $(v).prop('checked');
                    var $box = $(v).closest('.mrBox');
                    if (n === true) {
                        var d = {};
                        d.value = $(v).val();
                        d.sorts = $box.find('.number').val();
                        d.width = $box.find('.width').val();
                        d.name = $box.find('.name').val();
                        d.rather = $box.find('[id="rather"]').val();
                        $.each($box.find('.align'), function (v, i) {
                            var is = $(this).prop('checked');
                            if (is == true) {
                                d.align = $(this).val();
                            }
                        });
                        arr.push(d);
                    }
                });
                arr = arr.sort(function (a, b) {
                    return a.sorts - b.sorts;
                });
                var d = {};
                d.dsid = $('#portalElementAddAllocationval').val();
                d.dsname = $('#portalElementAddAllocationname').val();
                (d.dsurl = $('#portalElementAddAllocationval').attr('url')), // 数据源地址
                    urlArr.push(d.dsurl);
                d.content = {};
                d.content.orlist = $('#portalElementAddOrTh').prop('checked'); // 是否启用表头
                d.content.orlist = d.content.orlist == true ? 0 : 1; // 0：启用  1:不启用
                d.content.istable = $('#portalElementAddOrTable').prop('checked'); // 是否表格
                d.content.istable = d.content.istable == true ? 0 : 1; // 0：启用  1:不启用
                d.content.rather = $('#portalElementAddOrRather').prop('checked'); // 是否比较
                d.content.rather = d.content.rather == true ? 0 : 1; // 0：启用  1:不启用
                // d.content.lump = $('#portalElementAddOrLump').prop('checked'); // 是否表格
                // d.content.lump = d.content.lump == true ? 0:1; // 0：启用  1:不启用
                d.content.rows = arr;
                return d;
            }
            function getListData(data) {
                var mr = mrCheckbox();
                var fm = $.getFormAllData('portalElementAdd__formBox');
                if (data.category == 3 || data.category == 4) {
                    mr.content = {};
                    mr.content.cgy = data['portalElementAdd-intro__nameval'];
                    mr.content.title = data['portalElementAdd-intro__titleval'];
                    mr.content.con = data['portalElementAdd-intro__conval'];
                }
                mr.content.argument = $('#portalElementAdd-form__argumentval').val(); // 已选参数
                mr.content.category = data.category;
                mr.content.urls = data.urls;
                mr.content.linkType = data.linkType;
                mr.content.detailTitle = data.detailTitle;
                mr.content.detailUeser = data.detailUeser;
                mr.content.detailDep = data.detailDep;
                mr.content.detailDate = data.detailDate;
                mr.content.detailCon = data.detailCon;
                mr.content.detailArgument = data.detailArgument;
                mr.content.detailUrl = data.detailUrl;
                mr.content.listNum = data.listNum;
                mr.content.elmheight = data.elmheight;
                mr.content.token = data.token;
                mr.content.usercode = data.usercode;
                mr.content.depcode = data.depcode;
                mr.content.isSrc = data.isSrc;
                mr.content.speed = data.speed;
                // mr.content = $.extend(true, mr.content, fm);
                return mr;
            }

            // 图片数据
            function getImg() {
                var len = $('#portalElementAddImgLIst .portalElementAddlimgBox').length;
                var d = [];
                for (var i = 0; i < len; i++) {
                    var elm = $('#portalElementAddImgLIst .portalElementAddlimgBox').eq(i);
                    var _d = {
                        src: elm.find('img').attr('src'),
                        title: elm.find('[name="img_title"]').val(),
                        url: elm.find('[name="img_url"]').val(),
                    };
                    d.push(_d);
                }
                return d;
            }

            // 图表数据
            function setImgform() {
                var arr = [];
                for (var i = 1; i <= imgformi; i++) {
                    var d = {
                        title: $('#portalElementAddTBtitle' + i).val(), // 名称
                        dsidname: $('#portalElementAddTBsource' + i + 'name').val(), // 数据源name
                        dsurl: $('#portalElementAddTBsource' + i + 'val').attr('url'), // 数据源地址
                        dsid: $('#portalElementAddTBsource' + i + 'val').val(), // 数据源id
                        xn: $('#portalElementAddTBacross' + i + 'name').val(), // 横坐标name
                        x: $('#portalElementAddTBacross' + i + 'val').val(), // 横坐标code
                        yn: $('#portalElementAddTBendlong' + i + 'name').val(), // 纵坐标name
                        y: $('#portalElementAddTBendlong' + i + 'val').val(), // 纵坐标code
                        type: $('[lay-filter="portalElementAddTBtypeSel' + i + '"]').val(), // 图类型
                        smalltype: $('#portalElementAddTBRimg' + i + ' .active').attr('state'),
                    };
                    if (d.dsid != '' && d.x != '' && d.y != '' && d.smalltype) {
                        arr.push(d);
                        urlArr.push(d.dsurl);
                    }
                }
                return arr;
            }

            // 多页签数据
            function getPageData() {
                var arr = $('#portalElementAddMorePageBox .portalElementAddMorePageRow');
                var _arr = [];
                $.each(arr, function (i, v) {
                    var o = {
                        name: $(v).find('[name="name"]').val(),
                        text: $(v).find('[name="morePageName"]').val(),
                        id: $(v).find('[name="morePageId"]').val(),
                    };
                    _arr.push(o);
                });
                return _arr;
            }

            // 快捷链接取数据
            function getLinksData() {
                var arr = [];
                $.each($('#portalElementAddLinksBox .portalElementAddlinksRow'), function (i, v) {
                    var dom = $(v).find('.input');
                    var d = {};
                    $.each(dom, function (l, item) {
                        var name = $(item).attr('name');
                        var val = $(item).val();
                        d[name] = val;
                    });
                    d.number = $('#portalElementAddLinksRownumber').val();
                    arr.push(d);
                });
                return arr;
            }

            // 方块签
            function getSignData() {
                var arr = [];
                $.each($('#portalElementAddsignBox .portalElementAddsignRow'), function (i, v) {
                    var dom = $(v).find('.input');
                    var d = {};
                    $.each(dom, function (l, item) {
                        var name = $(item).attr('name');
                        var val = $(item).val();
                        d[name] = val;
                    });
                    arr.push(d);
                });
                var d = {};
                d.dsid = $('#portalElementAddAllocationSignval').val();
                d.dsname = $('#portalElementAddAllocationSignname').val();
                (d.dsurl = $('#portalElementAddAllocationSignval').attr('url')), // 数据源地址
                    (d.arr = arr);
                return d;
            }

            // 日程
            function getCalendar() {
                var arr = [];
                $.each($('#portalElementAddCalenderLineBox .row'), function (i, v) {
                    var c = $(v).find('.check').prop('checked');
                    if (c == true) {
                        c = $(v).find('.check').val();
                        var d = {
                            name: c,
                            number: $(v).find('.number').val(),
                        };
                        arr.push(d);
                    }
                });
                var d = {
                    type: $('#portalElementAddCalenderTypeInput').val(),
                    calender: {
                        dsid: $('#portalElementAddCalenderval').val(),
                        dsname: $('#portalElementAddCalendername').val(),
                        dsurl: $('#portalElementAddCalenderval').attr('url'),
                        timeName: $('#portalElementAddCalenderTimeval').val(),
                    },
                    line: {
                        dsid: $('#portalElementAddCalenderLineval').val(),
                        dsname: $('#portalElementAddCalenderLinename').val(),
                        dsurl: $('#portalElementAddCalenderLineval').attr('url'),
                        timeName: $('#portalElementAddCalenderLineTimeval').val(),
                        arr: arr,
                    },
                };
                return d;
            }

            // 提交数据处理
            function submitdata(data) {
                var d = {};
                d.elementid = data.elementid;
                d.elementname = data.elementname;
                d.typeName = data.typeName;
                d.isUse = data.isUse;
                d.typeId = data.typeId;
                switch (data.typeId) {
                    case '1':
                        // 列表
                        var mr = getListData(data);
                        d.cotent = JSON.stringify(mr);
                        break;
                    case '2':
                        // 文本
                        d.cotent = editor.txt.html();
                        break;
                    case '3':
                        // 图片
                        d.cotent = JSON.stringify(getImg());
                        break;
                    case '4':
                        // 媒体
                        d.cotent = $('#portalElementAddVideoCon').val();
                        break;
                    case '5':
                        // HTML
                        break;
                    case '6':
                        // 报表工具
                        d.cotent = JSON.stringify(setImgform());
                        break;
                    case '7':
                        // 快捷链接
                        var _d = {
                            set: {
                                f_listNum: data.f_listNum,
                                f_elmheight: data.f_elmheight,
                                f_type: data.f_type,
                            },
                            con: getLinksData(),
                        };
                        d.cotent = JSON.stringify(_d);
                        break;
                    case '8':
                        // 多页签
                        d.cotent = JSON.stringify(getPageData());
                        break;
                    case '10':
                        // 方块签
                        d.cotent = JSON.stringify(getSignData());
                        break;
                    case '11':
                        // 日程
                        d.cotent = JSON.stringify(getCalendar());
                        break;
                }
                return d;
            }
            // -------------- 保存数据获取 end -------------

            // 颜色插件
            function colorFn(elm, color) {
                $('#' + elm).colpick({
                    color: color,
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        // 改变颜色
                        $('#' + elm + ' .text').val(hex);
                        $('#' + elm + ' .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        // 改变颜色
                        $('#' + elm + ' .text').val(hex);
                        $('#' + elm + ' .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });
            }

            // 元素类型选择
            form.on('select(portalElementAddYStype)', function (data) {
                ystype = data.value;
                urlArr = [];
                $('#portalElementAddYStypeHtml .item')
                    .eq(Number(data.value) - 1)
                    .show()
                    .siblings()
                    .hide();
                $('#portalElementAddYStypeName').val($(data.elem).find('option:selected').text());
                if (data.value == 6) {
                    var is = $('#portalElementAddTBCONbox .layui-col-xs5').length;
                    if (is == 0) {
                        // 图表选择
                        addIMGform(imgformi);
                    }
                }
                if (data.value == 1) {
                    articledata(null);
                }
                if (data.value == 2 && editor == null) {
                    var E = window.wangEditor;
                    editor = new E('#myEditor');
                    // 或者 var editor = new E( document.getElementById('editor') )
                    editor.create();
                }
                if (data.value == 7) {
                    var h = $('#portalElementAddFastLinkstHtml').html();
                    var _h = $('#portalElementAddLinksBox').html();
                    if (_h == '') {
                        $('#portalElementAddLinksBox').html(h);
                        $('#portalElementLinksMoveTitleBox').attr('id', 'portalElementLinksMoveTitleBox0');
                        fastSJYs('portalElementLinksMoveTitleBox0', 0, {}); // 数据源
                        fastColorInit(0, '333333', 'f7f7f7'); // 颜色
                    }
                }
            });

            // 保存
            form.on('submit(portalElementAddCofirm)', function (data) {
                var d = data.field;
                var id = $('#hsbMotifAddId').val();
                var data = submitdata(d);
                if (data.typeId == 6 && data.cotent.length == 0) {
                    layer.msg('图表设置不正确！');
                    return false;
                }
                data.colUrl = JSON.stringify(urlArr);
                $.ajax({
                    type: 'put',
                    url: common.url + '/ts-portal/api/portal/elment/elmentAddorUpdate',
                    data: data,
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.mTable.refresh();
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        layer.msg('操作失败！');
                    },
                });
            });

            // ------ end
        });
    };
});
