"use strict";
define(function(require, exports, module) {
    exports.init = function(opt, html) {
        layui.use(['form', 'trasen', 'treeSelect', 'zTreeSearch'], function() {
            var form = layui.form,
                trasen = layui.trasen,
                laydate = layui.laydate,
                zTreeSearch = layui.zTreeSearch;

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['450px', '350px'],
                skin: 'yourclass',
                content: html,
                success: function(layero, index) {
                    getDictInfoList(dict_first_education_type, "第一学历", "firstEducationTypeSel"); 
                    getDictInfoList(dict_employee_category , "员工类别", "employee_Category_Sle"); 
                    if (opt.data) {
                        trasen.setNamesVal(layero, opt.data);
                    }
                    form.render();
                }
           
            });

            form.on('submit(educationSalaryAddSub)', function(data) {
                var _url = common.url + "/ts-hrms/educationSalary/save";
                if (opt.data) {
                    _url = common.url + "/ts-hrms/educationSalary/update"
                }
                var _data = JSON.stringify(data.field);
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: _url,
                    data: _data,
                    success: function(res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('保存成功！');
                            opt.ref && opt.ref();
                            opt.refT && opt.refT();
                        } else {
                            layer.msg(res.message || '操作失败');
                        }
                    }
                });
                return false;
            })

            $('#salaryManageEducationSalaryAddHtml #educationSalaryClose').off('click').on('click', function() {
                layer.closeAll();
            });
        });
    };
});