'use strict';
define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'laydate', 'upload', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;

            // 表格渲染
            var trasenTable = new $.trasenTable('grid-table-positionManageTable', {
                url: common.url + '/ts-basics-bottom/position/list',
                pager: 'grid-pager-positionManagePager',
                shrinkToFit: true,
                sortname: 'serial_number', //要排序的字段 //默认表格加载时根据fca09列排序
                sortorder: 'asc', //默认的排序方式,跟数据库的asc,desc一样 asc 降序 desc 升序
                sortable: true, //sortable并不是加在colModel中。
                rowKey: 'positionId',
                colModel: [
                    { label: 'ID', name: 'positionId', hidden: true, key: true },
                    { label: '职务名称', name: 'positionName', index: 'position_name', width: 200, editable: false, align: 'left' },
                    { label: '描述', name: 'remark', index: 'remark', width: 370, editable: false, align: 'left' },
                    {
                        label: '状态',
                        name: 'isEnable',
                        index: 'isEnable',
                        width: 40,
                        editable: false,
                        align: 'left',
                        formatter: function (cell, opt, row) {
                            if (row.isEnable == 1) {
                                return '启用';
                            } else {
                                return '禁用';
                            }
                        },
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: 'caozuo',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        frozen: true,
                        classes: 'visible  ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button  class="layui-btn  positionManage-edit" row-id="' + opt.rowId + '">编辑</button> ';
                            if (row.isEnable == 1) {
                                btns += '<button  class="layui-btn  positionManage-dis" row-id="' + opt.rowId + '">禁用</button> ';
                            } else {
                                btns += '<button  class="layui-btn  positionManage-enable" row-id="' + opt.rowId + '">启用</button> ';
                            }
                            btns += '<button  class="layui-btn  positionManage-del" row-id="' + opt.rowId + '">删除</button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                ],
                queryFormId: 'positionManageQueryForm',
                gridComplete: function () {
                    var that = this;
                    $(this).jqGrid('sortableRows', {
                        update: function () {
                            var arr = [];
                            $.each($(that).find('tr.jqgrow[role="row"]'), function (i, v) {
                                arr.push({
                                    positionId: $(this).attr('id'),
                                    serialNumber: $(this)
                                        .find('[aria-describedby="' + $(that).attr('id') + '_rn"]')
                                        .text(),
                                });
                            });
                            $.ajax({
                                url: '/ts-basics-bottom/position/updateSort',
                                method: 'post',
                                contentType: 'application/json',
                                data: JSON.stringify(arr),
                                success: function (res) {
                                    refreshTable();
                                },
                            });
                        },
                    });
                },
            });

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            // 查询
            form.on('submit(positionManageSearch)', function (data) {
                refreshTable();
            });
            //重置
            form.on('submit(positionManageRefresh)', function (data) {
                $("#positionManageQueryForm input[name='positionName']").val('');
                form.render();
                refreshTable();
            });

            // 编辑职务
            $('#positionManageDiv')
                .off('click', '.positionManage-edit')
                .on('click', '.positionManage-edit', function () {
                    var rowid = $(this).attr('row-id');
                    var rowData = trasenTable.getSelectRowData(rowid);
                    $.quoteFun('orgManage/position/modules/add', {
                        title: '编辑职务',
                        data: rowData,
                        ref: refreshTable,
                    });
                });

            // 新增职务
            $('#positionManageDiv')
                .off('click', '#positionManageAdd')
                .on('click', '#positionManageAdd', function () {
                    $.quoteFun('orgManage/position/modules/add', {
                        title: '新增职务',
                        ref: refreshTable,
                    });
                });

            $('#positionManageDiv')
                .off('click', '.positionManage-dis')
                .on('click', '.positionManage-dis', function () {
                    var rowid = $(this).attr('row-id');
                    var rowData = trasenTable.getSourceRowData(rowid);
                    $.ajax({
                        url: '/ts-basics-bottom/position/verifyEnable',
                        data: {
                            positionId: rowData.positionId,
                        },
                        success: function (res) {
                            if (res.success) {
                                layer.confirm('是否禁用该职务？',{
                                    btn: ['确定', '取消'],
                                    title: '提示',
                                    closeBtn: 0
                                }, function (index) {
                                    $.ajax({
                                        url: '/ts-basics-bottom/position/enable',
                                        data: {
                                            positionId: rowData.positionId,
                                            enable: 0,
                                        },
                                        success: function (res) {
                                            if (res.success) {
                                                layer.close(index);
                                                refreshTable();
                                                layer.msg('禁用成功');
                                            } else {
                                                layer.msg(res.message || '禁用失败');
                                            }
                                        },
                                    });
                                });
                            } else {
                                layer.msg(res.message || '不能禁用');
                            }
                        },
                    });
                });
            $('#positionManageDiv')
                .off('click', '.positionManage-enable')
                .on('click', '.positionManage-enable', function () {
                    var rowid = $(this).attr('row-id');
                    var rowData = trasenTable.getSourceRowData(rowid);
                    layer.confirm('是否启用该职务？',{
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    }, function (index) {
                        $.ajax({
                            url: '/ts-basics-bottom/position/enable',
                            data: {
                                positionId: rowData.positionId,
                                enable: 1,
                            },
                            success: function (res) {
                                if (res.success) {
                                    layer.close(index);
                                    refreshTable();
                                    layer.msg('启用成功');
                                } else {
                                    layer.msg(res.message || '启用失败');
                                }
                            },
                        });
                    });
                });

            // 删除职务
            $('#positionManageDiv')
                .off('click', '.positionManage-del')
                .on('click', '.positionManage-del', function () {
                    var rowid = $(this).attr('row-id');
                    var rowData = trasenTable.getSelectRowData(rowid);
                    layer.confirm(
                        '确定要删除该职务吗？',
                        { 
                            btn: ['确定', '取消'],
                            title: '提示',
                            closeBtn: 0
                         },
                        function () {
                            $.ajax({
                                type: 'post',
                                contentType: 'application/json; charset=utf-8',
                                url: common.url + '/ts-basics-bottom/position/deletedById/' + rowData.positionId,
                                success: function (res) {
                                    if (res.success) {
                                        refreshTable();
                                        layer.closeAll();
                                        layer.msg(res.message || '删除成功');
                                    } else {
                                        layer.closeAll();
                                        layer.msg(res.message || '操作失败');
                                    }
                                },
                            });
                        },
                        function () {}
                    );
                });
        });
    };
});
