define(function (require, exports, module) {
  var workOrderDetail = {};
  exports.init = function (opt, html) {
    var currentIndex = 0,
      currentSelected = 1;
    var API = {
      getOrderInfo: '/ts-worksheet/workSheet/workSheetInfo/',
      getPeopleInfoList: '/ts-worksheet/workSheetPeopple/getNoPagePeopleInfoList',//获取人员信息
      dispatchOrder: '/ts-worksheet/workSheet/workSheetDispatch',
      workSheetAccept: '/ts-worksheet/workSheet/workSheetAccept',
      workSheetProcessingComplete: '/ts-worksheet/workSheet/workSheetProcessingComplete',
      workSheetBack: '/ts-worksheet/workSheet/workSheetBack',
      workSheetResend: '/ts-worksheet/workSheet/workSheetResend',
      workSheetHasStopped: '/ts-worksheet/workSheet/workSheetHasStopped',
      workSheetTerminated: '/ts-worksheet/workTask/workSheetTerminated',
      workSheetAssist: '/ts-worksheet/workSheet/workSheetAssist',
      workSheetUpdateProgress: '/ts-worksheet/workSheet/workSheetUpdateProgress',
      meauList: '/ts-worksheet/workSheet/meauList',//获取处理科室列表
      upload: '/ts-basics-bottom/fileAttachment/upload?moduleName=workDesk',
    },
      workNumber = opt.data.workNumber,
      logData = null,
      peopleData = {},
      selectedIndex = 0,
      selectedArray = [],
      orderDetail = {},
      oldFaultType = {},//旧的故障类型
      oldHandleDeptId = {},//旧的处理科室
      requestTimer = null,
      pic = [],
      txt = [],
      wsFileOutVoList = [],
      coastTable = null;
    function initPage() {
      layui.use(['form', 'trasen', 'layer'], function () {
        var form = layui.form,
          trasen = layui.trasen
        layer = layui.layer
        form.verify({
          redioRequired: function(value, item){
            let verifyName = $(item).attr('name'),
            formElem=$(item).parents('.layui-form'),
            verifyElem=formElem.find('input[name='+verifyName+']'),//获取需要校验的元素
            isTrue= verifyElem.is(':checked'),//是否命中校验
            focusElem = verifyElem.next().find('i.layui-icon')
            if(!isTrue || !value){
              //定位焦点
              focusElem.css({"color":"#FF5722"});
              //对非输入框设置焦点
              focusElem.first().attr("tabIndex","1").css("outline","0").blur(function() {
                  focusElem.css({"color":""});
               }).focus();
              return '必填项不能为空';
            }
          }
        })
        layer.open({
          type: 1,
          title: opt.title,
          closeBtn: 1,
          area: '900px',
          shadeClose: false,
          skin: 'myorderHandle',
          content: html,
          success: function (layero, index) {
            $('span[name="actionTitle"]').each((index, dom)=>{
              dom.innerHTML = opt.title
            })
            $.ajax({
              url: API.getOrderInfo + workNumber,
              type: 'GET',
              async: false,
              success: function (res) {
                if(res.success == false){
                  layer.msg(res.message||"获取工单详情出错")
                  return
                }
                let object = res.object || {};
                var data = object.wsWsSheetInfoOutVo || {};
                workOrderDetail = data;
                wsFileOutVoList = object.wsFileOutVoList || [];
                logData = object.wsTaskInfoOutVoList || [];
                data.repairDeptAddress = [data.hospitalDistrictName, data.repairDeptAddress]
                  .filter(item => item)
                  .join('-');
                if(data.assist){
                    data.fkUserName = `${data.fkUserName || ''}（协助：${data.assist}）`;
                }
                orderDetail = object.wsWsSheetInfoOutVo || {}
                opt.title == '响应' ? renderHandleDept(1) : renderHandleDept(2);
                renderHandleSelect(data.fkFaultTypeId);
                // form.render();

                //添加title属性
                $('#myorderHandle [name="repairDeptAddress"]').attr('title', data.repairDeptAddress);
                // $('#myorderHandle [name="faultDeion"]').attr('title', data.faultDeion);
                $('#myorderHandle [name="fkUserName"]').attr('title', data.fkUserName);

                data.faultAffectScopeValue != '个人事件'
                  ? $('#myorderHandle [name="faultAffectScopeValue"]').css({
                      color: '#5260ff',
                      fontWeight: '600'
                    })
                  : null;
                data.faultEmergencyValue != '常规处理'
                  ? $('#myorderHandle [name="faultEmergencyValue"]').css({
                      color: '#5260ff',
                      fontWeight: '600'
                    })
                  : null;

                let toast = $('#myorderHandle .handle-top [name="toast"]');
                let toastSpan = ''
                data.hatencount ? toastSpan = `工单已催办${data.hatencount}次` : null;
                toastSpan ? toastSpan += '，' : null;
                if(data.requiredCompletionTime && data.cqDays <= 0 && data.cqDays >= -3 && data.workStatus<=3 ){
                  toastSpan += data.cqDays == 0 ? '工单今天到期，' : `工单还有${Math.abs(data.cqDays)}天到期，`;
                } else if(data.requiredCompletionTime && data.cqDays > 0 && data.workStatus<=3 ){
                  toastSpan +=  `工单已超期${data.cqDays}天，`;
                }
                toastSpan ? toastSpan += '需尽快处理' : null;

                if(toastSpan){
                  let html = `
                  <img
                    src="/static/img/other/order_jinji.svg" 
                    alt="" 
                    style="height: 18px; min-width: 18px"
                    />
                  ${toastSpan}`

                  toast.append(html);
                }


                //给故障描述还有协助添加 title 属性 防止过多信息溢出
                $('.overInput[name="fkUserName"]').attr('title', data.fkUserName);
                $('.overInput[name="faultDeion"]').attr('title', data.faultDeion);
                $('#handleOrderTime')[0].innerHTML = '已用时' + data.workHours + 'H';

                if(opt.data.workStatus === '2') {
                  data.revTime = ''
                }

                trasen.setNamesVal(layero, data);
                //给处理科室以及故障类型赋值
                $('[render="department"] [name="businessDeptName"]').val(data.businessDeptName);
                $('[render="department"] [name="businessDeptId"]').val(data.businessDeptId);
                $('[name="fkFaultTypeName"]').val(data.fkFaultType);
                $('[name="fkFaultTypeId"]').val(data.fkFaultTypeId);

                // let contentHeight = $('#myorderHandle #operatingInfoContent')[0].clientHeight + 'px';
                // $('#myorderHandle #resource')[0].style.height = contentHeight;
                // $("#myorderHandle #operatingInfoContent")[0].style.height = contentHeight;
                setTimeout(()=>{
                  let firstLoadHeight = $(".layui-layer.myorderHandle")[0].clientHeight + 'px';
                  $(".layui-layer.myorderHandle")[0].style.height = firstLoadHeight;
                })
              }
            })

            initCostTable();
            renderResource();//渲染资源文件
          }
        });
        form.render()
        bindOptionsBarEvent()
        form.on('submit(orderFinishBtn)', function (data) {
          $.loadings();
          $('#myorderHandle #orderFinishBtn').attr('disabled', 'disabled');
          var _data = {},
            msg = null
          _data.fkUserId = opt.data.fkUserId
          _data.pkWsTaskId = opt.data.pkWsTaskId
          _data.remark = data.field.handleRemark
          opt.peopleType==3 ? null : _data.yesOrNo = data.field.yesOrNo;
          let fileList = pic.filter(item=>item).concat(txt.filter(item=>item))
          _data.files = fileList;
          if(data.field.handleWorkHours === '0') {
            $('#myorderHandle #orderFinishBtn').attr('disabled', false);
            $.closeloadings();
            layer.msg('处理工时不能为0')
            return 
          }
          _data.workHours = data.field.handleWorkHours
          requestTimer && clearTimeout(requestTimer);
          requestTimer = setTimeout(()=>{
            $.ajax({
              url: opt.data.peopleType == 3 || opt.data.peopleType == 5 ? API.workSheetUpdateProgress : API.workSheetProcessingComplete,
              type: 'POST',
              data: JSON.stringify(_data),
              contentType: "application/json; charset=utf-8",
              async: false,
              success: function (res) {
                $('#myorderHandle #orderFinishBtn').attr('disabled', false);
                $.closeloadings();
                opt.ref()
                layer.closeAll()
                layer.msg(res.message)
              }
            })
          })
        })
        form.on('submit(orderEndBtn)', (data) => {
          $.loadings();
          $('#myorderHandle #orderEndBtn').attr('disabled', 'disabled');
          switch (opt.data.entryType) {
            case 'end':
              var _data = {}
              _data.pkWsTaskId = opt.data.pkWsTaskId
              _data.remark = data.field.endRemark
              _data.yesOrNo = "1";
              requestTimer && clearTimeout(requestTimer);
              requestTimer = setTimeout(()=>{
                $.ajax({
                  url: API.workSheetTerminated,
                  type: 'POST',
                  data: JSON.stringify(_data),
                  contentType: "application/json; charset=utf-8",
                  async: false,
                  success: function (res) {
                    $('#myorderHandle #orderEndBtn').attr('disabled', false);
                    $.closeloadings();
                    opt.ref()
                    layer.closeAll()
                    if (res.success) {
                      layer.msg('终止/暂停成功')
                    }
                    else {
                      layer.msg(res.message||'操作失败')
                    }
                  }
                })
              })
              break;
            case 'pause':
              var _data = {}
              _data.pkWsTaskId = opt.data.pkWsTaskId
              _data.remark = data.field.endRemark
              _data.fkUserId = opt.data.fkUserId;
              requestTimer && clearTimeout(requestTimer);
              requestTimer = setTimeout(()=>{
                $.ajax({
                  url: API.workSheetHasStopped,
                  type: 'POST',
                  data: JSON.stringify(_data),
                  contentType: "application/json; charset=utf-8",
                  async: false,
                  success: function (res) {
                    $('#myorderHandle #orderEndBtn').attr('disabled', false);
                    $.closeloadings();
                    opt.ref()
                    layer.closeAll()
                    if (res.success) {
                      layer.msg('终止/暂停成功')
                    }
                    else {
                      layer.msg(res.message||'操作失败')
                    }
                  }
                })
              })
              break
          }
        })
        form.on('submit(orderResendBtn)', function (data) {
          $.loadings();
          $('#myorderHandle #orderResendBtn').attr('disabled', 'disabled');
          var _data = {}
          if(opt.data.entryType === 'resend') {
            if(opt.data.fkUserId === data.field.fkUserId) {
              $.closeloadings();
              $('#myorderHandle #orderResendBtn').attr('disabled', false);
              layer.msg('处理人无变化，无需转发');
              return
            }
          }
          if(data.field.fkUserId){
            _data.fkUserId = data.field.fkUserId
          }
          if(data.field.fkFaultTypeId){
            _data.fkFaultTypeId = data.field.fkFaultTypeId
          }

          _data.fkFormerUserId = opt.data.fkUserId
          _data.pkWsTaskId = opt.data.pkWsTaskId
          _data.remark = data.field.resendRemark
          _data.businessDeptId = data.field.businessDeptId
          requestTimer && clearTimeout(requestTimer);
          requestTimer = setTimeout(()=>{
            $.ajax({
              url: API.workSheetResend,
              type: 'POST',
              data: JSON.stringify(_data),
              contentType: "application/json; charset=utf-8",
              async: false,
              success: function (res) {
                $('#myorderHandle #orderResendBtn').attr('disabled', false);
                $.closeloadings();
                if(res.success){
                  opt.ref()
                  layer.closeAll()
                  layer.msg(res.object)
                } else {
                  layer.msg(res.message || '服务器出错，请稍后再试')
                }
              }
            })
          })
        })
        let handleCount = 0;
        $('#myorderAddAssistBtn').bind('click', function(){
          $('#myorderAddAssistItem').append(
            `
              <div class="flex flex-center" count="${handleCount}">
                <div class="layui-form-item" style="flex:1;">
                  <div class="flex">
                    <label class="input-box-title assignment-text"><span class="required">*</span>协助人</label>
                    <input class="layui-input" name="handleNames" count="${handleCount}" readonly></input>
                    <input type="hidden" name="handleIds" count="${handleCount}">
                  </div>
                </div>
                <div class="flex layui-form-item" style="flex:1;">
                  <label class="input-box-title" style="width: 97px;"><span class="required">*</span>须协助内容</label>
                  <div style="flex: 1;">
                    <input type="text" name="assistContent" lay-verify="required" class="layui-input">
                  </div>
                </div>
                <i class="layui-icon delete-icon" title="删除">&#xe640;</i> 
              </div>
            `
          )
          handleCount++;

          let childList = $('#myorderAddAssistItem').children(),
              lastChild = $(childList[childList.length-1])[0];

          //打开人员选择框组件
          $('[name="handleNames"]', lastChild).bind('click', function(e){
            let node = e.target,
            valueInput = $(node).next()[0],
            selectedNames = (node.value && node.value.split(',')) || [],
            selectedIds = (valueInput.value && valueInput.value.split(",")) || [];

            $.quoteFun('myWorkOrder/modules/handlePersonSelect',{
              title: '人员选择',
              data: { selectedNames, selectedIds },
              faultTypeId: orderDetail.fkFaultTypeId,
              ref: function(idList, nameList){
                let newIds = idList.join(','),
                newNames = nameList.join(',');

                node.value != newNames ? node.value = newNames : null;
                valueInput.value != newIds ? valueInput.value = newIds : null;
                node.title = newNames;
                if(idList.length){
                  $(node).removeClass('can-not-null');
                }
                else{
                  layer.msg('必填项不能为空');
                  $(node).addClass('can-not-null');
                }
              }
            })
          })

          //当新增后子元素多于一个，则为第一个子元素添加删除按钮
          if(childList.length>1){
            if( !$('.delete-icon', childList[0]).length ){
              $(childList[0]).append(`
                <i class="layui-icon delete-icon" title="删除">&#xe640;</i> 
              `);

              //为该元素添加点击事件
              $('.delete-icon', childList[0]).bind('click', handleDeleteAssite)
            }
          } else {
            $('.delete-icon', childList[0]).remove();
          }

          //为新增的删除按钮添加点击事件
          $('.delete-icon', lastChild).bind('click', handleDeleteAssite);
          //为新增的协助内容添加输入事件 清除必填样式
          $('[name="assistContent"]input', lastChild).bind('input propertychange change paste', function(e){
            let node = e.target;
            if( node.className.indexOf('can-not-null') == -1 ) return;
            $(node).removeClass('can-not-null');
          })
        })

        if(opt.data.entryType == 'assist'){
          $('#myorderAddAssistBtn').trigger('click');
        }
        //多人协助确认
        $('#myorderAssistConfirm').click(function(){
          $.loadings();
          requestTimer && clearTimeout(requestTimer);
          requestTimer = setTimeout(()=>{
            let childList = $('#myorderAddAssistItem').children();

            //遍历判断数组
            let handleIds = [],
            handleRemarks = [],
            contentNode = $('#myorderAddAssistItem')[0];
  
            for(let i = 0; i<childList.length; i++){
              let child = childList[i],
              fkUserIdNode = $('[name="handleIds"]', child)[0],
              fkUserNameNode = $('[name="handleNames"]', child)[0],
              assistNode = $('[name="assistContent"]', child)[0];
  
              if( !fkUserNameNode.value || !assistNode.value ){
                !fkUserNameNode.value ?
                  $(fkUserNameNode).addClass('can-not-null')
                  : null;
  
                !assistNode.value ?
                  $(assistNode).addClass('can-not-null')
                  : null;
  
                if(contentNode.scrollHeight > contentNode.clientHeight){
                  let tarTop = $(child).offset().top,
                  contentTop = $(contentNode).offset().top,
                  nowScroll = $(contentNode).scrollTop();
  
                  $(contentNode).scrollTop(tarTop - contentTop + nowScroll);
                }
                
                $.closeloadings();
                layer.msg('必填项不能为空', { icon: 2 });
                return;
              }
  
              (fkUserIdNode.value.split(',') || []).forEach(item=>{
                handleIds.push(item);
                handleRemarks.push(assistNode.value);
              })
            }
  
            var _data = {
              workNumber: opt.data.workNumber,
              fkUserIds: handleIds.join(','),
              remarks: handleRemarks.join(',')
            }
  
            $.ajax({
              url: API.workSheetAssist,
              contentType: "application/json; charset=utf-8",
              type: 'POST',
              async: false,
              data: JSON.stringify(_data),
              success: function (res) {
                opt.ref()
                layer.closeAll()
                $.closeloadings();
                if(res.success){
                  layer.msg('添加协助人成功')
                }
                else{
                  layer.msg(res.message||'添加协助人失败')
                }
              }
            })
          }, 300)
        })

        $('#myOrderhandleCancelBtn').click(function () {
          layer.closeAll()
        })
        $('#myOrderRerurnCancelBtn').click(function () {
          layer.closeAll()
        })
        $('#myOrderEndCancelBtn').click(function () {
          layer.closeAll()
        })
        $('#myOrderResendCancelBtn').click(function () {
          layer.closeAll()
        })
        $('#myorderAssistCancel').click(function () {
          layer.closeAll()
        })
        $("#upload[type=file]").funs('change', handleHandleUPloadFile);
        switch (opt.data.entryType) {
          case 'response':
            $('.response-form-wrap').css('display', 'block')
            bindOptionEvent(form)
            break
          case 'handle':
            $('.handle-form-wrap').css('display', 'block');
            if(opt.data.peopleType == '3' || opt.data.peopleType == '5' ){
                $('.handle-form-wrap [name="isFinished"]').css('display','none');
                $('.handle-form-wrap [name="isFinished"]').next().children(":first").css('justify-content','flex-start');

                $('.handle-form-wrap [name="isFinished"] span.required').remove();
                $('.handle-form-wrap [name="isFinished"] [lay-verify="required"]').removeAttr('lay-verify');
                $('.handle-form-wrap [name="isFinished"] [lay-verify="redioRequired"]').removeAttr('lay-verify');
            }
            $('#myorderHandle #myorderUpload').funs('click', function(){
              $("#upload[type=file]").click();
            });
            break;
          case 'end':
            $('.end-form-wrap').css('display', 'block')
            break
          case 'pause':
            $('.end-form-wrap').css('display', 'block')
            break
          case 'assist':
            $('.assist-form-wrap').css('display', 'block')
            break
          case 'resend':
            $('.resend-form-wrap').css('display', 'block')
            break
        }
      })
    }
    initPage()
    /*------------------------------渲染方法--------------------------------*/
    //响应工单根据radio内容渲染下方
    function renderBottomForm(value, form) {
      switch (value) {
        case 1:
          $('.receive-item-form').css('display', 'flex')
          $('.return-item-form').css('display', 'none')
          $('.resend-item-form').css('display', 'none')
          $('#retuenOrderReason').attr('lay-verify', '')
          $('#handlerPersonBox-resend input').attr('lay-verify', '')
          break
        case 2:
          $('.receive-item-form').css('display', 'none')
          $('.return-item-form').css('display', 'flex')
          $('.resend-item-form').css('display', 'none')
          $('#retuenOrderReason').attr('lay-verify', 'required')
          $('#handlerPersonBox-resend input').attr('lay-verify', '')
          break
        case 3:
          $('.receive-item-form').css('display', 'none')
          $('.return-item-form').css('display', 'none')
          $('.resend-item-form').css('display', 'flex')
          $('#retuenOrderReason').attr('lay-verify', '')
          $('#handlerPersonBox-resend input').attr('lay-verify', 'required')
          break
      }
      form.on('submit(receiveOrder)', function (data) {
        $.loadings();
        $('#myorderHandle #orderInfoSave').attr('disabled', 'disabled');
        var _data = {}
        _data.pkWsTaskId = opt.data.pkWsTaskId
        switch (data.field.responseType) {
          case '1':
            _data.fkUserId = opt.data.fkUserId;
            _data.remark = data.field.receiveOrderRemark
            requestTimer && clearTimeout(requestTimer);
            requestTimer = setTimeout(()=>{
              $.ajax({
                url: API.workSheetAccept,
                contentType: "application/json; charset=utf-8",
                type: 'POST',
                async: false,
                data: JSON.stringify(_data),
                success: function (res) {
                  $('#myorderHandle #orderInfoSave').attr('disabled', false);
                  $.closeloadings();
                  opt.ref()
                  layer.closeAll()
                  res.success == false ? layer.msg(res.message||'操作失败') : layer.msg('操作成功')
                }
              })
            });
            break;
          case '2':
            _data.fkUserId = opt.data.fkUserId;
            _data.remark = data.field.retuenOrderReason
            requestTimer && clearTimeout(requestTimer);
            requestTimer = setTimeout(()=>{
              $.ajax({
                url: API.workSheetBack,
                contentType: "application/json; charset=utf-8",
                type: 'POST',
                async: false,
                data: JSON.stringify(_data),
                success: function (res) {
                  $('#myorderHandle #orderInfoSave').attr('disabled', false);
                  $.closeloadings();
                  opt.ref()
                  layer.closeAll()
                  res.success == false ? layer.msg(res.message||'操作失败') : layer.msg('操作成功')
                }
              })
            })
            break;
          case '3':
            _data.fkFormerUserId = opt.data.fkUserId
            if(data.field.fkUserId){
              _data.fkUserId = data.field.fkUserId
            }
            if(data.field.fkFaultTypeId){
              _data.fkFaultTypeId = data.field.fkFaultTypeId
            }
            _data.remark = data.field.resendOrderRemark
            _data.businessDeptId = data.field.businessDeptId
            if(_data.fkUserId == opt.data.fkUserId){
              $('#myorderHandle #orderInfoSave').attr('disabled', false);
              $.closeloadings();
              layer.msg('处理人无变化，无需转发');
              return
            }
            requestTimer && clearTimeout(requestTimer);
            requestTimer = setTimeout(()=>{
              $.ajax({
                url: API.workSheetResend,
                contentType: "application/json; charset=utf-8",
                type: 'POST',
                async: false,
                data: JSON.stringify(_data),
                success: function (res) {
                  $('#myorderHandle #orderInfoSave').attr('disabled', false);
                  $.closeloadings();
                  opt.ref()
                  layer.closeAll()
                  res.success == false ? layer.msg(res.message||'操作失败') : layer.msg('操作成功')
                }
              })
            })
            break;
        }
      })
    }
    function renderHandleSelect(id, index){
        let required = $('[render="department"] [name="businessDeptId"]').val() == workOrderDetail.businessDeptId ? 'required' : 'none',
        deptId = $('#myorderHandle [render="department"] [name="businessDeptId"]').val() || workOrderDetail.businessDeptId;
        //多人协助渲染
        let optionsAssist = {
            url: API.getPeopleInfoList,
            datatype: 'POST', // 请求方式
            searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
            textName: 'name', 
            valName: 'userId', 
            inpTextName: 'handleName', 
            inpValName: 'fkUserId', 
            condition: 'employeeName',
            layout: 'concat',
            labelConcatList: ['name', 'deptName', 'processCountString'],
            required,
            data: {
                faultTypeId: id || null,
                pageSize: 10,
                deptId
            },
            callback: function (res) {
            },
        };

        // new $.selectPlug(`.layui-form[index="${index}"] [render="handlePersonBox"]`, optionsAssist);

        //响应工单转发渲染
        new $.selectPlug('#handlerPersonBox-resend', optionsAssist);
        //转发操作渲染
        new $.selectPlug('#handlerPersonBox-newHandle', optionsAssist);
    }
    //渲染处理科室
    function renderHandleDept(index){
      let item = $('[render="department"]')[index-1]
      let realIndex = item.getAttribute('index'),
        faultTypeName = `.layui-form[index="${realIndex}"] [name="fkFaultTypeName"]`,
        faultTypeId = `.layui-form[index="${realIndex}"] [name="fkFaultTypeId"]`,
        handlePersonBox = `.layui-form[index="${realIndex}"] [render="handlePersonBox"] input`
        options = {
          url: API.meauList,
          datatype: 'get', // 请求方式
          searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
          textName: 'deptName', 
          valName: 'deptId', 
          inpTextName: 'businessDeptName', 
          inpValName: 'businessDeptId', 
          condition: 'employeeName',
          layout: 'concat',
          required: 'required',
          labelConcatList: 'deptName',
          data: {
              pageSize: 999,
          },
          callback: function(res) {
            if(!res || oldHandleDeptId[realIndex] === res.deptId){
              return
            }
            
            //清空故障类型、处理人数据
            oldFaultType[realIndex] = {
              name: null,
              id: null
            };
            oldHandleDeptId[realIndex] = res.deptId;
            $(faultTypeId).val('');
            $(faultTypeName).val('');
            $(handlePersonBox).val('')
            
            if(!res.deptId){
              $(faultTypeName).attr('disabled', true);
              $(handlePersonBox).attr('disabled', true);
              return
            }

            if(res.deptId !== workOrderDetail.businessDeptId){
              // $('#myorderHandle [name="no-required"]').each((index, item)=>{
              //   item.innerHTML = `&ensp;`
              //   item.classList.remove('required')
              // })
              $('#myorderHandle [name="fkFaultTypeName"]').attr('lay-verify', 'none');
              $('#myorderHandle #handlerPersonBox-resend [name="handleName"]').attr('lay-verify', 'none');
              $('#myorderHandle #handlerPersonBox-resend [name="fkUserId"]').attr('lay-verify', 'none');

              $('#myorderHandle #handlerPersonBox-newHandle [name="handleName"]').attr('lay-verify', 'none');
              $('#myorderHandle #handlerPersonBox-newHandle [name="fkUserId"]').attr('lay-verify', 'none');
              $('#myorderHandle [name="no-required"]').each((index, item)=>{
                // item.innerHTML = `*`
                // item.classList.add('required')
                item.style.display = 'none';
              })

            } else {
              $('#myorderHandle [name="no-required"]').each((index, item)=>{
                // item.innerHTML = `*`
                // item.classList.add('required')
                item.style.display = 'flex';
              })
              $('#myorderHandle [name="fkFaultTypeName"]').attr('lay-verify', 'required');
              $('#myorderHandle #handlerPersonBox-resend [name="handleName"]').attr('lay-verify', 'required');
              $('#myorderHandle #handlerPersonBox-resend [name="fkUserId"]').attr('lay-verify', 'required');
             
              $('#myorderHandle #handlerPersonBox-newHandle [name="handleName"]').attr('lay-verify', 'required');
              $('#myorderHandle #handlerPersonBox-newHandle [name="fkUserId"]').attr('lay-verify', 'required');

            }

            $(faultTypeName).attr('disabled', false)
            //清空处理人，允许编辑
            $(handlePersonBox).attr('disabled','false')
            treeSelect(res.deptId, realIndex);
          },
        }
        new $.selectPlug(item, options)
        
        treeSelect( orderDetail.businessDeptId, realIndex);
        oldHandleDeptId[realIndex] = orderDetail.businessDeptId
        oldFaultType[realIndex] = {
          name: orderDetail.fkFaultType,
          id: orderDetail.fkFaultTypeId
        }

        //绑定 报修科室失焦事件，如果id为空则置空输入框
        $(faultTypeName).bind('blur', ()=>{
          if(!(oldFaultType[realIndex] || {}).id && !$(faultTypeId)[0].value){
            $(faultTypeName)[0].value = '';
            $(handlePersonBox).val('')
            $(handlePersonBox).attr('disabled','true')
            //当 当前选中和旧数据相同 或者 只是搜索了但是没有选择的时候
          } else if( oldFaultType[realIndex].id ==  $(faultTypeId)[0].value ){
            $(faultTypeName)[0].value = oldFaultType[realIndex].name;
          }
        })

        //绑定未选择提醒事件
        $($(faultTypeName)[0].parentNode).funs('click', function(){
          let val = $('[name="businessDeptId"]', item).val();
          if(!val){
            layer.msg('请先选择处理科室')
          }
        })
        $(`.layui-form[index="${realIndex}"] [render="handlePersonBox"]`).funs('click',function(e){
          let val = $(faultTypeName).val();
          if(!val){
            layer.msg('请先选择业务类型')
          }
        })
      
      return 
    }
    //渲染报故障类型
    function treeSelect(deptId, index){
      layui.use(['zTreeSearch', 'trasen'], function(){
        var zTreeSearch = layui.zTreeSearch,
        trasen = layui.trasen;
        zTreeSearch.init(`[name="fkFaultTypeName"][index="${index}"]`, {
          url: common.url + '/ts-worksheet/faultType/getFaultTypeAllList/1/' + deptId,
          type: 'get',
          checkbox: false,
          condition: 'name',
          zTreeOnClick: function(treeId, treeNode) {
            if (treeNode) {
              $('[name="fkFaultTypeId"]', $(`.layui-form[index="${index}"]`)).val(treeNode.id);
              $('[name="fkFaultTypeName"]', $(`.layui-form[index="${index}"]`)).val(treeNode.fullPath);
              var arr = treeNode.getPath(),
                  path = '',
                  data = {}
              for(var i = 0; i < arr.length; i++){
                var item = arr[i]
                path += item.name
                if(i != arr.length - 1){
                  path += '>'
                }
              }
              data.fullPath = path
  
              //重新给之前选中的故障类型赋值
              oldFaultType[index] = {
                name: treeNode.fullPath,
                id: treeNode.id
              }
              
              renderHandleSelect(treeNode.id, index);
              $('[render="handlePersonBox"] input', $(`.layui-form[index="${index}"]`)).attr('disabled', false);
              trasen.setNamesVal($(`.layui-form[index="${index}"]`), data);
            }
          },
          callback: function(){
              oldFaultType[index] = {
                name: null,
                id: null
              }
              //清空id值
              $(`[name="fkFaultTypeId"][index="${index}"]`)[0].value = '';
              $('[render="handlePersonBox"] input', $(`.layui-form[index="${index}"]`)).val('')
              $('[render="handlePersonBox"] input', $(`.layui-form[index="${index}"]`)).attr('disabled', true)
          },
          ztreeInitFun(treeObj){
            nodes = treeObj.getNodes();
            nodes.forEach(item=>{
              treeObj.expandNode(item, true, false);
            })
          }
        });
      })
    }

    //渲染费用明细表格
    function initCostTable(){
      coastTable = new $.trasenTable('orderDetailCostTable', {
        url: common.url + '/ts-worksheet/wsCostList',
        // pager: '#orderDetailCostPager',
        mtype: 'get',
        shrinkToFit: true,
        sortname: 'create_time',
        sortorder: 'desc',
        postData: {
          workNumber: opt.data.workNumber
        },
        rowNum: 9999,
        colModel: [
          {
            label: '金额(元)',
            name: 'money',
            align: 'right',
            width: 100,
            fixed: true,
            sortable: false,
            resizable: false,
            formatter: function(cellvalue, options, rowObject){
              let labelStr = cellvalue.toLocaleString();
              if(labelStr.split(".").length >1){
                labelStr.split('.')[1].length == 1 ? (labelStr += '0') :  null;
              }
              labelStr.split(".").length == 1 ? (labelStr += '.00') : null;
              return labelStr
            }
          },
          {
            label: '费用描述',
            name: 'costDeion',
            formatter: function(cellvalue, options, rowObject){
              return (
                `<span 
                  class="table-action-item" 
                  data-index="${options.rowId}" 
                  name="previewCost" 
                  style="color: #5260ff; cursor: pointer;"
                >
                  ${cellvalue}
                </span>`
              )
            }
          },
          {
            label: '发生时间',
            name: 'costTime',
            align: 'center',
            width: 150,
            fixed: true,
            sortable: true,
            index: 'create_time',
            resizable: false
          },
          {
            label: '填报科室',
            name: 'fillDeptName',
            width: 80,
            fixed: true,
            sortable: false,
            resizable: false,
          },
          {
            label: '填报人',
            name: 'fillUser',
            width: 66,
            fixed: true,
            sortable: false,
            resizable: false,
          },
          {
            label: '填报时间',
            name: 'createTime',
            align: 'center',
            width: 150,
            fixed: true,
            sortable: false,
            resizable: false
          },
          {
            label: '附件数',
            name: 'fileCount',
            align: 'right',
            width: 45,
            fixed: true,
            sortable: false,
            resizable: false
          },
          {
            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
            name: '',
            sortable: false,
            width: 80,
            fixed: true,
            editable: false,
            align: 'center',
            title: false,
            classes: 'visible jqgrid-rownum ui-state-default',
            formatter: function(cell, opt, row){
              return  $.cookie('sso_user_code') == row.fillUserId
              ? `
                <span class="table-action-item" data-index="${opt.rowId}" name="edit">编辑</span>
                <span class="table-action-item" style="margin-left: 8px;" data-index="${opt.rowId}" name="delete">删除</span>
              `
              : '';
            }
          }
        ],
        loadComplete: function(res){
          let allMoney = 0;
          res.rows.forEach(item=>{
            allMoney += item.money
          })

          let moneyStr = allMoney.toLocaleString();
          if(moneyStr.split('.').length > 1){
            moneyStr.split('.')[1].length == 2 ? null : moneyStr +='0';
          }
          moneyStr.split('.').length == 1 ? moneyStr +='.00' : null;

          $('#myorderHandle #costDetail .cost-top span')[0].innerHTML = '填报经费共计 ' + moneyStr + '元';
        }
      })

      $('#myorderHandle #orderDetailCostTable .table-action-item').funs('click', function(e){
        let node = e.currentTarget,
        type = node.getAttribute('name'),
        rowId = node.getAttribute('data-index'),
        rowData = coastTable.getSourceRowData(rowId);
        
        switch(type){
          case 'edit':
            $.quoteFun('myHandleOrder/modules/costRegister',{
              data: Object.assign({}, rowData, { workNumber: opt.data.workNumber, faultDeion: opt.data.faultDeion }) ,
              ref: function(){
                coastTable.refresh();
              }
            })
            break;
          case 'delete':
            layer.confirm(
              '删除后将无法恢复，确定要删除该记录吗？',
              {
                btn: ['确定', '取消'],
                title: '提示',
                closeBtn: 0
              },
              function(confirmIndex){
                $.ajax({
                  url: '/ts-worksheet/wsCostDel',
                  type: 'post',
                  contentType: 'application/json',
                  data: JSON.stringify([rowData.pkWsCostId]),
                  success: function(res){
                    layer.close(confirmIndex)
                    if(!res.success){
                      layer.msg(res.message || '删除失败');
                      return
                    }
                    coastTable.refresh();
                  }
                })
              },
              function(){}
            )
            break;
          case 'previewCost':
            $.quoteFun('myHandleOrder/modules/previewCost',{
              data: Object.assign({}, rowData, { workNumber: opt.data.workNumber, faultDeion: opt.data.faultDeion })
            })
            break;
        }
      })
      $('#myorderHandle #costDetail [name="addCostRegistBtn"]').funs('click', function(e){
        $.quoteFun('myHandleOrder/modules/costRegister',{
          data: { workNumber: opt.data.workNumber, faultDeion: opt.data.faultDeion },
          ref: function(){
            coastTable.refresh();
          }
        })
      })
      $('#myorderHandle #costDetail [name="exportCostDetailBtn"]').funs('click', function(e){
        common.downFile({
          url: '/ts-worksheet/exportExcel/' + opt.data.workNumber
        })
      })
      
      let showAdd = ['2', '3', '4', '5'].indexOf(opt.data.peopleType) >= 0
      showAdd
        ? null
        : $('#myorderHandle #costDetail [name="addCostRegistBtn"]').css('display', 'none');
    }
    /*------------------------------事件绑定--------------------------------*/
    function bindOptionsBarEvent() {
      $('[name="handleWorkHours"]')[0].addEventListener("keypress",function(event){
        if(event.keyCode == 45){
          event.preventDefault();
        }
      });

      $('.handle-options-item-text')[currentIndex].className += ' handle-option-active-text'
      $('#handleOrderOptions').click(function (e) {
        var tar = e.target,
          clickIndex = tar.getAttribute('data-index')
        if (clickIndex == null || clickIndex == currentIndex || isNaN(clickIndex)) return
        
        $(`#myorderHandle .handle-options-item-text[data-index="${currentIndex}"]`)[0].className = 'handle-options-item-text'
        $(`#myorderHandle .handle-options-item-text[data-index="${clickIndex}"]`)[0].className += ' handle-option-active-text'
        currentIndex = clickIndex;
        $('#myorderHandle #costDetail').css('display', 'none');
        $('#baseInfoWrap').css('display', 'none');
        $('#timeLine').css('display', 'none');
        $('#resource').css('display', 'none');
        switch (clickIndex) {
          case '0':
            $('#baseInfoWrap').css('display', 'block');
            break;
          case '1':
            $('#resource').css('display', 'block');
            break;
          case '2':
            var timeNode = $('#timeLine');
            if (!timeNode.length) renderOperateLog();
            $('#timeLine').css('display', 'block');
            break;
          case '3':
            $('#myorderHandle #costDetail').css('display', 'block');
        }
      })
    }
    function bindOptionEvent(form) {
      renderBottomForm(currentSelected, form)
      form.on('radio(responesType)', function (data) {
        var value = parseInt(data.value)
        if (currentSelected == value) return
        renderBottomForm(value, form)
        currentSelected = value
      })
    }
    
    //渲染操作日志
    function renderOperateLog() {
      $('#operatingInfoContent').append(
        "<ul class='timeLine operate-log-info' id='timeLine'></ul>"
      )
      for (var i = 0; i < logData.length; i++) {
        var item = logData[i]
        let html = `
          <li>
            <div class="dot"></div>
            <div class="dot-line"></div>
            <span class="timeline-operate">
              【${item.taskNameVaule}】
              <span>操作人：${item.createByName}</span>
              <span style="margin-left: 5px;">${item.createTime}</span>
            </span>
        `;
        if(!item.takeRemark){
          html += '</li>';
          $('#timeLine').append(html);
          continue;
        }

        const logRemark = item.takeRemark.split('#cut@');
        logRemark.forEach(remark => {
          if(remark){
            html += `<span>&ensp;${remark}</span>`
          }
        })

        if(item.files && item.files.length){
          let txtFiles = item.files.filter(
            item =>
              common.isDoc(item.fkFileName) ||
              item.fkFileName.toLowerCase().indexOf('.mhtml') >= 0
          ),
          picFiles = item.files.filter(item=>common.isImg(item.fkFileName));

          let picHtml = `<div style="display:flex; flex-wrap: wrap; width: 352px;">`
          picFiles.forEach(pic=>{
            picHtml += `
              <div class="operate-log-file-item operate-log-pic-item" style="position: relative;">
                <img src="${pic.fileUrl}" style="height: 80px; width: 80px; margin-right: 8px; margin-bottom: 8px;"/>
                <div class="flex" 
                  style="
                    justify-content: center;
                    position: absolute; 
                    top: 0; 
                    background: rgba(0, 0, 0, 0.15); 
                    z-index: 2; 
                    width: 80px; 
                    height: 80px; 
                    align-items: center; 
                    opacity: 0;">
                  <span
                    class="preview layui-icon layui-icon-search"
                    data-index="${i}"
                    data-fileUrl="${pic.fileUrl}"
                    data-fileName="${pic.fkFileName}"
                    style="color: #fff; cursor: pointer; display:inline-block; height: 16px"
                  ></span>
                  
                  <a
                    style="margin-left: 8px; color: #FFF; height: 16px; display: inline-block;" 
                    href="/ts-basics-bottom/fileAttachment/downloadFile/${pic.fkFileId}"
                    >
                    <i class="layui-icon layui-icon-triangle-d"></i>
                  </a>
                </div>
              </div>
            `
          })
          picHtml +="</div>"
          html += picHtml;

          txtFiles.forEach(file=>{
            html += `
              <div class="flex operate-log-file-item">
                <div style="overflow:hidden; white-space: nowrap; text-overflow: ellipsis;">
                  ${file.fkFileName}
                </div>
              `
              + (file.fkFileName.toLowerCase().indexOf('.mhtml') >= 0 
                  ? '' 
                  : `
                    <div 
                      class="preview"
                      data-index="${i}"
                      data-fileUrl="${file.fileUrl}"
                      data-fileName="${file.fkFileName}"
                      style="margin-left: 8px; flex-shrink: 0; color: #5260FF; cursor: pointer;"
                      >
                      预览
                    </div>
                  `
              )
              + `<a href="${file.fileUrl}" style="margin-left: 8px; flex-shrink: 0; color: #5260FF;">下载</a>
              </div>
            `
          })
        }
        html += '</li>';
        $('#timeLine').append(html);

        $('#myorderHandle .operate-log-file-item .preview').off("click").bind('click', function(e){
          let node = e.target,
            index = node.getAttribute('data-index'),
            fileName = node.getAttribute('data-fileName'),
            fileUrl = node.getAttribute('data-fileUrl');

          if(common.isDoc(fileName)){
            common.viewerDocBase(fileUrl, fileName);
          } else if(common.isImg(fileName)){
            let picFiles = logData[index].files.filter(item=>common.isImg(item.fkFileName));
            let list = picFiles.map(item=> Object.assign({}, item, { fileUrl: item.fileUrl, fileName: item.fkFileName}));
            common.viewerImg( list, fileUrl, 'hr');
          }
        })
      }
    }
    /*----------------------------*-----------------------------*/
    

    //渲染资源文件
    function renderResource(){
        wsFileOutVoList.length
        ? ($(
            '#myorderHandle .handle-options-item-text[data-index="1"]'
          )[0].innerHTML = `资源文件(${wsFileOutVoList.length})`)
        : null;
        let picList = [],
            txtList = [],
            audio = []

        if( !opt.data ){ return }

        for( let item of (wsFileOutVoList||[]) ){
            let newItem = Object.assign({}, item, { fileName: item.fkFileName });

            common.isDoc(newItem.fileName) || newItem.fileName.toLowerCase().indexOf('.mhtml') >= 0 ? txtList.push(newItem) :
              common.isImg(newItem.fileName) ? picList.push(newItem) :
                item.fileSuffix == 'wav' ? audio.push(item) : null;
        }

        for( let i=0; i<picList.length; i++ ){
           let isComeFromFlow = picList[i].fileUrl.indexOf('/ts-basics-bottom') >=0;
            $('#myorderHandle #resource [name="resource-pic"]').append(
                "<div class='upload-box'>" +
                    "<img width='100%' height='100%' src='" + 
                      (isComeFromFlow
                        ? "/ts-basics-bottom/fileAttachment/downloadFile/"
                        : "/ts-document/attachment/downloadFile/")
                        + `${picList[i].fkFileId}' />` +
                    "<span class='upload-list-item-actions'>" +
                        "<span class='upload-item-action' type='preview' index='"+ i +"'>" + "<i class='layui-icon layui-icon-search' style='pointer-events: none;'></i>" + "</span>" +
                        " <a href='" + 
                            (isComeFromFlow
                              ? "/ts-basics-bottom/fileAttachment/downloadFile/"
                              : "/ts-document/attachment/downloadFile/")
                              + `${picList[i].fkFileId}' >` +
                            "<span class='upload-item-action' type='download'>" +
                                "<i class='layui-icon layui-icon-triangle-d' style='pointer-events: none;'></i>" +
                            "</span>" +
                        "</a> " +
                    "</span>" +
                "</div>"
            );
        }

        for( let i=0; i<txtList.length; i++ ){
            let isComeFromFlow = txtList[i].fileUrl.indexOf('/ts-basics-bottom') >=0;
            $('#myorderHandle #resource [name="resource-file"]').append(
                "<div style='display: flex; align-items: center; position:relative;'>" +
                    "<span  name='viewerFile' fileId='"+ txtList[i].fkFileId + "' fileUrl='"+ txtList[i].fileUrl +"' style='cursor: pointer;'  title="+  txtList[i].fkFileName  + " >"+ txtList[i].fkFileName +"</span>"+
                    "<a href='" + 
                      (isComeFromFlow
                        ? "/ts-basics-bottom/fileAttachment/downloadFile/"
                        : "/ts-document/attachment/downloadFile/")
                    + `${txtList[i].fkFileId}' >下载</a>` +
                "</div>"
            )
        }
        for( let i=0; i<audio.length; i++ ){
            $('#myorderHandle #resource [name="resource-audio"]').append(
                "<div class='audio-item'>" +
                    "<audio controls src='http://" + window.location.host + audio[i].fileUrl +"'></audio>"+
                    // "<a href='http://" + window.location.host +  audio[i].fileUrl +"'>下载</a>" +
                "</div>"
            )
        }

        if( !audio.length ){
            $('#myorderHandle #resource [name="resource-audio"]').append(
                "<div>暂无资源</div>"
            )
        }
        if( !picList.length && !txtList.length ){
            $('#myorderHandle #resource [name="resource-pic"]').append(
                "<div>暂无资源</div>"
            );
        }

        //绑定图片预览事件
        $('#myorderHandle #resource .upload-item-action[type="preview"]').bind('click', (e)=>{
          let index = e.target.getAttribute("index"),
          list = picList.map(item => Object.assign({}, item, { fileUrl: item.fileUrl }))
          
          list = list.map(item => Object.assign({}, item, {
            fileUrl: item.fileUrl.indexOf('ts-basics-bottom') >= 0 ? item.fileUrl : 
            `/ts-document/attachment/downloadFile/${item.fkFileId}`
          }))
          common.viewerImgBase(list, list[index].fileUrl);
        })

        //绑定文件预览事件
        $('#myorderHandle #resource [name="viewerFile"]').bind('click', (e)=>{
          let node = e.target,
            fileUrl = node.getAttribute('fileUrl'),
            fileName = node.innerText,
            fileItem =  txtList.find(item=>item.fileUrl == fileUrl) || {};
          
          if(fileName.toLowerCase().indexOf('.mhtml')>=0){
            layer.msg('不支持预览的文件类型');
            return
          }
          fileUrl.indexOf('ts-basics-bottom') >= 0
            ? common.viewerDocBase(fileUrl, fileName)
            : common.viewerDoc2(fileItem.fkFileId, fileName);
        })
    }

    //处理删除多人协助事件
    function handleDeleteAssite(e){
      let node = e.target,
      parent = node.parentNode;

      $(parent).remove();

      let childList = $('#myorderAddAssistItem').children();

      if(childList.length==1){
        $('.delete-icon', childList[0]).remove();
      }
    }

    //处理文件上传
    function handleHandleUPloadFile(e){
      let files = this.files,
        upLoadFileList = [],
        errorTypeList = [];
      for (let i = 0; i < files.length; i++) {
        let item = files[i];

        if (
          !common.isImg(item.name) &&
          !common.isDoc(item.name) &&
          item.name.toLowerCase().indexOf('.mhtml') == -1
        ) {
          errorTypeList.push(item.name);
          continue;
        }

        upLoadFileList.push(item);
      }

      if (!upLoadFileList.length) {
        layer.msg(
          errorTypeList.join(',') +
            '文件类型错误，请选择正确的上传文件类型',
          { icon: 2 }
        );
        $('#myorderHandle #upload')[0].value = null;
        return;
      }

      for (let i = 0; i < upLoadFileList.length; i++) {
        let item = upLoadFileList[i];

        let formData = new FormData();
        formData.append('file', item);
        upLoadAndRenderFile(formData, item.name);
      }
      $('#myorderHandle #upload')[0].value = null;
    }
    function upLoadAndRenderFile(formData, name){
      $.ajax({
        contentType: false,
        cache: false,
        processData: false,
        url: API.upload,
        type: 'POST',
        data: formData,
        async: false,
        success: function (data) {
          if( !data.success ){
              layer.msg(name + ',' + data.message||'上传出错啦', { icon:2 } );
              return
          }
  
          let object = (data.object && data.object[0]) || {},
              item = {
                fileUrl: object.filePath,
                fkFileId: object.fileId,
                fkFileName: object.fileRealName,
                fileName: object.fileRealName,
              }
          common.isDoc(item.fileName) || item.fileName.toLowerCase().indexOf('.mhtml') >= 0 ? txt.push(item) :
              common.isImg(item.fileName) ? pic.push(item) : null;
          
          $('#myorderHandle .upload-box-container [name="upload-file"]').append(
            `
              <div class="flex">
                <span>
                  <i class="fa fa-paperclip" style="transform: rotate(90deg);"></i>
                  <span style="flex:1; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">${item.fileName}</span>
            ` +
            (
              item.fileName.toLowerCase().indexOf('.mhtml') >= 0 ? ''
              : `
                    <span 
                      class="action-item" 
                      name="preview"
                      data-file-name="${item.fileName}"
                      data-fileUrl="${item.fileUrl}"
                      style="margin-left: 8px; color: #5260FF; flex-shrink: 0;"
                    >
                      预览
                    </span>
              `
            )
  
            + `
                  <a 
                    class="action-item" 
                    href="/ts-basics-bottom/fileAttachment/downloadFile/${item.fkFileId}"
                    style="margin-left: 8px; color: #5260FF; flex-shrink: 0;"
                  >
                    下载
                  </a>
  
                  <span
                    class="delete action-item" 
                    name="deleteFile"
                    style="margin-left: 8px; color: #E24242; flex-shrink: 0;"
                    data-fkFileId="${item.fkFileId}"
                    data-file-name="${item.fkFileName}"
                  >
                    删除
                  </span>
                </span>
              </div>
            `
          )
          $('#myorderHandle .upload-box-container [name="upload-file"] [name="preview"]').off("click").bind('click', function(e){
            let node = e.target,
              fileName = node.getAttribute('data-file-name'),
              fileUrl = node.getAttribute('data-fileUrl')
  
            if(common.isDoc(fileName)){
              common.viewerDocBase(fileUrl, fileName);
            } else if(common.isImg(fileName)){
              let list = pic.filter(item=>item).map(item => Object.assign({}, item, { fileUrl: item.fileUrl }));
              common.viewerImg( list, fileUrl, 'hr');
            }
          })
  
          $('#myorderHandle .upload-box-container [name="upload-file"] [name="deleteFile"]').off("click").bind('click', function(e){
            let node = e.target,
              parent = node.parentNode.parentNode,
              fileName = node.getAttribute('data-file-name'),
              fkFileId = node.getAttribute('data-fkFileId')
  
            if(common.isDoc(fileName)){
              let index = txt.findIndex(item=>item.fkFileId == fkFileId);
              if(index>=0){
                txt[index] = undefined;
                $(parent).remove();
              }
            } else if(common.isImg(fileName)){
              let index = pic.findIndex(item=>item.fkFileId == fkFileId);
              if(index>=0){
                pic[index] = undefined;
                $(parent).remove();
              }
            }
          })
        },
        error: function () {
          layer.msg(name + ',' + '上传失败');
        }
      });
    }
  }
})
