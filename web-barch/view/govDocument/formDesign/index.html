<link rel="stylesheet" href="/view/process/formtable/formdesign/index.css?1" />
<style>
    .editor_body {
        margin: 0 !important;
        padding-top: 40px;
    }
    .formDesignTop {
        user-select: none;
        position: absolute;
        top: -33px;
        height: 24px;
        left: 50%;
        width: 625px;
        transform: translateX(-50%);
    }
    .formDesignTop li {
        display: inline-block;
        line-height: 24px;
        position: relative;
        margin-right: 30px;
    }
    .formDesignTop li::before {
        position: absolute;
        left: -25px;
        top: 2px;
        content: '';
        height: 18px;
        width: 18px;
        border: 1px solid #ccc;
        border-radius: 50%;
    }
    .formDesignTop li.sel::after {
        position: absolute;
        left: -21px;
        top: 6px;
        content: '';
        height: 12px;
        width: 12px;
        border-radius: 50%;
        background-color: #ccc;
    }
    .formDesignTop li .line {
        display: inline-block;
        vertical-align: top;
        border-bottom: 1px solid #ccc;
        width: 60px;
        margin-top: 12px;
    }
</style>
<div id="formDesign" class="layui-tab-content">
    <div class="formDesignTop">
        <ul>
            <li class="sel">
                表单设计
                <div class="line"></div>
            </li>
            <li>
                手机端设置
                <div class="line"></div>
            </li>
            <li>
                打印模板设置
                <div class="line"></div>
            </li>
            <li>办理查阅设置</li>
        </ul>
    </div>
    <div class="layui-tab-content layui-content-box" style="padding: 8px; background-color: #f4f4f4">
        <form class="layui-form" id="formDesignForm" style="padding: 16px; background-color: #fff">
            <div class="layui-row">
                <input type="text" class="none" name="tableName" />
                <div class="layui-col-md4">
                    <label class="layui-form-label">
                        <span class="required">*</span>
                        表单名称：
                    </label>
                    <div class="layui-input-block" style="width: auto">
                        <input type="text" name="templateName" class="layui-input" autocomplete="off" lay-verify="required" />
                    </div>
                </div>
                <div class="layui-col-md4">
                    <label class="layui-form-label">
                        <span class="required">*</span>
                        表单分类：
                    </label>
                    <div class="layui-input-block" style="width: auto">
                        <input type="text" name="classifyName" class="layui-input" autocomplete="off" lay-verify="required" readonly />
                        <input type="text" name="classifyId" class="layui-input none" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-col-md4">
                    <label class="layui-form-label">
                        <span class="required">*</span>
                        公文类型：
                    </label>
                    <div class="layui-input-block" style="width: auto">
                        <select name="templateType" lay-filter="officalDocType" lay-search>
                            <option value="1">发文</option>
                            <option value="2">收文</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-md12">
                    <label class="layui-form-label">
                        <span class="required">*</span>
                        表单描述：
                    </label>
                    <div class="layui-input-block" style="width: auto">
                        <textarea name="templateCode" class="layui-textarea" autocomplete="off" style="min-height: 60px" lay-verify="required"></textarea>
                    </div>
                </div>
            </div>
            <button class="none" lay-submit lay-filter="customerFormListFormSaveNext"></button>
            <button class="none" lay-submit lay-filter="customerFormListFormSave"></button>
        </form>
        <div class="formDesignEditor">
            <div id="formItemAttr">
                <form class="layui-form">
                    <div class="layui-content-box layui-tab layui-tab-oa-nav">
                        <ul class="layui-tab-title">
                            <li class="layui-this">字段属性</li>
                        </ul>
                        <div class="layui-tab-content">
                            <input type="text" class="none" name="showName" />
                            <div class="layui-tab-item layui-show">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">
                                        <span class="required">*</span>
                                        字段选择:
                                    </label>
                                    <div class="layui-input-block">
                                        <select name="fieldName" lay-filter="formItemFieldName" lay-search></select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">字段名称:</label>
                                    <div class="layui-input-block">
                                        <input name="showName" class="layui-input" />
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">提示文字:</label>
                                    <div class="layui-input-block">
                                        <textarea name="promptText" class="layui-textarea" style="min-height: 60px"></textarea>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否必填:</label>
                                    <div class="layui-input-block">
                                        <label class="labelCheckbox" style="line-height: 24px; display: block">
                                            <input type="checkbox" value="Y" name="isMust" autocomplete="off" class="self-checkbox" />
                                            <i class="self-checkbox-icon"></i>
                                        </label>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否只读:</label>
                                    <div class="layui-input-block">
                                        <label class="labelCheckbox" style="line-height: 24px; display: block">
                                            <input type="checkbox" value="Y" name="isReadonly" autocomplete="off" class="self-checkbox" />
                                            <i class="self-checkbox-icon"></i>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="archivesTabBtn">
                        <button class="layui-btn" lay-submit lay-filter="formItemAttrSave">确定</button>
                        <button class="layui-btn layui-btn-primary" id="resetFormItemAttr">取消</button>
                    </div>
                </form>
            </div>
            <div class="editorContent">
                <textarea id="editContent" name="editContent" style="width: 1000px; height: 480px; visibility: hidden; height: 100%">
                    <table style="border-collapse: collapse; width: 100%;" border="1" data-mce-selected="1"><tbody><tr><td style="width: 49.2163%;"><br></td><td style="width: 49.2693%;"><br></td></tr><tr><td style="width: 49.2163%;"><br></td><td style="width: 49.2693%;"><br data-mce-bogus="1"></td></tr></tbody></table>
                </textarea>
            </div>
        </div>
    </div>
    <div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-normal none" id="syncPrint">同步打印模板</button>
        <button type="button" class="layui-btn layui-btn-normal" id="customerFormListFormSaveNext">保存，下一步</button>
        <button type="button" class="layui-btn layui-btn-normal" id="customerFormListFormSave">保存</button>
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="close">关闭</a>
    </div>
</div>
