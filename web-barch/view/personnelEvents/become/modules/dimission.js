'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                laydate = layui.laydate,
                upload = layui.upload,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;
            var businessId = randomString();
            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['850px', '600px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {

                    if(opt.data && opt.data.businessId!=null && opt.data.businessId!='') {
                        businessId = opt.data.businessId;
                    }

                    if (opt.type == 'view') {
                        $('#dimissionFormHtml input').each(function () {
                            $(this).attr('disabled', 'disabeld');
                            
                        })
                        $('#dimissionFormHtml select').each(function () {
                            $(this).attr('disabled', 'disabeld');
                        })
                        $("#dimissionFormAddSub").hide();
                    } else {
                        new $.selectPlug('#employeeAddSelBox', {
                            url: common.url + '/ts-basics-bottom/employee/getEmployeeListBybeCome',
                            searchType: 'json',
                            textName: 'employeeName',
                            valName: 'employeeNo',
                            inpValName: 'employeeCode',
                            inpTextName: 'employeeName',
                            callback: function (res) {
                                if (res) {
                                    $('#dimissionForm .orgName').val(res.orgName);
                                    $('#dimissionForm .orgId').val(res.orgId);
                                    $('#dimissionForm .birthday').val(res.birthday);
                                    $('#dimissionForm .jobCategory').val(res.personalIdentityName);
                                    $('#dimissionForm .entryDate').val(res.entryDate);
                                    $('#dimissionForm .highestProfessional').val(res.jobtitleCategoryName);
                                    $('#dimissionForm .education').val(res.educationTypeName);
                                }
                            },
                        });

                    }

                    trasen.setNamesVal($('#dimissionForm'), opt.data);

                    var publicUpload = $.publicUpload($("#dimissionFileupload"), {
                        formId: "dimissionUploadFileForm",
                        fileTableEl: $("#infoInfoDynamiccheckFilefileTableId"),
                        deleted: true,
                        width: 800,
                        formData: {
                            businessId: businessId
                        },
                    });


                    form.render();
                },
            });

            laydate.render({
				elem: '#dimissionDate',
				trigger: 'click'
			});
            // 保存
            form.on('submit(dimissionFormAddSub)', function (data) {
                data.field.businessId = $("#dimissionUploadFileForm input[name='businessId']").val();
                var d = data.field;
                var url = '/ts-hrms/api/beCome/save';
                $.ajax({
                    type: 'post',
                    url: common.url + url,
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify(d),
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('保存成功');
                            opt.ref();
                        } else {
                            layer.msg(res.message || '保存失败');
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
                return false;

            });


            //切换 tab 
            $('#dimissionFormHtml .oa-nav .oa-nav_item').off('click').on('click', function () {
                $(this).addClass('active').siblings().removeClass('active');
                if ($(this).index() == 0) {
                    $('#dimissionFormHtml #dimissionForm').removeClass('none');
                    $('#dimissionFormHtml #dimissionUploadFileForm').addClass('none');
                } else {
                    $('#dimissionFormHtml #dimissionForm').addClass('none');
                    $('#dimissionFormHtml #dimissionUploadFileForm').removeClass('none');
                }
            });

            $('#dimissionFormHtml #dimissionFormAddSub').off('click').on('click', function () {
                $('#dimissionFormHtml [lay-filter="dimissionFormAddSub"]').trigger('click');
            })

            // 取消
            $('#dimissionFormHtml #dimissionFormClose')
                .off('click')
                .on('click', function () {
                    layer.closeAll();
                });
        });
    };
});
