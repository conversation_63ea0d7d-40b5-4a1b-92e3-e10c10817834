"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function () {
        layui.use(['form', 'laydate', 'upload', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;

            // 开始时间
			laydate.render({
			    elem: '#outRecordDiv #outstartTimeSerach',
			    type: 'date',
			    range: '~',
			    done: function (value, date, endDate) {
			        var dateArr = value.split(' ~ ');
			        $("#outStartTime").val(dateArr[0])
			        $("#outEndTime").val(dateArr[1])
			        refreshTable();
			    }
			});
			
			// 开始时间
			laydate.render({
			    elem: '#outRecordDiv #outEndTimeSerach',
			    type: 'date',
			    range: '~',
			    done: function (value, date, endDate) {
			        var dateArr = value.split(' ~ ');
			        $("#outEndStartTime").val(dateArr[0])
			        $("#outEndEndTime").val(dateArr[1])
			        refreshTable();
			    }
			});

            var trasenTable = new $.trasenTable("grid-table-outRecordTable", {
                url: common.url + '/ts-hrms/api/outRecord/list',
                pager: 'grid-pager-outRecordPage',
                mtype: 'get',
                shrinkToFit: true,
                rownumbers: true,
                colModel: [{
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        name: 'opt',
                        width: 40,
                        title: false,
                        formatter: function (cellvalue, options, row) {
                            var btnStr = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            btnStr += '<button style="color: #5260ff;cursor:pointer;" class="moralityResultEdit" id="outRecordTableEditor" row-id="' + options.rowId + '">编辑</button>';
                            if ("1" == row.status) {
                                btnStr += '<button style="color: #5260ff;cursor:pointer;" class="moralityResultEdit" id="outRecordTableCancel" row-id="' + options.rowId + '">取消</button>';
                            } else {
                                btnStr += '<button style="color: #5260ff;cursor:pointer;" class="moralityResultEdit" id="outRecordTableRecovery" row-id="' + options.rowId + '">恢复</button>';
                            }

                            btnStr += '<button style="color: #5260ff;cursor:pointer;" class="moralityResultCancel" id="outRecordTableDel" row-id="' + options.rowId + '">删除</button>'
                            btnStr += '</div></div>';
                            return btnStr;
                        }
                    },
                    {
                        label: '申请人',
                        name: 'applyUserNameText',
                        index: '',
                        width: 110,
                        editable: false,
                        sortable: false,
						formatter: function (cellvalue, options, rowObject) {
                            let iconSpan = '';
                            if (rowObject.workId) {
                                iconSpan = `<img 
                                              class="see-work-info"
                                              wfInstId=${rowObject.workId}
                                              style="height: 22px;vertical-align: middle;cursor: pointer;float: left;margin: 0 4px;" 
                                              src="/static/img/other/from_process.svg"/>`;
                            };
							return `
                                <div>
                                    ${iconSpan}
                                    <span style="color: #5260ff;cursor:pointer;" class="outRecordDetails">${rowObject.applyUserName}</span>
                                </div>
                            `;
                    },
                    },
                    { label: '身份证号', name: 'identityNumber', index: 'identity_number', width: 140, editable: false,align:'center' },
                    {
                        label: '申请人部门',
                        name: 'applyDeptName',
                        index: 'apply_dept_name',
                        width: 150,
                        editable: false,
                        align: 'center'
                    },
					{
					    label: '岗位',
					    name: 'jobs',
					    index: 'jobs',
					    width: 100,
					    editable: false,
					    align: 'center'
					},
                    {
                        label: '状态',
                        name: 'status',
                        index: 'status',
                        width: 80,
                        editable: false,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            if ("1" == cellvalue) {
                                return "正常"
                            } else {
                                return "<span style='color:red'>已取消</span>"
                            }
                        }
                    },
                    {
                        label: '外出类型',
                        name: 'outType',
                        index: 'out_type',
                        width: 120,
                        editable: false,
                        align: 'center'
                    },
                    {
                        label: '省内或省外',
                        name: 'provinceType',
                        index: 'province_type',
                        width: 100,
                        hidden: "csjkyy" == common.globalSetting.orgCode,
                        editable: false,
                        align: 'center'
                    }, 
                    {
                        label: '外出开始时间',
                        name: 'startTime',
                        index: 'start_time',
                        width: 150,
                        editable: false,
                        align: 'center'
                    },
                    {
                        label: '外出结束时间',
                        name: 'endTime',
                        index: 'end_time',
                        width: 150,
                        editable: false,
                        align: 'center'
                    },
                    {
                        label: '外出天数',
                        name: 'outDays',
                        index: 'out_days',
                        width: 80,
                        editable: false,
                        align: 'center'
                    },
                    {
                        label: '外出地点',
                        name: 'outAddress',
                        index: 'out_address',
                        width: 200,
                        editable: false,
                        align: 'left'
                    },
                    {
                        label: '外出事由',
                        name: 'outRemark',
                        index: 'out_remark',
                        width: 300,
                        editable: false,
                        align: 'left'
                    },
                    {
                        label: 'id',
                        name: 'id',
                        hidden: true
                    },
                    {
                        label: 'employeeId',
                        name: 'employeeId',
                        hidden: true
                    },
                    {
                        label: 'jobs',
                        name: 'jobs',
                        hidden: true
                    },
                    {
                        label: 'startTime',
                        name: 'startTime',
                        hidden: true
                    },
                    {
                        label: 'endTime',
                        name: 'endTime',
                        hidden: true
                    },
                    {
                        label: 'fileId',
                        name: 'fileId',
                        hidden: true
                    },
                    {
                        label: 'applyUser',
                        name: 'applyUser',
                        hidden: true
                    },
                    {
                        label: 'applyUserName',
                        name: 'applyUserName',
                        hidden: true
                    },
                    {
                        label: 'realStartTime',
                        name: 'realStartTime',
                        hidden: true
                    },
					{
					    label: 'realEndTime',
					    name: 'realEndTime',
					    hidden: true
					}
                ],
                // queryFormId: 'outRecordQueryForm',
                buidQueryParams: function () {
                    var search = $('#outRecordQueryForm').serializeArray();
                    var opt = $('#outRecordMoreSearchForm').serializeArray();
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                }
            });

            $("#grid-table-outRecordTable").jqGrid('setLabel', '0', '序号', 'labelstyle');
			
			// if("csjkyy" == common.globalSetting.orgCode){//长沙经开医院隐藏省外省内
				//  $('#grid-table-outRecordTable').setGridParam().hideCol('provinceType');
			// }

            // 刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            $("#outRecordDiv .outRecordTop .oa-nav_item").off("click").on("click", function () {
                $(this).addClass("active").siblings().removeClass("active");
                $("#outType").val($(this).attr("data-value"));
                refreshTable();
            })



            $('#outRecordDiv').off('click', '.see-work-info').on('click', '.see-work-info', function () {
                const businessId = $(this).attr('wfInstid');

                $.ajax({
                    type: "get",
                    async: false,
                    contentType: "application/json; charset=utf-8",
                    url: common.url + `/ts-workflow/workflow/wfInst/info/businessId/${businessId}`,
                    success: function(res) {
                        if (res.statusCode === 200 &&res.success) {
                            const { 
                                workflowNo,
                                businessId,
                                wfInstanceId:workflowInstId
                            } = res.object;
                            let url = encodeURI('/view-new/processView/see/index.html?' + 'workflowNo=' + workflowNo + '&businessId=' + businessId + '&wfInstanceId=' + workflowInstId + '&currentStepNo=end' + '&role=deal&type=see');

                            var a = document.createElement('a');
                            document.body.appendChild(a);
                            a.href = url;
                            a.target = '_blank';
                            a.click();
                            document.body.removeChild(a);
                        }
                    }
                });
            });


            //时间控件
            // laydate.render({
            //     elem: '#outRecordTime',
            //     range: '~',
            //     trigger: 'click',
            //     showBottom: true,
            //     done: function (value, date, endDate) {
            //         var dateArr = value.split(' ~ ');
            //         $('#outStartTime').val(dateArr[0]);
            //         $('#outEndTime').val(dateArr[1]);
            //     },
            // });

            // 查询
            form.on('submit(outRecordSearch)', function (data) {
                refreshTable();
            });

            // 查询
            $('#outRecordMoreSearchFormResetSearch').funs('click', function () {
                refreshTable();
            });

            // 重置
            form.on('submit(outRecordReset)', function (data) {
                $("#outRecordQueryForm")[0].reset();
                $("#outRecordMoreSearchForm")[0].reset();
                // $('#outStartTime').val('');
                // $('#outEndTime').
				$("#outStartTime").val("");
				$("#outEndTime").val("");
				$("#outEndStartTime").val("");
				$("#outEndEndTime").val("");
                form.render();
                refreshTable();
            });

            //重置
            $('#outRecordMoreSearchFormReset').funs('click', function () {
                $("#outRecordQueryForm")[0].reset();
                $("#outRecordMoreSearchForm")[0].reset();
                // $('#outStartTime').val('');
                // $('#outEndTime').val('');
				$("#outStartTime").val("");
				$("#outEndTime").val("");
				$("#outEndStartTime").val("");
				$("#outEndEndTime").val("");
                form.render();
                refreshTable();
            });

            //详情
            $('body').off('click', '.outRecordDetails').on('click', '.outRecordDetails', function () {
                var data = trasenTable.getSelectRowData();
                if (data === null) {
                    trasen.info('请选择一条需要查看的数据!');
                    return false;
                } else {
                    $.quoteFun('personnelEvents/outRecord/modules/addOutRecord', {
                        data: data,
                        title: '详情',
                        details: true,
                        ref: refreshTable
                    });
                }
            });

            // 新增
            $("#outRecordDiv").off("click", "#outRecordTableAdd").on("click", "#outRecordTableAdd", function () {
                $.quoteFun('personnelEvents/outRecord/modules/addOutRecord', {
                    title: '新增',
                    ref: refreshTable
                });
            });

            // 编辑
            $("#outRecordDiv").off("click", "#outRecordTableEditor").on("click", "#outRecordTableEditor", function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                $.quoteFun('personnelEvents/outRecord/modules/addOutRecord', {
                    title: '编辑',
                    data: rowData,
                    ref: refreshTable
                });
            });

            //取消
            $("#outRecordDiv").off("click", "#outRecordTableCancel").on("click", "#outRecordTableCancel", function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                rowData.status = 2;
                layer.confirm('确定取消该条记录吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/api/outRecord/update",
                        data: JSON.stringify(rowData),
                        success: function (res) {
                            if (res.success) {
                                refreshTable();
                                layer.closeAll();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                }, function () {});
            });

            //恢复
            $("#outRecordDiv").off("click", "#outRecordTableRecovery").on("click", "#outRecordTableRecovery", function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                rowData.status = 1;
                layer.confirm('确定恢复该条记录吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/api/outRecord/update",
                        data: JSON.stringify(rowData),
                        success: function (res) {
                            if (res.success) {
                                refreshTable();
                                layer.closeAll();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                }, function () {});
            });

            // 删除
            $("#outRecordDiv").off("click", "#outRecordTableDel").on("click", "#outRecordTableDel", function () {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.confirm('确定要删除该条记录吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/api/outRecord/delete/" + rowData.id,
                        success: function (res) {
                            if (res.success) {
                                refreshTable();
                                layer.closeAll();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                }, function () {});
            });

            //导出
            $('.expotOutRecordBut').funs('click', function () {
                var queryData = trasenTable.oTable.getGridParam('postData');
                trasenTable.refresh();
                var url = common.url + '/ts-hrms/api/outRecord/export?';
                var exportParam = '';
                for (var key in queryData) {
                    exportParam += key + '=' + queryData[key] + '&';
                }

                // return false;
                location.href = url + exportParam;
            });

        });

    }
});