<style>
  #orderInfoDeskDiv {
    height: 100%;
  }
  #orderInfoDeskDiv input:-internal-autofill-previewed,
  #orderInfoDeskDiv input:-internal-autofill-selected {
    transition: background-color 5000s ease-out 0.5s;
  }

  #orderInfoDeskDiv .order-info-desk-box {
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px #e6eaf0;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  #orderInfoDeskDiv .info-desk-font {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 20px;
  }

  #orderInfoDeskDiv .flex {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
  }

  #orderInfoDeskDiv .info-desk-status-bar {
    position: relative;
    display: flex;
    width: 100%;
    height: 48px;
  }

  #orderInfoDeskDiv .status-bar-time {
    color: #333333;
    padding-top: 14px;
    padding-left: 16px;
    font-weight: 400;
  }

  #orderInfoDeskDiv .status-bar-text {
    color: #666;
    padding-top: 14px;
    padding-left: 23px;
    font-weight: 400;
  }

  #orderInfoDeskDiv .status-item-wrap {
    margin-top: 14px;
    margin-left: 8px;
  }

  #orderInfoDeskDiv .status-item {
    margin-right: 24px;
    cursor: default;
  }
  #orderInfoDeskDiv .status-item:nth-child(2) {
    cursor: pointer;
  }

  #orderInfoDeskDiv .status-item-title {
    margin-right: 8px;
  }

  #orderInfoDeskDiv .status-item-num-red {
    width: 32px;
    height: 20px;
    background: #ffeaea;
    border-radius: 10px;
    text-align: center;
    color: #ff5d5d;
    /* cursor: pointer; */
  }

  #orderInfoDeskDiv .status-item-num-blue {
    width: 32px;
    height: 20px;
    background: #e8eef8;
    border-radius: 10px;
    text-align: center;
    color: #5260ff;
    /* cursor: pointer; */
  }

  #orderInfoDeskDiv .address-book-icon {
    background-image: url(/static/img/other/order_tongxunlu.png);
    width: 24px;
    height: 24px;
  }

  #orderInfoDeskDiv .address-book-wrap {
    position: absolute;
    margin-top: 12px;
    right: 126px;
    cursor: pointer;
  }

  #orderInfoDeskDiv .status-bar-img-text {
    color: #333333;
    font-weight: 400;
    margin-left: 8px;
    margin-top: 2px;
  }

  #orderInfoDeskDiv .phone-toast {
    position: absolute;
    right: 8px;
  }

  #orderInfoDeskDiv .phone-toast-text {
    margin-top: 12px;
    color: #333;
    background-color: #fff;
    line-height: 24px;
    min-width: 60px;
    padding: 0 8px;
    font-size: 14px;
    cursor: pointer;
  }
  #orderInfoDeskDiv .phone-toast-text:hover {
    opacity: 0.8;
  }

  #orderInfoDeskDiv .phone-input-label {
    margin-top: 2px;
    margin-right: 5px;
  }

  #orderInfoDeskDiv .pohone-toast-box {
    position: absolute;
    width: 345px;
    height: 108px;
    z-index: 9;
    right: 0;
    top: 48px;
    visibility: hidden;
    padding-top: 8px;
    box-shadow: 0 1px 4px #b2b2b2;
  }

  #orderInfoDeskDiv .phone-info-input {
    width: 215px;
    height: 25px;
    display: block;
  }

  #orderInfoDeskDiv .pohone-info-status-wrap {
    /* margin-top: 20px;
    margin-left: 20px; */
  }

  #orderInfoDeskDiv .phone-info-radio {
    margin-left: 0px;
    margin-right: 70px;
  }

  #orderInfoDeskDiv .layui-btn,
  .layui-input {
    transition: unset;
  }

  #orderInfoDeskDiv .layui-form-label {
    width: 80px;
    padding: 0;
  }

  #orderInfoDeskDiv .phone-info-clear {
    margin-left: 8px;
    margin-top: 2px;
    cursor: pointer;
  }
  #orderInfoDeskDiv .phone-info-clear:hover {
    opacity: 0.8;
  }

  #orderInfoDeskDiv .layui-form-switch {
    margin-top: 0px;
  }

  #orderInfoDeskDiv .order-list-box {
    /* width: 100%; */
    overflow: hidden;
    height: calc(100% - 58px);
    padding-top: 8px;
    padding-left: 8px;
  }
  #orderInfoDeskDiv .desk-options-item-text {
    font-weight: 400;
    color: #333333;
    /* width: 76px; */
    height: 32px;
    /* margin: 2px 1px 2px 1px; */
    text-align: center;
    line-height: 32px;
    width: 84px;
    background: #e8ecf2;
    padding: 0 8px;
    border: 2px solid #e8ecf2;
    white-space: nowrap;
    cursor: pointer;
  }

  #orderInfoDeskDiv .desk-options-item-text:hover {
    color: #5260ff;
  }

  #orderInfoDeskDiv .desk-options-item-text:first-child {
    border-radius: 4px 0 0 4px;
  }

  #orderInfoDeskDiv .desk-options-item-text:nth-last-child(2) {
    border-radius: 0 4px 4px 0;
  }

  #orderInfoDeskDiv .desk-option-active-text {
    color: #5260ff !important;
    background: #ffffff;
  }

  #orderInfoDeskDiv .order-utils-bar {
    margin-top: 10px;
  }

  #orderInfoDeskDiv .date-select-text {
    width: 56px;
    height: 20px;
    line-height: 20px;
    font-weight: 400;
    color: #666666;
    margin-top: 5px;
    margin-right: 4px;
  }

  #orderInfoDeskDiv .search-info-input {
    width: 300px;
    height: 30px;
    margin-right: 8px;
  }

  #orderInfoDeskDiv .order-confirm-btn {
    width: 68px;
    height: 30px;
    background: #5260ff;
    border-radius: 4px;
  }

  #orderInfoDeskDiv .layui-btn-cancel {
    width: 68px;
    height: 30px;
    background: #fff;
    border-radius: 4px;
  }

  #orderInfoDeskDiv .ayui-btn-cancel {
    border-color: #5260ff;
    color: #5260ff;
    background-color: #fff;
  }

  #orderInfoDeskDiv .order-create-btn {
    width: 74px;
    height: 30px;
    border-radius: 4px;
    background-color: #f45555;
    border: 1px #f45555 solid;
    color: #fff;
    line-height: 28px;
    min-width: 60px;
    padding: 0 8px;
    height: 30px;
    font-size: 14px;
    cursor: pointer;
  }

  #orderInfoDeskDiv .order-create-btn:hover {
    background-color: #fc7070;
  }

  #orderInfoDeskDiv .layui-inline {
    margin-right: 0px;
  }

  #orderInfoDeskDiv .search-input {
    margin-left: 16px;
  }
  #orderInfoDeskDiv .trasen-con-box {
    top: 0;
    margin-top: 155px;
    z-index: 2;
    /* width: calc(100% - 24px); */
    margin-left: 8px;
    margin-right: 8px;
  }
  #orderInfoDeskDiv .incoming-call-box {
    position: absolute;
    width: 320px;
    height: 160px;
    background: #ffffff;
    box-shadow: 4px 2px 4px 2px #e6eaf0;
    border-radius: 8px;
    z-index: 9;
    bottom: 140px;
  }
  #orderInfoDeskDiv .caller-info-warp {
    margin-left: 40px;
    margin-top: 24px;
    margin-right: 48px;
    margin-bottom: 24px;
  }
  #orderInfoDeskDiv .caller-avator {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    padding-top: 6px;
    /* background-image: url('/static/img/home/<USER>'); */
  }
  #orderInfoDeskDiv .caller-phone {
    width: 145px;
    height: 32px;
    font-weight: 600;
    color: #333333;
    line-height: 32px;
    font-size: 24px;
  }
  #orderInfoDeskDiv .caller-from {
    width: 168px;
    height: 22px;
    font-size: 16px;
    font-weight: 400;
    color: #666666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  #orderInfoDeskDiv .incoming-call-btn-wrap {
    margin-left: 24px;
  }
  #orderInfoDeskDiv .cancel-btn {
    width: 120px;
    height: 40px;
    background: #f45555;
    border-radius: 100px;
    margin-right: 32px;
  }
  #orderInfoDeskDiv .accept-btn {
    width: 120px;
    height: 40px;
    background: #5260ff;
    border-radius: 100px;
  }
  #orderInfoDeskDiv .incoming-call-box-btn {
    font-size: 16px;
    line-height: 40px;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    cursor: pointer;
  }
  #orderInfoDeskDiv .call-cancel,
  .call-accept {
    width: 16px;
    height: 16px;
    margin: 12px -29px 12px 30px;
  }
  #orderInfoDeskDiv .waiting-list {
    position: absolute;
    z-index: 9;
    width: 208px;
    height: 88px;
    bottom: 24px;
  }
  #orderInfoDeskDiv .waiting-list .waiting-bg {
    height: 100%;
    width: 100%;
  }
  #orderInfoDeskDiv .waiting-list .waiting-title {
    color: #fff;
    position: absolute;
    top: 50%;
    left: 22px;
    transform: translate(0, -50%);
    /* font-weight: 600; */
    width: 44px;
    height: 56px;
    letter-spacing: 2px;
    font-size: 20px;
  }
  #orderInfoDeskDiv .waiting-list .waiting-info {
    position: absolute;
    top: 50%;
    left: 88px;
    transform: translate(0, -50%);
    font-size: 12px;
    color: #333333;
    line-height: 18px;
  }
  #orderInfoDeskDiv .waiting-list .waiting-info .waiting-num {
    font-size: 16px;
    font-weight: 600;
    color: #f45555;
    line-height: 22px;
    margin-top: 2px;
  }
  #orderInfoDeskDiv .tra-lay-select-title.pubSelectPesonBox,
  #orderInfoDeskDiv input {
    font-size: inherit !important;
  }
  .tra-lay-select-dl dl dd,
  .tra-lay-select-dl dl dt {
    font-size: inherit;
  }
  #orderInfoDeskDiv .oa-nav-search {
    padding-top: 8px;
    min-width: 1150px;
    padding-right: 10px;
  }
  #orderInfoDeskDiv .more-search-box {
    margin-right: 15px;
  }
  #orderInfoDeskDiv .emergencyIcon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background-image: linear-gradient(45deg, red, #f27940);
    border-radius: 50%;
    align-items: center;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    font-weight: 500;
    color: #fff;
    margin-right: 5px;
    cursor: default;
  }
  #orderInfoDeskDiv .rebackIcon {
    background: #33333380;
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    line-height: 20px;
    text-align: center;
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    margin-right: 5px;
    cursor: default;
  }
  #orderInfoDeskDiv [name='createOrderBtn'] {
    /* width: 60px; */
    height: 30px;
    background-color: #5260ff;
    border-radius: 2px;
    border: 1px solid #5260ff;
    color: #fff;
  }
  #orderInfoDeskDiv .search-time-range {
    white-space: nowrap;
  }
  #orderInfoDeskDiv .oa-nav-search [render='time'] {
    padding-right: 30px;
    background: url(../../static/img/other/icon_riqi.png) no-repeat right 8px
      top 6px;
    background-size: 16px;
  }
  #orderInfoDeskDiv .oa-nav-search .layui-edge,
  #orderInfoDeskDiv .oa-nav-search .tra-lay-select-title i.icon {
    border: none;
    display: inline-block;
    background: url(../../static/img/other/icon_xiala.png) no-repeat;
    background-size: 16px;
    height: 16px;
    width: 16px;
    top: 8px;
  }
  #orderInfoDeskDiv .oa-nav-search .layui-edge {
    top: 10px;
  }
  #orderInfoDeskDiv .oa-nav-search .layui-form-selected .layui-edge {
    top: 15px;
  }
  #orderInfoDeskDiv #orderOptionsBar .has-dot {
    position: relative;
  }
  #orderInfoDeskDiv #orderOptionsBar .red-dot {
    background: #f45555;
    border-radius: 10px;
    position: absolute;
    top: 0px;
    right: 2px;
    line-height: 14px;
    width: 20px;
    height: 14px;
    display: none;
    justify-content: center;
    align-items: center;
  }
  #orderInfoDeskDiv #orderOptionsBar .red-dot div {
    color: #fff;
    font-size: 12px;
    line-height: 14px;
    transform: scale(0.8);
  }
  #orderInfoDeskDiv .ui-jqgrid-view .ui-jqgrid-htable th * {
    text-align: center;
  }
  @media screen and (max-width: 1366px) {
    #orderInfoDeskDiv .outer-search-time .layui-input-inline {
      width: 140px;
    }
  }
  #orderInfoDeskDiv .custom-audio-content {
    padding: 0 8px;
  }
  #orderInfoDeskDiv .custom-audio-content img {
    height: 16px;
    width: 16px;
    cursor: pointer;
    margin-right: 4px;
    transition: all 0.3s;
  }
  #orderInfoDeskDiv .custom-audio-content div,
  #orderInfoDeskDiv .custom-audio-content div span {
    color: #666666;
    line-height: 18px;
    font-size: 12px;
  }
  #orderInfoDeskDiv .custom-audio-timeline {
    cursor: pointer;
  }
  #orderInfoDeskDiv .custom-audio-timeline {
    height: 2px;
    position: absolute;
    border-radius: 2px;
  }
  #orderInfoDeskDiv .shell-search-box {
    margin-right: 8px;
  }
  #orderInfoDeskDiv .queryForm > div.fr {
    display: flex;
  }
  #orderInfoDeskDiv .custom-audio-timeline .play-dot {
    display: none;
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    top: -2px;
    z-index: 5;
    background-color: #5260ff;
    /* outline: 3px transparent solid; */
    animation: bouncingDot 1.5s linear infinite;
  }
  @keyframes bouncingDot {
    0% {
      outline: 0px #5260ff66 solid;
    }
    70% {
      outline: 3px #5260ff66 solid;
    }
    100% {
    }
  }
  #orderInfoDeskDiv .audio-timeline-toast-box {
    position: absolute;
    top: -20px;
    border-radius: 4px;
    padding: 0px 4px;
    background: rgb(255, 255, 255);
    box-shadow: rgb(51 51 51 / 20%) 2px 2px 5px 0px;
    opacity: 0;
    transition: opacity 0.5s;
  }
  #orderInfoDeskDiv .audio-timeline-toast-box.show {
    opacity: 1;
  }
  #orderInfoDeskDiv .svg-icon {
    display: inline-block;
    height: 27px;
    width: 27px;
    margin-right: 5px;
    background-repeat: no-repeat;
    background-size: 14px 14px;
    background-position: center;
  }
  #orderInfoDeskDiv .icon_work_order_show_rasen {
    background-image: url(/static/img/other/icon_work_order_show_rasen.svg);
  }
  #orderInfoDeskDiv .icon_work_order_send_message {
    background-image: url(/static/img/other/icon_work_order_send_message.svg);
  }
  #orderInfoDeskDiv .icon_work_order_mark_useless {
    background-image: url(/static/img/other/icon_work_order_mark_useless.svg);
  }
  #orderInfoDeskDiv .come-from-tech {
    margin-left: 8px;
    cursor: pointer;
    user-select: none;
  }
  #orderInfoDeskDiv .page-total-count {
    line-height: 36px;
    font-size: 14px;
    margin-left: 16px;
  }
</style>

<div id="orderInfoDeskDiv">
  <div
    class="info-desk-status-bar order-info-desk-box flex layui-form"
    id="infoDeskStatusBar"
  >
    <div
      class="info-desk-font status-bar-time"
      id="orderInfoStatusBarTime"
    ></div>
    <div class="info-desk-font status-bar-text">今日服务台:</div>
    <div class="flex status-item-wrap" id="orderInfoStautsItemWrap"></div>
    <div class="flex address-book-wrap">
      <div class="address-book-icon"></div>
      <div class="status-bar-img-text info-desk-font">通讯录</div>
    </div>
    <div class="phone-toast flex">
      <div
        class="info-desk-font phone-toast-text flex"
        id="orderInfoPhoneToastText"
        style="align-items: center"
      >
        <img
          src="static/img/other/icon_laidiantanping.svg"
          style="margin-right: 8px"
        />
        来电弹屏
      </div>
      <div class="phone-swtich layui-form" lay-filter="phoneInfoForm">
        <div
          class="pohone-toast-box order-info-desk-box"
          id="orderInfoPhoneToast"
        >
          <form id="orderInfoPhoneToastInfo">
            <div class="layui-form-item flex phone-info-input-wrap">
              <label class="layui-form-label phone-input-label"
                >来电弹屏:</label
              >
              <input
                type="checkbox"
                name="phoneSwtich"
                lay-filter="phoneSwtich"
                lay-skin="switch"
              />
            </div>
            <div class="layui-form-item flex phone-info-input-wrap">
              <label class="layui-form-label phone-input-label"
                >语音播报:</label
              >
              <input
                type="checkbox"
                name="videoSwtich"
                lay-filter="videoSwtich"
                lay-skin="switch"
              />
            </div>
            <!-- <div class="layui-form-item flex pohone-info-input-wrap">
              <label class="layui-form-label phone-input-label">我的坐席:</label>
              <input type="number" name="phoneNumber" placeholder="绑定您的服务座机号/手机号" autocomplete="off"
                class="layui-input phone-info-input" lay-verify="phoneNumber">
              <div class="phone-info-clear info-desk-font" id="clearPhone"> 
                <i class="layui-icon layui-icon-refresh"></i>
              </div>
            </div>
            <div class="layui-form-item pohone-info-status-wrap">
              <label class="layui-form-label phone-input-label">坐席状态:</label>
              <input type="radio" name="custometServiceStatus" value="1" title="工作中" lay-filter="radioRelaxed">
              <input type="radio" name="custometServiceStatus" value="0" title="休息中" checked lay-filter="radioRelaxed">
            </div> -->
            <div class="layui-form-item">
              <div class="layer-btn archivesTabBtn">
                <button
                  type="button"
                  class="layui-btn"
                  lay-submit=""
                  lay-filter="phonInfoSubmit"
                >
                  确定
                </button>
                <button
                  type="button"
                  class="layui-btn ayui-btn-cancel"
                  lay-submit
                  lay-filter="phonInfoClose"
                  name="phonInfoClose"
                >
                  关闭
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <div class="order-list-box order-info-desk-box layui-form">
    <div class="desk-options-bar flex" id="orderOptionsBar">
      <div
        class="desk-options-item-text desk-option-active-text"
        data-index="5"
      >
        全部工单
      </div>
      <div class="desk-options-item-text" data-index="0">待派单(0)</div>
      <div class="desk-options-item-text" data-index="1">处理中(0)</div>
      <div class="desk-options-item-text" data-index="2">未建单(0)</div>
      <div class="desk-options-item-text" data-index="3">已完成</div>
      <div class="desk-options-item-text has-dot" data-index="4">
        通话记录
        <div class="red-dot"><div></div></div>
      </div>

      <div class="page-total-count">
        当前搜索条件共查询到<span
          style="
            font-size: 18px;
            margin: 0 4px;
            color: #ff5d5d;
            font-weight: 700;
          "
          >0</span
        >条记录
      </div>
    </div>

    <div class="oa-nav-search search-bar">
      <!-- 处理中搜索 -->
      <div class="queryForm" style="display: none">
        <form
          class="layui-form"
          id="orderInfo1-searchForm"
          lay-filter="orderInfo1"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="orderInfo1"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="orderInfo1"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">状态</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                id="orderStatus"
                class="layui-input"
                autocomplete="off"
              />
              <input
                type="text"
                style="display: none"
                name="workStatusG"
                value="2,3,7"
              />
            </div>
          </div>

          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="orderInfo1"
              id="orderInfo1-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox0"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="orderInfo1-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" name="createOrderBtn">
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button>
          <button
            type="button"
            class="layui-btn export-btn"
            name="export"
            data-index="1"
            data-status="10"
            data-table="orderHandleTable"
          >
            导出
          </button>
        </div>

        <div class="screen-box more-search-box" screen-box="myOrderSearchBox0">
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="orderInfo1-searchFormHidden"
              lay-filter="orderInfo1-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    class="layui-inline search-time-range outer-search-time"
                    style="display: flex; height: 30px"
                  >
                    <div class="layui-input-inline" style="width: 160px">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span> - </span>
                    <div class="layui-input-inline" style="width: 160px">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="searchBar-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName0"
                    placeholder="请选择报修科室"
                    data-repairman-id="orderInfo1-repairName"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId0"
                    id="orderInfo1-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">处理人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo1-fkUserName"
                    render="handler"
                    handler="0"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo1-repairName"
                    render="repairMan"
                    repairMan="0"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">处理科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="fkDeptName"
                    class="layui-input"
                    id="searchBar-fkDeptName"
                    render="fkDep"
                    depNameVal="fkDeptName0"
                    placeholder="请选择处理科室"
                    data-repairman-id="orderInfo1-fkDeptName"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="fkDeptId"
                    class="layui-input"
                    depIdVal="fkDeptId0"
                    id="orderInfo1-fkDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="orderInfo1-searchBtnHidden"
              search-btn="orderInfo1-1"
              screen-box-tar="myOrderSearchBox0"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="orderInfo1-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="orderInfo1-close"
              screen-box-tar="myOrderSearchBox0"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 未建单搜索 -->
      <div class="queryForm" style="display: none">
        <form
          class="layui-form"
          id="orderInfo2-searchForm"
          lay-filter="orderInfo2"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">备注说明</span>
            <div class="shell-layer-input-box">
              <select name="type" lay-search>
                <option value="1">全部</option>
                <option value="2">未建单</option>
                <option value="3">未接听</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="orderInfo2"
              id="orderInfo2-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="orderInfo2-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" name="createOrderBtn">
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button>
          <button
            type="button"
            class="layui-btn export-btn"
            name="export"
            data-index="2"
            data-table="orderUnbuildTable"
          >
            导出
          </button>
        </div>
      </div>

      <!-- 已完成 搜索栏 -->
      <div class="queryForm" style="display: none">
        <form
          class="layui-form"
          id="orderInfo3-searchForm"
          lay-filter="orderInfo3"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="orderInfo3"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="orderInfo3"
              />
            </div>
          </div>
          <!-- 待完成  下拉还是怎么打 -->
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">状态</span>
            <div class="shell-layer-input-box">
              <select name="workStatusValue" lay-search>
                <option value="">全部</option>
                <option value="5">待评价</option>
                <option value="6">已完成</option>
                <option value="8">已终止</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="orderInfo3"
              id="orderInfo3-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox2"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="orderInfo3-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" name="createOrderBtn">
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button>
          <button
            type="button"
            class="layui-btn export-btn"
            name="export"
            data-index="3"
            data-status="11"
            data-table="orderFinishedTable"
          >
            导出
          </button>
        </div>

        <div class="screen-box more-search-box" screen-box="myOrderSearchBox2">
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="orderInfo3-searchFormHidden"
              lay-filter="orderInfo3-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="orderInfo3-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName1"
                    data-repairman-id="orderInfo3-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId1"
                    id="orderInfo3-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">处理人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo3-fkUserName"
                    render="handler"
                    handler="1"
                  ></div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo3-repairName"
                    render="repairMan"
                    repairMan="1"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">确认时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">处理科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="fkDeptName"
                    class="layui-input"
                    id="orderInfo3-fkDeptName"
                    render="fkDep"
                    depNameVal="fkDeptName1"
                    placeholder="请选择处理科室"
                    data-repairman-id="orderInfo3-fkDeptName"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="fkDeptId"
                    class="layui-input"
                    depIdVal="fkDeptId1"
                    id="orderInfo3-fkDeptId"
                    value=""
                  />
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="orderInfo3-searchBtnHidden"
              search-btn="orderInfo3-1"
              screen-box-tar="myOrderSearchBox2"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="orderInfo3-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="orderInfo3-close"
              screen-box-tar="myOrderSearchBox2"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 通话记录 搜索栏 -->
      <div class="queryForm" style="display: none">
        <form
          class="layui-form"
          id="orderInfo4-searchForm"
          lay-filter="orderInfo4"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">通话时间</span>
            <div class="shell-layer-input-box" style="width: auto">
              <div class="layui-inline search-time-range">
                <div class="layui-input-inline" style="width: 140px;">
                  <input
                    class="layui-input"
                    name="beginCallTime"
                    render="time"
                    placeholder="开始日期"
                  />
                </div>
                <span>-</span>
                <div class="layui-input-inline" style="width: 140px;">
                  <input
                    class="layui-input"
                    name="endCallTime"
                    render="time"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">是否接听</span>
            <div class="shell-layer-input-box">
              <select name="callType" lay-search>
                <option value="">全部</option>
                <option value="1">是</option>
                <option value="0">否</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">进展</span>
            <div class="shell-layer-input-box">
              <select lay-search name="workStatus"></select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="orderInfo4"
              id="orderInfo4-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox3"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="orderInfo4-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" name="createOrderBtn">
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button>
          <button
            type="button"
            class="layui-btn export-btn"
            name="export"
            data-index="4"
            data-table="orderRecordTable"
          >
            导出
          </button>
        </div>

        <div class="screen-box more-search-box" screen-box="myOrderSearchBox3">
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="orderInfo4-searchFormHidden"
              lay-filter="orderInfo4-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="orderInfo4-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName2"
                    data-repairman-id="orderInfo4-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId2"
                    id="orderInfo4-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo4-repairName"
                    render="repairMan"
                    repairMan="2"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="orderInfo4-searchBtnHidden"
              search-btn="orderInfo4-1"
              screen-box-tar="myOrderSearchBox3"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="orderInfo4-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="orderInfo4-close"
              screen-box-tar="myOrderSearchBox3"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 待派单 搜索栏 -->
      <div class="queryForm" style="display: none">
        <form
          class="layui-form"
          id="orderInfo5-searchForm"
          lay-filter="orderInfo5"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                search-input="orderInfo5"
                placeholder="请输入故障描述"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">紧急程度</span>
            <div class="shell-layer-input-box">
              <select name="faultEmergency" lay-search>
                <option value="">请选择紧急程度</option>
                <option value="1">非常紧急</option>
                <option value="2">比较急</option>
                <option value="3">常规处理</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">影响范围</span>
            <div class="shell-layer-input-box">
              <select lay-search name="faultAffectScope">
                <option value="">请选择影响范围</option>
                <option value="1">个人事件</option>
                <option value="2">科室事件</option>
                <option value="3">多科室事件</option>
                <option value="4">全院事件</option>
              </select>
            </div>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="orderInfo5"
              id="orderInfo5-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox4"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="orderInfo5-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" name="createOrderBtn">
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button>
          <button
            type="button"
            class="layui-btn export-btn"
            name="export"
            data-index="5"
            data-status="12"
            data-table="orderDispatchTable"
          >
            导出
          </button>
        </div>

        <div class="screen-box more-search-box" screen-box="myOrderSearchBox4">
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="orderInfo5-searchFormHidden"
              lay-filter="orderInfo5-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="orderInfo5-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName3"
                    data-repairman-id="orderInfo5-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId3"
                    id="orderInfo5-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo5-repairName"
                    render="repairMan"
                    repairMan="3"
                  ></div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">工单编号</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="number"
                    name="workNumber"
                    class="layui-input"
                    search-input="orderInfo5"
                    placeholder="请输入工单编号"
                  />
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修时间</div>
                </div>
                <div class="layui-col-md10">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="orderInfo5-searchBtnHidden"
              search-btn="orderInfo5-1"
              screen-box-tar="myOrderSearchBox4"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="orderInfo5-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="orderInfo5-close"
              screen-box-tar="myOrderSearchBox4"
              >关闭</span
            >
          </div>
        </div>
      </div>

      <!-- 全部工单 搜索栏 -->
      <div class="queryForm">
        <form
          class="layui-form"
          id="orderInfo6-searchForm"
          lay-filter="orderInfo6"
        >
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">工单编号</span>
            <div class="shell-layer-input-box">
              <input
                type="number"
                name="workNumber"
                class="layui-input"
                search-input="orderInfo6"
                placeholder="请输入工单编号"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">故障描述</span>
            <div class="shell-layer-input-box">
              <input
                type="text"
                name="faultDeion"
                class="layui-input"
                placeholder="请输入故障描述"
                search-input="orderInfo6"
              />
            </div>
          </div>
          <div class="shell-search-box">
            <span class="shell-layer-input-boxTit">报修时间</span>
            <div class="shell-layer-input-box" style="width: auto">
              <div
                class="layui-inline search-time-range outer-search-time"
                style="display: flex; height: 30px"
              >
                <div class="layui-input-inline" style="width: 140px;">
                  <input
                    class="layui-input"
                    name="beginTime"
                    render="time"
                    placeholder="开始日期"
                  />
                </div>
                <span>-</span>
                <div class="layui-input-inline" style="width: 140px;">
                  <input
                    class="layui-input"
                    name="endTime"
                    render="time"
                    placeholder="结束日期"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="shell-search-box">
            <button
              type="button"
              class="layui-btn"
              search-btn="orderInfo6"
              id="orderInfo6-searchBtn"
              btnType="searchBtn"
            >
              搜索
            </button>
          </div>
          <div class="shell-search-box">
            <button
              type="button"
              class="search_list"
              screen-box-tar="myOrderSearchBox5"
            >
              <i class="oaicon oa-icon-search_list"></i>
            </button>
            <button
              type="button"
              class="layui-btn oa-btn-reset"
              id="orderInfo6-refresh"
              btntype="resetBtn"
            >
              <i class="layui-icon layui-icon-refresh"></i>
            </button>
          </div>
        </form>
        <div class="fr">
          <button type="button" class="layui-btn" name="createOrderBtn">
            <i class="icon iconfont icon_tianjia"></i>
            创建工单
          </button>
          <button
            type="button"
            class="layui-btn export-btn"
            name="export"
            data-index="6"
            data-status="13"
            data-table="orderAllOrderTable"
          >
            导出
          </button>
        </div>

        <div class="screen-box more-search-box" screen-box="myOrderSearchBox5">
          <div class="screen-box_tit">更多查询条件</div>
          <div class="screen-box_con">
            <form
              class="layui-form row"
              id="orderInfo6-searchFormHidden"
              lay-filter="orderInfo6-hidden"
            >
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">状态</div>
                </div>
                <div class="layui-col-md7">
                  <select lay-search name="workStatusValue">
                    <option value="">全部</option>
                    <option value="1">待派单</option>
                    <option value="2">待接单</option>
                    <option value="3">处理中</option>
                    <option value="4">待验收</option>
                    <option value="5">待评价</option>
                    <option value="6">已完成</option>
                    <option value="7">已暂停</option>
                    <option value="8">已终止</option>
                  </select>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="repairManDeptName"
                    class="layui-input"
                    id="searchBar-repairManDeptName"
                    render="repairDep"
                    depNameVal="repairManDeptName4"
                    data-repairman-id="orderInfo6-repairName"
                    placeholder="请选择报修科室"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="repairManDeptId"
                    class="layui-input"
                    depIdVal="repairManDeptId4"
                    id="orderInfo6-repairManDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">处理人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo6-fkUserName"
                    render="handler"
                    handler="2"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">报修人</div>
                </div>
                <div class="layui-col-md7">
                  <div
                    id="orderInfo6-repairName"
                    render="repairMan"
                    repairMan="4"
                  ></div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">处理科室</div>
                </div>
                <div class="layui-col-md7">
                  <input
                    type="text"
                    name="fkDeptName"
                    class="layui-input"
                    id="searchBar-fkDeptName"
                    render="fkDep"
                    depNameVal="fkDeptName4"
                    placeholder="请选择处理科室"
                    data-repairman-id="orderInfo6-fkDeptName"
                    autocomplete="off"
                  />
                  <input
                    type="hidden"
                    name="fkDeptId"
                    class="layui-input"
                    depIdVal="fkDeptId4"
                    id="orderInfo6-fkDeptId"
                    value=""
                  />
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="layui-col-md2">
                  <div class="title">要求日期</div>
                </div>
                <div class="layui-col-md7">
                  <div class="layui-inline search-time-range">
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="beginRequiredCompletionTime"
                        render="time"
                        placeholder="开始日期"
                      />
                    </div>
                    <span>-</span>
                    <div class="layui-input-inline">
                      <input
                        class="layui-input"
                        name="endRequiredCompletionTime"
                        render="time"
                        placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="screen-box_btn">
            <button
              class="layui-btn"
              id="orderInfo6-searchBtnHidden"
              search-btn="orderInfo6-1"
              screen-box-tar="myOrderSearchBox5"
            >
              搜索
            </button>
            <span class="layui-btn layui-btn-primary" id="orderInfo6-reset"
              >重置</span
            >
            <span
              class="layui-btn layui-btn-primary"
              id="orderInfo6-close"
              screen-box-tar="myOrderSearchBox5"
              >关闭</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 全部 -->
  <form id="orderAllOrderForm">
    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="orderAllOrderTable"></table>
        <!-- 分页 -->
        <div id="orderAllOrderPager"></div>
      </div>
    </div>
  </form>

  <!-- 待派单 -->
  <form id="orderDispatchForm" style="display: none">
    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="orderDispatchTable"></table>
        <!-- 分页 -->
        <div id="orderDispatchPager"></div>
      </div>
    </div>
  </form>

  <!-- 处理中表单 -->
  <form id="orderHandleForm" style="display: none">
    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="orderHandleTable"></table>
        <!-- 分页 -->
        <div id="orderHandlePager"></div>
      </div>
    </div>
  </form>

  <!-- 未建单表单 -->
  <form id="orderUnbuildForm" style="display: none">
    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="orderUnbuildTable"></table>
        <!-- 分页 -->
        <div id="orderUnbuildPager"></div>
      </div>
    </div>
  </form>

  <!-- 已完成表单 -->
  <form id="orderFinishedForm" style="display: none">
    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="orderFinishedTable"></table>
        <!-- 分页 -->
        <div id="orderFinishedPager"></div>
      </div>
    </div>
  </form>

  <!-- 通话记录表单 -->
  <form id="orderRecordForm" style="display: none">
    <div class="trasen-con-box">
      <div class="table-box">
        <!-- 表单 -->
        <table id="orderRecordTable"></table>
        <!-- 分页 -->
        <div id="orderRecordPager"></div>
      </div>
    </div>
  </form>

  <!-- 来电显示 -->
  <!-- <div class="incoming-call-box" id="orderIncomingCallBox" style="display: none;">
    <div class="caller-info-warp flex">
      <div class="caller-avator"></div>
      <div class="caller-info">
        <div class="caller-phone info-desk-font">15080939085</div>
        <div class="caller-from info-desk-font">儿科办公室 张丹来电...</div>
      </div>
    </div>
    <div class="incoming-call-btn-wrap flex">
      <div class="cancel-btn info-desk-font incoming-call-box-btn" id="orederCallCancel">
        <div class="call-cancel fl" style="background-image: url('/static/img/other/order_guanbi0.png');"></div>关闭
      </div>
      <div class="accept-btn info-desk-font incoming-call-box-btn" id="orederCallAccept">
        <div class="call-accept fl" style="background-image: url('/static/img/other/order_jieting.png');"></div>接听
      </div>
    </div>
  </div> -->
  <!-- 等候人数 -->
  <!-- <div class="waiting-list" id="waitingListBox" style="visibility: hidden;">
      <img class="waiting-bg" src="../static/img/other/order_waiting_list.png">
      <div class="waiting-title">等候列表</div>
      <div class="waiting-info">
          <div>正在等候的人数</div>
          <div class="waiting-num">等候1人</div>
      </div>
  </div> -->
</div>
