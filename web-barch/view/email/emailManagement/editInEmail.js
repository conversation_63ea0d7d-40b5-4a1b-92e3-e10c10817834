"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(["form", "layedit", "laydate", "trasen", "upload"], function () {
      var form = layui.form,
        laydate = layui.laydate,
        trasen = layui.trasen,
        upload = layui.upload,
        layedit = layui.layedit;

      var fileArray = new Array();
      var oldFileArray = new Array();
      var RecentContactsArr = [];
      var ed = {};
      var url = common.url + "/ts-document/attachment/";
      var token = $.cookie("token");

      var chooseInput = "toEmailSelUser";

      var editIndex;
      var isInit = true;
      //弹窗初始化
      layer.open({
        type: 1,
        title: opt.title,
        closeBtn: 1,
        shadeClose: false,
        area: ["100%", "100%"],
        skin: "yourclass",
        content: html,
        success: function (layero, index) {
          var empSignimg = getEmpSignimg(); // 获取个性签名
          getEmailContact(); // 获取常用联系人
		  
		  if ("cssdeshfly" == common.globalSetting.orgCode) {
			 $("#outEmailWrapDiv").hide();
			 $("#firstTypeRadio").remove();
		  }
	
          if (opt.data) {
            $("#emailId").val(opt.id);
            if (null != empSignimg && "" != empSignimg) {
              if (
                opt.data.content.indexOf(
                  '<hr style="margin: 0 0 10px 0; border: 0; border-bottom: 1px solid #E4E5E6; width: 50px;" />'
                ) == -1
              ) {
                opt.data.content =
                  opt.data.content +
                  "<br><br><br><hr style='margin: 0 0 10px 0;border: 0;border-bottom: 1px solid #E4E5E6;width: 50px;'>" +
                  empSignimg;
              }
            }
            trasen.setNamesVal($("#editInEmailForm"), opt.data);
            $("#emailHtmlContext").val(opt.data.content);
            $("#toEmailUserId").val(opt.data.toId);
            $("#ccEmailUserId").val(opt.data.ccId);
            initEmailFiles(opt.data.attachmentList); //初始化附件
            $("#saveToOutbox").prop("checked", true);
            $("#sendToWx").prop("checked", true);
            if ("1" == opt.data.sendToAll) {
              $("#sendToAllSwitch").prop("checked", true);
              $("#toEmailSearchInput").attr("sendToAll", "true");
            }
            if ("1" == opt.data.timing) {
              $("#startTiming").removeAttr("disabled");
              $("#startTiming").val(
                opt.data.startTiming.substring(
                  0,
                  opt.data.startTiming.length - 3
                )
              );
              $("#timingSwitch").attr("checked", "checked");
            } else {
              $("#startTiming").val("");
            }
            if ("2" == opt.data.stateLevel) {
              $("#stateLevelBox").attr("checked", "checked");
            } else {
              $("#stateLevelBox").removeAttr("checked");
            }
            if ("1" == opt.data.readReceipt) {
              $("#readReceiptBox").attr("checked", "checked");
            } else {
              $("#readReceiptBox").removeAttr("checked");
            }
          } else {
            if (null != empSignimg && "" != empSignimg) {
              $("#emailHtmlContext").val(
                "<br><br><br><hr style='margin: 0 0 10px 0;border: 0;border-bottom: 1px solid #E4E5E6;width: 50px;'>" +
                  empSignimg
              );
            }
          }

          initEditor();
          form.render();
        },
      });

      //定时发送时间控件
      laydate.render({
        elem: "#startTiming",
        type: "datetime",
        format: "yyyy-MM-dd HH:mm",
        showBottom: true,
        trigger: "click",
        min: new Date().toLocaleString("chinese", { hour12: false }),
      });

      // 输入搜索
      $("#editInEmailForm")
        .off("input", "#RecentContactsSearch")
        .on("input", "#RecentContactsSearch", function () {
          const val = $(this).val();
          $("#emailContactUl").empty();

          let searchArr = [];
          if (!val) {
            searchArr = RecentContactsArr;
          } else {
            searchArr = RecentContactsArr.filter(
              (item) => item.employeeName.indexOf(val) !== -1
            );
          }

          if (searchArr && searchArr.length > 0) {
            var liStr = "";
            $.each(searchArr, function (i, item) {
              const { employeeNo, employeeName, deptName } = item;
              liStr += `<li class="emailContactLi" employeeNo=${employeeNo} employeeName=${employeeName}>
                ${employeeName + "-" + deptName}
              </li>`;
            });
            $("#emailContactUl").append(liStr);
          } else {
            $("#emailContactUl").append(
              '<div style="margin: 8px 0 0 8px;">暂无数据</div>'
            );
          }
        });

      //收件人员选择
      const toEmailSearchInput = new $.selectSearchPerson(
        "#toEmailSearchInput",
        {
          placeholder: "收件人",
          searchType: "json",
          url: "/ts-oa/employee/list?userSel=true",
          contentType: "application/x-www-form-urlencoded; charset=UTF-8",
          textName: "empName",
          valName: "empCode",
          deptName: "empDeptName",
          defaultText: $(`#toEmailSelUser`).val(),
          defaultVal: $(`#toEmailselUserCode`).val(),
          choice: true,
          callback: function (data = []) {
            if (data) {
              $("#toEmailSelUser").val(
                data.map((item) => item.empName).join(",")
              );
              $("#toEmailUserId").val(
                data.map((item) => item.empCode).join(",")
              );
              $("#toEmailselUserCode").val(
                data.map((item) => item.empCode).join(",")
              );
            }
          },
        }
      );
      $("#selectToEmailSelUser").funs("click", function () {
        var data = {
          isCheckDept: "N",
          user_str: "toEmailSelUser",
          user_id: "toEmailUserId",
          user_code: "toEmailselUserCode",
        };
        $.quoteFun("/common/userSel", {
          trasen: trasenTable,
          title: "人员选择",
          rowNum: 200,
          rowList: [200, 500, 1000, 2000],
          data: data,
          callback: function (
            selUserNameVal,
            userIdArr,
            userCodeArr,
            deptNameArr,
            deptUserNameArr
          ) {
            var textarea = document.getElementById("toEmailSelUser");
            makeExpandingArea(textarea, userIdArr.length);
            toEmailSearchInput.renderSelectItem({
              names: selUserNameVal,
              idArr: userIdArr,
              codesArr: userCodeArr,
            });
          },
        });
      });

      //抄送人员选择
      const ccEmailSearchInput = new $.selectSearchPerson(
        "#ccEmailSearchInput",
        {
          placeholder: "抄送人",
          searchType: "json",
          url: "/ts-oa/employee/list?userSel=true",
          contentType: "application/x-www-form-urlencoded; charset=UTF-8",
          textName: "empName",
          valName: "empCode",
          deptName: "empDeptName",
          defaultText: $(`#ccEmailSelUser`).val(),
          defaultVal: $(`#ccEmailselUserCode`).val(),
          choice: true,
          callback: function (data = []) {
            if (data) {
              $("#ccEmailSelUser").val(
                data.map((item) => item.empName).join(",")
              );
              $("#ccEmailUserId").val(
                data.map((item) => item.empCode).join(",")
              );
              $("#ccEmailselUserCode").val(
                data.map((item) => item.empCode).join(",")
              );
            }
          },
        }
      );
      $("#selectCcEmailSelUser").funs("click", function () {
        var data = {
          isCheckDept: "N",
          user_str: "ccEmailSelUser",
          user_id: "ccEmailUserId",
          user_code: "ccEmailselUserCode",
        };
        $.quoteFun("/common/userSel", {
          trasen: trasenTable,
          rowNum: 200,
          rowList: [200, 500, 1000, 2000],
          title: "人员选择",
          data: data,
          callback: function (
            selUserNameVal,
            userIdArr,
            userCodeArr,
            deptNameArr,
            deptUserNameArr
          ) {
            var textarea = document.getElementById("ccEmailSelUser");
            makeExpandingArea(textarea, userIdArr.length);
            ccEmailSearchInput.renderSelectItem({
              names: selUserNameVal,
              idArr: userIdArr,
              codesArr: userCodeArr,
            });
          },
        });
      });

      // 密送人
      const bccEmailSearchInput = new $.selectSearchPerson(
        "#bccEmailSearchInput",
        {
          placeholder: "密送人",
          searchType: "json",
          url: "/ts-oa/employee/list?userSel=true",
          contentType: "application/x-www-form-urlencoded; charset=UTF-8",
          textName: "empName",
          valName: "empCode",
          deptName: "empDeptName",
          defaultText: $(`#bccEmailSelUser`).val(),
          defaultVal: $(`#bccEmailselUserCode`).val(),
          choice: true,
          callback: function (data = []) {
            if (data) {
              $("#bccEmailSelUser").val(
                data.map((item) => item.empName).join(",")
              );
              $("#bccEmailUserId").val(
                data.map((item) => item.empCode).join(",")
              );
              $("#bccEmailselUserCode").val(
                data.map((item) => item.empCode).join(",")
              );
            }
          },
        }
      );
      $("#selectBccEmailSelUser").funs("click", function () {
        var data = {
          isCheckDept: "N",
          user_str: "bccEmailSelUser",
          user_id: "bccEmailUserId",
          user_code: "bccEmailselUserCode",
        };
        $.quoteFun("/common/userSel", {
          trasen: trasenTable,
          rowNum: 200,
          rowList: [200, 500, 1000, 2000],
          title: "人员选择",
          data: data,
          callback: function (
            selUserNameVal,
            userIdArr,
            userCodeArr,
            deptNameArr,
            deptUserNameArr
          ) {
            var textarea = document.getElementById("bccEmailSelUser");
            makeExpandingArea(textarea, userIdArr.length);

            bccEmailSearchInput.renderSelectItem({
              names: selUserNameVal,
              idArr: userIdArr,
              codesArr: userCodeArr,
            });
          },
        });
      });

      function makeExpandingArea(el, len) {
        var timer = null;
        var setStyle = function (el, auto) {
          if (auto) el.style.height = auto;
          if (len == 0) {
            el.style.height = "30px";
          } else {
            el.style.height = el.scrollHeight + "px";
          }
        };
        var delayedResize = function (el) {
          if (timer) {
            clearTimeout(timer);
            timer = null;
          }
          timer = setTimeout(function () {
            setStyle(el);
          }, 200);
        };
        if (el.addEventListener) {
          el.addEventListener(
            "input",
            function () {
              setStyle(el, 1);
            },
            false
          );
          setStyle(el);
        } else if (el.attachEvent) {
          el.attachEvent("onpropertychange", function () {
            setStyle(el);
          });
          setStyle(el);
        }
        if (window.VBArray && window.addEventListener) {
          el.attachEvent("onkeydown", function () {
            var key = window.event.keyCode;
            if (key == 8 || key == 46) delayedResize(el);
          });
          el.attachEvent("oncut", function () {
            delayedResize(el);
          });
        }
      }

      $("#editInEmailForm")
        .off("click", "#toNameReset")
        .on("click", "#toNameReset", function (e) {
          $("#toEmailSelUser").val("");
          $("#toEmailUserId").val("");
          $("#toEmailselUserCode").val("");
          toEmailSearchInput.remove();
          var textarea = document.getElementById("toEmailSelUser");
          makeExpandingArea(textarea, 0);
        });

      $("#editInEmailForm")
        .off("click", "#ccNameReset")
        .on("click", "#ccNameReset", function (e) {
          $("#ccEmailSelUser").val("");
          $("#ccEmailUserId").val("");
          $("#ccEmailselUserCode").val("");
          ccEmailSearchInput.remove();
          var textarea = document.getElementById("ccEmailSelUser");
          makeExpandingArea(textarea, 0);
        });
      $("#editInEmailForm")
        .off("click", "#bccNameReset")
        .on("click", "#bccNameReset", function (e) {
          $("#bccEmailSelUser").val("");
          $("#bccEmailUserId").val("");
          $("#bccEmailselUserCode").val("");
          bccEmailSearchInput.remove();
          var textarea = document.getElementById("bccEmailSelUser");
          makeExpandingArea(textarea, 0);
        });

      //监听发送全院开发
      form.on("switch(sendToAllSwitch)", function (data) {
        if (data.elem.checked == true) {
          toEmailSearchInput.changeShow("hide");
          ccEmailSearchInput.changeShow("hide");
          bccEmailSearchInput.changeShow("hide");
          $("#sendToAll").val("1");

          $("#selectToEmailSelUser").hide();
          $("#toEmailUserId").val("");
          $("#toEmailSelUser").val("全院人员");
          $("#toEmailselUserCode").val("");
          $("#toEmailSelUser").addClass("disabledClass");
          $("#toEmailSelUser").attr("disabled", "disabled");
          $("#toEmailSelUser").removeAttr("lay-verify");
          $("#selectToEmailSelUser").addClass("noClick");
          $("#toNameReset").attr("disabled", "disabled");

          $("#ccEmailSelUser").val("");
          $("#ccEmailSelUser").attr("disabled", "disabled");
          $("#ccEmailUserId").val("");
          $("#ccEmailselUserCode").val("");
          $("#selectCcEmailSelUser").addClass("noClick");
          $("#ccNameReset").attr("disabled", "disabled");

          $("#bccEmailSelUser").val("");
          $("#bccEmailSelUser").attr("disabled", "disabled");
          $("#bccEmailUserId").val("");
          $("#bccEmailselUserCode").val("");
          $("#selectBccEmailSelUser").addClass("noClick");
          $("#bccNameReset").attr("disabled", "disabled");

          $("#emailContactDiv").addClass("noClick");
          $("#frequentContactsDiv").hide();
          $("#ccEmailSelUserDiv").hide();
          $("#bccEmailSelUserDiv").hide();
        } else {
          toEmailSearchInput.changeShow("show");
          ccEmailSearchInput.changeShow("show");
          bccEmailSearchInput.changeShow("show");
          $("#sendToAll").val("0");

          $("#toEmailSelUser").val("");
          $("#toEmailSelUser").removeClass("disabledClass");
          $("#toEmailSelUser").removeAttr("disabled");
          $("#toEmailSelUser").attr("lay-verify", "required");
          $("#selectToEmailSelUser").show();
          $("#selectToEmailSelUser").removeClass("noClick");
          $("#toNameReset").removeAttr("disabled");

          $("#ccEmailSelUser").removeAttr("disabled", "disabled");
          $("#selectCcEmailSelUser").removeClass("noClick");
          $("#ccNameReset").removeAttr("disabled");

          $("#bccEmailSelUser").removeAttr("disabled", "disabled");
          $("#selectBccEmailSelUser").removeClass("noClick");
          $("#bccNameReset").removeAttr("disabled");

          $("#ccEmailSelUserDiv").show();
          $("#bccEmailSelUserDiv").show();
          $("#emailContactDiv").removeClass("noClick");
          $("#frequentContactsDiv").show();
        }
      });

      //监听是否定时发送
      form.on("switch(timingSwitch)", function (data) {
        if (data.elem.checked == true) {
          $("#timing").val("1");
          $("#startTiming").removeAttr("disabled");
          $("#startTiming").attr("lay-verify", "required");
        } else {
          $("#timing").val("0");
          $("#startTiming").val("");
          $("#startTiming").attr("disabled", "disabled");
          $("#startTiming").removeAttr("lay-verify");
        }
      });

      //监听是否紧急
      form.on("checkbox(stateLevelBox)", function (data) {
        if (data.elem.checked == true) {
          $("#stateLevel").val("2");
        } else {
          $("#stateLevel").val("0");
        }
      });

      //监听回执
      form.on("checkbox(readReceiptBox)", function (data) {
        var toEmailselUserCodeArr = $("#toEmailselUserCode").val().split(",");
        if (
          data.elem.checked &&
          (toEmailselUserCodeArr.length > 10 || "1" == $("#sendToAll").val())
        ) {
          $("#readReceipt").val("1");
          layer.confirm(
            "当前发送对象较多，您确定要接收所有收件人的邮件回执吗？",
            {
              btn: ["确定", "取消"],
              title: "提示",
              closeBtn: 0,
            },
            function (index) {
              layer.close(index);
              $("#readReceiptBox").prop("checked", true);
            },
            function (index, layero) {
              layer.close(index);
              $("#readReceiptBox").prop("checked", false);
            }
          );
          form.render();
        } else {
          if (data.elem.checked == true) {
            $("#readReceipt").val("1");
          } else {
            $("#readReceipt").val("0");
          }
        }
      });

      //附件上传
      var emailManagementFileDataList = $("#emailManagementFileDataList"),
        uploadListIns = upload.render({
          elem: "#emailManagementFileUploadBtn",
          url: common.url + "/ts-document/attachment/fileUpload?module=email",
          accept: "file",
          multiple: true,
          auto: true,
          choose: function (obj) {
            var files = (this.files = obj.pushFile()); //将每次选择的文件追加到文件队列
            if ($("#subject").val() == "" && isInit) {
              let key = Object.keys(files)[0];
              $("#subject").val(files[key].name);
              isInit = false;
            }
            $("#emailManagementFileList").show();
            //读取本地文件
            obj.preview(function (index, file, result) {
              var tr = $(
                [
                  '<tr id="upload-' + index + '">',
                  "<td>" + file.name + "</td>",
                  "<td>" + (file.size / 1024).toFixed(1) + "kb</td>",
                  "<td>等待上传</td>",
                  "<td>",
                  '<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>',
                  "</td>",
                  "</tr>",
                ].join("")
              );

              //删除
              tr.find(".demo-delete").on("click", function () {
                delete files[index]; //删除对应的文件
                tr.remove();
                uploadListIns.config.elem.next()[0].value = ""; //清空 input file 值，以免删除后出现同名文件不可选
                //删除数组中指定元素
                fileArray = $.grep(fileArray, function (n, i) {
                  return n["index"] != index;
                });
              });
              emailManagementFileDataList.append(tr);
            });
          },
          done: function (res, index, upload) {
            if (res.success == true) {
              //上传成功
              var tr = emailManagementFileDataList.find("tr#upload-" + index),
                tds = tr.children();
              tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
              res.object[0].index = index;
              fileArray.push(res.object[0]);
              return delete this.files[index]; //删除文件队列已经上传成功的文件
            }
            this.error(index, upload, res.message);
          },
          error: function (index, upload, errorMsg) {
            var tr = emailManagementFileDataList.find("tr#upload-" + index),
              tds = tr.children();
            tds
              .eq(2)
              .html(
                '<span style="color: #FF5722;">上传失败:' + errorMsg + "</span>"
              );
          },
        });

      //发送
      form.on("submit(editInEmailSubmit)", function (data) {
        var d = data.field;
        d.isDraft = 0;
        var url = "/ts-information/optimize/emailInternal/sendEmailInternal";
        var toEmailSelUser = $("#toEmailSelUser").val();
        var outEmailAddress = $("#outEmailAddress").val();
        if (
          (null == toEmailSelUser || "" == toEmailSelUser) &&
          (null == outEmailAddress || "" == outEmailAddress)
        ) {
          layer.msg("收件人和外部邮箱必须有一项不能为空");
          return;
        }
        if (!d.id) delete d.id;
        d.content = tinymce.get("emailHtmlContext").getContent();
        var uploadedFile = "";
        $.each(fileArray, function (index, obj) {
          uploadedFile += obj.fileId + ",";
        });
        $.each(oldFileArray, function (index, obj) {
          uploadedFile += obj + ",";
        });

        d.uploadedFile = uploadedFile.substring(0, uploadedFile.length - 1);
        $.loadings();
        $.ajax({
          type: "post",
          url: common.url + url,
          contentType: "application/json;charset=UTF-8",
          data: JSON.stringify(d),
          success: function (res) {
            opt.ref && opt.ref();
            $.closeloadings();
            if (res.success) {
              layer.closeAll();
              tinymce.remove("#emailHtmlContext");
              layer.msg(res.object);
              if (d.timing == 1) {
                $("#draftBoxTab").click();
              } else {
                $("#outBoxTab").click();
              }
            } else {
              layer.closeAll();
              tinymce.remove("#emailHtmlContext");
              layer.msg(res.message);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.object);
          },
        });
        return false;
      });

      // 发送并继续
      /*form.on('submit(editInEmailProceedSubmit)', function (data) {
				var d = data.field;
				d.isDraft = 0;
				var url = '/ts-information/emailInternal/sendEmailInternal';
				if (!d.id) delete d.id;
				d.content = layedit.getContent(editIndex);
				var uploadedFile = "";
				$.each(fileArray, function (index, obj) {
					uploadedFile += obj.fileId + ",";
				});
				$.each(oldFileArray, function (index, obj) {
					uploadedFile += obj + ",";
				});

				d.uploadedFile = uploadedFile.substring(0, uploadedFile.length - 1);

				$.loadings();
				$.ajax({
					type: 'post',
					url: common.url + url,
					contentType: "application/json;charset=UTF-8",
					data: JSON.stringify(d),
					success: function (res) {
						opt.ref && opt.ref()
						$.closeloadings();
						if (res.success) {
							layer.msg(res.object);
							$("#editInEmailForm")[0].reset();
							layedit.clearContent(editIndex);
							$("#emailManagementFileDataList").empty();
							fileArray.length = 0;
							oldFileArray.length = 0;
							$("#outBoxTab").click();
						} else {
							layer.closeAll();
							layer.msg(res.object);
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});
				return false;
			});*/

      // 保存草稿
      form.on("submit(editInEmailDraftSubmit)", function (data) {
        var d = data.field;
        var url;
        if (d.id) {
          url = "/ts-information/optimize/emailInternal/update";
        } else {
          d.isDraft = 1;
          url =
            "/ts-information/optimize/emailInternal/sendEmailInternal?isDraft=1";
        }
        if (!d.id) delete d.id;
        //d.content = layedit.getContent(editIndex);
        d.content = tinymce.get("emailHtmlContext").getContent();
        var uploadedFile = "";
        $.each(fileArray, function (index, obj) {
          uploadedFile += obj.fileId + ",";
        });
        $.each(oldFileArray, function (index, obj) {
          uploadedFile += obj + ",";
        });

        d.uploadedFile = uploadedFile.substring(0, uploadedFile.length - 1);

        $.loadings();
        $.ajax({
          type: "post",
          url: common.url + url,
          contentType: "application/json;charset=UTF-8",
          data: JSON.stringify(d),
          success: function (res) {
            opt.ref && opt.ref();
            $.closeloadings();
            if (res.success) {
              layer.closeAll();
              tinymce.remove("#emailHtmlContext");
              layer.msg(res.object);
              $("#draftBoxTab").click();
            } else {
              layer.closeAll();
              tinymce.remove("#emailHtmlContext");
              layer.msg(res.object);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          },
        });
        return false;
      });

      //取消
      $("#closeInEmail").funs("click", function () {
        tinymce.remove("#emailHtmlContext");
        layer.closeAll();
      });

      // 邮箱教研规则
      form.verify({
        /*content: function(value) { 
					if('' == layedit.getText(editIndex)){
						return "内容不能为空";
					}else{
						return layedit.sync(editIndex);
					}
					},*/
        emailVerify: function (value) {
          var emailArray = value.split(",");
          var emailReg =
            /^([\.a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
          var emailStr = "";
          $.each(emailArray, function (index, obj) {
            if (!emailReg.test(obj)) {
              emailStr = obj;
            }
          });
          if (emailStr != "") {
            return emailStr + "的邮箱格式错误，请重新输入！";
          }
        },
      });

      function fileUpload(file, cb) {
        var formData = new FormData();
        //假设接口接收参数为file,值为选中的文件
        formData.append("file", file);
        $.ajax({
          url: "/ts-document/attachment/fileUpload?module=email&fillupf=2",
          method: "post",
          contentType: false,
          processData: false,
          data: formData,
          success: function (res) {
            cb(res.location);
          },
        });
      }

      function initEditor() {
        tinymce.init({
          selector: "#emailHtmlContext",
          auto_focus: true,
          elementpath: false,
          statusbar: false,
          language: "zh_CN",
          plugins: [
            "quickbars",
            "link",
            "table",
            "code",
            "image",
            "advlist",
            "lists",
            "media",
            "paste",
          ],
          menubar: "",
          menu: {},
          toolbar: [
            "outdent indent | bold italic underline strikethrough backcolor forecolor formatselect | fontsizeselect | lineheight cut copy paste | link image  media | alignleft aligncenter alignright alignjustify ",
          ],
          table_clone_elements: "p",
          table_grid: false,
          fontsize_formats:
            "42pt 36pt 26pt 24pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 9pt 7.5pt 6.5pt 5.5pt 5pt",
          font_formats:
            "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
          width: "100%",
          height: "100%",
          paste_enable_default_filters: false,
          images_upload_url:
            common.url +
            "/ts-document/attachment/fileUpload?module=email&fillupf=2", //接受上传文件的后端地址
          setup: function (editor) {
            editor.on("init", function (ed) {
              ed.target.editorCommands.execCommand("fontName", false, "宋体");
            });
          },
          paste_postprocess: function (plugin, args) {
            let res = args.node.querySelectorAll("img");
            var flag = false;
            for (let i = 0; i < res.length; i++) {
              if (res[i].src.indexOf("file:") != -1) {
                flag = true;
              }
            }
            if (flag) {
              $.ajax({
                type: "get",
                url: "http://localhost:26789/file/readfile",
                success: function (res) {
                  updateImgBase64();
                },
                error: function (res) {
                  layer.open({
                    type: 1,
                    skin: "layui-layer-demo", //样式类名
                    closeBtn: 0, //不显示关闭按钮
                    anim: 2,
                    shadeClose: true, //开启遮罩关闭
                    content:
                      '<div style="padding:10px">粘贴WORD图文模式，您需要先安装一个插件<a style="color:blue" href="/static/wordPasterPlug/trasenWordPaster.zip">点击下载</a>  <br><div>',
                  });
                },
              });
            }
          },
          file_picker_types: "media",
          convert_urls: false, //这个参数加上去就可以了
          media_alt_source: false,
          media_filter_html: false,
          powerpaste_word_import: "merge", // 参数可以是propmt, merge, clear
          powerpaste_html_import: "merge", // propmt, merge, clear
          powerpaste_allow_local_images: true, //允许带图片
          paste_data_images: true,
          file_picker_callback: function (cb, value, meta) {
            if (meta.filetype == "media") {
              //创建一个隐藏的type=file的文件选择input
              let input = document.createElement("input");
              input.setAttribute("type", "file");
              input.onchange = function () {
                var file = this.files[0];
                fileUpload(file, cb);
              };
              //触发点击
              input.click();
            }
          },
          init_instance_callback: function (editor) {
            //页面初始化事件
            ed = editor;
          },
        });
      }

      //上传Base64图片
      function updateImgBase64() {
        let res =
          ed.iframeElement.contentWindow.document.querySelectorAll("img");
        let ajax = [];
        for (let i = 0; i < res.length; i++) {
          if (res[i].src.indexOf("file:") != -1) {
            ajax.push(
              $.get(
                `http://localhost:26789/file/readfile?img=${res[i].src}&dataIndex=${i}`
              )
            );
          }
        }
        if (ajax.length != 0) {
          Promise.all(ajax).then((_res) => {
            _res.forEach((_item) => {
              res[_item.dataIndex]["data-src"] = res[_item.dataIndex]["src"];
              res[_item.dataIndex]["src"] = _item.base64;
            });
            updateImg();
          });
        }
      }

      /**@desc 提交前替换图片请求服务器资源**/
      function updateImg() {
        let res =
          ed.iframeElement.contentWindow.document.querySelectorAll("img");
        let ajax = [];
        let dom = [];
        for (let i = 0; i < res.length; i++) {
          if (res[i].src.indexOf("data:image/png;base64,") != -1) {
            dom.push(res[i]);
            ajax.push(
              $.post(`${url}imageBase64Upload`, {
                token,
                module: "richfile",
                fillupf: "2",
                imageBase64: res[i].src.split("data:image/png;base64,")[1],
              })
            );
          }
        }
        if (ajax.length != 0) {
          Promise.all(ajax).then((_res) => {
            _res.forEach((_item, index) => {
              if (_item.success) {
                dom[index].src = `${_item.object.location}`;
                dom[index].removeAttribute("data-mce-src");
              }
            });
            //submit();
          });
        }
      }

      //初始化附件信息
      function initEmailFiles(attachmentList) {
        if (null != attachmentList && undefined != attachmentList) {
          $("#emailManagementFileList").show();
          $.each(attachmentList, function (index, item) {
            var trStr =
              "<tr id='upload-" +
              item.id +
              "'><td>" +
              item.originalName +
              "</td>";
            trStr +=
              "<td>" +
              (item.fileSize / 1024).toFixed(1) +
              "kb</td><td><span style='color: #5FB878;'>上传成功</span></td>";
            trStr +=
              '<td><button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button></td></tr>';
            $("#emailManagementFileList").append(trStr);
            oldFileArray.push(item.id);
          });

          $("body")
            .off("click", ".demo-delete")
            .on("click", ".demo-delete", function () {
              var idStr = $(this).parent().parent("tr").attr("id");
              var idArray = idStr.split("-");
              oldFileArray.splice($.inArray(idArray[1], oldFileArray), 1);
              $(this).parent().parent("tr").remove();
            });
        }
      }

      //获取个性签名
      function getEmpSignimg() {
        var empSignimg = "";
        $.ajax({
          type: "get",
          url: common.url + "/ts-oa/employee/selectEmpSignimg",
          async: false,
          success: function (res) {
            if (res.object != null && res.object != "") {
              empSignimg = res.object.replace(/\n/g, "<br/>");
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          },
        });
        return empSignimg;
      }

      //常用联系人
      function getEmailContact() {
        $.ajax({
          type: "get",
          url:
            common.url +
            "/ts-information/api/emailContact/selectEmailContactByUsercode",
          async: false,
          success: function (res) {
            if (res.success) {
              RecentContactsArr = res.object;
              $("#emailContactUl").empty();
              var liStr = "";
              $.each(res.object, function (i, item) {
                const { employeeNo, employeeName, deptName } = item;
                liStr += `<li class="emailContactLi" employeeNo=${employeeNo} employeeName=${employeeName}>
                  ${employeeName + "-" + deptName}
                </li>`;
              });
              $("#emailContactUl").append(liStr);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          },
        });
      }

      // 常用联系人 记住焦点
      $("#editInEmailForm")
        .off("click", "#toEmailSearchInput")
        .on("click", "#toEmailSearchInput", function (e) {
          e.stopPropagation();
          chooseInput = "toEmailSelUser";
        });

      $("#editInEmailForm")
        .off("click", "#ccEmailSearchInput")
        .on("click", "#ccEmailSearchInput", function (e) {
          e.stopPropagation();
          chooseInput = "ccEmailSelUser";
        });

      $("#editInEmailForm")
        .off("click", "#bccEmailSearchInput")
        .on("click", "#bccEmailSearchInput", function (e) {
          e.stopPropagation();
          chooseInput = "bccEmailSelUser";
        });

      // 常用联系人tree 点击
      $("body")
        .off("click", ".emailContactLi")
        .on("click", ".emailContactLi", function () {
          var employeeNo = $(this).attr("employeeNo");
          var employeeName = $(this).attr("employeeName");
          if($(this).text().indexOf('-外部邮箱') != -1) {
            if ("" != $("#outEmailAddress").val()) {
              var outEmailAddressArr = $("#outEmailAddress")
                .val()
                .split(",");
              if ($.inArray(employeeNo, outEmailAddressArr) == -1) {
                $("#outEmailAddress").val(
                  $("#outEmailAddress").val() + "," + employeeName
                );
              } else {
                layer.msg("已添加至外部邮箱地址");
              }
            } else {
              $("#outEmailAddress").val(employeeName);
            }
          }
          else if ("toEmailSelUser" == chooseInput) {
            if ("" != $("#toEmailSelUser").val()) {
              var toEmailselUserCodeArr = $("#toEmailselUserCode")
                .val()
                .split(",");
              if ($.inArray(employeeNo, toEmailselUserCodeArr) == -1) {
                $("#toEmailSelUser").val(
                  $("#toEmailSelUser").val() + "," + employeeName
                );
                $("#toEmailUserId").val(
                  $("#toEmailUserId").val() + "," + employeeNo
                );
                $("#toEmailselUserCode").val(
                  $("#toEmailselUserCode").val() + "," + employeeNo
                );
              } else {
                layer.msg("已添加至收件人");
              }
            } else {
              $("#toEmailSelUser").val(employeeName);
              $("#toEmailUserId").val(employeeNo);
              $("#toEmailselUserCode").val(employeeNo);
            }

            toEmailSearchInput.renderSelectItem({
              names: $("#toEmailSelUser").val(),
              idArr: $("#toEmailUserId").val().split(","),
              codesArr: $("#toEmailselUserCode").val().split(","),
            });
          }
          else if ("ccEmailSelUser" == chooseInput) {
            if ("" != $("#ccEmailSelUser").val()) {
              var ccEmailselUserCodeArr = $("#ccEmailselUserCode")
                .val()
                .split(",");
              if ($.inArray(employeeNo, ccEmailselUserCodeArr) == -1) {
                $("#ccEmailSelUser").val(
                  $("#ccEmailSelUser").val() + "," + employeeName
                );
                $("#ccEmailUserId").val(
                  $("#ccEmailUserId").val() + "," + employeeNo
                );
                $("#ccEmailselUserCode").val(
                  $("#ccEmailselUserCode").val() + "," + employeeNo
                );
              } else {
                layer.msg("已添加至抄送人");
              }
            } else {
              $("#ccEmailSelUser").val(employeeName);
              $("#ccEmailUserId").val(employeeNo);
              $("#ccEmailselUserCode").val(employeeNo);
            }

            ccEmailSearchInput.renderSelectItem({
              names: $("#ccEmailSelUser").val(),
              idArr: $("#ccEmailUserId").val().split(","),
              codesArr: $("#ccEmailselUserCode").val().split(","),
            });
          }
          else if ("bccEmailSelUser" == chooseInput) {
            if ("" != $("#bccEmailSelUser").val()) {
              var ccEmailselUserCodeArr = $("#bccEmailselUserCode")
                .val()
                .split(",");
              if ($.inArray(employeeNo, ccEmailselUserCodeArr) == -1) {
                $("#bccEmailSelUser").val(
                  $("#bccEmailSelUser").val() + "," + employeeName
                );
                $("#bccEmailUserId").val(
                  $("#bccEmailUserId").val() + "," + employeeNo
                );
                $("#bccEmailselUserCode").val(
                  $("#bccEmailselUserCode").val() + "," + employeeNo
                );
              } else {
                layer.msg("已添加至密送人");
              }
            } else {
              $("#bccEmailSelUser").val(employeeName);
              $("#bccEmailUserId").val(employeeNo);
              $("#bccEmailselUserCode").val(employeeNo);
            }

            bccEmailSearchInput.renderSelectItem({
              names: $("#bccEmailSelUser").val(),
              idArr: $("#bccEmailUserId").val().split(","),
              codesArr: $("#bccEmailselUserCode").val().split(","),
            });
          }
        });
    });
  };
});
