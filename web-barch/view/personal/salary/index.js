'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                util = layui.util,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;

            //tab切换
            $('#salaryListBox .oa-nav .oa-nav_item')
                .off('click')
                .on('click', function () {
                    $(this).addClass('active').siblings().removeClass('active');
                    $('#salaryListBox .salaryListBox').hide();
                    $('#salaryListBox .salaryListBox').eq($(this).index()).show();
                    if ($(this).index() == 0) {
                        reloadSalaryList();
                    } else if ($(this).index() == 1) {
                        reloadJxSalaryList();
                    }
                });

            var date = new Date();
            var year = date.getFullYear(); // 获取完整的年份(4位)
            var month = date.getMonth() + 1; // 获取当前月份(0-11,0代表1月)的上一个月
            if (month < 10) {
                month = '0' + month;
            }
            var yearmonth = year + '-' + month;
            laydate.render({
                elem: '#toa_salary_jbgzSearchDate',
                type: 'month',
                value: yearmonth,
                done: function (value, date, endDate) {
                    $('#toa_salary_jbgzSearchDate').val(value);
                    reloadSalaryList();
                },
            });
            laydate.render({
                elem: '#toa_salary_jxgzSearchDate',
                type: 'month',
                value: yearmonth,
                done: function (value, date, endDate) {
                    $('#toa_salary_jxgzSearchDate').val(value);
                    reloadJxSalaryList();
                },
            });

            var trasenTable, trasenTableJx;
            reloadSalaryList();
            // 工资动态表格
            function reloadSalaryList() {
                $.ajax({
                    type: 'post',
                    contentType: 'application/json; charset=utf-8',
                    url: common.url + '/ts-oa/salary/salarySet/getTableHeadCols/toa_salary_jbgz/' + $('#toa_salary_jbgzSearchDate').val(),
                    success: function (res) {
                        if (res.success) {
                            $('#toa_salary_jbgzTable_box').html('<table id="toa_salary_jbgzTable"></table><div id="toa_salary_jbgzPager"></div>');
                            var headCols = res.object.headCols;
                            var isSalaryAdmin = res.object.isSalaryAdmin;
                            if (isSalaryAdmin && headCols.length > 0) {
                                headCols.push({
                                    label: '操作',
                                    sortable: false,
                                    name: 'opt',
                                    width: 100,
                                    align: 'center',
                                    classes: 'visible',
                                    formatter: function (cellvalue, options, row) {
                                        return '<button type="button" id="toa_salary_jbgzDelBtn" class="layui-btn ">删除 </button>';
                                    },
                                });
                            }
                            // 表格渲染
                            trasenTable = new $.trasenTable('toa_salary_jbgzTable', {
                                url: common.url + '/ts-oa/salary/salarySet/salaryList/toa_salary_jbgz',
                                pager: 'toa_salary_jbgzPager',
                                sortname: 'salary_date',
                                rowNum: 10,
                                rowList: [15, 30, 50, 100],
                                postData: {
                                    dateStr: $('#toa_salary_jbgzSearchDate').val(),
                                },
                                colModel: headCols,
                                buidQueryParams: function () {
                                    var search = $('#toa_salary_jbgzForm').serializeArray();
                                    var opt = [];
                                    var data = {};
                                    for (var i in search) {
                                        opt.push(search[i]);
                                    }
                                    for (var i in opt) {
                                        data[opt[i].name] = opt[i].value;
                                    }
                                    return data;
                                },
                            });
                        }
                    },
                });
            }

            // 绩效动态表格
            function reloadJxSalaryList() {
                $.ajax({
                    type: 'post',
                    contentType: 'application/json; charset=utf-8',
                    url: common.url + '/ts-oa/salary/salarySet/getTableHeadCols/toa_salary_jxgz/' + $('#toa_salary_jxgzSearchDate').val(),
                    success: function (res) {
                        if (res.success) {
                            $('#toa_salary_jxgzTable_box').html('<table id="toa_salary_jxgzTable"></table><div id="toa_salary_jxgzPager"></div>');
                            var headCols = res.object.headCols;
                            var isSalaryAdmin = res.object.isSalaryAdmin;
                            if (isSalaryAdmin && headCols.length > 0) {
                                headCols.push({
                                    label: '操作',
                                    sortable: false,
                                    name: 'opt',
                                    width: 100,
                                    align: 'center',
                                    classes: 'visible',
                                    formatter: function (cellvalue, options, row) {
                                        return '<button type="button" id="toa_salary_jxgzDelBtn" class="layui-btn ">删除 </button>';
                                    },
                                });
                            }
                            // 表格渲染
                            trasenTableJx = new $.trasenTable('toa_salary_jxgzTable', {
                                url: common.url + '/ts-oa/salary/salarySet/salaryList/toa_salary_jxgz',
                                pager: 'toa_salary_jxgzPager',
                                sortname: 'salary_date',
                                rowNum: 10,
                                rowList: [15, 30, 50, 100],
                                postData: {
                                    dateStr: $('#toa_salary_jxgzSearchDate').val(),
                                },
                                colModel: headCols,
                                buidQueryParams: function () {
                                    var search = $('#toa_salary_jxgzForm').serializeArray();
                                    var opt = [];
                                    var data = {};
                                    for (var i in search) {
                                        opt.push(search[i]);
                                    }
                                    for (var i in opt) {
                                        data[opt[i].name] = opt[i].value;
                                    }
                                    return data;
                                },
                            });
                        }
                    },
                });
            }

            //查询
            $('#toa_salary_jbgzSeach').funs('click', function () {
                salaryRefresh();
            });
            $('#toa_salary_jxgzSeach').funs('click', function () {
                salaryRefreshJx();
            });

            //发送工资消息提醒
            $('#toa_salary_jbgzSendMsg').funs('click', function () {
                var sendDate = $('#toa_salary_jbgzSearchDate').val();
                var sendDateArray = sendDate.split('-');
                var pushNumbers = 0;
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-oa/salary/salarySet/getPushNumbers/toa_salary_jbgz/' + sendDate,
                    async: false,
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            pushNumbers = res.object;
                        } else {
                            layer.closeAll();
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });

                layer.confirm('确定推送[' + sendDateArray[0] + '年' + sendDateArray[1] + '月]的工资消息提醒吗<span style="color:red">(第' + (pushNumbers + 1) + '次推送)</span>?', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/salary/salarySet/sendMsgSalaryDataByMonth/toa_salary_jbgz/' + sendDate,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('推送成功');
                            } else {
                                layer.closeAll();
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            });
            $('#toa_salary_jxgzSendMsg').funs('click', function () {
                var sendDate = $('#toa_salary_jxgzSearchDate').val();
                var sendDateArray = sendDate.split('-');
                var pushNumbers = 0;
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-oa/salary/salarySet/getPushNumbers/toa_salary_jxgz/' + sendDate,
                    async: false,
                    success: function (res) {
                        $.closeloadings();
                        if (res.success) {
                            layer.closeAll();
                            pushNumbers = res.object;
                        } else {
                            layer.closeAll();
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        res = JSON.parse(res.responseText);
                        layer.msg(res.message);
                    },
                });
                layer.confirm('确定推送[' + sendDateArray[0] + '年' + sendDateArray[1] + '月]的工资消息提醒吗<span style="color:red">(第' + (pushNumbers + 1) + '次推送)</span>?', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/salary/salarySet/sendMsgSalaryDataByMonth/toa_salary_jxgz/' + sendDate,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('推送成功');
                            } else {
                                layer.closeAll();
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            });

            //根据月份 删除
            $('#toa_salary_jbgzDrop').funs('click', function () {
                var delDate = $('#toa_salary_jbgzSearchDate').val();
                var delDateArray = delDate.split('-');
                layer.confirm('确定删除[' + delDateArray[0] + '年' + delDateArray[1] + '月]的数据?', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/salary/salarySet/deletedSalaryDataByMonth/toa_salary_jbgz/' + delDate,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('删除成功');
                                salaryRefresh();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            });
            $('#toa_salary_jxgzDrop').funs('click', function () {
                var delDate = $('#toa_salary_jxgzSearchDate').val();
                var delDateArray = delDate.split('-');
                layer.confirm('确定删除[' + delDateArray[0] + '年' + delDateArray[1] + '月]的数据?', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/salary/salarySet/deletedSalaryDataByMonth/toa_salary_jxgz/' + delDate,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('删除成功');
                                salaryRefreshJx();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            });

            // 删除
            $('#toa_salary_jbgzDelBtn').funs('click', function () {
                var data = trasenTable.getSourceRowData();
                var delDate = $('#toa_salary_jbgzSearchDate').val();
                layer.confirm('确定删除该数据?', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/salary/salarySet/deletedSalaryData/toa_salary_jbgz/' + delDate + '/' + data.id,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('删除成功');
                                salaryRefresh();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            });
            $('#toa_salary_jxgzDelBtn').funs('click', function () {
                var data = trasenTable.getSourceRowData();
                var delDate = $('#toa_salary_jxgzSearchDate').val();
                layer.confirm('确定删除该数据?', { 
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                 }, function (index) {
                    $.ajax({
                        type: 'post',
                        url: common.url + '/ts-oa/salary/salarySet/deletedSalaryData/toa_salary_jxgz/' + delDate + '/' + data.id,
                        success: function (res) {
                            $.closeloadings();
                            if (res.success) {
                                layer.closeAll();
                                layer.msg('删除成功');
                                salaryRefreshJx();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            res = JSON.parse(res.responseText);
                            layer.msg(res.message);
                        },
                    });
                });
            });

            //下载模板
            $('#toa_salary_jbgzDowload').funs('click', function () {
                var downDate = $('#toa_salary_jbgzSearchDate').val();
                window.location.href = common.url + '/ts-oa/salary/salarySet/exportTemplate/toa_salary_jbgz/' + downDate;
            });
            $('#toa_salary_jxgzDowload').funs('click', function () {
                var downDate = $('#toa_salary_jxgzSearchDate').val();
                window.location.href = common.url + '/ts-oa/salary/salarySet/exportTemplate/toa_salary_jxgz/' + downDate;
            });

            //导入
            $('#toa_salary_jbgzImport').funs('click', function () {
                var data = {};
                var downDate = $('#toa_salary_jbgzSearchDate').val();
                var downloadUrl = common.url + '/ts-oa/salary/salarySet/exportTemplate/toa_salary_jbgz/' + downDate;
                data.version = '2';
                data.tableName = 'toa_salary_jbgz';
                data.downloadUrl = downloadUrl;
                data.dateStr = $('#toa_salary_jbgzSearchDate').val();
                $.quoteFun('/personal/import', {
                    trasen: trasenTable,
                    title: '导入',
                    ref: reloadSalaryList,
                    data: data,
                });
            });
            $('#toa_salary_jxgzImport').funs('click', function () {
                var data = {};
                var downDate = $('#toa_salary_jxgzSearchDate').val();
                var downloadUrl = common.url + '/ts-oa/salary/salarySet/exportTemplate/toa_salary_jxgz/' + downDate;
                data.version = '2';
                data.downloadUrl = downloadUrl;
                data.tableName = 'toa_salary_jxgz';
                data.dateStr = $('#toa_salary_jxgzSearchDate').val();
                $.quoteFun('/personal/import', {
                    trasen: trasenTable,
                    title: '导入',
                    ref: reloadJxSalaryList,
                    data: data,
                });
            });

            //基本工资导入记录
            $('#toa_salary_jbgzRecord').funs('click', function () {
                $.quoteFun('/personal/salary/salaryRecord', {
                    title: '工资导入操作记录',
                    salaryType: '基本工资',
                });
            });

            //绩效工资导入记录
            $('#toa_salary_jxgzRecord').funs('click', function () {
                $.quoteFun('/personal/salary/salaryRecord', {
                    title: '绩效导入操作记录',
                    salaryType: '绩效工资',
                });
            });

            //刷新
            function salaryRefresh() {
                trasenTable.refresh();
            }
            function salaryRefreshJx() {
                trasenTableJx.refresh();
            }
        });
    };
});
